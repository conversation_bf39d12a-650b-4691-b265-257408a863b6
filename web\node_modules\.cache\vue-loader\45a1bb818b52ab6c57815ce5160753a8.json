{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\WrongQuestionPractice.vue?vue&type=style&index=0&id=ae609f68&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\WrongQuestionPractice.vue", "mtime": 1753195991411}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.wrong-practice-container {\n  min-height: 100vh;\n  background-color: #f0f2f5;\n}\n\n\n\n.loading-container, .empty-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n  flex-direction: column;\n}\n\n.loading-text {\n  margin-top: 16px;\n  color: #666;\n}\n\n.practice-area {\n  padding: 0 24px 24px;\n}\n\n.practice-area.full-screen-mode {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  background: white;\n  padding: 16px;\n}\n\n.question-container {\n  margin-top: 16px;\n}\n", {"version": 3, "sources": ["WrongQuestionPractice.vue"], "names": [], "mappings": ";AAi2BA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "WrongQuestionPractice.vue", "sourceRoot": "src/views/examSystem/components", "sourcesContent": ["<template>\n  <div class=\"wrong-practice-container\">\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <a-spin size=\"large\">\n        <div class=\"loading-text\">正在加载错题...</div>\n      </a-spin>\n    </div>\n\n    <!-- 无题目状态 -->\n    <div v-else-if=\"!loading && questionList.length === 0\" class=\"empty-container\">\n      <a-empty description=\"没有找到符合条件的错题\">\n        <a-button type=\"primary\" @click=\"$emit('back-to-list')\">返回错题记录</a-button>\n      </a-empty>\n    </div>\n\n    <!-- 练习界面 -->\n    <div v-else class=\"practice-area\" :class=\"{'full-screen-mode': isFullScreen, 'horizontal-layout': isFullScreen}\">\n      <!-- 练习状态栏 -->\n      <practice-status-bar\n        :isFullScreen=\"isFullScreen\"\n        :isReviewMode=\"isReviewMode\"\n        :showAnswer=\"showAnswer\"\n        :practiseMode=\"'wrong'\"\n        :practiseCount=\"questionList.length\"\n        :practiceTitle=\"practiceConfig.title\"\n        :remainingTimeText=\"''\"\n        :questionList=\"questionList\"\n        :currentQuestionIndex=\"currentQuestionIndex\"\n        :answeredQuestions=\"answeredQuestions\"\n        :userAnswersMap=\"userAnswersMap\"\n        :isCollected=\"isCollected\"\n        :collectLoading=\"collectLoading\"\n        @check-answer-correct=\"isAnswerCorrect\"\n        @jump-to-question=\"jumpToQuestion\"\n        @exit-review-mode=\"exitReviewMode\"\n        @toggle-show-answer=\"showAnswer = !showAnswer\"\n        @collect-question=\"collectQuestion\"\n        @exit-practise=\"exitPractise\"\n      />\n\n      <!-- 题目与答题区 -->\n      <a-card class=\"question-container\" :bordered=\"false\">\n        <!-- 客观题显示 -->\n        <question-display\n          v-if=\"currentQuestion.questionType !== 3\"\n          :currentQuestion=\"currentQuestion\"\n          :currentQuestionIndex=\"currentQuestionIndex\"\n          :questionList=\"questionList\"\n          :userAnswer.sync=\"userAnswer\"\n          :userAnswersMap=\"userAnswersMap\"\n          :isReviewMode=\"isReviewMode\"\n          :showAnswer=\"showAnswer\"\n          :currentQuestionStatus=\"currentQuestionStatus\"\n          @prev-question=\"prevQuestion\"\n          @next-question=\"nextQuestion\"\n        />\n\n        <!-- 编程题显示 -->\n        <coding-question\n          v-else\n          :currentQuestion=\"currentQuestion\"\n          :currentQuestionIndex=\"currentQuestionIndex\"\n          :questionList=\"questionList\"\n          :showAnswer=\"showAnswer\"\n          :isFullScreen=\"isFullScreen\"\n          :isReviewMode=\"isReviewMode\"\n          :currentQuestionStatus=\"currentQuestionStatus\"\n          :code.sync=\"code\"\n          :selectedLanguage=\"selectedLanguage\"\n          :supportedLanguages=\"supportedLanguages\"\n          :editorHeight=\"editorHeight\"\n          :editorTheme.sync=\"editorTheme\"\n          :editorFontSize.sync=\"editorFontSize\"\n          :editorTabSize=\"editorTabSize\"\n          :openTestCaseDrawer.sync=\"openTestCaseDrawer\"\n          :testInputMap=\"testInputMap\"\n          :testResultMap=\"testResultMap\"\n          :activeTestCaseIndexMap=\"activeTestCaseIndexMap\"\n          :isSubmitting=\"isSubmitting\"\n          @prev-question=\"prevQuestion\"\n          @next-question=\"nextQuestion\"\n          @language-change=\"handleLanguageChange\"\n          @reset-code=\"resetCode\"\n          @get-last-accepted-code=\"getUserLastAcceptedCode\"\n          @switch-focus-mode=\"switchFocusMode\"\n          @formal-submission=\"handleFormalSubmission\"\n          @update-test-input=\"updateTestInput\"\n          @update-test-result=\"updateTestResult\"\n          @update-active-test-case-index=\"updateActiveTestCaseIndex\"\n          ref=\"codeMirror\"\n        />\n      </a-card>\n\n      <!-- 练习结果弹窗 -->\n      <practice-result-modal\n        v-model=\"practiseCompleted\"\n        :correctCount=\"correctCount\"\n        :incorrectCount=\"incorrectCount\"\n        :unfinishedCount=\"unfinishedCount\"\n        :totalCount=\"questionList.length\"\n        :hasAnsweredQuestions=\"answeredQuestions.length > 0\"\n        :isWrongPracticeMode=\"true\"\n        @start-new-practise=\"startNewPractise\"\n        @enter-review-mode=\"enterReviewMode\"\n        @close=\"handlePracticeSummaryClose\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getLastAcceptedCode, addWrongRecord, addCollection, deleteCollection, checkCollection, queryProblemById, getWrongRecordList } from '@/api/examSystem'\nimport { submitTestJudge, getTestJudgeResult } from '@/api/manage'\nimport { getCodeTemplate } from '@/utils/codeTemplates'\nimport PracticeStatusBar from './PracticeStatusBar.vue'\nimport QuestionDisplay from './QuestionDisplay.vue'\nimport CodingQuestion from './CodingQuestion.vue'\nimport PracticeResultModal from './PracticeResultModal.vue'\n\nexport default {\n  name: 'WrongQuestionPractice',\n  components: {\n    PracticeStatusBar,\n    QuestionDisplay,\n    CodingQuestion,\n    PracticeResultModal\n  },\n  props: {\n    practiceConfig: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      questionList: [],\n      currentQuestionIndex: 1,\n      \n      // 用户答案相关\n      userAnswer: '',\n      answeredQuestions: [],\n      userAnswersMap: {},\n      \n      // 编程题相关\n      selectedLanguage: 'C++',\n      code: '',\n      editorHeight: 600,\n      editorTheme: 'solarized',\n      editorFontSize: '14px',\n      editorTabSize: 4,\n      openTestCaseDrawer: false,\n      supportedLanguages: ['C', 'C++', 'Java', 'Python3', 'Python2'],\n      \n      // 测试数据状态管理\n      testInputMap: {},\n      testResultMap: {},\n      activeTestCaseIndexMap: {},\n      isSubmitting: false,\n      \n      // 练习状态\n      showAnswer: false,\n      isReviewMode: false,\n      practiseCompleted: false,\n      isFullScreen: false,\n      \n      // 收藏相关\n      collectionStatusMap: {},\n      collectLoading: false\n    }\n  },\n  computed: {\n    currentQuestion() {\n      if (!this.questionList.length) return {}\n      return this.questionList[this.currentQuestionIndex - 1] || {}\n    },\n    \n    isCollected() {\n      return this.currentQuestion && !!this.collectionStatusMap[this.currentQuestion.id]\n    },\n    \n    correctCount() {\n      return this.questionList.filter(q => \n        this.answeredQuestions.includes(q.id) && \n        this.isAnswerCorrect(q)\n      ).length\n    },\n    \n    incorrectCount() {\n      return this.answeredQuestions.length - this.correctCount\n    },\n    \n    unfinishedCount() {\n      return this.questionList.length - this.answeredQuestions.length\n    },\n    \n    currentQuestionStatus() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return 'unfinished'\n      \n      if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n        return 'unfinished'\n      }\n      \n      return this.isAnswerCorrect(this.currentQuestion) ? 'correct' : 'incorrect'\n    }\n  },\n  watch: {\n    practiceConfig: {\n      handler(newConfig) {\n        if (newConfig && Object.keys(newConfig).length > 0) {\n          this.loadQuestions()\n        }\n      },\n      immediate: true\n    },\n    \n    userAnswer(newVal) {\n      if (newVal && newVal !== '') {\n        const currentQuestionId = this.currentQuestion.id\n        const alreadyAnswered = this.answeredQuestions.includes(currentQuestionId)\n        \n        if (!alreadyAnswered) {\n          this.answeredQuestions.push(currentQuestionId)\n          this.userAnswersMap[currentQuestionId] = newVal\n          \n          if (this.currentQuestionIndex < this.questionList.length) {\n            setTimeout(() => {\n              this.nextQuestion()\n            }, 500)\n          }\n        }\n      }\n    }\n  },\n  created() {\n    document.addEventListener('keydown', this.handleKeyDown)\n  },\n  beforeDestroy() {\n    document.removeEventListener('keydown', this.handleKeyDown)\n    document.body.style.overflow = ''\n  },\n  methods: {\n    // 加载题目\n    async loadQuestions() {\n      this.loading = true\n      \n      try {\n        let questions = []\n        \n        if (this.practiceConfig.mode === 'single') {\n          // 单题练习\n          const res = await queryProblemById(this.practiceConfig.questionId)\n          if (res.success) {\n            questions = [res.result]\n          }\n        } else if (this.practiceConfig.mode === 'selected') {\n          // 选中题目练习\n          const questionPromises = this.practiceConfig.questionIds.map(id => queryProblemById(id))\n          const results = await Promise.all(questionPromises)\n          questions = results.filter(res => res.success).map(res => res.result)\n        } else if (this.practiceConfig.mode === 'wrongRecords') {\n          // 所有错题练习\n          const params = {\n            ...this.practiceConfig.queryParam,\n            pageNo: 1,\n            pageSize: 1000 // 获取所有错题\n          }\n\n          // 先获取错题记录\n          const wrongRecordsRes = await getWrongRecordList(params)\n          if (wrongRecordsRes.success && wrongRecordsRes.result) {\n            const wrongRecords = wrongRecordsRes.result.records || wrongRecordsRes.result\n\n            // 提取所有错题的questionId\n            const questionIds = wrongRecords.map(record => record.questionId).filter(id => id)\n\n            // 根据questionId批量获取题目详情\n            if (questionIds.length > 0) {\n              const questionPromises = questionIds.map(id => queryProblemById(id))\n              const results = await Promise.all(questionPromises)\n              questions = results.filter(res => res.success).map(res => res.result)\n            }\n          }\n        }\n        \n        // 处理题目数据\n        this.questionList = this.processQuestions(questions)\n        \n        if (this.questionList.length > 0) {\n          this.currentQuestionIndex = 1\n          this.resetAnswerState()\n          this.loadSavedAnswer()\n          this.checkCurrentQuestionCollection()\n        }\n      } catch (error) {\n        console.error('加载题目失败:', error)\n        this.$message.error('加载题目失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 处理题目数据\n    processQuestions(questions) {\n      return questions.map(question => {\n        try {\n          if (typeof question.content === 'string' && question.content) {\n            question.content = JSON.parse(question.content)\n          } else if (!question.content) {\n            question.content = {}\n          }\n\n          // 根据题型处理数据\n          if (question.questionType === 1) {\n            if (!question.content.options || !Array.isArray(question.content.options)) {\n              question.content.options = []\n            }\n            if (!question.content.answer) {\n              question.content.answer = ''\n            }\n          } else if (question.questionType === 2) {\n            if (!['true', 'false', '正确', '错误', 'T', 'F'].includes(question.content.answer)) {\n              question.content.answer = ''\n            }\n          } else if (question.questionType === 3) {\n            if (!question.content.description) question.content.description = ''\n            if (!question.content.input_format) question.content.input_format = ''\n            if (!question.content.output_format) question.content.output_format = ''\n\n            if (!question.content.sample_cases || !Array.isArray(question.content.sample_cases)) {\n              question.content.sample_cases = []\n            } else {\n              question.content.sample_cases = question.content.sample_cases.map(sample => ({\n                input: typeof sample.input === 'string' ? sample.input : String(sample.input || ''),\n                output: typeof sample.output === 'string' ? sample.output : String(sample.output || '')\n              }))\n            }\n          }\n\n          if (!question.content.analysis) {\n            question.content.analysis = ''\n          }\n        } catch (error) {\n          console.error('处理题目数据时出错:', error)\n          question.content = {\n            answer: '',\n            options: [],\n            description: '题目数据处理失败',\n            analysis: '',\n            sample_cases: []\n          }\n        }\n\n        return question\n      })\n    },\n\n    // 重置答案状态\n    resetAnswerState() {\n      this.userAnswer = ''\n      this.code = ''\n      this.answeredQuestions = []\n      this.userAnswersMap = {}\n      this.testInputMap = {}\n      this.testResultMap = {}\n      this.activeTestCaseIndexMap = {}\n    },\n\n    // 判断答案是否正确\n    isAnswerCorrect(question) {\n      if (!question) return false\n\n      const userAns = this.userAnswersMap[question.id]\n      const correctAns = question.content && question.content.answer ? question.content.answer : ''\n\n      if (question.questionType === 1) {\n        return userAns === correctAns\n      } else if (question.questionType === 2) {\n        let standardUserAns = userAns\n        if (userAns === 'true') standardUserAns = 'T'\n        if (userAns === 'false') standardUserAns = 'F'\n\n        const isCorrectAnswerTrue = ['true', '正确', 'T'].includes(correctAns)\n        const isUserAnswerTrue = ['true', '正确', 'T'].includes(standardUserAns)\n\n        return isCorrectAnswerTrue === isUserAnswerTrue\n      } else if (question.questionType === 3) {\n        return userAns && typeof userAns === 'object' &&\n               userAns.submitted === true && userAns.isCorrect === true\n      }\n\n      return false\n    },\n\n    // 下一题\n    nextQuestion() {\n      this.saveCurrentAnswer()\n\n      if (this.currentQuestionIndex < this.questionList.length) {\n        this.currentQuestionIndex++\n        this.loadSavedAnswer()\n        this.openTestCaseDrawer = false\n      } else {\n        if (this.answeredQuestions.length < this.questionList.length) {\n          this.$confirm({\n            title: '未完成全部题目',\n            content: `您还有 ${this.unfinishedCount} 道题目未完成，确定要结束本次练习吗？`,\n            okText: '仍要结束',\n            cancelText: '我再想想',\n            onOk: () => {\n              this.submitWrongRecords()\n              this.practiseCompleted = true\n            }\n          })\n        } else {\n          this.submitWrongRecords()\n          this.practiseCompleted = true\n        }\n      }\n\n      this.checkCurrentQuestionCollection()\n    },\n\n    // 上一题\n    prevQuestion() {\n      this.saveCurrentAnswer()\n\n      if (this.currentQuestionIndex > 1) {\n        this.currentQuestionIndex--\n        this.loadSavedAnswer()\n        this.openTestCaseDrawer = false\n      }\n\n      this.checkCurrentQuestionCollection()\n    },\n\n    // 跳转到指定题目\n    jumpToQuestion(index) {\n      if (index >= 1 && index <= this.questionList.length) {\n        this.saveCurrentAnswer()\n        this.currentQuestionIndex = index\n        this.loadSavedAnswer()\n        this.openTestCaseDrawer = false\n      }\n\n      this.checkCurrentQuestionCollection()\n    },\n\n    // 保存当前题目答案\n    saveCurrentAnswer() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return\n\n      const questionId = this.currentQuestion.id\n\n      if (this.currentQuestion.questionType === 3) {\n        if (this.code && this.code.trim() !== '') {\n          const existingAnswer = this.userAnswersMap[questionId]\n\n          if (existingAnswer && typeof existingAnswer === 'object') {\n            this.userAnswersMap[questionId] = {\n              ...existingAnswer,\n              code: this.code\n            }\n          } else {\n            this.userAnswersMap[questionId] = {\n              code: this.code,\n              submitted: false\n            }\n          }\n        }\n      } else {\n        if (this.userAnswer) {\n          this.userAnswersMap[questionId] = this.userAnswer\n\n          if (!this.answeredQuestions.includes(questionId)) {\n            this.answeredQuestions.push(questionId)\n          }\n        }\n      }\n    },\n\n    // 加载已保存的答案\n    loadSavedAnswer() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return\n\n      const questionId = this.currentQuestion.id\n      const savedAnswer = this.userAnswersMap[questionId]\n\n      if (savedAnswer) {\n        if (this.currentQuestion.questionType === 3) {\n          if (typeof savedAnswer === 'object' && savedAnswer.code) {\n            this.code = savedAnswer.code\n          } else if (typeof savedAnswer === 'string') {\n            this.code = savedAnswer\n          } else {\n            this.code = ''\n          }\n        } else {\n          this.userAnswer = savedAnswer\n        }\n      } else {\n        this.resetCurrentAnswerState()\n      }\n    },\n\n    // 重置当前题目答案状态\n    resetCurrentAnswerState() {\n      this.userAnswer = ''\n      this.code = ''\n    },\n\n    // 处理键盘事件\n    handleKeyDown(event) {\n      if (event.key === 'Escape' && this.isFullScreen) {\n        this.isFullScreen = false\n\n        if (this.$refs.codeMirror) {\n          this.$refs.codeMirror.switchFocusMode(false)\n        }\n\n        document.body.style.overflow = ''\n        this.$message.success('已退出专注模式')\n      }\n    },\n\n    // 切换焦点模式\n    switchFocusMode(isOpen) {\n      this.isFullScreen = isOpen\n\n      if (isOpen) {\n        document.body.style.overflow = 'hidden'\n        this.$message.success('已进入专注模式，按ESC可退出')\n      } else {\n        document.body.style.overflow = ''\n        this.$message.success('已退出专注模式')\n      }\n    },\n\n    // 退出练习\n    exitPractise() {\n      this.$confirm({\n        title: '确定要退出错题练习吗?',\n        content: '当前错题练习进度将会丢失',\n        onOk: () => {\n          this.$emit('back-to-list')\n        }\n      })\n    },\n\n    // 收藏题目\n    collectQuestion() {\n      if (!this.currentQuestion || !this.currentQuestion.id) {\n        this.$message.warning('当前题目信息不完整，无法操作')\n        return\n      }\n\n      if (this.collectLoading) return\n      this.collectLoading = true\n\n      const questionId = this.currentQuestion.id\n\n      if (this.isCollected) {\n        const collectionId = this.collectionStatusMap[questionId]\n        deleteCollection({ id: collectionId }).then(res => {\n          if (res.success) {\n            this.$message.success('已取消收藏')\n            this.$delete(this.collectionStatusMap, questionId)\n          } else {\n            this.$message.warning(res.message || '取消收藏失败')\n          }\n        }).finally(() => {\n          this.collectLoading = false\n        })\n      } else {\n        addCollection({ questionId }).then(res => {\n          if (res.success) {\n            this.$message.success('收藏成功')\n            this.$set(this.collectionStatusMap, questionId, res.result.id)\n          } else {\n            this.$message.warning(res.message || '收藏失败')\n          }\n        }).finally(() => {\n          this.collectLoading = false\n        })\n      }\n    },\n\n    // 检查当前题目收藏状态\n    checkCurrentQuestionCollection() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return\n\n      checkCollection({ questionId: this.currentQuestion.id }).then(res => {\n        if (res.success && res.result) {\n          this.$set(this.collectionStatusMap, this.currentQuestion.id, res.result.id)\n        } else {\n          this.$delete(this.collectionStatusMap, this.currentQuestion.id)\n        }\n      }).catch(() => {\n        this.$delete(this.collectionStatusMap, this.currentQuestion.id)\n      })\n    },\n\n    // 编程题相关方法\n    handleLanguageChange(newLang) {\n      this.selectedLanguage = newLang\n    },\n\n    resetCode() {\n      const templateCode = getCodeTemplate(this.selectedLanguage)\n\n      if (this.currentQuestion &&\n          this.currentQuestion.content &&\n          this.currentQuestion.content.template &&\n          this.currentQuestion.content.template[this.selectedLanguage]) {\n        this.code = this.currentQuestion.content.template[this.selectedLanguage]\n      } else {\n        this.code = templateCode\n      }\n    },\n\n    getUserLastAcceptedCode() {\n      if (!this.currentQuestion || !this.currentQuestion.id) {\n        this.$message.warning('无法获取题目信息')\n        return\n      }\n\n      const params = {\n        pid: this.currentQuestion.id,\n        language: this.selectedLanguage\n      }\n\n      getLastAcceptedCode(params).then(res => {\n        if (res.success && res.result) {\n          this.code = res.result.code\n          this.$message.success('成功获取最近通过的代码')\n        } else {\n          this.$message.warning(res.message || '您还未通过该题目，无法获取最近通过的代码')\n        }\n      }).catch(err => {\n        console.error('获取最近通过代码出错：', err)\n        this.$message.error('获取最近通过代码失败')\n      })\n    },\n\n    handleFormalSubmission(data) {\n      if (this.isSubmitting) {\n        this.$message.warning('正在处理您的提交，请稍候...')\n        return\n      }\n\n      this.isSubmitting = true\n      this.$message.loading({ content: '正在提交评测...', key: 'submitting' })\n\n      const params = {\n        ...data,\n        isSubmit: true\n      }\n\n      submitTestJudge(params).then(res => {\n        if (res.success && res.result && res.result.submissionId) {\n          const submissionKey = res.result.submissionId\n          this.$message.success({ content: '提交成功，开始获取结果...', key: 'submitting', duration: 2 })\n          this.pollJudgeResult(submissionKey, 0, true)\n        } else if (res.success && res.result && res.result.submissionId === null) {\n          this.$message.warning({\n            content: '系统已接收您的提交，但由于短时间内提交过于频繁，暂时无法查看结果，请稍后再试',\n            key: 'submitting',\n            duration: 3\n          })\n        } else {\n          this.$message.error({ content: `提交失败: ${res.message || '未能获取到判题ID'}`, key: 'submitting', duration: 3 })\n        }\n      }).catch(err => {\n        this.$message.error({ content: `提交异常: ${err.message || '网络错误'}`, key: 'submitting', duration: 3 })\n      }).finally(() => {\n        setTimeout(() => {\n          this.isSubmitting = false\n        }, 1000)\n      })\n    },\n\n    pollJudgeResult(submissionKey, times = 0, isSubmit = false) {\n      if (times > 20) {\n        this.$message.error('判题超时，请稍后再试')\n        return\n      }\n\n      setTimeout(() => {\n        getTestJudgeResult(submissionKey).then(res => {\n          if (res.success) {\n            if (res.result.status === 'PENDING' || res.result.status === 'Judging') {\n              this.pollJudgeResult(submissionKey, times + 1, isSubmit)\n            } else {\n              if (isSubmit) {\n                if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                  this.answeredQuestions.push(this.currentQuestion.id)\n                }\n\n                const immediateErrorStatus = ['Compile Error', 'System Error', 'Runtime Error']\n\n                if (immediateErrorStatus.includes(res.result.status)) {\n                  this.$notification.error({\n                    message: '提交失败',\n                    description: `状态: ${res.result.status}，这通常不是代码逻辑问题，请检查您的代码语法或环境。`,\n                    duration: 5\n                  })\n\n                  this.userAnswersMap[this.currentQuestion.id] = {\n                    code: this.code,\n                    isCorrect: false,\n                    submitted: true\n                  }\n\n                  if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                    this.answeredQuestions.push(this.currentQuestion.id)\n                  }\n                } else {\n                  this.$notification.success({\n                    message: '评测完成',\n                    description: '您的代码已执行完毕，结果已记录。',\n                    duration: 3\n                  })\n\n                  this.userAnswersMap[this.currentQuestion.id] = {\n                    code: this.code,\n                    isCorrect: res.result.status === 'Accepted',\n                    submitted: true\n                  }\n\n                  if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                    this.answeredQuestions.push(this.currentQuestion.id)\n                  }\n\n                  if (this.currentQuestionIndex < this.questionList.length) {\n                    setTimeout(() => {\n                      this.nextQuestion()\n                    }, 500)\n                  }\n                }\n              } else {\n                this.$message.success('测试运行完成')\n              }\n            }\n          } else {\n            this.$message.error(res.message || '获取判题结果失败')\n          }\n        })\n      }, 2000)\n    },\n\n    updateTestInput(input) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.testInputMap, this.currentQuestion.id, input)\n      }\n    },\n\n    updateTestResult(result) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.testResultMap, this.currentQuestion.id, result)\n      }\n    },\n\n    updateActiveTestCaseIndex(index) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.activeTestCaseIndexMap, this.currentQuestion.id, index)\n      }\n    },\n\n    // 练习结果相关方法\n    startNewPractise() {\n      this.practiseCompleted = false\n      this.resetAnswerState()\n      this.currentQuestionIndex = 1\n      this.loadSavedAnswer()\n    },\n\n    enterReviewMode() {\n      this.isReviewMode = true\n      this.showAnswer = true\n      this.practiseCompleted = false\n\n      const errorQuestions = this.questionList.filter(q =>\n        this.answeredQuestions.includes(q.id) && !this.isAnswerCorrect(q)\n      )\n\n      if (errorQuestions.length > 0) {\n        const firstErrorIndex = this.questionList.findIndex(q => q.id === errorQuestions[0].id)\n        this.currentQuestionIndex = firstErrorIndex + 1\n        this.loadSavedAnswer()\n      }\n    },\n\n    exitReviewMode() {\n      this.$confirm({\n        title: '确定要退出查阅答题模式吗?',\n        content: '退出后将返回到错题记录页面',\n        onOk: () => {\n          this.isReviewMode = false; // 关闭查阅答题模式\n          this.showAnswer = false; // 关闭显示答案\n          // 退出查阅模式时返回错题记录页面\n          this.$emit('back-to-list');\n        }\n      });\n    },\n\n    handlePracticeSummaryClose() {\n      this.practiseCompleted = false\n      this.$emit('back-to-list')\n    },\n\n    // 提交错题记录\n    submitWrongRecords() {\n      const wrongQuestions = this.questionList.filter(q =>\n        this.answeredQuestions.includes(q.id) && !this.isAnswerCorrect(q)\n      )\n\n      if (wrongQuestions.length > 0) {\n        wrongQuestions.forEach(question => {\n          const userAnswer = this.userAnswersMap[question.id]\n          let formattedAnswer = ''\n\n          if (question.questionType === 3) {\n            formattedAnswer = JSON.stringify({\n              code: userAnswer.code || '',\n              language: this.selectedLanguage\n            })\n          } else if (question.questionType === 2) {\n            if (userAnswer === 'true') {\n              formattedAnswer = 'T'\n            } else if (userAnswer === 'false') {\n              formattedAnswer = 'F'\n            } else {\n              formattedAnswer = String(userAnswer)\n            }\n          } else {\n            formattedAnswer = String(userAnswer)\n          }\n\n          this.recordWrongQuestion(question.id, formattedAnswer)\n        })\n\n        this.$message.success(`已将${wrongQuestions.length}道错题记录/更新到错题本`)\n      }\n    },\n\n    recordWrongQuestion(questionId, answer) {\n      const params = {\n        questionId: questionId,\n        lastAnswer: answer\n      }\n\n      addWrongRecord(params).then(res => {\n        if (!res.success) {\n          console.error('记录错题失败:', res.message)\n        }\n      }).catch(err => {\n        console.error('记录错题出错:', err)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.wrong-practice-container {\n  min-height: 100vh;\n  background-color: #f0f2f5;\n}\n\n\n\n.loading-container, .empty-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n  flex-direction: column;\n}\n\n.loading-text {\n  margin-top: 16px;\n  color: #666;\n}\n\n.practice-area {\n  padding: 0 24px 24px;\n}\n\n.practice-area.full-screen-mode {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  background: white;\n  padding: 16px;\n}\n\n.question-container {\n  margin-top: 16px;\n}\n</style>\n"]}]}