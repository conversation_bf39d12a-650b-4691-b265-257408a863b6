{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getAction } from '@/api/manage';\nimport Ellipsis from '@/components/Ellipsis';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nimport { cloneObject, pushIfNotExist } from '@/utils/util';\nexport default {\n  name: 'JSelectBizComponentModal',\n  mixins: [JeecgListMixin],\n  components: {\n    Ellipsis: Ellipsis\n  },\n  props: {\n    value: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    valueKey: {\n      type: String,\n      required: true\n    },\n    multiple: {\n      type: Boolean,\n      default: true\n    },\n    width: {\n      type: Number,\n      default: 900\n    },\n    name: {\n      type: String,\n      default: ''\n    },\n    listUrl: {\n      type: String,\n      required: true,\n      default: ''\n    },\n    // 根据 value 获取显示文本的地址，例如存的是 username，可以通过该地址获取到 realname\n    valueUrl: {\n      type: String,\n      default: ''\n    },\n    displayKey: {\n      type: String,\n      default: null\n    },\n    columns: {\n      type: Array,\n      required: true,\n      default: function _default() {\n        return [];\n      }\n    },\n    // 查询条件Code\n    queryParamCode: {\n      type: String,\n      default: null\n    },\n    // 查询条件文字\n    queryParamText: {\n      type: String,\n      default: null\n    },\n    rowKey: {\n      type: String,\n      default: 'id'\n    },\n    // 过长裁剪长度，设置为 -1 代表不裁剪\n    ellipsisLength: {\n      type: Number,\n      default: 12\n    }\n  },\n  data: function data() {\n    var _this = this;\n    var h = this.$createElement;\n    return {\n      innerValue: [],\n      // 已选择列表\n      selectedTable: {\n        pagination: false,\n        scroll: {\n          y: 240\n        },\n        columns: [_objectSpread(_objectSpread({}, this.columns[0]), {}, {\n          width: this.columns[0].widthRight || this.columns[0].width\n        }), {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          width: 60,\n          scopedSlots: {\n            customRender: 'action'\n          }\n        }],\n        dataSource: []\n      },\n      renderEllipsis: function renderEllipsis(value) {\n        return h(\"ellipsis\", {\n          \"attrs\": {\n            \"length\": _this.ellipsisLength\n          }\n        }, [value]);\n      },\n      url: {\n        list: this.listUrl\n      },\n      /* 分页参数 */\n      ipagination: {\n        current: 1,\n        pageSize: 5,\n        pageSizeOptions: ['5', '10', '20', '30'],\n        showTotal: function showTotal(total, range) {\n          return range[0] + '-' + range[1] + ' 共' + total + '条';\n        },\n        showQuickJumper: true,\n        showSizeChanger: true,\n        total: 0\n      },\n      options: [],\n      dataSourceMap: {}\n    };\n  },\n  computed: {\n    // 表头\n    innerColumns: function innerColumns() {\n      var _this2 = this;\n      var columns = cloneObject(this.columns);\n      columns.forEach(function (column) {\n        // 给所有的列加上过长裁剪\n        if (_this2.ellipsisLength !== -1) {\n          column.customRender = function (text) {\n            return _this2.renderEllipsis(text);\n          };\n        }\n      });\n      return columns;\n    }\n  },\n  watch: {\n    value: {\n      deep: true,\n      immediate: true,\n      handler: function handler(val) {\n        this.innerValue = cloneObject(val);\n        this.selectedRowKeys = [];\n        this.valueWatchHandler(val);\n        this.queryOptionsByValue(val);\n      }\n    },\n    dataSource: {\n      deep: true,\n      handler: function handler(val) {\n        this.emitOptions(val);\n        this.valueWatchHandler(this.innerValue);\n      }\n    },\n    selectedRowKeys: {\n      immediate: true,\n      deep: true,\n      handler: function handler(val) {\n        var _this3 = this;\n        this.selectedTable.dataSource = val.map(function (key) {\n          var _iterator = _createForOfIteratorHelper(_this3.dataSource),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var data = _step.value;\n              if (data[_this3.rowKey] === key) {\n                pushIfNotExist(_this3.innerValue, data[_this3.valueKey]);\n                return data;\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n          var _iterator2 = _createForOfIteratorHelper(_this3.selectedTable.dataSource),\n            _step2;\n          try {\n            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n              var _data = _step2.value;\n              if (_data[_this3.rowKey] === key) {\n                pushIfNotExist(_this3.innerValue, _data[_this3.valueKey]);\n                return _data;\n              }\n            }\n          } catch (err) {\n            _iterator2.e(err);\n          } finally {\n            _iterator2.f();\n          }\n          console.warn('未找到选择的行信息，key：' + key);\n          return {};\n        });\n      }\n    }\n  },\n  methods: {\n    /** 关闭弹窗 */close: function close() {\n      this.$emit('update:visible', false);\n    },\n    valueWatchHandler: function valueWatchHandler(val) {\n      var _this4 = this;\n      val.forEach(function (item) {\n        _this4.dataSource.concat(_this4.selectedTable.dataSource).forEach(function (data) {\n          if (data[_this4.valueKey] === item) {\n            pushIfNotExist(_this4.selectedRowKeys, data[_this4.rowKey]);\n          }\n        });\n      });\n    },\n    queryOptionsByValue: function queryOptionsByValue(value) {\n      var _this5 = this;\n      if (!value || value.length === 0) {\n        return;\n      }\n      // 判断options是否存在value，如果已存在数据就不再请求后台了\n      var notExist = false;\n      var _iterator3 = _createForOfIteratorHelper(value),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var val = _step3.value;\n          var find = false;\n          var _iterator4 = _createForOfIteratorHelper(this.options),\n            _step4;\n          try {\n            for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n              var option = _step4.value;\n              if (val === option.value) {\n                find = true;\n                break;\n              }\n            }\n          } catch (err) {\n            _iterator4.e(err);\n          } finally {\n            _iterator4.f();\n          }\n          if (!find) {\n            notExist = true;\n            break;\n          }\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      if (!notExist) return;\n      getAction(this.valueUrl || this.listUrl, _defineProperty(_defineProperty(_defineProperty({}, this.valueKey, value.join(',') + ','), \"pageNo\", 1), \"pageSize\", value.length)).then(function (res) {\n        if (res.success) {\n          var dataSource = res.result;\n          if (!(dataSource instanceof Array)) {\n            dataSource = res.result.records;\n          }\n          _this5.emitOptions(dataSource, function (data) {\n            pushIfNotExist(_this5.innerValue, data[_this5.valueKey]);\n            pushIfNotExist(_this5.selectedRowKeys, data[_this5.rowKey]);\n            pushIfNotExist(_this5.selectedTable.dataSource, data, _this5.rowKey);\n          });\n        }\n      });\n    },\n    emitOptions: function emitOptions(dataSource, callback) {\n      var _this6 = this;\n      dataSource.forEach(function (data) {\n        var key = data[_this6.valueKey];\n        _this6.dataSourceMap[key] = data;\n        pushIfNotExist(_this6.options, {\n          label: data[_this6.displayKey || _this6.valueKey],\n          value: key\n        }, 'value');\n        typeof callback === 'function' ? callback(data) : '';\n      });\n      this.$emit('options', this.options, this.dataSourceMap);\n    },\n    /** 完成选择 */handleOk: function handleOk() {\n      var _this7 = this;\n      var value = this.selectedTable.dataSource.map(function (data) {\n        return data[_this7.valueKey];\n      });\n      this.$emit('input', value);\n      this.close();\n    },\n    /** 删除已选择的 */handleDeleteSelected: function handleDeleteSelected(record, index) {\n      this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(record[this.rowKey]), 1);\n      this.selectedTable.dataSource.splice(index, 1);\n    },\n    customRowFn: function customRowFn(record) {\n      var _this8 = this;\n      return {\n        on: {\n          click: function click() {\n            var key = record[_this8.rowKey];\n            if (!_this8.multiple) {\n              _this8.selectedRowKeys = [key];\n              _this8.selectedTable.dataSource = [record];\n            } else {\n              var index = _this8.selectedRowKeys.indexOf(key);\n              if (index === -1) {\n                _this8.selectedRowKeys.push(key);\n                _this8.selectedTable.dataSource.push(record);\n              } else {\n                _this8.handleDeleteSelected(record, index);\n              }\n            }\n          }\n        }\n      };\n    }\n  }\n};", {"version": 3, "names": ["getAction", "El<PERSON><PERSON>", "JeecgListMixin", "cloneObject", "pushIfNotExist", "name", "mixins", "components", "props", "value", "type", "Array", "default", "_default", "visible", "Boolean", "valueKey", "String", "required", "multiple", "width", "Number", "listUrl", "valueUrl", "displayKey", "columns", "queryParamCode", "queryParamText", "<PERSON><PERSON><PERSON>", "ellipsis<PERSON><PERSON><PERSON>", "data", "_this", "h", "$createElement", "innerValue", "selectedTable", "pagination", "scroll", "y", "_objectSpread", "widthRight", "title", "dataIndex", "align", "scopedSlots", "customRender", "dataSource", "renderEllipsis", "url", "list", "ipagination", "current", "pageSize", "pageSizeOptions", "showTotal", "total", "range", "showQuickJumper", "showSizeChanger", "options", "dataSourceMap", "computed", "innerColumns", "_this2", "for<PERSON>ach", "column", "text", "watch", "deep", "immediate", "handler", "val", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueWatchHandler", "queryOptionsByValue", "emitOptions", "_this3", "map", "key", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "err", "e", "f", "_iterator2", "_step2", "console", "warn", "methods", "close", "$emit", "_this4", "item", "concat", "_this5", "length", "notExist", "_iterator3", "_step3", "find", "_iterator4", "_step4", "option", "_defineProperty", "join", "then", "res", "success", "result", "records", "callback", "_this6", "label", "handleOk", "_this7", "handleDeleteSelected", "record", "index", "splice", "indexOf", "customRowFn", "_this8", "on", "click", "push"], "sources": ["src/components/jeecgbiz/JSelectBizComponent/JSelectBizComponentModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    centered\n    :title=\"name + '选择'\"\n    :width=\"width\"\n    :visible=\"visible\"\n    @ok=\"handleOk\"\n    @cancel=\"close\"\n    cancelText=\"关闭\">\n\n    <a-row :gutter=\"18\">\n      <a-col :span=\"16\">\n        <!-- 查询区域 -->\n        <div class=\"table-page-search-wrapper\">\n          <a-form layout=\"inline\">\n            <a-row :gutter=\"24\">\n\n              <a-col :span=\"14\">\n                <a-form-item :label=\"(queryParamText||name)\">\n                  <a-input v-model=\"queryParam[queryParamCode||valueKey]\" :placeholder=\"'请输入' + (queryParamText||name)\" @pressEnter=\"searchQuery\"/>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"8\">\n                  <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n                    <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n                    <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n                  </span>\n              </a-col>\n\n            </a-row>\n          </a-form>\n        </div>\n\n        <a-table\n          size=\"small\"\n          bordered\n          :rowKey=\"rowKey\"\n          :columns=\"innerColumns\"\n          :dataSource=\"dataSource\"\n          :pagination=\"ipagination\"\n          :loading=\"loading\"\n          :scroll=\"{ y: 240 }\"\n          :rowSelection=\"{selectedRowKeys, onChange: onSelectChange, type: multiple ? 'checkbox':'radio'}\"\n          :customRow=\"customRowFn\"\n          @change=\"handleTableChange\">\n        </a-table>\n\n      </a-col>\n      <a-col :span=\"8\">\n        <a-card :title=\"'已选' + name\" :bordered=\"false\" :head-style=\"{padding:0}\" :body-style=\"{padding:0}\">\n\n          <a-table size=\"small\" :rowKey=\"rowKey\" bordered v-bind=\"selectedTable\">\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"handleDeleteSelected(record, index)\">删除</a>\n              </span>\n          </a-table>\n\n        </a-card>\n      </a-col>\n    </a-row>\n  </a-modal>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n  import Ellipsis from '@/components/Ellipsis'\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import { cloneObject, pushIfNotExist } from '@/utils/util'\n\n  export default {\n    name: 'JSelectBizComponentModal',\n    mixins: [JeecgListMixin],\n    components: { Ellipsis },\n    props: {\n      value: {\n        type: Array,\n        default: () => []\n      },\n      visible: {\n        type: Boolean,\n        default: false\n      },\n      valueKey: {\n        type: String,\n        required: true\n      },\n      multiple: {\n        type: Boolean,\n        default: true\n      },\n      width: {\n        type: Number,\n        default: 900\n      },\n\n      name: {\n        type: String,\n        default: ''\n      },\n      listUrl: {\n        type: String,\n        required: true,\n        default: ''\n      },\n      // 根据 value 获取显示文本的地址，例如存的是 username，可以通过该地址获取到 realname\n      valueUrl: {\n        type: String,\n        default: ''\n      },\n      displayKey: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true,\n        default: () => []\n      },\n      // 查询条件Code\n      queryParamCode: {\n        type: String,\n        default: null\n      },\n      // 查询条件文字\n      queryParamText: {\n        type: String,\n        default: null\n      },\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 过长裁剪长度，设置为 -1 代表不裁剪\n      ellipsisLength: {\n        type: Number,\n        default: 12\n      },\n    },\n    data() {\n      return {\n        innerValue: [],\n        // 已选择列表\n        selectedTable: {\n          pagination: false,\n          scroll: { y: 240 },\n          columns: [\n            {\n              ...this.columns[0],\n              width: this.columns[0].widthRight || this.columns[0].width,\n            },\n            { title: '操作', dataIndex: 'action', align: 'center', width: 60, scopedSlots: { customRender: 'action' }, }\n          ],\n          dataSource: [],\n        },\n        renderEllipsis: (value) => (<ellipsis length={this.ellipsisLength}>{value}</ellipsis>),\n        url: { list: this.listUrl },\n        /* 分页参数 */\n        ipagination: {\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['5', '10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + '-' + range[1] + ' 共' + total + '条'\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        options: [],\n        dataSourceMap: {},\n      }\n    },\n    computed: {\n      // 表头\n      innerColumns() {\n        let columns = cloneObject(this.columns)\n        columns.forEach(column => {\n          // 给所有的列加上过长裁剪\n          if (this.ellipsisLength !== -1) {\n            column.customRender = (text) => this.renderEllipsis(text)\n          }\n        })\n        return columns\n      },\n    },\n    watch: {\n      value: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          this.innerValue = cloneObject(val)\n          this.selectedRowKeys = []\n          this.valueWatchHandler(val)\n          this.queryOptionsByValue(val)\n        }\n      },\n      dataSource: {\n        deep: true,\n        handler(val) {\n          this.emitOptions(val)\n          this.valueWatchHandler(this.innerValue)\n        }\n      },\n      selectedRowKeys: {\n        immediate: true,\n        deep: true,\n        handler(val) {\n          this.selectedTable.dataSource = val.map(key => {\n            for (let data of this.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            for (let data of this.selectedTable.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            console.warn('未找到选择的行信息，key：' + key)\n            return {}\n          })\n        },\n      }\n    },\n\n    methods: {\n\n      /** 关闭弹窗 */\n      close() {\n        this.$emit('update:visible', false)\n      },\n\n      valueWatchHandler(val) {\n        val.forEach(item => {\n          this.dataSource.concat(this.selectedTable.dataSource).forEach(data => {\n            if (data[this.valueKey] === item) {\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n            }\n          })\n        })\n      },\n\n      queryOptionsByValue(value) {\n        if (!value || value.length === 0) {\n          return\n        }\n        // 判断options是否存在value，如果已存在数据就不再请求后台了\n        let notExist = false\n        for (let val of value) {\n          let find = false\n          for (let option of this.options) {\n            if (val === option.value) {\n              find = true\n              break\n            }\n          }\n          if (!find) {\n            notExist = true\n            break\n          }\n        }\n        if (!notExist) return\n        getAction(this.valueUrl || this.listUrl, {\n          // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确\n          [this.valueKey]: value.join(',') + ',',\n          pageNo: 1,\n          pageSize: value.length\n        }).then((res) => {\n          if (res.success) {\n            let dataSource = res.result\n            if (!(dataSource instanceof Array)) {\n              dataSource = res.result.records\n            }\n            this.emitOptions(dataSource, (data) => {\n              pushIfNotExist(this.innerValue, data[this.valueKey])\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n              pushIfNotExist(this.selectedTable.dataSource, data, this.rowKey)\n            })\n          }\n        })\n      },\n\n      emitOptions(dataSource, callback) {\n        dataSource.forEach(data => {\n          let key = data[this.valueKey]\n          this.dataSourceMap[key] = data\n          pushIfNotExist(this.options, { label: data[this.displayKey || this.valueKey], value: key }, 'value')\n          typeof callback === 'function' ? callback(data) : ''\n        })\n        this.$emit('options', this.options, this.dataSourceMap)\n      },\n\n      /** 完成选择 */\n      handleOk() {\n        let value = this.selectedTable.dataSource.map(data => data[this.valueKey])\n        this.$emit('input', value)\n        this.close()\n      },\n\n      /** 删除已选择的 */\n      handleDeleteSelected(record, index) {\n        this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(record[this.rowKey]), 1)\n        this.selectedTable.dataSource.splice(index, 1)\n      },\n\n      customRowFn(record) {\n        return {\n          on: {\n            click: () => {\n              let key = record[this.rowKey]\n              if (!this.multiple) {\n                this.selectedRowKeys = [key]\n                this.selectedTable.dataSource = [record]\n              } else {\n                let index = this.selectedRowKeys.indexOf(key)\n                if (index === -1) {\n                  this.selectedRowKeys.push(key)\n                  this.selectedTable.dataSource.push(record)\n                } else {\n                  this.handleDeleteSelected(record, index)\n                }\n              }\n            }\n          }\n        }\n      },\n\n    }\n  }\n</script>\n<style lang=\"less\" scoped>\n</style>"], "mappings": ";;;;;;;;;AAgEA,SAAAA,SAAA;AACA,OAAAC,QAAA;AACA,SAAAC,cAAA;AACA,SAAAC,WAAA,EAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAJ,cAAA;EACAK,UAAA;IAAAN,QAAA,EAAAA;EAAA;EACAO,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAC,OAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAT,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAQ,KAAA;MACAV,IAAA,EAAAW,MAAA;MACAT,OAAA;IACA;IAEAP,IAAA;MACAK,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,MAAA;MACAC,QAAA;MACAN,OAAA;IACA;IACA;IACAW,QAAA;MACAb,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAY,UAAA;MACAd,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAa,OAAA;MACAf,IAAA,EAAAC,KAAA;MACAO,QAAA;MACAN,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAa,cAAA;MACAhB,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACA;IACAe,cAAA;MACAjB,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAgB,MAAA;MACAlB,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACA;IACAiB,cAAA;MACAnB,IAAA,EAAAW,MAAA;MACAT,OAAA;IACA;EACA;EACAkB,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,UAAA;MACA;MACAC,aAAA;QACAC,UAAA;QACAC,MAAA;UAAAC,CAAA;QAAA;QACAb,OAAA,GAAAc,aAAA,CAAAA,aAAA,KAEA,KAAAd,OAAA;UACAL,KAAA,OAAAK,OAAA,IAAAe,UAAA,SAAAf,OAAA,IAAAL;QAAA,IAEA;UAAAqB,KAAA;UAAAC,SAAA;UAAAC,KAAA;UAAAvB,KAAA;UAAAwB,WAAA;YAAAC,YAAA;UAAA;QAAA,EACA;QACAC,UAAA;MACA;MACAC,cAAA,WAAAA,eAAAtC,KAAA;QAAA,OAAAuB,CAAA;UAAA;YAAA,UAAAD,KAAA,CAAAF;UAAA;QAAA,IAAApB,KAAA;MAAA;MACAuC,GAAA;QAAAC,IAAA,OAAA3B;MAAA;MACA;MACA4B,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,eAAA;QACAC,SAAA,WAAAA,UAAAC,KAAA,EAAAC,KAAA;UACA,OAAAA,KAAA,YAAAA,KAAA,aAAAD,KAAA;QACA;QACAE,eAAA;QACAC,eAAA;QACAH,KAAA;MACA;MACAI,OAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAtC,OAAA,GAAAtB,WAAA,MAAAsB,OAAA;MACAA,OAAA,CAAAuC,OAAA,WAAAC,MAAA;QACA;QACA,IAAAF,MAAA,CAAAlC,cAAA;UACAoC,MAAA,CAAApB,YAAA,aAAAqB,IAAA;YAAA,OAAAH,MAAA,CAAAhB,cAAA,CAAAmB,IAAA;UAAA;QACA;MACA;MACA,OAAAzC,OAAA;IACA;EACA;EACA0C,KAAA;IACA1D,KAAA;MACA2D,IAAA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAArC,UAAA,GAAA/B,WAAA,CAAAoE,GAAA;QACA,KAAAC,eAAA;QACA,KAAAC,iBAAA,CAAAF,GAAA;QACA,KAAAG,mBAAA,CAAAH,GAAA;MACA;IACA;IACAzB,UAAA;MACAsB,IAAA;MACAE,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAI,WAAA,CAAAJ,GAAA;QACA,KAAAE,iBAAA,MAAAvC,UAAA;MACA;IACA;IACAsC,eAAA;MACAH,SAAA;MACAD,IAAA;MACAE,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAK,MAAA;QACA,KAAAzC,aAAA,CAAAW,UAAA,GAAAyB,GAAA,CAAAM,GAAA,WAAAC,GAAA;UAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAJ,MAAA,CAAA9B,UAAA;YAAAmC,KAAA;UAAA;YAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;cAAA,IAAAtD,IAAA,GAAAmD,KAAA,CAAAxE,KAAA;cACA,IAAAqB,IAAA,CAAA8C,MAAA,CAAAhD,MAAA,MAAAkD,GAAA;gBACA1E,cAAA,CAAAwE,MAAA,CAAA1C,UAAA,EAAAJ,IAAA,CAAA8C,MAAA,CAAA5D,QAAA;gBACA,OAAAc,IAAA;cACA;YACA;UAAA,SAAAuD,GAAA;YAAAN,SAAA,CAAAO,CAAA,CAAAD,GAAA;UAAA;YAAAN,SAAA,CAAAQ,CAAA;UAAA;UAAA,IAAAC,UAAA,GAAAR,0BAAA,CACAJ,MAAA,CAAAzC,aAAA,CAAAW,UAAA;YAAA2C,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAL,CAAA,IAAAC,IAAA;cAAA,IAAAtD,KAAA,GAAA2D,MAAA,CAAAhF,KAAA;cACA,IAAAqB,KAAA,CAAA8C,MAAA,CAAAhD,MAAA,MAAAkD,GAAA;gBACA1E,cAAA,CAAAwE,MAAA,CAAA1C,UAAA,EAAAJ,KAAA,CAAA8C,MAAA,CAAA5D,QAAA;gBACA,OAAAc,KAAA;cACA;YACA;UAAA,SAAAuD,GAAA;YAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;UAAA;YAAAG,UAAA,CAAAD,CAAA;UAAA;UACAG,OAAA,CAAAC,IAAA,oBAAAb,GAAA;UACA;QACA;MACA;IACA;EACA;EAEAc,OAAA;IAEA,WACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;IACA;IAEArB,iBAAA,WAAAA,kBAAAF,GAAA;MAAA,IAAAwB,MAAA;MACAxB,GAAA,CAAAP,OAAA,WAAAgC,IAAA;QACAD,MAAA,CAAAjD,UAAA,CAAAmD,MAAA,CAAAF,MAAA,CAAA5D,aAAA,CAAAW,UAAA,EAAAkB,OAAA,WAAAlC,IAAA;UACA,IAAAA,IAAA,CAAAiE,MAAA,CAAA/E,QAAA,MAAAgF,IAAA;YACA5F,cAAA,CAAA2F,MAAA,CAAAvB,eAAA,EAAA1C,IAAA,CAAAiE,MAAA,CAAAnE,MAAA;UACA;QACA;MACA;IACA;IAEA8C,mBAAA,WAAAA,oBAAAjE,KAAA;MAAA,IAAAyF,MAAA;MACA,KAAAzF,KAAA,IAAAA,KAAA,CAAA0F,MAAA;QACA;MACA;MACA;MACA,IAAAC,QAAA;MAAA,IAAAC,UAAA,GAAArB,0BAAA,CACAvE,KAAA;QAAA6F,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAnB,CAAA,MAAAoB,MAAA,GAAAD,UAAA,CAAAlB,CAAA,IAAAC,IAAA;UAAA,IAAAb,GAAA,GAAA+B,MAAA,CAAA7F,KAAA;UACA,IAAA8F,IAAA;UAAA,IAAAC,UAAA,GAAAxB,0BAAA,CACA,KAAArB,OAAA;YAAA8C,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAAtB,CAAA,MAAAuB,MAAA,GAAAD,UAAA,CAAArB,CAAA,IAAAC,IAAA;cAAA,IAAAsB,MAAA,GAAAD,MAAA,CAAAhG,KAAA;cACA,IAAA8D,GAAA,KAAAmC,MAAA,CAAAjG,KAAA;gBACA8F,IAAA;gBACA;cACA;YACA;UAAA,SAAAlB,GAAA;YAAAmB,UAAA,CAAAlB,CAAA,CAAAD,GAAA;UAAA;YAAAmB,UAAA,CAAAjB,CAAA;UAAA;UACA,KAAAgB,IAAA;YACAH,QAAA;YACA;UACA;QACA;MAAA,SAAAf,GAAA;QAAAgB,UAAA,CAAAf,CAAA,CAAAD,GAAA;MAAA;QAAAgB,UAAA,CAAAd,CAAA;MAAA;MACA,KAAAa,QAAA;MACApG,SAAA,MAAAuB,QAAA,SAAAD,OAAA,EAAAqF,eAAA,CAAAA,eAAA,CAAAA,eAAA,KAEA,KAAA3F,QAAA,EAAAP,KAAA,CAAAmG,IAAA,wBACA,gBACAnG,KAAA,CAAA0F,MAAA,CACA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA,IAAAjE,UAAA,GAAAgE,GAAA,CAAAE,MAAA;UACA,MAAAlE,UAAA,YAAAnC,KAAA;YACAmC,UAAA,GAAAgE,GAAA,CAAAE,MAAA,CAAAC,OAAA;UACA;UACAf,MAAA,CAAAvB,WAAA,CAAA7B,UAAA,YAAAhB,IAAA;YACA1B,cAAA,CAAA8F,MAAA,CAAAhE,UAAA,EAAAJ,IAAA,CAAAoE,MAAA,CAAAlF,QAAA;YACAZ,cAAA,CAAA8F,MAAA,CAAA1B,eAAA,EAAA1C,IAAA,CAAAoE,MAAA,CAAAtE,MAAA;YACAxB,cAAA,CAAA8F,MAAA,CAAA/D,aAAA,CAAAW,UAAA,EAAAhB,IAAA,EAAAoE,MAAA,CAAAtE,MAAA;UACA;QACA;MACA;IACA;IAEA+C,WAAA,WAAAA,YAAA7B,UAAA,EAAAoE,QAAA;MAAA,IAAAC,MAAA;MACArE,UAAA,CAAAkB,OAAA,WAAAlC,IAAA;QACA,IAAAgD,GAAA,GAAAhD,IAAA,CAAAqF,MAAA,CAAAnG,QAAA;QACAmG,MAAA,CAAAvD,aAAA,CAAAkB,GAAA,IAAAhD,IAAA;QACA1B,cAAA,CAAA+G,MAAA,CAAAxD,OAAA;UAAAyD,KAAA,EAAAtF,IAAA,CAAAqF,MAAA,CAAA3F,UAAA,IAAA2F,MAAA,CAAAnG,QAAA;UAAAP,KAAA,EAAAqE;QAAA;QACA,OAAAoC,QAAA,kBAAAA,QAAA,CAAApF,IAAA;MACA;MACA,KAAAgE,KAAA,iBAAAnC,OAAA,OAAAC,aAAA;IACA;IAEA,WACAyD,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAA7G,KAAA,QAAA0B,aAAA,CAAAW,UAAA,CAAA+B,GAAA,WAAA/C,IAAA;QAAA,OAAAA,IAAA,CAAAwF,MAAA,CAAAtG,QAAA;MAAA;MACA,KAAA8E,KAAA,UAAArF,KAAA;MACA,KAAAoF,KAAA;IACA;IAEA,aACA0B,oBAAA,WAAAA,qBAAAC,MAAA,EAAAC,KAAA;MACA,KAAAjD,eAAA,CAAAkD,MAAA,MAAAlD,eAAA,CAAAmD,OAAA,CAAAH,MAAA,MAAA5F,MAAA;MACA,KAAAO,aAAA,CAAAW,UAAA,CAAA4E,MAAA,CAAAD,KAAA;IACA;IAEAG,WAAA,WAAAA,YAAAJ,MAAA;MAAA,IAAAK,MAAA;MACA;QACAC,EAAA;UACAC,KAAA,WAAAA,MAAA;YACA,IAAAjD,GAAA,GAAA0C,MAAA,CAAAK,MAAA,CAAAjG,MAAA;YACA,KAAAiG,MAAA,CAAA1G,QAAA;cACA0G,MAAA,CAAArD,eAAA,IAAAM,GAAA;cACA+C,MAAA,CAAA1F,aAAA,CAAAW,UAAA,IAAA0E,MAAA;YACA;cACA,IAAAC,KAAA,GAAAI,MAAA,CAAArD,eAAA,CAAAmD,OAAA,CAAA7C,GAAA;cACA,IAAA2C,KAAA;gBACAI,MAAA,CAAArD,eAAA,CAAAwD,IAAA,CAAAlD,GAAA;gBACA+C,MAAA,CAAA1F,aAAA,CAAAW,UAAA,CAAAkF,IAAA,CAAAR,MAAA;cACA;gBACAK,MAAA,CAAAN,oBAAA,CAAAC,MAAA,EAAAC,KAAA;cACA;YACA;UACA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}