{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue?vue&type=template&id=cbdab026&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :okButtonProps=\"{ props: {disabled: disableSubmit} }\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"航班号\"\n        hasFeedback>\n        <a-input\n          placeholder=\"请输入航班号\"\n          :readOnly=\"disableSubmit\"\n          v-decorator=\"['ticketCode', {rules:[{ required: true,message: '请输入航班号!'}]}]\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"航班时间\"\n        hasFeedback>\n        <j-date :trigger-change=\"true\"  v-decorator=\"['tickectDate',{rules:[{ required: true,message: '请输入航班号!'}]}]\"></j-date>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单号码\"\n        v-model=\"this.orderId\"\n        :hidden=\"hiding\"\n        hasFeedback>\n        <a-input v-decorator=\"[ 'orderId', {}]\" disabled=\"disabled\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"创建人\"\n        :hidden=\"hiding\"\n        hasFeedback>\n        <a-input v-decorator=\"[ 'createBy', {}]\" :readOnly=\"disableSubmit\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"创建时间\"\n        :hidden=\"hiding\"\n        hasFeedback>\n        <a-input v-decorator=\"[ 'createTime', {}]\" :readOnly=\"disableSubmit\"/>\n      </a-form-item>\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}