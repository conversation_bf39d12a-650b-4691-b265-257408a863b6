{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\HttpTrace.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\HttpTrace.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport moment from 'moment';\nimport { getAction } from '@/api/manage';\nmoment.locale('zh-cn');\nexport default {\n  data: function data() {\n    return {\n      advanced: false,\n      dataSource: [],\n      pagination: {\n        defaultPageSize: 10,\n        defaultCurrent: 1,\n        pageSizeOptions: ['10', '20', '30', '40', '100'],\n        showQuickJumper: true,\n        showSizeChanger: true,\n        showTotal: function showTotal(total, range) {\n          return \"\\u663E\\u793A \".concat(range[0], \" ~ \").concat(range[1], \" \\u6761\\u8BB0\\u5F55\\uFF0C\\u5171 \").concat(total, \" \\u6761\\u8BB0\\u5F55\");\n        }\n      },\n      loading: true,\n      tableLoading: true\n    };\n  },\n  computed: {\n    columns: function columns() {\n      return [{\n        title: '请求时间',\n        dataIndex: 'timestamp',\n        customRender: function customRender(text) {\n          return moment(text).format('YYYY-MM-DD HH:mm:ss');\n        }\n      }, {\n        title: '请求方法',\n        dataIndex: 'request.method',\n        scopedSlots: {\n          customRender: 'requestMethod'\n        },\n        filters: [{\n          text: 'GET',\n          value: 'GET'\n        }, {\n          text: 'POST',\n          value: 'POST'\n        }, {\n          text: 'PUT',\n          value: 'PUT'\n        }, {\n          text: 'DELETE',\n          value: 'DELETE'\n        }],\n        filterMultiple: true,\n        onFilter: function onFilter(value, record) {\n          return record.request.method.includes(value);\n        }\n      }, {\n        title: '请求URL',\n        dataIndex: 'request.uri',\n        customRender: function customRender(text) {\n          return text.split('?')[0];\n        }\n      }, {\n        title: '响应状态',\n        dataIndex: 'response.status',\n        scopedSlots: {\n          customRender: 'responseStatus'\n        }\n      }, {\n        title: '请求耗时',\n        dataIndex: 'timeTaken',\n        scopedSlots: {\n          customRender: 'timeTaken'\n        }\n      }];\n    }\n  },\n  mounted: function mounted() {\n    this.fetch();\n  },\n  methods: {\n    handleClickUpdate: function handleClickUpdate() {\n      this.fetch();\n    },\n    handleTableChange: function handleTableChange() {\n      this.fetch();\n    },\n    fetch: function fetch() {\n      var _this = this;\n      this.tableLoading = true;\n      getAction('actuator/httptrace').then(function (data) {\n        var filterData = [];\n        var _iterator = _createForOfIteratorHelper(data.traces),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var d = _step.value;\n            if (d.request.method !== 'OPTIONS' && d.request.uri.indexOf('httptrace') === -1) {\n              filterData.push(d);\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n        _this.dataSource = filterData;\n      }).catch(function (e) {\n        console.error(e);\n        _this.$message.error('获取HTTP信息失败');\n      }).finally(function () {\n        _this.loading = false;\n        _this.tableLoading = false;\n      });\n    }\n  }\n};", {"version": 3, "names": ["moment", "getAction", "locale", "data", "advanced", "dataSource", "pagination", "defaultPageSize", "defaultCurrent", "pageSizeOptions", "showQuickJumper", "showSizeChanger", "showTotal", "total", "range", "concat", "loading", "tableLoading", "computed", "columns", "title", "dataIndex", "customRender", "text", "format", "scopedSlots", "filters", "value", "filterMultiple", "onFilter", "record", "request", "method", "includes", "split", "mounted", "fetch", "methods", "handleClickUpdate", "handleTableChange", "_this", "then", "filterData", "_iterator", "_createForOfIteratorHelper", "traces", "_step", "s", "n", "done", "d", "uri", "indexOf", "push", "err", "e", "f", "catch", "console", "error", "$message", "finally"], "sources": ["src/views/modules/monitor/HttpTrace.vue"], "sourcesContent": ["<template>\n  <a-skeleton active :loading=\"loading\" :paragraph=\"{rows: 17}\">\n    <a-card :bordered=\"false\" class=\"card-area\">\n\n      <a-alert type=\"info\" :showIcon=\"true\">\n        <div slot=\"message\">\n          共追踪到 {{ dataSource.length }} 条近期HTTP请求记录\n          <a-divider type=\"vertical\"/>\n          <a @click=\"handleClickUpdate\">立即刷新</a>\n        </div>\n      </a-alert>\n\n      <!-- 表格区域 -->\n      <a-table\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"pagination\"\n        :loading=\"tableLoading\"\n        :scroll=\"{ x: 900 }\"\n        style=\"margin-top: 20px;\"\n        @change=\"handleTableChange\">\n\n        <template slot=\"timeTaken\" slot-scope=\"text\">\n          <a-tag v-if=\"text < 500\" color=\"green\">{{ text }} ms</a-tag>\n          <a-tag v-else-if=\"text < 1000\" color=\"cyan\">{{ text }} ms</a-tag>\n          <a-tag v-else-if=\"text < 1500\" color=\"orange\">{{ text }} ms</a-tag>\n          <a-tag v-else color=\"red\">{{ text }} ms</a-tag>\n        </template>\n\n        <template slot=\"responseStatus\" slot-scope=\"text\">\n          <a-tag v-if=\"text < 200\" color=\"pink\">{{ text }} </a-tag>\n          <a-tag v-else-if=\"text < 201\" color=\"green\">{{ text }} </a-tag>\n          <a-tag v-else-if=\"text < 399\" color=\"cyan\">{{ text }} </a-tag>\n          <a-tag v-else-if=\"text < 403\" color=\"orange\">{{ text }} </a-tag>\n          <a-tag v-else-if=\"text < 501\" color=\"red\">{{ text }} </a-tag>\n          <span v-else>{{ text }}</span>\n        </template>\n\n        <template slot=\"requestMethod\" slot-scope=\"text\">\n          <a-tag v-if=\"text === 'GET'\" color=\"#87d068\">{{ text }}</a-tag>\n          <a-tag v-else-if=\"text === 'POST'\" color=\"#2db7f5\">{{ text }}</a-tag>\n          <a-tag v-else-if=\"text === 'PUT'\" color=\"#ffba5a\">{{ text }}</a-tag>\n          <a-tag v-else-if=\"text === 'DELETE'\" color=\"#f50\">{{ text }}</a-tag>\n          <span v-else>{{ text }} ms</span>\n        </template>\n\n      </a-table>\n\n    </a-card>\n  </a-skeleton>\n</template>\n\n<script>\n  import moment from 'moment'\n  import { getAction } from '@/api/manage'\n\n  moment.locale('zh-cn')\n\n  export default {\n    data() {\n      return {\n        advanced: false,\n        dataSource: [],\n        pagination: {\n          defaultPageSize: 10,\n          defaultCurrent: 1,\n          pageSizeOptions: ['10', '20', '30', '40', '100'],\n          showQuickJumper: true,\n          showSizeChanger: true,\n          showTotal: (total, range) => `显示 ${range[0]} ~ ${range[1]} 条记录，共 ${total} 条记录`\n        },\n        loading: true,\n        tableLoading: true\n      }\n    },\n    computed: {\n      columns() {\n        return [{\n          title: '请求时间',\n          dataIndex: 'timestamp',\n          customRender(text) {\n            return moment(text).format('YYYY-MM-DD HH:mm:ss')\n          }\n        }, {\n          title: '请求方法',\n          dataIndex: 'request.method',\n          scopedSlots: { customRender: 'requestMethod' },\n          filters: [\n            { text: 'GET', value: 'GET' },\n            { text: 'POST', value: 'POST' },\n            { text: 'PUT', value: 'PUT' },\n            { text: 'DELETE', value: 'DELETE' }\n          ],\n          filterMultiple: true,\n          onFilter: (value, record) => record.request.method.includes(value)\n        }, {\n          title: '请求URL',\n          dataIndex: 'request.uri',\n          customRender(text) {\n            return text.split('?')[0]\n          }\n        }, {\n          title: '响应状态',\n          dataIndex: 'response.status',\n          scopedSlots: { customRender: 'responseStatus' }\n        }, {\n          title: '请求耗时',\n          dataIndex: 'timeTaken',\n          scopedSlots: { customRender: 'timeTaken' }\n        }]\n      }\n    },\n    mounted() {\n      this.fetch()\n    },\n    methods: {\n\n      handleClickUpdate() {\n        this.fetch()\n      },\n\n      handleTableChange() {\n        this.fetch()\n      },\n\n      fetch() {\n        this.tableLoading = true\n        getAction('actuator/httptrace').then((data) => {\n          let filterData = []\n          for (let d of data.traces) {\n            if (d.request.method !== 'OPTIONS' && d.request.uri.indexOf('httptrace') === -1) {\n              filterData.push(d)\n            }\n          }\n          this.dataSource = filterData\n        }).catch((e) => {\n          console.error(e)\n          this.$message.error('获取HTTP信息失败')\n        }).finally(() => {\n          this.loading = false\n          this.tableLoading = false\n        })\n      }\n\n    }\n  }\n</script>\n<style></style>\n"], "mappings": ";;;AAqDA,OAAAA,MAAA;AACA,SAAAC,SAAA;AAEAD,MAAA,CAAAE,MAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,UAAA;MACAC,UAAA;QACAC,eAAA;QACAC,cAAA;QACAC,eAAA;QACAC,eAAA;QACAC,eAAA;QACAC,SAAA,WAAAA,UAAAC,KAAA,EAAAC,KAAA;UAAA,uBAAAC,MAAA,CAAAD,KAAA,YAAAC,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAF,KAAA;QAAA;MACA;MACAG,OAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA;QACAC,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAvB,MAAA,CAAAuB,IAAA,EAAAC,MAAA;QACA;MACA;QACAJ,KAAA;QACAC,SAAA;QACAI,WAAA;UAAAH,YAAA;QAAA;QACAI,OAAA,GACA;UAAAH,IAAA;UAAAI,KAAA;QAAA,GACA;UAAAJ,IAAA;UAAAI,KAAA;QAAA,GACA;UAAAJ,IAAA;UAAAI,KAAA;QAAA,GACA;UAAAJ,IAAA;UAAAI,KAAA;QAAA,EACA;QACAC,cAAA;QACAC,QAAA,WAAAA,SAAAF,KAAA,EAAAG,MAAA;UAAA,OAAAA,MAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAN,KAAA;QAAA;MACA;QACAP,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAW,KAAA;QACA;MACA;QACAd,KAAA;QACAC,SAAA;QACAI,WAAA;UAAAH,YAAA;QAAA;MACA;QACAF,KAAA;QACAC,SAAA;QACAI,WAAA;UAAAH,YAAA;QAAA;MACA;IACA;EACA;EACAa,OAAA,WAAAA,QAAA;IACA,KAAAC,KAAA;EACA;EACAC,OAAA;IAEAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,KAAA;IACA;IAEAG,iBAAA,WAAAA,kBAAA;MACA,KAAAH,KAAA;IACA;IAEAA,KAAA,WAAAA,MAAA;MAAA,IAAAI,KAAA;MACA,KAAAvB,YAAA;MACAhB,SAAA,uBAAAwC,IAAA,WAAAtC,IAAA;QACA,IAAAuC,UAAA;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAzC,IAAA,CAAA0C,MAAA;UAAAC,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;YAAA,IAAAC,CAAA,GAAAJ,KAAA,CAAAnB,KAAA;YACA,IAAAuB,CAAA,CAAAnB,OAAA,CAAAC,MAAA,kBAAAkB,CAAA,CAAAnB,OAAA,CAAAoB,GAAA,CAAAC,OAAA;cACAV,UAAA,CAAAW,IAAA,CAAAH,CAAA;YACA;UACA;QAAA,SAAAI,GAAA;UAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;QAAA;UAAAX,SAAA,CAAAa,CAAA;QAAA;QACAhB,KAAA,CAAAnC,UAAA,GAAAqC,UAAA;MACA,GAAAe,KAAA,WAAAF,CAAA;QACAG,OAAA,CAAAC,KAAA,CAAAJ,CAAA;QACAf,KAAA,CAAAoB,QAAA,CAAAD,KAAA;MACA,GAAAE,OAAA;QACArB,KAAA,CAAAxB,OAAA;QACAwB,KAAA,CAAAvB,YAAA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}