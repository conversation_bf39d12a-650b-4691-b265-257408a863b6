{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\ChartCard.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\ChartCard.vue", "mtime": 1752894786327}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./ChartCard.vue?vue&type=template&id=d2662de4&scoped=true\"\nimport script from \"./ChartCard.vue?vue&type=script&lang=js\"\nexport * from \"./ChartCard.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChartCard.vue?vue&type=style&index=0&id=d2662de4&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d2662de4\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('d2662de4')) {\n      api.createRecord('d2662de4', component.options)\n    } else {\n      api.reload('d2662de4', component.options)\n    }\n    module.hot.accept(\"./ChartCard.vue?vue&type=template&id=d2662de4&scoped=true\", function () {\n      api.rerender('d2662de4', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/ChartCard.vue\"\nexport default component.exports"]}