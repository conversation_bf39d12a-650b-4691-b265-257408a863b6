{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue?vue&type=template&id=9078f37c&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n  <a-card :bordered=\"false\">\n    <a-row>\n      <a-col :sm=\"8\" :xs=\"24\">\n        <head-info title=\"我的待办\" content=\"8个任务\" :bordered=\"true\"/>\n      </a-col>\n      <a-col :sm=\"8\" :xs=\"24\">\n        <head-info title=\"本周任务平均处理时间\" content=\"32分钟\" :bordered=\"true\"/>\n      </a-col>\n      <a-col :sm=\"8\" :xs=\"24\">\n        <head-info title=\"本周完成任务数\" content=\"24个\"/>\n      </a-col>\n    </a-row>\n  </a-card>\n\n  <a-card\n    style=\"margin-top: 24px\"\n    :bordered=\"false\"\n    title=\"标准列表\">\n\n    <div slot=\"extra\">\n      <a-radio-group>\n        <a-radio-button>全部</a-radio-button>\n        <a-radio-button>进行中</a-radio-button>\n        <a-radio-button>等待中</a-radio-button>\n      </a-radio-group>\n      <a-input-search style=\"margin-left: 16px; width: 272px;\" />\n    </div>\n\n    <div class=\"operate\">\n      <a-button type=\"dashed\" style=\"width: 100%\" icon=\"plus\">添加</a-button>\n    </div>\n\n    <a-list size=\"large\" :pagination=\"{showSizeChanger: true, showQuickJumper: true, pageSize: 5, total: 50}\">\n      <a-list-item :key=\"index\" v-for=\"(item, index) in data\">\n        <a-list-item-meta :description=\"item.description\">\n          <a-avatar slot=\"avatar\" size=\"large\" shape=\"square\" :src=\"item.avatar\"/>\n          <a slot=\"title\">{{ item.title }}</a>\n        </a-list-item-meta>\n        <div slot=\"actions\">\n          <a>编辑</a>\n        </div>\n        <div slot=\"actions\">\n          <a-dropdown>\n            <a-menu slot=\"overlay\">\n              <a-menu-item><a>编辑</a></a-menu-item>\n              <a-menu-item><a>删除</a></a-menu-item>\n            </a-menu>\n            <a>更多<a-icon type=\"down\"/></a>\n          </a-dropdown>\n        </div>\n        <div class=\"list-content\">\n          <div class=\"list-content-item\">\n            <span>Owner</span>\n            <p>{{ item.owner }}</p>\n          </div>\n          <div class=\"list-content-item\">\n            <span>开始时间</span>\n            <p>{{ item.startAt }}</p>\n          </div>\n          <div class=\"list-content-item\">\n            <a-progress :percent=\"item.progress.value\" :status=\"!item.progress.status ? null : item.progress.status\" style=\"width: 180px\" />\n          </div>\n        </div>\n      </a-list-item>\n    </a-list>\n\n  </a-card>\n</div>\n", null]}