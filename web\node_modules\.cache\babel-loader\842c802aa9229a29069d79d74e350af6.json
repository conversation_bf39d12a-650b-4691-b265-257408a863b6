{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ThreeLinkage.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ThreeLinkage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { FormTypes } from '@/utils/JEditableTableUtil';\nimport JEditableTable from '@/components/jeecg/JEditableTable';\nexport default {\n  name: 'ThreeLinkage',\n  components: {\n    JEditableTable: JEditableTable\n  },\n  data: function data() {\n    return {\n      columns: [{\n        title: '省/直辖市/自治区',\n        key: 's1',\n        type: FormTypes.select,\n        width: '240px',\n        options: [],\n        placeholder: '请选择${title}'\n      }, {\n        title: '市',\n        key: 's2',\n        type: FormTypes.select,\n        width: '240px',\n        options: [],\n        placeholder: '请选择${title}'\n      }, {\n        title: '县/区',\n        key: 's3',\n        type: FormTypes.select,\n        width: '240px',\n        options: [],\n        placeholder: '请选择${title}'\n      }],\n      dataSource: [],\n      mockData: [{\n        label: '北京市',\n        value: '110000',\n        parent: null\n      }, {\n        label: '天津市',\n        value: '120000',\n        parent: null\n      }, {\n        label: '河北省',\n        value: '130000',\n        parent: null\n      }, {\n        label: '上海市',\n        value: '310000',\n        parent: null\n      }, {\n        label: '北京市',\n        value: '110100',\n        parent: '110000'\n      }, {\n        label: '天津市市',\n        value: '120100',\n        parent: '120000'\n      }, {\n        label: '石家庄市',\n        value: '130100',\n        parent: '130000'\n      }, {\n        label: '唐山市',\n        value: '130200',\n        parent: '130000'\n      }, {\n        label: '秦皇岛市',\n        value: '130300',\n        parent: '130000'\n      }, {\n        label: '上海市',\n        value: '310100',\n        parent: '310000'\n      }, {\n        label: '东城区',\n        value: '110101',\n        parent: '110100'\n      }, {\n        label: '西城区',\n        value: '110102',\n        parent: '110100'\n      }, {\n        label: '朝阳区',\n        value: '110105',\n        parent: '110100'\n      }, {\n        label: '和平区',\n        value: '120101',\n        parent: '120000'\n      }, {\n        label: '河东区',\n        value: '120102',\n        parent: '120000'\n      }, {\n        label: '河西区',\n        value: '120103',\n        parent: '120000'\n      }, {\n        label: '黄浦区',\n        value: '310101',\n        parent: '310100'\n      }, {\n        label: '徐汇区',\n        value: '310104',\n        parent: '310100'\n      }, {\n        label: '长宁区',\n        value: '310105',\n        parent: '310100'\n      }, {\n        label: '长安区',\n        value: '130102',\n        parent: '130100'\n      }, {\n        label: '桥西区',\n        value: '130104',\n        parent: '130100'\n      }, {\n        label: '新华区',\n        value: '130105',\n        parent: '130100'\n      }, {\n        label: '路南区',\n        value: '130202',\n        parent: '130200'\n      }, {\n        label: '路北区',\n        value: '130203',\n        parent: '130200'\n      }, {\n        label: '古冶区',\n        value: '130204',\n        parent: '130200'\n      }, {\n        label: '海港区',\n        value: '130302',\n        parent: '130300'\n      }, {\n        label: '山海关区',\n        value: '130303',\n        parent: '130300'\n      }, {\n        label: '北戴河区',\n        value: '130304',\n        parent: '130300'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    // 初始化数据\n    this.columns[0].options = this.request(null);\n  },\n  methods: {\n    request: function request(parentId) {\n      return this.mockData.filter(function (i) {\n        return i.parent === parentId;\n      });\n    },\n    /** 当选项被改变时，联动其他组件 */handleValueChange: function handleValueChange(event) {\n      var type = event.type,\n        row = event.row,\n        column = event.column,\n        value = event.value,\n        target = event.target;\n      if (type === FormTypes.select) {\n        // 第一列\n        if (column.key === 's1') {\n          // 设置第二列的 options\n          this.columns[1].options = this.request(value);\n          // 清空后两列的数据\n          target.setValues([{\n            rowKey: row.id,\n            values: {\n              s2: '',\n              s3: ''\n            }\n          }]);\n          this.columns[2].options = [];\n        } else\n          // 第二列\n          if (column.key === 's2') {\n            this.columns[2].options = this.request(value);\n            target.setValues([{\n              rowKey: row.id,\n              values: {\n                s3: ''\n              }\n            }]);\n          }\n      }\n    }\n  }\n};", {"version": 3, "names": ["FormTypes", "JEditableTable", "name", "components", "data", "columns", "title", "key", "type", "select", "width", "options", "placeholder", "dataSource", "mockData", "label", "value", "parent", "mounted", "request", "methods", "parentId", "filter", "i", "handleValueChange", "event", "row", "column", "target", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "id", "values", "s2", "s3"], "sources": ["src/views/jeecg/modules/JEditableTable/ThreeLinkage.vue"], "sourcesContent": ["<template>\n  <j-editable-table\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :rowNumber=\"true\"\n    :actionButton=\"true\"\n    :rowSelection=\"true\"\n    :maxHeight=\"400\"\n    @valueChange=\"handleValueChange\"\n  />\n</template>\n\n<script>\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'ThreeLinkage',\n    components: { JEditableTable },\n    data() {\n      return {\n        columns: [\n          {\n            title: '省/直辖市/自治区',\n            key: 's1',\n            type: FormTypes.select,\n            width: '240px',\n            options: [],\n            placeholder: '请选择${title}'\n          },\n          {\n            title: '市',\n            key: 's2',\n            type: FormTypes.select,\n            width: '240px',\n            options: [],\n            placeholder: '请选择${title}'\n          },\n          {\n            title: '县/区',\n            key: 's3',\n            type: FormTypes.select,\n            width: '240px',\n            options: [],\n            placeholder: '请选择${title}'\n          }\n        ],\n        dataSource: [],\n\n        mockData: [\n          { label: '北京市', value: '110000', parent: null },\n          { label: '天津市', value: '120000', parent: null },\n          { label: '河北省', value: '130000', parent: null },\n          { label: '上海市', value: '310000', parent: null },\n\n          { label: '北京市', value: '110100', parent: '110000' },\n          { label: '天津市市', value: '120100', parent: '120000' },\n          { label: '石家庄市', value: '130100', parent: '130000' },\n          { label: '唐山市', value: '130200', parent: '130000' },\n          { label: '秦皇岛市', value: '130300', parent: '130000' },\n          { label: '上海市', value: '310100', parent: '310000' },\n\n          { label: '东城区', value: '110101', parent: '110100' },\n          { label: '西城区', value: '110102', parent: '110100' },\n          { label: '朝阳区', value: '110105', parent: '110100' },\n          { label: '和平区', value: '120101', parent: '120000' },\n          { label: '河东区', value: '120102', parent: '120000' },\n          { label: '河西区', value: '120103', parent: '120000' },\n          { label: '黄浦区', value: '310101', parent: '310100' },\n          { label: '徐汇区', value: '310104', parent: '310100' },\n          { label: '长宁区', value: '310105', parent: '310100' },\n          { label: '长安区', value: '130102', parent: '130100' },\n          { label: '桥西区', value: '130104', parent: '130100' },\n          { label: '新华区', value: '130105', parent: '130100' },\n          { label: '路南区', value: '130202', parent: '130200' },\n          { label: '路北区', value: '130203', parent: '130200' },\n          { label: '古冶区', value: '130204', parent: '130200' },\n          { label: '海港区', value: '130302', parent: '130300' },\n          { label: '山海关区', value: '130303', parent: '130300' },\n          { label: '北戴河区', value: '130304', parent: '130300' },\n        ]\n      }\n    },\n    mounted() {\n      // 初始化数据\n      this.columns[0].options = this.request(null)\n    },\n    methods: {\n\n      request(parentId) {\n        return this.mockData.filter(i => i.parent === parentId)\n      },\n\n      /** 当选项被改变时，联动其他组件 */\n      handleValueChange(event) {\n        const { type, row, column, value, target } = event\n\n        if (type === FormTypes.select) {\n\n          // 第一列\n          if (column.key === 's1') {\n            // 设置第二列的 options\n            this.columns[1].options = this.request(value)\n            // 清空后两列的数据\n            target.setValues([{\n              rowKey: row.id,\n              values: { s2: '', s3: '' }\n            }])\n            this.columns[2].options = []\n          } else\n          // 第二列\n          if (column.key === 's2') {\n            this.columns[2].options = this.request(value)\n            target.setValues([{\n              rowKey: row.id,\n              values: { s3: '' }\n            }])\n          }\n        }\n\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAaA,SAAAA,SAAA;AACA,OAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,cAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAS,MAAA;QACAC,KAAA;QACAC,OAAA;QACAC,WAAA;MACA,GACA;QACAN,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAS,MAAA;QACAC,KAAA;QACAC,OAAA;QACAC,WAAA;MACA,GACA;QACAN,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAS,MAAA;QACAC,KAAA;QACAC,OAAA;QACAC,WAAA;MACA,EACA;MACAC,UAAA;MAEAC,QAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GAEA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GAEA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,MAAA;MAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAb,OAAA,IAAAM,OAAA,QAAAQ,OAAA;EACA;EACAC,OAAA;IAEAD,OAAA,WAAAA,QAAAE,QAAA;MACA,YAAAP,QAAA,CAAAQ,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAN,MAAA,KAAAI,QAAA;MAAA;IACA;IAEA,qBACAG,iBAAA,WAAAA,kBAAAC,KAAA;MACA,IAAAjB,IAAA,GAAAiB,KAAA,CAAAjB,IAAA;QAAAkB,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAX,KAAA,GAAAS,KAAA,CAAAT,KAAA;QAAAY,MAAA,GAAAH,KAAA,CAAAG,MAAA;MAEA,IAAApB,IAAA,KAAAR,SAAA,CAAAS,MAAA;QAEA;QACA,IAAAkB,MAAA,CAAApB,GAAA;UACA;UACA,KAAAF,OAAA,IAAAM,OAAA,QAAAQ,OAAA,CAAAH,KAAA;UACA;UACAY,MAAA,CAAAC,SAAA;YACAC,MAAA,EAAAJ,GAAA,CAAAK,EAAA;YACAC,MAAA;cAAAC,EAAA;cAAAC,EAAA;YAAA;UACA;UACA,KAAA7B,OAAA,IAAAM,OAAA;QACA;UACA;UACA,IAAAgB,MAAA,CAAApB,GAAA;YACA,KAAAF,OAAA,IAAAM,OAAA,QAAAQ,OAAA,CAAAH,KAAA;YACAY,MAAA,CAAAC,SAAA;cACAC,MAAA,EAAAJ,GAAA,CAAAK,EAAA;cACAC,MAAA;gBAAAE,EAAA;cAAA;YACA;UACA;MACA;IAEA;EAEA;AACA", "ignoreList": []}]}