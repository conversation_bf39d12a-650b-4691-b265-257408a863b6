{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport {getAction} from '@/api/manage'\n\n  export default {\n    name: \"Analysis\",\n    components: {\n\n    },\n    data() {\n      return {\n         brandName: this.$store.getters.sysConfig.brandName,\n         html: ''\n      }\n    },\n    created() {\n      getAction(\"/sys/config/getConfig?key=_indexHtml\").then(res=>{\n        if(res.success){\n          this.html = res.result\n        }\n      })\n    },\n    methods: {\n\n    }\n  }\n", {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";AAQA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "Index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <a-card :bordered=\"null\">\n    <div v-if=\"html\" v-html=\"html\"></div>\n    <h1 v-else>欢迎使用{{brandName}}</h1>\n  </a-card>\n</template>\n\n<script>\nimport {getAction} from '@/api/manage'\n\n  export default {\n    name: \"Analysis\",\n    components: {\n\n    },\n    data() {\n      return {\n         brandName: this.$store.getters.sysConfig.brandName,\n         html: ''\n      }\n    },\n    created() {\n      getAction(\"/sys/config/getConfig?key=_indexHtml\").then(res=>{\n        if(res.success){\n          this.html = res.result\n        }\n      })\n    },\n    methods: {\n\n    }\n  }\n</script>"]}]}