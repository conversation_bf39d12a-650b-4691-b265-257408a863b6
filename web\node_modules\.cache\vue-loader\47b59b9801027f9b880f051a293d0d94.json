{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SplitPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SplitPanel.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import SplitPanelModal from './modules/SplitPanelModal'\n\n  export default {\n    name: 'SplitPanel',\n    components:{\n      SplitPanelModal,\n    },\n    data() {\n      return {\n        description: '分屏',\n      }\n    },\n    methods:{\n      splitPane(){\n        this.$refs.splitPanelModal.show();\n      }\n    }\n  }\n", {"version": 3, "sources": ["SplitPanel.vue"], "names": [], "mappings": ";AASA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SplitPanel.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card>\n    <a-button @click=\"splitPane\" type=\"primary\" icon=\"desktop\">点我分屏</a-button>\n\n    <split-panel-modal ref=\"splitPanelModal\"></split-panel-modal>\n  </a-card>\n</template>\n\n<script>\n  import SplitPanelModal from './modules/SplitPanelModal'\n\n  export default {\n    name: 'SplitPanel',\n    components:{\n      SplitPanelModal,\n    },\n    data() {\n      return {\n        description: '分屏',\n      }\n    },\n    methods:{\n      splitPane(){\n        this.$refs.splitPanelModal.show();\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}