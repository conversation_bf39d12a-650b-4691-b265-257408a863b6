{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\TaskForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\TaskForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"TaskForm\",\n  props: {\n    showSubmit: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      form: this.$form.createForm(this)\n    };\n  },\n  methods: {\n    handleSubmit: function handleSubmit(e) {\n      var _this = this;\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          _this.$notification['error']({\n            message: 'Received values of form:',\n            description: values\n          });\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "showSubmit", "type", "Boolean", "default", "data", "form", "$form", "createForm", "methods", "handleSubmit", "e", "_this", "preventDefault", "validateFields", "err", "values", "$notification", "message", "description"], "sources": ["src/views/form/advancedForm/TaskForm.vue"], "sourcesContent": ["<template>\n  <a-form @submit=\"handleSubmit\" :form=\"form\" class=\"form\">\n    <a-row class=\"form-row\" :gutter=\"16\">\n      <a-col :lg=\"6\" :md=\"12\" :sm=\"24\">\n        <a-form-item\n          label=\"任务名\">\n          <a-input placeholder=\"请输入任务名称\" v-decorator=\"[ 'task.name', {rules: [{ required: true, message: '请输入任务名称', whitespace: true}]} ]\" />\n        </a-form-item>\n      </a-col>\n      <a-col :xl=\"{span: 7, offset: 1}\" :lg=\"{span: 8}\" :md=\"{span: 12}\" :sm=\"24\">\n        <a-form-item\n          label=\"任务描述\">\n          <a-input placeholder=\"请输入任务描述\" v-decorator=\"[ 'task.description', {rules: [{ required: true, message: '请输入任务描述', whitespace: true}]} ]\" />\n        </a-form-item>\n      </a-col>\n      <a-col :xl=\"{span: 9, offset: 1}\" :lg=\"{span: 10}\" :md=\"{span: 24}\" :sm=\"24\">\n        <a-form-item\n          label=\"执行人\">\n          <a-select\n            placeholder=\"请选择执行人\"\n            v-decorator=\"[\n              'task.executor',\n              {rules: [{ required: true, message: '请选择执行人'}]}\n            ]\" >\n            <a-select-option value=\"黄丽丽\">黄丽丽</a-select-option>\n            <a-select-option value=\"李大刀\">李大刀</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-col>\n    </a-row>\n    <a-row class=\"form-row\" :gutter=\"16\">\n      <a-col :lg=\"6\" :md=\"12\" :sm=\"24\">\n        <a-form-item\n          label=\"责任人\">\n          <a-select\n            placeholder=\"请选择责任人\"\n            v-decorator=\"[\n              'task.manager',\n              {rules: [{ required: true, message: '请选择责任人'}]}\n            ]\" >\n            <a-select-option value=\"王伟\">王伟</a-select-option>\n            <a-select-option value=\"李红军\">李红军</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-col>\n      <a-col :xl=\"{span: 7, offset: 1}\" :lg=\"{span: 8}\" :md=\"{span: 12}\" :sm=\"24\">\n        <a-form-item\n          label=\"提醒时间\">\n          <a-time-picker\n            style=\"width: 100%\"\n            v-decorator=\"[\n              'task.time',\n              {rules: [{ required: true, message: '请选择提醒时间'}]}\n            ]\" />\n        </a-form-item>\n      </a-col>\n      <a-col :xl=\"{span: 9, offset: 1}\" :lg=\"{span: 10}\" :md=\"{span: 24}\" :sm=\"24\">\n        <a-form-item\n          label=\"任务类型\">\n          <a-select\n            placeholder=\"请选择任务类型\"\n            v-decorator=\"[ 'task.type', {rules: [{ required: true, message: '请选择任务类型'}]} ]\" >\n            <a-select-option value=\"定时执行\">定时执行</a-select-option>\n            <a-select-option value=\"周期执行\">周期执行</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-col>\n    </a-row>\n    <a-form-item v-if=\"showSubmit\">\n      <a-button htmlType=\"submit\" >Submit</a-button>\n    </a-form-item>\n  </a-form>\n</template>\n\n<script>\n  export default {\n    name: \"TaskForm\",\n    props: {\n      showSubmit: {\n        type: Boolean,\n        default: false\n      }\n    },\n    data () {\n      return {\n        form: this.$form.createForm(this)\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            this.$notification['error']({\n              message: 'Received values of form:',\n              description: values\n            })\n          }\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AA2EA;EACAA,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MAAA,IAAAC,KAAA;MACAD,CAAA,CAAAE,cAAA;MACA,KAAAP,IAAA,CAAAQ,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAH,KAAA,CAAAK,aAAA;YACAC,OAAA;YACAC,WAAA,EAAAH;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}