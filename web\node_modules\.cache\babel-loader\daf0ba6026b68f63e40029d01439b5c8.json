{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import Step1 from './Step1';\nimport Step2 from './Step2';\nimport Step3 from './Step3';\nexport default {\n  name: \"StepForm\",\n  components: {\n    Step1: Step1,\n    Step2: Step2,\n    Step3: Step3\n  },\n  data: function data() {\n    return {\n      description: '将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。',\n      currentTab: 0,\n      // form\n      form: null\n    };\n  },\n  methods: {\n    // handler\n    nextStep: function nextStep() {\n      if (this.currentTab < 2) {\n        this.currentTab += 1;\n      }\n    },\n    prevStep: function prevStep() {\n      if (this.currentTab > 0) {\n        this.currentTab -= 1;\n      }\n    },\n    finish: function finish() {\n      this.currentTab = 0;\n    }\n  }\n};", {"version": 3, "names": ["Step1", "Step2", "Step3", "name", "components", "data", "description", "currentTab", "form", "methods", "nextStep", "prevStep", "finish"], "sources": ["src/views/form/stepForm/StepForm.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-steps class=\"steps\" :current=\"currentTab\">\n      <a-step title=\"填写转账信息\" />\n      <a-step title=\"确认转账信息\" />\n      <a-step title=\"完成\" />\n    </a-steps>\n    <div class=\"content\">\n      <step1 v-if=\"currentTab === 0\" @nextStep=\"nextStep\"/>\n      <step2 v-if=\"currentTab === 1\" @nextStep=\"nextStep\" @prevStep=\"prevStep\"/>\n      <step3 v-if=\"currentTab === 2\" @prevStep=\"prevStep\" @finish=\"finish\"/>\n    </div>\n  </a-card>\n</template>\n\n<script>\n  import Step1 from './Step1'\n  import Step2 from './Step2'\n  import Step3 from './Step3'\n\n  export default {\n    name: \"StepForm\",\n    components: {\n      Step1,\n      Step2,\n      Step3\n    },\n    data () {\n      return {\n        description: '将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。',\n        currentTab: 0,\n\n        // form\n        form: null,\n      }\n    },\n    methods: {\n\n      // handler\n      nextStep () {\n        if (this.currentTab < 2) {\n          this.currentTab += 1\n        }\n      },\n      prevStep () {\n        if (this.currentTab > 0) {\n          this.currentTab -= 1\n        }\n      },\n      finish () {\n        this.currentTab = 0\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .steps {\n    max-width: 750px;\n    margin: 16px auto;\n  }\n</style>"], "mappings": "AAgBA,OAAAA,KAAA;AACA,OAAAC,KAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,KAAA,EAAAA,KAAA;IACAC,KAAA,EAAAA,KAAA;IACAC,KAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MAEA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IAEA;IACAC,QAAA,WAAAA,SAAA;MACA,SAAAH,UAAA;QACA,KAAAA,UAAA;MACA;IACA;IACAI,QAAA,WAAAA,SAAA;MACA,SAAAJ,UAAA;QACA,KAAAA,UAAA;MACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAAL,UAAA;IACA;EACA;AACA", "ignoreList": []}]}