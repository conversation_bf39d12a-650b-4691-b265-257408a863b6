{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import { getAction, getFileAccessHttpUrl } from '@/api/manage';\nimport UnitViewModal from './modules/UnitViewModal';\nexport default {\n  name: 'MineCourseList',\n  components: {\n    UnitViewModal: UnitViewModal\n  },\n  data: function data() {\n    return {\n      dataSource: [],\n      courseInfo: {},\n      url: {\n        courseInfo: \"/teaching/teachingCourse/queryById\",\n        unitList: \"/teaching/teachingCourseUnit/mineUnit\"\n      },\n      visible: false,\n      unit: {}\n    };\n  },\n  created: function created() {\n    var courseId = this.$route.query.id;\n    console.log(\"courseId\" + courseId);\n    if (courseId) {\n      this.getCourseInfo(courseId);\n      this.getUnitList(courseId);\n    }\n  },\n  mounted: function mounted() {},\n  methods: {\n    getFileAccessHttpUrl: getFileAccessHttpUrl,\n    getCourseInfo: function getCourseInfo(courseId) {\n      var _this = this;\n      getAction(this.url.courseInfo, {\n        id: courseId\n      }).then(function (res) {\n        console.log(res);\n        if (res.success) {\n          _this.courseInfo = res.result;\n          _this.$route.meta.title = \"我的课程-\" + _this.courseInfo.courseName;\n          _this.courseInfo.map = _this.getFileAccessHttpUrl(_this.courseInfo.map);\n        } else {\n          _this.$message.error(res.message);\n        }\n      });\n    },\n    getUnitList: function getUnitList(courseId) {\n      var _this2 = this;\n      getAction(this.url.unitList, {\n        courseId: courseId,\n        pageNo: 1,\n        pageSize: 99\n      }).then(function (res) {\n        console.log(res);\n        if (res.success) {\n          _this2.dataSource = res.result.records;\n        } else {\n          _this2.$message.error(res.message);\n        }\n      });\n    },\n    viewUnit: function viewUnit(unit) {\n      console.log(unit);\n      this.$refs.unitViewModal.visible = true;\n      this.$refs.unitViewModal.unit = unit;\n    }\n  }\n};", {"version": 3, "names": ["getAction", "getFileAccessHttpUrl", "UnitViewModal", "name", "components", "data", "dataSource", "courseInfo", "url", "unitList", "visible", "unit", "created", "courseId", "$route", "query", "id", "console", "log", "getCourseInfo", "getUnitList", "mounted", "methods", "_this", "then", "res", "success", "result", "meta", "title", "courseName", "map", "$message", "error", "message", "_this2", "pageNo", "pageSize", "records", "viewUnit", "$refs", "unitViewModal"], "sources": ["src/views/account/course/CourseUnitListCard.vue"], "sourcesContent": ["<template>\n  <div class=\"app-list\">\n    <a-card v-for=\"item in dataSource\" :key=\"item.id\" @click=\"viewUnit(item)\">\n      <a-card-meta>\n          <div style=\"margin-bottom: 3px\" slot=\"title\">\n            <a-icon type=\"right-circle\"/>&nbsp;&nbsp;\n            {{ item.unitName }}\n          </div>\n          <div class=\"meta-cardInfo\" slot=\"description\">\n            <img :src=\"getFileAccessHttpUrl(item.unitCover)\" height=\"25px\" style=\"width:100%;height:100%;\"/>\n          </div>\n      </a-card-meta>\n    </a-card>\n    <unitView-modal ref=\"unitViewModal\"/>\n  </div>\n</template>\n<script>\nimport { getAction,getFileAccessHttpUrl } from '@/api/manage'\n import UnitViewModal from './modules/UnitViewModal'\nexport default {\n  name: 'MineCourseList',\n  components: {\n    UnitViewModal\n  },\n  data() {\n    return {\n      dataSource: [],\n      courseInfo: {},\n      url:{\n        courseInfo: \"/teaching/teachingCourse/queryById\",\n        unitList: \"/teaching/teachingCourseUnit/mineUnit\"\n      },\n      visible : false,\n      unit: {}\n    }\n  },\n  created(){\n    let courseId = this.$route.query.id\n    console.log(\"courseId\"+courseId)\n    if(courseId){\n        this.getCourseInfo(courseId)\n        this.getUnitList(courseId)\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    getCourseInfo(courseId){\n        getAction(this.url.courseInfo, {id: courseId}).then(res=>{\n            console.log(res)\n            if(res.success){\n              this.courseInfo = res.result\n              this.$route.meta.title = \"我的课程-\" + this.courseInfo.courseName\n              this.courseInfo.map = this.getFileAccessHttpUrl(this.courseInfo.map)\n            }else{\n              this.$message.error(res.message)\n            }\n        })\n    },\n    getUnitList(courseId){\n        getAction(this.url.unitList, {courseId: courseId, pageNo: 1, pageSize:99}).then(res=>{\n            console.log(res)\n            if(res.success){\n              this.dataSource = res.result.records\n            }else{\n              this.$message.error(res.message)\n            }\n        })\n    },\n    viewUnit(unit){\n      console.log(unit);\n      this.$refs.unitViewModal.visible = true;\n      this.$refs.unitViewModal.unit = unit\n    },\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.app-list {\n  .ant-card {\n    min-height: 200px;\n    max-height: 400px;\n    width: 300px;\n    display: inline-block;\n    margin: 20px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    margin-top: 16px;\n    .title{\n        margin-right:20px;\n    }\n    img {\n      width: 100%;\n      max-height: 200px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n}\n</style>"], "mappings": "AAiBA,SAAAA,SAAA,EAAAC,oBAAA;AACA,OAAAC,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF,aAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;MACAC,GAAA;QACAD,UAAA;QACAE,QAAA;MACA;MACAC,OAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;IACAC,OAAA,CAAAC,GAAA,cAAAL,QAAA;IACA,IAAAA,QAAA;MACA,KAAAM,aAAA,CAAAN,QAAA;MACA,KAAAO,WAAA,CAAAP,QAAA;IACA;EACA;EACAQ,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACArB,oBAAA,EAAAA,oBAAA;IACAkB,aAAA,WAAAA,cAAAN,QAAA;MAAA,IAAAU,KAAA;MACAvB,SAAA,MAAAQ,GAAA,CAAAD,UAAA;QAAAS,EAAA,EAAAH;MAAA,GAAAW,IAAA,WAAAC,GAAA;QACAR,OAAA,CAAAC,GAAA,CAAAO,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAH,KAAA,CAAAhB,UAAA,GAAAkB,GAAA,CAAAE,MAAA;UACAJ,KAAA,CAAAT,MAAA,CAAAc,IAAA,CAAAC,KAAA,aAAAN,KAAA,CAAAhB,UAAA,CAAAuB,UAAA;UACAP,KAAA,CAAAhB,UAAA,CAAAwB,GAAA,GAAAR,KAAA,CAAAtB,oBAAA,CAAAsB,KAAA,CAAAhB,UAAA,CAAAwB,GAAA;QACA;UACAR,KAAA,CAAAS,QAAA,CAAAC,KAAA,CAAAR,GAAA,CAAAS,OAAA;QACA;MACA;IACA;IACAd,WAAA,WAAAA,YAAAP,QAAA;MAAA,IAAAsB,MAAA;MACAnC,SAAA,MAAAQ,GAAA,CAAAC,QAAA;QAAAI,QAAA,EAAAA,QAAA;QAAAuB,MAAA;QAAAC,QAAA;MAAA,GAAAb,IAAA,WAAAC,GAAA;QACAR,OAAA,CAAAC,GAAA,CAAAO,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAS,MAAA,CAAA7B,UAAA,GAAAmB,GAAA,CAAAE,MAAA,CAAAW,OAAA;QACA;UACAH,MAAA,CAAAH,QAAA,CAAAC,KAAA,CAAAR,GAAA,CAAAS,OAAA;QACA;MACA;IACA;IACAK,QAAA,WAAAA,SAAA5B,IAAA;MACAM,OAAA,CAAAC,GAAA,CAAAP,IAAA;MACA,KAAA6B,KAAA,CAAAC,aAAA,CAAA/B,OAAA;MACA,KAAA8B,KAAA,CAAAC,aAAA,CAAA9B,IAAA,GAAAA,IAAA;IACA;EACA;AACA", "ignoreList": []}]}