{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue?vue&type=template&id=6d5cf9b0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"a-form\", {\n    staticStyle: {\n      \"max-width\": \"500px\",\n      margin: \"40px auto 0\"\n    }\n  }, [_c(\"a-alert\", {\n    staticStyle: {\n      \"margin-bottom\": \"24px\"\n    },\n    attrs: {\n      closable: true,\n      message: \"确认转账后，资金将直接打入对方账户，无法退回。\"\n    }\n  }), _c(\"a-form-item\", {\n    staticClass: \"stepFormText\",\n    attrs: {\n      label: \"付款账户\",\n      labelCol: {\n        span: 5\n      },\n      wrapperCol: {\n        span: 19\n      }\n    }\n  }, [_vm._v(\"\\n      <EMAIL>\\n    \")]), _c(\"a-form-item\", {\n    staticClass: \"stepFormText\",\n    attrs: {\n      label: \"收款账户\",\n      labelCol: {\n        span: 5\n      },\n      wrapperCol: {\n        span: 19\n      }\n    }\n  }, [_vm._v(\"\\n      <EMAIL>\\n    \")]), _c(\"a-form-item\", {\n    staticClass: \"stepFormText\",\n    attrs: {\n      label: \"收款人姓名\",\n      labelCol: {\n        span: 5\n      },\n      wrapperCol: {\n        span: 19\n      }\n    }\n  }, [_vm._v(\"\\n      Alex\\n    \")]), _c(\"a-form-item\", {\n    staticClass: \"stepFormText\",\n    attrs: {\n      label: \"转账金额\",\n      labelCol: {\n        span: 5\n      },\n      wrapperCol: {\n        span: 19\n      }\n    }\n  }, [_vm._v(\"\\n      ￥ 5,000.00\\n    \")]), _c(\"a-form-item\", {\n    attrs: {\n      wrapperCol: {\n        span: 19,\n        offset: 5\n      }\n    }\n  }, [_c(\"a-button\", {\n    attrs: {\n      loading: _vm.loading,\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.nextStep\n    }\n  }, [_vm._v(\"提交\")]), _c(\"a-button\", {\n    staticStyle: {\n      \"margin-left\": \"8px\"\n    },\n    on: {\n      click: _vm.prevStep\n    }\n  }, [_vm._v(\"上一步\")])], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "margin", "attrs", "closable", "message", "staticClass", "label", "labelCol", "span", "wrapperCol", "_v", "offset", "loading", "type", "on", "click", "nextStep", "prevStep", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/form/stepForm/Step2.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"a-form\",\n        { staticStyle: { \"max-width\": \"500px\", margin: \"40px auto 0\" } },\n        [\n          _c(\"a-alert\", {\n            staticStyle: { \"margin-bottom\": \"24px\" },\n            attrs: {\n              closable: true,\n              message: \"确认转账后，资金将直接打入对方账户，无法退回。\",\n            },\n          }),\n          _c(\n            \"a-form-item\",\n            {\n              staticClass: \"stepFormText\",\n              attrs: {\n                label: \"付款账户\",\n                labelCol: { span: 5 },\n                wrapperCol: { span: 19 },\n              },\n            },\n            [_vm._v(\"\\n      <EMAIL>\\n    \")]\n          ),\n          _c(\n            \"a-form-item\",\n            {\n              staticClass: \"stepFormText\",\n              attrs: {\n                label: \"收款账户\",\n                labelCol: { span: 5 },\n                wrapperCol: { span: 19 },\n              },\n            },\n            [_vm._v(\"\\n      <EMAIL>\\n    \")]\n          ),\n          _c(\n            \"a-form-item\",\n            {\n              staticClass: \"stepFormText\",\n              attrs: {\n                label: \"收款人姓名\",\n                labelCol: { span: 5 },\n                wrapperCol: { span: 19 },\n              },\n            },\n            [_vm._v(\"\\n      Alex\\n    \")]\n          ),\n          _c(\n            \"a-form-item\",\n            {\n              staticClass: \"stepFormText\",\n              attrs: {\n                label: \"转账金额\",\n                labelCol: { span: 5 },\n                wrapperCol: { span: 19 },\n              },\n            },\n            [_vm._v(\"\\n      ￥ 5,000.00\\n    \")]\n          ),\n          _c(\n            \"a-form-item\",\n            { attrs: { wrapperCol: { span: 19, offset: 5 } } },\n            [\n              _c(\n                \"a-button\",\n                {\n                  attrs: { loading: _vm.loading, type: \"primary\" },\n                  on: { click: _vm.nextStep },\n                },\n                [_vm._v(\"提交\")]\n              ),\n              _c(\n                \"a-button\",\n                {\n                  staticStyle: { \"margin-left\": \"8px\" },\n                  on: { click: _vm.prevStep },\n                },\n                [_vm._v(\"上一步\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE;MAAE,WAAW,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAc;EAAE,CAAC,EAChE,CACEH,EAAE,CAAC,SAAS,EAAE;IACZE,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO,CAAC;IACxCE,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,EACFN,EAAE,CACA,aAAa,EACb;IACEO,WAAW,EAAE,cAAc;IAC3BH,KAAK,EAAE;MACLI,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrBC,UAAU,EAAE;QAAED,IAAI,EAAE;MAAG;IACzB;EACF,CAAC,EACD,CAACX,GAAG,CAACa,EAAE,CAAC,qCAAqC,CAAC,CAChD,CAAC,EACDZ,EAAE,CACA,aAAa,EACb;IACEO,WAAW,EAAE,cAAc;IAC3BH,KAAK,EAAE;MACLI,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrBC,UAAU,EAAE;QAAED,IAAI,EAAE;MAAG;IACzB;EACF,CAAC,EACD,CAACX,GAAG,CAACa,EAAE,CAAC,gCAAgC,CAAC,CAC3C,CAAC,EACDZ,EAAE,CACA,aAAa,EACb;IACEO,WAAW,EAAE,cAAc;IAC3BH,KAAK,EAAE;MACLI,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrBC,UAAU,EAAE;QAAED,IAAI,EAAE;MAAG;IACzB;EACF,CAAC,EACD,CAACX,GAAG,CAACa,EAAE,CAAC,oBAAoB,CAAC,CAC/B,CAAC,EACDZ,EAAE,CACA,aAAa,EACb;IACEO,WAAW,EAAE,cAAc;IAC3BH,KAAK,EAAE;MACLI,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrBC,UAAU,EAAE;QAAED,IAAI,EAAE;MAAG;IACzB;EACF,CAAC,EACD,CAACX,GAAG,CAACa,EAAE,CAAC,0BAA0B,CAAC,CACrC,CAAC,EACDZ,EAAE,CACA,aAAa,EACb;IAAEI,KAAK,EAAE;MAAEO,UAAU,EAAE;QAAED,IAAI,EAAE,EAAE;QAAEG,MAAM,EAAE;MAAE;IAAE;EAAE,CAAC,EAClD,CACEb,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MAAEU,OAAO,EAAEf,GAAG,CAACe,OAAO;MAAEC,IAAI,EAAE;IAAU,CAAC;IAChDC,EAAE,EAAE;MAAEC,KAAK,EAAElB,GAAG,CAACmB;IAAS;EAC5B,CAAC,EACD,CAACnB,GAAG,CAACa,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDZ,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE;MAAE,aAAa,EAAE;IAAM,CAAC;IACrCc,EAAE,EAAE;MAAEC,KAAK,EAAElB,GAAG,CAACoB;IAAS;EAC5B,CAAC,EACD,CAACpB,GAAG,CAACa,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIQ,eAAe,GAAG,EAAE;AACxBtB,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}]}