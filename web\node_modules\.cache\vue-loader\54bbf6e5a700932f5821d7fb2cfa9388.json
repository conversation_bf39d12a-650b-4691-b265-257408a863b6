{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\index.vue?vue&type=style&index=0&id=7cddf3ce&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.j-select-biz-component-box {\n\n  @width: 82px;\n\n  .left {\n    width: calc(100% - @width - 8px);\n  }\n\n  .right {\n    width: @width;\n  }\n\n  .full {\n    width: 100%;\n  }\n\n  /deep/ .ant-select-search__field {\n    display: none !important;\n  }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqKA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/jeecgbiz/JSelectBizComponent", "sourcesContent": ["<template>\n  <a-row class=\"j-select-biz-component-box\" type=\"flex\" :gutter=\"8\">\n    <a-col class=\"left\" :class=\"{'full': !buttons}\">\n      <slot name=\"left\">\n        <a-select\n          mode=\"multiple\"\n          :placeholder=\"placeholder\"\n          v-model=\"selectValue\"\n          :options=\"selectOptions\"\n          allowClear\n          :disabled=\"disabled\"\n          :open=\"selectOpen\"\n          style=\"width: 100%;\"\n          @dropdownVisibleChange=\"handleDropdownVisibleChange\"\n          @click.native=\"visible=(buttons?visible:true)\"\n        />\n      </slot>\n    </a-col>\n\n    <a-col v-if=\"buttons\" class=\"right\">\n      <a-button type=\"primary\" icon=\"search\" :disabled=\"disabled\" @click=\"visible=true\">{{selectButtonText}}</a-button>\n    </a-col>\n\n    <j-select-biz-component-modal\n      v-model=\"selectValue\"\n      :visible.sync=\"visible\"\n      v-bind=\"modalProps\"\n      @options=\"handleOptions\"\n    />\n  </a-row>\n</template>\n\n<script>\n  import JSelectBizComponentModal from './JSelectBizComponentModal'\n\n  export default {\n    name: 'JSelectBizComponent',\n    components: { JSelectBizComponentModal },\n    props: {\n      value: {\n        type: String,\n        default: ''\n      },\n      /** 是否返回 id，默认 false，返回 code */\n      returnId: {\n        type: Boolean,\n        default: false\n      },\n      placeholder: {\n        type: String,\n        default: '请选择'\n      },\n      disabled: {\n        type: Boolean,\n        default: false\n      },\n      // 是否支持多选，默认 true\n      multiple: {\n        type: Boolean,\n        default: true\n      },\n      // 是否显示按钮，默认 true\n      buttons: {\n        type: Boolean,\n        default: true\n      },\n      // 显示的 Key\n      displayKey: {\n        type: String,\n        default: null\n      },\n      // 返回的 key\n      returnKeys: {\n        type: Array,\n        default: () => ['id', 'id']\n      },\n      // 选择按钮文字\n      selectButtonText: {\n        type: String,\n        default: '选择'\n      },\n\n    },\n    data() {\n      return {\n        selectValue: [],\n        selectOptions: [],\n        dataSourceMap: {},\n        visible: false,\n        selectOpen: false,\n      }\n    },\n    computed: {\n      valueKey() {\n        return this.returnId ? this.returnKeys[0] : this.returnKeys[1]\n      },\n      modalProps() {\n        return Object.assign({\n          valueKey: this.valueKey,\n          multiple: this.multiple,\n          returnKeys: this.returnKeys,\n          displayKey: this.displayKey || this.valueKey\n        }, this.$attrs)\n      },\n    },\n    watch: {\n      value: {\n        immediate: true,\n        handler(val) {\n          if (val) {\n            this.selectValue = val.split(',')\n          } else {\n            this.selectValue = []\n          }\n        }\n      },\n      selectValue: {\n        deep: true,\n        handler(val) {\n          let rows = val.map(key => this.dataSourceMap[key])\n          this.$emit('select', rows)\n          let data = val.join(',')\n          this.$emit('input', data)\n          this.$emit('change', data)\n        }\n      }\n    },\n    methods: {\n      handleOptions(options, dataSourceMap) {\n        this.selectOptions = options\n        this.dataSourceMap = dataSourceMap\n      },\n      handleDropdownVisibleChange() {\n        // 解决antdv自己的bug —— open 设置为 false 了，点击后还是添加了 open 样式，导致点击事件失效\n        this.selectOpen = true\n        this.$nextTick(() => {\n          this.selectOpen = false\n        })\n      },\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .j-select-biz-component-box {\n\n    @width: 82px;\n\n    .left {\n      width: calc(100% - @width - 8px);\n    }\n\n    .right {\n      width: @width;\n    }\n\n    .full {\n      width: 100%;\n    }\n\n    /deep/ .ant-select-search__field {\n      display: none !important;\n    }\n  }\n</style>"]}]}