{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderModalForJEditableTable.vue?vue&type=template&id=c1e8ad42&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderModalForJEditableTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"1200\"\n  :visible=\"visible\"\n  :maskClosable=\"false\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n      <!-- 主表单区域 -->\n      <a-row class=\"form-row\" :gutter=\"0\">\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单号\">\n            <a-input\n              placeholder=\"请输入订单号\"\n              v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单类型\">\n            <a-select placeholder=\"请选择订单类型\" v-decorator=\"['ctype',{}]\">\n              <a-select-option value=\"1\">国内订单</a-select-option>\n              <a-select-option value=\"2\">国际订单</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单日期\">\n            <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\" v-decorator=\"[ 'orderDate',{}]\"/>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      <a-row class=\"form-row\" :gutter=\"0\">\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单金额\">\n            <a-input-number placeholder=\"请输入订单金额\" style=\"width: 100%\" v-decorator=\"[ 'orderMoney', {}]\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单备注\">\n            <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\"/>\n          </a-form-item>\n        </a-col>\n      </a-row>\n    </a-form>\n\n    <!-- 子表单区域 -->\n    <a-tabs v-model=\"activeKey\" @change=\"handleChangeTabs\">\n      <a-tab-pane tab=\"客户信息\" key=\"1\" :forceRender=\"true\">\n\n        <j-editable-table\n          ref=\"editableTable1\"\n          :loading=\"table1.loading\"\n          :columns=\"table1.columns\"\n          :dataSource=\"table1.dataSource\"\n          :maxHeight=\"300\"\n          :rowNumber=\"true\"\n          :rowSelection=\"true\"\n          :actionButton=\"true\"/>\n\n      </a-tab-pane>\n\n      <a-tab-pane tab=\"机票信息\" key=\"2\" :forceRender=\"true\">\n\n        <j-editable-table\n          ref=\"editableTable2\"\n          :loading=\"table2.loading\"\n          :columns=\"table2.columns\"\n          :dataSource=\"table2.dataSource\"\n          :maxHeight=\"300\"\n          :rowNumber=\"true\"\n          :rowSelection=\"true\"\n          :actionButton=\"true\"/>\n\n      </a-tab-pane>\n    </a-tabs>\n\n  </a-spin>\n</a-modal>\n", null]}