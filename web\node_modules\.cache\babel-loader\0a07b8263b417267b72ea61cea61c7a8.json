{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\GlobalNotificationListener.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\GlobalNotificationListener.vue", "mtime": 1753242963559}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getAction } from '@/api/manage';\nimport store from '@/store/';\nimport moment from 'moment';\nimport { notification } from 'ant-design-vue';\nexport default {\n  name: \"GlobalNotificationListener\",\n  data: function data() {\n    return {\n      websock: null,\n      lockReconnect: false,\n      heartCheck: null,\n      url: {\n        queryById: \"/sys/annountCement/queryById\"\n      }\n    };\n  },\n  mounted: function mounted() {\n    // 只有在用户已登录时才初始化WebSocket连接\n    if (this.isUserLoggedIn()) {\n      this.initWebSocket();\n      this.heartCheckFun();\n    }\n  },\n  destroyed: function destroyed() {\n    this.websocketclose();\n  },\n  methods: {\n    // 检查用户是否已登录\n    isUserLoggedIn: function isUserLoggedIn() {\n      try {\n        var userInfo = store.getters.userInfo;\n        return userInfo && userInfo.id;\n      } catch (error) {\n        console.log(\"检查用户登录状态失败:\", error);\n        return false;\n      }\n    },\n    initWebSocket: function initWebSocket() {\n      // 再次检查用户登录状态\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，跳过WebSocket连接\");\n        return;\n      }\n\n      // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https\n      var userId = store.getters.userInfo.id;\n      var url = window._CONFIG['domianURL'].replace(\"https://\", \"wss://\").replace(\"http://\", \"ws://\") + \"/websocket/\" + userId;\n      console.log(\"首页WebSocket连接URL:\", url);\n      this.websock = new WebSocket(url);\n      this.websock.onopen = this.websocketOnopen;\n      this.websock.onerror = this.websocketOnerror;\n      this.websock.onmessage = this.websocketOnmessage;\n      this.websock.onclose = this.websocketOnclose;\n    },\n    websocketOnopen: function websocketOnopen() {\n      console.log(\"首页WebSocket连接成功\");\n      //心跳检测重置\n      this.heartCheck.reset().start();\n    },\n    websocketOnerror: function websocketOnerror(e) {\n      console.log(\"首页WebSocket连接发生错误\");\n      this.reconnect();\n    },\n    websocketOnmessage: function websocketOnmessage(e) {\n      console.log(\"首页接收WebSocket消息:\", e.data);\n      var data = eval(\"(\" + e.data + \")\"); //解析对象\n      if (data.cmd == \"topic\") {\n        // 显示右下角通知\n        this.openNotification(data);\n      } else if (data.cmd == \"user\") {\n        //用户消息\n        // 显示右下角通知\n        this.openNotification(data);\n      }\n      //心跳检测重置\n      this.heartCheck.reset().start();\n    },\n    websocketOnclose: function websocketOnclose(e) {\n      console.log(\"首页WebSocket连接关闭 (\" + e.code + \")\");\n      this.reconnect();\n    },\n    websocketSend: function websocketSend(text) {\n      // 数据发送\n      try {\n        this.websock.send(text);\n      } catch (err) {\n        console.log(\"首页WebSocket发送失败 (\" + err.code + \")\");\n      }\n    },\n    openNotification: function openNotification(data) {\n      var _this = this;\n      var text = data.msgTxt;\n      var key = \"open\".concat(Date.now());\n      notification.open({\n        message: '消息提醒',\n        placement: 'bottomRight',\n        description: text,\n        key: key,\n        btn: function btn(h) {\n          return h('a-button', {\n            props: {\n              type: 'primary',\n              size: 'small'\n            },\n            on: {\n              click: function click() {\n                return _this.showDetail(key, data);\n              }\n            }\n          }, '查看详情');\n        }\n      });\n    },\n    reconnect: function reconnect() {\n      var that = this;\n      if (that.lockReconnect) return;\n\n      // 检查用户是否仍然登录，如果未登录则不重连\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，停止WebSocket重连\");\n        return;\n      }\n      that.lockReconnect = true;\n      //没连接上会一直重连，设置延迟避免请求过多\n      setTimeout(function () {\n        console.info(\"首页尝试重连WebSocket...\");\n        that.initWebSocket();\n        that.lockReconnect = false;\n      }, 5000);\n    },\n    websocketclose: function websocketclose() {\n      // 关闭WebSocket连接\n      if (this.websock) {\n        // 移除心跳检测\n        if (this.heartCheck && this.heartCheck.timeoutObj) {\n          clearTimeout(this.heartCheck.timeoutObj);\n        }\n\n        // 移除服务器响应超时检测\n        if (this.heartCheck && this.heartCheck.serverTimeoutObj) {\n          clearTimeout(this.heartCheck.serverTimeoutObj);\n        }\n\n        // 关闭连接前先设置lockReconnect为true，防止reconnect方法重新连接\n        this.lockReconnect = true;\n\n        // 关闭WebSocket连接\n        this.websock.close();\n\n        // 清空websock对象\n        this.websock = null;\n        console.log(\"首页WebSocket连接已关闭\");\n      }\n    },\n    heartCheckFun: function heartCheckFun() {\n      var that = this;\n      //心跳检测,每20s心跳一次\n      that.heartCheck = {\n        timeout: 20000,\n        timeoutObj: null,\n        serverTimeoutObj: null,\n        reset: function reset() {\n          // 确保清除之前的定时器\n          clearTimeout(this.timeoutObj);\n          clearTimeout(this.serverTimeoutObj);\n          return this;\n        },\n        start: function start() {\n          var self = this;\n          this.timeoutObj = setTimeout(function () {\n            //这里发送一个心跳，后端收到后，返回一个心跳消息，\n            //onmessage拿到返回的心跳就说明连接正常\n            that.websocketSend(\"HeartBeat\");\n            console.info(\"首页客户端发送心跳\");\n\n            // 添加服务器响应超时检测\n            self.serverTimeoutObj = setTimeout(function () {\n              // 如果超过一定时间还没重置，说明后端主动断开了\n              if (that.websock && that.websock.readyState === 1) {\n                // 如果连接还存在\n                console.log(\"心跳无响应，关闭重连\");\n                that.websock.close(); // 关闭连接，触发onclose方法\n              }\n            }, self.timeout);\n          }, this.timeout);\n        }\n      };\n    },\n    showDetail: function showDetail(key, data) {\n      var _this2 = this;\n      notification.close(key);\n\n      // 检查是否是课程通知(msgCategory为3的是课程通知)\n      if (data.msgCategory === \"3\") {\n        // 直接跳转到课程通知页面\n        this.showNotificationDetail(data);\n        return;\n      }\n\n      // 处理系统通知\n      var id = data.msgId;\n      getAction(this.url.queryById, {\n        id: id\n      }).then(function (res) {\n        if (res.success) {\n          var record = res.result;\n          _this2.showAnnouncementDetail(record);\n        }\n      });\n    },\n    // 显示课程通知详情\n    showNotificationDetail: function showNotificationDetail(record) {\n      console.log(\"跳转到课程通知页面\", record);\n\n      // 打开通知详情页面，不标记为已读\n      this.$router.push({\n        path: '/teaching/my-notification',\n        name: 'teaching-my-notification'\n      });\n    },\n    // 显示系统公告详情\n    showAnnouncementDetail: function showAnnouncementDetail(record) {\n      // 根据打开类型显示不同内容\n      if (record.openType === 'component') {\n        // 组件类型，跳转到对应页面\n        this.$router.push({\n          path: record.openPage,\n          query: {\n            id: record.busId\n          }\n        });\n      } else {\n        // 普通公告，跳转到公告页面\n        this.$router.push({\n          path: '/isps/userAnnouncement',\n          query: {\n            id: record.id\n          }\n        });\n      }\n    }\n  }\n};", {"version": 3, "names": ["getAction", "store", "moment", "notification", "name", "data", "websock", "lockReconnect", "heart<PERSON><PERSON><PERSON>", "url", "queryById", "mounted", "isUserLoggedIn", "initWebSocket", "heartCheck<PERSON>un", "destroyed", "websocketclose", "methods", "userInfo", "getters", "id", "error", "console", "log", "userId", "window", "_CONFIG", "replace", "WebSocket", "onopen", "websocketOnopen", "onerror", "websocketOnerror", "onmessage", "websocketOnmessage", "onclose", "websocketOnclose", "reset", "start", "e", "reconnect", "eval", "cmd", "openNotification", "code", "websocketSend", "text", "send", "err", "_this", "msgTxt", "key", "concat", "Date", "now", "open", "message", "placement", "description", "btn", "h", "props", "type", "size", "on", "click", "showDetail", "that", "setTimeout", "info", "timeoutObj", "clearTimeout", "serverTimeoutObj", "close", "timeout", "self", "readyState", "_this2", "msgCategory", "showNotificationDetail", "msgId", "then", "res", "success", "record", "result", "showAnnouncementDetail", "$router", "push", "path", "openType", "openPage", "query", "busId"], "sources": ["src/views/home/<USER>/GlobalNotificationListener.vue"], "sourcesContent": ["<template>\n  <!-- 全局消息通知监听器，不渲染任何可见内容 -->\n  <div style=\"display: none;\"></div>\n</template>\n\n<script>\nimport { getAction } from '@/api/manage'\nimport store from '@/store/'\nimport moment from 'moment'\nimport { notification } from 'ant-design-vue'\n\nexport default {\n  name: \"GlobalNotificationListener\",\n  data() {\n    return {\n      websock: null,\n      lockReconnect: false,\n      heartCheck: null,\n      url: {\n        queryById: \"/sys/annountCement/queryById\"\n      }\n    }\n  },\n  mounted() {\n    // 只有在用户已登录时才初始化WebSocket连接\n    if (this.isUserLoggedIn()) {\n      this.initWebSocket()\n      this.heartCheckFun()\n    }\n  },\n  destroyed() {\n    this.websocketclose()\n  },\n  methods: {\n    // 检查用户是否已登录\n    isUserLoggedIn() {\n      try {\n        const userInfo = store.getters.userInfo\n        return userInfo && userInfo.id\n      } catch (error) {\n        console.log(\"检查用户登录状态失败:\", error)\n        return false\n      }\n    },\n    initWebSocket: function() {\n      // 再次检查用户登录状态\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，跳过WebSocket连接\")\n        return\n      }\n\n      // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https\n      var userId = store.getters.userInfo.id\n      var url = window._CONFIG['domianURL'].replace(\"https://\", \"wss://\").replace(\"http://\", \"ws://\") + \"/websocket/\" + userId\n      console.log(\"首页WebSocket连接URL:\", url)\n      this.websock = new WebSocket(url)\n      this.websock.onopen = this.websocketOnopen\n      this.websock.onerror = this.websocketOnerror\n      this.websock.onmessage = this.websocketOnmessage\n      this.websock.onclose = this.websocketOnclose\n    },\n    websocketOnopen: function() {\n      console.log(\"首页WebSocket连接成功\")\n      //心跳检测重置\n      this.heartCheck.reset().start()\n    },\n    websocketOnerror: function(e) {\n      console.log(\"首页WebSocket连接发生错误\")\n      this.reconnect()\n    },\n    websocketOnmessage: function(e) {\n      console.log(\"首页接收WebSocket消息:\", e.data)\n      var data = eval(\"(\" + e.data + \")\") //解析对象\n      if (data.cmd == \"topic\") {\n        // 显示右下角通知\n        this.openNotification(data)\n      } else if (data.cmd == \"user\") {\n        //用户消息\n        // 显示右下角通知\n        this.openNotification(data)\n      }\n      //心跳检测重置\n      this.heartCheck.reset().start()\n    },\n    websocketOnclose: function(e) {\n      console.log(\"首页WebSocket连接关闭 (\" + e.code + \")\")\n      this.reconnect()\n    },\n    websocketSend(text) { // 数据发送\n      try {\n        this.websock.send(text)\n      } catch (err) {\n        console.log(\"首页WebSocket发送失败 (\" + err.code + \")\")\n      }\n    },\n    openNotification(data) {\n      var text = data.msgTxt\n      const key = `open${Date.now()}`\n      notification.open({\n        message: '消息提醒',\n        placement: 'bottomRight',\n        description: text,\n        key,\n        btn: h => {\n          return h('a-button', {\n            props: {\n              type: 'primary',\n              size: 'small',\n            },\n            on: {\n              click: () => this.showDetail(key, data)\n            }\n          }, '查看详情')\n        },\n      })\n    },\n    reconnect() {\n      var that = this\n      if (that.lockReconnect) return\n\n      // 检查用户是否仍然登录，如果未登录则不重连\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，停止WebSocket重连\")\n        return\n      }\n\n      that.lockReconnect = true\n      //没连接上会一直重连，设置延迟避免请求过多\n      setTimeout(function() {\n        console.info(\"首页尝试重连WebSocket...\")\n        that.initWebSocket()\n        that.lockReconnect = false\n      }, 5000)\n    },\n    websocketclose() {\n      // 关闭WebSocket连接\n      if (this.websock) {\n        // 移除心跳检测\n        if (this.heartCheck && this.heartCheck.timeoutObj) {\n          clearTimeout(this.heartCheck.timeoutObj)\n        }\n        \n        // 移除服务器响应超时检测\n        if (this.heartCheck && this.heartCheck.serverTimeoutObj) {\n          clearTimeout(this.heartCheck.serverTimeoutObj)\n        }\n        \n        // 关闭连接前先设置lockReconnect为true，防止reconnect方法重新连接\n        this.lockReconnect = true\n        \n        // 关闭WebSocket连接\n        this.websock.close()\n        \n        // 清空websock对象\n        this.websock = null\n        \n        console.log(\"首页WebSocket连接已关闭\")\n      }\n    },\n    heartCheckFun() {\n      var that = this\n      //心跳检测,每20s心跳一次\n      that.heartCheck = {\n        timeout: 20000,\n        timeoutObj: null,\n        serverTimeoutObj: null,\n        reset: function() {\n          // 确保清除之前的定时器\n          clearTimeout(this.timeoutObj)\n          clearTimeout(this.serverTimeoutObj)\n          return this\n        },\n        start: function() {\n          var self = this;\n          this.timeoutObj = setTimeout(function() {\n            //这里发送一个心跳，后端收到后，返回一个心跳消息，\n            //onmessage拿到返回的心跳就说明连接正常\n            that.websocketSend(\"HeartBeat\")\n            console.info(\"首页客户端发送心跳\")\n            \n            // 添加服务器响应超时检测\n            self.serverTimeoutObj = setTimeout(function() {\n              // 如果超过一定时间还没重置，说明后端主动断开了\n              if(that.websock && that.websock.readyState === 1) { // 如果连接还存在\n                console.log(\"心跳无响应，关闭重连\");\n                that.websock.close(); // 关闭连接，触发onclose方法\n              }\n            }, self.timeout);\n          }, this.timeout)\n        }\n      }\n    },\n    showDetail(key, data) {\n      notification.close(key)\n      \n      // 检查是否是课程通知(msgCategory为3的是课程通知)\n      if(data.msgCategory === \"3\") {\n        // 直接跳转到课程通知页面\n        this.showNotificationDetail(data)\n        return;\n      }\n      \n      // 处理系统通知\n      var id = data.msgId\n      getAction(this.url.queryById, { id: id }).then((res) => {\n        if (res.success) {\n          var record = res.result\n            this.showAnnouncementDetail(record)\n        }\n      })\n    },\n    // 显示课程通知详情\n    showNotificationDetail(record) {\n      console.log(\"跳转到课程通知页面\", record);\n      \n      // 打开通知详情页面，不标记为已读\n      this.$router.push({\n        path: '/teaching/my-notification',\n        name: 'teaching-my-notification'\n      });\n    },\n    // 显示系统公告详情\n    showAnnouncementDetail(record) {\n      // 根据打开类型显示不同内容\n      if (record.openType === 'component') {\n        // 组件类型，跳转到对应页面\n        this.$router.push({\n          path: record.openPage,\n          query: { id: record.busId }\n        })\n      } else {\n        // 普通公告，跳转到公告页面\n        this.$router.push({\n          path: '/isps/userAnnouncement',\n          query: { id: record.id }\n        })\n      }\n    }\n  }\n}\n</script> "], "mappings": "AAMA,SAAAA,SAAA;AACA,OAAAC,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;MACAC,GAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,SAAAC,cAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAL,cAAA,WAAAA,eAAA;MACA;QACA,IAAAM,QAAA,GAAAjB,KAAA,CAAAkB,OAAA,CAAAD,QAAA;QACA,OAAAA,QAAA,IAAAA,QAAA,CAAAE,EAAA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAC,GAAA,gBAAAF,KAAA;QACA;MACA;IACA;IACAR,aAAA,WAAAA,cAAA;MACA;MACA,UAAAD,cAAA;QACAU,OAAA,CAAAC,GAAA;QACA;MACA;;MAEA;MACA,IAAAC,MAAA,GAAAvB,KAAA,CAAAkB,OAAA,CAAAD,QAAA,CAAAE,EAAA;MACA,IAAAX,GAAA,GAAAgB,MAAA,CAAAC,OAAA,cAAAC,OAAA,uBAAAA,OAAA,uCAAAH,MAAA;MACAF,OAAA,CAAAC,GAAA,sBAAAd,GAAA;MACA,KAAAH,OAAA,OAAAsB,SAAA,CAAAnB,GAAA;MACA,KAAAH,OAAA,CAAAuB,MAAA,QAAAC,eAAA;MACA,KAAAxB,OAAA,CAAAyB,OAAA,QAAAC,gBAAA;MACA,KAAA1B,OAAA,CAAA2B,SAAA,QAAAC,kBAAA;MACA,KAAA5B,OAAA,CAAA6B,OAAA,QAAAC,gBAAA;IACA;IACAN,eAAA,WAAAA,gBAAA;MACAR,OAAA,CAAAC,GAAA;MACA;MACA,KAAAf,UAAA,CAAA6B,KAAA,GAAAC,KAAA;IACA;IACAN,gBAAA,WAAAA,iBAAAO,CAAA;MACAjB,OAAA,CAAAC,GAAA;MACA,KAAAiB,SAAA;IACA;IACAN,kBAAA,WAAAA,mBAAAK,CAAA;MACAjB,OAAA,CAAAC,GAAA,qBAAAgB,CAAA,CAAAlC,IAAA;MACA,IAAAA,IAAA,GAAAoC,IAAA,OAAAF,CAAA,CAAAlC,IAAA;MACA,IAAAA,IAAA,CAAAqC,GAAA;QACA;QACA,KAAAC,gBAAA,CAAAtC,IAAA;MACA,WAAAA,IAAA,CAAAqC,GAAA;QACA;QACA;QACA,KAAAC,gBAAA,CAAAtC,IAAA;MACA;MACA;MACA,KAAAG,UAAA,CAAA6B,KAAA,GAAAC,KAAA;IACA;IACAF,gBAAA,WAAAA,iBAAAG,CAAA;MACAjB,OAAA,CAAAC,GAAA,uBAAAgB,CAAA,CAAAK,IAAA;MACA,KAAAJ,SAAA;IACA;IACAK,aAAA,WAAAA,cAAAC,IAAA;MAAA;MACA;QACA,KAAAxC,OAAA,CAAAyC,IAAA,CAAAD,IAAA;MACA,SAAAE,GAAA;QACA1B,OAAA,CAAAC,GAAA,uBAAAyB,GAAA,CAAAJ,IAAA;MACA;IACA;IACAD,gBAAA,WAAAA,iBAAAtC,IAAA;MAAA,IAAA4C,KAAA;MACA,IAAAH,IAAA,GAAAzC,IAAA,CAAA6C,MAAA;MACA,IAAAC,GAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA;MACAnD,YAAA,CAAAoD,IAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA,EAAAZ,IAAA;QACAK,GAAA,EAAAA,GAAA;QACAQ,GAAA,WAAAA,IAAAC,CAAA;UACA,OAAAA,CAAA;YACAC,KAAA;cACAC,IAAA;cACAC,IAAA;YACA;YACAC,EAAA;cACAC,KAAA,WAAAA,MAAA;gBAAA,OAAAhB,KAAA,CAAAiB,UAAA,CAAAf,GAAA,EAAA9C,IAAA;cAAA;YACA;UACA;QACA;MACA;IACA;IACAmC,SAAA,WAAAA,UAAA;MACA,IAAA2B,IAAA;MACA,IAAAA,IAAA,CAAA5D,aAAA;;MAEA;MACA,UAAAK,cAAA;QACAU,OAAA,CAAAC,GAAA;QACA;MACA;MAEA4C,IAAA,CAAA5D,aAAA;MACA;MACA6D,UAAA;QACA9C,OAAA,CAAA+C,IAAA;QACAF,IAAA,CAAAtD,aAAA;QACAsD,IAAA,CAAA5D,aAAA;MACA;IACA;IACAS,cAAA,WAAAA,eAAA;MACA;MACA,SAAAV,OAAA;QACA;QACA,SAAAE,UAAA,SAAAA,UAAA,CAAA8D,UAAA;UACAC,YAAA,MAAA/D,UAAA,CAAA8D,UAAA;QACA;;QAEA;QACA,SAAA9D,UAAA,SAAAA,UAAA,CAAAgE,gBAAA;UACAD,YAAA,MAAA/D,UAAA,CAAAgE,gBAAA;QACA;;QAEA;QACA,KAAAjE,aAAA;;QAEA;QACA,KAAAD,OAAA,CAAAmE,KAAA;;QAEA;QACA,KAAAnE,OAAA;QAEAgB,OAAA,CAAAC,GAAA;MACA;IACA;IACAT,aAAA,WAAAA,cAAA;MACA,IAAAqD,IAAA;MACA;MACAA,IAAA,CAAA3D,UAAA;QACAkE,OAAA;QACAJ,UAAA;QACAE,gBAAA;QACAnC,KAAA,WAAAA,MAAA;UACA;UACAkC,YAAA,MAAAD,UAAA;UACAC,YAAA,MAAAC,gBAAA;UACA;QACA;QACAlC,KAAA,WAAAA,MAAA;UACA,IAAAqC,IAAA;UACA,KAAAL,UAAA,GAAAF,UAAA;YACA;YACA;YACAD,IAAA,CAAAtB,aAAA;YACAvB,OAAA,CAAA+C,IAAA;;YAEA;YACAM,IAAA,CAAAH,gBAAA,GAAAJ,UAAA;cACA;cACA,IAAAD,IAAA,CAAA7D,OAAA,IAAA6D,IAAA,CAAA7D,OAAA,CAAAsE,UAAA;gBAAA;gBACAtD,OAAA,CAAAC,GAAA;gBACA4C,IAAA,CAAA7D,OAAA,CAAAmE,KAAA;cACA;YACA,GAAAE,IAAA,CAAAD,OAAA;UACA,QAAAA,OAAA;QACA;MACA;IACA;IACAR,UAAA,WAAAA,WAAAf,GAAA,EAAA9C,IAAA;MAAA,IAAAwE,MAAA;MACA1E,YAAA,CAAAsE,KAAA,CAAAtB,GAAA;;MAEA;MACA,IAAA9C,IAAA,CAAAyE,WAAA;QACA;QACA,KAAAC,sBAAA,CAAA1E,IAAA;QACA;MACA;;MAEA;MACA,IAAAe,EAAA,GAAAf,IAAA,CAAA2E,KAAA;MACAhF,SAAA,MAAAS,GAAA,CAAAC,SAAA;QAAAU,EAAA,EAAAA;MAAA,GAAA6D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA,IAAAC,MAAA,GAAAF,GAAA,CAAAG,MAAA;UACAR,MAAA,CAAAS,sBAAA,CAAAF,MAAA;QACA;MACA;IACA;IACA;IACAL,sBAAA,WAAAA,uBAAAK,MAAA;MACA9D,OAAA,CAAAC,GAAA,cAAA6D,MAAA;;MAEA;MACA,KAAAG,OAAA,CAAAC,IAAA;QACAC,IAAA;QACArF,IAAA;MACA;IACA;IACA;IACAkF,sBAAA,WAAAA,uBAAAF,MAAA;MACA;MACA,IAAAA,MAAA,CAAAM,QAAA;QACA;QACA,KAAAH,OAAA,CAAAC,IAAA;UACAC,IAAA,EAAAL,MAAA,CAAAO,QAAA;UACAC,KAAA;YAAAxE,EAAA,EAAAgE,MAAA,CAAAS;UAAA;QACA;MACA;QACA;QACA,KAAAN,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAG,KAAA;YAAAxE,EAAA,EAAAgE,MAAA,CAAAhE;UAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}