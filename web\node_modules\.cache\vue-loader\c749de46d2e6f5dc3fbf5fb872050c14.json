{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderCustomerModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderCustomerModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import Vue from 'vue'\n  import {ACCESS_TOKEN} from \"@/store/mutation-types\"\n  import JImageUpload from '../../../../components/jeecg/JImageUpload'\n\n  export default {\n    name: \"JeecgOrderCustomerModal\",\n    components: { JImageUpload },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        // 表头\n        columns: [\n          {\n            title: '客户名',\n            align: \"center\",\n            dataIndex: 'name',\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex',\n          },\n          {\n            title: '身份证号码',\n            align: \"center\",\n            dataIndex: 'idcard',\n          },\n          {\n            title: '身份证扫描件',\n            align: \"center\",\n            dataIndex: 'idcardPic',\n          },\n          {\n            title: '电话',\n            dataIndex: 'telphone',\n            align: \"center\",\n          },\n          {\n            title: '订单号码',\n            dataIndex: 'orderId',\n            align: \"center\",\n          },\n          {\n            title: '创建人',\n            dataIndex: 'createBy',\n            align: \"center\",\n          },\n          {\n            title: '创建时间',\n            dataIndex: 'createTime',\n            align: \"center\",\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updateBy',\n            align: \"center\",\n          },\n          {\n            title: '更新人',\n            dataIndex: 'updateTime',\n            align: \"center\",\n          },\n        ],\n        fileList: [],\n        disableSubmit: false,\n        selectedRowKeys: [],\n        orderId: \"\",\n        hiding: false,\n        headers: {},\n        picUrl: \"\",\n        picArray:[],\n        previewVisible: false,\n        previewImage: '',\n        addStatus: false,\n        editStatus: false,\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        url: {\n          add: \"/test/order/addCustomer\",\n          edit: \"/test/order/editCustomer\",\n          fileUpload: window._CONFIG['domianURL'] + \"/sys/common/upload\",\n          getOrderCustomerList: \"/test/order/listOrderCustomerByMainId\",\n        },\n        validatorRules: {\n          telphone: {rules: [{validator: this.validateMobile}]},\n          idcard: {rules: [{validator: this.validateIdCard}]}\n        },\n      }\n    },\n    computed: {\n      uploadAction: function () {\n        return this.url.fileUpload;\n      }\n    },\n    created() {\n      const token = Vue.ls.get(ACCESS_TOKEN);\n      this.headers = {\"X-Access-Token\": token}\n    },\n    methods: {\n      add(orderId) {\n        this.hiding = true;\n        if (orderId) {\n          this.orderId = orderId;\n          this.edit({orderId}, '');\n        } else {\n          this.$message.warning(\"请选择一个客户信息\");\n        }\n      },\n      detail(record) {\n        this.edit(record, 'd');\n      },\n      edit(record, v) {\n        if (v == 'e') {\n          this.hiding = false;\n          this.disableSubmit = false;\n        } else if (v == 'd') {\n          this.hiding = false;\n          this.disableSubmit = true;\n        } else {\n          this.hiding = true;\n          this.disableSubmit = false;\n        }\n\n        this.form.resetFields();\n        this.orderId = record.orderId;\n        this.model = Object.assign({}, record);\n        if (record.id) {\n          this.hiding = false;\n          this.addStatus = false;\n          this.editStatus = true;\n          this.$nextTick(() => {\n            this.form.setFieldsValue(pick(this.model, 'id', 'name', 'sex', 'idcard','telphone', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'))\n          });\n          setTimeout(() => {\n            this.fileList = record.idcardPic\n          }, 5)\n        } else {\n          this.addStatus = false;\n          this.editStatus = true;\n        }\n        this.visible = true;\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n        this.picUrl = \"\";\n        this.fileList=[];\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            console.log(formData);\n            formData.orderId = this.orderId;\n            if(this.fileList != '') {\n              formData.idcardPic = this.fileList;\n            }else{\n              formData.idcardPic = '';\n            }\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close();\n      },\n      validateMobile(rule, value, callback) {\n        if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\\d{8}$/).test(value)) {\n          callback();\n        } else {\n          callback(\"您的手机号码格式不正确!\");\n        }\n      },\n      validateIdCard(rule, value, callback) {\n        if (!value || new RegExp(/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/).test(value)) {\n          callback();\n        } else {\n          callback(\"您的身份证号码格式不正确!\");\n        }\n      },\n    }\n  }\n", {"version": 3, "sources": ["JeecgOrderCustomerModal.vue"], "names": [], "mappings": ";AAqEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JeecgOrderCustomerModal.vue", "sourceRoot": "src/views/jeecg/tablist/form", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    :okButtonProps=\"{ props: {disabled: disableSubmit} }\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <!-- 编辑 -->\n    <a-spin :spinning=\"confirmLoading\" v-if=\"editStatus\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"客户姓名\"\n          hasFeedback>\n          <a-input placeholder=\"请输入客户姓名\" v-decorator=\"['name', {rules: [{ required: true, message: '请输入客户姓名!' }]}]\"\n                   :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"性别\"\n          hasFeedback>\n          <a-select v-decorator=\"['sex', {}]\" placeholder=\"请选择性别\">\n            <a-select-option value=\"1\">男性</a-select-option>\n            <a-select-option value=\"2\">女性</a-select-option>\n          </a-select>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"身份证号码\"\n          hasFeedback>\n          <a-input placeholder=\"请输入身份证号码\" v-decorator=\"['idcard', validatorRules.idcard]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"身份证扫描件\"\n          hasFeedback>\n          <j-image-upload text=\"上传\" v-model=\"fileList\" :isMultiple=\"true\"></j-image-upload>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"联系方式\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'telphone', validatorRules.telphone]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单号码\"\n          v-model=\"this.orderId\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'orderId', {}]\" disabled=\"disabled\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import Vue from 'vue'\n  import {ACCESS_TOKEN} from \"@/store/mutation-types\"\n  import JImageUpload from '../../../../components/jeecg/JImageUpload'\n\n  export default {\n    name: \"JeecgOrderCustomerModal\",\n    components: { JImageUpload },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        // 表头\n        columns: [\n          {\n            title: '客户名',\n            align: \"center\",\n            dataIndex: 'name',\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex',\n          },\n          {\n            title: '身份证号码',\n            align: \"center\",\n            dataIndex: 'idcard',\n          },\n          {\n            title: '身份证扫描件',\n            align: \"center\",\n            dataIndex: 'idcardPic',\n          },\n          {\n            title: '电话',\n            dataIndex: 'telphone',\n            align: \"center\",\n          },\n          {\n            title: '订单号码',\n            dataIndex: 'orderId',\n            align: \"center\",\n          },\n          {\n            title: '创建人',\n            dataIndex: 'createBy',\n            align: \"center\",\n          },\n          {\n            title: '创建时间',\n            dataIndex: 'createTime',\n            align: \"center\",\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updateBy',\n            align: \"center\",\n          },\n          {\n            title: '更新人',\n            dataIndex: 'updateTime',\n            align: \"center\",\n          },\n        ],\n        fileList: [],\n        disableSubmit: false,\n        selectedRowKeys: [],\n        orderId: \"\",\n        hiding: false,\n        headers: {},\n        picUrl: \"\",\n        picArray:[],\n        previewVisible: false,\n        previewImage: '',\n        addStatus: false,\n        editStatus: false,\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        url: {\n          add: \"/test/order/addCustomer\",\n          edit: \"/test/order/editCustomer\",\n          fileUpload: window._CONFIG['domianURL'] + \"/sys/common/upload\",\n          getOrderCustomerList: \"/test/order/listOrderCustomerByMainId\",\n        },\n        validatorRules: {\n          telphone: {rules: [{validator: this.validateMobile}]},\n          idcard: {rules: [{validator: this.validateIdCard}]}\n        },\n      }\n    },\n    computed: {\n      uploadAction: function () {\n        return this.url.fileUpload;\n      }\n    },\n    created() {\n      const token = Vue.ls.get(ACCESS_TOKEN);\n      this.headers = {\"X-Access-Token\": token}\n    },\n    methods: {\n      add(orderId) {\n        this.hiding = true;\n        if (orderId) {\n          this.orderId = orderId;\n          this.edit({orderId}, '');\n        } else {\n          this.$message.warning(\"请选择一个客户信息\");\n        }\n      },\n      detail(record) {\n        this.edit(record, 'd');\n      },\n      edit(record, v) {\n        if (v == 'e') {\n          this.hiding = false;\n          this.disableSubmit = false;\n        } else if (v == 'd') {\n          this.hiding = false;\n          this.disableSubmit = true;\n        } else {\n          this.hiding = true;\n          this.disableSubmit = false;\n        }\n\n        this.form.resetFields();\n        this.orderId = record.orderId;\n        this.model = Object.assign({}, record);\n        if (record.id) {\n          this.hiding = false;\n          this.addStatus = false;\n          this.editStatus = true;\n          this.$nextTick(() => {\n            this.form.setFieldsValue(pick(this.model, 'id', 'name', 'sex', 'idcard','telphone', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'))\n          });\n          setTimeout(() => {\n            this.fileList = record.idcardPic\n          }, 5)\n        } else {\n          this.addStatus = false;\n          this.editStatus = true;\n        }\n        this.visible = true;\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n        this.picUrl = \"\";\n        this.fileList=[];\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            console.log(formData);\n            formData.orderId = this.orderId;\n            if(this.fileList != '') {\n              formData.idcardPic = this.fileList;\n            }else{\n              formData.idcardPic = '';\n            }\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close();\n      },\n      validateMobile(rule, value, callback) {\n        if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\\d{8}$/).test(value)) {\n          callback();\n        } else {\n          callback(\"您的手机号码格式不正确!\");\n        }\n      },\n      validateIdCard(rule, value, callback) {\n        if (!value || new RegExp(/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/).test(value)) {\n          callback();\n        } else {\n          callback(\"您的身份证号码格式不正确!\");\n        }\n      },\n    }\n  }\n</script>\n\n<style scoped>\n  /* tile uploaded pictures */\n  .upload-list-inline > > > .ant-upload-list-item {\n    float: left;\n    width: 200px;\n    margin-right: 8px;\n  }\n\n  .upload-list-inline > > > .ant-upload-animate-enter {\n    animation-name: uploadAnimateInlineIn;\n  }\n\n  .upload-list-inline > > > .ant-upload-animate-leave {\n    animation-name: uploadAnimateInlineOut;\n  }\n</style>"]}]}