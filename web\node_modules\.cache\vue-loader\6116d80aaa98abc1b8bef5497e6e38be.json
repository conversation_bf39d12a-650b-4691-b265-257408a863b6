{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\testManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\testManage.vue", "mtime": 1753195481732}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport Vue from 'vue'\nimport moment from 'moment'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport {\n  getPaperList,\n  addPaper,\n  editPaper,\n  deletePaper,\n  deleteBatchPaper,\n  queryPaperById,\n  importPapers,\n  exportPapers,\n  getPaperQuestions,\n  autoFormatPaperTemplate\n} from '@/api/examSystem'\nimport PaperModal from './modules/PaperModal'\nimport { mapGetters } from 'vuex'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\n\nexport default {\n  name: \"TestManage\",\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\n  components: {\n    PaperModal\n  },\n  data() {\n    return {\n      description: '试卷管理页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/testManage/list',\n        delete: '/teaching/examSystem/testManage/delete',\n        deleteBatch: '/teaching/examSystem/testManage/deleteBatch',\n        exportXlsUrl: '/teaching/examSystem/testManage/exportXls',\n        importExcelUrl: '/teaching/examSystem/testManage/importExcel'\n      },\n      // 表头\n      columns: [\n        {\n          title: '序号',\n          dataIndex: '',\n          key:'rowIndex',\n          width:60,\n          align:\"center\",\n          customRender:function (t,r,index) {\n            return parseInt(index)+1;\n          }\n        },\n        {\n          title: '试卷ID',\n          align: 'center',\n          dataIndex: 'id',\n          width: '120px'\n        },\n        {\n          title: '标题',\n          align: 'center',\n          dataIndex: 'title'\n        },\n        {\n          title: '科目',\n          align: 'center',\n          dataIndex: 'subject',\n          sorter: true\n        },\n        {\n          title: '级别',\n          align: 'center',\n          dataIndex: 'level',\n          sorter: true\n        },\n        {\n          title: '难度',\n          align: 'center',\n          dataIndex: 'difficulty',\n          scopedSlots: { customRender: 'difficultySlot' },\n          sorter: true\n        },\n        {\n          title: '类型',\n          align: 'center',\n          dataIndex: 'type',\n          sorter: true\n        },\n        {\n          title: '年份',\n          align: 'center',\n          dataIndex: 'year',\n          sorter: true\n        },\n        {\n          title: '作者',\n          align: 'center',\n          dataIndex: 'author'\n        },\n        {\n          title: '考试时长',\n          align: 'center',\n          dataIndex: 'examDuration',\n          scopedSlots: { customRender: 'durationSlot' }\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          scopedSlots: { customRender: 'action' },\n          width: '200px'\n        }\n      ],\n      // 注意：dataSource, ipagination, loading, selectedRowKeys 等状态\n      // 已由JeecgListMixin提供，无需重复定义\n      // 导入模态框相关\n      importModalVisible: false,\n      importConfirmLoading: false,\n      uploadHeaders: {},\n      // 智能引用模式相关\n      importPreviewData: null,\n      selectedImportMode: 'complete', // 默认选择完整模式\n      // 导出相关\n      exportLoading: false,\n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      // 试卷预览相关\n      previewModalVisible: false,\n      previewLoading: false,\n      previewData: null,\n      previewContent: {},\n      // 导入文件相关\n      selectedFile: null,\n      isDragover: false,\n      // 自动格式化相关\n      autoTemplateModalVisible: false,\n      autoTemplateConfirmLoading: false,\n      autoTemplateSelectedFile: null,\n      isAutoTemplateDragover: false,\n      autoTemplateParam: {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo'])\n  },\n  mounted() {\n    // 注意：loadData() 已由JeecgListMixin自动调用，无需重复调用\n    this.uploadHeaders = {\n      'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n    }\n  },\n  methods: {\n    // 获取级别选项\n    getLevelOptions() {\n      const subject = this.queryParam.subject\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 年份选择面板变化\n    handleYearPanelChange(value, mode) {\n      this.queryParam.year = value\n      this.yearMode = 'year'\n    },\n    \n    // 年份选择变化\n    handleYearChange(date, dateString) {\n      this.queryParam.year = date\n    },\n    \n    // 注意：loadData、searchReset、handleTableChange 等方法\n    // 已由JeecgListMixin提供，无需重复实现\n    \n    // 选择行变化\n    onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys\n    },\n    \n    // 新增\n    handleAdd() {\n      this.$refs.modalForm.add()\n    },\n    \n    // 编辑\n    handleEdit(record) {\n      this.$refs.modalForm.edit(record)\n    },\n    \n    // 复制\n    handleCopy(record) {\n      this.$refs.modalForm.copy(record)\n    },\n    \n    // 预览\n    handlePreview(record) {\n      this.previewModalVisible = true\n      this.previewLoading = true\n      \n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          this.previewData = res.result\n          \n          // 解析试卷内容\n          if (this.previewData.content) {\n            try {\n              this.previewContent = JSON.parse(this.previewData.content)\n              \n              // 获取试卷题目详情\n              this.loadPaperQuestions(record.id)\n            } catch (e) {\n              this.previewContent = {}\n              console.error('解析试卷内容失败', e)\n              this.previewLoading = false\n            }\n          } else {\n            this.previewLoading = false\n          }\n        } else {\n          this.$message.warning(res.message || '获取试卷详情失败')\n          this.previewLoading = false\n        }\n      }).catch(() => {\n        this.previewLoading = false\n      })\n    },\n    \n    // 加载试卷题目详情\n    loadPaperQuestions(paperId) {\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then((res) => {\n        if (res.success) {\n          // 将题目按类型分组\n          const questionData = res.result || []\n          \n          // 创建题目类型到分数的映射\n          const scoreMap = {}\n          if (this.previewContent.questions) {\n            this.previewContent.questions.forEach(item => {\n              scoreMap[item.questionId] = item.score\n            })\n          }\n          \n          // 按题目类型分组\n          const singleChoiceQuestions = []\n          const judgmentQuestions = []\n          const programmingQuestions = []\n          \n          questionData.forEach(item => {\n            const question = item.question\n            const score = item.score\n            \n            // 处理题目数据\n            if (question) {\n              // 解析题目内容\n              let questionDetail = question\n              if (typeof question.content === 'string' && question.content) {\n                try {\n                  const contentObj = JSON.parse(question.content)\n                  questionDetail = { ...question, ...contentObj }\n                  \n                  // 处理编程题特殊内容\n                  if (question.questionType === 3) {\n                    // 转换样例格式\n                    if (contentObj.sample_cases) {\n                      questionDetail.samples = contentObj.sample_cases.map(sample => ({\n                        input: sample.input,\n                        output: sample.output\n                      }))\n                    }\n                    \n                    // 转换时间和内存限制\n                    questionDetail.timeLimit = contentObj.time_limit\n                    questionDetail.memoryLimit = contentObj.memory_limit\n                    questionDetail.description = contentObj.description\n                    questionDetail.inputFormat = contentObj.input_format\n                    questionDetail.outputFormat = contentObj.output_format\n                    questionDetail.hint = contentObj.hint\n                  }\n                } catch (e) {\n                  console.error('解析题目内容失败', e)\n                }\n              }\n              \n              // 根据题型分组\n              switch (question.questionType) {\n                case 1: // 单选题\n                  singleChoiceQuestions.push(questionDetail)\n                  break\n                case 2: // 判断题\n                  judgmentQuestions.push(questionDetail)\n                  break\n                case 3: // 编程题\n                  programmingQuestions.push(questionDetail)\n                  break\n              }\n            }\n          })\n          \n          // 更新预览内容\n          this.previewContent = {\n            ...this.previewContent,\n            singleChoiceQuestions,\n            judgmentQuestions,\n            programmingQuestions,\n            // 使用已有分数或默认值\n            singleChoiceScore: this.previewContent.singleChoiceScore || 2,\n            judgmentScore: this.previewContent.judgmentScore || 2,\n            programmingScore: this.previewContent.programmingScore || 25\n          }\n        } else {\n          this.$message.warning('获取试卷题目失败')\n        }\n        this.previewLoading = false\n      }).catch(() => {\n        this.$message.warning('获取试卷题目失败')\n        this.previewLoading = false\n      })\n    },\n    \n    // 关闭预览\n    handlePreviewCancel() {\n      this.previewModalVisible = false\n      this.previewData = null\n      this.previewContent = {}\n    },\n    \n    // 删除\n    handleDelete(id) {\n      deletePaper({id: id}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '删除成功')\n          this.loadData()\n        } else {\n          this.$message.warning(res.message || '删除失败')\n        }\n      })\n    },\n    \n    // 批量删除\n    batchDel() {\n      if (this.selectedRowKeys.length <= 0) {\n        this.$message.warning('请选择至少一条记录！')\n        return\n      }\n      \n      // 添加确认对话框防止误操作\n      this.$confirm({\n        title: '批量删除确认',\n        content: `确定要删除所选择的 ${this.selectedRowKeys.length} 条试卷吗？此操作不可恢复！`,\n        okText: '确定删除',\n        okType: 'danger',\n        cancelText: '取消',\n        onOk: () => {\n          // 执行原有的删除逻辑\n          const ids = this.selectedRowKeys.join(',')\n          deleteBatchPaper({ids: ids}).then((res) => {\n            if (res.success) {\n              this.$message.success(res.message || '批量删除成功')\n              this.loadData()\n              this.selectedRowKeys = []\n            } else {\n              this.$message.warning(res.message || '批量删除失败')\n            }\n          })\n        }\n      });\n    },\n    \n    // 导入\n    handleImport() {\n      // 重置导入表单\n      this.importParam = {\n        title: undefined, // 使用undefined代替空字符串，确保placeholder显示\n        subject: undefined,\n        level: undefined,\n        difficulty: undefined,\n        type: undefined,\n        year: undefined,\n        author: undefined,\n        examDuration: undefined // 移除默认考试时长，让后端根据级别自动设置\n      }\n      this.selectedFile = null\n      this.importModalVisible = true\n    },\n    \n    // 导出\n    handleExport() {\n      // 添加提示，告知用户只导出符合查询条件的试卷\n      this.$confirm({\n        title: '批量导出试卷',\n        content: '将导出符合当前查询条件的试卷。如需导出全部试卷，请先清空查询条件。',\n        onOk: () => {\n          const params = {...this.queryParam}\n          \n          // 处理年份参数\n          if (params.year) {\n            params.year = moment(params.year).format('YYYY')\n          }\n          \n          this.exportLoading = true\n          \n          exportPapers(params).then((res) => {\n            if (res.success) {\n              // 检查res.result的内容\n              let contentToExport = null\n              \n              // 处理不同的返回结构\n              if (res.result && typeof res.result === 'object' && res.result.content) {\n                // 新格式，从content字段获取内容\n                contentToExport = res.result.content\n              } else if (typeof res.result === 'string') {\n                // 旧格式，直接使用result字符串\n                contentToExport = res.result\n              }\n              \n              // 如果内容为空，显示错误消息\n              if (!contentToExport) {\n                this.$message.error('导出内容为空，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 确保内容是字符串\n              if (typeof contentToExport !== 'string') {\n                contentToExport = JSON.stringify(contentToExport, null, 2)\n              }\n              \n              // 避免导出\"null\"字符串\n              if (contentToExport === \"null\") {\n                this.$message.error('导出内容异常，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 创建Blob对象，使用适当的MIME类型\n              const blob = new Blob([contentToExport], { type: 'text/plain;charset=utf-8' })\n              const fileName = `试卷导出_${moment().format('YYYYMMDDHHmmss')}.txt`\n              \n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName)\n              } else {\n                // 现代浏览器\n                const link = document.createElement('a')\n                link.href = URL.createObjectURL(blob)\n                link.download = fileName\n                link.style.display = 'none'\n                document.body.appendChild(link)\n                link.click()\n                \n                // 清理创建的对象URL\n                setTimeout(() => {\n                  URL.revokeObjectURL(link.href)\n                  document.body.removeChild(link)\n                }, 100)\n              }\n              \n              this.$message.success('导出成功')\n            } else {\n              this.$message.warning(res.message || '导出失败')\n            }\n            this.exportLoading = false\n          }).catch((error) => {\n            console.error('导出失败:', error)\n            this.$message.error('导出过程发生错误')\n            this.exportLoading = false\n          })\n        }\n      })\n    },\n    \n    // 模态框提交回调\n    modalFormOk() {\n      this.loadData()\n    },\n    \n    // 获取级别选项\n    getLevelOptionsBySubject(subject) {\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 导入科目变化\n    onImportSubjectChange(value) {\n      this.importParam.subject = value\n    },\n    \n    // 导入级别变化\n    onImportLevelChange(value) {\n      this.importParam.level = value\n      console.log('选择级别:', value)\n      \n      // 根据级别自动设置考试时长\n      if (value) {\n        // 更新考试时长显示，但不传递到后端，让后端根据级别自动设置\n        // 这里只是为了在界面上显示预期的考试时长\n        const duration = this.getExamDurationByLevel(value)\n        this.$nextTick(() => {\n          this.$set(this, 'expectedDuration', duration)\n        })\n      }\n    },\n    \n    // 根据级别计算考试时长\n    getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n      \n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.includes('五') || level.includes('六') || level.includes('七') || level.includes('八') || \n            level.includes('5') || level.includes('6') || level.includes('7') || level.includes('8')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    \n    // 上传前验证\n    beforeUpload(file) {\n      const isTxt = file.type === 'text/plain' || file.name.endsWith('.txt')\n      if (!isTxt) {\n        this.$message.error('只能上传 .txt 文件!')\n      }\n      return isTxt\n    },\n    \n    // 上传状态变化\n    handleUploadChange(info) {\n      this.fileList = [...info.fileList].slice(-1)\n    },\n    \n    // 导入确认\n    handleImportOk() {\n      if (!this.selectedFile) {\n        this.$message.warning('请选择要导入的文件')\n        return\n      }\n\n      // 如果还没有预览过，先进行预览\n      if (!this.importPreviewData) {\n        this.previewImport()\n        return\n      }\n\n      // 如果已经预览过，显示选择模式对话框\n      this.showImportModeDialog()\n    },\n\n    // 预览导入\n    previewImport() {\n      this.importConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      formData.append('mode', 'preview')\n\n      this.performImportRequest(formData, (res) => {\n        this.importConfirmLoading = false\n        if (res.success && res.result) {\n          this.importPreviewData = res.result\n\n          // 无论是否可以直接导入，都显示预览窗口让用户选择\n          // 这样用户可以看到完整的检测结果和导入选项\n          this.showImportModeDialog()\n        } else {\n          // 如果错误消息较长，使用通知框显示\n          if (res.message && res.message.length > 20) {\n            this.$notification.error({\n              message: '预览失败',\n              description: res.message,\n              duration: 8,\n              style: {\n                width: '400px',\n                marginLeft: `${335 - 400}px`,\n              }\n            })\n          } else {\n            this.$message.error(res.message || '预览失败')\n          }\n        }\n      })\n    },\n\n    // 获取当前检测状态\n    getCurrentDetectionStatus() {\n      const previewData = this.importPreviewData\n      if (!previewData) return 'unknown'\n\n      const { newQuestions, duplicateQuestions, similarQuestions } = previewData\n      const totalQuestions = newQuestions + duplicateQuestions + similarQuestions\n\n      if (newQuestions === totalQuestions) {\n        return 'all_new' // 全部新题\n      } else if (duplicateQuestions === totalQuestions) {\n        return 'all_duplicate' // 全部重复\n      } else {\n        return 'partial_duplicate' // 部分重复\n      }\n    },\n\n    // 显示导入模式选择对话框\n    showImportModeDialog() {\n      const previewData = this.importPreviewData\n      const currentStatus = this.getCurrentDetectionStatus()\n\n      // 使用自定义Modal显示导入方式选择\n      this.$info({\n        title: '检测完成 - 选择导入方式',\n        width: 520,\n        okButtonProps: { style: { display: 'none' } }, // 隐藏默认的确定按钮\n        maskClosable: false, // 防止点击遮罩关闭\n        content: (h) => {\n          return h('div', { style: { lineHeight: '1.6' } }, [\n            // 检测结果区域\n            h('div', { style: { marginBottom: '20px' } }, [\n              h('h4', { style: { margin: '0 0 12px 0', color: '#1890ff' } }, '📊 检测结果'),\n              h('div', {\n                style: {\n                  background: '#f6f8fa',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  marginBottom: '16px'\n                }\n              }, [\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '📝 共解析：',\n                  h('strong', {}, previewData.totalQuestions),\n                  ' 个题目'\n                ]),\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '✨ 新题目：',\n                  h('strong', { style: { color: '#52c41a' } }, previewData.newQuestions),\n                  ' 个'\n                ]),\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '🔄 重复题目：',\n                  h('strong', { style: { color: '#fa8c16' } }, previewData.duplicateQuestions),\n                  ' 个'\n                ]),\n                h('div', {}, [\n                  '🔍 相似题目：',\n                  h('strong', { style: { color: '#722ed1' } }, previewData.similarQuestions),\n                  ' 个'\n                ])\n              ])\n            ]),\n\n            // 模式选择区域\n            h('div', {}, [\n              h('h4', { style: { margin: '0 0 12px 0', color: '#1890ff' } }, '🎯 导入方式'),\n\n              // 完整模式\n              h('div', {\n                style: {\n                  background: '#e6f7ff',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  marginBottom: '12px',\n                  borderLeft: '4px solid #1890ff'\n                }\n              }, [\n                h('div', { style: { fontWeight: 'bold', marginBottom: '4px' } }, '🎯 完整模式（推荐）'),\n                h('div', { style: { color: '#666', fontSize: '13px', lineHeight: '1.4' } }, [\n                  '• 全部新题：导入所有题目，创建试卷',\n                  currentStatus === 'all_new' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 部分重复：导入新题+引用重复题，创建完整试卷',\n                  currentStatus === 'partial_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 全部重复：引用所有题目，创建试卷（避免重复存储）',\n                  currentStatus === 'all_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : ''\n                ])\n              ]),\n\n              // 新题模式\n              h('div', {\n                style: {\n                  background: '#fff2e8',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  borderLeft: '4px solid #fa8c16'\n                }\n              }, [\n                h('div', { style: { fontWeight: 'bold', marginBottom: '4px' } }, '📝 新题模式'),\n                h('div', { style: { color: '#666', fontSize: '13px', lineHeight: '1.4' } }, [\n                  '• 全部新题：导入所有题目，创建试卷',\n                  currentStatus === 'all_new' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 部分重复：只导入新题目，创建试卷（可能不完整）',\n                  currentStatus === 'partial_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 全部重复：',\n                  h('span', { style: { color: '#ff4d4f', fontWeight: 'bold' } }, '无法创建试卷'),\n                  currentStatus === 'all_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : ''\n                ])\n              ])\n            ]),\n\n            // 添加自定义按钮区域\n            h('div', {\n              style: {\n                marginTop: '20px',\n                textAlign: 'center',\n                borderTop: '1px solid #f0f0f0',\n                paddingTop: '16px',\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                gap: '12px',\n                flexWrap: 'wrap'\n              }\n            }, [\n              h('a-button', {\n                props: {\n                  type: 'primary',\n                  size: 'large',\n                  style: { minWidth: '120px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框并执行完整模式导入\n                    this.$destroyAll()\n                    this.confirmImport(true) // 完整模式，允许引用\n                  }\n                }\n              }, '完整模式（推荐）'),\n\n              h('a-button', {\n                props: {\n                  size: 'large',\n                  style: { minWidth: '100px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框并执行新题模式导入\n                    this.$destroyAll()\n                    this.confirmImport(false) // 新题模式，不允许引用\n                  }\n                }\n              }, '新题模式'),\n\n              h('a-button', {\n                props: {\n                  type: 'default',\n                  size: 'large',\n                  style: { minWidth: '80px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框，不进行任何导入\n                    this.$destroyAll()\n                    this.$message.info('已取消导入')\n                  }\n                }\n              }, '取消')\n            ])\n          ])\n        }\n      })\n    },\n\n    // 确认导入\n    confirmImport(allowReference) {\n      this.importConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      formData.append('mode', 'confirm')\n      formData.append('allowReference', allowReference)\n\n      this.performImportRequest(formData, (res) => {\n        this.importConfirmLoading = false\n\n        // 添加调试信息\n        console.log('导入响应:', res)\n        console.log('allowReference:', allowReference)\n\n        if (res.success) {\n          // 检查返回结果中是否有详细的消息\n          if (res.result && res.result.message) {\n            // 如果消息包含统计信息，使用通知框显示\n            if (res.result.message.includes('道题目') && res.result.message.includes('重复题目')) {\n              this.$notification.success({\n                message: '导入成功',\n                description: res.result.message,\n                duration: 6,\n                style: {\n                  width: '400px',\n                  marginLeft: `${335 - 400}px`,\n                }\n              })\n            } else {\n              // 普通消息使用message显示\n              this.$message.success(res.result.message)\n            }\n          } else {\n            // 使用默认消息\n            this.$message.success(res.message || '导入成功')\n          }\n          this.handleImportCancel()\n          this.loadData()\n        } else {\n          console.error('导入失败，响应详情:', res)\n\n          // 检查是否是试卷重复检测 - 数据可能在 res.result 中\n          const resultData = res.result || res;\n          if (resultData.isDuplicatePaper && resultData.duplicatePaper) {\n            console.log(\"弹出试卷重复警告对话框\")\n            console.log(\"传递给对话框的数据:\", resultData)\n            console.log(\"uploadedPaper:\", resultData.uploadedPaper)\n            console.log(\"duplicatePaper:\", resultData.duplicatePaper)\n            // 显示试卷重复警告对话框\n            this.showDuplicatePaperDialog(resultData)\n          } else {\n            // 普通错误消息\n            this.$message.error(res.message || '导入失败')\n          }\n        }\n      })\n    },\n\n    // 显示试卷重复警告对话框\n    showDuplicatePaperDialog(res) {\n      console.log('重复检测对话框数据:', res)\n      const duplicatePaper = res.duplicatePaper\n      const uploadedPaper = res.uploadedPaper || {}\n      console.log('上传试卷信息:', uploadedPaper)\n      console.log('重复试卷信息:', duplicatePaper)\n\n      this.$warning({\n        title: '⚠️ 检测到重复试卷',\n        content: h => h('div', { style: { lineHeight: '1.6' } }, [\n          h('div', { style: { marginBottom: '12px', color: '#ff4d4f', fontWeight: 'bold' } },\n            '发现重复试卷！'),\n\n          // 上传的文件信息\n          h('div', { style: { marginBottom: '8px' } }, [\n            '上传文件：',\n            h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, (this.selectedFile && this.selectedFile.name) || '未知')\n          ]),\n\n          // 解析出的试卷信息\n          h('div', { style: { marginBottom: '8px' } }, [\n            '上传试卷：',\n            h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, uploadedPaper.title || '未知'),\n            h('span', { style: { fontSize: '12px', color: '#999', marginLeft: '8px' } })\n          ]),\n\n          // 数据库中的重复试卷\n          h('div', { style: { marginBottom: '8px' } }, [\n            '重复试卷：',\n            h('span', { style: { fontWeight: 'bold', color: '#ff4d4f' } }, duplicatePaper.title)\n          ]),\n\n          // 详细信息\n          h('div', { style: { marginBottom: '12px' } }, [\n            '科目：',\n            h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.subject),\n            '，级别：',\n            h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.level),\n            duplicatePaper.year ? ['，年份：', h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.year)] : ''\n          ]),\n\n          h('div', { style: { color: '#666', fontSize: '13px' } },\n            '建议检查是否重复导入，或修改试卷标题后重新导入。')\n        ]),\n        okText: '我知道了',\n        width: 500\n      })\n    },\n\n    // 执行导入请求的通用方法\n    performImportRequest(formData, callback) {\n      // 使用原生XMLHttpRequest处理文件上传，避免序列化问题\n      const xhr = new XMLHttpRequest()\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      const baseURL = window._CONFIG['domianURL'] || ''\n\n      // 设置请求超时\n      xhr.timeout = 120000 // 2分钟超时\n\n      // 设置上传完成回调\n      xhr.onreadystatechange = () => {\n        if (xhr.readyState === 4) {\n          if (xhr.status === 200) {\n            try {\n              const res = JSON.parse(xhr.responseText)\n              callback(res)\n            } catch (e) {\n              console.error('解析响应失败:', e)\n              console.error('原始响应文本:', xhr.responseText)\n              this.importConfirmLoading = false\n              this.$message.error('导入处理失败，请检查文件格式')\n            }\n          } else {\n            console.error('导入失败，响应内容:', xhr.responseText)\n            this.importConfirmLoading = false\n            this.$message.error(`导入失败，状态码: ${xhr.status}`)\n          }\n        }\n      }\n\n      // 设置上传进度回调\n      xhr.upload.onprogress = (event) => {\n        if (event.lengthComputable) {\n          const percentComplete = Math.round((event.loaded * 100) / event.total)\n          console.log(`上传进度: ${percentComplete}%`)\n        }\n      }\n\n      // 设置超时处理\n      xhr.ontimeout = () => {\n        this.importConfirmLoading = false\n        this.$message.error('上传超时，请检查文件大小或网络连接')\n      }\n\n      // 设置错误处理\n      xhr.onerror = () => {\n        this.importConfirmLoading = false\n        this.$message.error('网络错误，导入失败')\n      }\n\n      // 发送请求\n      xhr.open('POST', `${baseURL}/teaching/examSystem/testManage/import`, true)\n      xhr.setRequestHeader('X-Access-Token', token)\n      xhr.send(formData)\n    },\n    \n    // 导入取消\n    handleImportCancel() {\n      this.importModalVisible = false\n      this.selectedFile = null\n      this.importConfirmLoading = false\n      this.importPreviewData = null\n      this.selectedImportMode = 'complete'\n    },\n    \n    // 下载导入模板\n    downloadTemplate() {\n      const templateContent = `【元数据】\n【试卷标题】请填写试卷标题，例如：C++一级2025年03月\n【所属科目】请填写科目，例如：C++、Python、Scratch\n【所属级别】请填写级别，例如：1、2、3\n【难度】请填写难度，例如：1、2、3（1=简单，2=中等，3=困难）\n【类型】请填写类型，例如：真题、模拟题\n【年份】请填写年份，例如：2025\n\n【一、单选题】\n【1.】关于C++语言的描述，下面哪个是正确的？\n【A. 】C++语言不支持面向对象编程。\n【B. 】C++语言的程序最终会被编译成机器指令执行。\n【C. 】C++语言只能用于游戏开发。\n【D. 】C++语言中，变量可以不声明就直接使用。\n【答案】B\n【解析】C++是一种支持面向对象的高级编程语言，应用广泛，变量使用前必须声明。程序需要通过编译器转换为机器码才能运行。\n\n【二、判断题】\n【1.】在C++中，表达式 N * 2 % N 中如果 N 的值为正整数，则其值为0。\n【答案】正确\n【解析】N*2可以被N整除，所以余数为0。\n\n【三、编程题】\n【1.】四舍五入\n【- 时间限制：1.0 s - 内存限制：512.0 MB】\n【题目描述】\n四舍五入是一种常见的近似计算方法。现在，给定 n 个整数，你需要将每个整数四舍五入到最接近的整十数。\n【输入格式】\n共 n + 1 行，第一行是一个整数 n，表示接下来输入的整数个数。\n接下来 n 行，每行一个整数 ai。\n【输出格式】\nn 行，每行一个整数，表示每个整数四舍五入后的结果。\n【输入样例 1】\n5\n43\n58\n25\n【输出样例 1】\n40\n60\n30\n【数据范围】\n对于所有测试点，保证 1 ≤ n ≤ 100，1 ≤ ai ≤ 10000。`\n\n      // 创建下载链接\n      const blob = new Blob([templateContent], { type: 'text/plain;charset=utf-8' })\n      const link = document.createElement('a')\n      link.href = window.URL.createObjectURL(blob)\n      link.download = '纯文本模板.txt'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      window.URL.revokeObjectURL(link.href)\n    },\n    \n    // 导入年份面板变化\n    handleImportYearPanelChange(value, mode) {\n      this.importParam.year = value\n      this.yearMode = 'year'\n    },\n    \n    // 处理文件选择\n    onFileSelected(e) {\n      const file = e.target.files[0]\n      if (!file) return\n      \n      // 检查文件类型\n      if (this.beforeUpload(file)) {\n        this.selectedFile = file\n      }\n      \n      // 重置文件输入框，使相同文件可以再次选择\n      e.target.value = ''\n    },\n\n    // 移除选择的文件\n    removeSelectedFile() {\n      this.selectedFile = null\n      if (this.$refs.fileInput) {\n        this.$refs.fileInput.value = ''\n      }\n    },\n\n    // 触发文件输入框点击\n    triggerFileInput() {\n      this.$refs.fileInput.click()\n    },\n    \n    // 文件拖拽进入\n    handleDragEnter() {\n      this.isDragover = true\n    },\n\n    // 文件拖拽离开\n    handleDragLeave() {\n      this.isDragover = false\n    },\n\n    // 处理文件拖放\n    onFileDrop(event) {\n      this.isDragover = false\n      const file = event.dataTransfer.files[0]\n      if (file) {\n        if (this.beforeUpload(file)) {\n          this.selectedFile = file\n        }\n      }\n    },\n\n    // 自动格式化相关方法\n    showAutoTemplateModal() {\n      this.autoTemplateModalVisible = true\n      // 重置表单数据\n      this.autoTemplateParam = {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateCancel() {\n      this.autoTemplateModalVisible = false\n      this.autoTemplateSelectedFile = null\n      this.autoTemplateParam = {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n    },\n\n    triggerAutoTemplateFileInput() {\n      this.$refs.autoTemplateFileInput.click()\n    },\n\n    onAutoTemplateFileChange(event) {\n      const files = event.target.files\n      if (files.length > 0) {\n        const file = files[0]\n        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n          this.autoTemplateSelectedFile = file\n        } else {\n          this.$message.warning('请选择.txt格式的文本文件')\n          event.target.value = ''\n        }\n      }\n    },\n\n    handleAutoTemplateDragEnter() {\n      this.isAutoTemplateDragover = true\n    },\n\n    handleAutoTemplateDragLeave() {\n      this.isAutoTemplateDragover = false\n    },\n\n    handleAutoTemplateDrop(e) {\n      e.preventDefault()\n      this.isAutoTemplateDragover = false\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onAutoTemplateFileChange({ target: { files } })\n      }\n    },\n\n    removeAutoTemplateSelectedFile() {\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateOk() {\n      // 验证必填项\n      if (!this.autoTemplateParam.title) {\n        this.$message.warning('请输入试卷标题')\n        return\n      }\n      if (!this.autoTemplateParam.subject) {\n        this.$message.warning('请选择科目')\n        return\n      }\n      if (!this.autoTemplateParam.level) {\n        this.$message.warning('请输入级别')\n        return\n      }\n      if (!this.autoTemplateParam.difficulty) {\n        this.$message.warning('请选择难度')\n        return\n      }\n      if (!this.autoTemplateParam.type) {\n        this.$message.warning('请选择类型')\n        return\n      }\n      if (!this.autoTemplateParam.year) {\n        this.$message.warning('请输入年份')\n        return\n      }\n      if (!this.autoTemplateSelectedFile) {\n        this.$message.warning('请选择要格式化的文件')\n        return\n      }\n\n      this.autoTemplateConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.autoTemplateSelectedFile)\n      formData.append('title', this.autoTemplateParam.title)\n      formData.append('subject', this.autoTemplateParam.subject)\n      formData.append('level', this.autoTemplateParam.level)\n      formData.append('difficulty', this.autoTemplateParam.difficulty)\n      formData.append('type', this.autoTemplateParam.type)\n      formData.append('year', this.autoTemplateParam.year)\n\n      // 调用自动格式化API\n      autoFormatPaperTemplate(formData).then((res) => {\n        if (res.success) {\n          this.$message.success('自动格式化成功，正在下载格式化后的文件...')\n\n          if (res.result) {\n            this.downloadFormattedFile(res.result)\n          } else {\n            this.$message.error('未获取到格式化内容')\n          }\n\n          this.handleAutoTemplateCancel()\n        } else {\n          this.$message.warning(res.message || '自动格式化失败')\n        }\n        this.autoTemplateConfirmLoading = false\n      }).catch((error) => {\n        console.error('自动格式化失败:', error)\n        this.$message.error('自动格式化失败，请重试')\n        this.autoTemplateConfirmLoading = false\n      })\n    },\n\n    // 下载格式化后的文件\n    downloadFormattedFile(content) {\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n      const blobUrl = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = blobUrl\n      link.download = '格式化试卷文件.txt'\n      document.body.appendChild(link)\n      link.click()\n      setTimeout(() => {\n        window.URL.revokeObjectURL(blobUrl)\n        document.body.removeChild(link)\n      }, 100)\n    },\n\n    // 格式化文件大小\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n  }\n}\n", {"version": 3, "sources": ["testManage.vue"], "names": [], "mappings": ";AA4cA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "testManage.vue", "sourceRoot": "src/views/examSystem", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"24\">\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"试卷标题\">\n              <a-input placeholder=\"请输入试卷标题\" v-model=\"queryParam.title\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"所属科目\">\n              <a-select placeholder=\"请选择所属科目\" v-model=\"queryParam.subject\" allowClear>\n                <a-select-option value=\"Scratch\">Scratch</a-select-option>\n                <a-select-option value=\"Python\">Python</a-select-option>\n                <a-select-option value=\"C++\">C++</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"所属级别\">\n              <a-select placeholder=\"请选择所属级别\" v-model=\"queryParam.level\" allowClear>\n                <a-select-option v-for=\"(level, index) in getLevelOptions()\" :key=\"index\" :value=\"level\">\n                  {{ level }}\n                </a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"难度\">\n              <a-select placeholder=\"请选择难度\" v-model=\"queryParam.difficulty\" allowClear>\n                <a-select-option :value=\"1\">简单</a-select-option>\n                <a-select-option :value=\"2\">中等</a-select-option>\n                <a-select-option :value=\"3\">困难</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"类型\">\n              <a-select placeholder=\"请选择类型\" v-model=\"queryParam.type\" allowClear>\n                <a-select-option value=\"真题\">真题</a-select-option>\n                <a-select-option value=\"模拟\">模拟</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"年份\">\n              <a-date-picker\n                placeholder=\"请选择年份\"\n                v-model=\"queryParam.year\"\n                :format=\"yearFormat\"\n                :mode=\"yearMode\"\n                @panelChange=\"handleYearPanelChange\"\n                @change=\"handleYearChange\"\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <span class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\n              <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button type=\"primary\" icon=\"plus\" @click=\"handleAdd\">新增试卷</a-button>\n      <a-button type=\"primary\" icon=\"cloud-upload\" @click=\"handleImport\">导入试卷</a-button>\n      <a-button type=\"primary\" icon=\"cloud-download\" @click=\"handleExport\">批量导出</a-button>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\" />删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作 <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- 表格区域 -->\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      @change=\"handleTableChange\">\n      \n      <!-- 自定义难度展示 -->\n      <template slot=\"difficultySlot\" slot-scope=\"text\">\n        <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n        <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n        <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n        <a-tag v-else>未知</a-tag>\n      </template>\n      \n      <!-- 自定义考试时长展示 -->\n      <template slot=\"durationSlot\" slot-scope=\"text\">\n        {{ text }} 分钟\n      </template>\n      \n      <!-- 操作列 -->\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a @click=\"handlePreview(record)\">预览</a>\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a @click=\"handleCopy(record)\">复制</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </a-table>\n\n    <!-- 表单模态框 -->\n    <paper-modal ref=\"modalForm\" @ok=\"modalFormOk\"></paper-modal>\n    \n    <!-- 导入模态框 -->\n    <a-modal\n      title=\"导入试卷\"\n      :width=\"800\"\n      :visible=\"importModalVisible\"\n      :maskClosable=\"false\"\n      :confirmLoading=\"importConfirmLoading\"\n      @ok=\"handleImportOk\"\n      @cancel=\"handleImportCancel\">\n      \n      <a-alert\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px\"\n      >\n        <div slot=\"message\">\n          <span>导入试卷说明</span>\n          <a-tooltip placement=\"right\" overlayClassName=\"import-help-tooltip\">\n            <template slot=\"title\">\n              <div style=\"max-width: 400px;\">\n                <div><strong>标准导入流程详解：</strong></div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤1：获取纯文本模板</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    点击\"下载模板\"获取【纯文本模板】文件\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤2：填写试卷数据</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    在【纯文本模板】中按格式填写您的试卷内容\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤3：自动格式化</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    点击\"自动格式化\"上传填好的【纯文本模板】<br/>\n                    填写试卷标题、科目、级别、难度等元数据<br/>\n                    下载生成的【格式化试卷文件】\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤4：导入</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    使用【格式化试卷文件】进行导入\n                  </div>\n                </div>\n              </div>\n            </template>\n            <a-icon\n              type=\"question-circle\"\n              style=\"margin-left: 8px; color: #1890ff; cursor: help;\"\n            />\n          </a-tooltip>\n        </div>\n        <div slot=\"description\">\n          <div style=\"padding: 8px;\">\n            <strong>💡 完整流程</strong>：【纯文本模板】→ 填写数据 → 【自动格式化】→ 【格式化试卷文件】→ 导入\n          </div>\n        </div>\n      </a-alert>\n      \n      <!-- 减少分隔线上下间距 -->\n      <a-divider style=\"margin: 4px 0\" />\n      \n      <!-- 文件上传区域，与problemManage.vue保持一致 -->\n      <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n        <input\n          ref=\"fileInput\"\n          type=\"file\"\n          accept=\".txt\"\n          @change=\"onFileSelected\"\n          style=\"display: none\"\n        />\n        <div \n          class=\"upload-drop-area\" \n          @click=\"triggerFileInput\"\n          @dragover.prevent\n          @dragenter.prevent=\"isDragover = true\"\n          @dragleave.prevent=\"isDragover = false\"\n          @drop.prevent=\"onFileDrop\"\n          :class=\"{'is-dragover': isDragover}\"\n          style=\"padding: 16px 24px;\"\n        >\n          <a-icon type=\"cloud-upload\" class=\"upload-icon\" />\n          <div class=\"upload-text\" style=\"margin: 8px 0;\">\n            <span v-if=\"!selectedFile\">点击或拖拽文件到此区域上传</span>\n            <span v-else class=\"selected-file\">\n              <a-icon type=\"file-text\" /> {{ selectedFile.name }}\n              <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeSelectedFile\" />\n            </span>\n          </div>\n          <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerFileInput\" style=\"margin-top: 8px;\">\n            选择文件\n          </a-button>\n        </div>\n        <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n          <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，遵循标准格式化试卷文件格式\n          <a class=\"template-link\" @click=\"downloadTemplate\">下载模板</a>\n          <a-divider type=\"vertical\" />\n          <a class=\"template-link\" @click=\"showAutoTemplateModal\">自动格式化</a>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 预览模态框 -->\n    <a-modal\n      title=\"试卷预览\"\n      :visible=\"previewModalVisible\"\n      :width=\"800\"\n      :footer=\"null\"\n      @cancel=\"handlePreviewCancel\"\n    >\n      <a-spin :spinning=\"previewLoading\">\n        <div v-if=\"previewData\">\n          <h2>{{ previewData.title }}</h2>\n          <div class=\"paper-info\">\n            <p><strong>科目：</strong>{{ previewData.subject }}</p>\n            <p><strong>级别：</strong>{{ previewData.level }}</p>\n            <p><strong>难度：</strong>\n              <a-tag color=\"green\" v-if=\"previewData.difficulty === 1\">简单</a-tag>\n              <a-tag color=\"orange\" v-else-if=\"previewData.difficulty === 2\">中等</a-tag>\n              <a-tag color=\"red\" v-else-if=\"previewData.difficulty === 3\">困难</a-tag>\n            </p>\n            <p><strong>类型：</strong>{{ previewData.type }}</p>\n            <p><strong>年份：</strong>{{ previewData.year }}</p>\n            <p><strong>考试时长：</strong>{{ previewData.examDuration }} 分钟</p>\n            <p><strong>作者：</strong>{{ previewData.author }}</p>\n          </div>\n          \n          <!-- 试卷内容预览 -->\n          <div class=\"paper-content\" v-if=\"previewData.content\">\n            <!-- 单选题部分 -->\n            <div v-if=\"previewContent.singleChoiceQuestions && previewContent.singleChoiceQuestions.length > 0\">\n              <h3>一、单选题（每题 {{ previewContent.singleChoiceScore }} 分，共 {{ previewContent.singleChoiceQuestions.length * previewContent.singleChoiceScore }} 分）</h3>\n              <div v-for=\"(question, index) in previewContent.singleChoiceQuestions\" :key=\"'sc-'+question.id\" class=\"question-item\">\n                <p>{{ index + 1 }}. {{ question.title }}</p>\n                <p v-if=\"question.options && question.options.length >= 4\">\n                  <div>A. {{ question.options[0] }}</div>\n                  <div>B. {{ question.options[1] }}</div>\n                  <div>C. {{ question.options[2] }}</div>\n                  <div>D. {{ question.options[3] }}</div>\n                </p>\n              </div>\n            </div>\n            \n            <!-- 判断题部分 -->\n            <div v-if=\"previewContent.judgmentQuestions && previewContent.judgmentQuestions.length > 0\">\n              <h3>二、判断题（每题 {{ previewContent.judgmentScore }} 分，共 {{ previewContent.judgmentQuestions.length * previewContent.judgmentScore }} 分）</h3>\n              <div v-for=\"(question, index) in previewContent.judgmentQuestions\" :key=\"'jd-'+question.id\" class=\"question-item\">\n                <p>{{ index + 1 }}. {{ question.title }}</p>\n              </div>\n            </div>\n            \n            <!-- 编程题部分 -->\n            <div v-if=\"previewContent.programmingQuestions && previewContent.programmingQuestions.length > 0\">\n              <h3>三、编程题（每题 {{ previewContent.programmingScore }} 分，共 {{ previewContent.programmingQuestions.length * previewContent.programmingScore }} 分）</h3>\n              <div v-for=\"(question, index) in previewContent.programmingQuestions\" :key=\"'pg-'+question.id\" class=\"question-item\">\n                <p>{{ index + 1 }}. {{ question.title }}</p>\n                <div class=\"question-limits\">\n                  <span>时间限制：{{ question.timeLimit }} ms</span>\n                  <span>内存限制：{{ question.memoryLimit }} MB</span>\n                </div>\n                <div v-if=\"question.description\" class=\"markdown-body\">\n                  <h4>题目描述</h4>\n                  <div>{{ question.description }}</div>\n                </div>\n                <div v-if=\"question.inputFormat\" class=\"markdown-body\">\n                  <h4>输入格式</h4>\n                  <div>{{ question.inputFormat }}</div>\n                </div>\n                <div v-if=\"question.outputFormat\" class=\"markdown-body\">\n                  <h4>输出格式</h4>\n                  <div>{{ question.outputFormat }}</div>\n                </div>\n                <!-- 样例展示 -->\n                <div v-if=\"question.samples && question.samples.length > 0\">\n                  <div v-for=\"(sample, sIndex) in question.samples\" :key=\"'sample-'+sIndex\">\n                    <h4>样例 {{ sIndex + 1 }}</h4>\n                    <div class=\"sample-container\">\n                      <div class=\"sample-input\">\n                        <div class=\"sample-header\">输入</div>\n                        <pre>{{ sample.input }}</pre>\n                      </div>\n                      <div class=\"sample-output\">\n                        <div class=\"sample-header\">输出</div>\n                        <pre>{{ sample.output }}</pre>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <!-- 提示信息 -->\n                <div v-if=\"question.hint\" class=\"markdown-body\">\n                  <h4>提示</h4>\n                  <div>{{ question.hint }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </a-spin>\n    </a-modal>\n\n    <!-- 自动格式化模态框 -->\n    <a-modal\n      title=\"自动格式化\"\n      :width=\"600\"\n      :visible=\"autoTemplateModalVisible\"\n      :maskClosable=\"false\"\n      :confirmLoading=\"autoTemplateConfirmLoading\"\n      @ok=\"handleAutoTemplateOk\"\n      @cancel=\"handleAutoTemplateCancel\"\n    >\n      <a-alert\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px\"\n        message=\"自动格式化说明\"\n      >\n        <div slot=\"description\">\n          <div><strong>功能说明</strong>：上传填写好试卷数据的【纯文本模板】文件，系统将自动格式化为标准导入格式。</div>\n          <div>支持自动识别试卷元数据，并添加相应的格式标记。</div>\n          <div>格式化后生成【格式化试卷文件】，可直接用于导入。</div>\n        </div>\n      </a-alert>\n\n      <!-- 元数据输入区域 -->\n      <a-form :label-col=\"{ span: 6 }\" :wrapper-col=\"{ span: 18 }\" style=\"margin-bottom: 16px;\">\n        <!-- 试卷标题单独一行 -->\n        <a-form-item label=\"试卷标题\">\n          <a-input v-model=\"autoTemplateParam.title\" placeholder=\"请输入试卷标题，例如：C++一级2025年3月真题\" />\n        </a-form-item>\n\n        <!-- 所属级别单独一行 -->\n        <a-form-item label=\"所属级别\">\n          <a-input v-model=\"autoTemplateParam.level\" placeholder=\"请输入级别，例如：1、2、3\" />\n        </a-form-item>\n\n        <!-- 年份单独一行 -->\n        <a-form-item label=\"年份\">\n          <a-input v-model=\"autoTemplateParam.year\" placeholder=\"请输入年份，例如：2025\" />\n        </a-form-item>\n\n        <!-- 所属科目单独一行 -->\n        <a-form-item label=\"所属科目\">\n          <a-select\n            v-model=\"autoTemplateParam.subject\"\n            placeholder=\"请选择科目\"\n            allowClear\n          >\n            <a-select-option value=\"Scratch\">Scratch</a-select-option>\n            <a-select-option value=\"Python\">Python</a-select-option>\n            <a-select-option value=\"C++\">C++</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <!-- 难度单独一行 -->\n        <a-form-item label=\"难度\">\n          <a-select\n            v-model=\"autoTemplateParam.difficulty\"\n            placeholder=\"请选择难度\"\n            allowClear\n          >\n            <a-select-option :value=\"1\">简单</a-select-option>\n            <a-select-option :value=\"2\">中等</a-select-option>\n            <a-select-option :value=\"3\">困难</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <!-- 类型单独一行（因为只有两个选项，给它更多空间） -->\n        <a-form-item label=\"类型\">\n          <a-select\n            v-model=\"autoTemplateParam.type\"\n            placeholder=\"请选择类型\"\n            allowClear\n          >\n            <a-select-option value=\"真题\">真题</a-select-option>\n            <a-select-option value=\"模拟\">模拟</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-form>\n\n      <a-divider style=\"margin: 16px 0\" />\n\n      <!-- 文件上传区域 -->\n      <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n        <input\n          ref=\"autoTemplateFileInput\"\n          type=\"file\"\n          accept=\".txt\"\n          @change=\"onAutoTemplateFileChange\"\n          style=\"display: none\"\n        />\n        <div\n          class=\"upload-drop-area\"\n          @click=\"triggerAutoTemplateFileInput\"\n          @dragover.prevent\n          @dragenter.prevent=\"handleAutoTemplateDragEnter\"\n          @dragleave.prevent=\"handleAutoTemplateDragLeave\"\n          @drop.prevent=\"handleAutoTemplateDrop\"\n          :class=\"{'is-dragover': isAutoTemplateDragover}\"\n          style=\"padding: 16px 24px;\"\n        >\n          <a-icon type=\"file-text\" class=\"upload-icon\" />\n          <div class=\"upload-text\" style=\"margin: 8px 0;\">\n            <span v-if=\"!autoTemplateSelectedFile\">点击或拖拽纯文本文件到此区域上传</span>\n            <span v-else class=\"selected-file\">\n              <a-icon type=\"file-text\" /> {{ autoTemplateSelectedFile.name }}\n              <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeAutoTemplateSelectedFile\" />\n            </span>\n          </div>\n          <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerAutoTemplateFileInput\" style=\"margin-top: 8px;\">\n            选择文件\n          </a-button>\n        </div>\n        <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n          <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，系统将自动格式化为导入模板格式\n        </div>\n      </div>\n    </a-modal>\n  </a-card>\n</template>\n\n<script>\nimport Vue from 'vue'\nimport moment from 'moment'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport {\n  getPaperList,\n  addPaper,\n  editPaper,\n  deletePaper,\n  deleteBatchPaper,\n  queryPaperById,\n  importPapers,\n  exportPapers,\n  getPaperQuestions,\n  autoFormatPaperTemplate\n} from '@/api/examSystem'\nimport PaperModal from './modules/PaperModal'\nimport { mapGetters } from 'vuex'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\n\nexport default {\n  name: \"TestManage\",\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\n  components: {\n    PaperModal\n  },\n  data() {\n    return {\n      description: '试卷管理页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/testManage/list',\n        delete: '/teaching/examSystem/testManage/delete',\n        deleteBatch: '/teaching/examSystem/testManage/deleteBatch',\n        exportXlsUrl: '/teaching/examSystem/testManage/exportXls',\n        importExcelUrl: '/teaching/examSystem/testManage/importExcel'\n      },\n      // 表头\n      columns: [\n        {\n          title: '序号',\n          dataIndex: '',\n          key:'rowIndex',\n          width:60,\n          align:\"center\",\n          customRender:function (t,r,index) {\n            return parseInt(index)+1;\n          }\n        },\n        {\n          title: '试卷ID',\n          align: 'center',\n          dataIndex: 'id',\n          width: '120px'\n        },\n        {\n          title: '标题',\n          align: 'center',\n          dataIndex: 'title'\n        },\n        {\n          title: '科目',\n          align: 'center',\n          dataIndex: 'subject',\n          sorter: true\n        },\n        {\n          title: '级别',\n          align: 'center',\n          dataIndex: 'level',\n          sorter: true\n        },\n        {\n          title: '难度',\n          align: 'center',\n          dataIndex: 'difficulty',\n          scopedSlots: { customRender: 'difficultySlot' },\n          sorter: true\n        },\n        {\n          title: '类型',\n          align: 'center',\n          dataIndex: 'type',\n          sorter: true\n        },\n        {\n          title: '年份',\n          align: 'center',\n          dataIndex: 'year',\n          sorter: true\n        },\n        {\n          title: '作者',\n          align: 'center',\n          dataIndex: 'author'\n        },\n        {\n          title: '考试时长',\n          align: 'center',\n          dataIndex: 'examDuration',\n          scopedSlots: { customRender: 'durationSlot' }\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          scopedSlots: { customRender: 'action' },\n          width: '200px'\n        }\n      ],\n      // 注意：dataSource, ipagination, loading, selectedRowKeys 等状态\n      // 已由JeecgListMixin提供，无需重复定义\n      // 导入模态框相关\n      importModalVisible: false,\n      importConfirmLoading: false,\n      uploadHeaders: {},\n      // 智能引用模式相关\n      importPreviewData: null,\n      selectedImportMode: 'complete', // 默认选择完整模式\n      // 导出相关\n      exportLoading: false,\n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      // 试卷预览相关\n      previewModalVisible: false,\n      previewLoading: false,\n      previewData: null,\n      previewContent: {},\n      // 导入文件相关\n      selectedFile: null,\n      isDragover: false,\n      // 自动格式化相关\n      autoTemplateModalVisible: false,\n      autoTemplateConfirmLoading: false,\n      autoTemplateSelectedFile: null,\n      isAutoTemplateDragover: false,\n      autoTemplateParam: {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo'])\n  },\n  mounted() {\n    // 注意：loadData() 已由JeecgListMixin自动调用，无需重复调用\n    this.uploadHeaders = {\n      'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n    }\n  },\n  methods: {\n    // 获取级别选项\n    getLevelOptions() {\n      const subject = this.queryParam.subject\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 年份选择面板变化\n    handleYearPanelChange(value, mode) {\n      this.queryParam.year = value\n      this.yearMode = 'year'\n    },\n    \n    // 年份选择变化\n    handleYearChange(date, dateString) {\n      this.queryParam.year = date\n    },\n    \n    // 注意：loadData、searchReset、handleTableChange 等方法\n    // 已由JeecgListMixin提供，无需重复实现\n    \n    // 选择行变化\n    onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys\n    },\n    \n    // 新增\n    handleAdd() {\n      this.$refs.modalForm.add()\n    },\n    \n    // 编辑\n    handleEdit(record) {\n      this.$refs.modalForm.edit(record)\n    },\n    \n    // 复制\n    handleCopy(record) {\n      this.$refs.modalForm.copy(record)\n    },\n    \n    // 预览\n    handlePreview(record) {\n      this.previewModalVisible = true\n      this.previewLoading = true\n      \n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          this.previewData = res.result\n          \n          // 解析试卷内容\n          if (this.previewData.content) {\n            try {\n              this.previewContent = JSON.parse(this.previewData.content)\n              \n              // 获取试卷题目详情\n              this.loadPaperQuestions(record.id)\n            } catch (e) {\n              this.previewContent = {}\n              console.error('解析试卷内容失败', e)\n              this.previewLoading = false\n            }\n          } else {\n            this.previewLoading = false\n          }\n        } else {\n          this.$message.warning(res.message || '获取试卷详情失败')\n          this.previewLoading = false\n        }\n      }).catch(() => {\n        this.previewLoading = false\n      })\n    },\n    \n    // 加载试卷题目详情\n    loadPaperQuestions(paperId) {\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then((res) => {\n        if (res.success) {\n          // 将题目按类型分组\n          const questionData = res.result || []\n          \n          // 创建题目类型到分数的映射\n          const scoreMap = {}\n          if (this.previewContent.questions) {\n            this.previewContent.questions.forEach(item => {\n              scoreMap[item.questionId] = item.score\n            })\n          }\n          \n          // 按题目类型分组\n          const singleChoiceQuestions = []\n          const judgmentQuestions = []\n          const programmingQuestions = []\n          \n          questionData.forEach(item => {\n            const question = item.question\n            const score = item.score\n            \n            // 处理题目数据\n            if (question) {\n              // 解析题目内容\n              let questionDetail = question\n              if (typeof question.content === 'string' && question.content) {\n                try {\n                  const contentObj = JSON.parse(question.content)\n                  questionDetail = { ...question, ...contentObj }\n                  \n                  // 处理编程题特殊内容\n                  if (question.questionType === 3) {\n                    // 转换样例格式\n                    if (contentObj.sample_cases) {\n                      questionDetail.samples = contentObj.sample_cases.map(sample => ({\n                        input: sample.input,\n                        output: sample.output\n                      }))\n                    }\n                    \n                    // 转换时间和内存限制\n                    questionDetail.timeLimit = contentObj.time_limit\n                    questionDetail.memoryLimit = contentObj.memory_limit\n                    questionDetail.description = contentObj.description\n                    questionDetail.inputFormat = contentObj.input_format\n                    questionDetail.outputFormat = contentObj.output_format\n                    questionDetail.hint = contentObj.hint\n                  }\n                } catch (e) {\n                  console.error('解析题目内容失败', e)\n                }\n              }\n              \n              // 根据题型分组\n              switch (question.questionType) {\n                case 1: // 单选题\n                  singleChoiceQuestions.push(questionDetail)\n                  break\n                case 2: // 判断题\n                  judgmentQuestions.push(questionDetail)\n                  break\n                case 3: // 编程题\n                  programmingQuestions.push(questionDetail)\n                  break\n              }\n            }\n          })\n          \n          // 更新预览内容\n          this.previewContent = {\n            ...this.previewContent,\n            singleChoiceQuestions,\n            judgmentQuestions,\n            programmingQuestions,\n            // 使用已有分数或默认值\n            singleChoiceScore: this.previewContent.singleChoiceScore || 2,\n            judgmentScore: this.previewContent.judgmentScore || 2,\n            programmingScore: this.previewContent.programmingScore || 25\n          }\n        } else {\n          this.$message.warning('获取试卷题目失败')\n        }\n        this.previewLoading = false\n      }).catch(() => {\n        this.$message.warning('获取试卷题目失败')\n        this.previewLoading = false\n      })\n    },\n    \n    // 关闭预览\n    handlePreviewCancel() {\n      this.previewModalVisible = false\n      this.previewData = null\n      this.previewContent = {}\n    },\n    \n    // 删除\n    handleDelete(id) {\n      deletePaper({id: id}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '删除成功')\n          this.loadData()\n        } else {\n          this.$message.warning(res.message || '删除失败')\n        }\n      })\n    },\n    \n    // 批量删除\n    batchDel() {\n      if (this.selectedRowKeys.length <= 0) {\n        this.$message.warning('请选择至少一条记录！')\n        return\n      }\n      \n      // 添加确认对话框防止误操作\n      this.$confirm({\n        title: '批量删除确认',\n        content: `确定要删除所选择的 ${this.selectedRowKeys.length} 条试卷吗？此操作不可恢复！`,\n        okText: '确定删除',\n        okType: 'danger',\n        cancelText: '取消',\n        onOk: () => {\n          // 执行原有的删除逻辑\n          const ids = this.selectedRowKeys.join(',')\n          deleteBatchPaper({ids: ids}).then((res) => {\n            if (res.success) {\n              this.$message.success(res.message || '批量删除成功')\n              this.loadData()\n              this.selectedRowKeys = []\n            } else {\n              this.$message.warning(res.message || '批量删除失败')\n            }\n          })\n        }\n      });\n    },\n    \n    // 导入\n    handleImport() {\n      // 重置导入表单\n      this.importParam = {\n        title: undefined, // 使用undefined代替空字符串，确保placeholder显示\n        subject: undefined,\n        level: undefined,\n        difficulty: undefined,\n        type: undefined,\n        year: undefined,\n        author: undefined,\n        examDuration: undefined // 移除默认考试时长，让后端根据级别自动设置\n      }\n      this.selectedFile = null\n      this.importModalVisible = true\n    },\n    \n    // 导出\n    handleExport() {\n      // 添加提示，告知用户只导出符合查询条件的试卷\n      this.$confirm({\n        title: '批量导出试卷',\n        content: '将导出符合当前查询条件的试卷。如需导出全部试卷，请先清空查询条件。',\n        onOk: () => {\n          const params = {...this.queryParam}\n          \n          // 处理年份参数\n          if (params.year) {\n            params.year = moment(params.year).format('YYYY')\n          }\n          \n          this.exportLoading = true\n          \n          exportPapers(params).then((res) => {\n            if (res.success) {\n              // 检查res.result的内容\n              let contentToExport = null\n              \n              // 处理不同的返回结构\n              if (res.result && typeof res.result === 'object' && res.result.content) {\n                // 新格式，从content字段获取内容\n                contentToExport = res.result.content\n              } else if (typeof res.result === 'string') {\n                // 旧格式，直接使用result字符串\n                contentToExport = res.result\n              }\n              \n              // 如果内容为空，显示错误消息\n              if (!contentToExport) {\n                this.$message.error('导出内容为空，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 确保内容是字符串\n              if (typeof contentToExport !== 'string') {\n                contentToExport = JSON.stringify(contentToExport, null, 2)\n              }\n              \n              // 避免导出\"null\"字符串\n              if (contentToExport === \"null\") {\n                this.$message.error('导出内容异常，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 创建Blob对象，使用适当的MIME类型\n              const blob = new Blob([contentToExport], { type: 'text/plain;charset=utf-8' })\n              const fileName = `试卷导出_${moment().format('YYYYMMDDHHmmss')}.txt`\n              \n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName)\n              } else {\n                // 现代浏览器\n                const link = document.createElement('a')\n                link.href = URL.createObjectURL(blob)\n                link.download = fileName\n                link.style.display = 'none'\n                document.body.appendChild(link)\n                link.click()\n                \n                // 清理创建的对象URL\n                setTimeout(() => {\n                  URL.revokeObjectURL(link.href)\n                  document.body.removeChild(link)\n                }, 100)\n              }\n              \n              this.$message.success('导出成功')\n            } else {\n              this.$message.warning(res.message || '导出失败')\n            }\n            this.exportLoading = false\n          }).catch((error) => {\n            console.error('导出失败:', error)\n            this.$message.error('导出过程发生错误')\n            this.exportLoading = false\n          })\n        }\n      })\n    },\n    \n    // 模态框提交回调\n    modalFormOk() {\n      this.loadData()\n    },\n    \n    // 获取级别选项\n    getLevelOptionsBySubject(subject) {\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 导入科目变化\n    onImportSubjectChange(value) {\n      this.importParam.subject = value\n    },\n    \n    // 导入级别变化\n    onImportLevelChange(value) {\n      this.importParam.level = value\n      console.log('选择级别:', value)\n      \n      // 根据级别自动设置考试时长\n      if (value) {\n        // 更新考试时长显示，但不传递到后端，让后端根据级别自动设置\n        // 这里只是为了在界面上显示预期的考试时长\n        const duration = this.getExamDurationByLevel(value)\n        this.$nextTick(() => {\n          this.$set(this, 'expectedDuration', duration)\n        })\n      }\n    },\n    \n    // 根据级别计算考试时长\n    getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n      \n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.includes('五') || level.includes('六') || level.includes('七') || level.includes('八') || \n            level.includes('5') || level.includes('6') || level.includes('7') || level.includes('8')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    \n    // 上传前验证\n    beforeUpload(file) {\n      const isTxt = file.type === 'text/plain' || file.name.endsWith('.txt')\n      if (!isTxt) {\n        this.$message.error('只能上传 .txt 文件!')\n      }\n      return isTxt\n    },\n    \n    // 上传状态变化\n    handleUploadChange(info) {\n      this.fileList = [...info.fileList].slice(-1)\n    },\n    \n    // 导入确认\n    handleImportOk() {\n      if (!this.selectedFile) {\n        this.$message.warning('请选择要导入的文件')\n        return\n      }\n\n      // 如果还没有预览过，先进行预览\n      if (!this.importPreviewData) {\n        this.previewImport()\n        return\n      }\n\n      // 如果已经预览过，显示选择模式对话框\n      this.showImportModeDialog()\n    },\n\n    // 预览导入\n    previewImport() {\n      this.importConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      formData.append('mode', 'preview')\n\n      this.performImportRequest(formData, (res) => {\n        this.importConfirmLoading = false\n        if (res.success && res.result) {\n          this.importPreviewData = res.result\n\n          // 无论是否可以直接导入，都显示预览窗口让用户选择\n          // 这样用户可以看到完整的检测结果和导入选项\n          this.showImportModeDialog()\n        } else {\n          // 如果错误消息较长，使用通知框显示\n          if (res.message && res.message.length > 20) {\n            this.$notification.error({\n              message: '预览失败',\n              description: res.message,\n              duration: 8,\n              style: {\n                width: '400px',\n                marginLeft: `${335 - 400}px`,\n              }\n            })\n          } else {\n            this.$message.error(res.message || '预览失败')\n          }\n        }\n      })\n    },\n\n    // 获取当前检测状态\n    getCurrentDetectionStatus() {\n      const previewData = this.importPreviewData\n      if (!previewData) return 'unknown'\n\n      const { newQuestions, duplicateQuestions, similarQuestions } = previewData\n      const totalQuestions = newQuestions + duplicateQuestions + similarQuestions\n\n      if (newQuestions === totalQuestions) {\n        return 'all_new' // 全部新题\n      } else if (duplicateQuestions === totalQuestions) {\n        return 'all_duplicate' // 全部重复\n      } else {\n        return 'partial_duplicate' // 部分重复\n      }\n    },\n\n    // 显示导入模式选择对话框\n    showImportModeDialog() {\n      const previewData = this.importPreviewData\n      const currentStatus = this.getCurrentDetectionStatus()\n\n      // 使用自定义Modal显示导入方式选择\n      this.$info({\n        title: '检测完成 - 选择导入方式',\n        width: 520,\n        okButtonProps: { style: { display: 'none' } }, // 隐藏默认的确定按钮\n        maskClosable: false, // 防止点击遮罩关闭\n        content: (h) => {\n          return h('div', { style: { lineHeight: '1.6' } }, [\n            // 检测结果区域\n            h('div', { style: { marginBottom: '20px' } }, [\n              h('h4', { style: { margin: '0 0 12px 0', color: '#1890ff' } }, '📊 检测结果'),\n              h('div', {\n                style: {\n                  background: '#f6f8fa',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  marginBottom: '16px'\n                }\n              }, [\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '📝 共解析：',\n                  h('strong', {}, previewData.totalQuestions),\n                  ' 个题目'\n                ]),\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '✨ 新题目：',\n                  h('strong', { style: { color: '#52c41a' } }, previewData.newQuestions),\n                  ' 个'\n                ]),\n                h('div', { style: { marginBottom: '6px' } }, [\n                  '🔄 重复题目：',\n                  h('strong', { style: { color: '#fa8c16' } }, previewData.duplicateQuestions),\n                  ' 个'\n                ]),\n                h('div', {}, [\n                  '🔍 相似题目：',\n                  h('strong', { style: { color: '#722ed1' } }, previewData.similarQuestions),\n                  ' 个'\n                ])\n              ])\n            ]),\n\n            // 模式选择区域\n            h('div', {}, [\n              h('h4', { style: { margin: '0 0 12px 0', color: '#1890ff' } }, '🎯 导入方式'),\n\n              // 完整模式\n              h('div', {\n                style: {\n                  background: '#e6f7ff',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  marginBottom: '12px',\n                  borderLeft: '4px solid #1890ff'\n                }\n              }, [\n                h('div', { style: { fontWeight: 'bold', marginBottom: '4px' } }, '🎯 完整模式（推荐）'),\n                h('div', { style: { color: '#666', fontSize: '13px', lineHeight: '1.4' } }, [\n                  '• 全部新题：导入所有题目，创建试卷',\n                  currentStatus === 'all_new' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 部分重复：导入新题+引用重复题，创建完整试卷',\n                  currentStatus === 'partial_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 全部重复：引用所有题目，创建试卷（避免重复存储）',\n                  currentStatus === 'all_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : ''\n                ])\n              ]),\n\n              // 新题模式\n              h('div', {\n                style: {\n                  background: '#fff2e8',\n                  padding: '12px',\n                  borderRadius: '6px',\n                  borderLeft: '4px solid #fa8c16'\n                }\n              }, [\n                h('div', { style: { fontWeight: 'bold', marginBottom: '4px' } }, '📝 新题模式'),\n                h('div', { style: { color: '#666', fontSize: '13px', lineHeight: '1.4' } }, [\n                  '• 全部新题：导入所有题目，创建试卷',\n                  currentStatus === 'all_new' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 部分重复：只导入新题目，创建试卷（可能不完整）',\n                  currentStatus === 'partial_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : '',\n                  h('br'),\n                  '• 全部重复：',\n                  h('span', { style: { color: '#ff4d4f', fontWeight: 'bold' } }, '无法创建试卷'),\n                  currentStatus === 'all_duplicate' ? h('span', { style: { color: '#52c41a', fontWeight: 'bold', marginLeft: '8px' } }, '  ←当前状态') : ''\n                ])\n              ])\n            ]),\n\n            // 添加自定义按钮区域\n            h('div', {\n              style: {\n                marginTop: '20px',\n                textAlign: 'center',\n                borderTop: '1px solid #f0f0f0',\n                paddingTop: '16px',\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                gap: '12px',\n                flexWrap: 'wrap'\n              }\n            }, [\n              h('a-button', {\n                props: {\n                  type: 'primary',\n                  size: 'large',\n                  style: { minWidth: '120px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框并执行完整模式导入\n                    this.$destroyAll()\n                    this.confirmImport(true) // 完整模式，允许引用\n                  }\n                }\n              }, '完整模式（推荐）'),\n\n              h('a-button', {\n                props: {\n                  size: 'large',\n                  style: { minWidth: '100px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框并执行新题模式导入\n                    this.$destroyAll()\n                    this.confirmImport(false) // 新题模式，不允许引用\n                  }\n                }\n              }, '新题模式'),\n\n              h('a-button', {\n                props: {\n                  type: 'default',\n                  size: 'large',\n                  style: { minWidth: '80px' }\n                },\n                on: {\n                  click: () => {\n                    // 关闭对话框，不进行任何导入\n                    this.$destroyAll()\n                    this.$message.info('已取消导入')\n                  }\n                }\n              }, '取消')\n            ])\n          ])\n        }\n      })\n    },\n\n    // 确认导入\n    confirmImport(allowReference) {\n      this.importConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      formData.append('mode', 'confirm')\n      formData.append('allowReference', allowReference)\n\n      this.performImportRequest(formData, (res) => {\n        this.importConfirmLoading = false\n\n        // 添加调试信息\n        console.log('导入响应:', res)\n        console.log('allowReference:', allowReference)\n\n        if (res.success) {\n          // 检查返回结果中是否有详细的消息\n          if (res.result && res.result.message) {\n            // 如果消息包含统计信息，使用通知框显示\n            if (res.result.message.includes('道题目') && res.result.message.includes('重复题目')) {\n              this.$notification.success({\n                message: '导入成功',\n                description: res.result.message,\n                duration: 6,\n                style: {\n                  width: '400px',\n                  marginLeft: `${335 - 400}px`,\n                }\n              })\n            } else {\n              // 普通消息使用message显示\n              this.$message.success(res.result.message)\n            }\n          } else {\n            // 使用默认消息\n            this.$message.success(res.message || '导入成功')\n          }\n          this.handleImportCancel()\n          this.loadData()\n        } else {\n          console.error('导入失败，响应详情:', res)\n\n          // 检查是否是试卷重复检测 - 数据可能在 res.result 中\n          const resultData = res.result || res;\n          if (resultData.isDuplicatePaper && resultData.duplicatePaper) {\n            console.log(\"弹出试卷重复警告对话框\")\n            console.log(\"传递给对话框的数据:\", resultData)\n            console.log(\"uploadedPaper:\", resultData.uploadedPaper)\n            console.log(\"duplicatePaper:\", resultData.duplicatePaper)\n            // 显示试卷重复警告对话框\n            this.showDuplicatePaperDialog(resultData)\n          } else {\n            // 普通错误消息\n            this.$message.error(res.message || '导入失败')\n          }\n        }\n      })\n    },\n\n    // 显示试卷重复警告对话框\n    showDuplicatePaperDialog(res) {\n      console.log('重复检测对话框数据:', res)\n      const duplicatePaper = res.duplicatePaper\n      const uploadedPaper = res.uploadedPaper || {}\n      console.log('上传试卷信息:', uploadedPaper)\n      console.log('重复试卷信息:', duplicatePaper)\n\n      this.$warning({\n        title: '⚠️ 检测到重复试卷',\n        content: h => h('div', { style: { lineHeight: '1.6' } }, [\n          h('div', { style: { marginBottom: '12px', color: '#ff4d4f', fontWeight: 'bold' } },\n            '发现重复试卷！'),\n\n          // 上传的文件信息\n          h('div', { style: { marginBottom: '8px' } }, [\n            '上传文件：',\n            h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, (this.selectedFile && this.selectedFile.name) || '未知')\n          ]),\n\n          // 解析出的试卷信息\n          h('div', { style: { marginBottom: '8px' } }, [\n            '上传试卷：',\n            h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, uploadedPaper.title || '未知'),\n            h('span', { style: { fontSize: '12px', color: '#999', marginLeft: '8px' } })\n          ]),\n\n          // 数据库中的重复试卷\n          h('div', { style: { marginBottom: '8px' } }, [\n            '重复试卷：',\n            h('span', { style: { fontWeight: 'bold', color: '#ff4d4f' } }, duplicatePaper.title)\n          ]),\n\n          // 详细信息\n          h('div', { style: { marginBottom: '12px' } }, [\n            '科目：',\n            h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.subject),\n            '，级别：',\n            h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.level),\n            duplicatePaper.year ? ['，年份：', h('span', { style: { fontWeight: 'bold' } }, duplicatePaper.year)] : ''\n          ]),\n\n          h('div', { style: { color: '#666', fontSize: '13px' } },\n            '建议检查是否重复导入，或修改试卷标题后重新导入。')\n        ]),\n        okText: '我知道了',\n        width: 500\n      })\n    },\n\n    // 执行导入请求的通用方法\n    performImportRequest(formData, callback) {\n      // 使用原生XMLHttpRequest处理文件上传，避免序列化问题\n      const xhr = new XMLHttpRequest()\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      const baseURL = window._CONFIG['domianURL'] || ''\n\n      // 设置请求超时\n      xhr.timeout = 120000 // 2分钟超时\n\n      // 设置上传完成回调\n      xhr.onreadystatechange = () => {\n        if (xhr.readyState === 4) {\n          if (xhr.status === 200) {\n            try {\n              const res = JSON.parse(xhr.responseText)\n              callback(res)\n            } catch (e) {\n              console.error('解析响应失败:', e)\n              console.error('原始响应文本:', xhr.responseText)\n              this.importConfirmLoading = false\n              this.$message.error('导入处理失败，请检查文件格式')\n            }\n          } else {\n            console.error('导入失败，响应内容:', xhr.responseText)\n            this.importConfirmLoading = false\n            this.$message.error(`导入失败，状态码: ${xhr.status}`)\n          }\n        }\n      }\n\n      // 设置上传进度回调\n      xhr.upload.onprogress = (event) => {\n        if (event.lengthComputable) {\n          const percentComplete = Math.round((event.loaded * 100) / event.total)\n          console.log(`上传进度: ${percentComplete}%`)\n        }\n      }\n\n      // 设置超时处理\n      xhr.ontimeout = () => {\n        this.importConfirmLoading = false\n        this.$message.error('上传超时，请检查文件大小或网络连接')\n      }\n\n      // 设置错误处理\n      xhr.onerror = () => {\n        this.importConfirmLoading = false\n        this.$message.error('网络错误，导入失败')\n      }\n\n      // 发送请求\n      xhr.open('POST', `${baseURL}/teaching/examSystem/testManage/import`, true)\n      xhr.setRequestHeader('X-Access-Token', token)\n      xhr.send(formData)\n    },\n    \n    // 导入取消\n    handleImportCancel() {\n      this.importModalVisible = false\n      this.selectedFile = null\n      this.importConfirmLoading = false\n      this.importPreviewData = null\n      this.selectedImportMode = 'complete'\n    },\n    \n    // 下载导入模板\n    downloadTemplate() {\n      const templateContent = `【元数据】\n【试卷标题】请填写试卷标题，例如：C++一级2025年03月\n【所属科目】请填写科目，例如：C++、Python、Scratch\n【所属级别】请填写级别，例如：1、2、3\n【难度】请填写难度，例如：1、2、3（1=简单，2=中等，3=困难）\n【类型】请填写类型，例如：真题、模拟题\n【年份】请填写年份，例如：2025\n\n【一、单选题】\n【1.】关于C++语言的描述，下面哪个是正确的？\n【A. 】C++语言不支持面向对象编程。\n【B. 】C++语言的程序最终会被编译成机器指令执行。\n【C. 】C++语言只能用于游戏开发。\n【D. 】C++语言中，变量可以不声明就直接使用。\n【答案】B\n【解析】C++是一种支持面向对象的高级编程语言，应用广泛，变量使用前必须声明。程序需要通过编译器转换为机器码才能运行。\n\n【二、判断题】\n【1.】在C++中，表达式 N * 2 % N 中如果 N 的值为正整数，则其值为0。\n【答案】正确\n【解析】N*2可以被N整除，所以余数为0。\n\n【三、编程题】\n【1.】四舍五入\n【- 时间限制：1.0 s - 内存限制：512.0 MB】\n【题目描述】\n四舍五入是一种常见的近似计算方法。现在，给定 n 个整数，你需要将每个整数四舍五入到最接近的整十数。\n【输入格式】\n共 n + 1 行，第一行是一个整数 n，表示接下来输入的整数个数。\n接下来 n 行，每行一个整数 ai。\n【输出格式】\nn 行，每行一个整数，表示每个整数四舍五入后的结果。\n【输入样例 1】\n5\n43\n58\n25\n【输出样例 1】\n40\n60\n30\n【数据范围】\n对于所有测试点，保证 1 ≤ n ≤ 100，1 ≤ ai ≤ 10000。`\n\n      // 创建下载链接\n      const blob = new Blob([templateContent], { type: 'text/plain;charset=utf-8' })\n      const link = document.createElement('a')\n      link.href = window.URL.createObjectURL(blob)\n      link.download = '纯文本模板.txt'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      window.URL.revokeObjectURL(link.href)\n    },\n    \n    // 导入年份面板变化\n    handleImportYearPanelChange(value, mode) {\n      this.importParam.year = value\n      this.yearMode = 'year'\n    },\n    \n    // 处理文件选择\n    onFileSelected(e) {\n      const file = e.target.files[0]\n      if (!file) return\n      \n      // 检查文件类型\n      if (this.beforeUpload(file)) {\n        this.selectedFile = file\n      }\n      \n      // 重置文件输入框，使相同文件可以再次选择\n      e.target.value = ''\n    },\n\n    // 移除选择的文件\n    removeSelectedFile() {\n      this.selectedFile = null\n      if (this.$refs.fileInput) {\n        this.$refs.fileInput.value = ''\n      }\n    },\n\n    // 触发文件输入框点击\n    triggerFileInput() {\n      this.$refs.fileInput.click()\n    },\n    \n    // 文件拖拽进入\n    handleDragEnter() {\n      this.isDragover = true\n    },\n\n    // 文件拖拽离开\n    handleDragLeave() {\n      this.isDragover = false\n    },\n\n    // 处理文件拖放\n    onFileDrop(event) {\n      this.isDragover = false\n      const file = event.dataTransfer.files[0]\n      if (file) {\n        if (this.beforeUpload(file)) {\n          this.selectedFile = file\n        }\n      }\n    },\n\n    // 自动格式化相关方法\n    showAutoTemplateModal() {\n      this.autoTemplateModalVisible = true\n      // 重置表单数据\n      this.autoTemplateParam = {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateCancel() {\n      this.autoTemplateModalVisible = false\n      this.autoTemplateSelectedFile = null\n      this.autoTemplateParam = {\n        title: '',\n        subject: undefined,\n        level: '',\n        difficulty: undefined,\n        type: undefined,\n        year: ''\n      }\n    },\n\n    triggerAutoTemplateFileInput() {\n      this.$refs.autoTemplateFileInput.click()\n    },\n\n    onAutoTemplateFileChange(event) {\n      const files = event.target.files\n      if (files.length > 0) {\n        const file = files[0]\n        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n          this.autoTemplateSelectedFile = file\n        } else {\n          this.$message.warning('请选择.txt格式的文本文件')\n          event.target.value = ''\n        }\n      }\n    },\n\n    handleAutoTemplateDragEnter() {\n      this.isAutoTemplateDragover = true\n    },\n\n    handleAutoTemplateDragLeave() {\n      this.isAutoTemplateDragover = false\n    },\n\n    handleAutoTemplateDrop(e) {\n      e.preventDefault()\n      this.isAutoTemplateDragover = false\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onAutoTemplateFileChange({ target: { files } })\n      }\n    },\n\n    removeAutoTemplateSelectedFile() {\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateOk() {\n      // 验证必填项\n      if (!this.autoTemplateParam.title) {\n        this.$message.warning('请输入试卷标题')\n        return\n      }\n      if (!this.autoTemplateParam.subject) {\n        this.$message.warning('请选择科目')\n        return\n      }\n      if (!this.autoTemplateParam.level) {\n        this.$message.warning('请输入级别')\n        return\n      }\n      if (!this.autoTemplateParam.difficulty) {\n        this.$message.warning('请选择难度')\n        return\n      }\n      if (!this.autoTemplateParam.type) {\n        this.$message.warning('请选择类型')\n        return\n      }\n      if (!this.autoTemplateParam.year) {\n        this.$message.warning('请输入年份')\n        return\n      }\n      if (!this.autoTemplateSelectedFile) {\n        this.$message.warning('请选择要格式化的文件')\n        return\n      }\n\n      this.autoTemplateConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.autoTemplateSelectedFile)\n      formData.append('title', this.autoTemplateParam.title)\n      formData.append('subject', this.autoTemplateParam.subject)\n      formData.append('level', this.autoTemplateParam.level)\n      formData.append('difficulty', this.autoTemplateParam.difficulty)\n      formData.append('type', this.autoTemplateParam.type)\n      formData.append('year', this.autoTemplateParam.year)\n\n      // 调用自动格式化API\n      autoFormatPaperTemplate(formData).then((res) => {\n        if (res.success) {\n          this.$message.success('自动格式化成功，正在下载格式化后的文件...')\n\n          if (res.result) {\n            this.downloadFormattedFile(res.result)\n          } else {\n            this.$message.error('未获取到格式化内容')\n          }\n\n          this.handleAutoTemplateCancel()\n        } else {\n          this.$message.warning(res.message || '自动格式化失败')\n        }\n        this.autoTemplateConfirmLoading = false\n      }).catch((error) => {\n        console.error('自动格式化失败:', error)\n        this.$message.error('自动格式化失败，请重试')\n        this.autoTemplateConfirmLoading = false\n      })\n    },\n\n    // 下载格式化后的文件\n    downloadFormattedFile(content) {\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n      const blobUrl = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = blobUrl\n      link.download = '格式化试卷文件.txt'\n      document.body.appendChild(link)\n      link.click()\n      setTimeout(() => {\n        window.URL.revokeObjectURL(blobUrl)\n        document.body.removeChild(link)\n      }, 100)\n    },\n\n    // 格式化文件大小\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.table-page-search-wrapper {\n  margin-bottom: 8px;\n}\n\n// 新增样式，与problemManage.vue保持一致\n.modern-upload-area {\n  margin-bottom: 16px;\n}\n\n.upload-drop-area {\n  padding: 24px;\n  background-color: #fff;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  text-align: center;\n  transition: all 0.3s;\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-drop-area:hover {\n  border-color: #1890ff;\n  background-color: #f0f7ff;\n}\n\n.is-dragover {\n  border-color: #1890ff;\n  background-color: #f0f7ff;\n  box-shadow: 0 0 10px rgba(24, 144, 255, 0.2);\n}\n\n.upload-icon {\n  font-size: 36px;\n  color: #1890ff;\n  margin-bottom: 8px;\n}\n\n.upload-text {\n  margin: 12px 0;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.65);\n}\n\n.selected-file {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  background-color: #f5f5f5;\n  padding: 8px 16px;\n  border-radius: 4px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.remove-file {\n  cursor: pointer;\n  color: #ff4d4f;\n  font-size: 16px;\n  transition: all 0.3s;\n}\n\n.remove-file:hover {\n  color: #ff7875;\n}\n\n.upload-button {\n  margin-top: 12px;\n}\n\n.upload-tip {\n  margin-top: 12px;\n  font-size: 13px;\n  color: #666;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.template-link {\n  color: #1890ff;\n  text-decoration: none;\n  font-weight: 500;\n  transition: all 0.3s;\n}\n\n.template-link:hover {\n  color: #40a9ff;\n  text-decoration: underline;\n}\n\n// 保留原有样式\n.upload-area {\n  border: 2px dashed #d9d9d9;\n  border-radius: 4px;\n  padding: 20px;\n  text-align: center;\n  background-color: #fafafa;\n  transition: all 0.3s;\n  cursor: pointer;\n\n  &.is-dragover {\n  border-color: #1890ff;\n    background-color: rgba(24, 144, 255, 0.05);\n}\n\n  .upload-placeholder {\n    p {\n      margin: 10px 0 0;\n      color: rgba(0, 0, 0, 0.45);\n      \n      &.upload-hint {\n        font-size: 12px;\n      }\n    }\n}\n\n  .file-info {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n    \n    .file-details {\n      margin: 0 10px;\n      text-align: left;\n      \n      .file-name {\n  font-weight: 500;\n        margin-bottom: 4px;\n}\n\n      .file-size {\n        font-size: 12px;\n        color: rgba(0, 0, 0, 0.45);\n}\n    }\n  }\n}\n</style> "]}]}