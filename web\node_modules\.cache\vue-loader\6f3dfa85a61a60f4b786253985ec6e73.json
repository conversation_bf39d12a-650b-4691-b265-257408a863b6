{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\problemManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\problemManage.vue", "mtime": 1753517009971}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport {\n  getProblemList,\n  addProblem,\n  editProblem,\n  deleteProblem,\n  importProblems,\n  exportProblems,\n  deleteBatchProblem,\n  autoFormatTemplate\n} from '@/api/examSystem'\nimport QuestionModal from './modules/QuestionModal'\nimport { mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\nimport { QuestionDataParser } from '@/utils/questionDataStructure'\n\nexport default {\n  name: \"ProblemManage\",\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\n  components: {\n    QuestionModal\n  },\n  data() {\n    return {\n      description: '题库管理页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/problemManage/list',\n        delete: '/teaching/examSystem/problemManage/delete',\n        deleteBatch: '/teaching/examSystem/problemManage/deleteBatch',\n        exportXlsUrl: '/teaching/examSystem/problemManage/exportXls',\n        importExcelUrl: '/teaching/examSystem/problemManage/importExcel'\n      },\n      // 导入参数\n      importParam: {},\n      // 表头\n      columns: [\n        {\n          title: '序号',\n          dataIndex: '',\n          key:'rowIndex',\n          width:60,\n          align:\"center\",\n          customRender:function (t,r,index) {\n            return parseInt(index)+1;\n          }\n        },\n        {\n          title: '题目ID',\n          align: 'center',\n          dataIndex: 'id',\n          width: '120px'\n        },\n        {\n          title: '标题',\n          align: 'center',\n          dataIndex: 'title',\n          scopedSlots: { customRender: 'titleSlot' },\n          ellipsis: true\n        },\n        {\n          title: '题目类型',\n          align: 'center',\n          dataIndex: 'questionType',\n          scopedSlots: { customRender: 'questionTypeSlot' },\n          sorter: true\n        },\n        {\n          title: '科目',\n          align: 'center',\n          dataIndex: 'subject',\n          sorter: true\n        },\n        {\n          title: '级别',\n          align: 'center',\n          dataIndex: 'level',\n          sorter: true\n        },\n        {\n          title: '难度',\n          align: 'center',\n          dataIndex: 'difficulty',\n          scopedSlots: { customRender: 'difficultySlot' },\n          sorter: true\n        },\n        {\n          title: '作者',\n          align: 'center',\n          dataIndex: 'author'\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          scopedSlots: { customRender: 'action' },\n          width: '150px'\n        }\n      ],\n      // 数据源 - 与JeecgListMixin配合使用\n      dataSource: [],\n      // 分页配置 - 与JeecgListMixin配合使用\n      ipagination: {\n        current: 1,\n        pageSize: 10,\n        pageSizeOptions: ['10', '20', '30'],\n        showTotal: (total, range) => {\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n        },\n        showQuickJumper: true,\n        showSizeChanger: true,\n        total: 0\n      },\n      // 加载状态\n      loading: false,\n      // 选择的行\n      selectedRowKeys: [],\n      // 导入模态框相关\n      importModalVisible: false,\n      importConfirmLoading: false,\n      selectedFile: null,\n      uploadAction: \"/teaching/examSystem/problemManage/import\",\n      uploadHeaders: {},\n      importForm: this.$form.createForm(this),\n      // 导出相关\n      exportLoading: false,\n      isDragover: false,\n      // 自动格式化相关\n      autoTemplateModalVisible: false,\n      autoTemplateConfirmLoading: false,\n      autoTemplateSelectedFile: null,\n      isAutoTemplateDragover: false,\n      autoTemplateParam: {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo'])\n  },\n  mounted() {\n    // 注意：loadData() 已由JeecgListMixin自动调用，无需重复调用\n    this.uploadHeaders = {\n      'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n    }\n  },\n  methods: {\n    // 获取题目标题（支持新数据结构）\n    getQuestionTitle(record) {\n      if (!record) return ''\n\n      // 编程题直接使用title字段\n      if (record.questionType === 3) {\n        return record.title || '无标题'\n      }\n\n      // 客观题尝试从content中解析标题\n      try {\n        if (record.content) {\n          const parsedContent = QuestionDataParser.parseQuestionContent(record)\n          return parsedContent.title || record.title || '无标题'\n        }\n      } catch (error) {\n        console.warn('解析题目标题失败:', error)\n      }\n\n      return record.title || '无标题'\n    },\n\n    // 获取级别选项\n    getLevelOptions() {\n      const subject = this.queryParam.subject\n      if (!subject) return []\n      \n      return this.getLevelOptionsBySubject(subject)\n    },\n    \n    // 根据科目获取级别选项\n    getLevelOptionsBySubject(subject) {\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 加载数据\n    loadData(arg) {\n      if (arg === 1) {\n        this.ipagination.current = 1\n      }\n      this.loading = true\n      const params = {...this.queryParam}\n\n      // 优化标题处理 - 仅保留trim操作，不再做其他处理\n      // 服务端已优化模糊查询和特殊字符处理\n      if (params.title) {\n        params.title = params.title.trim()\n      }\n\n      params.pageNo = this.ipagination.current\n      params.pageSize = this.ipagination.pageSize\n\n      getProblemList(params).then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records || res.result\n          this.ipagination.total = res.result.total || 0\n        } else {\n          this.$message.warning(res.message || '获取数据失败')\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n\n    // 搜索重置\n    searchReset() {\n      this.queryParam = {}\n      this.loadData(1)\n    },\n\n    // 表格变化\n    handleTableChange(pagination, filters, sorter) {\n      this.ipagination.current = pagination.current\n\n      // 添加排序参数\n      if (sorter && sorter.field) {\n        this.queryParam.sortField = sorter.field\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\n      } else {\n        delete this.queryParam.sortField\n        delete this.queryParam.sortOrder\n      }\n\n      this.loadData()\n    },\n    \n    // 选择行变化\n    onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys\n    },\n    \n    // 新增\n    handleAdd() {\n      this.$refs.modalForm.add()\n    },\n    \n    // 编辑\n    handleEdit(record) {\n      this.$refs.modalForm.edit(record)\n    },\n    \n    // 删除\n    handleDelete(id) {\n      deleteProblem({id: id}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '删除成功')\n          this.loadData()\n        } else {\n          this.$message.warning(res.message || '删除失败')\n        }\n      })\n    },\n    \n    // 批量删除\n    batchDel() {\n      if (this.selectedRowKeys.length <= 0) {\n        this.$message.warning('请选择至少一条记录！')\n        return\n      }\n      \n      // 添加确认对话框防止误操作\n      this.$confirm({\n        title: '批量删除确认',\n        content: `确定要删除所选择的 ${this.selectedRowKeys.length} 条记录吗？此操作不可恢复！`,\n        okText: '确定删除',\n        okType: 'danger',\n        cancelText: '取消',\n        onOk: () => {\n          // 执行原有的删除逻辑\n      const ids = this.selectedRowKeys.join(',')\n      deleteBatchProblem({ids: ids}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '批量删除成功')\n          this.loadData()\n          this.selectedRowKeys = []\n        } else {\n          this.$message.warning(res.message || '批量删除失败')\n        }\n      })\n        }\n      });\n    },\n    \n    // 导入\n    handleImport() {\n      // 重置导入\n      this.selectedFile = null\n      this.importModalVisible = true\n    },\n    \n    // 导出\n    handleExport() {\n      // 添加提示，告知用户只导出符合查询条件的题目\n      this.$confirm({\n        title: '批量导出题目',\n        content: '将导出符合当前查询条件的题目。如需导出全部题目，请先清空查询条件。',\n        onOk: () => {\n      const params = {...this.queryParam}\n      this.exportLoading = true\n      \n      exportProblems(params).then((res) => {\n        if (res.success) {\n              // 检查res.result的内容\n              let contentToExport = null\n              \n              // 处理不同的返回结构\n              if (res.result && typeof res.result === 'object' && res.result.content) {\n                // 新格式，从content字段获取内容\n                contentToExport = res.result.content\n              } else if (typeof res.result === 'string') {\n                // 旧格式，直接使用result字符串\n                contentToExport = res.result\n              }\n              \n              // 如果内容为空，显示错误消息\n              if (!contentToExport) {\n                this.$message.error('导出内容为空，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 确保内容是字符串\n              if (typeof contentToExport !== 'string') {\n                contentToExport = JSON.stringify(contentToExport, null, 2)\n              }\n              \n              // 避免导出\"null\"字符串\n              if (contentToExport === \"null\") {\n                this.$message.error('导出内容异常，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 处理导出内容，移除元数据标题行\n              if (contentToExport.startsWith(\"【元数据】\")) {\n                // 查找元数据部分的结束位置（两个连续的换行符后面紧跟的是题型标题）\n                const metadataEndIndex = contentToExport.indexOf(\"\\n\\n\");\n                if (metadataEndIndex !== -1) {\n                  // 移除元数据部分\n                  contentToExport = contentToExport.substring(metadataEndIndex + 2);\n                }\n              }\n              \n              // 创建Blob对象，使用适当的MIME类型\n              const blob = new Blob([contentToExport], { type: 'text/plain;charset=utf-8' });\n              \n              // 创建文件名\n              const fileName = `题目导出_${new Date().getTime()}.txt`;\n              \n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName);\n              } else {\n                // 现代浏览器\n                const link = document.createElement('a');\n                link.href = URL.createObjectURL(blob);\n                link.download = fileName;\n                link.style.display = 'none';\n                document.body.appendChild(link);\n                link.click();\n                \n                // 清理创建的对象URL\n                setTimeout(() => {\n                  URL.revokeObjectURL(link.href);\n                  document.body.removeChild(link);\n                }, 100);\n              }\n              \n              this.$message.success('导出成功');\n        } else {\n              this.$message.warning(res.message || '导出失败');\n        }\n            this.exportLoading = false;\n          }).catch((error) => {\n            console.error('导出失败:', error);\n            this.$message.error('导出过程发生错误');\n            this.exportLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 模态框提交回调\n    modalFormOk() {\n      this.loadData()\n    },\n    \n    // 触发文件输入框点击\n    triggerFileInput() {\n      this.$refs.fileInput.click()\n    },\n    \n    // 处理文件选择变更\n    onFileChange(e) {\n      const file = e.target.files[0]\n      if (!file) return\n      \n      // 检查文件类型\n      if (!file.name.toLowerCase().endsWith('.txt') && file.type !== 'text/plain') {\n        this.$message.error('只能上传 .txt 文件!')\n        return\n      }\n      \n      this.selectedFile = file\n      \n      // 重置文件输入框，使相同文件可以再次选择\n      e.target.value = ''\n    },\n    \n    // 导入确认 - 重写为使用原生XMLHttpRequest\n    handleImportOk() {\n      if (!this.selectedFile) {\n        this.$message.warning('请选择要导入的文件')\n        return\n      }\n      \n      this.importConfirmLoading = true\n      \n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      \n      // 移除所有元数据参数，只从文件中获取元数据\n      \n      // 使用原生XMLHttpRequest处理文件上传，避免序列化问题\n      const xhr = new XMLHttpRequest()\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      const baseURL = window._CONFIG['domianURL'] || ''\n      \n      // 设置请求超时\n      xhr.timeout = 120000 // 2分钟超时\n      \n      // 设置上传完成回调\n      xhr.onreadystatechange = () => {\n        if (xhr.readyState === 4) {\n          this.importConfirmLoading = false\n          \n          if (xhr.status === 200) {\n            try {\n              const res = JSON.parse(xhr.responseText)\n              console.log('导入响应数据:', res) // 调试日志\n\n              if (res.success) {\n                // 检查是否有详细的导入结果消息\n                let message = '导入成功'\n\n                if (res.result && res.result.message) {\n                  // 使用后端返回的详细消息\n                  message = res.result.message\n                  console.log('使用详细消息:', message) // 调试日志\n                } else if (res.message) {\n                  // 使用基本消息\n                  message = res.message\n                  console.log('使用基本消息:', message) // 调试日志\n                }\n\n                // 根据导入结果显示不同类型的消息\n                if (res.result && res.result.skippedCount > 0) {\n                  // 有跳过的题目，显示警告样式\n                  this.$message.warning(message, 5) // 显示5秒\n                } else {\n                  // 正常导入，显示成功样式\n                  this.$message.success(message)\n                }\n\n                this.handleImportCancel()\n                this.loadData()\n              } else {\n                // 检查是否是因为缺少元数据导致的错误\n                if (res.message && (\n                    res.message.includes(\"Field\") || \n                    res.message.includes(\"不存在\") || \n                    res.message.includes(\"没有默认值\") ||\n                    res.message.includes(\"不能为空\")\n                  )) {\n                  // 提示用户文件中需包含元数据\n                  this.$message.warning('导入失败：上传的文件缺少必要的元数据。请确保文件包含科目、级别、难度等必要信息。')\n              } else {\n                this.$message.warning(res.message || '导入失败')\n                }\n              }\n            } catch (e) {\n              console.error('解析响应失败:', e)\n              console.error('原始响应文本:', xhr.responseText)\n              this.$message.error('导入处理失败，请检查文件格式')\n            }\n          } else {\n            console.error('导入失败，响应内容:', xhr.responseText)\n            this.$message.error(`导入失败，状态码: ${xhr.status}`)\n          }\n        }\n      }\n      \n      // 设置上传进度回调\n      xhr.upload.onprogress = (event) => {\n        if (event.lengthComputable) {\n          const percentComplete = Math.round((event.loaded * 100) / event.total)\n          console.log(`上传进度: ${percentComplete}%`)\n        }\n      }\n      \n      // 设置超时处理\n      xhr.ontimeout = () => {\n        this.importConfirmLoading = false\n        this.$message.error('上传超时，请检查文件大小或网络连接')\n      }\n      \n      // 设置错误处理\n      xhr.onerror = () => {\n        this.importConfirmLoading = false\n        this.$message.error('网络错误，导入失败')\n      }\n      \n      // 发送请求\n      xhr.open('POST', `${baseURL}/teaching/examSystem/problemManage/import`, true)\n      xhr.setRequestHeader('X-Access-Token', token)\n      xhr.send(formData)\n    },\n    \n    // 导入取消\n    handleImportCancel() {\n      this.importModalVisible = false\n      this.selectedFile = null\n      this.importConfirmLoading = false\n    },\n    \n    // 下载导入模板\n    downloadTemplate() {\n      // 构建查询参数字符串\n      let queryParams = []\n      \n      // 添加当前导入表单中的参数\n      if (this.importParam.subject) {\n        queryParams.push(`subject=${encodeURIComponent(this.importParam.subject)}`)\n      }\n      if (this.importParam.level) {\n        queryParams.push(`level=${encodeURIComponent(this.importParam.level)}`)\n      }\n      if (this.importParam.difficulty) {\n        queryParams.push(`difficulty=${this.importParam.difficulty}`)\n      }\n      if (this.importParam.author) {\n        queryParams.push(`author=${encodeURIComponent(this.importParam.author)}`)\n      }\n      \n      // 构建完整URL\n      const baseURL = window._CONFIG['domianURL'] || '' // 获取基础URL\n      let url = `${baseURL}/teaching/examSystem/problemManage/downloadTemplate`\n      if (queryParams.length > 0) {\n        url += '?' + queryParams.join('&')\n      }\n      \n      // 显示下载中提示\n      this.$message.loading('模板下载中...', 0)\n      \n      // 使用fetch API下载文件\n      fetch(url, {\n        method: 'GET',\n        headers: {\n          'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n        }\n      })\n      .then(response => {\n        if (!response.ok) {\n          throw new Error(`下载失败: ${response.status}`)\n        }\n        return response.blob()\n      })\n      .then(blob => {\n        // 创建Blob URL\n        const blobUrl = window.URL.createObjectURL(blob)\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = blobUrl\n        link.download = '纯文本模板.txt'\n        document.body.appendChild(link)\n        link.click()\n        // 清理\n        setTimeout(() => {\n          window.URL.revokeObjectURL(blobUrl)\n          document.body.removeChild(link)\n        }, 100)\n        this.$message.destroy()\n      })\n      .catch(error => {\n        console.error('下载模板失败:', error)\n        this.$message.destroy()\n        this.$message.error('下载模板失败，请重试')\n      })\n    },\n    \n    // 处理拖拽事件\n    handleDragEnter() {\n      this.isDragover = true\n    },\n    handleDragLeave() {\n      this.isDragover = false\n    },\n    handleDrop(e) {\n      e.preventDefault()\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onFileChange({ target: { files } })\n      }\n    },\n    removeSelectedFile() {\n      this.selectedFile = null\n    },\n\n    // 自动格式化相关方法\n    showAutoTemplateModal() {\n      this.autoTemplateModalVisible = true\n      // 重置表单数据\n      this.autoTemplateParam = {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateCancel() {\n      this.autoTemplateModalVisible = false\n      this.autoTemplateSelectedFile = null\n      this.autoTemplateParam = {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n    },\n\n    triggerAutoTemplateFileInput() {\n      this.$refs.autoTemplateFileInput.click()\n    },\n\n    onAutoTemplateFileChange(event) {\n      const files = event.target.files\n      if (files.length > 0) {\n        const file = files[0]\n        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n          this.autoTemplateSelectedFile = file\n        } else {\n          this.$message.warning('请选择.txt格式的文本文件')\n          event.target.value = ''\n        }\n      }\n    },\n\n    handleAutoTemplateDragEnter() {\n      this.isAutoTemplateDragover = true\n    },\n\n    handleAutoTemplateDragLeave() {\n      this.isAutoTemplateDragover = false\n    },\n\n    handleAutoTemplateDrop(e) {\n      e.preventDefault()\n      this.isAutoTemplateDragover = false\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onAutoTemplateFileChange({ target: { files } })\n      }\n    },\n\n    removeAutoTemplateSelectedFile() {\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateOk() {\n      // 验证必填项\n      if (!this.autoTemplateParam.subject) {\n        this.$message.warning('请选择科目')\n        return\n      }\n      if (!this.autoTemplateParam.level) {\n        this.$message.warning('请输入级别')\n        return\n      }\n      if (!this.autoTemplateParam.difficulty) {\n        this.$message.warning('请选择难度')\n        return\n      }\n      if (!this.autoTemplateSelectedFile) {\n        this.$message.warning('请选择要格式化的文件')\n        return\n      }\n\n      this.autoTemplateConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.autoTemplateSelectedFile)\n      formData.append('subject', this.autoTemplateParam.subject)\n      formData.append('level', this.autoTemplateParam.level)\n      formData.append('difficulty', this.autoTemplateParam.difficulty)\n\n      // 调用自动格式化API\n      autoFormatTemplate(formData).then((res) => {\n        if (res.success) {\n          this.$message.success('自动格式化成功，正在下载格式化后的文件...')\n\n          if (res.result) {\n            this.downloadFormattedFile(res.result)\n          } else {\n            this.$message.error('未获取到格式化内容')\n          }\n\n          this.handleAutoTemplateCancel()\n        } else {\n          this.$message.warning(res.message || '自动格式化失败')\n        }\n        this.autoTemplateConfirmLoading = false\n      }).catch((error) => {\n        console.error('自动格式化失败:', error)\n        this.$message.error('自动格式化失败，请重试')\n        this.autoTemplateConfirmLoading = false\n      })\n    },\n\n    // 下载格式化后的文件\n    downloadFormattedFile(content) {\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n      const blobUrl = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = blobUrl\n      link.download = '格式化题目文件.txt'\n      document.body.appendChild(link)\n      link.click()\n      setTimeout(() => {\n        window.URL.revokeObjectURL(blobUrl)\n        document.body.removeChild(link)\n      }, 100)\n    }\n  }\n}\n", {"version": 3, "sources": ["problemManage.vue"], "names": [], "mappings": ";AAuVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "problemManage.vue", "sourceRoot": "src/views/examSystem", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"24\">\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"题目标题\">\n              <a-input placeholder=\"请输入题目标题\" v-model=\"queryParam.title\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"题目类型\">\n              <a-select placeholder=\"请选择题目类型\" v-model=\"queryParam.questionType\" allowClear>\n                <a-select-option :value=\"1\">单选题</a-select-option>\n                <a-select-option :value=\"2\">判断题</a-select-option>\n                <a-select-option :value=\"3\">编程题</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"所属科目\">\n              <a-select placeholder=\"请选择所属科目\" v-model=\"queryParam.subject\" allowClear>\n                <a-select-option value=\"Scratch\">Scratch</a-select-option>\n                <a-select-option value=\"Python\">Python</a-select-option>\n                <a-select-option value=\"C++\">C++</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"题目级别\">\n              <a-select placeholder=\"请选择题目级别\" v-model=\"queryParam.level\" allowClear>\n                <a-select-option v-for=\"(level, index) in getLevelOptions()\" :key=\"index\" :value=\"level\">\n                  {{ level }}\n                </a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"难度\">\n              <a-select placeholder=\"请选择难度\" v-model=\"queryParam.difficulty\" allowClear>\n                <a-select-option :value=\"1\">简单</a-select-option>\n                <a-select-option :value=\"2\">中等</a-select-option>\n                <a-select-option :value=\"3\">困难</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <span class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\">查询</a-button>\n              <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button type=\"primary\" icon=\"plus\" @click=\"handleAdd\">新增题目</a-button>\n      <a-button type=\"primary\" icon=\"cloud-upload\" @click=\"handleImport\">批量导入</a-button>\n      <a-button type=\"primary\" icon=\"cloud-download\" @click=\"handleExport\">批量导出</a-button>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\" />删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作 <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- 表格区域 -->\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      @change=\"handleTableChange\">\n      \n      <!-- 自定义标题展示 -->\n      <template slot=\"titleSlot\" slot-scope=\"text, record\">\n        <span :title=\"getQuestionTitle(record)\">{{ getQuestionTitle(record) }}</span>\n      </template>\n\n      <!-- 自定义题目类型展示 -->\n      <template slot=\"questionTypeSlot\" slot-scope=\"text\">\n        <a-tag color=\"blue\" v-if=\"text === 1\">单选题</a-tag>\n        <a-tag color=\"green\" v-else-if=\"text === 2\">判断题</a-tag>\n        <a-tag color=\"purple\" v-else-if=\"text === 3\">编程题</a-tag>\n        <a-tag v-else>未知题型</a-tag>\n      </template>\n      \n      <!-- 自定义难度展示 -->\n      <template slot=\"difficultySlot\" slot-scope=\"text\">\n        <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n        <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n        <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n        <a-tag v-else>未知</a-tag>\n      </template>\n      \n      <!-- 操作列 -->\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n          <a>删除</a>\n        </a-popconfirm>\n      </span>\n    </a-table>\n\n    <!-- 表单模态框 -->\n    <question-modal ref=\"modalForm\" @ok=\"modalFormOk\"></question-modal>\n    \n    <!-- 导入模态框 -->\n    <a-modal\n      title=\"批量导入题目\"\n      :width=\"800\"\n      :visible=\"importModalVisible\"\n      :maskClosable=\"false\"\n      :confirmLoading=\"importConfirmLoading\"\n      @ok=\"handleImportOk\"\n      @cancel=\"handleImportCancel\"\n    >\n      <!-- 简化的批量导入说明 -->\n        <a-alert\n          type=\"info\"\n          show-icon\n          style=\"margin-bottom: 16px\"\n        >\n          <div slot=\"message\">\n            <span>批量导入题目说明</span>\n            <a-tooltip placement=\"right\" overlayClassName=\"import-help-tooltip\">\n              <template slot=\"title\">\n                <div style=\"max-width: 400px;\">\n                  <div><strong>标准导入流程详解：</strong></div>\n                  <div style=\"margin-top: 8px;\">\n                    <div><strong>步骤1：获取纯文本模板</strong></div>\n                    <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                      点击\"下载模板\"获取【纯文本模板】文件\n                    </div>\n                  </div>\n                  <div style=\"margin-top: 8px;\">\n                    <div><strong>步骤2：填写题目数据</strong></div>\n                    <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                      在【纯文本模板】中按格式填写您的题目内容\n                    </div>\n                  </div>\n                  <div style=\"margin-top: 8px;\">\n                    <div><strong>步骤3：自动格式化</strong></div>\n                    <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                      点击\"自动格式化\"上传填好的【纯文本模板】<br/>\n                      填写科目、级别、难度等元数据<br/>\n                      下载生成的【格式化题目文件】\n                    </div>\n                  </div>\n                  <div style=\"margin-top: 8px;\">\n                    <div><strong>步骤4：批量导入</strong></div>\n                    <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                      使用【格式化题目文件】进行批量导入\n                    </div>\n                  </div>\n                </div>\n              </template>\n              <a-icon\n                type=\"question-circle\"\n                style=\"margin-left: 8px; color: #1890ff; cursor: help;\"\n              />\n            </a-tooltip>\n          </div>\n          <div slot=\"description\">\n            <div style=\"padding: 8px;\">\n              <strong>💡 完整流程</strong>：【纯文本模板】→ 填写数据 → 【自动格式化】→ 【格式化题目文件】→ 批量导入\n            </div>\n          </div>\n        </a-alert>\n        \n      <!-- 减少分隔线上下间距 -->\n      <a-divider style=\"margin: 4px 0\" />\n      \n      <!-- 文件上传区域 -->\n      <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n        <input\n          ref=\"fileInput\"\n          type=\"file\"\n          accept=\".txt\"\n          @change=\"onFileChange\"\n          style=\"display: none\"\n        />\n        <div \n          class=\"upload-drop-area\" \n          @click=\"triggerFileInput\"\n          @dragover.prevent\n          @dragenter.prevent=\"handleDragEnter\"\n          @dragleave.prevent=\"handleDragLeave\"\n          @drop.prevent=\"handleDrop\"\n          :class=\"{'is-dragover': isDragover}\"\n          style=\"padding: 16px 24px;\"\n        >\n          <a-icon type=\"cloud-upload\" class=\"upload-icon\" />\n          <div class=\"upload-text\" style=\"margin: 8px 0;\">\n            <span v-if=\"!selectedFile\">点击或拖拽文件到此区域上传</span>\n            <span v-else class=\"selected-file\">\n              <a-icon type=\"file-text\" /> {{ selectedFile.name }}\n              <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeSelectedFile\" />\n            </span>\n          </div>\n          <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerFileInput\" style=\"margin-top: 8px;\">\n            选择文件\n        </a-button>\n        </div>\n        <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n          <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，遵循标准格式化题目文件格式\n          <a class=\"template-link\" @click=\"downloadTemplate\">下载模板</a>\n          <a-divider type=\"vertical\" />\n          <a class=\"template-link\" @click=\"showAutoTemplateModal\">自动格式化</a>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 自动格式化模态框 -->\n    <a-modal\n      title=\"自动格式化\"\n      :width=\"600\"\n      :visible=\"autoTemplateModalVisible\"\n      :maskClosable=\"false\"\n      :confirmLoading=\"autoTemplateConfirmLoading\"\n      @ok=\"handleAutoTemplateOk\"\n      @cancel=\"handleAutoTemplateCancel\"\n    >\n      <a-alert\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px\"\n        message=\"自动格式化说明\"\n      >\n        <div slot=\"description\">\n          <div><strong>功能说明</strong>：上传填写好题目数据的【纯文本模板】文件，系统将自动格式化为标准导入格式。</div>\n          <div>支持自动识别单选题、判断题、编程题，并添加相应的格式标记。</div>\n          <div>格式化后生成【格式化题目文件】，可直接用于批量导入。</div>\n        </div>\n      </a-alert>\n\n      <!-- 元数据输入区域 -->\n      <a-form :label-col=\"{ span: 6 }\" :wrapper-col=\"{ span: 18 }\" style=\"margin-bottom: 16px;\">\n        <a-form-item label=\"科目\">\n          <a-select\n            v-model=\"autoTemplateParam.subject\"\n            placeholder=\"请选择科目\"\n            allowClear\n          >\n            <a-select-option value=\"Scratch\">Scratch</a-select-option>\n            <a-select-option value=\"Python\">Python</a-select-option>\n            <a-select-option value=\"C++\">C++</a-select-option>\n          </a-select>\n        </a-form-item>\n        <a-form-item label=\"级别\">\n          <a-input v-model=\"autoTemplateParam.level\" placeholder=\"请输入级别，例如：1、2、3\" />\n        </a-form-item>\n        <a-form-item label=\"难度\">\n          <a-select\n            v-model=\"autoTemplateParam.difficulty\"\n            placeholder=\"请选择难度\"\n            allowClear\n          >\n            <a-select-option :value=\"1\">简单</a-select-option>\n            <a-select-option :value=\"2\">中等</a-select-option>\n            <a-select-option :value=\"3\">困难</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-form>\n\n      <a-divider style=\"margin: 16px 0\" />\n\n      <!-- 文件上传区域 -->\n      <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n        <input\n          ref=\"autoTemplateFileInput\"\n          type=\"file\"\n          accept=\".txt\"\n          @change=\"onAutoTemplateFileChange\"\n          style=\"display: none\"\n        />\n        <div\n          class=\"upload-drop-area\"\n          @click=\"triggerAutoTemplateFileInput\"\n          @dragover.prevent\n          @dragenter.prevent=\"handleAutoTemplateDragEnter\"\n          @dragleave.prevent=\"handleAutoTemplateDragLeave\"\n          @drop.prevent=\"handleAutoTemplateDrop\"\n          :class=\"{'is-dragover': isAutoTemplateDragover}\"\n          style=\"padding: 16px 24px;\"\n        >\n          <a-icon type=\"file-text\" class=\"upload-icon\" />\n          <div class=\"upload-text\" style=\"margin: 8px 0;\">\n            <span v-if=\"!autoTemplateSelectedFile\">点击或拖拽纯文本文件到此区域上传</span>\n            <span v-else class=\"selected-file\">\n              <a-icon type=\"file-text\" /> {{ autoTemplateSelectedFile.name }}\n              <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeAutoTemplateSelectedFile\" />\n            </span>\n          </div>\n          <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerAutoTemplateFileInput\" style=\"margin-top: 8px;\">\n            选择文件\n          </a-button>\n        </div>\n        <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n          <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，系统将自动格式化为导入模板格式\n        </div>\n      </div>\n    </a-modal>\n  </a-card>\n</template>\n\n<style scoped>\n/* 导入帮助提示样式 */\n:deep(.import-help-tooltip .ant-tooltip-inner) {\n  max-width: 450px;\n  text-align: left;\n  background-color: #001529;\n  border-radius: 6px;\n  padding: 12px 16px;\n}\n\n:deep(.import-help-tooltip .ant-tooltip-arrow::before) {\n  background-color: #001529;\n}\n\n/* 问号图标悬停效果 */\n.ant-icon[type=\"question-circle\"]:hover {\n  color: #40a9ff !important;\n  transform: scale(1.1);\n  transition: all 0.2s ease;\n}\n</style>\n\n<script>\nimport {\n  getProblemList,\n  addProblem,\n  editProblem,\n  deleteProblem,\n  importProblems,\n  exportProblems,\n  deleteBatchProblem,\n  autoFormatTemplate\n} from '@/api/examSystem'\nimport QuestionModal from './modules/QuestionModal'\nimport { mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\nimport { QuestionDataParser } from '@/utils/questionDataStructure'\n\nexport default {\n  name: \"ProblemManage\",\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\n  components: {\n    QuestionModal\n  },\n  data() {\n    return {\n      description: '题库管理页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/problemManage/list',\n        delete: '/teaching/examSystem/problemManage/delete',\n        deleteBatch: '/teaching/examSystem/problemManage/deleteBatch',\n        exportXlsUrl: '/teaching/examSystem/problemManage/exportXls',\n        importExcelUrl: '/teaching/examSystem/problemManage/importExcel'\n      },\n      // 导入参数\n      importParam: {},\n      // 表头\n      columns: [\n        {\n          title: '序号',\n          dataIndex: '',\n          key:'rowIndex',\n          width:60,\n          align:\"center\",\n          customRender:function (t,r,index) {\n            return parseInt(index)+1;\n          }\n        },\n        {\n          title: '题目ID',\n          align: 'center',\n          dataIndex: 'id',\n          width: '120px'\n        },\n        {\n          title: '标题',\n          align: 'center',\n          dataIndex: 'title',\n          scopedSlots: { customRender: 'titleSlot' },\n          ellipsis: true\n        },\n        {\n          title: '题目类型',\n          align: 'center',\n          dataIndex: 'questionType',\n          scopedSlots: { customRender: 'questionTypeSlot' },\n          sorter: true\n        },\n        {\n          title: '科目',\n          align: 'center',\n          dataIndex: 'subject',\n          sorter: true\n        },\n        {\n          title: '级别',\n          align: 'center',\n          dataIndex: 'level',\n          sorter: true\n        },\n        {\n          title: '难度',\n          align: 'center',\n          dataIndex: 'difficulty',\n          scopedSlots: { customRender: 'difficultySlot' },\n          sorter: true\n        },\n        {\n          title: '作者',\n          align: 'center',\n          dataIndex: 'author'\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          scopedSlots: { customRender: 'action' },\n          width: '150px'\n        }\n      ],\n      // 数据源 - 与JeecgListMixin配合使用\n      dataSource: [],\n      // 分页配置 - 与JeecgListMixin配合使用\n      ipagination: {\n        current: 1,\n        pageSize: 10,\n        pageSizeOptions: ['10', '20', '30'],\n        showTotal: (total, range) => {\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n        },\n        showQuickJumper: true,\n        showSizeChanger: true,\n        total: 0\n      },\n      // 加载状态\n      loading: false,\n      // 选择的行\n      selectedRowKeys: [],\n      // 导入模态框相关\n      importModalVisible: false,\n      importConfirmLoading: false,\n      selectedFile: null,\n      uploadAction: \"/teaching/examSystem/problemManage/import\",\n      uploadHeaders: {},\n      importForm: this.$form.createForm(this),\n      // 导出相关\n      exportLoading: false,\n      isDragover: false,\n      // 自动格式化相关\n      autoTemplateModalVisible: false,\n      autoTemplateConfirmLoading: false,\n      autoTemplateSelectedFile: null,\n      isAutoTemplateDragover: false,\n      autoTemplateParam: {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo'])\n  },\n  mounted() {\n    // 注意：loadData() 已由JeecgListMixin自动调用，无需重复调用\n    this.uploadHeaders = {\n      'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n    }\n  },\n  methods: {\n    // 获取题目标题（支持新数据结构）\n    getQuestionTitle(record) {\n      if (!record) return ''\n\n      // 编程题直接使用title字段\n      if (record.questionType === 3) {\n        return record.title || '无标题'\n      }\n\n      // 客观题尝试从content中解析标题\n      try {\n        if (record.content) {\n          const parsedContent = QuestionDataParser.parseQuestionContent(record)\n          return parsedContent.title || record.title || '无标题'\n        }\n      } catch (error) {\n        console.warn('解析题目标题失败:', error)\n      }\n\n      return record.title || '无标题'\n    },\n\n    // 获取级别选项\n    getLevelOptions() {\n      const subject = this.queryParam.subject\n      if (!subject) return []\n      \n      return this.getLevelOptionsBySubject(subject)\n    },\n    \n    // 根据科目获取级别选项\n    getLevelOptionsBySubject(subject) {\n      if (!subject) return []\n      \n      if (subject === 'Scratch') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级']\n      } else if (subject === 'Python' || subject === 'C++') {\n        // 将数字级别改为中文格式\n        return ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      }\n      \n      return []\n    },\n    \n    // 加载数据\n    loadData(arg) {\n      if (arg === 1) {\n        this.ipagination.current = 1\n      }\n      this.loading = true\n      const params = {...this.queryParam}\n\n      // 优化标题处理 - 仅保留trim操作，不再做其他处理\n      // 服务端已优化模糊查询和特殊字符处理\n      if (params.title) {\n        params.title = params.title.trim()\n      }\n\n      params.pageNo = this.ipagination.current\n      params.pageSize = this.ipagination.pageSize\n\n      getProblemList(params).then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records || res.result\n          this.ipagination.total = res.result.total || 0\n        } else {\n          this.$message.warning(res.message || '获取数据失败')\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n\n    // 搜索重置\n    searchReset() {\n      this.queryParam = {}\n      this.loadData(1)\n    },\n\n    // 表格变化\n    handleTableChange(pagination, filters, sorter) {\n      this.ipagination.current = pagination.current\n\n      // 添加排序参数\n      if (sorter && sorter.field) {\n        this.queryParam.sortField = sorter.field\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\n      } else {\n        delete this.queryParam.sortField\n        delete this.queryParam.sortOrder\n      }\n\n      this.loadData()\n    },\n    \n    // 选择行变化\n    onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys\n    },\n    \n    // 新增\n    handleAdd() {\n      this.$refs.modalForm.add()\n    },\n    \n    // 编辑\n    handleEdit(record) {\n      this.$refs.modalForm.edit(record)\n    },\n    \n    // 删除\n    handleDelete(id) {\n      deleteProblem({id: id}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '删除成功')\n          this.loadData()\n        } else {\n          this.$message.warning(res.message || '删除失败')\n        }\n      })\n    },\n    \n    // 批量删除\n    batchDel() {\n      if (this.selectedRowKeys.length <= 0) {\n        this.$message.warning('请选择至少一条记录！')\n        return\n      }\n      \n      // 添加确认对话框防止误操作\n      this.$confirm({\n        title: '批量删除确认',\n        content: `确定要删除所选择的 ${this.selectedRowKeys.length} 条记录吗？此操作不可恢复！`,\n        okText: '确定删除',\n        okType: 'danger',\n        cancelText: '取消',\n        onOk: () => {\n          // 执行原有的删除逻辑\n      const ids = this.selectedRowKeys.join(',')\n      deleteBatchProblem({ids: ids}).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message || '批量删除成功')\n          this.loadData()\n          this.selectedRowKeys = []\n        } else {\n          this.$message.warning(res.message || '批量删除失败')\n        }\n      })\n        }\n      });\n    },\n    \n    // 导入\n    handleImport() {\n      // 重置导入\n      this.selectedFile = null\n      this.importModalVisible = true\n    },\n    \n    // 导出\n    handleExport() {\n      // 添加提示，告知用户只导出符合查询条件的题目\n      this.$confirm({\n        title: '批量导出题目',\n        content: '将导出符合当前查询条件的题目。如需导出全部题目，请先清空查询条件。',\n        onOk: () => {\n      const params = {...this.queryParam}\n      this.exportLoading = true\n      \n      exportProblems(params).then((res) => {\n        if (res.success) {\n              // 检查res.result的内容\n              let contentToExport = null\n              \n              // 处理不同的返回结构\n              if (res.result && typeof res.result === 'object' && res.result.content) {\n                // 新格式，从content字段获取内容\n                contentToExport = res.result.content\n              } else if (typeof res.result === 'string') {\n                // 旧格式，直接使用result字符串\n                contentToExport = res.result\n              }\n              \n              // 如果内容为空，显示错误消息\n              if (!contentToExport) {\n                this.$message.error('导出内容为空，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 确保内容是字符串\n              if (typeof contentToExport !== 'string') {\n                contentToExport = JSON.stringify(contentToExport, null, 2)\n              }\n              \n              // 避免导出\"null\"字符串\n              if (contentToExport === \"null\") {\n                this.$message.error('导出内容异常，请联系管理员')\n                this.exportLoading = false\n                return\n              }\n              \n              // 处理导出内容，移除元数据标题行\n              if (contentToExport.startsWith(\"【元数据】\")) {\n                // 查找元数据部分的结束位置（两个连续的换行符后面紧跟的是题型标题）\n                const metadataEndIndex = contentToExport.indexOf(\"\\n\\n\");\n                if (metadataEndIndex !== -1) {\n                  // 移除元数据部分\n                  contentToExport = contentToExport.substring(metadataEndIndex + 2);\n                }\n              }\n              \n              // 创建Blob对象，使用适当的MIME类型\n              const blob = new Blob([contentToExport], { type: 'text/plain;charset=utf-8' });\n              \n              // 创建文件名\n              const fileName = `题目导出_${new Date().getTime()}.txt`;\n              \n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName);\n              } else {\n                // 现代浏览器\n                const link = document.createElement('a');\n                link.href = URL.createObjectURL(blob);\n                link.download = fileName;\n                link.style.display = 'none';\n                document.body.appendChild(link);\n                link.click();\n                \n                // 清理创建的对象URL\n                setTimeout(() => {\n                  URL.revokeObjectURL(link.href);\n                  document.body.removeChild(link);\n                }, 100);\n              }\n              \n              this.$message.success('导出成功');\n        } else {\n              this.$message.warning(res.message || '导出失败');\n        }\n            this.exportLoading = false;\n          }).catch((error) => {\n            console.error('导出失败:', error);\n            this.$message.error('导出过程发生错误');\n            this.exportLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 模态框提交回调\n    modalFormOk() {\n      this.loadData()\n    },\n    \n    // 触发文件输入框点击\n    triggerFileInput() {\n      this.$refs.fileInput.click()\n    },\n    \n    // 处理文件选择变更\n    onFileChange(e) {\n      const file = e.target.files[0]\n      if (!file) return\n      \n      // 检查文件类型\n      if (!file.name.toLowerCase().endsWith('.txt') && file.type !== 'text/plain') {\n        this.$message.error('只能上传 .txt 文件!')\n        return\n      }\n      \n      this.selectedFile = file\n      \n      // 重置文件输入框，使相同文件可以再次选择\n      e.target.value = ''\n    },\n    \n    // 导入确认 - 重写为使用原生XMLHttpRequest\n    handleImportOk() {\n      if (!this.selectedFile) {\n        this.$message.warning('请选择要导入的文件')\n        return\n      }\n      \n      this.importConfirmLoading = true\n      \n      const formData = new FormData()\n      formData.append('file', this.selectedFile)\n      \n      // 移除所有元数据参数，只从文件中获取元数据\n      \n      // 使用原生XMLHttpRequest处理文件上传，避免序列化问题\n      const xhr = new XMLHttpRequest()\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      const baseURL = window._CONFIG['domianURL'] || ''\n      \n      // 设置请求超时\n      xhr.timeout = 120000 // 2分钟超时\n      \n      // 设置上传完成回调\n      xhr.onreadystatechange = () => {\n        if (xhr.readyState === 4) {\n          this.importConfirmLoading = false\n          \n          if (xhr.status === 200) {\n            try {\n              const res = JSON.parse(xhr.responseText)\n              console.log('导入响应数据:', res) // 调试日志\n\n              if (res.success) {\n                // 检查是否有详细的导入结果消息\n                let message = '导入成功'\n\n                if (res.result && res.result.message) {\n                  // 使用后端返回的详细消息\n                  message = res.result.message\n                  console.log('使用详细消息:', message) // 调试日志\n                } else if (res.message) {\n                  // 使用基本消息\n                  message = res.message\n                  console.log('使用基本消息:', message) // 调试日志\n                }\n\n                // 根据导入结果显示不同类型的消息\n                if (res.result && res.result.skippedCount > 0) {\n                  // 有跳过的题目，显示警告样式\n                  this.$message.warning(message, 5) // 显示5秒\n                } else {\n                  // 正常导入，显示成功样式\n                  this.$message.success(message)\n                }\n\n                this.handleImportCancel()\n                this.loadData()\n              } else {\n                // 检查是否是因为缺少元数据导致的错误\n                if (res.message && (\n                    res.message.includes(\"Field\") || \n                    res.message.includes(\"不存在\") || \n                    res.message.includes(\"没有默认值\") ||\n                    res.message.includes(\"不能为空\")\n                  )) {\n                  // 提示用户文件中需包含元数据\n                  this.$message.warning('导入失败：上传的文件缺少必要的元数据。请确保文件包含科目、级别、难度等必要信息。')\n              } else {\n                this.$message.warning(res.message || '导入失败')\n                }\n              }\n            } catch (e) {\n              console.error('解析响应失败:', e)\n              console.error('原始响应文本:', xhr.responseText)\n              this.$message.error('导入处理失败，请检查文件格式')\n            }\n          } else {\n            console.error('导入失败，响应内容:', xhr.responseText)\n            this.$message.error(`导入失败，状态码: ${xhr.status}`)\n          }\n        }\n      }\n      \n      // 设置上传进度回调\n      xhr.upload.onprogress = (event) => {\n        if (event.lengthComputable) {\n          const percentComplete = Math.round((event.loaded * 100) / event.total)\n          console.log(`上传进度: ${percentComplete}%`)\n        }\n      }\n      \n      // 设置超时处理\n      xhr.ontimeout = () => {\n        this.importConfirmLoading = false\n        this.$message.error('上传超时，请检查文件大小或网络连接')\n      }\n      \n      // 设置错误处理\n      xhr.onerror = () => {\n        this.importConfirmLoading = false\n        this.$message.error('网络错误，导入失败')\n      }\n      \n      // 发送请求\n      xhr.open('POST', `${baseURL}/teaching/examSystem/problemManage/import`, true)\n      xhr.setRequestHeader('X-Access-Token', token)\n      xhr.send(formData)\n    },\n    \n    // 导入取消\n    handleImportCancel() {\n      this.importModalVisible = false\n      this.selectedFile = null\n      this.importConfirmLoading = false\n    },\n    \n    // 下载导入模板\n    downloadTemplate() {\n      // 构建查询参数字符串\n      let queryParams = []\n      \n      // 添加当前导入表单中的参数\n      if (this.importParam.subject) {\n        queryParams.push(`subject=${encodeURIComponent(this.importParam.subject)}`)\n      }\n      if (this.importParam.level) {\n        queryParams.push(`level=${encodeURIComponent(this.importParam.level)}`)\n      }\n      if (this.importParam.difficulty) {\n        queryParams.push(`difficulty=${this.importParam.difficulty}`)\n      }\n      if (this.importParam.author) {\n        queryParams.push(`author=${encodeURIComponent(this.importParam.author)}`)\n      }\n      \n      // 构建完整URL\n      const baseURL = window._CONFIG['domianURL'] || '' // 获取基础URL\n      let url = `${baseURL}/teaching/examSystem/problemManage/downloadTemplate`\n      if (queryParams.length > 0) {\n        url += '?' + queryParams.join('&')\n      }\n      \n      // 显示下载中提示\n      this.$message.loading('模板下载中...', 0)\n      \n      // 使用fetch API下载文件\n      fetch(url, {\n        method: 'GET',\n        headers: {\n          'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)\n        }\n      })\n      .then(response => {\n        if (!response.ok) {\n          throw new Error(`下载失败: ${response.status}`)\n        }\n        return response.blob()\n      })\n      .then(blob => {\n        // 创建Blob URL\n        const blobUrl = window.URL.createObjectURL(blob)\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = blobUrl\n        link.download = '纯文本模板.txt'\n        document.body.appendChild(link)\n        link.click()\n        // 清理\n        setTimeout(() => {\n          window.URL.revokeObjectURL(blobUrl)\n          document.body.removeChild(link)\n        }, 100)\n        this.$message.destroy()\n      })\n      .catch(error => {\n        console.error('下载模板失败:', error)\n        this.$message.destroy()\n        this.$message.error('下载模板失败，请重试')\n      })\n    },\n    \n    // 处理拖拽事件\n    handleDragEnter() {\n      this.isDragover = true\n    },\n    handleDragLeave() {\n      this.isDragover = false\n    },\n    handleDrop(e) {\n      e.preventDefault()\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onFileChange({ target: { files } })\n      }\n    },\n    removeSelectedFile() {\n      this.selectedFile = null\n    },\n\n    // 自动格式化相关方法\n    showAutoTemplateModal() {\n      this.autoTemplateModalVisible = true\n      // 重置表单数据\n      this.autoTemplateParam = {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateCancel() {\n      this.autoTemplateModalVisible = false\n      this.autoTemplateSelectedFile = null\n      this.autoTemplateParam = {\n        subject: undefined,\n        level: '',\n        difficulty: undefined\n      }\n    },\n\n    triggerAutoTemplateFileInput() {\n      this.$refs.autoTemplateFileInput.click()\n    },\n\n    onAutoTemplateFileChange(event) {\n      const files = event.target.files\n      if (files.length > 0) {\n        const file = files[0]\n        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n          this.autoTemplateSelectedFile = file\n        } else {\n          this.$message.warning('请选择.txt格式的文本文件')\n          event.target.value = ''\n        }\n      }\n    },\n\n    handleAutoTemplateDragEnter() {\n      this.isAutoTemplateDragover = true\n    },\n\n    handleAutoTemplateDragLeave() {\n      this.isAutoTemplateDragover = false\n    },\n\n    handleAutoTemplateDrop(e) {\n      e.preventDefault()\n      this.isAutoTemplateDragover = false\n      const files = e.dataTransfer.files\n      if (files.length > 0) {\n        this.onAutoTemplateFileChange({ target: { files } })\n      }\n    },\n\n    removeAutoTemplateSelectedFile() {\n      this.autoTemplateSelectedFile = null\n    },\n\n    handleAutoTemplateOk() {\n      // 验证必填项\n      if (!this.autoTemplateParam.subject) {\n        this.$message.warning('请选择科目')\n        return\n      }\n      if (!this.autoTemplateParam.level) {\n        this.$message.warning('请输入级别')\n        return\n      }\n      if (!this.autoTemplateParam.difficulty) {\n        this.$message.warning('请选择难度')\n        return\n      }\n      if (!this.autoTemplateSelectedFile) {\n        this.$message.warning('请选择要格式化的文件')\n        return\n      }\n\n      this.autoTemplateConfirmLoading = true\n\n      const formData = new FormData()\n      formData.append('file', this.autoTemplateSelectedFile)\n      formData.append('subject', this.autoTemplateParam.subject)\n      formData.append('level', this.autoTemplateParam.level)\n      formData.append('difficulty', this.autoTemplateParam.difficulty)\n\n      // 调用自动格式化API\n      autoFormatTemplate(formData).then((res) => {\n        if (res.success) {\n          this.$message.success('自动格式化成功，正在下载格式化后的文件...')\n\n          if (res.result) {\n            this.downloadFormattedFile(res.result)\n          } else {\n            this.$message.error('未获取到格式化内容')\n          }\n\n          this.handleAutoTemplateCancel()\n        } else {\n          this.$message.warning(res.message || '自动格式化失败')\n        }\n        this.autoTemplateConfirmLoading = false\n      }).catch((error) => {\n        console.error('自动格式化失败:', error)\n        this.$message.error('自动格式化失败，请重试')\n        this.autoTemplateConfirmLoading = false\n      })\n    },\n\n    // 下载格式化后的文件\n    downloadFormattedFile(content) {\n      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n      const blobUrl = window.URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = blobUrl\n      link.download = '格式化题目文件.txt'\n      document.body.appendChild(link)\n      link.click()\n      setTimeout(() => {\n        window.URL.revokeObjectURL(blobUrl)\n        document.body.removeChild(link)\n      }, 100)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.table-operator {\n  margin-bottom: 18px;\n}\n.table-operator button {\n  margin-right: 8px;\n}\n\n.custom-upload {\n  padding: 16px;\n  background-color: #fafafa;\n  border: 1px dashed #d9d9d9;\n  border-radius: 4px;\n  text-align: center;\n  transition: border-color 0.3s;\n}\n.custom-upload:hover {\n  border-color: #1890ff;\n}\n.file-name {\n  margin-left: 8px;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.65);\n}\n\n.modern-upload-area {\n  margin-bottom: 16px;\n}\n\n.upload-drop-area {\n  padding: 24px;\n  background-color: #fff;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  text-align: center;\n  transition: all 0.3s;\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-drop-area:hover {\n  border-color: #1890ff;\n  background-color: #f0f7ff;\n}\n\n.is-dragover {\n  border-color: #1890ff;\n  background-color: #f0f7ff;\n  box-shadow: 0 0 10px rgba(24, 144, 255, 0.2);\n}\n\n.upload-icon {\n  font-size: 36px;\n  color: #1890ff;\n  margin-bottom: 8px;\n}\n\n.upload-text {\n  margin: 12px 0;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.65);\n}\n\n.selected-file {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  background-color: #f5f5f5;\n  padding: 8px 16px;\n  border-radius: 4px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.remove-file {\n  cursor: pointer;\n  color: #ff4d4f;\n  font-size: 16px;\n  transition: all 0.3s;\n}\n\n.remove-file:hover {\n  color: #ff7875;\n}\n\n.upload-button {\n  margin-top: 12px;\n}\n\n.upload-tip {\n  margin-top: 12px;\n  font-size: 13px;\n  color: #666;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.template-link {\n  color: #1890ff;\n  text-decoration: none;\n  font-weight: 500;\n  transition: all 0.3s;\n}\n\n.template-link:hover {\n  color: #40a9ff;\n  text-decoration: underline;\n}\n</style> "]}]}