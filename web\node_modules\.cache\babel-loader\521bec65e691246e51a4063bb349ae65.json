{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeDict.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeDict.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { getAction } from '@/api/manage';\nexport default {\n  name: 'JTreeDict',\n  data: function data() {\n    return {\n      treeData: [],\n      treeValue: null,\n      url_root: \"/sys/category/loadTreeRoot\",\n      url_children: \"/sys/category/loadTreeChildren\",\n      url_view: '/sys/category/loadOne'\n    };\n  },\n  props: {\n    value: {\n      type: String,\n      required: false\n    },\n    placeholder: {\n      type: String,\n      default: '请选择',\n      required: false\n    },\n    parentCode: {\n      type: String,\n      default: '',\n      required: false\n    },\n    field: {\n      type: String,\n      default: 'id',\n      required: false\n    },\n    root: {\n      type: Object,\n      required: false,\n      default: function _default() {\n        return {\n          pid: '0'\n        };\n      }\n    },\n    async: {\n      type: Boolean,\n      default: false,\n      required: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n      required: false\n    }\n  },\n  watch: {\n    root: {\n      handler: function handler(val) {\n        console.log(\"root-change\", val);\n      },\n      deep: true\n    },\n    parentCode: {\n      handler: function handler() {\n        this.loadRoot();\n      }\n    },\n    value: {\n      handler: function handler() {\n        this.loadViewInfo();\n      }\n    }\n  },\n  created: function created() {\n    this.loadRoot();\n    this.loadViewInfo();\n  },\n  model: {\n    prop: 'value',\n    event: 'change'\n  },\n  methods: {\n    loadViewInfo: function loadViewInfo() {\n      var _this = this;\n      if (!this.value || this.value == \"0\") {\n        this.treeValue = null;\n      } else {\n        var param = {\n          field: this.field,\n          val: this.value\n        };\n        getAction(this.url_view, param).then(function (res) {\n          if (res.success) {\n            _this.treeValue = {\n              value: _this.value,\n              label: res.result.name\n            };\n          }\n        });\n      }\n    },\n    loadRoot: function loadRoot() {\n      var _this2 = this;\n      var param = {\n        async: this.async,\n        pcode: this.parentCode\n      };\n      getAction(this.url_root, param).then(function (res) {\n        if (res.success) {\n          _this2.handleTreeNodeValue(res.result);\n          console.log(\"aaaa\", res.result);\n          _this2.treeData = _toConsumableArray(res.result);\n        } else {\n          _this2.$message.error(res.message);\n        }\n      });\n    },\n    asyncLoadTreeData: function asyncLoadTreeData(treeNode) {\n      var _this3 = this;\n      return new Promise(function (resolve) {\n        if (!_this3.async) {\n          resolve();\n          return;\n        }\n        if (treeNode.$vnode.children) {\n          resolve();\n          return;\n        }\n        var pid = treeNode.$vnode.key;\n        var param = {\n          pid: pid\n        };\n        getAction(_this3.url_children, param).then(function (res) {\n          if (res.success) {\n            _this3.handleTreeNodeValue(res.result);\n            _this3.addChildren(pid, res.result, _this3.treeData);\n            _this3.treeData = _toConsumableArray(_this3.treeData);\n          }\n          resolve();\n        });\n      });\n    },\n    addChildren: function addChildren(pid, children, treeArray) {\n      if (treeArray && treeArray.length > 0) {\n        var _iterator = _createForOfIteratorHelper(treeArray),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var item = _step.value;\n            if (item.key == pid) {\n              if (!children || children.length == 0) {\n                item.leaf = true;\n              } else {\n                item.children = children;\n              }\n              break;\n            } else {\n              this.addChildren(pid, children, item.children);\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      }\n    },\n    handleTreeNodeValue: function handleTreeNodeValue(result) {\n      var storeField = this.field == 'code' ? 'code' : 'key';\n      var _iterator2 = _createForOfIteratorHelper(result),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var i = _step2.value;\n          i.value = i[storeField];\n          i.isLeaf = !i.leaf ? false : true;\n          if (i.children && i.children.length > 0) {\n            this.handleTreeNodeValue(i.children);\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n    },\n    onChange: function onChange(value) {\n      console.log(value);\n      if (!value) {\n        this.$emit('change', '');\n      } else {\n        this.$emit('change', value.value);\n      }\n      this.treeValue = value;\n    },\n    onSearch: function onSearch(value) {\n      console.log(value);\n    },\n    getCurrTreeData: function getCurrTreeData() {\n      return this.treeData;\n    }\n  }\n};", {"version": 3, "names": ["getAction", "name", "data", "treeData", "treeValue", "url_root", "url_children", "url_view", "props", "value", "type", "String", "required", "placeholder", "default", "parentCode", "field", "root", "Object", "_default", "pid", "async", "Boolean", "disabled", "watch", "handler", "val", "console", "log", "deep", "loadRoot", "loadViewInfo", "created", "model", "prop", "event", "methods", "_this", "param", "then", "res", "success", "label", "result", "_this2", "pcode", "handleTreeNodeValue", "_toConsumableArray", "$message", "error", "message", "asyncLoadTreeData", "treeNode", "_this3", "Promise", "resolve", "$vnode", "children", "key", "add<PERSON><PERSON><PERSON><PERSON>", "treeArray", "length", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "leaf", "err", "e", "f", "storeField", "_iterator2", "_step2", "i", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "$emit", "onSearch", "getCurrTreeData"], "sources": ["src/components/jeecg/JTreeDict.vue"], "sourcesContent": ["<template>\n  <a-tree-select\n    allowClear\n    labelInValue\n    style=\"width: 100%\"\n    :disabled=\"disabled\"\n    :dropdownStyle=\"{ maxHeight: '400px', overflow: 'auto' }\"\n    :placeholder=\"placeholder\"\n    :loadData=\"asyncLoadTreeData\"\n    :value=\"treeValue\"\n    :treeData=\"treeData\"\n    @change=\"onChange\"\n    @search=\"onSearch\">\n  </a-tree-select>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'JTreeDict',\n    data(){\n      return {\n        treeData:[],\n        treeValue: null,\n        url_root:\"/sys/category/loadTreeRoot\",\n        url_children:\"/sys/category/loadTreeChildren\",\n        url_view:'/sys/category/loadOne',\n      }\n    },\n    props:{\n      value:{\n        type: String,\n        required: false\n      },\n      placeholder:{\n        type: String,\n        default: '请选择',\n        required: false\n      },\n      parentCode:{\n        type: String,\n        default: '',\n        required: false\n      },\n      field:{\n        type: String,\n        default: 'id',\n        required: false\n      },\n      root:{\n        type:Object,\n        required:false,\n        default:()=>{\n          return {\n            pid:'0'\n          }\n        }\n      },\n      async:{\n        type:Boolean,\n        default:false,\n        required:false\n      },\n      disabled:{\n        type:Boolean,\n        default:false,\n        required:false\n      }\n    },\n    watch:{\n      root:{\n        handler(val){\n          console.log(\"root-change\",val)\n        },\n        deep:true\n      },\n      parentCode:{\n        handler(){\n          this.loadRoot()\n        }\n      },\n      value:{\n        handler(){\n          this.loadViewInfo()\n        }\n      }\n    },\n    created(){\n      this.loadRoot()\n      this.loadViewInfo()\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    methods:{\n      loadViewInfo(){\n        if(!this.value || this.value==\"0\"){\n          this.treeValue = null\n        }else{\n          let param = {\n            field:this.field,\n            val:this.value\n          }\n          getAction(this.url_view,param).then(res=>{\n            if(res.success){\n              this.treeValue = {\n                value:this.value,\n                label:res.result.name\n              }\n            }\n          })\n        }\n      },\n      loadRoot(){\n        let param = {\n          async:this.async,\n          pcode:this.parentCode\n        }\n        getAction(this.url_root,param).then(res=>{\n          if(res.success){\n            this.handleTreeNodeValue(res.result)\n            console.log(\"aaaa\",res.result)\n            this.treeData = [...res.result]\n          }else{\n            this.$message.error(res.message)\n          }\n        })\n      },\n      asyncLoadTreeData (treeNode) {\n        return new Promise((resolve) => {\n          if(!this.async){\n            resolve()\n            return\n          }\n          if (treeNode.$vnode.children) {\n            resolve()\n            return\n          }\n          let pid = treeNode.$vnode.key\n          let param = {\n            pid:pid\n          }\n          getAction(this.url_children,param).then(res=>{\n            if(res.success){\n              this.handleTreeNodeValue(res.result)\n              this.addChildren(pid,res.result,this.treeData)\n              this.treeData = [...this.treeData]\n            }\n            resolve()\n          })\n        })\n      },\n      addChildren(pid,children,treeArray){\n        if(treeArray && treeArray.length>0){\n          for(let item of treeArray){\n            if(item.key == pid){\n              if(!children || children.length==0){\n                item.leaf = true\n              }else{\n                item.children = children\n              }\n              break\n            }else{\n              this.addChildren(pid,children,item.children)\n            }\n          }\n        }\n      },\n      handleTreeNodeValue(result){\n        let storeField = this.field=='code'?'code':'key'\n        for(let i of result){\n          i.value = i[storeField]\n          i.isLeaf = (!i.leaf)?false:true\n          if(i.children && i.children.length>0){\n            this.handleTreeNodeValue(i.children)\n          }\n        }\n      },\n      onChange(value){\n        console.log(value)\n        if(!value){\n          this.$emit('change', '');\n        }else{\n          this.$emit('change', value.value);\n        }\n        this.treeValue = value\n      },\n      onSearch(value){\n        console.log(value)\n      },\n      getCurrTreeData(){\n        return this.treeData\n      }\n    }\n\n  }\n</script>"], "mappings": ";;;;;;;AAiBA,SAAAA,SAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;MACAF,QAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAC,MAAA;MACAG,OAAA;MACAF,QAAA;IACA;IACAI,KAAA;MACAN,IAAA,EAAAC,MAAA;MACAG,OAAA;MACAF,QAAA;IACA;IACAK,IAAA;MACAP,IAAA,EAAAQ,MAAA;MACAN,QAAA;MACAE,OAAA,WAAAK,SAAA;QACA;UACAC,GAAA;QACA;MACA;IACA;IACAC,KAAA;MACAX,IAAA,EAAAY,OAAA;MACAR,OAAA;MACAF,QAAA;IACA;IACAW,QAAA;MACAb,IAAA,EAAAY,OAAA;MACAR,OAAA;MACAF,QAAA;IACA;EACA;EACAY,KAAA;IACAP,IAAA;MACAQ,OAAA,WAAAA,QAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,gBAAAF,GAAA;MACA;MACAG,IAAA;IACA;IACAd,UAAA;MACAU,OAAA,WAAAA,QAAA;QACA,KAAAK,QAAA;MACA;IACA;IACArB,KAAA;MACAgB,OAAA,WAAAA,QAAA;QACA,KAAAM,YAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,QAAA;IACA,KAAAC,YAAA;EACA;EACAE,KAAA;IACAC,IAAA;IACAC,KAAA;EACA;EACAC,OAAA;IACAL,YAAA,WAAAA,aAAA;MAAA,IAAAM,KAAA;MACA,UAAA5B,KAAA,SAAAA,KAAA;QACA,KAAAL,SAAA;MACA;QACA,IAAAkC,KAAA;UACAtB,KAAA,OAAAA,KAAA;UACAU,GAAA,OAAAjB;QACA;QACAT,SAAA,MAAAO,QAAA,EAAA+B,KAAA,EAAAC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,OAAA;YACAJ,KAAA,CAAAjC,SAAA;cACAK,KAAA,EAAA4B,KAAA,CAAA5B,KAAA;cACAiC,KAAA,EAAAF,GAAA,CAAAG,MAAA,CAAA1C;YACA;UACA;QACA;MACA;IACA;IACA6B,QAAA,WAAAA,SAAA;MAAA,IAAAc,MAAA;MACA,IAAAN,KAAA;QACAjB,KAAA,OAAAA,KAAA;QACAwB,KAAA,OAAA9B;MACA;MACAf,SAAA,MAAAK,QAAA,EAAAiC,KAAA,EAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAG,MAAA,CAAAE,mBAAA,CAAAN,GAAA,CAAAG,MAAA;UACAhB,OAAA,CAAAC,GAAA,SAAAY,GAAA,CAAAG,MAAA;UACAC,MAAA,CAAAzC,QAAA,GAAA4C,kBAAA,CAAAP,GAAA,CAAAG,MAAA;QACA;UACAC,MAAA,CAAAI,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,OAAA;QACA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACA,KAAAF,MAAA,CAAAhC,KAAA;UACAkC,OAAA;UACA;QACA;QACA,IAAAH,QAAA,CAAAI,MAAA,CAAAC,QAAA;UACAF,OAAA;UACA;QACA;QACA,IAAAnC,GAAA,GAAAgC,QAAA,CAAAI,MAAA,CAAAE,GAAA;QACA,IAAApB,KAAA;UACAlB,GAAA,EAAAA;QACA;QACApB,SAAA,CAAAqD,MAAA,CAAA/C,YAAA,EAAAgC,KAAA,EAAAC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,OAAA;YACAY,MAAA,CAAAP,mBAAA,CAAAN,GAAA,CAAAG,MAAA;YACAU,MAAA,CAAAM,WAAA,CAAAvC,GAAA,EAAAoB,GAAA,CAAAG,MAAA,EAAAU,MAAA,CAAAlD,QAAA;YACAkD,MAAA,CAAAlD,QAAA,GAAA4C,kBAAA,CAAAM,MAAA,CAAAlD,QAAA;UACA;UACAoD,OAAA;QACA;MACA;IACA;IACAI,WAAA,WAAAA,YAAAvC,GAAA,EAAAqC,QAAA,EAAAG,SAAA;MACA,IAAAA,SAAA,IAAAA,SAAA,CAAAC,MAAA;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAH,SAAA;UAAAI,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAvD,KAAA;YACA,IAAA2D,IAAA,CAAAV,GAAA,IAAAtC,GAAA;cACA,KAAAqC,QAAA,IAAAA,QAAA,CAAAI,MAAA;gBACAO,IAAA,CAAAC,IAAA;cACA;gBACAD,IAAA,CAAAX,QAAA,GAAAA,QAAA;cACA;cACA;YACA;cACA,KAAAE,WAAA,CAAAvC,GAAA,EAAAqC,QAAA,EAAAW,IAAA,CAAAX,QAAA;YACA;UACA;QAAA,SAAAa,GAAA;UAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;QAAA;UAAAR,SAAA,CAAAU,CAAA;QAAA;MACA;IACA;IACA1B,mBAAA,WAAAA,oBAAAH,MAAA;MACA,IAAA8B,UAAA,QAAAzD,KAAA;MAAA,IAAA0D,UAAA,GAAAX,0BAAA,CACApB,MAAA;QAAAgC,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAR,CAAA,IAAAC,IAAA;UAAA,IAAAS,CAAA,GAAAD,MAAA,CAAAlE,KAAA;UACAmE,CAAA,CAAAnE,KAAA,GAAAmE,CAAA,CAAAH,UAAA;UACAG,CAAA,CAAAC,MAAA,IAAAD,CAAA,CAAAP,IAAA;UACA,IAAAO,CAAA,CAAAnB,QAAA,IAAAmB,CAAA,CAAAnB,QAAA,CAAAI,MAAA;YACA,KAAAf,mBAAA,CAAA8B,CAAA,CAAAnB,QAAA;UACA;QACA;MAAA,SAAAa,GAAA;QAAAI,UAAA,CAAAH,CAAA,CAAAD,GAAA;MAAA;QAAAI,UAAA,CAAAF,CAAA;MAAA;IACA;IACAM,QAAA,WAAAA,SAAArE,KAAA;MACAkB,OAAA,CAAAC,GAAA,CAAAnB,KAAA;MACA,KAAAA,KAAA;QACA,KAAAsE,KAAA;MACA;QACA,KAAAA,KAAA,WAAAtE,KAAA,CAAAA,KAAA;MACA;MACA,KAAAL,SAAA,GAAAK,KAAA;IACA;IACAuE,QAAA,WAAAA,SAAAvE,KAAA;MACAkB,OAAA,CAAAC,GAAA,CAAAnB,KAAA;IACA;IACAwE,eAAA,WAAAA,gBAAA;MACA,YAAA9E,QAAA;IACA;EACA;AAEA", "ignoreList": []}]}