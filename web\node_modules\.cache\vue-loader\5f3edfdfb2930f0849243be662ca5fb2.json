{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderMainModal.vue?vue&type=style&index=0&id=3727f7e1&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderMainModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.ant-btn {\n  padding: 0 10px;\n  margin-left: 3px;\n}\n.ant-form-item-control {\n  line-height: 0px;\n}\n/** 主表单行间距 */\n.ant-form .ant-form-item {\n  margin-bottom: 10px;\n}\n/** Tab页面行间距 */\n.ant-tabs-content .ant-form-item {\n  margin-bottom: 0px;\n}\n", {"version": 3, "sources": ["JeecgOrderMainModal.vue"], "names": [], "mappings": ";AAkTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JeecgOrderMainModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"1200\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 主表单区域 -->\n        <a-row class=\"form-row\" :gutter=\"16\">\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单号\">\n              <a-input placeholder=\"请输入订单号\" v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\" />\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单类型\">\n              <a-select placeholder=\"请输入订单类型\" v-decorator=\"['ctype',{}]\">\n                <a-select-option value=\"1\">国内订单</a-select-option>\n                <a-select-option value=\"2\">国际订单</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单日期\">\n              <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" v-decorator=\"[ 'orderDate',{}]\"/>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"16\">\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单金额\">\n              <a-input-number style=\"width: 200px\" v-decorator=\"[ 'orderMoney', {}]\" />\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单备注\">\n              <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\" />\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n        <!-- 子表单区域 -->\n        <a-tabs defaultActiveKey=\"1\" >\n          <a-tab-pane tab=\"客户信息\" key=\"1\">\n            <div>\n              <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\">\n                <a-col :span=\"5\">客户名</a-col>\n                <a-col :span=\"5\">性别</a-col>\n                <a-col :span=\"5\">身份证号码</a-col>\n                <a-col :span=\"5\">手机号</a-col>\n                <a-col :span=\"4\">操作</a-col>\n              </a-row>\n\n              <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in orderMainModel.jeecgOrderCustomerList\" :key=\"index\">\n                <a-col :span=\"5\">\n                  <a-form-item>\n                    <a-input placeholder=\"客户名\" v-decorator=\"['jeecgOrderCustomerList['+index+'].name', {'initialValue':item.name,rules: [{ required: true, message: '请输入用户名!' }]}]\" />\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"5\">\n                  <a-form-item>\n                    <a-select placeholder=\"性别\" v-decorator=\"['jeecgOrderCustomerList['+index+'].sex', {'initialValue':item.sex}]\">\n                      <a-select-option value=\"1\">男</a-select-option>\n                      <a-select-option value=\"2\">女</a-select-option>\n                    </a-select>\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"5\">\n                  <a-form-item>\n                    <a-input placeholder=\"身份证号\" v-decorator=\"['jeecgOrderCustomerList['+index+'].idcard', {'initialValue':item.idcard,rules: [{ pattern: '^\\\\d{6}(18|19|20)?\\\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\\\d|3[01])\\\\d{3}(\\\\d|[xX])$', message: '身份证号格式不对!' }]}]\"/>\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"5\">\n                  <a-form-item>\n                    <a-input placeholder=\"手机号\" v-decorator=\"['jeecgOrderCustomerList['+index+'].telphone', {'initialValue':item.telphone,rules: [{ pattern: '^1(3|4|5|7|8)\\\\d{9}$', message: '手机号格式不对!' }]}]\"/>\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"4\">\n                  <a-form-item>\n                    <a-button @click=\"addRowCustom\" icon=\"plus\"></a-button>&nbsp;\n                    <a-button @click=\"delRowCustom(index)\" icon=\"minus\"></a-button>\n                  </a-form-item>\n                </a-col>\n              </a-row>\n            </div>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"机票信息\" key=\"2\" forceRender>\n            <div>\n              <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\">\n                <a-col :span=\"6\">航班号</a-col>\n                <a-col :span=\"6\">航班时间</a-col>\n                <a-col :span=\"6\">操作</a-col>\n              </a-row>\n              <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in orderMainModel.jeecgOrderTicketList\" :key=\"index\">\n                <a-col :span=\"6\">\n                  <a-form-item>\n                    <a-input placeholder=\"航班号\" v-decorator=\"['jeecgOrderTicketList['+index+'].ticketCode', {'initialValue':item.ticketCode,rules: [{ required: true, message: '请输入航班号!' }]}]\" />\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"6\">\n                  <a-form-item>\n                    <j-date placeholder=\"航班时间\" :trigger-change=\"true\" v-decorator=\"['jeecgOrderTicketList['+index+'].tickectDate', {'initialValue':item.tickectDate}]\"></j-date>\n                  </a-form-item>\n                </a-col>\n                <a-col :span=\"6\">\n                  <a-form-item>\n                    <a-button @click=\"addRowTicket\" icon=\"plus\"></a-button>&nbsp;\n                    <a-button @click=\"delRowTicket(index)\" icon=\"minus\"></a-button>\n                  </a-form-item>\n                </a-col>\n              </a-row>\n            </div>\n          </a-tab-pane>\n        </a-tabs>\n\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { httpAction,getAction } from '@/api/manage'\n  import JDate from '@/components/jeecg/JDate'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgOrderMainModal\",\n    components: {\n      JDate\n    },\n    data () {\n      return {\n        title:\"操作\",\n        visible: false,\n        orderMainModel: {jeecgOrderCustomerList: [{}],\n                           jeecgOrderTicketList: [{}]},\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules:{\n        },\n        url: {\n          add: \"/test/jeecgOrderMain/add\",\n          edit: \"/test/jeecgOrderMain/edit\",\n          orderCustomerList: \"/test/jeecgOrderMain/queryOrderCustomerListByMainId\",\n          orderTicketList: \"/test/jeecgOrderMain/queryOrderTicketListByMainId\",\n        },\n      }\n    },\n    created () {\n    },\n    methods: {\n      add () {\n        this.edit({});\n      },\n      edit (record) {\n        this.form.resetFields();\n        this.orderMainModel = Object.assign({}, record);\n        this.orderMainModel.jeecgOrderCustomerList = [{}];\n        this.orderMainModel.jeecgOrderTicketList = [{}];\n        //--------------------------------------------------------\n        //初始化明细表数据\n        console.log(this.orderMainModel.id)\n        if(this.orderMainModel.id){\n          let params = {id:this.orderMainModel.id}\n          //初始化订单机票列表\n          getAction(this.url.orderCustomerList,params).then((res)=>{\n            if(res.success){\n              this.orderMainModel.jeecgOrderCustomerList = res.result;\n              this.$forceUpdate()\n            }\n          })\n          //初始化订单客户列表\n          getAction(this.url.orderTicketList,params).then((res)=>{\n            if(res.success){\n              this.orderMainModel.jeecgOrderTicketList = res.result;\n              this.$forceUpdate()\n            }\n          })\n        }\n        //--------------------------------------------------------\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.orderMainModel,'orderCode','ctype','orderMoney','content'))\n          this.form.setFieldsValue({orderDate:this.orderMainModel.orderDate?moment(this.orderMainModel.orderDate):null}) //时间格式化\n        });\n        console.log(this.orderMainModel)\n      },\n      close () {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if(!this.orderMainModel.id){\n              httpurl+=this.url.add;\n              method = 'post';\n            }else{\n              httpurl+=this.url.edit;\n               method = 'put';\n            }\n            let orderMainData = Object.assign(this.orderMainModel, values);\n            //时间格式化\n            orderMainData.orderDate = orderMainData.orderDate?orderMainData.orderDate.format('YYYY-MM-DD HH:mm:ss'):null;\n            let formData = {\n              ...orderMainData,\n              jeecgOrderCustomerList: orderMainData.jeecgOrderCustomerList,\n              jeecgOrderTicketList: orderMainData.jeecgOrderTicketList\n            }\n\n            console.log(formData)\n            httpAction(httpurl,formData,method).then((res)=>{\n              if(res.success){\n                that.$message.success(res.message);\n                that.$emit('ok');\n              }else{\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n          }\n        })\n      },\n      handleCancel () {\n        this.close()\n      },\n      addRowCustom () {\n        this.orderMainModel.jeecgOrderCustomerList.push({});\n        this.$forceUpdate();\n      },\n      delRowCustom (index) {\n        console.log(index)\n        this.orderMainModel.jeecgOrderCustomerList.splice(index,1);\n        this.$forceUpdate();\n      },\n      addRowTicket () {\n        this.orderMainModel.jeecgOrderTicketList.push({});\n        console.log(this.orderMainModel.jeecgOrderTicketList)\n        this.$forceUpdate();\n      },\n      delRowTicket (index) {\n        console.log(index)\n        this.orderMainModel.jeecgOrderTicketList.splice(index,1);\n        this.$forceUpdate();\n      },\n\n\n    }\n  }\n</script>\n\n<style scoped>\n  .ant-btn {\n    padding: 0 10px;\n    margin-left: 3px;\n  }\n  .ant-form-item-control {\n    line-height: 0px;\n  }\n  /** 主表单行间距 */\n  .ant-form .ant-form-item {\n    margin-bottom: 10px;\n  }\n  /** Tab页面行间距 */\n  .ant-tabs-content .ant-form-item {\n    margin-bottom: 0px;\n  }\n</style>"]}]}