{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue?vue&type=template&id=790d3a66&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  centered\n  :title=\"name + '选择'\"\n  :width=\"width\"\n  :visible=\"visible\"\n  @ok=\"handleOk\"\n  @cancel=\"close\"\n  cancelText=\"关闭\">\n\n  <a-row :gutter=\"18\">\n    <a-col :span=\"16\">\n      <!-- 查询区域 -->\n      <div class=\"table-page-search-wrapper\">\n        <a-form layout=\"inline\">\n          <a-row :gutter=\"24\">\n\n            <a-col :span=\"14\">\n              <a-form-item :label=\"(queryParamText||name)\">\n                <a-input v-model=\"queryParam[queryParamCode||valueKey]\" :placeholder=\"'请输入' + (queryParamText||name)\" @pressEnter=\"searchQuery\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :span=\"8\">\n                <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n                  <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n                  <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n                </span>\n            </a-col>\n\n          </a-row>\n        </a-form>\n      </div>\n\n      <a-table\n        size=\"small\"\n        bordered\n        :rowKey=\"rowKey\"\n        :columns=\"innerColumns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :scroll=\"{ y: 240 }\"\n        :rowSelection=\"{selectedRowKeys, onChange: onSelectChange, type: multiple ? 'checkbox':'radio'}\"\n        :customRow=\"customRowFn\"\n        @change=\"handleTableChange\">\n      </a-table>\n\n    </a-col>\n    <a-col :span=\"8\">\n      <a-card :title=\"'已选' + name\" :bordered=\"false\" :head-style=\"{padding:0}\" :body-style=\"{padding:0}\">\n\n        <a-table size=\"small\" :rowKey=\"rowKey\" bordered v-bind=\"selectedTable\">\n            <span slot=\"action\" slot-scope=\"text, record, index\">\n              <a @click=\"handleDeleteSelected(record, index)\">删除</a>\n            </span>\n        </a-table>\n\n      </a-card>\n    </a-col>\n  </a-row>\n</a-modal>\n", null]}