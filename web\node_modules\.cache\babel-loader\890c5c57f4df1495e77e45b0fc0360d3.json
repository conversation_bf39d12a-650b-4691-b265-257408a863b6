{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\testSuites\\performanceTestSuite.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\testSuites\\performanceTestSuite.js", "mtime": 1753520860097}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 性能测试套件\n * 测试大量图片加载性能、富文本编辑器性能、数据库查询性能、优化性能瓶颈\n */\n\nimport { mathRenderer } from '../mathRenderer';\n\n/**\n * 性能测试套件类\n */\nexport var PerformanceTestSuite = /*#__PURE__*/function () {\n  function PerformanceTestSuite() {\n    _classCallCheck(this, PerformanceTestSuite);\n    this.name = '性能测试套件';\n    this.description = '测试系统各组件的性能表现和资源使用情况';\n    this.performanceObserver = null;\n    this.memoryBaseline = null;\n  }\n\n  /**\n   * 测试套件初始化\n   */\n  return _createClass(PerformanceTestSuite, [{\n    key: \"setup\",\n    value: (function () {\n      var _setup = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              console.log('初始化性能测试套件...');\n\n              // 记录内存基线\n              if (performance.memory) {\n                this.memoryBaseline = {\n                  usedJSHeapSize: performance.memory.usedJSHeapSize,\n                  totalJSHeapSize: performance.memory.totalJSHeapSize,\n                  jsHeapSizeLimit: performance.memory.jsHeapSizeLimit\n                };\n              }\n\n              // 清理性能标记\n              if (performance.clearMarks) {\n                performance.clearMarks();\n              }\n              if (performance.clearMeasures) {\n                performance.clearMeasures();\n              }\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function setup() {\n        return _setup.apply(this, arguments);\n      }\n      return setup;\n    }()\n    /**\n     * 测试套件清理\n     */\n    )\n  }, {\n    key: \"teardown\",\n    value: (function () {\n      var _teardown = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              console.log('清理性能测试套件...');\n\n              // 断开性能观察器\n              if (this.performanceObserver) {\n                this.performanceObserver.disconnect();\n                this.performanceObserver = null;\n              }\n            case 2:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function teardown() {\n        return _teardown.apply(this, arguments);\n      }\n      return teardown;\n    }()\n    /**\n     * 获取测试用例列表\n     * @returns {Array} 测试用例数组\n     */\n    )\n  }, {\n    key: \"getTestCases\",\n    value: function getTestCases() {\n      return [\n      // 图片加载性能测试\n      {\n        name: 'image_loading_performance',\n        description: '测试大量图片加载性能',\n        run: this.testImageLoadingPerformance.bind(this)\n      }, {\n        name: 'image_rendering_performance',\n        description: '测试图片渲染性能',\n        run: this.testImageRenderingPerformance.bind(this)\n      },\n      // 富文本编辑器性能测试\n      {\n        name: 'tinymce_initialization_performance',\n        description: '测试TinyMCE初始化性能',\n        run: this.testTinyMCEInitializationPerformance.bind(this)\n      }, {\n        name: 'tinymce_content_loading_performance',\n        description: '测试TinyMCE内容加载性能',\n        run: this.testTinyMCEContentLoadingPerformance.bind(this)\n      }, {\n        name: 'tinymce_typing_performance',\n        description: '测试TinyMCE输入性能',\n        run: this.testTinyMCETypingPerformance.bind(this)\n      },\n      // 数学公式渲染性能测试\n      {\n        name: 'math_formula_rendering_performance',\n        description: '测试数学公式渲染性能',\n        run: this.testMathFormulaRenderingPerformance.bind(this)\n      }, {\n        name: 'math_formula_batch_rendering',\n        description: '测试批量数学公式渲染性能',\n        run: this.testMathFormulaBatchRendering.bind(this)\n      },\n      // 数据处理性能测试\n      {\n        name: 'data_parsing_performance',\n        description: '测试数据解析性能',\n        run: this.testDataParsingPerformance.bind(this)\n      }, {\n        name: 'large_dataset_processing',\n        description: '测试大数据集处理性能',\n        run: this.testLargeDatasetProcessing.bind(this)\n      },\n      // 内存使用测试\n      {\n        name: 'memory_usage_monitoring',\n        description: '测试内存使用情况',\n        run: this.testMemoryUsageMonitoring.bind(this)\n      }, {\n        name: 'memory_leak_detection',\n        description: '测试内存泄漏检测',\n        run: this.testMemoryLeakDetection.bind(this)\n      },\n      // 网络性能测试\n      {\n        name: 'api_response_time',\n        description: '测试API响应时间',\n        run: this.testAPIResponseTime.bind(this)\n      }, {\n        name: 'file_upload_performance',\n        description: '测试文件上传性能',\n        run: this.testFileUploadPerformance.bind(this)\n      }];\n    }\n\n    /**\n     * 测试图片加载性能\n     */\n  }, {\n    key: \"testImageLoadingPerformance\",\n    value: (function () {\n      var _testImageLoadingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var startTime, imageCount, loadPromises, _loop, i, results, endTime, successCount, totalTime, averageLoadTime;\n        return _regeneratorRuntime().wrap(function _callee3$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              startTime = performance.now();\n              imageCount = 20;\n              loadPromises = [];\n              _context4.prev = 3;\n              _loop = /*#__PURE__*/_regeneratorRuntime().mark(function _loop(i) {\n                var promise;\n                return _regeneratorRuntime().wrap(function _loop$(_context3) {\n                  while (1) switch (_context3.prev = _context3.next) {\n                    case 0:\n                      promise = new Promise(function (resolve, reject) {\n                        var img = new Image();\n                        var imageStartTime = performance.now();\n                        img.onload = function () {\n                          var loadTime = performance.now() - imageStartTime;\n                          resolve({\n                            success: true,\n                            loadTime: loadTime\n                          });\n                        };\n                        img.onerror = function () {\n                          var loadTime = performance.now() - imageStartTime;\n                          resolve({\n                            success: false,\n                            loadTime: loadTime\n                          });\n                        };\n\n                        // 使用测试图片URL或生成随机图片\n                        img.src = \"data:image/svg+xml;base64,\".concat(btoa(\"<svg width=\\\"100\\\" height=\\\"100\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"rgb(\".concat(Math.floor(Math.random() * 255), \",\").concat(Math.floor(Math.random() * 255), \",\").concat(Math.floor(Math.random() * 255), \")\\\"/><text x=\\\"50\\\" y=\\\"50\\\" text-anchor=\\\"middle\\\" dy=\\\".3em\\\" fill=\\\"white\\\">\").concat(i + 1, \"</text></svg>\")));\n                      });\n                      loadPromises.push(promise);\n                    case 2:\n                    case \"end\":\n                      return _context3.stop();\n                  }\n                }, _loop);\n              });\n              i = 0;\n            case 6:\n              if (!(i < imageCount)) {\n                _context4.next = 11;\n                break;\n              }\n              return _context4.delegateYield(_loop(i), \"t0\", 8);\n            case 8:\n              i++;\n              _context4.next = 6;\n              break;\n            case 11:\n              _context4.next = 13;\n              return Promise.all(loadPromises);\n            case 13:\n              results = _context4.sent;\n              endTime = performance.now();\n              successCount = results.filter(function (r) {\n                return r.success;\n              }).length;\n              totalTime = endTime - startTime;\n              averageLoadTime = results.reduce(function (sum, r) {\n                return sum + r.loadTime;\n              }, 0) / results.length;\n              return _context4.abrupt(\"return\", {\n                success: successCount >= imageCount * 0.8,\n                // 80%成功率\n                metrics: {\n                  totalImages: imageCount,\n                  successCount: successCount,\n                  totalTime: totalTime,\n                  averageLoadTime: averageLoadTime,\n                  imagesPerSecond: imageCount / (totalTime / 1000)\n                }\n              });\n            case 21:\n              _context4.prev = 21;\n              _context4.t1 = _context4[\"catch\"](3);\n              return _context4.abrupt(\"return\", {\n                success: false,\n                error: _context4.t1.message\n              });\n            case 24:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee3, null, [[3, 21]]);\n      }));\n      function testImageLoadingPerformance() {\n        return _testImageLoadingPerformance.apply(this, arguments);\n      }\n      return testImageLoadingPerformance;\n    }()\n    /**\n     * 测试图片渲染性能\n     */\n    )\n  }, {\n    key: \"testImageRenderingPerformance\",\n    value: (function () {\n      var _testImageRenderingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var startTime, container, imageCount, i, img, endTime, renderTime;\n        return _regeneratorRuntime().wrap(function _callee4$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              startTime = performance.now();\n              _context5.prev = 1;\n              // 创建测试容器\n              container = document.createElement('div');\n              container.style.position = 'absolute';\n              container.style.top = '-9999px';\n              document.body.appendChild(container);\n\n              // 添加多个图片元素\n              imageCount = 50;\n              for (i = 0; i < imageCount; i++) {\n                img = document.createElement('img');\n                img.src = \"data:image/svg+xml;base64,\".concat(btoa(\"<svg width=\\\"50\\\" height=\\\"50\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"20\\\" fill=\\\"blue\\\"/></svg>\"));\n                img.style.width = '50px';\n                img.style.height = '50px';\n                container.appendChild(img);\n              }\n\n              // 强制重排和重绘\n              container.offsetHeight;\n              endTime = performance.now();\n              renderTime = endTime - startTime; // 清理\n              document.body.removeChild(container);\n              return _context5.abrupt(\"return\", {\n                success: renderTime < 1000,\n                // 1秒内完成\n                metrics: {\n                  imageCount: imageCount,\n                  renderTime: renderTime,\n                  averageRenderTime: renderTime / imageCount\n                }\n              });\n            case 15:\n              _context5.prev = 15;\n              _context5.t0 = _context5[\"catch\"](1);\n              return _context5.abrupt(\"return\", {\n                success: false,\n                error: _context5.t0.message\n              });\n            case 18:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee4, null, [[1, 15]]);\n      }));\n      function testImageRenderingPerformance() {\n        return _testImageRenderingPerformance.apply(this, arguments);\n      }\n      return testImageRenderingPerformance;\n    }()\n    /**\n     * 测试TinyMCE初始化性能\n     */\n    )\n  }, {\n    key: \"testTinyMCEInitializationPerformance\",\n    value: (function () {\n      var _testTinyMCEInitializationPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var startTime, textarea, endTime;\n        return _regeneratorRuntime().wrap(function _callee5$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              startTime = performance.now();\n              _context6.prev = 1;\n              // 创建测试文本区域\n              textarea = document.createElement('textarea');\n              textarea.id = 'performance-test-editor';\n              textarea.style.position = 'absolute';\n              textarea.style.top = '-9999px';\n              document.body.appendChild(textarea);\n\n              // 模拟TinyMCE初始化（如果TinyMCE可用）\n              if (!window.tinymce) {\n                _context6.next = 12;\n                break;\n              }\n              _context6.next = 10;\n              return new Promise(function (resolve, reject) {\n                window.tinymce.init({\n                  target: textarea,\n                  height: 300,\n                  menubar: false,\n                  toolbar: 'bold italic underline',\n                  setup: function setup(editor) {\n                    editor.on('init', function () {\n                      var endTime = performance.now();\n                      var initTime = endTime - startTime;\n\n                      // 清理\n                      editor.destroy();\n                      document.body.removeChild(textarea);\n                      resolve({\n                        success: initTime < 3000,\n                        // 3秒内初始化\n                        metrics: {\n                          initializationTime: initTime\n                        }\n                      });\n                    });\n                  }\n                });\n              });\n            case 10:\n              _context6.next = 17;\n              break;\n            case 12:\n              _context6.next = 14;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 100);\n              });\n            case 14:\n              endTime = performance.now();\n              document.body.removeChild(textarea);\n              return _context6.abrupt(\"return\", {\n                success: true,\n                metrics: {\n                  initializationTime: endTime - startTime,\n                  note: 'TinyMCE not available, simulated initialization'\n                }\n              });\n            case 17:\n              _context6.next = 22;\n              break;\n            case 19:\n              _context6.prev = 19;\n              _context6.t0 = _context6[\"catch\"](1);\n              return _context6.abrupt(\"return\", {\n                success: false,\n                error: _context6.t0.message\n              });\n            case 22:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee5, null, [[1, 19]]);\n      }));\n      function testTinyMCEInitializationPerformance() {\n        return _testTinyMCEInitializationPerformance.apply(this, arguments);\n      }\n      return testTinyMCEInitializationPerformance;\n    }()\n    /**\n     * 测试TinyMCE内容加载性能\n     */\n    )\n  }, {\n    key: \"testTinyMCEContentLoadingPerformance\",\n    value: (function () {\n      var _testTinyMCEContentLoadingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var startTime, largeContent, div, endTime, loadTime;\n        return _regeneratorRuntime().wrap(function _callee6$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              startTime = performance.now();\n              _context7.prev = 1;\n              // 生成大量测试内容\n              largeContent = this.generateLargeHTMLContent(1000); // 1000行内容\n              // 模拟内容加载\n              div = document.createElement('div');\n              div.style.position = 'absolute';\n              div.style.top = '-9999px';\n              document.body.appendChild(div);\n              div.innerHTML = largeContent;\n\n              // 强制渲染\n              div.offsetHeight;\n              endTime = performance.now();\n              loadTime = endTime - startTime; // 清理\n              document.body.removeChild(div);\n              return _context7.abrupt(\"return\", {\n                success: loadTime < 2000,\n                // 2秒内加载\n                metrics: {\n                  contentSize: largeContent.length,\n                  loadTime: loadTime,\n                  loadSpeed: largeContent.length / loadTime // 字符/毫秒\n                }\n              });\n            case 15:\n              _context7.prev = 15;\n              _context7.t0 = _context7[\"catch\"](1);\n              return _context7.abrupt(\"return\", {\n                success: false,\n                error: _context7.t0.message\n              });\n            case 18:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee6, this, [[1, 15]]);\n      }));\n      function testTinyMCEContentLoadingPerformance() {\n        return _testTinyMCEContentLoadingPerformance.apply(this, arguments);\n      }\n      return testTinyMCEContentLoadingPerformance;\n    }()\n    /**\n     * 测试TinyMCE输入性能\n     */\n    )\n  }, {\n    key: \"testTinyMCETypingPerformance\",\n    value: (function () {\n      var _testTinyMCETypingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var startTime, inputCount, inputTimes, _loop2, i, endTime, totalTime, averageInputTime;\n        return _regeneratorRuntime().wrap(function _callee7$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              startTime = performance.now();\n              _context9.prev = 1;\n              // 模拟快速输入\n              inputCount = 100;\n              inputTimes = [];\n              _loop2 = /*#__PURE__*/_regeneratorRuntime().mark(function _loop2() {\n                var inputStart;\n                return _regeneratorRuntime().wrap(function _loop2$(_context8) {\n                  while (1) switch (_context8.prev = _context8.next) {\n                    case 0:\n                      inputStart = performance.now(); // 模拟输入操作\n                      _context8.next = 3;\n                      return new Promise(function (resolve) {\n                        setTimeout(function () {\n                          var inputEnd = performance.now();\n                          inputTimes.push(inputEnd - inputStart);\n                          resolve();\n                        }, 1);\n                      });\n                    case 3:\n                    case \"end\":\n                      return _context8.stop();\n                  }\n                }, _loop2);\n              });\n              i = 0;\n            case 6:\n              if (!(i < inputCount)) {\n                _context9.next = 11;\n                break;\n              }\n              return _context9.delegateYield(_loop2(), \"t0\", 8);\n            case 8:\n              i++;\n              _context9.next = 6;\n              break;\n            case 11:\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageInputTime = inputTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / inputTimes.length;\n              return _context9.abrupt(\"return\", {\n                success: averageInputTime < 50,\n                // 平均输入时间小于50ms\n                metrics: {\n                  inputCount: inputCount,\n                  totalTime: totalTime,\n                  averageInputTime: averageInputTime,\n                  maxInputTime: Math.max.apply(Math, inputTimes),\n                  minInputTime: Math.min.apply(Math, inputTimes)\n                }\n              });\n            case 17:\n              _context9.prev = 17;\n              _context9.t1 = _context9[\"catch\"](1);\n              return _context9.abrupt(\"return\", {\n                success: false,\n                error: _context9.t1.message\n              });\n            case 20:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee7, null, [[1, 17]]);\n      }));\n      function testTinyMCETypingPerformance() {\n        return _testTinyMCETypingPerformance.apply(this, arguments);\n      }\n      return testTinyMCETypingPerformance;\n    }()\n    /**\n     * 测试数学公式渲染性能\n     */\n    )\n  }, {\n    key: \"testMathFormulaRenderingPerformance\",\n    value: (function () {\n      var _testMathFormulaRenderingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var startTime, container, formulas, endTime, renderTime, renderedElements;\n        return _regeneratorRuntime().wrap(function _callee8$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              startTime = performance.now();\n              _context10.prev = 1;\n              // 创建测试容器\n              container = document.createElement('div');\n              container.style.position = 'absolute';\n              container.style.top = '-9999px';\n              document.body.appendChild(container);\n\n              // 添加多个数学公式\n              formulas = ['E = mc^2', '\\\\int_{-\\\\infty}^{\\\\infty} e^{-x^2} dx = \\\\sqrt{\\\\pi}', '\\\\sum_{n=1}^{\\\\infty} \\\\frac{1}{n^2} = \\\\frac{\\\\pi^2}{6}', '\\\\frac{d}{dx}\\\\int_{a}^{x} f(t) dt = f(x)', '\\\\lim_{x \\\\to \\\\infty} \\\\left(1 + \\\\frac{1}{x}\\\\right)^x = e'];\n              formulas.forEach(function (formula, index) {\n                var span = document.createElement('span');\n                span.className = 'math-tex';\n                span.setAttribute('data-math', formula);\n                span.textContent = formula;\n                container.appendChild(span);\n              });\n\n              // 渲染数学公式\n              mathRenderer.renderAll(container);\n\n              // 等待渲染完成\n              _context10.next = 11;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 1000);\n              });\n            case 11:\n              endTime = performance.now();\n              renderTime = endTime - startTime; // 检查渲染结果\n              renderedElements = container.querySelectorAll('.katex'); // 清理\n              document.body.removeChild(container);\n              return _context10.abrupt(\"return\", {\n                success: renderedElements.length === formulas.length && renderTime < 3000,\n                metrics: {\n                  formulaCount: formulas.length,\n                  renderedCount: renderedElements.length,\n                  renderTime: renderTime,\n                  averageRenderTime: renderTime / formulas.length\n                }\n              });\n            case 18:\n              _context10.prev = 18;\n              _context10.t0 = _context10[\"catch\"](1);\n              return _context10.abrupt(\"return\", {\n                success: false,\n                error: _context10.t0.message\n              });\n            case 21:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee8, null, [[1, 18]]);\n      }));\n      function testMathFormulaRenderingPerformance() {\n        return _testMathFormulaRenderingPerformance.apply(this, arguments);\n      }\n      return testMathFormulaRenderingPerformance;\n    }()\n    /**\n     * 测试批量数学公式渲染性能\n     */\n    )\n  }, {\n    key: \"testMathFormulaBatchRendering\",\n    value: (function () {\n      var _testMathFormulaBatchRendering = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee9() {\n        var startTime, container, formulaCount, i, span, endTime, renderTime, renderedElements;\n        return _regeneratorRuntime().wrap(function _callee9$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              startTime = performance.now();\n              _context11.prev = 1;\n              container = document.createElement('div');\n              container.style.position = 'absolute';\n              container.style.top = '-9999px';\n              document.body.appendChild(container);\n\n              // 生成大量数学公式\n              formulaCount = 50;\n              for (i = 0; i < formulaCount; i++) {\n                span = document.createElement('span');\n                span.className = 'math-tex';\n                span.setAttribute('data-math', \"x^\".concat(i, \" + y^\").concat(i, \" = z^\").concat(i));\n                span.textContent = \"x^\".concat(i, \" + y^\").concat(i, \" = z^\").concat(i);\n                container.appendChild(span);\n              }\n\n              // 批量渲染\n              mathRenderer.renderAll(container);\n              _context11.next = 11;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 2000);\n              });\n            case 11:\n              endTime = performance.now();\n              renderTime = endTime - startTime;\n              renderedElements = container.querySelectorAll('.katex');\n              document.body.removeChild(container);\n              return _context11.abrupt(\"return\", {\n                success: renderedElements.length >= formulaCount * 0.9 && renderTime < 5000,\n                metrics: {\n                  formulaCount: formulaCount,\n                  renderedCount: renderedElements.length,\n                  renderTime: renderTime,\n                  formulasPerSecond: formulaCount / (renderTime / 1000)\n                }\n              });\n            case 18:\n              _context11.prev = 18;\n              _context11.t0 = _context11[\"catch\"](1);\n              return _context11.abrupt(\"return\", {\n                success: false,\n                error: _context11.t0.message\n              });\n            case 21:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee9, null, [[1, 18]]);\n      }));\n      function testMathFormulaBatchRendering() {\n        return _testMathFormulaBatchRendering.apply(this, arguments);\n      }\n      return testMathFormulaBatchRendering;\n    }()\n    /**\n     * 测试数据解析性能\n     */\n    )\n  }, {\n    key: \"testDataParsingPerformance\",\n    value: (function () {\n      var _testDataParsingPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var startTime, dataCount, testData, i, parseStartTime, parsedData, parseEndTime, parseTime, endTime, totalTime;\n        return _regeneratorRuntime().wrap(function _callee10$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              startTime = performance.now();\n              _context12.prev = 1;\n              // 生成大量测试数据\n              dataCount = 1000;\n              testData = [];\n              for (i = 0; i < dataCount; i++) {\n                testData.push({\n                  id: \"test-\".concat(i),\n                  title: \"\\u6D4B\\u8BD5\\u9898\\u76EE\".concat(i),\n                  questionType: i % 3 + 1,\n                  content: JSON.stringify({\n                    version: '2.1',\n                    template_content: \"\\u9898\\u76EE\\uFF1A\\u6D4B\\u8BD5\\u9898\\u76EE\".concat(i),\n                    answer: 'A',\n                    analysis: \"\\u8FD9\\u662F\\u7B2C\".concat(i, \"\\u9053\\u9898\\u76EE\\u7684\\u89E3\\u6790\")\n                  })\n                });\n              }\n\n              // 解析数据\n              parseStartTime = performance.now();\n              parsedData = testData.map(function (item) {\n                try {\n                  return JSON.parse(item.content);\n                } catch (error) {\n                  return null;\n                }\n              }).filter(function (item) {\n                return item !== null;\n              });\n              parseEndTime = performance.now();\n              parseTime = parseEndTime - parseStartTime;\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              return _context12.abrupt(\"return\", {\n                success: parsedData.length === dataCount && parseTime < 1000,\n                metrics: {\n                  dataCount: dataCount,\n                  parsedCount: parsedData.length,\n                  parseTime: parseTime,\n                  totalTime: totalTime,\n                  parseSpeed: dataCount / (parseTime / 1000) // 项目/秒\n                }\n              });\n            case 14:\n              _context12.prev = 14;\n              _context12.t0 = _context12[\"catch\"](1);\n              return _context12.abrupt(\"return\", {\n                success: false,\n                error: _context12.t0.message\n              });\n            case 17:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee10, null, [[1, 14]]);\n      }));\n      function testDataParsingPerformance() {\n        return _testDataParsingPerformance.apply(this, arguments);\n      }\n      return testDataParsingPerformance;\n    }()\n    /**\n     * 测试大数据集处理性能\n     */\n    )\n  }, {\n    key: \"testLargeDatasetProcessing\",\n    value: (function () {\n      var _testLargeDatasetProcessing = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var startTime, dataSize, largeDataset, filterStartTime, filtered, filterTime, sortStartTime, sorted, sortTime, mapStartTime, mapped, mapTime, endTime, totalTime;\n        return _regeneratorRuntime().wrap(function _callee11$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              startTime = performance.now();\n              _context13.prev = 1;\n              // 生成大数据集\n              dataSize = 10000;\n              largeDataset = Array.from({\n                length: dataSize\n              }, function (_, i) {\n                return {\n                  id: i,\n                  title: \"\\u9898\\u76EE\".concat(i),\n                  content: \"\\u8FD9\\u662F\\u7B2C\".concat(i, \"\\u9053\\u9898\\u76EE\\u7684\\u5185\\u5BB9\\uFF0C\\u5305\\u542B\\u4E00\\u4E9B\\u6D4B\\u8BD5\\u6587\\u672C\\u548C\\u6570\\u636E\\u3002\").repeat(10),\n                  metadata: {\n                    created: new Date().toISOString(),\n                    tags: [\"tag\".concat(i % 10), \"category\".concat(i % 5)],\n                    difficulty: i % 3 + 1\n                  }\n                };\n              }); // 执行各种数据操作\n              filterStartTime = performance.now();\n              filtered = largeDataset.filter(function (item) {\n                return item.difficulty === 2;\n              });\n              filterTime = performance.now() - filterStartTime;\n              sortStartTime = performance.now();\n              sorted = [].concat(largeDataset).sort(function (a, b) {\n                return a.title.localeCompare(b.title);\n              });\n              sortTime = performance.now() - sortStartTime;\n              mapStartTime = performance.now();\n              mapped = largeDataset.map(function (item) {\n                return _objectSpread(_objectSpread({}, item), {}, {\n                  processed: true,\n                  contentLength: item.content.length\n                });\n              });\n              mapTime = performance.now() - mapStartTime;\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              return _context13.abrupt(\"return\", {\n                success: totalTime < 5000,\n                // 5秒内完成\n                metrics: {\n                  dataSize: dataSize,\n                  totalTime: totalTime,\n                  filterTime: filterTime,\n                  sortTime: sortTime,\n                  mapTime: mapTime,\n                  filteredCount: filtered.length,\n                  processingSpeed: dataSize / (totalTime / 1000)\n                }\n              });\n            case 18:\n              _context13.prev = 18;\n              _context13.t0 = _context13[\"catch\"](1);\n              return _context13.abrupt(\"return\", {\n                success: false,\n                error: _context13.t0.message\n              });\n            case 21:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee11, null, [[1, 18]]);\n      }));\n      function testLargeDatasetProcessing() {\n        return _testLargeDatasetProcessing.apply(this, arguments);\n      }\n      return testLargeDatasetProcessing;\n    }()\n    /**\n     * 测试内存使用情况\n     */\n    )\n  }, {\n    key: \"testMemoryUsageMonitoring\",\n    value: (function () {\n      var _testMemoryUsageMonitoring = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var beforeMemory, largeArray, afterMemory, memoryIncrease;\n        return _regeneratorRuntime().wrap(function _callee12$(_context14) {\n          while (1) switch (_context14.prev = _context14.next) {\n            case 0:\n              _context14.prev = 0;\n              if (performance.memory) {\n                _context14.next = 3;\n                break;\n              }\n              return _context14.abrupt(\"return\", {\n                success: false,\n                error: 'Performance.memory API not available'\n              });\n            case 3:\n              beforeMemory = {\n                usedJSHeapSize: performance.memory.usedJSHeapSize,\n                totalJSHeapSize: performance.memory.totalJSHeapSize\n              }; // 创建一些内存使用\n              largeArray = new Array(100000).fill(0).map(function (_, i) {\n                return {\n                  id: i,\n                  data: new Array(100).fill(\"data-\".concat(i))\n                };\n              }); // 强制垃圾回收（如果可用）\n              if (window.gc) {\n                window.gc();\n              }\n              _context14.next = 8;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 100);\n              });\n            case 8:\n              afterMemory = {\n                usedJSHeapSize: performance.memory.usedJSHeapSize,\n                totalJSHeapSize: performance.memory.totalJSHeapSize\n              };\n              memoryIncrease = afterMemory.usedJSHeapSize - beforeMemory.usedJSHeapSize; // 清理\n              largeArray.length = 0;\n              return _context14.abrupt(\"return\", {\n                success: memoryIncrease < 50 * 1024 * 1024,\n                // 内存增长小于50MB\n                metrics: {\n                  beforeMemory: beforeMemory,\n                  afterMemory: afterMemory,\n                  memoryIncrease: memoryIncrease,\n                  memoryIncreaseKB: Math.round(memoryIncrease / 1024)\n                }\n              });\n            case 14:\n              _context14.prev = 14;\n              _context14.t0 = _context14[\"catch\"](0);\n              return _context14.abrupt(\"return\", {\n                success: false,\n                error: _context14.t0.message\n              });\n            case 17:\n            case \"end\":\n              return _context14.stop();\n          }\n        }, _callee12, null, [[0, 14]]);\n      }));\n      function testMemoryUsageMonitoring() {\n        return _testMemoryUsageMonitoring.apply(this, arguments);\n      }\n      return testMemoryUsageMonitoring;\n    }()\n    /**\n     * 测试内存泄漏检测\n     */\n    )\n  }, {\n    key: \"testMemoryLeakDetection\",\n    value: (function () {\n      var _testMemoryLeakDetection = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13() {\n        var measurements, iterations, _loop3, i, memoryTrend, averageIncrease, hasMemoryLeak;\n        return _regeneratorRuntime().wrap(function _callee13$(_context16) {\n          while (1) switch (_context16.prev = _context16.next) {\n            case 0:\n              _context16.prev = 0;\n              if (performance.memory) {\n                _context16.next = 3;\n                break;\n              }\n              return _context16.abrupt(\"return\", {\n                success: false,\n                error: 'Performance.memory API not available'\n              });\n            case 3:\n              measurements = [];\n              iterations = 10;\n              _loop3 = /*#__PURE__*/_regeneratorRuntime().mark(function _loop3(i) {\n                var objects;\n                return _regeneratorRuntime().wrap(function _loop3$(_context15) {\n                  while (1) switch (_context15.prev = _context15.next) {\n                    case 0:\n                      // 创建和销毁对象\n                      objects = new Array(1000).fill(0).map(function (_, j) {\n                        return {\n                          id: \"\".concat(i, \"-\").concat(j),\n                          data: new Array(100).fill(\"iteration-\".concat(i))\n                        };\n                      }); // 记录内存使用\n                      measurements.push({\n                        iteration: i,\n                        usedJSHeapSize: performance.memory.usedJSHeapSize\n                      });\n\n                      // 清理对象\n                      objects.length = 0;\n                      _context15.next = 5;\n                      return new Promise(function (resolve) {\n                        return setTimeout(resolve, 50);\n                      });\n                    case 5:\n                    case \"end\":\n                      return _context15.stop();\n                  }\n                }, _loop3);\n              });\n              i = 0;\n            case 7:\n              if (!(i < iterations)) {\n                _context16.next = 12;\n                break;\n              }\n              return _context16.delegateYield(_loop3(i), \"t0\", 9);\n            case 9:\n              i++;\n              _context16.next = 7;\n              break;\n            case 12:\n              // 分析内存趋势\n              memoryTrend = measurements.map(function (m, i) {\n                return i > 0 ? m.usedJSHeapSize - measurements[i - 1].usedJSHeapSize : 0;\n              }).slice(1);\n              averageIncrease = memoryTrend.reduce(function (sum, inc) {\n                return sum + inc;\n              }, 0) / memoryTrend.length;\n              hasMemoryLeak = averageIncrease > 1024 * 1024; // 平均增长超过1MB\n              return _context16.abrupt(\"return\", {\n                success: !hasMemoryLeak,\n                metrics: {\n                  iterations: iterations,\n                  measurements: measurements,\n                  averageIncrease: averageIncrease,\n                  averageIncreaseKB: Math.round(averageIncrease / 1024),\n                  hasMemoryLeak: hasMemoryLeak\n                }\n              });\n            case 18:\n              _context16.prev = 18;\n              _context16.t1 = _context16[\"catch\"](0);\n              return _context16.abrupt(\"return\", {\n                success: false,\n                error: _context16.t1.message\n              });\n            case 21:\n            case \"end\":\n              return _context16.stop();\n          }\n        }, _callee13, null, [[0, 18]]);\n      }));\n      function testMemoryLeakDetection() {\n        return _testMemoryLeakDetection.apply(this, arguments);\n      }\n      return testMemoryLeakDetection;\n    }()\n    /**\n     * 测试API响应时间\n     */\n    )\n  }, {\n    key: \"testAPIResponseTime\",\n    value: (function () {\n      var _testAPIResponseTime = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15() {\n        var startTime, apiCalls, results, endTime, totalTime, averageResponseTime;\n        return _regeneratorRuntime().wrap(function _callee15$(_context18) {\n          while (1) switch (_context18.prev = _context18.next) {\n            case 0:\n              startTime = performance.now();\n              _context18.prev = 1;\n              // 模拟API调用\n              apiCalls = [{\n                url: '/api/test1',\n                delay: 100\n              }, {\n                url: '/api/test2',\n                delay: 200\n              }, {\n                url: '/api/test3',\n                delay: 150\n              }];\n              _context18.next = 5;\n              return Promise.all(apiCalls.map( /*#__PURE__*/function () {\n                var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee14(call) {\n                  var callStart, callEnd;\n                  return _regeneratorRuntime().wrap(function _callee14$(_context17) {\n                    while (1) switch (_context17.prev = _context17.next) {\n                      case 0:\n                        callStart = performance.now(); // 模拟网络延迟\n                        _context17.next = 3;\n                        return new Promise(function (resolve) {\n                          return setTimeout(resolve, call.delay);\n                        });\n                      case 3:\n                        callEnd = performance.now();\n                        return _context17.abrupt(\"return\", {\n                          url: call.url,\n                          responseTime: callEnd - callStart,\n                          success: true\n                        });\n                      case 5:\n                      case \"end\":\n                        return _context17.stop();\n                    }\n                  }, _callee14);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }()));\n            case 5:\n              results = _context18.sent;\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageResponseTime = results.reduce(function (sum, r) {\n                return sum + r.responseTime;\n              }, 0) / results.length;\n              return _context18.abrupt(\"return\", {\n                success: averageResponseTime < 500,\n                // 平均响应时间小于500ms\n                metrics: {\n                  apiCallCount: apiCalls.length,\n                  totalTime: totalTime,\n                  averageResponseTime: averageResponseTime,\n                  results: results\n                }\n              });\n            case 12:\n              _context18.prev = 12;\n              _context18.t0 = _context18[\"catch\"](1);\n              return _context18.abrupt(\"return\", {\n                success: false,\n                error: _context18.t0.message\n              });\n            case 15:\n            case \"end\":\n              return _context18.stop();\n          }\n        }, _callee15, null, [[1, 12]]);\n      }));\n      function testAPIResponseTime() {\n        return _testAPIResponseTime.apply(this, arguments);\n      }\n      return testAPIResponseTime;\n    }()\n    /**\n     * 测试文件上传性能\n     */\n    )\n  }, {\n    key: \"testFileUploadPerformance\",\n    value: (function () {\n      var _testFileUploadPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee16() {\n        var startTime, fileSize, fileContent, mockFile, uploadStart, reader, readPromise, uploadEnd, uploadTime, endTime, totalTime;\n        return _regeneratorRuntime().wrap(function _callee16$(_context19) {\n          while (1) switch (_context19.prev = _context19.next) {\n            case 0:\n              startTime = performance.now();\n              _context19.prev = 1;\n              // 创建模拟文件\n              fileSize = 1024 * 1024; // 1MB\n              fileContent = new Array(fileSize).fill('a').join('');\n              mockFile = new File([fileContent], 'test.txt', {\n                type: 'text/plain'\n              }); // 模拟文件上传处理\n              uploadStart = performance.now(); // 读取文件内容\n              reader = new FileReader();\n              readPromise = new Promise(function (resolve, reject) {\n                reader.onload = function () {\n                  return resolve(reader.result);\n                };\n                reader.onerror = function () {\n                  return reject(reader.error);\n                };\n              });\n              reader.readAsText(mockFile);\n              _context19.next = 11;\n              return readPromise;\n            case 11:\n              uploadEnd = performance.now();\n              uploadTime = uploadEnd - uploadStart;\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              return _context19.abrupt(\"return\", {\n                success: uploadTime < 2000,\n                // 2秒内完成\n                metrics: {\n                  fileSize: fileSize,\n                  fileSizeKB: Math.round(fileSize / 1024),\n                  uploadTime: uploadTime,\n                  totalTime: totalTime,\n                  uploadSpeed: fileSize / (uploadTime / 1000) // 字节/秒\n                }\n              });\n            case 18:\n              _context19.prev = 18;\n              _context19.t0 = _context19[\"catch\"](1);\n              return _context19.abrupt(\"return\", {\n                success: false,\n                error: _context19.t0.message\n              });\n            case 21:\n            case \"end\":\n              return _context19.stop();\n          }\n        }, _callee16, null, [[1, 18]]);\n      }));\n      function testFileUploadPerformance() {\n        return _testFileUploadPerformance.apply(this, arguments);\n      }\n      return testFileUploadPerformance;\n    }()\n    /**\n     * 生成大量HTML内容\n     * @param {Number} lineCount 行数\n     * @returns {String} HTML内容\n     */\n    )\n  }, {\n    key: \"generateLargeHTMLContent\",\n    value: function generateLargeHTMLContent(lineCount) {\n      var content = '';\n      for (var i = 0; i < lineCount; i++) {\n        content += \"<p>\\u8FD9\\u662F\\u7B2C\".concat(i + 1, \"\\u884C\\u5185\\u5BB9\\uFF0C\\u5305\\u542B\\u4E00\\u4E9B<strong>\\u7C97\\u4F53\\u6587\\u672C</strong>\\u548C<em>\\u659C\\u4F53\\u6587\\u672C</em>\\u3002</p>\\n\");\n        if (i % 10 === 0) {\n          content += \"<img src=\\\"data:image/svg+xml;base64,\".concat(btoa('<svg width=\"100\" height=\"50\" xmlns=\"http://www.w3.org/2000/svg\"><rect width=\"100\" height=\"50\" fill=\"lightblue\"/></svg>'), \"\\\" alt=\\\"\\u6D4B\\u8BD5\\u56FE\\u7247\").concat(i, \"\\\" />\\n\");\n        }\n      }\n      return content;\n    }\n  }]);\n}();\nexport default PerformanceTestSuite;", {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_toPrimitive", "toPrimitive", "String", "Number", "<PERSON><PERSON><PERSON><PERSON>", "PerformanceTestSuite", "description", "performanceObserver", "memoryBaseline", "_setup", "_callee", "_callee$", "_context", "console", "log", "performance", "memory", "usedJSHeapSize", "totalJSHeapSize", "jsHeapSizeLimit", "clearMarks", "clearMeasures", "setup", "_teardown", "_callee2", "_callee2$", "_context2", "disconnect", "teardown", "getTestCases", "run", "testImageLoadingPerformance", "bind", "testImageRenderingPerformance", "testTinyMCEInitializationPerformance", "testTinyMCEContentLoadingPerformance", "testTinyMCETypingPerformance", "testMathFormulaRenderingPerformance", "testMathFormulaBatchRendering", "testDataParsingPerformance", "testLargeDatasetProcessing", "testMemoryUsageMonitoring", "testMemoryLeakDetection", "testAPIResponseTime", "testFileUploadPerformance", "_testImageLoadingPerformance", "_callee3", "startTime", "imageCount", "loadPromises", "_loop", "results", "endTime", "successCount", "totalTime", "averageLoadTime", "_callee3$", "_context4", "now", "promise", "_loop$", "_context3", "reject", "img", "Image", "imageStartTime", "onload", "loadTime", "success", "onerror", "src", "concat", "btoa", "Math", "floor", "random", "all", "filter", "reduce", "sum", "metrics", "totalImages", "imagesPerSecond", "t1", "error", "message", "_testImageRenderingPerformance", "_callee4", "container", "renderTime", "_callee4$", "_context5", "document", "createElement", "style", "position", "top", "body", "append<PERSON><PERSON><PERSON>", "width", "height", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "averageRenderTime", "t0", "_testTinyMCEInitializationPerformance", "_callee5", "textarea", "_callee5$", "_context6", "id", "window", "<PERSON><PERSON><PERSON>", "init", "target", "menubar", "toolbar", "editor", "on", "initTime", "destroy", "initializationTime", "setTimeout", "note", "_testTinyMCEContentLoadingPerformance", "_callee6", "largeContent", "div", "_callee6$", "_context7", "generateLargeHTMLContent", "innerHTML", "contentSize", "loadSpeed", "_testTinyMCETypingPerformance", "_callee7", "inputCount", "inputTimes", "_loop2", "averageInputTime", "_callee7$", "_context9", "inputStart", "_loop2$", "_context8", "inputEnd", "time", "maxInputTime", "max", "minInputTime", "min", "_testMathFormulaRenderingPerformance", "_callee8", "formulas", "renderedElements", "_callee8$", "_context10", "formula", "index", "span", "className", "setAttribute", "textContent", "renderAll", "querySelectorAll", "formulaCount", "renderedCount", "_testMathFormulaBatchRendering", "_callee9", "_callee9$", "_context11", "formulasPerSecond", "_testDataParsingPerformance", "_callee10", "dataCount", "testData", "parseStartTime", "parsedData", "parseEndTime", "parseTime", "_callee10$", "_context12", "title", "questionType", "content", "JSON", "stringify", "version", "template_content", "answer", "analysis", "map", "item", "parse", "parsedCount", "parseSpeed", "_testLargeDatasetProcessing", "_callee11", "dataSize", "largeDataset", "filterStartTime", "filtered", "filterTime", "sortStartTime", "sorted", "sortTime", "mapStartTime", "mapped", "mapTime", "_callee11$", "_context13", "Array", "from", "_", "repeat", "metadata", "created", "Date", "toISOString", "tags", "difficulty", "sort", "b", "localeCompare", "_objectSpread", "processed", "contentLength", "filteredCount", "processingSpeed", "_testMemoryUsageMonitoring", "_callee12", "before<PERSON><PERSON><PERSON>", "largeArray", "after<PERSON><PERSON><PERSON>", "memoryIncrease", "_callee12$", "_context14", "fill", "data", "gc", "memoryIncreaseKB", "round", "_testMemoryLeakDetection", "_callee13", "measurements", "iterations", "_loop3", "memoryTrend", "averageIncrease", "hasMemoryLeak", "_callee13$", "_context16", "objects", "_loop3$", "_context15", "j", "iteration", "m", "inc", "averageIncreaseKB", "_testAPIResponseTime", "_callee15", "apiCalls", "averageResponseTime", "_callee15$", "_context18", "url", "delay", "_ref", "_callee14", "callStart", "callEnd", "_callee14$", "_context17", "responseTime", "_x", "apiCallCount", "_testFileUploadPerformance", "_callee16", "fileSize", "fileContent", "mockFile", "uploadStart", "reader", "readPromise", "uploadEnd", "uploadTime", "_callee16$", "_context19", "join", "File", "FileReader", "result", "readAsText", "fileSizeKB", "uploadSpeed", "lineCount"], "sources": ["E:/teachingproject/teaching/web/src/utils/testSuites/performanceTestSuite.js"], "sourcesContent": ["/**\n * 性能测试套件\n * 测试大量图片加载性能、富文本编辑器性能、数据库查询性能、优化性能瓶颈\n */\n\nimport { mathRenderer } from '../mathRenderer'\n\n/**\n * 性能测试套件类\n */\nexport class PerformanceTestSuite {\n  constructor() {\n    this.name = '性能测试套件'\n    this.description = '测试系统各组件的性能表现和资源使用情况'\n    this.performanceObserver = null\n    this.memoryBaseline = null\n  }\n\n  /**\n   * 测试套件初始化\n   */\n  async setup() {\n    console.log('初始化性能测试套件...')\n    \n    // 记录内存基线\n    if (performance.memory) {\n      this.memoryBaseline = {\n        usedJSHeapSize: performance.memory.usedJSHeapSize,\n        totalJSHeapSize: performance.memory.totalJSHeapSize,\n        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit\n      }\n    }\n\n    // 清理性能标记\n    if (performance.clearMarks) {\n      performance.clearMarks()\n    }\n    if (performance.clearMeasures) {\n      performance.clearMeasures()\n    }\n  }\n\n  /**\n   * 测试套件清理\n   */\n  async teardown() {\n    console.log('清理性能测试套件...')\n    \n    // 断开性能观察器\n    if (this.performanceObserver) {\n      this.performanceObserver.disconnect()\n      this.performanceObserver = null\n    }\n  }\n\n  /**\n   * 获取测试用例列表\n   * @returns {Array} 测试用例数组\n   */\n  getTestCases() {\n    return [\n      // 图片加载性能测试\n      {\n        name: 'image_loading_performance',\n        description: '测试大量图片加载性能',\n        run: this.testImageLoadingPerformance.bind(this)\n      },\n      {\n        name: 'image_rendering_performance',\n        description: '测试图片渲染性能',\n        run: this.testImageRenderingPerformance.bind(this)\n      },\n\n      // 富文本编辑器性能测试\n      {\n        name: 'tinymce_initialization_performance',\n        description: '测试TinyMCE初始化性能',\n        run: this.testTinyMCEInitializationPerformance.bind(this)\n      },\n      {\n        name: 'tinymce_content_loading_performance',\n        description: '测试TinyMCE内容加载性能',\n        run: this.testTinyMCEContentLoadingPerformance.bind(this)\n      },\n      {\n        name: 'tinymce_typing_performance',\n        description: '测试TinyMCE输入性能',\n        run: this.testTinyMCETypingPerformance.bind(this)\n      },\n\n      // 数学公式渲染性能测试\n      {\n        name: 'math_formula_rendering_performance',\n        description: '测试数学公式渲染性能',\n        run: this.testMathFormulaRenderingPerformance.bind(this)\n      },\n      {\n        name: 'math_formula_batch_rendering',\n        description: '测试批量数学公式渲染性能',\n        run: this.testMathFormulaBatchRendering.bind(this)\n      },\n\n      // 数据处理性能测试\n      {\n        name: 'data_parsing_performance',\n        description: '测试数据解析性能',\n        run: this.testDataParsingPerformance.bind(this)\n      },\n      {\n        name: 'large_dataset_processing',\n        description: '测试大数据集处理性能',\n        run: this.testLargeDatasetProcessing.bind(this)\n      },\n\n      // 内存使用测试\n      {\n        name: 'memory_usage_monitoring',\n        description: '测试内存使用情况',\n        run: this.testMemoryUsageMonitoring.bind(this)\n      },\n      {\n        name: 'memory_leak_detection',\n        description: '测试内存泄漏检测',\n        run: this.testMemoryLeakDetection.bind(this)\n      },\n\n      // 网络性能测试\n      {\n        name: 'api_response_time',\n        description: '测试API响应时间',\n        run: this.testAPIResponseTime.bind(this)\n      },\n      {\n        name: 'file_upload_performance',\n        description: '测试文件上传性能',\n        run: this.testFileUploadPerformance.bind(this)\n      }\n    ]\n  }\n\n  /**\n   * 测试图片加载性能\n   */\n  async testImageLoadingPerformance() {\n    const startTime = performance.now()\n    const imageCount = 20\n    const loadPromises = []\n\n    try {\n      // 创建多个图片加载任务\n      for (let i = 0; i < imageCount; i++) {\n        const promise = new Promise((resolve, reject) => {\n          const img = new Image()\n          const imageStartTime = performance.now()\n          \n          img.onload = () => {\n            const loadTime = performance.now() - imageStartTime\n            resolve({ success: true, loadTime })\n          }\n          \n          img.onerror = () => {\n            const loadTime = performance.now() - imageStartTime\n            resolve({ success: false, loadTime })\n          }\n          \n          // 使用测试图片URL或生成随机图片\n          img.src = `data:image/svg+xml;base64,${btoa(`<svg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\"><rect width=\"100\" height=\"100\" fill=\"rgb(${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)})\"/><text x=\"50\" y=\"50\" text-anchor=\"middle\" dy=\".3em\" fill=\"white\">${i+1}</text></svg>`)}`\n        })\n        \n        loadPromises.push(promise)\n      }\n\n      const results = await Promise.all(loadPromises)\n      const endTime = performance.now()\n      \n      const successCount = results.filter(r => r.success).length\n      const totalTime = endTime - startTime\n      const averageLoadTime = results.reduce((sum, r) => sum + r.loadTime, 0) / results.length\n\n      return {\n        success: successCount >= imageCount * 0.8, // 80%成功率\n        metrics: {\n          totalImages: imageCount,\n          successCount: successCount,\n          totalTime: totalTime,\n          averageLoadTime: averageLoadTime,\n          imagesPerSecond: imageCount / (totalTime / 1000)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试图片渲染性能\n   */\n  async testImageRenderingPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试容器\n      const container = document.createElement('div')\n      container.style.position = 'absolute'\n      container.style.top = '-9999px'\n      document.body.appendChild(container)\n\n      // 添加多个图片元素\n      const imageCount = 50\n      for (let i = 0; i < imageCount; i++) {\n        const img = document.createElement('img')\n        img.src = `data:image/svg+xml;base64,${btoa(`<svg width=\"50\" height=\"50\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"25\" cy=\"25\" r=\"20\" fill=\"blue\"/></svg>`)}`\n        img.style.width = '50px'\n        img.style.height = '50px'\n        container.appendChild(img)\n      }\n\n      // 强制重排和重绘\n      container.offsetHeight\n      \n      const endTime = performance.now()\n      const renderTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(container)\n\n      return {\n        success: renderTime < 1000, // 1秒内完成\n        metrics: {\n          imageCount: imageCount,\n          renderTime: renderTime,\n          averageRenderTime: renderTime / imageCount\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试TinyMCE初始化性能\n   */\n  async testTinyMCEInitializationPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试文本区域\n      const textarea = document.createElement('textarea')\n      textarea.id = 'performance-test-editor'\n      textarea.style.position = 'absolute'\n      textarea.style.top = '-9999px'\n      document.body.appendChild(textarea)\n\n      // 模拟TinyMCE初始化（如果TinyMCE可用）\n      if (window.tinymce) {\n        await new Promise((resolve, reject) => {\n          window.tinymce.init({\n            target: textarea,\n            height: 300,\n            menubar: false,\n            toolbar: 'bold italic underline',\n            setup: (editor) => {\n              editor.on('init', () => {\n                const endTime = performance.now()\n                const initTime = endTime - startTime\n                \n                // 清理\n                editor.destroy()\n                document.body.removeChild(textarea)\n                \n                resolve({\n                  success: initTime < 3000, // 3秒内初始化\n                  metrics: {\n                    initializationTime: initTime\n                  }\n                })\n              })\n            }\n          })\n        })\n      } else {\n        // TinyMCE不可用，模拟初始化时间\n        await new Promise(resolve => setTimeout(resolve, 100))\n        const endTime = performance.now()\n        \n        document.body.removeChild(textarea)\n        \n        return {\n          success: true,\n          metrics: {\n            initializationTime: endTime - startTime,\n            note: 'TinyMCE not available, simulated initialization'\n          }\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试TinyMCE内容加载性能\n   */\n  async testTinyMCEContentLoadingPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 生成大量测试内容\n      const largeContent = this.generateLargeHTMLContent(1000) // 1000行内容\n      \n      // 模拟内容加载\n      const div = document.createElement('div')\n      div.style.position = 'absolute'\n      div.style.top = '-9999px'\n      document.body.appendChild(div)\n      \n      div.innerHTML = largeContent\n      \n      // 强制渲染\n      div.offsetHeight\n      \n      const endTime = performance.now()\n      const loadTime = endTime - startTime\n      \n      // 清理\n      document.body.removeChild(div)\n      \n      return {\n        success: loadTime < 2000, // 2秒内加载\n        metrics: {\n          contentSize: largeContent.length,\n          loadTime: loadTime,\n          loadSpeed: largeContent.length / loadTime // 字符/毫秒\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试TinyMCE输入性能\n   */\n  async testTinyMCETypingPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 模拟快速输入\n      const inputCount = 100\n      const inputTimes = []\n      \n      for (let i = 0; i < inputCount; i++) {\n        const inputStart = performance.now()\n        \n        // 模拟输入操作\n        await new Promise(resolve => {\n          setTimeout(() => {\n            const inputEnd = performance.now()\n            inputTimes.push(inputEnd - inputStart)\n            resolve()\n          }, 1)\n        })\n      }\n      \n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      const averageInputTime = inputTimes.reduce((sum, time) => sum + time, 0) / inputTimes.length\n      \n      return {\n        success: averageInputTime < 50, // 平均输入时间小于50ms\n        metrics: {\n          inputCount: inputCount,\n          totalTime: totalTime,\n          averageInputTime: averageInputTime,\n          maxInputTime: Math.max(...inputTimes),\n          minInputTime: Math.min(...inputTimes)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试数学公式渲染性能\n   */\n  async testMathFormulaRenderingPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试容器\n      const container = document.createElement('div')\n      container.style.position = 'absolute'\n      container.style.top = '-9999px'\n      document.body.appendChild(container)\n\n      // 添加多个数学公式\n      const formulas = [\n        'E = mc^2',\n        '\\\\int_{-\\\\infty}^{\\\\infty} e^{-x^2} dx = \\\\sqrt{\\\\pi}',\n        '\\\\sum_{n=1}^{\\\\infty} \\\\frac{1}{n^2} = \\\\frac{\\\\pi^2}{6}',\n        '\\\\frac{d}{dx}\\\\int_{a}^{x} f(t) dt = f(x)',\n        '\\\\lim_{x \\\\to \\\\infty} \\\\left(1 + \\\\frac{1}{x}\\\\right)^x = e'\n      ]\n\n      formulas.forEach((formula, index) => {\n        const span = document.createElement('span')\n        span.className = 'math-tex'\n        span.setAttribute('data-math', formula)\n        span.textContent = formula\n        container.appendChild(span)\n      })\n\n      // 渲染数学公式\n      mathRenderer.renderAll(container)\n      \n      // 等待渲染完成\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      const endTime = performance.now()\n      const renderTime = endTime - startTime\n      \n      // 检查渲染结果\n      const renderedElements = container.querySelectorAll('.katex')\n      \n      // 清理\n      document.body.removeChild(container)\n      \n      return {\n        success: renderedElements.length === formulas.length && renderTime < 3000,\n        metrics: {\n          formulaCount: formulas.length,\n          renderedCount: renderedElements.length,\n          renderTime: renderTime,\n          averageRenderTime: renderTime / formulas.length\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试批量数学公式渲染性能\n   */\n  async testMathFormulaBatchRendering() {\n    const startTime = performance.now()\n    \n    try {\n      const container = document.createElement('div')\n      container.style.position = 'absolute'\n      container.style.top = '-9999px'\n      document.body.appendChild(container)\n\n      // 生成大量数学公式\n      const formulaCount = 50\n      for (let i = 0; i < formulaCount; i++) {\n        const span = document.createElement('span')\n        span.className = 'math-tex'\n        span.setAttribute('data-math', `x^${i} + y^${i} = z^${i}`)\n        span.textContent = `x^${i} + y^${i} = z^${i}`\n        container.appendChild(span)\n      }\n\n      // 批量渲染\n      mathRenderer.renderAll(container)\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      const endTime = performance.now()\n      const renderTime = endTime - startTime\n      \n      const renderedElements = container.querySelectorAll('.katex')\n      document.body.removeChild(container)\n      \n      return {\n        success: renderedElements.length >= formulaCount * 0.9 && renderTime < 5000,\n        metrics: {\n          formulaCount: formulaCount,\n          renderedCount: renderedElements.length,\n          renderTime: renderTime,\n          formulasPerSecond: formulaCount / (renderTime / 1000)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试数据解析性能\n   */\n  async testDataParsingPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 生成大量测试数据\n      const dataCount = 1000\n      const testData = []\n      \n      for (let i = 0; i < dataCount; i++) {\n        testData.push({\n          id: `test-${i}`,\n          title: `测试题目${i}`,\n          questionType: (i % 3) + 1,\n          content: JSON.stringify({\n            version: '2.1',\n            template_content: `题目：测试题目${i}`,\n            answer: 'A',\n            analysis: `这是第${i}道题目的解析`\n          })\n        })\n      }\n      \n      // 解析数据\n      const parseStartTime = performance.now()\n      const parsedData = testData.map(item => {\n        try {\n          return JSON.parse(item.content)\n        } catch (error) {\n          return null\n        }\n      }).filter(item => item !== null)\n      \n      const parseEndTime = performance.now()\n      const parseTime = parseEndTime - parseStartTime\n      \n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      \n      return {\n        success: parsedData.length === dataCount && parseTime < 1000,\n        metrics: {\n          dataCount: dataCount,\n          parsedCount: parsedData.length,\n          parseTime: parseTime,\n          totalTime: totalTime,\n          parseSpeed: dataCount / (parseTime / 1000) // 项目/秒\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试大数据集处理性能\n   */\n  async testLargeDatasetProcessing() {\n    const startTime = performance.now()\n    \n    try {\n      // 生成大数据集\n      const dataSize = 10000\n      const largeDataset = Array.from({ length: dataSize }, (_, i) => ({\n        id: i,\n        title: `题目${i}`,\n        content: `这是第${i}道题目的内容，包含一些测试文本和数据。`.repeat(10),\n        metadata: {\n          created: new Date().toISOString(),\n          tags: [`tag${i % 10}`, `category${i % 5}`],\n          difficulty: (i % 3) + 1\n        }\n      }))\n      \n      // 执行各种数据操作\n      const filterStartTime = performance.now()\n      const filtered = largeDataset.filter(item => item.difficulty === 2)\n      const filterTime = performance.now() - filterStartTime\n      \n      const sortStartTime = performance.now()\n      const sorted = [...largeDataset].sort((a, b) => a.title.localeCompare(b.title))\n      const sortTime = performance.now() - sortStartTime\n      \n      const mapStartTime = performance.now()\n      const mapped = largeDataset.map(item => ({\n        ...item,\n        processed: true,\n        contentLength: item.content.length\n      }))\n      const mapTime = performance.now() - mapStartTime\n      \n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      \n      return {\n        success: totalTime < 5000, // 5秒内完成\n        metrics: {\n          dataSize: dataSize,\n          totalTime: totalTime,\n          filterTime: filterTime,\n          sortTime: sortTime,\n          mapTime: mapTime,\n          filteredCount: filtered.length,\n          processingSpeed: dataSize / (totalTime / 1000)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试内存使用情况\n   */\n  async testMemoryUsageMonitoring() {\n    try {\n      if (!performance.memory) {\n        return {\n          success: false,\n          error: 'Performance.memory API not available'\n        }\n      }\n\n      const beforeMemory = {\n        usedJSHeapSize: performance.memory.usedJSHeapSize,\n        totalJSHeapSize: performance.memory.totalJSHeapSize\n      }\n\n      // 创建一些内存使用\n      const largeArray = new Array(100000).fill(0).map((_, i) => ({\n        id: i,\n        data: new Array(100).fill(`data-${i}`)\n      }))\n\n      // 强制垃圾回收（如果可用）\n      if (window.gc) {\n        window.gc()\n      }\n\n      await new Promise(resolve => setTimeout(resolve, 100))\n\n      const afterMemory = {\n        usedJSHeapSize: performance.memory.usedJSHeapSize,\n        totalJSHeapSize: performance.memory.totalJSHeapSize\n      }\n\n      const memoryIncrease = afterMemory.usedJSHeapSize - beforeMemory.usedJSHeapSize\n\n      // 清理\n      largeArray.length = 0\n\n      return {\n        success: memoryIncrease < 50 * 1024 * 1024, // 内存增长小于50MB\n        metrics: {\n          beforeMemory: beforeMemory,\n          afterMemory: afterMemory,\n          memoryIncrease: memoryIncrease,\n          memoryIncreaseKB: Math.round(memoryIncrease / 1024)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试内存泄漏检测\n   */\n  async testMemoryLeakDetection() {\n    try {\n      if (!performance.memory) {\n        return {\n          success: false,\n          error: 'Performance.memory API not available'\n        }\n      }\n\n      const measurements = []\n      const iterations = 10\n\n      for (let i = 0; i < iterations; i++) {\n        // 创建和销毁对象\n        const objects = new Array(1000).fill(0).map((_, j) => ({\n          id: `${i}-${j}`,\n          data: new Array(100).fill(`iteration-${i}`)\n        }))\n\n        // 记录内存使用\n        measurements.push({\n          iteration: i,\n          usedJSHeapSize: performance.memory.usedJSHeapSize\n        })\n\n        // 清理对象\n        objects.length = 0\n\n        await new Promise(resolve => setTimeout(resolve, 50))\n      }\n\n      // 分析内存趋势\n      const memoryTrend = measurements.map((m, i) => \n        i > 0 ? m.usedJSHeapSize - measurements[i-1].usedJSHeapSize : 0\n      ).slice(1)\n\n      const averageIncrease = memoryTrend.reduce((sum, inc) => sum + inc, 0) / memoryTrend.length\n      const hasMemoryLeak = averageIncrease > 1024 * 1024 // 平均增长超过1MB\n\n      return {\n        success: !hasMemoryLeak,\n        metrics: {\n          iterations: iterations,\n          measurements: measurements,\n          averageIncrease: averageIncrease,\n          averageIncreaseKB: Math.round(averageIncrease / 1024),\n          hasMemoryLeak: hasMemoryLeak\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试API响应时间\n   */\n  async testAPIResponseTime() {\n    const startTime = performance.now()\n    \n    try {\n      // 模拟API调用\n      const apiCalls = [\n        { url: '/api/test1', delay: 100 },\n        { url: '/api/test2', delay: 200 },\n        { url: '/api/test3', delay: 150 }\n      ]\n\n      const results = await Promise.all(\n        apiCalls.map(async (call) => {\n          const callStart = performance.now()\n          \n          // 模拟网络延迟\n          await new Promise(resolve => setTimeout(resolve, call.delay))\n          \n          const callEnd = performance.now()\n          return {\n            url: call.url,\n            responseTime: callEnd - callStart,\n            success: true\n          }\n        })\n      )\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length\n\n      return {\n        success: averageResponseTime < 500, // 平均响应时间小于500ms\n        metrics: {\n          apiCallCount: apiCalls.length,\n          totalTime: totalTime,\n          averageResponseTime: averageResponseTime,\n          results: results\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试文件上传性能\n   */\n  async testFileUploadPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建模拟文件\n      const fileSize = 1024 * 1024 // 1MB\n      const fileContent = new Array(fileSize).fill('a').join('')\n      const mockFile = new File([fileContent], 'test.txt', { type: 'text/plain' })\n\n      // 模拟文件上传处理\n      const uploadStart = performance.now()\n      \n      // 读取文件内容\n      const reader = new FileReader()\n      const readPromise = new Promise((resolve, reject) => {\n        reader.onload = () => resolve(reader.result)\n        reader.onerror = () => reject(reader.error)\n      })\n      \n      reader.readAsText(mockFile)\n      await readPromise\n      \n      const uploadEnd = performance.now()\n      const uploadTime = uploadEnd - uploadStart\n      \n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      \n      return {\n        success: uploadTime < 2000, // 2秒内完成\n        metrics: {\n          fileSize: fileSize,\n          fileSizeKB: Math.round(fileSize / 1024),\n          uploadTime: uploadTime,\n          totalTime: totalTime,\n          uploadSpeed: fileSize / (uploadTime / 1000) // 字节/秒\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 生成大量HTML内容\n   * @param {Number} lineCount 行数\n   * @returns {String} HTML内容\n   */\n  generateLargeHTMLContent(lineCount) {\n    let content = ''\n    for (let i = 0; i < lineCount; i++) {\n      content += `<p>这是第${i + 1}行内容，包含一些<strong>粗体文本</strong>和<em>斜体文本</em>。</p>\\n`\n      if (i % 10 === 0) {\n        content += `<img src=\"data:image/svg+xml;base64,${btoa('<svg width=\"100\" height=\"50\" xmlns=\"http://www.w3.org/2000/svg\"><rect width=\"100\" height=\"50\" fill=\"lightblue\"/></svg>')}\" alt=\"测试图片${i}\" />\\n`\n      }\n    }\n    return content\n  }\n}\n\nexport default PerformanceTestSuite\n"], "mappings": ";;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,gBAAAkB,OAAA,CAAAlB,CAAA,KAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,CAAAoB,OAAA,EAAAC,IAAA,WAAAnD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,EAAAqB,IAAA,WAAAnD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAAgD,2BAAA,eAAArD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAkD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA3B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAqB,KAAA,sCAAA/C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAsD,IAAA,eAAAlD,CAAA,CAAAmD,MAAA,GAAA9C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAoD,QAAA,MAAA3C,CAAA,QAAAE,CAAA,GAAA0C,mBAAA,CAAA5C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAmD,MAAA,EAAAnD,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAuD,KAAA,GAAAvD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAmD,MAAA,QAAAjD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAwD,iBAAA,CAAAxD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAmD,MAAA,IAAAnD,CAAA,CAAAyD,MAAA,WAAAzD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAkD,IAAA,GAAArB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAA0B,IAAA,EAAAlD,CAAA,CAAAkD,IAAA,kBAAAjB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAmD,MAAA,YAAAnD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA6B,oBAAA1D,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAsD,MAAA,EAAAjD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAuD,QAAA,qBAAApD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAkD,MAAA,KAAA7D,CAAA,CAAAsD,MAAA,aAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAyD,mBAAA,CAAA1D,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAsD,MAAA,kBAAAnD,CAAA,KAAAH,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,uCAAA3D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA2C,IAAA,IAAArD,CAAA,CAAAF,CAAA,CAAAiE,UAAA,IAAArD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAAgE,IAAA,GAAAlE,CAAA,CAAAmE,OAAA,eAAAjE,CAAA,CAAAsD,MAAA,KAAAtD,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,sCAAA9D,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,cAAAiC,aAAAnE,CAAA,QAAAD,CAAA,KAAAqE,MAAA,EAAApE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAsE,QAAA,GAAArE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAuE,UAAA,GAAAtE,CAAA,KAAAD,CAAA,CAAAwE,QAAA,GAAAvE,CAAA,WAAAwE,UAAA,CAAAC,IAAA,CAAA1E,CAAA,cAAA2E,cAAA1E,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA2E,UAAA,QAAA5E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA2E,UAAA,GAAA5E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAwE,UAAA,MAAAJ,MAAA,aAAApE,CAAA,CAAA4C,OAAA,CAAAuB,YAAA,cAAAS,KAAA,iBAAAnC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAkE,IAAA,SAAAlE,CAAA,OAAA8E,KAAA,CAAA9E,CAAA,CAAA+E,MAAA,SAAAxE,CAAA,OAAAG,CAAA,YAAAwD,KAAA,aAAA3D,CAAA,GAAAP,CAAA,CAAA+E,MAAA,OAAA1E,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA2D,IAAA,CAAAzD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA2D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAxD,CAAA,CAAAwD,IAAA,GAAAxD,CAAA,gBAAAsD,SAAA,CAAAf,OAAA,CAAAjD,CAAA,kCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA4C,WAAA,GAAA9D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAiF,mBAAA,aAAAhF,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAiF,WAAA,WAAAlF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAAgF,WAAA,IAAAhF,CAAA,CAAAmF,IAAA,OAAAnF,CAAA,CAAAoF,IAAA,aAAAnF,CAAA,WAAAE,MAAA,CAAAkF,cAAA,GAAAlF,MAAA,CAAAkF,cAAA,CAAApF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAqF,SAAA,GAAAjD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,aAAAkD,OAAA,EAAAlD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAwF,KAAA,aAAAvF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA+E,OAAA,OAAA7E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAiF,mBAAA,CAAA/E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAsD,IAAA,GAAAd,IAAA,WAAAnD,CAAA,WAAAA,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAsD,IAAA,WAAAtB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAA0F,IAAA,aAAAzF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAwE,IAAA,CAAArE,CAAA,UAAAH,CAAA,CAAAyF,OAAA,aAAAzB,KAAA,WAAAhE,CAAA,CAAA6E,MAAA,SAAA9E,CAAA,GAAAC,CAAA,CAAA0F,GAAA,QAAA3F,CAAA,IAAAD,CAAA,SAAAkE,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAlE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA8E,WAAA,EAAAzD,OAAA,EAAAoD,KAAA,WAAAA,MAAA7E,CAAA,aAAA6F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA3D,CAAA,OAAAsD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA3B,GAAA,GAAA5B,CAAA,OAAAwE,UAAA,CAAA5B,OAAA,CAAA8B,aAAA,IAAA3E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA4F,MAAA,OAAAzF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA4E,KAAA,EAAA5E,CAAA,CAAA6F,KAAA,cAAA7F,CAAA,IAAAD,CAAA,MAAA+F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAAtD,CAAA,QAAAwE,UAAA,IAAAG,UAAA,kBAAA3E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAoE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA7D,CAAA,aAAAuD,IAAA,QAAAvD,CAAA,MAAAE,CAAA,kBAAAgG,OAAA7F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAAgE,IAAA,GAAA7D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAkE,UAAA,CAAAM,MAAA,MAAAxE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA+D,UAAA,CAAAlE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAkE,UAAA,iBAAAlE,CAAA,CAAA2D,MAAA,SAAA6B,MAAA,aAAAxF,CAAA,CAAA2D,MAAA,SAAAwB,IAAA,QAAA/E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA6E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,gBAAAuB,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,cAAAzD,CAAA,aAAA+E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,qBAAAtD,CAAA,QAAAsC,KAAA,qDAAAuC,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,YAAAT,MAAA,WAAAA,OAAA7D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAuE,UAAA,CAAAM,MAAA,MAAA7E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAkE,UAAA,CAAAvE,CAAA,OAAAK,CAAA,CAAA8D,MAAA,SAAAwB,IAAA,IAAAxF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAsF,IAAA,GAAAtF,CAAA,CAAAgE,UAAA,QAAA7D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA2D,MAAA,IAAArE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA6D,UAAA,KAAA7D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAkE,UAAA,cAAAhE,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA8C,MAAA,gBAAAU,IAAA,GAAAxD,CAAA,CAAA6D,UAAA,EAAApC,CAAA,SAAAgE,QAAA,CAAAvF,CAAA,MAAAuF,QAAA,WAAAA,SAAAlG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAsC,IAAA,GAAAjE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAqE,IAAA,QAAApE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA2B,MAAA,kBAAAU,IAAA,yBAAAjE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAkE,IAAA,GAAAlE,CAAA,GAAAmC,CAAA,KAAAiE,MAAA,WAAAA,OAAAnG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAqE,UAAA,KAAAtE,CAAA,cAAAkG,QAAA,CAAAjG,CAAA,CAAA0E,UAAA,EAAA1E,CAAA,CAAAsE,QAAA,GAAAG,aAAA,CAAAzE,CAAA,GAAAiC,CAAA,OAAAkE,KAAA,WAAAC,OAAArG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAmE,MAAA,KAAApE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAA0E,UAAA,kBAAAvE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA8C,aAAA,CAAAzE,CAAA,YAAAK,CAAA,YAAA+C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAvG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAoD,QAAA,KAAA5C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAiE,UAAA,EAAA/D,CAAA,EAAAiE,OAAA,EAAA9D,CAAA,oBAAAmD,MAAA,UAAA3B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAwG,mBAAAnG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA6C,IAAA,GAAAtD,CAAA,CAAAe,CAAA,IAAAyE,OAAA,CAAAvC,OAAA,CAAAlC,CAAA,EAAAoC,IAAA,CAAAlD,CAAA,EAAAK,CAAA;AAAA,SAAAkG,kBAAApG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA0G,SAAA,aAAAjB,OAAA,WAAAvF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAA4G,MAAAvG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,UAAAxG,CAAA,cAAAwG,OAAAxG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,WAAAxG,CAAA,KAAAuG,KAAA;AAAA,SAAAE,gBAAAlG,CAAA,EAAAP,CAAA,UAAAO,CAAA,YAAAP,CAAA,aAAA2D,SAAA;AAAA,SAAA+C,kBAAA/G,CAAA,EAAAE,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAA6E,MAAA,EAAA9E,CAAA,UAAAM,CAAA,GAAAL,CAAA,CAAAD,CAAA,GAAAM,CAAA,CAAAY,UAAA,GAAAZ,CAAA,CAAAY,UAAA,QAAAZ,CAAA,CAAAa,YAAA,kBAAAb,CAAA,KAAAA,CAAA,CAAAc,QAAA,QAAAlB,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAgH,cAAA,CAAAzG,CAAA,CAAA0G,GAAA,GAAA1G,CAAA;AAAA,SAAA2G,aAAAlH,CAAA,EAAAE,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA6G,iBAAA,CAAA/G,CAAA,CAAAI,SAAA,EAAAF,CAAA,GAAAD,CAAA,IAAA8G,iBAAA,CAAA/G,CAAA,EAAAC,CAAA,GAAAE,MAAA,CAAAK,cAAA,CAAAR,CAAA,iBAAAqB,QAAA,SAAArB,CAAA;AAAA,SAAAgH,eAAA/G,CAAA,QAAAS,CAAA,GAAAyG,YAAA,CAAAlH,CAAA,gCAAAgD,OAAA,CAAAvC,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAyG,aAAAlH,CAAA,EAAAC,CAAA,oBAAA+C,OAAA,CAAAhD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAyG,WAAA,kBAAApH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,gCAAA+C,OAAA,CAAAvC,CAAA,UAAAA,CAAA,YAAAsD,SAAA,yEAAA9D,CAAA,GAAAmH,MAAA,GAAAC,MAAA,EAAArH,CAAA;AADA;AACA;AACA;AACA;;AAEA,SAASsH,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA,WAAaC,oBAAoB;EAC/B,SAAAA,qBAAA,EAAc;IAAAV,eAAA,OAAAU,oBAAA;IACZ,IAAI,CAACrC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACsC,WAAW,GAAG,qBAAqB;IACxC,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;;EAEA;AACF;AACA;EAFE,OAAAT,YAAA,CAAAM,oBAAA;IAAAP,GAAA;IAAAxG,KAAA;MAAA,IAAAmH,MAAA,GAAAnB,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAyC,QAAA;QAAA,OAAA9H,mBAAA,GAAAuB,IAAA,UAAAwG,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAlC,IAAA,GAAAkC,QAAA,CAAA7D,IAAA;YAAA;cACE8D,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;;cAE3B;cACA,IAAIC,WAAW,CAACC,MAAM,EAAE;gBACtB,IAAI,CAACR,cAAc,GAAG;kBACpBS,cAAc,EAAEF,WAAW,CAACC,MAAM,CAACC,cAAc;kBACjDC,eAAe,EAAEH,WAAW,CAACC,MAAM,CAACE,eAAe;kBACnDC,eAAe,EAAEJ,WAAW,CAACC,MAAM,CAACG;gBACtC,CAAC;cACH;;cAEA;cACA,IAAIJ,WAAW,CAACK,UAAU,EAAE;gBAC1BL,WAAW,CAACK,UAAU,CAAC,CAAC;cAC1B;cACA,IAAIL,WAAW,CAACM,aAAa,EAAE;gBAC7BN,WAAW,CAACM,aAAa,CAAC,CAAC;cAC7B;YAAC;YAAA;cAAA,OAAAT,QAAA,CAAA/B,IAAA;UAAA;QAAA,GAAA6B,OAAA;MAAA,CACF;MAAA,SAAAY,MAAA;QAAA,OAAAb,MAAA,CAAAjB,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA+B,KAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAxB,GAAA;IAAAxG,KAAA;MAAA,IAAAiI,SAAA,GAAAjC,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAuD,SAAA;QAAA,OAAA5I,mBAAA,GAAAuB,IAAA,UAAAsH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA3E,IAAA;YAAA;cACE8D,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;cAE1B;cACA,IAAI,IAAI,CAACP,mBAAmB,EAAE;gBAC5B,IAAI,CAACA,mBAAmB,CAACoB,UAAU,CAAC,CAAC;gBACrC,IAAI,CAACpB,mBAAmB,GAAG,IAAI;cACjC;YAAC;YAAA;cAAA,OAAAmB,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA,CACF;MAAA,SAAAI,SAAA;QAAA,OAAAL,SAAA,CAAA/B,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAqC,QAAA;IAAA;IAED;AACF;AACA;AACA;IAHE;EAAA;IAAA9B,GAAA;IAAAxG,KAAA,EAIA,SAAAuI,aAAA,EAAe;MACb,OAAO;MACL;MACA;QACE7D,IAAI,EAAE,2BAA2B;QACjCsC,WAAW,EAAE,YAAY;QACzBwB,GAAG,EAAE,IAAI,CAACC,2BAA2B,CAACC,IAAI,CAAC,IAAI;MACjD,CAAC,EACD;QACEhE,IAAI,EAAE,6BAA6B;QACnCsC,WAAW,EAAE,UAAU;QACvBwB,GAAG,EAAE,IAAI,CAACG,6BAA6B,CAACD,IAAI,CAAC,IAAI;MACnD,CAAC;MAED;MACA;QACEhE,IAAI,EAAE,oCAAoC;QAC1CsC,WAAW,EAAE,gBAAgB;QAC7BwB,GAAG,EAAE,IAAI,CAACI,oCAAoC,CAACF,IAAI,CAAC,IAAI;MAC1D,CAAC,EACD;QACEhE,IAAI,EAAE,qCAAqC;QAC3CsC,WAAW,EAAE,iBAAiB;QAC9BwB,GAAG,EAAE,IAAI,CAACK,oCAAoC,CAACH,IAAI,CAAC,IAAI;MAC1D,CAAC,EACD;QACEhE,IAAI,EAAE,4BAA4B;QAClCsC,WAAW,EAAE,eAAe;QAC5BwB,GAAG,EAAE,IAAI,CAACM,4BAA4B,CAACJ,IAAI,CAAC,IAAI;MAClD,CAAC;MAED;MACA;QACEhE,IAAI,EAAE,oCAAoC;QAC1CsC,WAAW,EAAE,YAAY;QACzBwB,GAAG,EAAE,IAAI,CAACO,mCAAmC,CAACL,IAAI,CAAC,IAAI;MACzD,CAAC,EACD;QACEhE,IAAI,EAAE,8BAA8B;QACpCsC,WAAW,EAAE,cAAc;QAC3BwB,GAAG,EAAE,IAAI,CAACQ,6BAA6B,CAACN,IAAI,CAAC,IAAI;MACnD,CAAC;MAED;MACA;QACEhE,IAAI,EAAE,0BAA0B;QAChCsC,WAAW,EAAE,UAAU;QACvBwB,GAAG,EAAE,IAAI,CAACS,0BAA0B,CAACP,IAAI,CAAC,IAAI;MAChD,CAAC,EACD;QACEhE,IAAI,EAAE,0BAA0B;QAChCsC,WAAW,EAAE,YAAY;QACzBwB,GAAG,EAAE,IAAI,CAACU,0BAA0B,CAACR,IAAI,CAAC,IAAI;MAChD,CAAC;MAED;MACA;QACEhE,IAAI,EAAE,yBAAyB;QAC/BsC,WAAW,EAAE,UAAU;QACvBwB,GAAG,EAAE,IAAI,CAACW,yBAAyB,CAACT,IAAI,CAAC,IAAI;MAC/C,CAAC,EACD;QACEhE,IAAI,EAAE,uBAAuB;QAC7BsC,WAAW,EAAE,UAAU;QACvBwB,GAAG,EAAE,IAAI,CAACY,uBAAuB,CAACV,IAAI,CAAC,IAAI;MAC7C,CAAC;MAED;MACA;QACEhE,IAAI,EAAE,mBAAmB;QACzBsC,WAAW,EAAE,WAAW;QACxBwB,GAAG,EAAE,IAAI,CAACa,mBAAmB,CAACX,IAAI,CAAC,IAAI;MACzC,CAAC,EACD;QACEhE,IAAI,EAAE,yBAAyB;QAC/BsC,WAAW,EAAE,UAAU;QACvBwB,GAAG,EAAE,IAAI,CAACc,yBAAyB,CAACZ,IAAI,CAAC,IAAI;MAC/C,CAAC,CACF;IACH;;IAEA;AACF;AACA;EAFE;IAAAlC,GAAA;IAAAxG,KAAA;MAAA,IAAAuJ,4BAAA,GAAAvD,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAA6E,SAAA;QAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,KAAA,EAAA3J,CAAA,EAAA4J,OAAA,EAAAC,OAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,eAAA;QAAA,OAAA3K,mBAAA,GAAAuB,IAAA,UAAAqJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,IAAA,GAAA+E,SAAA,CAAA1G,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC7BV,UAAU,GAAG,EAAE;cACfC,YAAY,GAAG,EAAE;cAAAQ,SAAA,CAAA/E,IAAA;cAAAwE,KAAA,gBAAAtK,mBAAA,GAAAqF,IAAA,UAAAiF,MAAA3J,CAAA;gBAAA,IAAAoK,OAAA;gBAAA,OAAA/K,mBAAA,GAAAuB,IAAA,UAAAyJ,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAA9G,IAAA;oBAAA;sBAKb4G,OAAO,GAAG,IAAIrF,OAAO,CAAC,UAACvC,OAAO,EAAE+H,MAAM,EAAK;wBAC/C,IAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;wBACvB,IAAMC,cAAc,GAAGlD,WAAW,CAAC2C,GAAG,CAAC,CAAC;wBAExCK,GAAG,CAACG,MAAM,GAAG,YAAM;0BACjB,IAAMC,QAAQ,GAAGpD,WAAW,CAAC2C,GAAG,CAAC,CAAC,GAAGO,cAAc;0BACnDlI,OAAO,CAAC;4BAAEqI,OAAO,EAAE,IAAI;4BAAED,QAAQ,EAARA;0BAAS,CAAC,CAAC;wBACtC,CAAC;wBAEDJ,GAAG,CAACM,OAAO,GAAG,YAAM;0BAClB,IAAMF,QAAQ,GAAGpD,WAAW,CAAC2C,GAAG,CAAC,CAAC,GAAGO,cAAc;0BACnDlI,OAAO,CAAC;4BAAEqI,OAAO,EAAE,KAAK;4BAAED,QAAQ,EAARA;0BAAS,CAAC,CAAC;wBACvC,CAAC;;wBAED;wBACAJ,GAAG,CAACO,GAAG,gCAAAC,MAAA,CAAgCC,IAAI,yHAAAD,MAAA,CAA8GE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAC,GAAG,CAAC,OAAAJ,MAAA,CAAIE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAC,GAAG,CAAC,OAAAJ,MAAA,CAAIE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAC,GAAG,CAAC,qFAAAJ,MAAA,CAAuEhL,CAAC,GAAC,CAAC,kBAAe,CAAC,CAAE;sBACtV,CAAC,CAAC;sBAEF0J,YAAY,CAAC1F,IAAI,CAACoG,OAAO,CAAC;oBAAA;oBAAA;sBAAA,OAAAE,SAAA,CAAAhF,IAAA;kBAAA;gBAAA,GAAAqE,KAAA;cAAA;cAnBnB3J,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGyJ,UAAU;gBAAAS,SAAA,CAAA1G,IAAA;gBAAA;cAAA;cAAA,OAAA0G,SAAA,CAAArE,aAAA,CAAA8D,KAAA,CAAA3J,CAAA;YAAA;cAAEA,CAAC,EAAE;cAAAkK,SAAA,CAAA1G,IAAA;cAAA;YAAA;cAAA0G,SAAA,CAAA1G,IAAA;cAAA,OAsBbuB,OAAO,CAACsG,GAAG,CAAC3B,YAAY,CAAC;YAAA;cAAzCE,OAAO,GAAAM,SAAA,CAAAjH,IAAA;cACP4G,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAE3BL,YAAY,GAAGF,OAAO,CAAC0B,MAAM,CAAC,UAAA9L,CAAC;gBAAA,OAAIA,CAAC,CAACqL,OAAO;cAAA,EAAC,CAACxG,MAAM;cACpD0F,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAC/BQ,eAAe,GAAGJ,OAAO,CAAC2B,MAAM,CAAC,UAACC,GAAG,EAAEhM,CAAC;gBAAA,OAAKgM,GAAG,GAAGhM,CAAC,CAACoL,QAAQ;cAAA,GAAE,CAAC,CAAC,GAAGhB,OAAO,CAACvF,MAAM;cAAA,OAAA6F,SAAA,CAAA9G,MAAA,WAEjF;gBACLyH,OAAO,EAAEf,YAAY,IAAIL,UAAU,GAAG,GAAG;gBAAE;gBAC3CgC,OAAO,EAAE;kBACPC,WAAW,EAAEjC,UAAU;kBACvBK,YAAY,EAAEA,YAAY;kBAC1BC,SAAS,EAAEA,SAAS;kBACpBC,eAAe,EAAEA,eAAe;kBAChC2B,eAAe,EAAElC,UAAU,IAAIM,SAAS,GAAG,IAAI;gBACjD;cACF,CAAC;YAAA;cAAAG,SAAA,CAAA/E,IAAA;cAAA+E,SAAA,CAAA0B,EAAA,GAAA1B,SAAA;cAAA,OAAAA,SAAA,CAAA9G,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAE3B,SAAA,CAAA0B,EAAA,CAAME;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA5B,SAAA,CAAA5E,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA,CAElD;MAAA,SAAAf,4BAAA;QAAA,OAAAc,4BAAA,CAAArD,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAwC,2BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAjC,GAAA;IAAAxG,KAAA;MAAA,IAAAgM,8BAAA,GAAAhG,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAsH,SAAA;QAAA,IAAAxC,SAAA,EAAAyC,SAAA,EAAAxC,UAAA,EAAAzJ,CAAA,EAAAwK,GAAA,EAAAX,OAAA,EAAAqC,UAAA;QAAA,OAAA7M,mBAAA,GAAAuB,IAAA,UAAAuL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjH,IAAA,GAAAiH,SAAA,CAAA5I,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAiC,SAAA,CAAAjH,IAAA;cAGjC;cACM8G,SAAS,GAAGI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CL,SAAS,CAACM,KAAK,CAACC,QAAQ,GAAG,UAAU;cACrCP,SAAS,CAACM,KAAK,CAACE,GAAG,GAAG,SAAS;cAC/BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACV,SAAS,CAAC;;cAEpC;cACMxC,UAAU,GAAG,EAAE;cACrB,KAASzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyJ,UAAU,EAAEzJ,CAAC,EAAE,EAAE;gBAC7BwK,GAAG,GAAG6B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBACzC9B,GAAG,CAACO,GAAG,gCAAAC,MAAA,CAAgCC,IAAI,kIAAoH,CAAC,CAAE;gBAClKT,GAAG,CAAC+B,KAAK,CAACK,KAAK,GAAG,MAAM;gBACxBpC,GAAG,CAAC+B,KAAK,CAACM,MAAM,GAAG,MAAM;gBACzBZ,SAAS,CAACU,WAAW,CAACnC,GAAG,CAAC;cAC5B;;cAEA;cACAyB,SAAS,CAACa,YAAY;cAEhBjD,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3B+B,UAAU,GAAGrC,OAAO,GAAGL,SAAS,EAEtC;cACA6C,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACd,SAAS,CAAC;cAAA,OAAAG,SAAA,CAAAhJ,MAAA,WAE7B;gBACLyH,OAAO,EAAEqB,UAAU,GAAG,IAAI;gBAAE;gBAC5BT,OAAO,EAAE;kBACPhC,UAAU,EAAEA,UAAU;kBACtByC,UAAU,EAAEA,UAAU;kBACtBc,iBAAiB,EAAEd,UAAU,GAAGzC;gBAClC;cACF,CAAC;YAAA;cAAA2C,SAAA,CAAAjH,IAAA;cAAAiH,SAAA,CAAAa,EAAA,GAAAb,SAAA;cAAA,OAAAA,SAAA,CAAAhJ,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEO,SAAA,CAAAa,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAM,SAAA,CAAA9G,IAAA;UAAA;QAAA,GAAA0G,QAAA;MAAA,CAElD;MAAA,SAAAtD,8BAAA;QAAA,OAAAqD,8BAAA,CAAA9F,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA0C,6BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAnC,GAAA;IAAAxG,KAAA;MAAA,IAAAmN,qCAAA,GAAAnH,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAyI,SAAA;QAAA,IAAA3D,SAAA,EAAA4D,QAAA,EAAAvD,OAAA;QAAA,OAAAxK,mBAAA,GAAAuB,IAAA,UAAAyM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAA9J,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAmD,SAAA,CAAAnI,IAAA;cAGjC;cACMiI,QAAQ,GAAGf,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;cACnDc,QAAQ,CAACG,EAAE,GAAG,yBAAyB;cACvCH,QAAQ,CAACb,KAAK,CAACC,QAAQ,GAAG,UAAU;cACpCY,QAAQ,CAACb,KAAK,CAACE,GAAG,GAAG,SAAS;cAC9BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACS,QAAQ,CAAC;;cAEnC;cAAA,KACII,MAAM,CAACC,OAAO;gBAAAH,SAAA,CAAA9J,IAAA;gBAAA;cAAA;cAAA8J,SAAA,CAAA9J,IAAA;cAAA,OACV,IAAIuB,OAAO,CAAC,UAACvC,OAAO,EAAE+H,MAAM,EAAK;gBACrCiD,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC;kBAClBC,MAAM,EAAEP,QAAQ;kBAChBP,MAAM,EAAE,GAAG;kBACXe,OAAO,EAAE,KAAK;kBACdC,OAAO,EAAE,uBAAuB;kBAChC9F,KAAK,EAAE,SAAAA,MAAC+F,MAAM,EAAK;oBACjBA,MAAM,CAACC,EAAE,CAAC,MAAM,EAAE,YAAM;sBACtB,IAAMlE,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;sBACjC,IAAM6D,QAAQ,GAAGnE,OAAO,GAAGL,SAAS;;sBAEpC;sBACAsE,MAAM,CAACG,OAAO,CAAC,CAAC;sBAChB5B,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACK,QAAQ,CAAC;sBAEnC5K,OAAO,CAAC;wBACNqI,OAAO,EAAEmD,QAAQ,GAAG,IAAI;wBAAE;wBAC1BvC,OAAO,EAAE;0BACPyC,kBAAkB,EAAEF;wBACtB;sBACF,CAAC,CAAC;oBACJ,CAAC,CAAC;kBACJ;gBACF,CAAC,CAAC;cACJ,CAAC,CAAC;YAAA;cAAAV,SAAA,CAAA9J,IAAA;cAAA;YAAA;cAAA8J,SAAA,CAAA9J,IAAA;cAAA,OAGI,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAE,GAAG,CAAC;cAAA,EAAC;YAAA;cAChDqH,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAEjCkC,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACK,QAAQ,CAAC;cAAA,OAAAE,SAAA,CAAAlK,MAAA,WAE5B;gBACLyH,OAAO,EAAE,IAAI;gBACbY,OAAO,EAAE;kBACPyC,kBAAkB,EAAErE,OAAO,GAAGL,SAAS;kBACvC4E,IAAI,EAAE;gBACR;cACF,CAAC;YAAA;cAAAd,SAAA,CAAA9J,IAAA;cAAA;YAAA;cAAA8J,SAAA,CAAAnI,IAAA;cAAAmI,SAAA,CAAAL,EAAA,GAAAK,SAAA;cAAA,OAAAA,SAAA,CAAAlK,MAAA,WAGI;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEyB,SAAA,CAAAL,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAwB,SAAA,CAAAhI,IAAA;UAAA;QAAA,GAAA6H,QAAA;MAAA,CAElD;MAAA,SAAAxE,qCAAA;QAAA,OAAAuE,qCAAA,CAAAjH,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA2C,oCAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAApC,GAAA;IAAAxG,KAAA;MAAA,IAAAsO,qCAAA,GAAAtI,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAA4J,SAAA;QAAA,IAAA9E,SAAA,EAAA+E,YAAA,EAAAC,GAAA,EAAA3E,OAAA,EAAAe,QAAA;QAAA,OAAAvL,mBAAA,GAAAuB,IAAA,UAAA6N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvJ,IAAA,GAAAuJ,SAAA,CAAAlL,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAuE,SAAA,CAAAvJ,IAAA;cAGjC;cACMoJ,YAAY,GAAG,IAAI,CAACI,wBAAwB,CAAC,IAAI,CAAC,EAAC;cAEzD;cACMH,GAAG,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACzCkC,GAAG,CAACjC,KAAK,CAACC,QAAQ,GAAG,UAAU;cAC/BgC,GAAG,CAACjC,KAAK,CAACE,GAAG,GAAG,SAAS;cACzBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAAC6B,GAAG,CAAC;cAE9BA,GAAG,CAACI,SAAS,GAAGL,YAAY;;cAE5B;cACAC,GAAG,CAAC1B,YAAY;cAEVjD,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BS,QAAQ,GAAGf,OAAO,GAAGL,SAAS,EAEpC;cACA6C,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACyB,GAAG,CAAC;cAAA,OAAAE,SAAA,CAAAtL,MAAA,WAEvB;gBACLyH,OAAO,EAAED,QAAQ,GAAG,IAAI;gBAAE;gBAC1Ba,OAAO,EAAE;kBACPoD,WAAW,EAAEN,YAAY,CAAClK,MAAM;kBAChCuG,QAAQ,EAAEA,QAAQ;kBAClBkE,SAAS,EAAEP,YAAY,CAAClK,MAAM,GAAGuG,QAAQ,CAAC;gBAC5C;cACF,CAAC;YAAA;cAAA8D,SAAA,CAAAvJ,IAAA;cAAAuJ,SAAA,CAAAzB,EAAA,GAAAyB,SAAA;cAAA,OAAAA,SAAA,CAAAtL,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAE6C,SAAA,CAAAzB,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA4C,SAAA,CAAApJ,IAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA,CAElD;MAAA,SAAA1F,qCAAA;QAAA,OAAAyF,qCAAA,CAAApI,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA4C,oCAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAArC,GAAA;IAAAxG,KAAA;MAAA,IAAAgP,6BAAA,GAAAhJ,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAsK,SAAA;QAAA,IAAAxF,SAAA,EAAAyF,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAnP,CAAA,EAAA6J,OAAA,EAAAE,SAAA,EAAAqF,gBAAA;QAAA,OAAA/P,mBAAA,GAAAuB,IAAA,UAAAyO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnK,IAAA,GAAAmK,SAAA,CAAA9L,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAmF,SAAA,CAAAnK,IAAA;cAGjC;cACM8J,UAAU,GAAG,GAAG;cAChBC,UAAU,GAAG,EAAE;cAAAC,MAAA,gBAAA9P,mBAAA,GAAAqF,IAAA,UAAAyK,OAAA;gBAAA,IAAAI,UAAA;gBAAA,OAAAlQ,mBAAA,GAAAuB,IAAA,UAAA4O,QAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAtK,IAAA,GAAAsK,SAAA,CAAAjM,IAAA;oBAAA;sBAGb+L,UAAU,GAAG/H,WAAW,CAAC2C,GAAG,CAAC,CAAC,EAEpC;sBAAAsF,SAAA,CAAAjM,IAAA;sBAAA,OACM,IAAIuB,OAAO,CAAC,UAAAvC,OAAO,EAAI;wBAC3B2L,UAAU,CAAC,YAAM;0BACf,IAAMuB,QAAQ,GAAGlI,WAAW,CAAC2C,GAAG,CAAC,CAAC;0BAClC+E,UAAU,CAAClL,IAAI,CAAC0L,QAAQ,GAAGH,UAAU,CAAC;0BACtC/M,OAAO,CAAC,CAAC;wBACX,CAAC,EAAE,CAAC,CAAC;sBACP,CAAC,CAAC;oBAAA;oBAAA;sBAAA,OAAAiN,SAAA,CAAAnK,IAAA;kBAAA;gBAAA,GAAA6J,MAAA;cAAA;cAVKnP,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGiP,UAAU;gBAAAK,SAAA,CAAA9L,IAAA;gBAAA;cAAA;cAAA,OAAA8L,SAAA,CAAAzJ,aAAA,CAAAsJ,MAAA;YAAA;cAAEnP,CAAC,EAAE;cAAAsP,SAAA,CAAA9L,IAAA;cAAA;YAAA;cAa7BqG,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BJ,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAC/B4F,gBAAgB,GAAGF,UAAU,CAAC3D,MAAM,CAAC,UAACC,GAAG,EAAEmE,IAAI;gBAAA,OAAKnE,GAAG,GAAGmE,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGT,UAAU,CAAC7K,MAAM;cAAA,OAAAiL,SAAA,CAAAlM,MAAA,WAErF;gBACLyH,OAAO,EAAEuE,gBAAgB,GAAG,EAAE;gBAAE;gBAChC3D,OAAO,EAAE;kBACPwD,UAAU,EAAEA,UAAU;kBACtBlF,SAAS,EAAEA,SAAS;kBACpBqF,gBAAgB,EAAEA,gBAAgB;kBAClCQ,YAAY,EAAE1E,IAAI,CAAC2E,GAAG,CAAA5J,KAAA,CAARiF,IAAI,EAAQgE,UAAU,CAAC;kBACrCY,YAAY,EAAE5E,IAAI,CAAC6E,GAAG,CAAA9J,KAAA,CAARiF,IAAI,EAAQgE,UAAU;gBACtC;cACF,CAAC;YAAA;cAAAI,SAAA,CAAAnK,IAAA;cAAAmK,SAAA,CAAA1D,EAAA,GAAA0D,SAAA;cAAA,OAAAA,SAAA,CAAAlM,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEyD,SAAA,CAAA1D,EAAA,CAAME;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAwD,SAAA,CAAAhK,IAAA;UAAA;QAAA,GAAA0J,QAAA;MAAA,CAElD;MAAA,SAAAnG,6BAAA;QAAA,OAAAkG,6BAAA,CAAA9I,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA6C,4BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAtC,GAAA;IAAAxG,KAAA;MAAA,IAAAiQ,oCAAA,GAAAjK,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAuL,SAAA;QAAA,IAAAzG,SAAA,EAAAyC,SAAA,EAAAiE,QAAA,EAAArG,OAAA,EAAAqC,UAAA,EAAAiE,gBAAA;QAAA,OAAA9Q,mBAAA,GAAAuB,IAAA,UAAAwP,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlL,IAAA,GAAAkL,UAAA,CAAA7M,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAkG,UAAA,CAAAlL,IAAA;cAGjC;cACM8G,SAAS,GAAGI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CL,SAAS,CAACM,KAAK,CAACC,QAAQ,GAAG,UAAU;cACrCP,SAAS,CAACM,KAAK,CAACE,GAAG,GAAG,SAAS;cAC/BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACV,SAAS,CAAC;;cAEpC;cACMiE,QAAQ,GAAG,CACf,UAAU,EACV,uDAAuD,EACvD,0DAA0D,EAC1D,2CAA2C,EAC3C,8DAA8D,CAC/D;cAEDA,QAAQ,CAAC/N,OAAO,CAAC,UAACmO,OAAO,EAAEC,KAAK,EAAK;gBACnC,IAAMC,IAAI,GAAGnE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;gBAC3CkE,IAAI,CAACC,SAAS,GAAG,UAAU;gBAC3BD,IAAI,CAACE,YAAY,CAAC,WAAW,EAAEJ,OAAO,CAAC;gBACvCE,IAAI,CAACG,WAAW,GAAGL,OAAO;gBAC1BrE,SAAS,CAACU,WAAW,CAAC6D,IAAI,CAAC;cAC7B,CAAC,CAAC;;cAEF;cACA3J,YAAY,CAAC+J,SAAS,CAAC3E,SAAS,CAAC;;cAEjC;cAAAoE,UAAA,CAAA7M,IAAA;cAAA,OACM,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAE,IAAI,CAAC;cAAA,EAAC;YAAA;cAEjDqH,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3B+B,UAAU,GAAGrC,OAAO,GAAGL,SAAS,EAEtC;cACM2G,gBAAgB,GAAGlE,SAAS,CAAC4E,gBAAgB,CAAC,QAAQ,CAAC,EAE7D;cACAxE,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACd,SAAS,CAAC;cAAA,OAAAoE,UAAA,CAAAjN,MAAA,WAE7B;gBACLyH,OAAO,EAAEsF,gBAAgB,CAAC9L,MAAM,KAAK6L,QAAQ,CAAC7L,MAAM,IAAI6H,UAAU,GAAG,IAAI;gBACzET,OAAO,EAAE;kBACPqF,YAAY,EAAEZ,QAAQ,CAAC7L,MAAM;kBAC7B0M,aAAa,EAAEZ,gBAAgB,CAAC9L,MAAM;kBACtC6H,UAAU,EAAEA,UAAU;kBACtBc,iBAAiB,EAAEd,UAAU,GAAGgE,QAAQ,CAAC7L;gBAC3C;cACF,CAAC;YAAA;cAAAgM,UAAA,CAAAlL,IAAA;cAAAkL,UAAA,CAAApD,EAAA,GAAAoD,UAAA;cAAA,OAAAA,UAAA,CAAAjN,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEwE,UAAA,CAAApD,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAuE,UAAA,CAAA/K,IAAA;UAAA;QAAA,GAAA2K,QAAA;MAAA,CAElD;MAAA,SAAAnH,oCAAA;QAAA,OAAAkH,oCAAA,CAAA/J,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA8C,mCAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAvC,GAAA;IAAAxG,KAAA;MAAA,IAAAiR,8BAAA,GAAAjL,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAuM,SAAA;QAAA,IAAAzH,SAAA,EAAAyC,SAAA,EAAA6E,YAAA,EAAA9Q,CAAA,EAAAwQ,IAAA,EAAA3G,OAAA,EAAAqC,UAAA,EAAAiE,gBAAA;QAAA,OAAA9Q,mBAAA,GAAAuB,IAAA,UAAAsQ,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhM,IAAA,GAAAgM,UAAA,CAAA3N,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAgH,UAAA,CAAAhM,IAAA;cAG3B8G,SAAS,GAAGI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CL,SAAS,CAACM,KAAK,CAACC,QAAQ,GAAG,UAAU;cACrCP,SAAS,CAACM,KAAK,CAACE,GAAG,GAAG,SAAS;cAC/BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACV,SAAS,CAAC;;cAEpC;cACM6E,YAAY,GAAG,EAAE;cACvB,KAAS9Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,YAAY,EAAE9Q,CAAC,EAAE,EAAE;gBAC/BwQ,IAAI,GAAGnE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;gBAC3CkE,IAAI,CAACC,SAAS,GAAG,UAAU;gBAC3BD,IAAI,CAACE,YAAY,CAAC,WAAW,OAAA1F,MAAA,CAAOhL,CAAC,WAAAgL,MAAA,CAAQhL,CAAC,WAAAgL,MAAA,CAAQhL,CAAC,CAAE,CAAC;gBAC1DwQ,IAAI,CAACG,WAAW,QAAA3F,MAAA,CAAQhL,CAAC,WAAAgL,MAAA,CAAQhL,CAAC,WAAAgL,MAAA,CAAQhL,CAAC,CAAE;gBAC7CiM,SAAS,CAACU,WAAW,CAAC6D,IAAI,CAAC;cAC7B;;cAEA;cACA3J,YAAY,CAAC+J,SAAS,CAAC3E,SAAS,CAAC;cAAAkF,UAAA,CAAA3N,IAAA;cAAA,OAC3B,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAE,IAAI,CAAC;cAAA,EAAC;YAAA;cAEjDqH,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3B+B,UAAU,GAAGrC,OAAO,GAAGL,SAAS;cAEhC2G,gBAAgB,GAAGlE,SAAS,CAAC4E,gBAAgB,CAAC,QAAQ,CAAC;cAC7DxE,QAAQ,CAACK,IAAI,CAACK,WAAW,CAACd,SAAS,CAAC;cAAA,OAAAkF,UAAA,CAAA/N,MAAA,WAE7B;gBACLyH,OAAO,EAAEsF,gBAAgB,CAAC9L,MAAM,IAAIyM,YAAY,GAAG,GAAG,IAAI5E,UAAU,GAAG,IAAI;gBAC3ET,OAAO,EAAE;kBACPqF,YAAY,EAAEA,YAAY;kBAC1BC,aAAa,EAAEZ,gBAAgB,CAAC9L,MAAM;kBACtC6H,UAAU,EAAEA,UAAU;kBACtBkF,iBAAiB,EAAEN,YAAY,IAAI5E,UAAU,GAAG,IAAI;gBACtD;cACF,CAAC;YAAA;cAAAiF,UAAA,CAAAhM,IAAA;cAAAgM,UAAA,CAAAlE,EAAA,GAAAkE,UAAA;cAAA,OAAAA,UAAA,CAAA/N,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEsF,UAAA,CAAAlE,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAqF,UAAA,CAAA7L,IAAA;UAAA;QAAA,GAAA2L,QAAA;MAAA,CAElD;MAAA,SAAAlI,8BAAA;QAAA,OAAAiI,8BAAA,CAAA/K,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA+C,6BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAxC,GAAA;IAAAxG,KAAA;MAAA,IAAAsR,2BAAA,GAAAtL,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAA4M,UAAA;QAAA,IAAA9H,SAAA,EAAA+H,SAAA,EAAAC,QAAA,EAAAxR,CAAA,EAAAyR,cAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,SAAA,EAAA/H,OAAA,EAAAE,SAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAAiR,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3M,IAAA,GAAA2M,UAAA,CAAAtO,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAA2H,UAAA,CAAA3M,IAAA;cAGjC;cACMoM,SAAS,GAAG,IAAI;cAChBC,QAAQ,GAAG,EAAE;cAEnB,KAASxR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuR,SAAS,EAAEvR,CAAC,EAAE,EAAE;gBAClCwR,QAAQ,CAACxN,IAAI,CAAC;kBACZuJ,EAAE,UAAAvC,MAAA,CAAUhL,CAAC,CAAE;kBACf+R,KAAK,6BAAA/G,MAAA,CAAShL,CAAC,CAAE;kBACjBgS,YAAY,EAAGhS,CAAC,GAAG,CAAC,GAAI,CAAC;kBACzBiS,OAAO,EAAEC,IAAI,CAACC,SAAS,CAAC;oBACtBC,OAAO,EAAE,KAAK;oBACdC,gBAAgB,+CAAArH,MAAA,CAAYhL,CAAC,CAAE;oBAC/BsS,MAAM,EAAE,GAAG;oBACXC,QAAQ,uBAAAvH,MAAA,CAAQhL,CAAC;kBACnB,CAAC;gBACH,CAAC,CAAC;cACJ;;cAEA;cACMyR,cAAc,GAAGjK,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAClCuH,UAAU,GAAGF,QAAQ,CAACgB,GAAG,CAAC,UAAAC,IAAI,EAAI;gBACtC,IAAI;kBACF,OAAOP,IAAI,CAACQ,KAAK,CAACD,IAAI,CAACR,OAAO,CAAC;gBACjC,CAAC,CAAC,OAAOpG,KAAK,EAAE;kBACd,OAAO,IAAI;gBACb;cACF,CAAC,CAAC,CAACP,MAAM,CAAC,UAAAmH,IAAI;gBAAA,OAAIA,IAAI,KAAK,IAAI;cAAA,EAAC;cAE1Bd,YAAY,GAAGnK,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAChCyH,SAAS,GAAGD,YAAY,GAAGF,cAAc;cAEzC5H,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BJ,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAAA,OAAAsI,UAAA,CAAA1O,MAAA,WAE9B;gBACLyH,OAAO,EAAE6G,UAAU,CAACrN,MAAM,KAAKkN,SAAS,IAAIK,SAAS,GAAG,IAAI;gBAC5DnG,OAAO,EAAE;kBACP8F,SAAS,EAAEA,SAAS;kBACpBoB,WAAW,EAAEjB,UAAU,CAACrN,MAAM;kBAC9BuN,SAAS,EAAEA,SAAS;kBACpB7H,SAAS,EAAEA,SAAS;kBACpB6I,UAAU,EAAErB,SAAS,IAAIK,SAAS,GAAG,IAAI,CAAC,CAAC;gBAC7C;cACF,CAAC;YAAA;cAAAE,UAAA,CAAA3M,IAAA;cAAA2M,UAAA,CAAA7E,EAAA,GAAA6E,UAAA;cAAA,OAAAA,UAAA,CAAA1O,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEiG,UAAA,CAAA7E,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAgG,UAAA,CAAAxM,IAAA;UAAA;QAAA,GAAAgM,SAAA;MAAA,CAElD;MAAA,SAAAtI,2BAAA;QAAA,OAAAqI,2BAAA,CAAApL,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAgD,0BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAzC,GAAA;IAAAxG,KAAA;MAAA,IAAA8S,2BAAA,GAAA9M,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAoO,UAAA;QAAA,IAAAtJ,SAAA,EAAAuJ,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,aAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,MAAA,EAAAC,OAAA,EAAA5J,OAAA,EAAAE,SAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAA8S,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxO,IAAA,GAAAwO,UAAA,CAAAnQ,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAwJ,UAAA,CAAAxO,IAAA;cAGjC;cACM4N,QAAQ,GAAG,KAAK;cAChBC,YAAY,GAAGY,KAAK,CAACC,IAAI,CAAC;gBAAExP,MAAM,EAAE0O;cAAS,CAAC,EAAE,UAACe,CAAC,EAAE9T,CAAC;gBAAA,OAAM;kBAC/DuN,EAAE,EAAEvN,CAAC;kBACL+R,KAAK,iBAAA/G,MAAA,CAAOhL,CAAC,CAAE;kBACfiS,OAAO,EAAE,qBAAAjH,MAAA,CAAMhL,CAAC,wHAAsB+T,MAAM,CAAC,EAAE,CAAC;kBAChDC,QAAQ,EAAE;oBACRC,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;oBACjCC,IAAI,EAAE,OAAApJ,MAAA,CAAOhL,CAAC,GAAG,EAAE,cAAAgL,MAAA,CAAehL,CAAC,GAAG,CAAC,EAAG;oBAC1CqU,UAAU,EAAGrU,CAAC,GAAG,CAAC,GAAI;kBACxB;gBACF,CAAC;cAAA,CAAC,CAAC,EAEH;cACMiT,eAAe,GAAGzL,WAAW,CAAC2C,GAAG,CAAC,CAAC;cACnC+I,QAAQ,GAAGF,YAAY,CAAC1H,MAAM,CAAC,UAAAmH,IAAI;gBAAA,OAAIA,IAAI,CAAC4B,UAAU,KAAK,CAAC;cAAA,EAAC;cAC7DlB,UAAU,GAAG3L,WAAW,CAAC2C,GAAG,CAAC,CAAC,GAAG8I,eAAe;cAEhDG,aAAa,GAAG5L,WAAW,CAAC2C,GAAG,CAAC,CAAC;cACjCkJ,MAAM,GAAG,GAAArI,MAAA,CAAIgI,YAAY,EAAEsB,IAAI,CAAC,UAACpU,CAAC,EAAEqU,CAAC;gBAAA,OAAKrU,CAAC,CAAC6R,KAAK,CAACyC,aAAa,CAACD,CAAC,CAACxC,KAAK,CAAC;cAAA,EAAC;cACzEuB,QAAQ,GAAG9L,WAAW,CAAC2C,GAAG,CAAC,CAAC,GAAGiJ,aAAa;cAE5CG,YAAY,GAAG/L,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAChCqJ,MAAM,GAAGR,YAAY,CAACR,GAAG,CAAC,UAAAC,IAAI;gBAAA,OAAAgC,aAAA,CAAAA,aAAA,KAC/BhC,IAAI;kBACPiC,SAAS,EAAE,IAAI;kBACfC,aAAa,EAAElC,IAAI,CAACR,OAAO,CAAC5N;gBAAM;cAAA,CAClC,CAAC;cACGoP,OAAO,GAAGjM,WAAW,CAAC2C,GAAG,CAAC,CAAC,GAAGoJ,YAAY;cAE1C1J,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BJ,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAAA,OAAAmK,UAAA,CAAAvQ,MAAA,WAE9B;gBACLyH,OAAO,EAAEd,SAAS,GAAG,IAAI;gBAAE;gBAC3B0B,OAAO,EAAE;kBACPsH,QAAQ,EAAEA,QAAQ;kBAClBhJ,SAAS,EAAEA,SAAS;kBACpBoJ,UAAU,EAAEA,UAAU;kBACtBG,QAAQ,EAAEA,QAAQ;kBAClBG,OAAO,EAAEA,OAAO;kBAChBmB,aAAa,EAAE1B,QAAQ,CAAC7O,MAAM;kBAC9BwQ,eAAe,EAAE9B,QAAQ,IAAIhJ,SAAS,GAAG,IAAI;gBAC/C;cACF,CAAC;YAAA;cAAA4J,UAAA,CAAAxO,IAAA;cAAAwO,UAAA,CAAA1G,EAAA,GAAA0G,UAAA;cAAA,OAAAA,UAAA,CAAAvQ,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAE8H,UAAA,CAAA1G,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA6H,UAAA,CAAArO,IAAA;UAAA;QAAA,GAAAwN,SAAA;MAAA,CAElD;MAAA,SAAA7J,2BAAA;QAAA,OAAA4J,2BAAA,CAAA5M,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAiD,0BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA1C,GAAA;IAAAxG,KAAA;MAAA,IAAA+U,0BAAA,GAAA/O,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAqQ,UAAA;QAAA,IAAAC,YAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,cAAA;QAAA,OAAA9V,mBAAA,GAAAuB,IAAA,UAAAwU,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlQ,IAAA,GAAAkQ,UAAA,CAAA7R,IAAA;YAAA;cAAA6R,UAAA,CAAAlQ,IAAA;cAAA,IAESqC,WAAW,CAACC,MAAM;gBAAA4N,UAAA,CAAA7R,IAAA;gBAAA;cAAA;cAAA,OAAA6R,UAAA,CAAAjS,MAAA,WACd;gBACLyH,OAAO,EAAE,KAAK;gBACdgB,KAAK,EAAE;cACT,CAAC;YAAA;cAGGmJ,YAAY,GAAG;gBACnBtN,cAAc,EAAEF,WAAW,CAACC,MAAM,CAACC,cAAc;gBACjDC,eAAe,EAAEH,WAAW,CAACC,MAAM,CAACE;cACtC,CAAC,EAED;cACMsN,UAAU,GAAG,IAAIrB,KAAK,CAAC,MAAM,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC9C,GAAG,CAAC,UAACsB,CAAC,EAAE9T,CAAC;gBAAA,OAAM;kBAC1DuN,EAAE,EAAEvN,CAAC;kBACLuV,IAAI,EAAE,IAAI3B,KAAK,CAAC,GAAG,CAAC,CAAC0B,IAAI,SAAAtK,MAAA,CAAShL,CAAC,CAAE;gBACvC,CAAC;cAAA,CAAC,CAAC,EAEH;cACA,IAAIwN,MAAM,CAACgI,EAAE,EAAE;gBACbhI,MAAM,CAACgI,EAAE,CAAC,CAAC;cACb;cAACH,UAAA,CAAA7R,IAAA;cAAA,OAEK,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAE,GAAG,CAAC;cAAA,EAAC;YAAA;cAEhD0S,WAAW,GAAG;gBAClBxN,cAAc,EAAEF,WAAW,CAACC,MAAM,CAACC,cAAc;gBACjDC,eAAe,EAAEH,WAAW,CAACC,MAAM,CAACE;cACtC,CAAC;cAEKwN,cAAc,GAAGD,WAAW,CAACxN,cAAc,GAAGsN,YAAY,CAACtN,cAAc,EAE/E;cACAuN,UAAU,CAAC5Q,MAAM,GAAG,CAAC;cAAA,OAAAgR,UAAA,CAAAjS,MAAA,WAEd;gBACLyH,OAAO,EAAEsK,cAAc,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;gBAAE;gBAC5C1J,OAAO,EAAE;kBACPuJ,YAAY,EAAEA,YAAY;kBAC1BE,WAAW,EAAEA,WAAW;kBACxBC,cAAc,EAAEA,cAAc;kBAC9BM,gBAAgB,EAAEvK,IAAI,CAACwK,KAAK,CAACP,cAAc,GAAG,IAAI;gBACpD;cACF,CAAC;YAAA;cAAAE,UAAA,CAAAlQ,IAAA;cAAAkQ,UAAA,CAAApI,EAAA,GAAAoI,UAAA;cAAA,OAAAA,UAAA,CAAAjS,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEwJ,UAAA,CAAApI,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAuJ,UAAA,CAAA/P,IAAA;UAAA;QAAA,GAAAyP,SAAA;MAAA,CAElD;MAAA,SAAA7L,0BAAA;QAAA,OAAA4L,0BAAA,CAAA7O,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAkD,yBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA3C,GAAA;IAAAxG,KAAA;MAAA,IAAA4V,wBAAA,GAAA5P,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAkR,UAAA;QAAA,IAAAC,YAAA,EAAAC,UAAA,EAAAC,MAAA,EAAA/V,CAAA,EAAAgW,WAAA,EAAAC,eAAA,EAAAC,aAAA;QAAA,OAAA7W,mBAAA,GAAAuB,IAAA,UAAAuV,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjR,IAAA,GAAAiR,UAAA,CAAA5S,IAAA;YAAA;cAAA4S,UAAA,CAAAjR,IAAA;cAAA,IAESqC,WAAW,CAACC,MAAM;gBAAA2O,UAAA,CAAA5S,IAAA;gBAAA;cAAA;cAAA,OAAA4S,UAAA,CAAAhT,MAAA,WACd;gBACLyH,OAAO,EAAE,KAAK;gBACdgB,KAAK,EAAE;cACT,CAAC;YAAA;cAGGgK,YAAY,GAAG,EAAE;cACjBC,UAAU,GAAG,EAAE;cAAAC,MAAA,gBAAA1W,mBAAA,GAAAqF,IAAA,UAAAqR,OAAA/V,CAAA;gBAAA,IAAAqW,OAAA;gBAAA,OAAAhX,mBAAA,GAAAuB,IAAA,UAAA0V,QAAAC,UAAA;kBAAA,kBAAAA,UAAA,CAAApR,IAAA,GAAAoR,UAAA,CAAA/S,IAAA;oBAAA;sBAGnB;sBACM6S,OAAO,GAAG,IAAIzC,KAAK,CAAC,IAAI,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAAC9C,GAAG,CAAC,UAACsB,CAAC,EAAE0C,CAAC;wBAAA,OAAM;0BACrDjJ,EAAE,KAAAvC,MAAA,CAAKhL,CAAC,OAAAgL,MAAA,CAAIwL,CAAC,CAAE;0BACfjB,IAAI,EAAE,IAAI3B,KAAK,CAAC,GAAG,CAAC,CAAC0B,IAAI,cAAAtK,MAAA,CAAchL,CAAC,CAAE;wBAC5C,CAAC;sBAAA,CAAC,CAAC,EAEH;sBACA6V,YAAY,CAAC7R,IAAI,CAAC;wBAChByS,SAAS,EAAEzW,CAAC;wBACZ0H,cAAc,EAAEF,WAAW,CAACC,MAAM,CAACC;sBACrC,CAAC,CAAC;;sBAEF;sBACA2O,OAAO,CAAChS,MAAM,GAAG,CAAC;sBAAAkS,UAAA,CAAA/S,IAAA;sBAAA,OAEZ,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;wBAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAE,EAAE,CAAC;sBAAA,EAAC;oBAAA;oBAAA;sBAAA,OAAA+T,UAAA,CAAAjR,IAAA;kBAAA;gBAAA,GAAAyQ,MAAA;cAAA;cAhB9C/V,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAG8V,UAAU;gBAAAM,UAAA,CAAA5S,IAAA;gBAAA;cAAA;cAAA,OAAA4S,UAAA,CAAAvQ,aAAA,CAAAkQ,MAAA,CAAA/V,CAAA;YAAA;cAAEA,CAAC,EAAE;cAAAoW,UAAA,CAAA5S,IAAA;cAAA;YAAA;cAmBnC;cACMwS,WAAW,GAAGH,YAAY,CAACrD,GAAG,CAAC,UAACkE,CAAC,EAAE1W,CAAC;gBAAA,OACxCA,CAAC,GAAG,CAAC,GAAG0W,CAAC,CAAChP,cAAc,GAAGmO,YAAY,CAAC7V,CAAC,GAAC,CAAC,CAAC,CAAC0H,cAAc,GAAG,CAAC;cAAA,CACjE,CAAC,CAACrC,KAAK,CAAC,CAAC,CAAC;cAEJ4Q,eAAe,GAAGD,WAAW,CAACzK,MAAM,CAAC,UAACC,GAAG,EAAEmL,GAAG;gBAAA,OAAKnL,GAAG,GAAGmL,GAAG;cAAA,GAAE,CAAC,CAAC,GAAGX,WAAW,CAAC3R,MAAM;cACrF6R,aAAa,GAAGD,eAAe,GAAG,IAAI,GAAG,IAAI,EAAC;cAAA,OAAAG,UAAA,CAAAhT,MAAA,WAE7C;gBACLyH,OAAO,EAAE,CAACqL,aAAa;gBACvBzK,OAAO,EAAE;kBACPqK,UAAU,EAAEA,UAAU;kBACtBD,YAAY,EAAEA,YAAY;kBAC1BI,eAAe,EAAEA,eAAe;kBAChCW,iBAAiB,EAAE1L,IAAI,CAACwK,KAAK,CAACO,eAAe,GAAG,IAAI,CAAC;kBACrDC,aAAa,EAAEA;gBACjB;cACF,CAAC;YAAA;cAAAE,UAAA,CAAAjR,IAAA;cAAAiR,UAAA,CAAAxK,EAAA,GAAAwK,UAAA;cAAA,OAAAA,UAAA,CAAAhT,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEuK,UAAA,CAAAxK,EAAA,CAAME;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAsK,UAAA,CAAA9Q,IAAA;UAAA;QAAA,GAAAsQ,SAAA;MAAA,CAElD;MAAA,SAAAzM,wBAAA;QAAA,OAAAwM,wBAAA,CAAA1P,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAmD,uBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA5C,GAAA;IAAAxG,KAAA;MAAA,IAAA8W,oBAAA,GAAA9Q,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAoS,UAAA;QAAA,IAAAtN,SAAA,EAAAuN,QAAA,EAAAnN,OAAA,EAAAC,OAAA,EAAAE,SAAA,EAAAiN,mBAAA;QAAA,OAAA3X,mBAAA,GAAAuB,IAAA,UAAAqW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/R,IAAA,GAAA+R,UAAA,CAAA1T,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAA+M,UAAA,CAAA/R,IAAA;cAGjC;cACM4R,QAAQ,GAAG,CACf;gBAAEI,GAAG,EAAE,YAAY;gBAAEC,KAAK,EAAE;cAAI,CAAC,EACjC;gBAAED,GAAG,EAAE,YAAY;gBAAEC,KAAK,EAAE;cAAI,CAAC,EACjC;gBAAED,GAAG,EAAE,YAAY;gBAAEC,KAAK,EAAE;cAAI,CAAC,CAClC;cAAAF,UAAA,CAAA1T,IAAA;cAAA,OAEqBuB,OAAO,CAACsG,GAAG,CAC/B0L,QAAQ,CAACvE,GAAG;gBAAA,IAAA6E,IAAA,GAAAtR,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAAC,SAAA4S,UAAOlW,IAAI;kBAAA,IAAAmW,SAAA,EAAAC,OAAA;kBAAA,OAAAnY,mBAAA,GAAAuB,IAAA,UAAA6W,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAvS,IAAA,GAAAuS,UAAA,CAAAlU,IAAA;sBAAA;wBAChB+T,SAAS,GAAG/P,WAAW,CAAC2C,GAAG,CAAC,CAAC,EAEnC;wBAAAuN,UAAA,CAAAlU,IAAA;wBAAA,OACM,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;0BAAA,OAAI2L,UAAU,CAAC3L,OAAO,EAAEpB,IAAI,CAACgW,KAAK,CAAC;wBAAA,EAAC;sBAAA;wBAEvDI,OAAO,GAAGhQ,WAAW,CAAC2C,GAAG,CAAC,CAAC;wBAAA,OAAAuN,UAAA,CAAAtU,MAAA,WAC1B;0BACL+T,GAAG,EAAE/V,IAAI,CAAC+V,GAAG;0BACbQ,YAAY,EAAEH,OAAO,GAAGD,SAAS;0BACjC1M,OAAO,EAAE;wBACX,CAAC;sBAAA;sBAAA;wBAAA,OAAA6M,UAAA,CAAApS,IAAA;oBAAA;kBAAA,GAAAgS,SAAA;gBAAA,CACF;gBAAA,iBAAAM,EAAA;kBAAA,OAAAP,IAAA,CAAApR,KAAA,OAAAD,SAAA;gBAAA;cAAA,IACH,CAAC;YAAA;cAdK4D,OAAO,GAAAsN,UAAA,CAAAjU,IAAA;cAgBP4G,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BJ,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAC/BwN,mBAAmB,GAAGpN,OAAO,CAAC2B,MAAM,CAAC,UAACC,GAAG,EAAEhM,CAAC;gBAAA,OAAKgM,GAAG,GAAGhM,CAAC,CAACmY,YAAY;cAAA,GAAE,CAAC,CAAC,GAAG/N,OAAO,CAACvF,MAAM;cAAA,OAAA6S,UAAA,CAAA9T,MAAA,WAEzF;gBACLyH,OAAO,EAAEmM,mBAAmB,GAAG,GAAG;gBAAE;gBACpCvL,OAAO,EAAE;kBACPoM,YAAY,EAAEd,QAAQ,CAAC1S,MAAM;kBAC7B0F,SAAS,EAAEA,SAAS;kBACpBiN,mBAAmB,EAAEA,mBAAmB;kBACxCpN,OAAO,EAAEA;gBACX;cACF,CAAC;YAAA;cAAAsN,UAAA,CAAA/R,IAAA;cAAA+R,UAAA,CAAAjK,EAAA,GAAAiK,UAAA;cAAA,OAAAA,UAAA,CAAA9T,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAEqL,UAAA,CAAAjK,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAoL,UAAA,CAAA5R,IAAA;UAAA;QAAA,GAAAwR,SAAA;MAAA,CAElD;MAAA,SAAA1N,oBAAA;QAAA,OAAAyN,oBAAA,CAAA5Q,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAoD,mBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA7C,GAAA;IAAAxG,KAAA;MAAA,IAAA+X,0BAAA,GAAA/R,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,CAGA,SAAAqT,UAAA;QAAA,IAAAvO,SAAA,EAAAwO,QAAA,EAAAC,WAAA,EAAAC,QAAA,EAAAC,WAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,UAAA,EAAA1O,OAAA,EAAAE,SAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAA4X,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtT,IAAA,GAAAsT,UAAA,CAAAjV,IAAA;YAAA;cACQgG,SAAS,GAAGhC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAAAsO,UAAA,CAAAtT,IAAA;cAGjC;cACM6S,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAC;cACvBC,WAAW,GAAG,IAAIrE,KAAK,CAACoE,QAAQ,CAAC,CAAC1C,IAAI,CAAC,GAAG,CAAC,CAACoD,IAAI,CAAC,EAAE,CAAC;cACpDR,QAAQ,GAAG,IAAIS,IAAI,CAAC,CAACV,WAAW,CAAC,EAAE,UAAU,EAAE;gBAAE/W,IAAI,EAAE;cAAa,CAAC,CAAC,EAE5E;cACMiX,WAAW,GAAG3Q,WAAW,CAAC2C,GAAG,CAAC,CAAC,EAErC;cACMiO,MAAM,GAAG,IAAIQ,UAAU,CAAC,CAAC;cACzBP,WAAW,GAAG,IAAItT,OAAO,CAAC,UAACvC,OAAO,EAAE+H,MAAM,EAAK;gBACnD6N,MAAM,CAACzN,MAAM,GAAG;kBAAA,OAAMnI,OAAO,CAAC4V,MAAM,CAACS,MAAM,CAAC;gBAAA;gBAC5CT,MAAM,CAACtN,OAAO,GAAG;kBAAA,OAAMP,MAAM,CAAC6N,MAAM,CAACvM,KAAK,CAAC;gBAAA;cAC7C,CAAC,CAAC;cAEFuM,MAAM,CAACU,UAAU,CAACZ,QAAQ,CAAC;cAAAO,UAAA,CAAAjV,IAAA;cAAA,OACrB6U,WAAW;YAAA;cAEXC,SAAS,GAAG9Q,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC7BoO,UAAU,GAAGD,SAAS,GAAGH,WAAW;cAEpCtO,OAAO,GAAGrC,WAAW,CAAC2C,GAAG,CAAC,CAAC;cAC3BJ,SAAS,GAAGF,OAAO,GAAGL,SAAS;cAAA,OAAAiP,UAAA,CAAArV,MAAA,WAE9B;gBACLyH,OAAO,EAAE0N,UAAU,GAAG,IAAI;gBAAE;gBAC5B9M,OAAO,EAAE;kBACPuM,QAAQ,EAAEA,QAAQ;kBAClBe,UAAU,EAAE7N,IAAI,CAACwK,KAAK,CAACsC,QAAQ,GAAG,IAAI,CAAC;kBACvCO,UAAU,EAAEA,UAAU;kBACtBxO,SAAS,EAAEA,SAAS;kBACpBiP,WAAW,EAAEhB,QAAQ,IAAIO,UAAU,GAAG,IAAI,CAAC,CAAC;gBAC9C;cACF,CAAC;YAAA;cAAAE,UAAA,CAAAtT,IAAA;cAAAsT,UAAA,CAAAxL,EAAA,GAAAwL,UAAA;cAAA,OAAAA,UAAA,CAAArV,MAAA,WAEM;gBAAEyH,OAAO,EAAE,KAAK;gBAAEgB,KAAK,EAAE4M,UAAA,CAAAxL,EAAA,CAAMnB;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA2M,UAAA,CAAAnT,IAAA;UAAA;QAAA,GAAAyS,SAAA;MAAA,CAElD;MAAA,SAAA1O,0BAAA;QAAA,OAAAyO,0BAAA,CAAA7R,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAqD,yBAAA;IAAA;IAED;AACF;AACA;AACA;AACA;IAJE;EAAA;IAAA9C,GAAA;IAAAxG,KAAA,EAKA,SAAA4O,yBAAyBsK,SAAS,EAAE;MAClC,IAAIhH,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIjS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiZ,SAAS,EAAEjZ,CAAC,EAAE,EAAE;QAClCiS,OAAO,4BAAAjH,MAAA,CAAahL,CAAC,GAAG,CAAC,iJAAoD;QAC7E,IAAIA,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;UAChBiS,OAAO,4CAAAjH,MAAA,CAA2CC,IAAI,CAAC,wHAAwH,CAAC,uCAAAD,MAAA,CAAchL,CAAC,YAAQ;QACzM;MACF;MACA,OAAOiS,OAAO;IAChB;EAAC;AAAA;AAGH,eAAenL,oBAAoB", "ignoreList": []}]}