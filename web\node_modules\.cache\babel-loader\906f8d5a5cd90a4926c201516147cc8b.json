{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue", "mtime": 1753520887545}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport TestManager from '@/utils/testManager';\nimport FunctionalTestSuite from '@/utils/testSuites/functionalTestSuite';\nimport PerformanceTestSuite from '@/utils/testSuites/performanceTestSuite';\nimport UserExperienceTestSuite from '@/utils/testSuites/userExperienceTestSuite';\nexport default {\n  name: 'ComprehensiveTestSuite',\n  data: function data() {\n    return {\n      // 测试管理器\n      testManager: new TestManager(),\n      // 测试套件\n      functionalSuite: new FunctionalTestSuite(),\n      performanceSuite: new PerformanceTestSuite(),\n      userExperienceSuite: new UserExperienceTestSuite(),\n      // 选中的测试套件\n      selectedSuites: ['functional', 'performance', 'userExperience'],\n      // 测试状态\n      isRunning: false,\n      hasResults: false,\n      // 测试进度\n      testProgress: {\n        progress: 0,\n        status: 'normal',\n        phase: '',\n        message: '',\n        currentSuite: null\n      },\n      // 测试结果\n      testResults: {},\n      testSummary: {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      },\n      // 测试用例数量\n      functionalTestCount: 0,\n      performanceTestCount: 0,\n      userExperienceTestCount: 0,\n      // 表格列定义\n      testCaseColumns: [{\n        title: '测试用例',\n        dataIndex: 'name',\n        key: 'name',\n        width: 200\n      }, {\n        title: '描述',\n        dataIndex: 'description',\n        key: 'description'\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        width: 80,\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '耗时',\n        dataIndex: 'duration',\n        key: 'duration',\n        width: 80,\n        scopedSlots: {\n          customRender: 'duration'\n        }\n      }, {\n        title: '指标',\n        key: 'metrics',\n        width: 80,\n        scopedSlots: {\n          customRender: 'metrics'\n        }\n      }, {\n        title: '错误',\n        dataIndex: 'error',\n        key: 'error',\n        width: 60,\n        scopedSlots: {\n          customRender: 'error'\n        }\n      }],\n      // 指标详情模态框\n      metricsModalVisible: false,\n      selectedTestMetrics: null\n    };\n  },\n  computed: {\n    testSuiteCount: function testSuiteCount() {\n      return this.selectedSuites.length;\n    },\n    totalTestCases: function totalTestCases() {\n      var total = 0;\n      if (this.selectedSuites.includes('functional')) total += this.functionalTestCount;\n      if (this.selectedSuites.includes('performance')) total += this.performanceTestCount;\n      if (this.selectedSuites.includes('userExperience')) total += this.userExperienceTestCount;\n      return total;\n    },\n    completedTests: function completedTests() {\n      return this.testSummary.totalTests;\n    },\n    successRate: function successRate() {\n      return Math.round(this.testSummary.successRate) || 0;\n    }\n  },\n  mounted: function mounted() {\n    this.initializeTestSuites();\n    this.updateTestCaseCount();\n  },\n  methods: {\n    // 初始化测试套件\n    initializeTestSuites: function initializeTestSuites() {\n      // 注册测试套件\n      this.testManager.registerTestSuite('functional', this.functionalSuite);\n      this.testManager.registerTestSuite('performance', this.performanceSuite);\n      this.testManager.registerTestSuite('userExperience', this.userExperienceSuite);\n\n      // 获取测试用例数量\n      this.functionalTestCount = this.functionalSuite.getTestCases().length;\n      this.performanceTestCount = this.performanceSuite.getTestCases().length;\n      this.userExperienceTestCount = this.userExperienceSuite.getTestCases().length;\n    },\n    // 更新测试用例数量\n    updateTestCaseCount: function updateTestCaseCount() {\n      // 这个方法在选择套件时调用，用于更新计算属性\n      this.$forceUpdate();\n    },\n    // 运行所有测试\n    runAllTests: function () {\n      var _runAllTests = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _this = this;\n        var tempManager, result;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!(this.selectedSuites.length === 0)) {\n                _context.next = 3;\n                break;\n              }\n              this.$message.warning('请至少选择一个测试套件');\n              return _context.abrupt(\"return\");\n            case 3:\n              this.isRunning = true;\n              this.hasResults = false;\n              this.testResults = {};\n              this.testSummary = {\n                totalSuites: 0,\n                totalTests: 0,\n                totalPassed: 0,\n                totalFailed: 0,\n                totalSkipped: 0,\n                totalDuration: 0,\n                successRate: 0,\n                performanceIssues: [],\n                recommendations: []\n              };\n              _context.prev = 7;\n              // 清理之前的结果\n              this.testManager.clearResults();\n\n              // 只注册选中的测试套件\n              tempManager = new TestManager();\n              if (this.selectedSuites.includes('functional')) {\n                tempManager.registerTestSuite('functional', this.functionalSuite);\n              }\n              if (this.selectedSuites.includes('performance')) {\n                tempManager.registerTestSuite('performance', this.performanceSuite);\n              }\n              if (this.selectedSuites.includes('userExperience')) {\n                tempManager.registerTestSuite('userExperience', this.userExperienceSuite);\n              }\n\n              // 运行测试\n              _context.next = 15;\n              return tempManager.runAllTests({}, this.handleTestProgress);\n            case 15:\n              result = _context.sent;\n              if (result.success) {\n                this.testResults = result.results;\n                this.testSummary = result.summary;\n                this.hasResults = true;\n                this.$message.success('所有测试已完成');\n\n                // 渲染图表\n                this.$nextTick(function () {\n                  _this.renderResultChart();\n                });\n              } else {\n                this.$message.error('测试运行失败: ' + result.error);\n              }\n              _context.next = 22;\n              break;\n            case 19:\n              _context.prev = 19;\n              _context.t0 = _context[\"catch\"](7);\n              this.$message.error('测试运行异常: ' + _context.t0.message);\n            case 22:\n              _context.prev = 22;\n              this.isRunning = false;\n              this.testProgress = {\n                progress: 0,\n                status: 'normal',\n                phase: '',\n                message: '',\n                currentSuite: null\n              };\n              return _context.finish(22);\n            case 26:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this, [[7, 19, 22, 26]]);\n      }));\n      function runAllTests() {\n        return _runAllTests.apply(this, arguments);\n      }\n      return runAllTests;\n    }(),\n    // 处理测试进度\n    handleTestProgress: function handleTestProgress(progress) {\n      this.testProgress = _objectSpread(_objectSpread({}, progress), {}, {\n        status: progress.phase === 'completed' ? 'success' : 'active'\n      });\n    },\n    // 停止测试\n    stopTests: function stopTests() {\n      this.testManager.stopTests();\n      this.isRunning = false;\n      this.$message.info('测试已停止');\n    },\n    // 清空结果\n    clearResults: function clearResults() {\n      this.hasResults = false;\n      this.testResults = {};\n      this.testSummary = {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      };\n      this.testManager.clearResults();\n      this.$message.success('测试结果已清空');\n    },\n    // 导出报告\n    exportReport: function exportReport() {\n      try {\n        var reportContent = this.testManager.exportReport('html');\n        var blob = new Blob([reportContent], {\n          type: 'text/html;charset=utf-8'\n        });\n        var url = window.URL.createObjectURL(blob);\n        var link = document.createElement('a');\n        link.href = url;\n        link.download = \"\\u6D4B\\u8BD5\\u62A5\\u544A_\".concat(new Date().toISOString().slice(0, 10), \".html\");\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        this.$message.success('测试报告已导出');\n      } catch (error) {\n        this.$message.error('导出失败: ' + error.message);\n      }\n    },\n    // 显示指标详情\n    showMetricsDetail: function showMetricsDetail(testCase) {\n      this.selectedTestMetrics = testCase;\n      this.metricsModalVisible = true;\n    },\n    // 渲染结果图表\n    renderResultChart: function renderResultChart() {\n      // 这里可以集成图表库（如ECharts）来渲染测试结果图表\n      // 为了简化，这里只是一个占位方法\n      console.log('渲染测试结果图表');\n    },\n    // 获取套件显示名称\n    getSuiteDisplayName: function getSuiteDisplayName(suiteName) {\n      var names = {\n        functional: '功能测试',\n        performance: '性能测试',\n        userExperience: '用户体验'\n      };\n      return names[suiteName] || suiteName;\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(status) {\n      var colors = {\n        passed: 'green',\n        failed: 'red',\n        skipped: 'orange',\n        running: 'blue'\n      };\n      return colors[status] || 'default';\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status) {\n      var texts = {\n        passed: '通过',\n        failed: '失败',\n        skipped: '跳过',\n        running: '运行中'\n      };\n      return texts[status] || status;\n    },\n    // 获取优先级颜色\n    getPriorityColor: function getPriorityColor(priority) {\n      var colors = {\n        high: 'red',\n        medium: 'orange',\n        low: 'blue'\n      };\n      return colors[priority] || 'default';\n    },\n    // 格式化持续时间\n    formatDuration: function formatDuration(ms) {\n      if (ms < 1000) {\n        return \"\".concat(Math.round(ms), \"ms\");\n      } else if (ms < 60000) {\n        return \"\".concat((ms / 1000).toFixed(1), \"s\");\n      } else {\n        return \"\".concat((ms / 60000).toFixed(1), \"min\");\n      }\n    },\n    // 格式化指标标签\n    formatMetricLabel: function formatMetricLabel(key) {\n      var labels = {\n        executionTime: '执行时间',\n        memoryUsage: '内存使用',\n        renderTime: '渲染时间',\n        loadTime: '加载时间',\n        responseTime: '响应时间',\n        fileSize: '文件大小',\n        imageCount: '图片数量',\n        formulaCount: '公式数量'\n      };\n      return labels[key] || key;\n    },\n    // 格式化指标值\n    formatMetricValue: function formatMetricValue(key, value) {\n      if (key.includes('Time') || key.includes('Duration')) {\n        return this.formatDuration(value);\n      } else if (key.includes('Size')) {\n        return \"\".concat(Math.round(value / 1024), \"KB\");\n      } else if (typeof value === 'number') {\n        return value.toLocaleString();\n      } else {\n        return value;\n      }\n    },\n    // 获取性能评分\n    getPerformanceScore: function getPerformanceScore() {\n      var successRate = this.testSummary.successRate;\n      var issueCount = this.testSummary.performanceIssues.length;\n      var score = successRate;\n      score -= issueCount * 5; // 每个性能问题扣5分\n\n      return Math.max(0, Math.round(score));\n    }\n  }\n};", {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "TestManager", "FunctionalTestSuite", "PerformanceTestSuite", "UserExperienceTestSuite", "data", "testManager", "functionalSuite", "performanceSuite", "userExperienceSuite", "selectedSuites", "isRunning", "hasResults", "testProgress", "progress", "status", "phase", "message", "currentSuite", "testResults", "testSummary", "totalSuites", "totalTests", "totalPassed", "totalFailed", "totalSkipped", "totalDuration", "successRate", "performanceIssues", "recommendations", "functionalTestCount", "performanceTestCount", "userExperienceTestCount", "testCaseColumns", "title", "dataIndex", "key", "width", "scopedSlots", "customRender", "metricsModalVisible", "selectedTestMetrics", "computed", "testSuiteCount", "totalTestCases", "total", "includes", "completedTests", "Math", "round", "mounted", "initializeTestSuites", "updateTestCaseCount", "methods", "registerTestSuite", "getTestCases", "$forceUpdate", "runAllTests", "_runAllTests", "_callee", "_this", "tempManager", "result", "_callee$", "_context", "$message", "warning", "clearResults", "handleTestProgress", "success", "results", "summary", "$nextTick", "renderResultChart", "error", "t0", "_objectSpread", "stopTests", "info", "exportReport", "reportContent", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "concat", "Date", "toISOString", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "showMetricsDetail", "testCase", "console", "log", "getSuiteDisplayName", "suiteName", "names", "functional", "performance", "userExperience", "getStatusColor", "colors", "passed", "failed", "skipped", "running", "getStatusText", "texts", "getPriorityColor", "priority", "high", "medium", "low", "formatDuration", "ms", "toFixed", "formatMetricLabel", "labels", "executionTime", "memoryUsage", "renderTime", "loadTime", "responseTime", "fileSize", "imageCount", "formulaCount", "formatMetricValue", "toLocaleString", "getPerformanceScore", "issueCount", "score", "max"], "sources": ["src/views/examSystem/test/ComprehensiveTestSuite.vue"], "sourcesContent": ["<template>\n  <div class=\"comprehensive-test-suite\">\n    <a-card title=\"阶段五：全面功能测试 - 综合测试套件\" style=\"margin-bottom: 16px;\">\n      <a-alert\n        message=\"综合测试说明\"\n        description=\"此页面提供完整的系统测试功能，包括功能测试、性能测试和用户体验测试。通过自动化测试确保系统的稳定性、性能和用户体验。\"\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px;\"\n      />\n      \n      <!-- 测试控制面板 -->\n      <div class=\"test-control-panel\" style=\"margin-bottom: 24px;\">\n        <a-row :gutter=\"16\">\n          <a-col :span=\"12\">\n            <a-statistic-group>\n              <a-statistic title=\"测试套件\" :value=\"testSuiteCount\" />\n              <a-statistic title=\"测试用例\" :value=\"totalTestCases\" />\n              <a-statistic title=\"已完成\" :value=\"completedTests\" />\n              <a-statistic title=\"成功率\" :value=\"successRate\" suffix=\"%\" />\n            </a-statistic-group>\n          </a-col>\n          <a-col :span=\"12\">\n            <div class=\"test-actions\">\n              <a-button \n                type=\"primary\" \n                size=\"large\"\n                :loading=\"isRunning\"\n                :disabled=\"isRunning\"\n                @click=\"runAllTests\"\n                icon=\"play-circle\">\n                运行全部测试\n              </a-button>\n              <a-button \n                :disabled=\"!hasResults\"\n                @click=\"exportReport\"\n                icon=\"download\"\n                style=\"margin-left: 8px;\">\n                导出报告\n              </a-button>\n              <a-button \n                :disabled=\"!isRunning\"\n                @click=\"stopTests\"\n                icon=\"stop\"\n                style=\"margin-left: 8px;\">\n                停止测试\n              </a-button>\n              <a-button \n                :disabled=\"!hasResults\"\n                @click=\"clearResults\"\n                icon=\"clear\"\n                style=\"margin-left: 8px;\">\n                清空结果\n              </a-button>\n            </div>\n          </a-col>\n        </a-row>\n      </div>\n\n      <!-- 测试进度 -->\n      <div v-if=\"isRunning\" class=\"test-progress\" style=\"margin-bottom: 24px;\">\n        <a-card title=\"测试进度\" size=\"small\">\n          <a-progress \n            :percent=\"testProgress.progress\" \n            :status=\"testProgress.status\"\n            :strokeWidth=\"8\"\n          />\n          <div style=\"margin-top: 12px;\">\n            <a-tag color=\"blue\">{{ testProgress.phase }}</a-tag>\n            <span style=\"margin-left: 8px;\">{{ testProgress.message }}</span>\n          </div>\n          <div v-if=\"testProgress.currentSuite\" style=\"margin-top: 8px;\">\n            <strong>当前套件：</strong>{{ testProgress.currentSuite }}\n          </div>\n        </a-card>\n      </div>\n    </a-card>\n\n    <!-- 测试套件选择 -->\n    <a-card title=\"测试套件配置\" style=\"margin-bottom: 16px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('functional') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"functional\"\n                @change=\"updateTestCaseCount\">\n                功能测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统核心功能的正确性和完整性</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>图片支持测试</li>\n              <li>数学公式测试</li>\n              <li>导入导出测试</li>\n              <li>兼容性测试</li>\n            </ul>\n            <a-tag color=\"green\">{{ functionalTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('performance') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"performance\"\n                @change=\"updateTestCaseCount\">\n                性能测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统各组件的性能表现</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>图片加载性能</li>\n              <li>编辑器性能</li>\n              <li>数据处理性能</li>\n              <li>内存使用监控</li>\n            </ul>\n            <a-tag color=\"orange\">{{ performanceTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('userExperience') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"userExperience\"\n                @change=\"updateTestCaseCount\">\n                用户体验测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统的用户界面和交互体验</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>界面流畅性</li>\n              <li>响应式设计</li>\n              <li>交互体验</li>\n              <li>可访问性</li>\n            </ul>\n            <a-tag color=\"purple\">{{ userExperienceTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n      </a-row>\n    </a-card>\n\n    <!-- 测试结果概览 -->\n    <a-card v-if=\"hasResults\" title=\"测试结果概览\" style=\"margin-bottom: 16px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"总测试数\" \n            :value=\"testSummary.totalTests\"\n            :value-style=\"{ color: '#1890ff' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"通过测试\" \n            :value=\"testSummary.totalPassed\"\n            :value-style=\"{ color: '#52c41a' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"失败测试\" \n            :value=\"testSummary.totalFailed\"\n            :value-style=\"{ color: '#ff4d4f' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"跳过测试\" \n            :value=\"testSummary.totalSkipped\"\n            :value-style=\"{ color: '#faad14' }\"\n          />\n        </a-col>\n      </a-row>\n\n      <!-- 测试结果图表 -->\n      <div style=\"margin-top: 24px;\">\n        <a-row :gutter=\"16\">\n          <a-col :span=\"12\">\n            <div class=\"test-chart\">\n              <h4>测试结果分布</h4>\n              <div class=\"result-chart\" ref=\"resultChart\" style=\"height: 200px;\"></div>\n            </div>\n          </a-col>\n          <a-col :span=\"12\">\n            <div class=\"performance-chart\">\n              <h4>性能指标</h4>\n              <div class=\"performance-metrics\">\n                <a-progress \n                  type=\"circle\" \n                  :percent=\"Math.round(testSummary.successRate)\"\n                  :status=\"testSummary.successRate >= 80 ? 'success' : 'exception'\"\n                  :width=\"80\"\n                />\n                <div style=\"margin-left: 16px;\">\n                  <p><strong>总耗时：</strong>{{ formatDuration(testSummary.totalDuration) }}</p>\n                  <p><strong>平均耗时：</strong>{{ formatDuration(testSummary.totalDuration / testSummary.totalTests) }}</p>\n                  <p><strong>性能评分：</strong>{{ getPerformanceScore() }}</p>\n                </div>\n              </div>\n            </div>\n          </a-col>\n        </a-row>\n      </div>\n    </a-card>\n\n    <!-- 详细测试结果 -->\n    <a-card v-if=\"hasResults\" title=\"详细测试结果\">\n      <a-tabs>\n        <a-tab-pane \n          v-for=\"(suiteResult, suiteName) in testResults\" \n          :key=\"suiteName\" \n          :tab=\"`${getSuiteDisplayName(suiteName)} (${suiteResult.passed}/${suiteResult.tests.length})`\">\n          \n          <!-- 套件概览 -->\n          <div style=\"margin-bottom: 16px;\">\n            <a-descriptions bordered size=\"small\">\n              <a-descriptions-item label=\"套件状态\">\n                <a-tag :color=\"suiteResult.status === 'passed' ? 'green' : 'red'\">\n                  {{ suiteResult.status === 'passed' ? '通过' : '失败' }}\n                </a-tag>\n              </a-descriptions-item>\n              <a-descriptions-item label=\"执行时间\">{{ formatDuration(suiteResult.duration) }}</a-descriptions-item>\n              <a-descriptions-item label=\"通过率\">{{ Math.round((suiteResult.passed / suiteResult.tests.length) * 100) }}%</a-descriptions-item>\n            </a-descriptions>\n          </div>\n\n          <!-- 测试用例列表 -->\n          <a-table\n            :dataSource=\"suiteResult.tests\"\n            :columns=\"testCaseColumns\"\n            :pagination=\"{ pageSize: 10 }\"\n            size=\"small\"\n            rowKey=\"name\">\n            \n            <template slot=\"status\" slot-scope=\"text, record\">\n              <a-tag :color=\"getStatusColor(record.status)\">\n                {{ getStatusText(record.status) }}\n              </a-tag>\n            </template>\n            \n            <template slot=\"duration\" slot-scope=\"text\">\n              {{ formatDuration(text) }}\n            </template>\n            \n            <template slot=\"metrics\" slot-scope=\"text, record\">\n              <a-button \n                v-if=\"Object.keys(record.metrics).length > 0\"\n                type=\"link\" \n                size=\"small\"\n                @click=\"showMetricsDetail(record)\">\n                查看指标\n              </a-button>\n            </template>\n            \n            <template slot=\"error\" slot-scope=\"text\">\n              <a-tooltip v-if=\"text\" :title=\"text\">\n                <a-icon type=\"exclamation-circle\" style=\"color: #ff4d4f;\" />\n              </a-tooltip>\n            </template>\n          </a-table>\n        </a-tab-pane>\n      </a-tabs>\n    </a-card>\n\n    <!-- 性能问题和建议 -->\n    <a-card v-if=\"hasResults && (testSummary.performanceIssues.length > 0 || testSummary.recommendations.length > 0)\" title=\"问题和建议\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"12\" v-if=\"testSummary.performanceIssues.length > 0\">\n          <h4>性能问题</h4>\n          <a-list\n            :dataSource=\"testSummary.performanceIssues\"\n            size=\"small\">\n            <a-list-item slot=\"renderItem\" slot-scope=\"issue\">\n              <a-list-item-meta>\n                <template slot=\"title\">\n                  <a-tag color=\"orange\">{{ issue.type }}</a-tag>\n                  {{ issue.test }}\n                </template>\n                <template slot=\"description\">\n                  {{ issue.message }}\n                </template>\n              </a-list-item-meta>\n            </a-list-item>\n          </a-list>\n        </a-col>\n        <a-col :span=\"12\" v-if=\"testSummary.recommendations.length > 0\">\n          <h4>优化建议</h4>\n          <a-list\n            :dataSource=\"testSummary.recommendations\"\n            size=\"small\">\n            <a-list-item slot=\"renderItem\" slot-scope=\"recommendation\">\n              <a-list-item-meta>\n                <template slot=\"title\">\n                  <a-tag :color=\"getPriorityColor(recommendation.priority)\">\n                    {{ recommendation.priority }}\n                  </a-tag>\n                  {{ recommendation.type }}\n                </template>\n                <template slot=\"description\">\n                  {{ recommendation.message }}\n                </template>\n              </a-list-item-meta>\n            </a-list-item>\n          </a-list>\n        </a-col>\n      </a-row>\n    </a-card>\n\n    <!-- 指标详情模态框 -->\n    <a-modal\n      v-model=\"metricsModalVisible\"\n      title=\"测试指标详情\"\n      :footer=\"null\"\n      width=\"600px\">\n      <div v-if=\"selectedTestMetrics\">\n        <h4>{{ selectedTestMetrics.name }}</h4>\n        <a-descriptions bordered size=\"small\">\n          <a-descriptions-item \n            v-for=\"(value, key) in selectedTestMetrics.metrics\" \n            :key=\"key\"\n            :label=\"formatMetricLabel(key)\">\n            {{ formatMetricValue(key, value) }}\n          </a-descriptions-item>\n        </a-descriptions>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport TestManager from '@/utils/testManager'\nimport FunctionalTestSuite from '@/utils/testSuites/functionalTestSuite'\nimport PerformanceTestSuite from '@/utils/testSuites/performanceTestSuite'\nimport UserExperienceTestSuite from '@/utils/testSuites/userExperienceTestSuite'\n\nexport default {\n  name: 'ComprehensiveTestSuite',\n  data() {\n    return {\n      // 测试管理器\n      testManager: new TestManager(),\n      \n      // 测试套件\n      functionalSuite: new FunctionalTestSuite(),\n      performanceSuite: new PerformanceTestSuite(),\n      userExperienceSuite: new UserExperienceTestSuite(),\n      \n      // 选中的测试套件\n      selectedSuites: ['functional', 'performance', 'userExperience'],\n      \n      // 测试状态\n      isRunning: false,\n      hasResults: false,\n      \n      // 测试进度\n      testProgress: {\n        progress: 0,\n        status: 'normal',\n        phase: '',\n        message: '',\n        currentSuite: null\n      },\n      \n      // 测试结果\n      testResults: {},\n      testSummary: {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      },\n      \n      // 测试用例数量\n      functionalTestCount: 0,\n      performanceTestCount: 0,\n      userExperienceTestCount: 0,\n      \n      // 表格列定义\n      testCaseColumns: [\n        {\n          title: '测试用例',\n          dataIndex: 'name',\n          key: 'name',\n          width: 200\n        },\n        {\n          title: '描述',\n          dataIndex: 'description',\n          key: 'description'\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 80,\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '耗时',\n          dataIndex: 'duration',\n          key: 'duration',\n          width: 80,\n          scopedSlots: { customRender: 'duration' }\n        },\n        {\n          title: '指标',\n          key: 'metrics',\n          width: 80,\n          scopedSlots: { customRender: 'metrics' }\n        },\n        {\n          title: '错误',\n          dataIndex: 'error',\n          key: 'error',\n          width: 60,\n          scopedSlots: { customRender: 'error' }\n        }\n      ],\n      \n      // 指标详情模态框\n      metricsModalVisible: false,\n      selectedTestMetrics: null\n    }\n  },\n  computed: {\n    testSuiteCount() {\n      return this.selectedSuites.length\n    },\n    \n    totalTestCases() {\n      let total = 0\n      if (this.selectedSuites.includes('functional')) total += this.functionalTestCount\n      if (this.selectedSuites.includes('performance')) total += this.performanceTestCount\n      if (this.selectedSuites.includes('userExperience')) total += this.userExperienceTestCount\n      return total\n    },\n    \n    completedTests() {\n      return this.testSummary.totalTests\n    },\n    \n    successRate() {\n      return Math.round(this.testSummary.successRate) || 0\n    }\n  },\n  mounted() {\n    this.initializeTestSuites()\n    this.updateTestCaseCount()\n  },\n  methods: {\n    // 初始化测试套件\n    initializeTestSuites() {\n      // 注册测试套件\n      this.testManager.registerTestSuite('functional', this.functionalSuite)\n      this.testManager.registerTestSuite('performance', this.performanceSuite)\n      this.testManager.registerTestSuite('userExperience', this.userExperienceSuite)\n      \n      // 获取测试用例数量\n      this.functionalTestCount = this.functionalSuite.getTestCases().length\n      this.performanceTestCount = this.performanceSuite.getTestCases().length\n      this.userExperienceTestCount = this.userExperienceSuite.getTestCases().length\n    },\n    \n    // 更新测试用例数量\n    updateTestCaseCount() {\n      // 这个方法在选择套件时调用，用于更新计算属性\n      this.$forceUpdate()\n    },\n    \n    // 运行所有测试\n    async runAllTests() {\n      if (this.selectedSuites.length === 0) {\n        this.$message.warning('请至少选择一个测试套件')\n        return\n      }\n      \n      this.isRunning = true\n      this.hasResults = false\n      this.testResults = {}\n      this.testSummary = {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      }\n      \n      try {\n        // 清理之前的结果\n        this.testManager.clearResults()\n        \n        // 只注册选中的测试套件\n        const tempManager = new TestManager()\n        if (this.selectedSuites.includes('functional')) {\n          tempManager.registerTestSuite('functional', this.functionalSuite)\n        }\n        if (this.selectedSuites.includes('performance')) {\n          tempManager.registerTestSuite('performance', this.performanceSuite)\n        }\n        if (this.selectedSuites.includes('userExperience')) {\n          tempManager.registerTestSuite('userExperience', this.userExperienceSuite)\n        }\n        \n        // 运行测试\n        const result = await tempManager.runAllTests({}, this.handleTestProgress)\n        \n        if (result.success) {\n          this.testResults = result.results\n          this.testSummary = result.summary\n          this.hasResults = true\n          this.$message.success('所有测试已完成')\n          \n          // 渲染图表\n          this.$nextTick(() => {\n            this.renderResultChart()\n          })\n        } else {\n          this.$message.error('测试运行失败: ' + result.error)\n        }\n      } catch (error) {\n        this.$message.error('测试运行异常: ' + error.message)\n      } finally {\n        this.isRunning = false\n        this.testProgress = {\n          progress: 0,\n          status: 'normal',\n          phase: '',\n          message: '',\n          currentSuite: null\n        }\n      }\n    },\n    \n    // 处理测试进度\n    handleTestProgress(progress) {\n      this.testProgress = {\n        ...progress,\n        status: progress.phase === 'completed' ? 'success' : 'active'\n      }\n    },\n    \n    // 停止测试\n    stopTests() {\n      this.testManager.stopTests()\n      this.isRunning = false\n      this.$message.info('测试已停止')\n    },\n    \n    // 清空结果\n    clearResults() {\n      this.hasResults = false\n      this.testResults = {}\n      this.testSummary = {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      }\n      this.testManager.clearResults()\n      this.$message.success('测试结果已清空')\n    },\n    \n    // 导出报告\n    exportReport() {\n      try {\n        const reportContent = this.testManager.exportReport('html')\n        const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = `测试报告_${new Date().toISOString().slice(0, 10)}.html`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n        \n        this.$message.success('测试报告已导出')\n      } catch (error) {\n        this.$message.error('导出失败: ' + error.message)\n      }\n    },\n    \n    // 显示指标详情\n    showMetricsDetail(testCase) {\n      this.selectedTestMetrics = testCase\n      this.metricsModalVisible = true\n    },\n    \n    // 渲染结果图表\n    renderResultChart() {\n      // 这里可以集成图表库（如ECharts）来渲染测试结果图表\n      // 为了简化，这里只是一个占位方法\n      console.log('渲染测试结果图表')\n    },\n    \n    // 获取套件显示名称\n    getSuiteDisplayName(suiteName) {\n      const names = {\n        functional: '功能测试',\n        performance: '性能测试',\n        userExperience: '用户体验'\n      }\n      return names[suiteName] || suiteName\n    },\n    \n    // 获取状态颜色\n    getStatusColor(status) {\n      const colors = {\n        passed: 'green',\n        failed: 'red',\n        skipped: 'orange',\n        running: 'blue'\n      }\n      return colors[status] || 'default'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const texts = {\n        passed: '通过',\n        failed: '失败',\n        skipped: '跳过',\n        running: '运行中'\n      }\n      return texts[status] || status\n    },\n    \n    // 获取优先级颜色\n    getPriorityColor(priority) {\n      const colors = {\n        high: 'red',\n        medium: 'orange',\n        low: 'blue'\n      }\n      return colors[priority] || 'default'\n    },\n    \n    // 格式化持续时间\n    formatDuration(ms) {\n      if (ms < 1000) {\n        return `${Math.round(ms)}ms`\n      } else if (ms < 60000) {\n        return `${(ms / 1000).toFixed(1)}s`\n      } else {\n        return `${(ms / 60000).toFixed(1)}min`\n      }\n    },\n    \n    // 格式化指标标签\n    formatMetricLabel(key) {\n      const labels = {\n        executionTime: '执行时间',\n        memoryUsage: '内存使用',\n        renderTime: '渲染时间',\n        loadTime: '加载时间',\n        responseTime: '响应时间',\n        fileSize: '文件大小',\n        imageCount: '图片数量',\n        formulaCount: '公式数量'\n      }\n      return labels[key] || key\n    },\n    \n    // 格式化指标值\n    formatMetricValue(key, value) {\n      if (key.includes('Time') || key.includes('Duration')) {\n        return this.formatDuration(value)\n      } else if (key.includes('Size')) {\n        return `${Math.round(value / 1024)}KB`\n      } else if (typeof value === 'number') {\n        return value.toLocaleString()\n      } else {\n        return value\n      }\n    },\n    \n    // 获取性能评分\n    getPerformanceScore() {\n      const successRate = this.testSummary.successRate\n      const issueCount = this.testSummary.performanceIssues.length\n      \n      let score = successRate\n      score -= issueCount * 5 // 每个性能问题扣5分\n      \n      return Math.max(0, Math.round(score))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comprehensive-test-suite {\n  padding: 24px;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.test-control-panel {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.test-actions {\n  text-align: right;\n}\n\n.suite-selected {\n  border: 2px solid #1890ff;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\n}\n\n.ant-statistic-group {\n  display: flex;\n  justify-content: space-around;\n  background: white;\n  padding: 16px;\n  border-radius: 6px;\n  border: 1px solid #e8e8e8;\n}\n\n.test-chart,\n.performance-chart {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.performance-metrics {\n  display: flex;\n  align-items: center;\n}\n\n.performance-metrics p {\n  margin-bottom: 8px;\n}\n\n.ant-card {\n  margin-bottom: 16px;\n}\n\n.ant-card-head-title {\n  font-weight: 600;\n}\n</style>\n"], "mappings": ";;;;;;+CA6UA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,gBAAAkB,OAAA,CAAAlB,CAAA,KAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,CAAAoB,OAAA,EAAAC,IAAA,WAAAnD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,EAAAqB,IAAA,WAAAnD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAAgD,2BAAA,eAAArD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAkD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA3B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAqB,KAAA,sCAAA/C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAsD,IAAA,eAAAlD,CAAA,CAAAmD,MAAA,GAAA9C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAoD,QAAA,MAAA3C,CAAA,QAAAE,CAAA,GAAA0C,mBAAA,CAAA5C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAmD,MAAA,EAAAnD,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAuD,KAAA,GAAAvD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAmD,MAAA,QAAAjD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAwD,iBAAA,CAAAxD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAmD,MAAA,IAAAnD,CAAA,CAAAyD,MAAA,WAAAzD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAkD,IAAA,GAAArB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAA0B,IAAA,EAAAlD,CAAA,CAAAkD,IAAA,kBAAAjB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAmD,MAAA,YAAAnD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA6B,oBAAA1D,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAsD,MAAA,EAAAjD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAuD,QAAA,qBAAApD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAkD,MAAA,KAAA7D,CAAA,CAAAsD,MAAA,aAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAyD,mBAAA,CAAA1D,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAsD,MAAA,kBAAAnD,CAAA,KAAAH,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,uCAAA3D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA2C,IAAA,IAAArD,CAAA,CAAAF,CAAA,CAAAiE,UAAA,IAAArD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAAgE,IAAA,GAAAlE,CAAA,CAAAmE,OAAA,eAAAjE,CAAA,CAAAsD,MAAA,KAAAtD,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,sCAAA9D,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,cAAAiC,aAAAnE,CAAA,QAAAD,CAAA,KAAAqE,MAAA,EAAApE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAsE,QAAA,GAAArE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAuE,UAAA,GAAAtE,CAAA,KAAAD,CAAA,CAAAwE,QAAA,GAAAvE,CAAA,WAAAwE,UAAA,CAAAC,IAAA,CAAA1E,CAAA,cAAA2E,cAAA1E,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA2E,UAAA,QAAA5E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA2E,UAAA,GAAA5E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAwE,UAAA,MAAAJ,MAAA,aAAApE,CAAA,CAAA4C,OAAA,CAAAuB,YAAA,cAAAS,KAAA,iBAAAnC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAkE,IAAA,SAAAlE,CAAA,OAAA8E,KAAA,CAAA9E,CAAA,CAAA+E,MAAA,SAAAxE,CAAA,OAAAG,CAAA,YAAAwD,KAAA,aAAA3D,CAAA,GAAAP,CAAA,CAAA+E,MAAA,OAAA1E,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA2D,IAAA,CAAAzD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA2D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAxD,CAAA,CAAAwD,IAAA,GAAAxD,CAAA,gBAAAsD,SAAA,CAAAf,OAAA,CAAAjD,CAAA,kCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA4C,WAAA,GAAA9D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAiF,mBAAA,aAAAhF,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAiF,WAAA,WAAAlF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAAgF,WAAA,IAAAhF,CAAA,CAAAmF,IAAA,OAAAnF,CAAA,CAAAoF,IAAA,aAAAnF,CAAA,WAAAE,MAAA,CAAAkF,cAAA,GAAAlF,MAAA,CAAAkF,cAAA,CAAApF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAqF,SAAA,GAAAjD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,aAAAkD,OAAA,EAAAlD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAwF,KAAA,aAAAvF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA+E,OAAA,OAAA7E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAiF,mBAAA,CAAA/E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAsD,IAAA,GAAAd,IAAA,WAAAnD,CAAA,WAAAA,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAsD,IAAA,WAAAtB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAA0F,IAAA,aAAAzF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAwE,IAAA,CAAArE,CAAA,UAAAH,CAAA,CAAAyF,OAAA,aAAAzB,KAAA,WAAAhE,CAAA,CAAA6E,MAAA,SAAA9E,CAAA,GAAAC,CAAA,CAAA0F,GAAA,QAAA3F,CAAA,IAAAD,CAAA,SAAAkE,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAlE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA8E,WAAA,EAAAzD,OAAA,EAAAoD,KAAA,WAAAA,MAAA7E,CAAA,aAAA6F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA3D,CAAA,OAAAsD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA3B,GAAA,GAAA5B,CAAA,OAAAwE,UAAA,CAAA5B,OAAA,CAAA8B,aAAA,IAAA3E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA4F,MAAA,OAAAzF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA4E,KAAA,EAAA5E,CAAA,CAAA6F,KAAA,cAAA7F,CAAA,IAAAD,CAAA,MAAA+F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAAtD,CAAA,QAAAwE,UAAA,IAAAG,UAAA,kBAAA3E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAoE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA7D,CAAA,aAAAuD,IAAA,QAAAvD,CAAA,MAAAE,CAAA,kBAAAgG,OAAA7F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAAgE,IAAA,GAAA7D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAkE,UAAA,CAAAM,MAAA,MAAAxE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA+D,UAAA,CAAAlE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAkE,UAAA,iBAAAlE,CAAA,CAAA2D,MAAA,SAAA6B,MAAA,aAAAxF,CAAA,CAAA2D,MAAA,SAAAwB,IAAA,QAAA/E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA6E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,gBAAAuB,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,cAAAzD,CAAA,aAAA+E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,qBAAAtD,CAAA,QAAAsC,KAAA,qDAAAuC,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,YAAAT,MAAA,WAAAA,OAAA7D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAuE,UAAA,CAAAM,MAAA,MAAA7E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAkE,UAAA,CAAAvE,CAAA,OAAAK,CAAA,CAAA8D,MAAA,SAAAwB,IAAA,IAAAxF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAsF,IAAA,GAAAtF,CAAA,CAAAgE,UAAA,QAAA7D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA2D,MAAA,IAAArE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA6D,UAAA,KAAA7D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAkE,UAAA,cAAAhE,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA8C,MAAA,gBAAAU,IAAA,GAAAxD,CAAA,CAAA6D,UAAA,EAAApC,CAAA,SAAAgE,QAAA,CAAAvF,CAAA,MAAAuF,QAAA,WAAAA,SAAAlG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAsC,IAAA,GAAAjE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAqE,IAAA,QAAApE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA2B,MAAA,kBAAAU,IAAA,yBAAAjE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAkE,IAAA,GAAAlE,CAAA,GAAAmC,CAAA,KAAAiE,MAAA,WAAAA,OAAAnG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAqE,UAAA,KAAAtE,CAAA,cAAAkG,QAAA,CAAAjG,CAAA,CAAA0E,UAAA,EAAA1E,CAAA,CAAAsE,QAAA,GAAAG,aAAA,CAAAzE,CAAA,GAAAiC,CAAA,OAAAkE,KAAA,WAAAC,OAAArG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAmE,MAAA,KAAApE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAA0E,UAAA,kBAAAvE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA8C,aAAA,CAAAzE,CAAA,YAAAK,CAAA,YAAA+C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAvG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAoD,QAAA,KAAA5C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAiE,UAAA,EAAA/D,CAAA,EAAAiE,OAAA,EAAA9D,CAAA,oBAAAmD,MAAA,UAAA3B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAwG,mBAAAnG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA6C,IAAA,GAAAtD,CAAA,CAAAe,CAAA,IAAAyE,OAAA,CAAAvC,OAAA,CAAAlC,CAAA,EAAAoC,IAAA,CAAAlD,CAAA,EAAAK,CAAA;AAAA,SAAAkG,kBAAApG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA0G,SAAA,aAAAjB,OAAA,WAAAvF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAA4G,MAAAvG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,UAAAxG,CAAA,cAAAwG,OAAAxG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,WAAAxG,CAAA,KAAAuG,KAAA;AAAA,OAAAE,WAAA;AACA,OAAAC,mBAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,uBAAA;AAEA;EACA9B,IAAA;EACA+B,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,WAAA,MAAAL,WAAA;MAEA;MACAM,eAAA,MAAAL,mBAAA;MACAM,gBAAA,MAAAL,oBAAA;MACAM,mBAAA,MAAAL,uBAAA;MAEA;MACAM,cAAA;MAEA;MACAC,SAAA;MACAC,UAAA;MAEA;MACAC,YAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,OAAA;QACAC,YAAA;MACA;MAEA;MACAC,WAAA;MACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,WAAA;QACAC,iBAAA;QACAC,eAAA;MACA;MAEA;MACAC,mBAAA;MACAC,oBAAA;MACAC,uBAAA;MAEA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;MACA,GACA;QACAF,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAE,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MAEA;MACAC,mBAAA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAAjC,cAAA,CAAAxC,MAAA;IACA;IAEA0E,cAAA,WAAAA,eAAA;MACA,IAAAC,KAAA;MACA,SAAAnC,cAAA,CAAAoC,QAAA,gBAAAD,KAAA,SAAAf,mBAAA;MACA,SAAApB,cAAA,CAAAoC,QAAA,iBAAAD,KAAA,SAAAd,oBAAA;MACA,SAAArB,cAAA,CAAAoC,QAAA,oBAAAD,KAAA,SAAAb,uBAAA;MACA,OAAAa,KAAA;IACA;IAEAE,cAAA,WAAAA,eAAA;MACA,YAAA3B,WAAA,CAAAE,UAAA;IACA;IAEAK,WAAA,WAAAA,YAAA;MACA,OAAAqB,IAAA,CAAAC,KAAA,MAAA7B,WAAA,CAAAO,WAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA;IACA;IACAF,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA7C,WAAA,CAAAgD,iBAAA,oBAAA/C,eAAA;MACA,KAAAD,WAAA,CAAAgD,iBAAA,qBAAA9C,gBAAA;MACA,KAAAF,WAAA,CAAAgD,iBAAA,wBAAA7C,mBAAA;;MAEA;MACA,KAAAqB,mBAAA,QAAAvB,eAAA,CAAAgD,YAAA,GAAArF,MAAA;MACA,KAAA6D,oBAAA,QAAAvB,gBAAA,CAAA+C,YAAA,GAAArF,MAAA;MACA,KAAA8D,uBAAA,QAAAvB,mBAAA,CAAA8C,YAAA,GAAArF,MAAA;IACA;IAEA;IACAkF,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAI,YAAA;IACA;IAEA;IACAC,WAAA;MAAA,IAAAC,YAAA,GAAA9D,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,UAAAoF,QAAA;QAAA,IAAAC,KAAA;QAAA,IAAAC,WAAA,EAAAC,MAAA;QAAA,OAAA5K,mBAAA,GAAAuB,IAAA,UAAAsJ,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhF,IAAA,GAAAgF,QAAA,CAAA3G,IAAA;YAAA;cAAA,MACA,KAAAqD,cAAA,CAAAxC,MAAA;gBAAA8F,QAAA,CAAA3G,IAAA;gBAAA;cAAA;cACA,KAAA4G,QAAA,CAAAC,OAAA;cAAA,OAAAF,QAAA,CAAA/G,MAAA;YAAA;cAIA,KAAA0D,SAAA;cACA,KAAAC,UAAA;cACA,KAAAO,WAAA;cACA,KAAAC,WAAA;gBACAC,WAAA;gBACAC,UAAA;gBACAC,WAAA;gBACAC,WAAA;gBACAC,YAAA;gBACAC,aAAA;gBACAC,WAAA;gBACAC,iBAAA;gBACAC,eAAA;cACA;cAAAmC,QAAA,CAAAhF,IAAA;cAGA;cACA,KAAAsB,WAAA,CAAA6D,YAAA;;cAEA;cACAN,WAAA,OAAA5D,WAAA;cACA,SAAAS,cAAA,CAAAoC,QAAA;gBACAe,WAAA,CAAAP,iBAAA,oBAAA/C,eAAA;cACA;cACA,SAAAG,cAAA,CAAAoC,QAAA;gBACAe,WAAA,CAAAP,iBAAA,qBAAA9C,gBAAA;cACA;cACA,SAAAE,cAAA,CAAAoC,QAAA;gBACAe,WAAA,CAAAP,iBAAA,wBAAA7C,mBAAA;cACA;;cAEA;cAAAuD,QAAA,CAAA3G,IAAA;cAAA,OACAwG,WAAA,CAAAJ,WAAA,UAAAW,kBAAA;YAAA;cAAAN,MAAA,GAAAE,QAAA,CAAAlH,IAAA;cAEA,IAAAgH,MAAA,CAAAO,OAAA;gBACA,KAAAlD,WAAA,GAAA2C,MAAA,CAAAQ,OAAA;gBACA,KAAAlD,WAAA,GAAA0C,MAAA,CAAAS,OAAA;gBACA,KAAA3D,UAAA;gBACA,KAAAqD,QAAA,CAAAI,OAAA;;gBAEA;gBACA,KAAAG,SAAA;kBACAZ,KAAA,CAAAa,iBAAA;gBACA;cACA;gBACA,KAAAR,QAAA,CAAAS,KAAA,cAAAZ,MAAA,CAAAY,KAAA;cACA;cAAAV,QAAA,CAAA3G,IAAA;cAAA;YAAA;cAAA2G,QAAA,CAAAhF,IAAA;cAAAgF,QAAA,CAAAW,EAAA,GAAAX,QAAA;cAEA,KAAAC,QAAA,CAAAS,KAAA,cAAAV,QAAA,CAAAW,EAAA,CAAA1D,OAAA;YAAA;cAAA+C,QAAA,CAAAhF,IAAA;cAEA,KAAA2B,SAAA;cACA,KAAAE,YAAA;gBACAC,QAAA;gBACAC,MAAA;gBACAC,KAAA;gBACAC,OAAA;gBACAC,YAAA;cACA;cAAA,OAAA8C,QAAA,CAAAzE,MAAA;YAAA;YAAA;cAAA,OAAAyE,QAAA,CAAA7E,IAAA;UAAA;QAAA,GAAAwE,OAAA;MAAA;MAAA,SAAAF,YAAA;QAAA,OAAAC,YAAA,CAAA5D,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA4D,WAAA;IAAA;IAIA;IACAW,kBAAA,WAAAA,mBAAAtD,QAAA;MACA,KAAAD,YAAA,GAAA+D,aAAA,CAAAA,aAAA,KACA9D,QAAA;QACAC,MAAA,EAAAD,QAAA,CAAAE,KAAA;MAAA,EACA;IACA;IAEA;IACA6D,SAAA,WAAAA,UAAA;MACA,KAAAvE,WAAA,CAAAuE,SAAA;MACA,KAAAlE,SAAA;MACA,KAAAsD,QAAA,CAAAa,IAAA;IACA;IAEA;IACAX,YAAA,WAAAA,aAAA;MACA,KAAAvD,UAAA;MACA,KAAAO,WAAA;MACA,KAAAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,aAAA;QACAC,WAAA;QACAC,iBAAA;QACAC,eAAA;MACA;MACA,KAAAvB,WAAA,CAAA6D,YAAA;MACA,KAAAF,QAAA,CAAAI,OAAA;IACA;IAEA;IACAU,YAAA,WAAAA,aAAA;MACA;QACA,IAAAC,aAAA,QAAA1E,WAAA,CAAAyE,YAAA;QACA,IAAAE,IAAA,OAAAC,IAAA,EAAAF,aAAA;UAAAjK,IAAA;QAAA;QACA,IAAAoK,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,IAAA,CAAAI,QAAA,+BAAAC,MAAA,KAAAC,IAAA,GAAAC,WAAA,GAAA5G,KAAA;QACAsG,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAT,IAAA;QACAA,IAAA,CAAAU,KAAA;QACAT,QAAA,CAAAO,IAAA,CAAAG,WAAA,CAAAX,IAAA;QACAH,MAAA,CAAAC,GAAA,CAAAc,eAAA,CAAAhB,GAAA;QAEA,KAAAlB,QAAA,CAAAI,OAAA;MACA,SAAAK,KAAA;QACA,KAAAT,QAAA,CAAAS,KAAA,YAAAA,KAAA,CAAAzD,OAAA;MACA;IACA;IAEA;IACAmF,iBAAA,WAAAA,kBAAAC,QAAA;MACA,KAAA5D,mBAAA,GAAA4D,QAAA;MACA,KAAA7D,mBAAA;IACA;IAEA;IACAiC,iBAAA,WAAAA,kBAAA;MACA;MACA;MACA6B,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,SAAA;MACA,IAAAC,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,cAAA;MACA;MACA,OAAAH,KAAA,CAAAD,SAAA,KAAAA,SAAA;IACA;IAEA;IACAK,cAAA,WAAAA,eAAA/F,MAAA;MACA,IAAAgG,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACA,OAAAJ,MAAA,CAAAhG,MAAA;IACA;IAEA;IACAqG,aAAA,WAAAA,cAAArG,MAAA;MACA,IAAAsG,KAAA;QACAL,MAAA;QACAC,MAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACA,OAAAE,KAAA,CAAAtG,MAAA,KAAAA,MAAA;IACA;IAEA;IACAuG,gBAAA,WAAAA,iBAAAC,QAAA;MACA,IAAAR,MAAA;QACAS,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACA,OAAAX,MAAA,CAAAQ,QAAA;IACA;IAEA;IACAI,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAA,EAAA;QACA,UAAAhC,MAAA,CAAA5C,IAAA,CAAAC,KAAA,CAAA2E,EAAA;MACA,WAAAA,EAAA;QACA,UAAAhC,MAAA,EAAAgC,EAAA,SAAAC,OAAA;MACA;QACA,UAAAjC,MAAA,EAAAgC,EAAA,UAAAC,OAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA1F,GAAA;MACA,IAAA2F,MAAA;QACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,OAAAR,MAAA,CAAA3F,GAAA,KAAAA,GAAA;IACA;IAEA;IACAoG,iBAAA,WAAAA,kBAAApG,GAAA,EAAAxI,KAAA;MACA,IAAAwI,GAAA,CAAAU,QAAA,YAAAV,GAAA,CAAAU,QAAA;QACA,YAAA6E,cAAA,CAAA/N,KAAA;MACA,WAAAwI,GAAA,CAAAU,QAAA;QACA,UAAA8C,MAAA,CAAA5C,IAAA,CAAAC,KAAA,CAAArJ,KAAA;MACA,kBAAAA,KAAA;QACA,OAAAA,KAAA,CAAA6O,cAAA;MACA;QACA,OAAA7O,KAAA;MACA;IACA;IAEA;IACA8O,mBAAA,WAAAA,oBAAA;MACA,IAAA/G,WAAA,QAAAP,WAAA,CAAAO,WAAA;MACA,IAAAgH,UAAA,QAAAvH,WAAA,CAAAQ,iBAAA,CAAA1D,MAAA;MAEA,IAAA0K,KAAA,GAAAjH,WAAA;MACAiH,KAAA,IAAAD,UAAA;;MAEA,OAAA3F,IAAA,CAAA6F,GAAA,IAAA7F,IAAA,CAAAC,KAAA,CAAA2F,KAAA;IACA;EACA;AACA", "ignoreList": []}]}