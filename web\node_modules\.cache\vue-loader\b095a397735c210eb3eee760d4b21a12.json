{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue?vue&type=template&id=3e5c3c68&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<!-- 定义在这里的参数都是不可在外部覆盖的，防止出现问题 -->\n<j-select-biz-component\n  :value=\"value\"\n  :ellipsisLength=\"25\"\n  :listUrl=\"url.list\"\n  :columns=\"columns\"\n  v-on=\"$listeners\"\n  v-bind=\"attrs\"\n/>\n", null]}