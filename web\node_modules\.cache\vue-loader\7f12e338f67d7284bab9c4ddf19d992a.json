{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue?vue&type=style&index=0&id=295f035e&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.steps {\n  max-width: 750px;\n  margin: 16px auto;\n}\n", {"version": 3, "sources": ["StepForm.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA", "file": "StepForm.vue", "sourceRoot": "src/views/form/stepForm", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-steps class=\"steps\" :current=\"currentTab\">\n      <a-step title=\"填写转账信息\" />\n      <a-step title=\"确认转账信息\" />\n      <a-step title=\"完成\" />\n    </a-steps>\n    <div class=\"content\">\n      <step1 v-if=\"currentTab === 0\" @nextStep=\"nextStep\"/>\n      <step2 v-if=\"currentTab === 1\" @nextStep=\"nextStep\" @prevStep=\"prevStep\"/>\n      <step3 v-if=\"currentTab === 2\" @prevStep=\"prevStep\" @finish=\"finish\"/>\n    </div>\n  </a-card>\n</template>\n\n<script>\n  import Step1 from './Step1'\n  import Step2 from './Step2'\n  import Step3 from './Step3'\n\n  export default {\n    name: \"StepForm\",\n    components: {\n      Step1,\n      Step2,\n      Step3\n    },\n    data () {\n      return {\n        description: '将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。',\n        currentTab: 0,\n\n        // form\n        form: null,\n      }\n    },\n    methods: {\n\n      // handler\n      nextStep () {\n        if (this.currentTab < 2) {\n          this.currentTab += 1\n        }\n      },\n      prevStep () {\n        if (this.currentTab > 0) {\n          this.currentTab -= 1\n        }\n      },\n      finish () {\n        this.currentTab = 0\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .steps {\n    max-width: 750px;\n    margin: 16px auto;\n  }\n</style>"]}]}