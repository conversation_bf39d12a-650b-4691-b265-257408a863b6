{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\exception\\ExceptionPage.vue?vue&type=style&index=0&id=75112974&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\exception\\ExceptionPage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.exception {\n  min-height: 500px;\n  height: 80%;\n  align-items: center;\n  text-align: center;\n  margin-top: 150px;\n  .img {\n    display: inline-block;\n    padding-right: 52px;\n    zoom: 1;\n    img {\n      height: 360px;\n      max-width: 430px;\n    }\n  }\n  .content {\n    display: inline-block;\n    flex: auto;\n    h1 {\n      color: #434e59;\n      font-size: 72px;\n      font-weight: 600;\n      line-height: 72px;\n      margin-bottom: 24px;\n    }\n    .desc {\n      color: rgba(0, 0, 0, .45);\n      font-size: 20px;\n      line-height: 28px;\n      margin-bottom: 16px;\n    }\n  }\n}\n\n.mobile {\n  .exception {\n    margin-top: 30px;\n    .img {\n      padding-right: unset;\n\n      img {\n        height: 40%;\n        max-width: 80%;\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["ExceptionPage.vue"], "names": [], "mappings": ";AAwFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ExceptionPage.vue", "sourceRoot": "src/views/exception", "sourcesContent": ["<template>\n  <div class=\"exception\">\n    <div class=\"img\">\n      <img :src=\"config[type].img\"/>\n    </div>\n    <div class=\"content\">\n      <h1>{{ config[type].title }}</h1>\n      <div class=\"desc\">{{ config[type].desc }}</div>\n      <div class=\"action\">\n        <a-button type=\"primary\" @click=\"handleToHome\">返回首页</a-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n  import types from './type'\n\n  export default {\n    name: \"Exception\",\n    props: {\n      type: {\n        type: String,\n        default: '404'\n      }\n    },\n    data() {\n      return {\n        config: types\n      }\n    },\n    methods: {\n      handleToHome () {\n        this.$router.push({ name: 'dashboard' })\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .exception {\n    min-height: 500px;\n    height: 80%;\n    align-items: center;\n    text-align: center;\n    margin-top: 150px;\n    .img {\n      display: inline-block;\n      padding-right: 52px;\n      zoom: 1;\n      img {\n        height: 360px;\n        max-width: 430px;\n      }\n    }\n    .content {\n      display: inline-block;\n      flex: auto;\n      h1 {\n        color: #434e59;\n        font-size: 72px;\n        font-weight: 600;\n        line-height: 72px;\n        margin-bottom: 24px;\n      }\n      .desc {\n        color: rgba(0, 0, 0, .45);\n        font-size: 20px;\n        line-height: 28px;\n        margin-bottom: 16px;\n      }\n    }\n  }\n\n  .mobile {\n    .exception {\n      margin-top: 30px;\n      .img {\n        padding-right: unset;\n\n        img {\n          height: 40%;\n          max-width: 80%;\n        }\n      }\n    }\n  }\n</style>"]}]}