{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Pie.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Pie.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  const DataSet = require('@antv/data-set')\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'Pie',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      height: {\n        type: Number,\n        default: 254\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { item: '示例一', count: 40 },\n          { item: '示例二', count: 21 },\n          { item: '示例三', count: 17 },\n          { item: '示例四', count: 13 },\n          { item: '示例五', count: 9 }\n        ]\n      }\n    },\n    data() {\n      return {\n        scale: [{\n          dataKey: 'percent',\n          min: 0,\n          formatter: '.0%'\n        }],\n        pieStyle: {\n          stroke: '#fff',\n          lineWidth: 1\n        },\n        labelConfig: ['percent', {\n          formatter: (val, item) => {\n            return item.point.item + ': ' + val\n          }\n        }]\n      }\n    },\n    computed: {\n      data() {\n        let dv = new DataSet.View().source(this.dataSource)\n        // 计算数据百分比\n        dv.transform({\n          type: 'percent',\n          field: 'count',\n          dimension: 'item',\n          as: 'percent'\n        })\n        return dv.rows\n      }\n    }\n  }\n", {"version": 3, "sources": ["Pie.vue"], "names": [], "mappings": ";AAWA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Pie.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :onClick=\"handleClick\">\n    <v-tooltip :showTitle=\"false\" dataKey=\"item*percent\"/>\n    <v-axis/>\n    <v-legend dataKey=\"item\"/>\n    <v-pie position=\"percent\" color=\"item\" :v-style=\"pieStyle\" :label=\"labelConfig\"/>\n    <v-coord type=\"theta\"/>\n  </v-chart>\n</template>\n\n<script>\n  const DataSet = require('@antv/data-set')\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'Pie',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      height: {\n        type: Number,\n        default: 254\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { item: '示例一', count: 40 },\n          { item: '示例二', count: 21 },\n          { item: '示例三', count: 17 },\n          { item: '示例四', count: 13 },\n          { item: '示例五', count: 9 }\n        ]\n      }\n    },\n    data() {\n      return {\n        scale: [{\n          dataKey: 'percent',\n          min: 0,\n          formatter: '.0%'\n        }],\n        pieStyle: {\n          stroke: '#fff',\n          lineWidth: 1\n        },\n        labelConfig: ['percent', {\n          formatter: (val, item) => {\n            return item.point.item + ': ' + val\n          }\n        }]\n      }\n    },\n    computed: {\n      data() {\n        let dv = new DataSet.View().source(this.dataSource)\n        // 计算数据百分比\n        dv.transform({\n          type: 'percent',\n          field: 'count',\n          dimension: 'item',\n          as: 'percent'\n        })\n        return dv.rows\n      }\n    }\n  }\n</script>"]}]}