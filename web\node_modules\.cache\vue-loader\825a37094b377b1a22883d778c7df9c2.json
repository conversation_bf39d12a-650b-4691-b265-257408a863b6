{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlinePractise.vue?vue&type=template&id=19f7cb75&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlinePractise.vue", "mtime": 1753195975822}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"practice-container\">\n  <!-- 刷题模式选择 -->\n  <practice-mode-selector\n    v-if=\"!practiseStarted\"\n    :loading=\"loading\"\n    :hasSavedProgress=\"hasSavedProgress\"\n    @start-practise=\"handleStartPractise\"\n    @start-quick-practise=\"handleStartQuickPractise\"\n    @continue-practise=\"handleContinuePractise\"\n    @reset-query=\"handleResetQuery\"\n    ref=\"modeSelector\"\n  />\n\n  <!-- 刷题界面 -->\n  <div v-if=\"practiseStarted\" class=\"practice-area\" :class=\"{'full-screen-mode': isFullScreen, 'horizontal-layout': isFullScreen}\">\n    <!-- 刷题状态区 -->\n    <practice-status-bar\n      :isFullScreen=\"isFullScreen\"\n      :isReviewMode=\"isReviewMode\"\n      :showAnswer=\"showAnswer\"\n      :practiseMode=\"practiseMode\"\n      :practiseCount=\"practiseCount\"\n      :remainingTimeText=\"remainingTimeText\"\n      :questionList=\"questionList\"\n      :currentQuestionIndex=\"currentQuestionIndex\"\n      :answeredQuestions=\"answeredQuestions\"\n      :userAnswersMap=\"userAnswersMap\"\n      :isCollected=\"isCollected\"\n      :collectLoading=\"collectLoading\"\n      @check-answer-correct=\"isAnswerCorrect\"\n      @jump-to-question=\"jumpToQuestion\"\n      @exit-review-mode=\"exitReviewMode\"\n      @toggle-show-answer=\"showAnswer = !showAnswer\"\n      @collect-question=\"collectQuestion\"\n      @exit-practise=\"exitPractise\"\n    />\n\n    <!-- 题目与答题区 -->\n    <a-card class=\"question-container\" :bordered=\"false\">\n      <!-- 客观题显示 -->\n      <question-display\n        v-if=\"currentQuestion.questionType !== 3\"\n        :currentQuestion=\"currentQuestion\"\n        :currentQuestionIndex=\"currentQuestionIndex\"\n        :questionList=\"questionList\"\n        :userAnswer.sync=\"userAnswer\"\n        :userAnswersMap=\"userAnswersMap\"\n        :isReviewMode=\"isReviewMode\"\n        :showAnswer=\"showAnswer\"\n        :currentQuestionStatus=\"currentQuestionStatus\"\n        @prev-question=\"prevQuestion\"\n        @next-question=\"nextQuestion\"\n      />\n\n      <!-- 编程题显示 -->\n      <coding-question\n        v-else\n        :currentQuestion=\"currentQuestion\"\n        :currentQuestionIndex=\"currentQuestionIndex\"\n        :questionList=\"questionList\"\n        :showAnswer=\"showAnswer\"\n        :isFullScreen=\"isFullScreen\"\n        :isReviewMode=\"isReviewMode\"\n        :currentQuestionStatus=\"currentQuestionStatus\"\n        :code.sync=\"code\"\n        :selectedLanguage=\"selectedLanguage\"\n        :supportedLanguages=\"supportedLanguages\"\n        :editorHeight=\"editorHeight\"\n        :editorTheme.sync=\"editorTheme\"\n        :editorFontSize.sync=\"editorFontSize\"\n        :editorTabSize=\"editorTabSize\"\n        :openTestCaseDrawer.sync=\"openTestCaseDrawer\"\n        :testInputMap=\"testInputMap\"\n        :testResultMap=\"testResultMap\"\n        :activeTestCaseIndexMap=\"activeTestCaseIndexMap\"\n        :isSubmitting=\"isSubmitting\"\n        @prev-question=\"prevQuestion\"\n        @next-question=\"nextQuestion\"\n        @language-change=\"handleLanguageChange\"\n        @reset-code=\"resetCode\"\n        @get-last-accepted-code=\"getUserLastAcceptedCode\"\n        @switch-focus-mode=\"switchFocusMode\"\n        @formal-submission=\"handleFormalSubmission\"\n        @update-test-input=\"updateTestInput\"\n        @update-test-result=\"updateTestResult\"\n        @update-active-test-case-index=\"updateActiveTestCaseIndex\"\n        ref=\"codeMirror\"\n      />\n    </a-card>\n\n    <!-- 练习结果弹窗 -->\n    <practice-result-modal\n      v-model=\"practiseCompleted\"\n      :correctCount=\"correctCount\"\n      :incorrectCount=\"incorrectCount\"\n      :unfinishedCount=\"unfinishedCount\"\n      :totalCount=\"questionList.length\"\n      :hasAnsweredQuestions=\"answeredQuestions.length > 0\"\n      @start-new-practise=\"startNewPractise\"\n      @enter-review-mode=\"enterReviewMode\"\n      @close=\"handlePracticeSummaryClose\"\n    />\n  </div>\n</div>\n", null]}