{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue?vue&type=template&id=4083ec1a&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue", "mtime": 1750046294782}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"game-center\">\n  <div class=\"container\">\n    <header>\n      <div class=\"header-content\">\n        <div class=\"title-area\">\n          <h1>休闲游戏中心</h1>\n          <p class=\"subtitle\">放松心情，享受游戏乐趣</p>\n        </div>\n        <div class=\"user-info\">\n          <img :src=\"userAvatar\" alt=\"用户头像\" class=\"user-avatar\"/>\n          <span class=\"user-name\">{{ userName }}</span>\n          <div class=\"coin-display\">\n            <span class=\"coin-icon\"></span>\n            <span class=\"coin-amount\">{{ userCoins }}</span>\n          </div>\n        </div>\n      </div>\n    </header>\n\n    <div class=\"game-grid\">\n      <!-- 游戏卡片列表 -->\n      <game-card\n        v-for=\"game in games\"\n        :key=\"game.id\"\n        :game=\"game\"\n        @play-game=\"openGameModal\"\n      />\n    </div>\n\n    <footer>\n      <p>&copy; 2025 休闲游戏中心 | CFish科技少儿编程</p>\n    </footer>\n  </div>\n\n  <!-- 游戏模态窗口 -->\n  <game-modal\n    v-if=\"showModal\"\n    :game=\"currentGame\"\n    :user-coins=\"userCoins\"\n    :user-name=\"userName\"\n    :user-avatar=\"userAvatar\"\n    @close=\"closeGameModal\"\n    @consume-coins=\"consumeCoins\"\n  />\n\n  <!-- 确认弹窗 -->\n  <confirm-dialog\n    v-if=\"showConfirmDialog\"\n    :message=\"confirmMessage\"\n    :game-cost=\"currentGame ? currentGame.cost : 0\"\n    @confirm=\"handleConfirm\"\n    @cancel=\"showConfirmDialog = false\"\n  />\n</div>\n", null]}