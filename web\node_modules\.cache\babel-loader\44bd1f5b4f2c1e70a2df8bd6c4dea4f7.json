{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getAction } from '@/api/manage';\nexport default {\n  name: \"Analysis\",\n  components: {},\n  data: function data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      html: ''\n    };\n  },\n  created: function created() {\n    var _this = this;\n    getAction(\"/sys/config/getConfig?key=_indexHtml\").then(function (res) {\n      if (res.success) {\n        _this.html = res.result;\n      }\n    });\n  },\n  methods: {}\n};", {"version": 3, "names": ["getAction", "name", "components", "data", "brandName", "$store", "getters", "sysConfig", "html", "created", "_this", "then", "res", "success", "result", "methods"], "sources": ["src/views/dashboard/Index.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"null\">\n    <div v-if=\"html\" v-html=\"html\"></div>\n    <h1 v-else>欢迎使用{{brandName}}</h1>\n  </a-card>\n</template>\n\n<script>\nimport {getAction} from '@/api/manage'\n\n  export default {\n    name: \"Analysis\",\n    components: {\n\n    },\n    data() {\n      return {\n         brandName: this.$store.getters.sysConfig.brandName,\n         html: ''\n      }\n    },\n    created() {\n      getAction(\"/sys/config/getConfig?key=_indexHtml\").then(res=>{\n        if(res.success){\n          this.html = res.result\n        }\n      })\n    },\n    methods: {\n\n    }\n  }\n</script>"], "mappings": "AAQA,SAAAA,SAAA;AAEA;EACAC,IAAA;EACAC,UAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAH,SAAA;MACAI,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAV,SAAA,yCAAAW,IAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACAH,KAAA,CAAAF,IAAA,GAAAI,GAAA,CAAAE,MAAA;MACA;IACA;EACA;EACAC,OAAA,GAEA;AACA", "ignoreList": []}]}