{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue?vue&type=template&id=48d8f00f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue", "mtime": 1753199441584}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"user-enter\"\n  }, [_vm.token ? _c(\"div\", [_c(\"a-avatar\", {\n    staticClass: \"avatar\",\n    attrs: {\n      shape: \"square\",\n      size: 100,\n      src: _vm.avatarUrl\n    }\n  }), _c(\"h3\", [_vm._v(\"欢迎您，\" + _vm._s(_vm.nickname()))]), _c(\"a-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.enter\n    }\n  }, [_vm._v(\"进入系统\")]), _c(\"a-button\", {\n    attrs: {\n      type: \"dashed\"\n    },\n    on: {\n      click: _vm.changeAccount\n    }\n  }, [_vm._v(\"切换账号\")])], 1) : _c(\"div\", [_c(\"a-avatar\", {\n    staticClass: \"avatar\",\n    attrs: {\n      shape: \"square\",\n      size: 100,\n      src: _vm.logo2\n    }\n  }), _c(\"h3\", {\n    staticClass: \"welcome\"\n  }, [_vm._v(\"欢迎来到\" + _vm._s(_vm.brandName))]), _c(\"a-button\", {\n    attrs: {\n      type: \"dashed\"\n    },\n    on: {\n      click: _vm.login\n    }\n  }, [_vm._v(\"登录/注册\")])], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "token", "attrs", "shape", "size", "src", "avatarUrl", "_v", "_s", "nickname", "type", "on", "click", "enter", "changeAccount", "logo2", "brandName", "login", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/UserEnter.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"user-enter\" }, [\n    _vm.token\n      ? _c(\n          \"div\",\n          [\n            _c(\"a-avatar\", {\n              staticClass: \"avatar\",\n              attrs: { shape: \"square\", size: 100, src: _vm.avatarUrl },\n            }),\n            _c(\"h3\", [_vm._v(\"欢迎您，\" + _vm._s(_vm.nickname()))]),\n            _c(\n              \"a-button\",\n              { attrs: { type: \"primary\" }, on: { click: _vm.enter } },\n              [_vm._v(\"进入系统\")]\n            ),\n            _c(\n              \"a-button\",\n              { attrs: { type: \"dashed\" }, on: { click: _vm.changeAccount } },\n              [_vm._v(\"切换账号\")]\n            ),\n          ],\n          1\n        )\n      : _c(\n          \"div\",\n          [\n            _c(\"a-avatar\", {\n              staticClass: \"avatar\",\n              attrs: { shape: \"square\", size: 100, src: _vm.logo2 },\n            }),\n            _c(\"h3\", { staticClass: \"welcome\" }, [\n              _vm._v(\"欢迎来到\" + _vm._s(_vm.brandName)),\n            ]),\n            _c(\n              \"a-button\",\n              { attrs: { type: \"dashed\" }, on: { click: _vm.login } },\n              [_vm._v(\"登录/注册\")]\n            ),\n          ],\n          1\n        ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CH,GAAG,CAACI,KAAK,GACLH,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,QAAQ;IACrBE,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,GAAG;MAAEC,GAAG,EAAER,GAAG,CAACS;IAAU;EAC1D,CAAC,CAAC,EACFR,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACU,EAAE,CAAC,MAAM,GAAGV,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACnDX,EAAE,CACA,UAAU,EACV;IAAEI,KAAK,EAAE;MAAEQ,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACgB;IAAM;EAAE,CAAC,EACxD,CAAChB,GAAG,CAACU,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDT,EAAE,CACA,UAAU,EACV;IAAEI,KAAK,EAAE;MAAEQ,IAAI,EAAE;IAAS,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACiB;IAAc;EAAE,CAAC,EAC/D,CAACjB,GAAG,CAACU,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,GACDT,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,QAAQ;IACrBE,KAAK,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,GAAG;MAAEC,GAAG,EAAER,GAAG,CAACkB;IAAM;EACtD,CAAC,CAAC,EACFjB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACnCH,GAAG,CAACU,EAAE,CAAC,MAAM,GAAGV,GAAG,CAACW,EAAE,CAACX,GAAG,CAACmB,SAAS,CAAC,CAAC,CACvC,CAAC,EACFlB,EAAE,CACA,UAAU,EACV;IAAEI,KAAK,EAAE;MAAEQ,IAAI,EAAE;IAAS,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEf,GAAG,CAACoB;IAAM;EAAE,CAAC,EACvD,CAACpB,GAAG,CAACU,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,CACF,EACD,CACF,CAAC,CACN,CAAC;AACJ,CAAC;AACD,IAAIW,eAAe,GAAG,EAAE;AACxBtB,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}]}