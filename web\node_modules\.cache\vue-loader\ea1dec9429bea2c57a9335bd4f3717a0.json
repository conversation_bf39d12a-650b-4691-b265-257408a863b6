{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexBdc.vue?vue&type=style&index=0&id=51655204&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexBdc.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.extra-wrapper {\n  line-height: 55px;\n  padding-right: 24px;\n\n  .extra-item {\n    display: inline-block;\n    margin-right: 24px;\n\n    a {\n      margin-left: 24px;\n    }\n  }\n}\n\n.item-group {\n  padding: 20px 0 8px 24px;\n  font-size: 0;\n  a {\n    color: rgba(0, 0, 0, 0.65);\n    display: inline-block;\n    font-size: 14px;\n    margin-bottom: 13px;\n    width: 25%;\n  }\n}\n\n.item-group {\n  .more-btn {\n    margin-bottom: 13px;\n    text-align: center;\n  }\n}\n\n.list-content-item {\n  color: rgba(0, 0, 0, .45);\n  display: inline-block;\n  vertical-align: middle;\n  font-size: 14px;\n  margin-left: 40px;\n}\n\n@media only screen and (min-width: 1600px) {\n  .list-content-item{\n    margin-left:60px;\n  }\n}\n\n@media only screen and (max-width: 1300px) {\n  .list-content-item{\n    margin-left:20px;\n  }\n  .width-hidden4{\n    display:none\n  }\n}\n.list-content-item{\n  span{line-height: 20px;}\n}\n.list-content-item{\n  p{margin-top: 4px;margin-bottom:0;line-height:22px;}\n}\n.anty-list-cust {\n  .ant-list-item-meta{flex: 0.3 !important;}\n}\n.anty-list-cust {\n  .ant-list-item-content{flex:1 !important; justify-content:flex-start !important;margin-left: 20px;}\n}\n\n\n", {"version": 3, "sources": ["IndexBdc.vue"], "names": [], "mappings": ";AAugBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexBdc.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-row :gutter=\"24\">\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"受理量\" :total=\"cardCount.sll | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-area :datasource=\"chartData.sll\" />\n          </div>\n          <template slot=\"footer\">今日受理量：<span>{{ todaySll }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"办结量\" :total=\"cardCount.bjl | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-area :datasource=\"chartData.bjl\"/>\n          </div>\n          <template slot=\"footer\">今日办结量：<span>{{ todayBjl }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"用户受理量\" :total=\"cardCount.isll | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-bar :datasource=\"chartData.isll\" :height=\"50\"/>\n          </div>\n          <template slot=\"footer\">用户今日受理量：<span>{{ todayISll }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"用户办结量\" :total=\"cardCount.ibjl | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-bar :datasource=\"chartData.ibjl\" :height=\"50\"/>\n          </div>\n          <template slot=\"footer\">用户今日办结量：<span>{{ todayIBjl }}</span></template>\n        </chart-card>\n      </a-col>\n    </a-row>\n\n    <a-card :loading=\"loading\" :bordered=\"false\" :body-style=\"{padding: '0'}\">\n      <div class=\"salesCard\">\n        <a-tabs default-active-key=\"1\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n          <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n            <div class=\"extra-item\">\n              <a>今日</a>\n              <a>本周</a>\n              <a>本月</a>\n              <a>本年</a>\n            </div>\n            <a-range-picker :style=\"{width: '256px'}\" />\n          </div>\n\n          <a-tab-pane loading=\"true\" tab=\"受理监管\" key=\"1\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <index-bar title=\"受理量统计\" />\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"交互监管\" key=\"2\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <bar-multid :sourceData=\"jhjgData\" :fields=\"jhjgFields\" title=\"平台与部门交互量统计\"></bar-multid>\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"存储监管\" key=\"4\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <a-row>\n                  <template v-if=\"diskInfo && diskInfo.length>0\">\n                    <a-col :span=\"12\" v-for=\"(item,index) in diskInfo\" :key=\" 'diskInfo'+index \">\n                      <dash-chart-demo :title=\"item.name\" :datasource=\"item.restPPT\"></dash-chart-demo>\n                    </a-col>\n                  </template>\n                </a-row>\n              </a-col>\n\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"10\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n        </a-tabs>\n\n      </div>\n    </a-card>\n\n    <a-row :gutter=\"12\">\n      <a-card :loading=\"loading\" :class=\"{ 'anty-list-cust':true }\" :bordered=\"false\" :style=\"{ marginTop: '24px' }\">\n\n        <a-tabs v-model=\"indexBottomTab\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n          <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n            <a-radio-group v-model=\"indexRegisterType\" @change=\"changeRegisterType\">\n              <a-radio-button value=\"转移登记\">转移登记</a-radio-button>\n              <a-radio-button value=\"抵押登记\">抵押登记</a-radio-button>\n              <a-radio-button value=\"\">所有</a-radio-button>\n            </a-radio-group>\n          </div>\n\n          <a-tab-pane loading=\"true\" tab=\"业务流程限时监管\" key=\"1\">\n\n            <a-table :dataSource=\"dataSource1\" size=\"default\" rowKey=\"id\" :columns=\"columns\" :pagination=\"ipagination1\" @change=\"tableChange1\">\n              <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n                <a-progress :strokeColor=\"getPercentColor(record.flowRate)\" :format=\"getPercentFormat\" :percent=\"getFlowRateNumber(record.flowRate)\" style=\"width:80px\" />\n              </template>\n            </a-table>\n          </a-tab-pane>\n\n          <a-tab-pane loading=\"true\" tab=\"业务节点限时监管\" key=\"2\">\n            <a-table :dataSource=\"dataSource2\" size=\"default\" rowKey=\"id\" :columns=\"columns2\" :pagination=\"ipagination2\" @change=\"tableChange2\">\n              <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n                <span style=\"color: red;\">{{ record.flowRate }}小时</span>\n              </template>\n            </a-table>\n          </a-tab-pane>\n\n        </a-tabs>\n\n\n      </a-card>\n    </a-row>\n\n  </div>\n</template>\n\n<script>\n\n  import ACol from \"ant-design-vue/es/grid/Col\"\n  import ATooltip from \"ant-design-vue/es/tooltip/Tooltip\"\n  import ChartCard from '@/components/ChartCard'\n  import MiniBar from '@/components/chart/MiniBar'\n  import MiniArea from '@/components/chart/MiniArea'\n  import IndexBar from '@/components/chart/IndexBar'\n  import BarMultid from '@/components/chart/BarMultid'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n\n  const jhjgData = [\n    { type: '房管', '1月': 900, '2月': 1120, '3月': 1380, '4月': 1480, '5月': 1450, '6月': 1100, '7月':1300, '8月':900,'9月':1000 ,'10月':1200 ,'11月':600 ,'12月':900 },\n    { type: '税务', '1月':1200, '2月': 1500, '3月': 1980, '4月': 2000, '5月': 1000, '6月': 600, '7月':900, '8月':1100,'9月':1300 ,'10月':2000 ,'11月':900 ,'12月':1100 },\n    { type: '不动产', '1月':2000, '2月': 1430, '3月': 1300, '4月': 1400, '5月': 900, '6月': 500, '7月':600, '8月':1000,'9月':600 ,'10月':1000 ,'11月':1500 ,'12月':1200 }\n  ]\n\n  const jhjgFields=[\n    '1月','2月','3月','4月','5月','6月',\n    '7月','8月','9月','10月','11月','12月'\n  ]\n\n  const xljgData = [\n    {type:'一月',\"房管\":1.12,\"税务\":1.55,\"不动产\":1.2},\n    {type:'二月',\"房管\":1.65,\"税务\":1.32,\"不动产\":1.42},\n    {type:'三月',\"房管\":1.85,\"税务\":1.1,\"不动产\":1.5},\n\n    {type:'四月',\"房管\":1.33,\"税务\":1.63,\"不动产\":1.4},\n    {type:'五月',\"房管\":1.63,\"税务\":1.8,\"不动产\":1.7},\n    {type:'六月',\"房管\":1.85,\"税务\":1.98,\"不动产\":1.8},\n\n    {type:'七月',\"房管\":1.98,\"税务\":1.5,\"不动产\":1.76},\n    {type:'八月',\"房管\":1.48,\"税务\":1.2,\"不动产\":1.3},\n    {type:'九月',\"房管\":1.41,\"税务\":1.9,\"不动产\":1.6},\n\n    {type:'十月',\"房管\":1.1,\"税务\":1.1,\"不动产\":1.4},\n    {type:'十一月',\"房管\":1.85,\"税务\":1.6,\"不动产\":1.5},\n    {type:'十二月',\"房管\":1.5,\"税务\":1.4,\"不动产\":1.3}\n  ]\n  const xljgFields=[\"房管\",\"税务\",\"不动产\"]\n\n  const dataCol1 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '业务类型',\n    align:\"center\",\n    dataIndex: 'type'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '受理时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '办理时长',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n  const dataSource1=[\n    {reBizCode:\"1\",type:\"转移登记\",acceptBy:'张三',acceptDate:\"2019-01-22\",curNode:\"任务分派\",flowRate:60},\n    {reBizCode:\"2\",type:\"抵押登记\",acceptBy:'李四',acceptDate:\"2019-01-23\",curNode:\"领导审核\",flowRate:30},\n    {reBizCode:\"3\",type:\"转移登记\",acceptBy:'王武',acceptDate:\"2019-01-25\",curNode:\"任务处理\",flowRate:20},\n    {reBizCode:\"4\",type:\"转移登记\",acceptBy:'赵楼',acceptDate:\"2019-11-22\",curNode:\"部门审核\",flowRate:80},\n    {reBizCode:\"5\",type:\"转移登记\",acceptBy:'钱就',acceptDate:\"2019-12-12\",curNode:\"任务分派\",flowRate:90},\n    {reBizCode:\"6\",type:\"转移登记\",acceptBy:'孙吧',acceptDate:\"2019-03-06\",curNode:\"任务处理\",flowRate:10},\n    {reBizCode:\"7\",type:\"抵押登记\",acceptBy:'周大',acceptDate:\"2019-04-13\",curNode:\"任务分派\",flowRate:100},\n    {reBizCode:\"8\",type:\"抵押登记\",acceptBy:'吴二',acceptDate:\"2019-05-09\",curNode:\"任务上报\",flowRate:50},\n    {reBizCode:\"9\",type:\"抵押登记\",acceptBy:'郑爽',acceptDate:\"2019-07-12\",curNode:\"任务处理\",flowRate:63},\n    {reBizCode:\"20\",type:\"抵押登记\",acceptBy:'林有',acceptDate:\"2019-12-12\",curNode:\"任务打回\",flowRate:59},\n    {reBizCode:\"11\",type:\"转移登记\",acceptBy:'码云',acceptDate:\"2019-09-10\",curNode:\"任务签收\",flowRate:87},\n  ]\n\n  const dataCol2 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '发起时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '超时时间',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n  const dataSource2=[\n    {reBizCode:\"A001\",type:\"转移登记\",acceptBy:'张四',acceptDate:\"2019-01-22\",curNode:\"任务分派\",flowRate:12},\n    {reBizCode:\"A002\",type:\"抵押登记\",acceptBy:'李吧',acceptDate:\"2019-01-23\",curNode:\"任务签收\",flowRate:3},\n    {reBizCode:\"A003\",type:\"转移登记\",acceptBy:'王三',acceptDate:\"2019-01-25\",curNode:\"任务处理\",flowRate:24},\n    {reBizCode:\"A004\",type:\"转移登记\",acceptBy:'赵二',acceptDate:\"2019-11-22\",curNode:\"部门审核\",flowRate:10},\n    {reBizCode:\"A005\",type:\"转移登记\",acceptBy:'钱大',acceptDate:\"2019-12-12\",curNode:\"任务签收\",flowRate:8},\n    {reBizCode:\"A006\",type:\"转移登记\",acceptBy:'孙就',acceptDate:\"2019-03-06\",curNode:\"任务处理\",flowRate:10},\n    {reBizCode:\"A007\",type:\"抵押登记\",acceptBy:'周晕',acceptDate:\"2019-04-13\",curNode:\"部门审核\",flowRate:24},\n    {reBizCode:\"A008\",type:\"抵押登记\",acceptBy:'吴有',acceptDate:\"2019-05-09\",curNode:\"部门审核\",flowRate:30},\n    {reBizCode:\"A009\",type:\"抵押登记\",acceptBy:'郑武',acceptDate:\"2019-07-12\",curNode:\"任务分派\",flowRate:1},\n    {reBizCode:\"A0010\",type:\"抵押登记\",acceptBy:'林爽',acceptDate:\"2019-12-12\",curNode:\"部门审核\",flowRate:16},\n    {reBizCode:\"A0011\",type:\"转移登记\",acceptBy:'码楼',acceptDate:\"2019-09-10\",curNode:\"部门审核\",flowRate:7},\n  ]\n\n  export default {\n    name: \"IndexBdc\",\n    components: {\n      ATooltip,\n      ACol,\n      ChartCard,\n      MiniArea,\n      MiniBar,\n      DashChartDemo,\n      BarMultid,\n      IndexBar\n    },\n    data() {\n      return {\n        loading: true,\n        cardCount:{\n          sll:100,\n          bjl:87,\n          isll:15,\n          ibjl:9\n        },\n\n        todaySll:60,\n        todayBjl:54,\n        todayISll:13,\n        todayIBjl:7,\n\n        chartData:{\n          sll:[],\n          bjl:[],\n          isll:[],\n          ibjl:[]\n        },\n        jhjgFields,\n        jhjgData,\n\n        xljgData,\n        xljgFields,\n\n        diskInfo:[\n          {name:\"C盘\",restPPT:7},\n          {name:\"D盘\",restPPT:5}\n        ],\n\n        registerTypeList:[{\n          text:\"业务受理\"\n        },{\n          text:\"业务管理\"\n        },{\n          text:\"文件管理\"\n        },{\n          text:\"信息查询\"\n        }],\n\n        dataSource1:[],\n        dataSource2:[],\n        columns:dataCol1,\n        columns2:dataCol2,\n        ipagination1:{\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0,\n\n        },\n        ipagination2:{\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0,\n        },\n        indexRegisterType:\"转移登记\",\n        indexBottomTab:\"1\"\n\n      }\n    },\n    methods:{\n      goPage(){\n        this.$message.success(\"根据业务自行处理跳转页面!\")\n      },\n      changeRegisterType(e){\n        this.indexRegisterType = e.target.value\n        if(this.indexBottomTab==\"1\"){\n          this.loadDataSource1()\n        }else{\n          this.loadDataSource2()\n        }\n      },\n      tableChange1(pagination){\n        this.ipagination1.current = pagination.current\n        this.ipagination1.pageSize = pagination.pageSize\n        this.queryTimeoutInfo()\n      },\n      tableChange2(pagination){\n        this.ipagination2.current = pagination.current\n        this.ipagination2.pageSize = pagination.pageSize\n        this.queryNodeTimeoutInfo()\n      },\n      getFlowRateNumber(value){\n        return Number(value)\n      },\n      getPercentFormat(value){\n        if(value==100){\n          return \"超时\"\n        }else{\n          return value+\"%\"\n        }\n      },\n      getPercentColor(value){\n        let p = Number(value)\n        if(p>=90 && p<100){\n          return 'rgb(244, 240, 89)'\n        }else if(p>=100){\n          return 'red'\n        }else{\n          return 'rgb(16, 142, 233)'\n        }\n      },\n\n      loadDataSource1(){\n        this.dataSource1 = dataSource1.filter(item=>{\n          if(!this.indexRegisterType){\n            return true\n          }\n          return item.type==this.indexRegisterType\n        })\n      },\n      loadDataSource2(){\n        this.dataSource2 = dataSource2.filter(item=>{\n          if(!this.indexRegisterType){\n            return true\n          }\n          return item.type==this.indexRegisterType\n        })\n      }\n    },\n    created() {\n      this.loadDataSource1()\n      this.loadDataSource2()\n      setTimeout(() => {\n        this.loading = !this.loading\n      }, 1000)\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .extra-wrapper {\n    line-height: 55px;\n    padding-right: 24px;\n\n    .extra-item {\n      display: inline-block;\n      margin-right: 24px;\n\n      a {\n        margin-left: 24px;\n      }\n    }\n  }\n\n  .item-group {\n    padding: 20px 0 8px 24px;\n    font-size: 0;\n    a {\n      color: rgba(0, 0, 0, 0.65);\n      display: inline-block;\n      font-size: 14px;\n      margin-bottom: 13px;\n      width: 25%;\n    }\n  }\n\n  .item-group {\n    .more-btn {\n      margin-bottom: 13px;\n      text-align: center;\n    }\n  }\n\n  .list-content-item {\n    color: rgba(0, 0, 0, .45);\n    display: inline-block;\n    vertical-align: middle;\n    font-size: 14px;\n    margin-left: 40px;\n  }\n\n  @media only screen and (min-width: 1600px) {\n    .list-content-item{\n      margin-left:60px;\n    }\n  }\n\n  @media only screen and (max-width: 1300px) {\n    .list-content-item{\n      margin-left:20px;\n    }\n    .width-hidden4{\n      display:none\n    }\n  }\n  .list-content-item{\n    span{line-height: 20px;}\n  }\n  .list-content-item{\n    p{margin-top: 4px;margin-bottom:0;line-height:22px;}\n  }\n  .anty-list-cust {\n    .ant-list-item-meta{flex: 0.3 !important;}\n  }\n  .anty-list-cust {\n    .ant-list-item-content{flex:1 !important; justify-content:flex-start !important;margin-left: 20px;}\n  }\n\n\n</style>"]}]}