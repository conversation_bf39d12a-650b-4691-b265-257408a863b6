{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\VueCronModal.vue?vue&type=template&id=5dcbc1c8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\VueCronModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  title=\"corn表达式\"\n  :width=\"modalWidth\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"change\"\n  @cancel=\"close\"\n  cancelText=\"关闭\">\n  <div class=\"card-container\">\n    <a-tabs type=\"card\">\n      <a-tab-pane key=\"1\" type=\"card\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" /> 秒</span>\n        <a-radio-group v-model=\"result.second.cronEvery\">\n          <a-row>\n            <a-radio value=\"1\">每一秒钟</a-radio>\n          </a-row>\n          <a-row>\n            <a-radio value=\"2\">每隔\n              <a-input-number size=\"small\" v-model=\"result.second.incrementIncrement\" :min=\"1\" :max=\"60\"></a-input-number>\n              秒执行 从\n              <a-input-number size=\"small\" v-model=\"result.second.incrementStart\" :min=\"0\" :max=\"59\"></a-input-number>\n              秒开始\n            </a-radio>\n          </a-row>\n          <a-row>\n            <a-radio value=\"3\">具体秒数(可多选)</a-radio>\n            <a-select style=\"width:354px;\" size=\"small\" mode=\"multiple\" v-model=\"result.second.specificSpecific\">\n              <a-select-option v-for=\"(val,index) in 60\" :key=\"index\" :value=\"index\">{{ index }}</a-select-option>\n            </a-select>\n          </a-row>\n          <a-row>\n            <a-radio value=\"4\">周期从\n              <a-input-number size=\"small\" v-model=\"result.second.rangeStart\" :min=\"1\" :max=\"60\"></a-input-number>\n              到\n              <a-input-number size=\"small\" v-model=\"result.second.rangeEnd\" :min=\"0\" :max=\"59\"></a-input-number>\n              秒\n            </a-radio>\n          </a-row>\n        </a-radio-group>\n      </a-tab-pane>\n      <a-tab-pane key=\"2\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" />分</span>\n        <div class=\"tabBody\">\n          <a-radio-group v-model=\"result.minute.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一分钟</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.minute.incrementIncrement\" :min=\"1\" :max=\"60\"></a-input-number>\n                分执行 从\n                <a-input-number size=\"small\" v-model=\"result.minute.incrementStart\" :min=\"0\" :max=\"59\"></a-input-number>\n                分开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"3\">具体分钟数(可多选)</a-radio>\n              <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.minute.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(60)\" :key=\"index\" :value=\"index\"> {{ index }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"4\">周期从\n                <a-input-number size=\"small\" v-model=\"result.minute.rangeStart\" :min=\"1\" :max=\"60\"></a-input-number>\n                到\n                <a-input-number size=\"small\" v-model=\"result.minute.rangeEnd\" :min=\"0\" :max=\"59\"></a-input-number>\n                分\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </div>\n      </a-tab-pane>\n      <a-tab-pane key=\"3\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" /> 时</span>\n        <div class=\"tabBody\">\n          <a-radio-group v-model=\"result.hour.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一小时</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.hour.incrementIncrement\" :min=\"0\" :max=\"23\"></a-input-number>\n                小时执行 从\n                <a-input-number size=\"small\" v-model=\"result.hour.incrementStart\" :min=\"0\" :max=\"23\"></a-input-number>\n                小时开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio class=\"long\" value=\"3\">具体小时数(可多选)</a-radio>\n              <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.hour.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(24)\" :key=\"index\" >{{ index }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"4\">周期从\n                <a-input-number size=\"small\" v-model=\"result.hour.rangeStart\" :min=\"0\" :max=\"23\"></a-input-number>\n                到\n                <a-input-number size=\"small\" v-model=\"result.hour.rangeEnd\" :min=\"0\" :max=\"23\"></a-input-number>\n                小时\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </div>\n      </a-tab-pane>\n      <a-tab-pane key=\"4\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" />  天</span>\n        <div class=\"tabBody\">\n          <a-radio-group v-model=\"result.day.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一天</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.week.incrementIncrement\" :min=\"1\" :max=\"7\"></a-input-number>\n                周执行 从\n                <a-select size=\"small\" v-model=\"result.week.incrementStart\">\n                  <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                </a-select>\n                开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"3\">每隔\n                <a-input-number size=\"small\" v-model=\"result.day.incrementIncrement\" :min=\"1\" :max=\"31\"></a-input-number>\n                天执行 从\n                <a-input-number size=\"small\" v-model=\"result.day.incrementStart\" :min=\"1\" :max=\"31\"></a-input-number>\n                天开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio class=\"long\" value=\"4\">具体星期几(可多选)</a-radio>\n              <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.week.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio class=\"long\" value=\"5\">具体天数(可多选)</a-radio>\n              <a-select style=\"width:354px;\" size=\"small\" mode=\"multiple\" v-model=\"result.day.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(31)\" :key=\"index\" :value=\"index\">{{ index+1 }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"6\">在这个月的最后一天</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"7\">在这个月的最后一个工作日</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"8\">在这个月的最后一个\n                <a-select size=\"small\" v-model=\"result.day.cronLastSpecificDomDay\">\n                  <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                </a-select>\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"9\">\n                <a-input-number size=\"small\" v-model=\"result.day.cronDaysBeforeEomMinus\" :min=\"1\" :max=\"31\"></a-input-number>\n                在本月底前\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"10\">最近的工作日（周一至周五）至本月\n                <a-input-number size=\"small\" v-model=\"result.day.cronDaysNearestWeekday\" :min=\"1\" :max=\"31\"></a-input-number>\n                日\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"11\">在这个月的第\n                <a-input-number size=\"small\" v-model=\"result.week.cronNthDayNth\" :min=\"1\" :max=\"5\"></a-input-number>\n                个\n                <a-select size=\"small\" v-model=\"result.week.cronNthDayDay\">\n                  <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                </a-select>\n\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </div>\n      </a-tab-pane>\n      <a-tab-pane key=\"5\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" /> 月</span>\n        <div class=\"tabBody\">\n          <a-radio-group v-model=\"result.month.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一月</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.month.incrementIncrement\" :min=\"0\" :max=\"12\"></a-input-number>\n                月执行 从\n                <a-input-number size=\"small\" v-model=\"result.month.incrementStart\" :min=\"0\" :max=\"12\"></a-input-number>\n                月开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio class=\"long\" value=\"3\">具体月数(可多选)</a-radio>\n              <a-select style=\"width:354px;\" size=\"small\" filterable mode=\"multiple\" v-model=\"result.month.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(12)\" :key=\"index\"  :value=\"index\">{{ index+1 }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"4\">从\n                <a-input-number size=\"small\" v-model=\"result.month.rangeStart\" :min=\"1\" :max=\"12\"></a-input-number>\n                到\n                <a-input-number size=\"small\" v-model=\"result.month.rangeEnd\" :min=\"1\" :max=\"12\"></a-input-number>\n                月之间的每个月\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </div>\n      </a-tab-pane>\n      <a-tab-pane key=\"6\">\n        <span slot=\"tab\"><a-icon type=\"schedule\" /> 年</span>\n        <div class=\"tabBody\">\n          <a-radio-group v-model=\"result.year.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一年</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.year.incrementIncrement\" :min=\"1\" :max=\"99\"></a-input-number>\n                年执行 从\n                <a-input-number size=\"small\" v-model=\"result.year.incrementStart\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                年开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio class=\"long\" value=\"3\">具体年份(可多选)</a-radio>\n              <a-select style=\"width:354px;\" size=\"small\" filterable mode=\"multiple\" v-model=\"result.year.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in Array(100)\" :key=\"index\" :value=\"2019+index\">{{ 2019+index }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"4\">从\n                <a-input-number size=\"small\" v-model=\"result.year.rangeStart\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                到\n                <a-input-number size=\"small\" v-model=\"result.year.rangeEnd\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                年之间的每一年\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </div>\n      </a-tab-pane>\n    </a-tabs>\n    <div class=\"bottom\">\n      <span class=\"value\">{{this.cron.label }}</span>\n    </div>\n  </div>\n</a-modal>\n", null]}