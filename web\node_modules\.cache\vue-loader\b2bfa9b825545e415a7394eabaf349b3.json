{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkDetail.vue?vue&type=template&id=17fcf9e8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkDetail.vue", "mtime": 1749627321850}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"container\"\n  :style=\"{\n    backgroundColor: sysConfig.homeBgColor,\n    backgroundImage: sysConfig.file_homeBg ? 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')' : '',\n    backgroundRepeat: sysConfig.homeBgRepeat ? sysConfig.homeBgRepeat : '',\n  }\"\n>\n  <a-layout>\n    <a-layout-header>\n      <Header/>\n    </a-layout-header>\n    <a-layout>\n      <a-layout-content>\n        <div class=\"project-detail\">\n          <!-- 播放器 -->\n          <div class=\"scratch-player\">\n            <iframe\n              :src=\"frameHref\"\n              id=\"player\"\n              frameborder=\"0\"\n              width=\"100%\"\n              height=\"100%\"\n              :scrolling=\"workInfo.workType==4||workInfo.workType==5||workInfo.workType==10?'auto':'no'\"\n            ></iframe>\n          </div>\n          <keyboard v-if=\"_isMobile() && workInfo.workType==2\" @event=\"keyEvent\"/>\n          <!-- 作品信息 -->\n          <div class=\"project-info\">\n            <a-row type=\"flex\" justify=\"space-around\">\n              <a-col :span=\"4\">\n                <a-avatar shape=\"square\" class=\"avatar\" :size=\"60\" :src=\"workInfo.avatar_url\" />\n                <p>{{ workInfo.realname || workInfo.username }}</p>\n              </a-col>\n              <a-col :span=\"14\" v-if=\"!_isMobile()\">\n                <div class=\"project-meta\">\n                  <h2 class=\"title\">{{ workInfo.workName }}</h2>\n                  <p class=\"time\">{{ workInfo.createTime }}</p>\n                </div>\n              </a-col>\n              <a-col :span=\"_isMobile()?12:6\">\n                <div class=\"project-op\">\n                  <a-icon type=\"eye\" theme=\"twoTone\" />\n                  <span class=\"gap\">{{ workInfo.viewNum }}</span>\n\n                  <a-icon type=\"like\" theme=\"twoTone\" @click=\"starWork\" />\n                  <span class=\"gap\">{{ workInfo.starNum }}</span>\n\n                  <a-popover v-if=\"!_isMobile()\" title=\"微信扫一扫手机体验和分享\">\n                    <template slot=\"content\">\n                      <qrcode :value=\"getShareUrl()\" :size=\"200\" level=\"H\"></qrcode>\n                    </template>\n                    <a-icon type=\"mobile\" theme=\"twoTone\" />\n                  </a-popover>\n                </div>\n              </a-col>\n              <a-col :span=\"24\" v-if=\"_isMobile()\">\n                <div class=\"project-meta\">\n                  <h2 class=\"title\">{{ workInfo.workName }}</h2>\n                  <p class=\"time\">{{ workInfo.createTime }}</p>\n                </div>\n              </a-col>\n            </a-row>\n          </div>\n\n          <!-- 评论区 -->\n          <div class=\"project-comment\">\n            <div class=\"publish\">\n              <a-row type=\"flex\" justify=\"space-between\">\n                <a-col :span=\"3\" class=\"comment-user\"  v-if=\"!_isMobile()\">\n                  <a-avatar shape=\"square\" :size=\"60\" icon=\"user\" :src=\"getFileAccessHttpUrl(avatar())\" />\n                  <p>\n                    {{ token ? nickname() : '未登录' }}\n                  </p>\n                </a-col>\n                <a-col :span=\"16\">\n                  <a-textarea\n                    v-model=\"commentContent\"\n                    :rows=\"5\"\n                    :maxLength=\"500\"\n                    placeholder=\"说说这个作品怎么样吧\"\n                  ></a-textarea>\n                </a-col>\n                <a-col :span=\"_isMobile()?6:4\">\n                  <div class=\"comment-btn\">\n                    <a-button :disabled=\"!token\" type=\"dashed\" @click=\"comment\">发表评论</a-button>\n                  </div>\n                </a-col>\n              </a-row>\n            </div>\n            <a-divider />\n            <a-list\n              class=\"comment-list\"\n              item-layout=\"horizontal\"\n              :locale=\"{ emptyText: '暂无评论' }\"\n              :data-source=\"comments\"\n            >\n              <a-list-item slot=\"renderItem\" slot-scope=\"item\">\n                <a-comment :author=\"item.realname || item.username\">\n                  <a-avatar shape=\"square\" :size=\"40\" slot=\"avatar\" icon=\"user\" :src=\"item.avatar_url\" />\n                  <p class=\"comment-content\" slot=\"content\">\n                    {{ item.comment }}\n                  </p>\n                  <a-tooltip slot=\"datetime\" :title=\"moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')\">\n                    <span>{{ moment(item.createTime).fromNow() }}</span>\n                  </a-tooltip>\n                </a-comment>\n              </a-list-item>\n              <div\n                v-if=\"showLoadingMore\"\n                slot=\"loadMore\"\n                :style=\"{\n                  textAlign: 'center',\n                  marginTop: '12px',\n                  height: '32px',\n                  lineHeight: '32px',\n                }\"\n              >\n                <a-spin v-if=\"loadingMore\" />\n                <a-button type=\"link\" v-else @click=\"workComments\"> 加载更多 </a-button>\n              </div>\n            </a-list>\n          </div>\n        </div>\n        <div v-if=\"shareHtml\" class=\"work-share-html\">\n          <a-divider></a-divider>\n          <div v-html=\"shareHtml\"></div>\n        </div>\n      </a-layout-content>\n      <a-layout-sider v-if=\"!_isMobile()\">\n        <UserEnter/>\n      </a-layout-sider>\n    </a-layout>\n    <a-layout-footer>\n      <Footer/>\n    </a-layout-footer>\n  </a-layout>\n</div>\n", null]}