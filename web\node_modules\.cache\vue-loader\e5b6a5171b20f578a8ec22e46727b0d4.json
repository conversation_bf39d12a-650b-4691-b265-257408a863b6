{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\VueCronModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\VueCronModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n    import pick from 'lodash.pick'\n    export default {\n        name:'VueCron',\n        props:['data','i18n'],\n        data(){\n            return {\n                visible: false,\n                confirmLoading:false,\n                size:'large',\n                weekDays:['天','一','二','三','四','五','六'].map(val=>'星期'+val),\n                result: {\n                  second:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:0,\n                    specificSpecific:[],\n                  },\n                  minute:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:'0',\n                    specificSpecific:[],\n                  },\n                  hour:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:'0',\n                    rangeEnd:'0',\n                    specificSpecific:[],\n                  },\n                  day:{\n                    cronEvery:'',\n                    incrementStart:1,\n                    incrementIncrement:'1',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                    cronLastSpecificDomDay:1,\n                    cronDaysBeforeEomMinus:'',\n                    cronDaysNearestWeekday:'',\n                  },\n                  week:{\n                    cronEvery:'',\n                    incrementStart:1,\n                    incrementIncrement:'1',\n                    specificSpecific:[],\n                    cronNthDayDay:1,\n                    cronNthDayNth:'1',\n                  },\n                  month:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:1,\n                    specificSpecific:[],\n                  },\n                  year:{\n                    cronEvery:'',\n                    incrementStart:2017,\n                    incrementIncrement:1,\n                    rangeStart:2019,\n                    rangeEnd: 2019,\n                    specificSpecific:[],\n                  },\n                  label:''\n                },\n                output:{\n                  second:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  minute:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  hour:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  day:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                    cronLastSpecificDomDay:'',\n                    cronDaysBeforeEomMinus:'',\n                    cronDaysNearestWeekday:'',\n                  },\n                  week:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    specificSpecific:[],\n                    cronNthDayDay:'',\n                    cronNthDayNth:'',\n                  },\n                  month:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  year:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  }\n                }\n            }\n        },\n        computed: {\n            modalWidth(){\n                return 608;\n            },\n            text(){\n                return Language['cn']\n            },\n            secondsText() {\n                let seconds = '';\n                let cronEvery=this.result.second.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        seconds = '*';\n                        break;\n                    case '2':\n                        seconds = this.result.second.incrementStart+'/'+this.result.second.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.second.specificSpecific.map(val=> {seconds += val+','});\n                        seconds = seconds.slice(0, -1);\n                        break;\n                    case '4':\n                        seconds = this.result.second.rangeStart+'-'+this.result.second.rangeEnd;\n                        break;\n                }\n                return seconds;\n            },\n            minutesText() {\n                let minutes = '';\n                let cronEvery=this.result.minute.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        minutes = '*';\n                        break;\n                    case '2':\n                        minutes = this.result.minute.incrementStart+'/'+this.result.minute.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.minute.specificSpecific.map(val=> {\n                            minutes += val+','\n                        });\n                        minutes = minutes.slice(0, -1);\n                        break;\n                    case '4':\n                        minutes = this.result.minute.rangeStart+'-'+this.result.minute.rangeEnd;\n                        break;\n                }\n                return minutes;\n            },\n            hoursText() {\n                let hours = '';\n                let cronEvery=this.result.hour.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        hours = '*';\n                        break;\n                    case '2':\n                        hours = this.result.hour.incrementStart+'/'+this.result.hour.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.hour.specificSpecific.map(val=> {\n                            hours += val+','\n                        });\n                        hours = hours.slice(0, -1);\n                        break;\n                    case '4':\n                        hours = this.result.hour.rangeStart+'-'+this.result.hour.rangeEnd;\n                        break;\n                }\n                return hours;\n            },\n            daysText() {\n                let days='';\n                let cronEvery=this.result.day.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        break;\n                    case '2':\n                    case '4':\n                    case '11':\n                        days = '?';\n                        break;\n                    case '3':\n                        days = this.result.day.incrementStart+'/'+this.result.day.incrementIncrement;\n                        break;\n                    case '5':\n                        this.result.day.specificSpecific.map(val=> {\n                            days += val+','\n                        });\n                        days = days.slice(0, -1);\n                        break;\n                    case '6':\n                        days = \"L\";\n                        break;\n                    case '7':\n                        days = \"LW\";\n                        break;\n                    case '8':\n                        days = this.result.day.cronLastSpecificDomDay + 'L';\n                        break;\n                    case '9':\n                        days = 'L-' + this.result.day.cronDaysBeforeEomMinus;\n                        break;\n                    case '10':\n                        days = this.result.day.cronDaysNearestWeekday+\"W\";\n                        break\n                }\n                return days;\n            },\n            weeksText() {\n                let weeks = '';\n                let cronEvery=this.result.day.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                    case '3':\n                    case '5':\n                        weeks = '?';\n                        break;\n                    case '2':\n                        weeks = this.result.week.incrementStart+'/'+this.result.week.incrementIncrement;\n                        break;\n                    case '4':\n                        this.result.week.specificSpecific.map(val=> {\n                            weeks += val+','\n                        });\n                        weeks = weeks.slice(0, -1);\n                        break;\n                    case '6':\n                    case '7':\n                    case '8':\n                    case '9':\n                    case '10':\n                        weeks = \"?\";\n                        break;\n                    case '11':\n                        weeks = this.result.week.cronNthDayDay+\"#\"+this.result.week.cronNthDayNth;\n                        break;\n                }\n                return weeks;\n            },\n            monthsText() {\n                let months = '';\n                let cronEvery=this.result.month.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        months = '*';\n                        break;\n                    case '2':\n                        months = this.result.month.incrementStart+'/'+this.result.month.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.month.specificSpecific.map(val=> {\n                            months += val+','\n                        });\n                        months = months.slice(0, -1);\n                        break;\n                    case '4':\n                        months = this.result.month.rangeStart+'-'+this.result.month.rangeEnd;\n                        break;\n                }\n                return months;\n            },\n            yearsText() {\n                let years = '';\n                let cronEvery=this.result.year.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        years = '*';\n                        break;\n                    case '2':\n                        years = this.result.year.incrementStart+'/'+this.result.year.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.year.specificSpecific.map(val=> {\n                            years += val+','\n                        });\n                        years = years.slice(0, -1);\n                        break;\n                    case '4':\n                        years = this.result.year.rangeStart+'-'+this.result.year.rangeEnd;\n                        break;\n                }\n                return years;\n            },\n            cron(){\n                return {\n                  value: this.result,\n                  label:`${this.secondsText||'*'} ${this.minutesText||'*'} ${this.hoursText||'*'} ${this.daysText||'*'} ${this.monthsText||'*'} ${this.weeksText||'*'} ${this.yearsText||'*'}`\n                }\n            },\n        },\n        watch:{\n          data(){\n            //this.rest(this.data);\n          }\n        },\n        methods: {\n            show(){\n              //this.rest(pick(this.data.value,'second','minute','hour','day','week','month','year'));\n              //this.rest(this.data.value);\n              Object.assign(this.data.value,this.result);\n              console.log('data初始化',this.data);\n              //this.result = this.data.value;\n              this.visible=true;\n            },\n            getValue(){\n                return this.cron;\n            },\n            change(){\n                console.log('返回前',this.cron);\n                this.$emit('change',this.cron);\n                this.close();\n                this.visible = false;\n            },\n            close(){\n                this.visible = false;\n                //this.$emit('close')\n            },\n            rest(data){\n                for(let i in data){\n                  console.log(data[i]);\n                    if(data[i] instanceof Object){\n                        this.rest(data[i])\n                    }else {\n                      switch(typeof data[i]) {\n                        case 'object':\n                          data[i] = [];\n                          break;\n                        case 'string':\n                          data[i] = '';\n                          break;\n                        case 'number':\n                          data[i] = null;\n                          break;\n                      }\n                    }\n                }\n            },\n            callback (key) {\n                //console.log(key)\n            }\n        }\n    }\n", {"version": 3, "sources": ["VueCronModal.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "VueCronModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    title=\"corn表达式\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"change\"\n    @cancel=\"close\"\n    cancelText=\"关闭\">\n    <div class=\"card-container\">\n      <a-tabs type=\"card\">\n        <a-tab-pane key=\"1\" type=\"card\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" /> 秒</span>\n          <a-radio-group v-model=\"result.second.cronEvery\">\n            <a-row>\n              <a-radio value=\"1\">每一秒钟</a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"2\">每隔\n                <a-input-number size=\"small\" v-model=\"result.second.incrementIncrement\" :min=\"1\" :max=\"60\"></a-input-number>\n                秒执行 从\n                <a-input-number size=\"small\" v-model=\"result.second.incrementStart\" :min=\"0\" :max=\"59\"></a-input-number>\n                秒开始\n              </a-radio>\n            </a-row>\n            <a-row>\n              <a-radio value=\"3\">具体秒数(可多选)</a-radio>\n              <a-select style=\"width:354px;\" size=\"small\" mode=\"multiple\" v-model=\"result.second.specificSpecific\">\n                <a-select-option v-for=\"(val,index) in 60\" :key=\"index\" :value=\"index\">{{ index }}</a-select-option>\n              </a-select>\n            </a-row>\n            <a-row>\n              <a-radio value=\"4\">周期从\n                <a-input-number size=\"small\" v-model=\"result.second.rangeStart\" :min=\"1\" :max=\"60\"></a-input-number>\n                到\n                <a-input-number size=\"small\" v-model=\"result.second.rangeEnd\" :min=\"0\" :max=\"59\"></a-input-number>\n                秒\n              </a-radio>\n            </a-row>\n          </a-radio-group>\n        </a-tab-pane>\n        <a-tab-pane key=\"2\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" />分</span>\n          <div class=\"tabBody\">\n            <a-radio-group v-model=\"result.minute.cronEvery\">\n              <a-row>\n                <a-radio value=\"1\">每一分钟</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"2\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.minute.incrementIncrement\" :min=\"1\" :max=\"60\"></a-input-number>\n                  分执行 从\n                  <a-input-number size=\"small\" v-model=\"result.minute.incrementStart\" :min=\"0\" :max=\"59\"></a-input-number>\n                  分开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"3\">具体分钟数(可多选)</a-radio>\n                <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.minute.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(60)\" :key=\"index\" :value=\"index\"> {{ index }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio value=\"4\">周期从\n                  <a-input-number size=\"small\" v-model=\"result.minute.rangeStart\" :min=\"1\" :max=\"60\"></a-input-number>\n                  到\n                  <a-input-number size=\"small\" v-model=\"result.minute.rangeEnd\" :min=\"0\" :max=\"59\"></a-input-number>\n                  分\n                </a-radio>\n              </a-row>\n            </a-radio-group>\n          </div>\n        </a-tab-pane>\n        <a-tab-pane key=\"3\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" /> 时</span>\n          <div class=\"tabBody\">\n            <a-radio-group v-model=\"result.hour.cronEvery\">\n              <a-row>\n                <a-radio value=\"1\">每一小时</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"2\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.hour.incrementIncrement\" :min=\"0\" :max=\"23\"></a-input-number>\n                  小时执行 从\n                  <a-input-number size=\"small\" v-model=\"result.hour.incrementStart\" :min=\"0\" :max=\"23\"></a-input-number>\n                  小时开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio class=\"long\" value=\"3\">具体小时数(可多选)</a-radio>\n                <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.hour.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(24)\" :key=\"index\" >{{ index }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio value=\"4\">周期从\n                  <a-input-number size=\"small\" v-model=\"result.hour.rangeStart\" :min=\"0\" :max=\"23\"></a-input-number>\n                  到\n                  <a-input-number size=\"small\" v-model=\"result.hour.rangeEnd\" :min=\"0\" :max=\"23\"></a-input-number>\n                  小时\n                </a-radio>\n              </a-row>\n            </a-radio-group>\n          </div>\n        </a-tab-pane>\n        <a-tab-pane key=\"4\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" />  天</span>\n          <div class=\"tabBody\">\n            <a-radio-group v-model=\"result.day.cronEvery\">\n              <a-row>\n                <a-radio value=\"1\">每一天</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"2\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.week.incrementIncrement\" :min=\"1\" :max=\"7\"></a-input-number>\n                  周执行 从\n                  <a-select size=\"small\" v-model=\"result.week.incrementStart\">\n                    <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                  </a-select>\n                  开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"3\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.day.incrementIncrement\" :min=\"1\" :max=\"31\"></a-input-number>\n                  天执行 从\n                  <a-input-number size=\"small\" v-model=\"result.day.incrementStart\" :min=\"1\" :max=\"31\"></a-input-number>\n                  天开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio class=\"long\" value=\"4\">具体星期几(可多选)</a-radio>\n                <a-select style=\"width:340px;\" size=\"small\" mode=\"multiple\" v-model=\"result.week.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio class=\"long\" value=\"5\">具体天数(可多选)</a-radio>\n                <a-select style=\"width:354px;\" size=\"small\" mode=\"multiple\" v-model=\"result.day.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(31)\" :key=\"index\" :value=\"index\">{{ index+1 }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio value=\"6\">在这个月的最后一天</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"7\">在这个月的最后一个工作日</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"8\">在这个月的最后一个\n                  <a-select size=\"small\" v-model=\"result.day.cronLastSpecificDomDay\">\n                    <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                  </a-select>\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"9\">\n                  <a-input-number size=\"small\" v-model=\"result.day.cronDaysBeforeEomMinus\" :min=\"1\" :max=\"31\"></a-input-number>\n                  在本月底前\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"10\">最近的工作日（周一至周五）至本月\n                  <a-input-number size=\"small\" v-model=\"result.day.cronDaysNearestWeekday\" :min=\"1\" :max=\"31\"></a-input-number>\n                  日\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"11\">在这个月的第\n                  <a-input-number size=\"small\" v-model=\"result.week.cronNthDayNth\" :min=\"1\" :max=\"5\"></a-input-number>\n                  个\n                  <a-select size=\"small\" v-model=\"result.week.cronNthDayDay\">\n                    <a-select-option v-for=\"(val,index) in Array(7)\" :key=\"index\" :value=\"index+1\">{{ weekDays[index] }}</a-select-option>\n                  </a-select>\n\n                </a-radio>\n              </a-row>\n            </a-radio-group>\n          </div>\n        </a-tab-pane>\n        <a-tab-pane key=\"5\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" /> 月</span>\n          <div class=\"tabBody\">\n            <a-radio-group v-model=\"result.month.cronEvery\">\n              <a-row>\n                <a-radio value=\"1\">每一月</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"2\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.month.incrementIncrement\" :min=\"0\" :max=\"12\"></a-input-number>\n                  月执行 从\n                  <a-input-number size=\"small\" v-model=\"result.month.incrementStart\" :min=\"0\" :max=\"12\"></a-input-number>\n                  月开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio class=\"long\" value=\"3\">具体月数(可多选)</a-radio>\n                <a-select style=\"width:354px;\" size=\"small\" filterable mode=\"multiple\" v-model=\"result.month.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(12)\" :key=\"index\"  :value=\"index\">{{ index+1 }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio value=\"4\">从\n                  <a-input-number size=\"small\" v-model=\"result.month.rangeStart\" :min=\"1\" :max=\"12\"></a-input-number>\n                  到\n                  <a-input-number size=\"small\" v-model=\"result.month.rangeEnd\" :min=\"1\" :max=\"12\"></a-input-number>\n                  月之间的每个月\n                </a-radio>\n              </a-row>\n            </a-radio-group>\n          </div>\n        </a-tab-pane>\n        <a-tab-pane key=\"6\">\n          <span slot=\"tab\"><a-icon type=\"schedule\" /> 年</span>\n          <div class=\"tabBody\">\n            <a-radio-group v-model=\"result.year.cronEvery\">\n              <a-row>\n                <a-radio value=\"1\">每一年</a-radio>\n              </a-row>\n              <a-row>\n                <a-radio value=\"2\">每隔\n                  <a-input-number size=\"small\" v-model=\"result.year.incrementIncrement\" :min=\"1\" :max=\"99\"></a-input-number>\n                  年执行 从\n                  <a-input-number size=\"small\" v-model=\"result.year.incrementStart\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                  年开始\n                </a-radio>\n              </a-row>\n              <a-row>\n                <a-radio class=\"long\" value=\"3\">具体年份(可多选)</a-radio>\n                <a-select style=\"width:354px;\" size=\"small\" filterable mode=\"multiple\" v-model=\"result.year.specificSpecific\">\n                  <a-select-option v-for=\"(val,index) in Array(100)\" :key=\"index\" :value=\"2019+index\">{{ 2019+index }}</a-select-option>\n                </a-select>\n              </a-row>\n              <a-row>\n                <a-radio value=\"4\">从\n                  <a-input-number size=\"small\" v-model=\"result.year.rangeStart\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                  到\n                  <a-input-number size=\"small\" v-model=\"result.year.rangeEnd\" :min=\"2019\" :max=\"2119\"></a-input-number>\n                  年之间的每一年\n                </a-radio>\n              </a-row>\n            </a-radio-group>\n          </div>\n        </a-tab-pane>\n      </a-tabs>\n      <div class=\"bottom\">\n        <span class=\"value\">{{this.cron.label }}</span>\n      </div>\n    </div>\n  </a-modal>\n</template>\n<script>\n    import pick from 'lodash.pick'\n    export default {\n        name:'VueCron',\n        props:['data','i18n'],\n        data(){\n            return {\n                visible: false,\n                confirmLoading:false,\n                size:'large',\n                weekDays:['天','一','二','三','四','五','六'].map(val=>'星期'+val),\n                result: {\n                  second:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:0,\n                    specificSpecific:[],\n                  },\n                  minute:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:'0',\n                    specificSpecific:[],\n                  },\n                  hour:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:'0',\n                    rangeEnd:'0',\n                    specificSpecific:[],\n                  },\n                  day:{\n                    cronEvery:'',\n                    incrementStart:1,\n                    incrementIncrement:'1',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                    cronLastSpecificDomDay:1,\n                    cronDaysBeforeEomMinus:'',\n                    cronDaysNearestWeekday:'',\n                  },\n                  week:{\n                    cronEvery:'',\n                    incrementStart:1,\n                    incrementIncrement:'1',\n                    specificSpecific:[],\n                    cronNthDayDay:1,\n                    cronNthDayNth:'1',\n                  },\n                  month:{\n                    cronEvery:'',\n                    incrementStart:3,\n                    incrementIncrement:5,\n                    rangeStart:1,\n                    rangeEnd:1,\n                    specificSpecific:[],\n                  },\n                  year:{\n                    cronEvery:'',\n                    incrementStart:2017,\n                    incrementIncrement:1,\n                    rangeStart:2019,\n                    rangeEnd: 2019,\n                    specificSpecific:[],\n                  },\n                  label:''\n                },\n                output:{\n                  second:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  minute:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  hour:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  day:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                    cronLastSpecificDomDay:'',\n                    cronDaysBeforeEomMinus:'',\n                    cronDaysNearestWeekday:'',\n                  },\n                  week:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    specificSpecific:[],\n                    cronNthDayDay:'',\n                    cronNthDayNth:'',\n                  },\n                  month:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  },\n                  year:{\n                    cronEvery:'',\n                    incrementStart:'',\n                    incrementIncrement:'',\n                    rangeStart:'',\n                    rangeEnd:'',\n                    specificSpecific:[],\n                  }\n                }\n            }\n        },\n        computed: {\n            modalWidth(){\n                return 608;\n            },\n            text(){\n                return Language['cn']\n            },\n            secondsText() {\n                let seconds = '';\n                let cronEvery=this.result.second.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        seconds = '*';\n                        break;\n                    case '2':\n                        seconds = this.result.second.incrementStart+'/'+this.result.second.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.second.specificSpecific.map(val=> {seconds += val+','});\n                        seconds = seconds.slice(0, -1);\n                        break;\n                    case '4':\n                        seconds = this.result.second.rangeStart+'-'+this.result.second.rangeEnd;\n                        break;\n                }\n                return seconds;\n            },\n            minutesText() {\n                let minutes = '';\n                let cronEvery=this.result.minute.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        minutes = '*';\n                        break;\n                    case '2':\n                        minutes = this.result.minute.incrementStart+'/'+this.result.minute.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.minute.specificSpecific.map(val=> {\n                            minutes += val+','\n                        });\n                        minutes = minutes.slice(0, -1);\n                        break;\n                    case '4':\n                        minutes = this.result.minute.rangeStart+'-'+this.result.minute.rangeEnd;\n                        break;\n                }\n                return minutes;\n            },\n            hoursText() {\n                let hours = '';\n                let cronEvery=this.result.hour.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        hours = '*';\n                        break;\n                    case '2':\n                        hours = this.result.hour.incrementStart+'/'+this.result.hour.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.hour.specificSpecific.map(val=> {\n                            hours += val+','\n                        });\n                        hours = hours.slice(0, -1);\n                        break;\n                    case '4':\n                        hours = this.result.hour.rangeStart+'-'+this.result.hour.rangeEnd;\n                        break;\n                }\n                return hours;\n            },\n            daysText() {\n                let days='';\n                let cronEvery=this.result.day.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        break;\n                    case '2':\n                    case '4':\n                    case '11':\n                        days = '?';\n                        break;\n                    case '3':\n                        days = this.result.day.incrementStart+'/'+this.result.day.incrementIncrement;\n                        break;\n                    case '5':\n                        this.result.day.specificSpecific.map(val=> {\n                            days += val+','\n                        });\n                        days = days.slice(0, -1);\n                        break;\n                    case '6':\n                        days = \"L\";\n                        break;\n                    case '7':\n                        days = \"LW\";\n                        break;\n                    case '8':\n                        days = this.result.day.cronLastSpecificDomDay + 'L';\n                        break;\n                    case '9':\n                        days = 'L-' + this.result.day.cronDaysBeforeEomMinus;\n                        break;\n                    case '10':\n                        days = this.result.day.cronDaysNearestWeekday+\"W\";\n                        break\n                }\n                return days;\n            },\n            weeksText() {\n                let weeks = '';\n                let cronEvery=this.result.day.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                    case '3':\n                    case '5':\n                        weeks = '?';\n                        break;\n                    case '2':\n                        weeks = this.result.week.incrementStart+'/'+this.result.week.incrementIncrement;\n                        break;\n                    case '4':\n                        this.result.week.specificSpecific.map(val=> {\n                            weeks += val+','\n                        });\n                        weeks = weeks.slice(0, -1);\n                        break;\n                    case '6':\n                    case '7':\n                    case '8':\n                    case '9':\n                    case '10':\n                        weeks = \"?\";\n                        break;\n                    case '11':\n                        weeks = this.result.week.cronNthDayDay+\"#\"+this.result.week.cronNthDayNth;\n                        break;\n                }\n                return weeks;\n            },\n            monthsText() {\n                let months = '';\n                let cronEvery=this.result.month.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        months = '*';\n                        break;\n                    case '2':\n                        months = this.result.month.incrementStart+'/'+this.result.month.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.month.specificSpecific.map(val=> {\n                            months += val+','\n                        });\n                        months = months.slice(0, -1);\n                        break;\n                    case '4':\n                        months = this.result.month.rangeStart+'-'+this.result.month.rangeEnd;\n                        break;\n                }\n                return months;\n            },\n            yearsText() {\n                let years = '';\n                let cronEvery=this.result.year.cronEvery;\n                switch (cronEvery.toString()){\n                    case '1':\n                        years = '*';\n                        break;\n                    case '2':\n                        years = this.result.year.incrementStart+'/'+this.result.year.incrementIncrement;\n                        break;\n                    case '3':\n                        this.result.year.specificSpecific.map(val=> {\n                            years += val+','\n                        });\n                        years = years.slice(0, -1);\n                        break;\n                    case '4':\n                        years = this.result.year.rangeStart+'-'+this.result.year.rangeEnd;\n                        break;\n                }\n                return years;\n            },\n            cron(){\n                return {\n                  value: this.result,\n                  label:`${this.secondsText||'*'} ${this.minutesText||'*'} ${this.hoursText||'*'} ${this.daysText||'*'} ${this.monthsText||'*'} ${this.weeksText||'*'} ${this.yearsText||'*'}`\n                }\n            },\n        },\n        watch:{\n          data(){\n            //this.rest(this.data);\n          }\n        },\n        methods: {\n            show(){\n              //this.rest(pick(this.data.value,'second','minute','hour','day','week','month','year'));\n              //this.rest(this.data.value);\n              Object.assign(this.data.value,this.result);\n              console.log('data初始化',this.data);\n              //this.result = this.data.value;\n              this.visible=true;\n            },\n            getValue(){\n                return this.cron;\n            },\n            change(){\n                console.log('返回前',this.cron);\n                this.$emit('change',this.cron);\n                this.close();\n                this.visible = false;\n            },\n            close(){\n                this.visible = false;\n                //this.$emit('close')\n            },\n            rest(data){\n                for(let i in data){\n                  console.log(data[i]);\n                    if(data[i] instanceof Object){\n                        this.rest(data[i])\n                    }else {\n                      switch(typeof data[i]) {\n                        case 'object':\n                          data[i] = [];\n                          break;\n                        case 'string':\n                          data[i] = '';\n                          break;\n                        case 'number':\n                          data[i] = null;\n                          break;\n                      }\n                    }\n                }\n            },\n            callback (key) {\n                //console.log(key)\n            }\n        }\n    }\n</script>\n\n<style lang=\"less\">\n    .card-container {\n        background: #fff;\n        overflow: hidden;\n        padding: 12px;\n        position: relative;\n        width: 100%;\n        .ant-tabs{\n            border:1px solid #e6ebf5;\n            padding: 0;\n            .ant-tabs-bar {\n                margin: 0;\n                outline: none;\n                border-bottom: none;\n                .ant-tabs-nav-container{\n                    margin: 0;\n                    .ant-tabs-tab {\n                        padding: 0 24px!important;\n                        background-color: #f5f7fa!important;\n                        margin-right: 0px!important;\n                        border-radius: 0;\n                        line-height: 38px;\n                        border: 1px solid transparent!important;\n                        border-bottom: 1px solid #e6ebf5!important;\n                    }\n                    .ant-tabs-tab-active.ant-tabs-tab{\n                        color: #409eff;\n                        background-color: #fff!important;\n                        border-right:1px solid #e6ebf5!important;\n                        border-left:1px solid #e6ebf5!important;\n                        border-bottom:1px solid #fff!important;\n                        font-weight: normal;\n                        transition:none!important;\n                    }\n                }\n            }\n            .ant-tabs-tabpane{\n                padding: 15px;\n                .ant-row{\n                    margin: 10px 0;\n                }\n                .ant-select,.ant-input-number{\n                    width: 100px;\n                }\n            }\n        }\n    }\n</style>\n<style lang=\"less\" scoped>\n    .container-widthEn{\n        width: 755px;\n    }\n    .container-widthCn{\n        width: 608px;\n    }\n    .language{\n        text-align: center;\n        position: absolute;\n        right: 13px;\n        top: 13px;\n        border: 1px solid transparent;\n        height: 40px;\n        line-height: 38px;\n        font-size: 16px;\n        color: #409eff;\n        z-index: 1;\n        background: #f5f7fa;\n        outline: none;\n        width: 47px;\n        border-bottom: 1px solid #e6ebf5;\n        border-radius: 0;\n    }\n    .card-container{\n        .bottom{\n            display: flex;\n            justify-content: center;\n            padding: 10px 0 0 0;\n            .cronButton{\n                margin: 0 10px;\n                line-height: 40px;\n            }\n        }\n    }\n    .tabBody{\n        .a-row{\n            margin: 10px 0;\n            .long{\n                .a-select{\n                    width:354px;\n                }\n            }\n            .a-input-number{\n                width: 110px;\n            }\n        }\n    }\n</style>\n"]}]}