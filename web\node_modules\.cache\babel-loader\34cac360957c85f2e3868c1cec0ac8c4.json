{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue", "mtime": 1749742934824}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: 'Confirm<PERSON><PERSON><PERSON>',\n  props: {\n    message: {\n      type: String,\n      required: true\n    },\n    gameCost: {\n      type: Number,\n      required: true\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "message", "type", "String", "required", "gameCost", "Number"], "sources": ["src/views/game/components/ConfirmDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"confirm-dialog\">\r\n    <div class=\"confirm-content\">\r\n      <div class=\"confirm-header\">\r\n        <div class=\"confirm-icon\">❓</div>\r\n        <h3>确认</h3>\r\n      </div>\r\n      <div class=\"confirm-body\">\r\n        <p>{{ message }}</p>\r\n        <div class=\"coin-info\">\r\n          <span class=\"coin-icon\"></span>\r\n          <span>{{ gameCost }} 金币</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"confirm-footer\">\r\n        <button class=\"confirm-button\" @click=\"$emit('confirm')\">确 定</button>\r\n        <button class=\"cancel-button\" @click=\"$emit('cancel')\">取 消</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ConfirmDialog',\r\n  props: {\r\n    message: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    gameCost: {\r\n      type: Number,\r\n      required: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.confirm-dialog {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}\r\n\r\n.confirm-content {\r\n  background: #ffffff;\r\n  width: 90%;\r\n  max-width: 400px;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n  animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n  border: 5px solid #3a416f;\r\n}\r\n\r\n@keyframes popIn {\r\n  from { transform: scale(0.8); opacity: 0; }\r\n  to { transform: scale(1); opacity: 1; }\r\n}\r\n\r\n.confirm-header {\r\n  background: linear-gradient(135deg, #3a416f, #4e54c8);\r\n  color: white;\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.confirm-icon {\r\n  font-size: 28px;\r\n  margin-right: 10px;\r\n  animation: bounce 1s infinite alternate;\r\n}\r\n\r\n@keyframes bounce {\r\n  from { transform: translateY(0); }\r\n  to { transform: translateY(-5px); }\r\n}\r\n\r\n.confirm-header h3 {\r\n  margin: 0;\r\n  font-size: 22px;\r\n  font-weight: bold;\r\n}\r\n\r\n.confirm-body {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.confirm-body p {\r\n  font-size: 18px;\r\n  margin-bottom: 15px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n.coin-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #ffd86f, #fc6076);\r\n  color: white;\r\n  padding: 10px 15px;\r\n  border-radius: 50px;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin: 15px auto;\r\n  width: fit-content;\r\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.coin-icon {\r\n  display: inline-block;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\r\n  background-size: contain;\r\n  margin-right: 8px;\r\n  animation: spin 10s infinite linear;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotateY(0deg); }\r\n  100% { transform: rotateY(360deg); }\r\n}\r\n\r\n.confirm-footer {\r\n  display: flex;\r\n  padding: 0 20px 20px;\r\n  justify-content: space-around;\r\n}\r\n\r\n.confirm-button, .cancel-button {\r\n  padding: 10px 30px;\r\n  border-radius: 50px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n  width: 45%;\r\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.confirm-button {\r\n  background: linear-gradient(135deg, #4CAF50, #8BC34A);\r\n  color: white;\r\n}\r\n\r\n.confirm-button:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);\r\n}\r\n\r\n.cancel-button {\r\n  background: linear-gradient(135deg, #f44336, #ff9a9e);\r\n  color: white;\r\n}\r\n\r\n.cancel-button:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 6px 15px rgba(244, 67, 54, 0.3);\r\n}\r\n</style> "], "mappings": "AAuBA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;IACA;EACA;AACA", "ignoreList": []}]}