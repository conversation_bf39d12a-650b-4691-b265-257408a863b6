{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JInput.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JInput.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  const JINPUT_QUERY_LIKE = 'like';\n  const JINPUT_QUERY_NE = 'ne';\n  const JINPUT_QUERY_GE = 'ge'; //大于等于\n  const JINPUT_QUERY_LE = 'le'; //小于等于\n  \n  export default {\n    name: 'JInput',\n    props:{\n      value:{\n        type:String,\n        required:false\n      },\n      type:{\n        type:String,\n        required:false,\n        default:JINPUT_QUERY_LIKE\n      },\n      placeholder:{\n        type:String,\n        required:false,\n        default:''\n      }\n    },\n    watch:{\n      value:{\n        immediate:true,\n        handler:function(){\n          this.initVal();\n        }\n      },\n      // update-begin author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n      type() {\n        this.backValue({ target: { value: this.inputVal } })\n      },\n      // update-end author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    data(){\n      return {\n        inputVal:''\n      }\n    },\n    methods:{\n      initVal(){\n        if(!this.value){\n          this.inputVal = ''\n        }else{\n          let text = this.value\n          switch (this.type) {\n            case JINPUT_QUERY_LIKE:\n              text = text.substring(1,text.length-1);\n              break;\n            case JINPUT_QUERY_NE:\n              text = text.substring(1);\n              break;\n            case JINPUT_QUERY_GE:\n              text = text.substring(2);\n              break;\n            case JINPUT_QUERY_LE:\n              text = text.substring(2);\n              break;\n            default:\n          }\n          this.inputVal = text\n        }\n      },\n      backValue(e){\n        let text = e.target.value\n        switch (this.type) {\n          case JINPUT_QUERY_LIKE:\n            text = \"*\"+text+\"*\";\n            break;\n          case JINPUT_QUERY_NE:\n            text = \"!\"+text;\n            break;\n          case JINPUT_QUERY_GE:\n            text = \">=\"+text;\n            break;\n          case JINPUT_QUERY_LE:\n            text = \"<=\"+text;\n            break;\n          default:\n        }\n        this.$emit(\"change\",text)\n      }\n    }\n  }\n", {"version": 3, "sources": ["JInput.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JInput.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-input :placeholder=\"placeholder\" :value=\"inputVal\" @input=\"backValue\"></a-input>\n</template>\n\n<script>\n  const JINPUT_QUERY_LIKE = 'like';\n  const JINPUT_QUERY_NE = 'ne';\n  const JINPUT_QUERY_GE = 'ge'; //大于等于\n  const JINPUT_QUERY_LE = 'le'; //小于等于\n  \n  export default {\n    name: 'JInput',\n    props:{\n      value:{\n        type:String,\n        required:false\n      },\n      type:{\n        type:String,\n        required:false,\n        default:JINPUT_QUERY_LIKE\n      },\n      placeholder:{\n        type:String,\n        required:false,\n        default:''\n      }\n    },\n    watch:{\n      value:{\n        immediate:true,\n        handler:function(){\n          this.initVal();\n        }\n      },\n      // update-begin author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n      type() {\n        this.backValue({ target: { value: this.inputVal } })\n      },\n      // update-end author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    data(){\n      return {\n        inputVal:''\n      }\n    },\n    methods:{\n      initVal(){\n        if(!this.value){\n          this.inputVal = ''\n        }else{\n          let text = this.value\n          switch (this.type) {\n            case JINPUT_QUERY_LIKE:\n              text = text.substring(1,text.length-1);\n              break;\n            case JINPUT_QUERY_NE:\n              text = text.substring(1);\n              break;\n            case JINPUT_QUERY_GE:\n              text = text.substring(2);\n              break;\n            case JINPUT_QUERY_LE:\n              text = text.substring(2);\n              break;\n            default:\n          }\n          this.inputVal = text\n        }\n      },\n      backValue(e){\n        let text = e.target.value\n        switch (this.type) {\n          case JINPUT_QUERY_LIKE:\n            text = \"*\"+text+\"*\";\n            break;\n          case JINPUT_QUERY_NE:\n            text = \"!\"+text;\n            break;\n          case JINPUT_QUERY_GE:\n            text = \">=\"+text;\n            break;\n          case JINPUT_QUERY_LE:\n            text = \"<=\"+text;\n            break;\n          default:\n        }\n        this.$emit(\"change\",text)\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}