{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\CodingQuestion.vue?vue&type=template&id=160e6122&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\CodingQuestion.vue", "mtime": 1753195937252}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"coding-question-wrapper\">\n  <!-- 题目头部信息 -->\n  <div class=\"question-header\">\n    <div class=\"question-type-info\">\n      <a-tag color=\"purple\">\n        <a-icon type=\"code\" />\n        编程题\n      </a-tag>\n    </div>\n    <div class=\"question-nav-container\">\n      <div class=\"navigation-buttons\">\n        <a-button\n          :disabled=\"currentQuestionIndex === 1\"\n          @click=\"handlePrevQuestion\"\n          size=\"small\"\n        >\n          <a-icon type=\"left\" /> 上一题\n        </a-button>\n        <a-button\n          :type=\"(isReviewMode && currentQuestionIndex === questionList.length) ? '' : 'primary'\"\n          :disabled=\"isReviewMode && currentQuestionIndex === questionList.length\"\n          @click=\"handleNextQuestion\"\n          size=\"small\"\n        >\n          {{ isReviewMode ? '下一题' : (currentQuestionIndex === questionList.length ? '完成练习' : '下一题') }} <a-icon type=\"right\" />\n        </a-button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 题目标题 -->\n  <div class=\"question-title\">\n    <div class=\"title-header\">\n      <span class=\"question-number\">{{ currentQuestionIndex }}</span>\n      <h3>\n        {{ currentQuestion.title }}\n        <!-- 题目状态标签 -->\n        <span v-if=\"isReviewMode\" :class=\"['status-tag', getStatusClass(currentQuestionStatus)]\">\n          <a-icon :type=\"getStatusIcon(currentQuestionStatus)\" />\n          {{ getStatusText(currentQuestionStatus) }}\n        </span>\n      </h3>\n      <div class=\"difficulty title-difficulty\">\n        难度: <a-rate :value=\"currentQuestion.difficulty\" :count=\"3\" disabled size=\"small\" />\n      </div>\n    </div>\n  </div>\n\n  <div class=\"coding-question-container\">\n  <a-row :gutter=\"20\">\n    <!-- 题目描述区域 -->\n    <a-col :md=\"10\" :sm=\"24\">\n      <div class=\"problem-description\" v-html=\"markdownToHtml(currentQuestion.content.description)\"></div>\n      <a-divider />\n      <div class=\"problem-section\">\n        <p><strong>输入格式：</strong></p>\n        <div v-html=\"markdownToHtml(currentQuestion.content.input_format)\"></div>\n      </div>\n      <a-divider />\n      <div class=\"problem-section\">\n        <p><strong>输出格式：</strong></p>\n        <div v-html=\"markdownToHtml(currentQuestion.content.output_format)\"></div>\n      </div>\n      <a-divider />\n      <div v-for=\"(sample, index) in currentQuestion.content.sample_cases\" :key=\"index\" class=\"problem-section\">\n        <p><strong>样例 {{ index + 1 }}：</strong></p>\n        <div class=\"sample-container\">\n          <div class=\"sample-input\">\n            <div class=\"sample-header\">输入：</div>\n            <pre>{{ sample.input }}</pre>\n          </div>\n          <div class=\"sample-output\">\n            <div class=\"sample-header\">输出：</div>\n            <pre>{{ sample.output }}</pre>\n          </div>\n        </div>\n      </div>\n      <a-divider v-if=\"currentQuestion.content.hint\" />\n      <div v-if=\"currentQuestion.content.hint\" class=\"problem-section\">\n        <p><strong>提示：</strong></p>\n        <div v-html=\"markdownToHtml(currentQuestion.content.hint)\"></div>\n      </div>\n      \n      <!-- 显示答案和解析 -->\n      <div v-if=\"showAnswer\" class=\"problem-section solution-section\">\n        <a-divider />\n        <div class=\"solution-header\">\n          <a-icon type=\"solution\" /> 参考解答\n        </div>\n        <div class=\"solution-content\" v-if=\"currentQuestion.content.analysis\">\n          <div v-html=\"markdownToHtml(currentQuestion.content.analysis)\"></div>\n        </div>\n        <div class=\"solution-content\" v-else>\n          <p>暂无解析</p>\n        </div>\n      </div>\n    </a-col>\n    \n    <!-- 代码编辑器区域 -->\n    <a-col :md=\"14\" :sm=\"24\">\n      <div class=\"code-editor-container\">\n        <code-mirror\n          ref=\"codeMirror\"\n          :value=\"code\"\n          @update:value=\"updateCode\"\n          :languages=\"supportedLanguages\"\n          :language=\"selectedLanguage\"\n          @changeLang=\"handleLanguageChange\"\n          :openTestCaseDrawer=\"openTestCaseDrawer\"\n          @update:openTestCaseDrawer=\"handleDrawerUpdate\"\n          :pid=\"currentQuestion.id\"\n          :question=\"currentQuestion\"\n          :type=\"'practice'\"\n          :problemTestCase=\"getProblemTestCases\"\n          :isAuthenticated=\"true\"\n          :height=\"editorHeight\"\n          :theme=\"editorTheme\"\n          @changeTheme=\"handleThemeChange\"\n          :fontSize=\"editorFontSize\"\n          @update:fontSize=\"handleFontSizeChange\"\n          :tabSize=\"editorTabSize\"\n          @resetCode=\"resetCode\"\n          @getUserLastAcceptedCode=\"getUserLastAcceptedCode\"\n          @switchFocusMode=\"switchFocusMode\"\n          :openFocusMode=\"isFullScreen\"\n          @submitToEvaluation=\"handleFormalSubmission\"\n          :userInput=\"testInputMap[currentQuestion.id] || ''\"\n          :testJudgeRes=\"testResultMap[currentQuestion.id] || {status: -10, problemJudgeMode: 'default'}\"\n          :activeTestCaseIndex=\"activeTestCaseIndexMap[currentQuestion.id] || -1\"\n          @updateTestInput=\"updateTestInput\"\n          @updateTestResult=\"updateTestResult\"\n          @updateActiveTestCaseIndex=\"updateActiveTestCaseIndex\"\n          :submitDisabled=\"isSubmitting\"\n        />\n      </div>\n    </a-col>\n  </a-row>\n  </div>\n</div>\n", null]}