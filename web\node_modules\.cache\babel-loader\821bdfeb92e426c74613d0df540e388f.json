{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SelectDemo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SelectDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import JDictSelectTag from '../../components/dict/JDictSelectTag.vue';\nimport JSelectDepart from '@/components/jeecgbiz/JSelectDepart';\nimport JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep';\nimport JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser';\nimport JSelectRole from '@/components/jeecgbiz/JSelectRole';\nimport JCheckbox from '@/components/jeecg/JCheckbox';\nimport JCodeEditor from '@/components/jeecg/JCodeEditor';\nimport JDate from '@/components/jeecg/JDate';\nimport JEditor from '@/components/jeecg/JEditor';\nimport JEllipsis from '@/components/jeecg/JEllipsis';\nimport JSlider from '@/components/jeecg/JSlider';\nimport JSelectMultiple from '@/components/jeecg/JSelectMultiple';\nimport JTreeDict from \"../../components/jeecg/JTreeDict.vue\";\nimport J<PERSON>ron from \"@/components/jeecg/JCron.vue\";\nimport JTreeSelect from '@/components/jeecg/JTreeSelect';\nimport JSuperQuery from '@/components/jeecg/JSuperQuery';\nimport JUpload from '@/components/jeecg/JUpload';\nimport JImageUpload from '@/components/jeecg/JImageUpload';\nimport JSelectPosition from '@comp/jeecgbiz/JSelectPosition';\nimport JCategorySelect from '@comp/jeecg/JCategorySelect';\nimport JMultiSelectTag from '@comp/dict/JMultiSelectTag';\nimport JInput from '@comp/jeecg/JInput';\nimport JAreaLinkage from '@comp/jeecg/JAreaLinkage';\nimport JSearchSelectTag from '@/components/dict/JSearchSelectTag';\nexport default {\n  name: 'SelectDemo',\n  inject: ['closeCurrent'],\n  components: {\n    JAreaLinkage: JAreaLinkage,\n    JInput: JInput,\n    JCategorySelect: JCategorySelect,\n    JSelectPosition: JSelectPosition,\n    JImageUpload: JImageUpload,\n    JUpload: JUpload,\n    JTreeDict: JTreeDict,\n    JDictSelectTag: JDictSelectTag,\n    JSelectDepart: JSelectDepart,\n    JSelectUserByDep: JSelectUserByDep,\n    JSelectMultiUser: JSelectMultiUser,\n    JSelectRole: JSelectRole,\n    JCheckbox: JCheckbox,\n    JCodeEditor: JCodeEditor,\n    JDate: JDate,\n    JEditor: JEditor,\n    JEllipsis: JEllipsis,\n    JSlider: JSlider,\n    JSelectMultiple: JSelectMultiple,\n    JCron: JCron,\n    JTreeSelect: JTreeSelect,\n    JSuperQuery: JSuperQuery,\n    JMultiSelectTag: JMultiSelectTag,\n    JSearchSelectTag: JSearchSelectTag\n  },\n  data: function data() {\n    return {\n      selectList: [],\n      selectedDepUsers: '',\n      formData: {\n        areaLinkage1: '110105',\n        areaLinkage2: '140221',\n        sex: 1\n      },\n      form: this.$form.createForm(this),\n      departId: '4f1765520d6346f9bd9c79e2479e5b12,57197590443c44f083d42ae24ef26a2c',\n      userIds: 'admin',\n      multiUser: 'admin,jeecg',\n      jcheckbox: {\n        values: 'spring,jeecgboot',\n        options: [{\n          label: 'Jeecg',\n          value: 'jeecg'\n        }, {\n          label: 'Jeecg-Boot',\n          value: 'jeecgboot'\n        }, {\n          label: 'Spring',\n          value: 'spring',\n          disabled: true\n        }, {\n          label: 'MyBaits',\n          value: 'mybatis'\n        }]\n      },\n      jcodedditor: {\n        value: \"function sayHi(word) {\\n  alert(word)\\n}\\nsayHi('hello, world!')\"\n      },\n      jdate: {\n        value: '2019-5-10 15:33:06'\n      },\n      jeditor: {\n        value: '<h2 style=\"text-align: center;\">富文本编辑器</h2> <p>这里是富文本编辑器。</p>'\n      },\n      jellipsis: {\n        value: '这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。'\n      },\n      jslider: {\n        value: false\n      },\n      jselectMultiple: {\n        options: [{\n          text: '字符串',\n          value: 'String'\n        }, {\n          text: '整数型',\n          value: 'Integer'\n        }, {\n          text: '浮点型',\n          value: 'Double'\n        }, {\n          text: '布尔型',\n          value: 'Boolean'\n        }],\n        value: 'Integer,Boolean'\n      },\n      modal: {\n        title: '这里是标题',\n        visible: false,\n        fullscreen: true,\n        switchFullscreen: true\n      },\n      cron: '',\n      superQuery: {\n        fieldList: [{\n          type: 'input',\n          value: 'name',\n          text: '姓名'\n        }, {\n          type: 'select',\n          value: 'sex',\n          text: '性别',\n          dictCode: 'sex'\n        }, {\n          type: 'number',\n          value: 'age',\n          text: '年龄'\n        }, {\n          type: 'select',\n          value: 'hobby',\n          text: '爱好',\n          options: [{\n            label: '音乐',\n            value: '1'\n          }, {\n            label: '游戏',\n            value: '2'\n          }, {\n            label: '电影',\n            value: '3'\n          }, {\n            label: '读书',\n            value: '4'\n          }]\n        }]\n      },\n      fileList: [],\n      imgList: [],\n      jInput: {\n        type: 'like',\n        options: [{\n          value: 'like',\n          label: '模糊（like）'\n        }, {\n          value: 'ne',\n          label: '不等于（ne）'\n        }, {\n          value: 'ge',\n          label: '大于等于（ge）'\n        }, {\n          value: 'le',\n          label: '小于等于（le)'\n        }]\n      },\n      content: '',\n      searchOptions: [{\n        text: \"选项一\",\n        value: \"1\"\n      }, {\n        text: \"选项二\",\n        value: \"2\"\n      }, {\n        text: \"选项三\",\n        value: \"3\"\n      }]\n    };\n  },\n  computed: {\n    nameList: function nameList() {\n      var names = [];\n      for (var a = 0; a < this.selectList.length; a++) {\n        names.push(this.selectList[a].name);\n      }\n      return names;\n    }\n  },\n  methods: {\n    handleChange: function handleChange() {},\n    getDepartIdValue: function getDepartIdValue() {\n      return this.form.getFieldValue('departId');\n    },\n    changeMe: function changeMe() {\n      console.log('you so ...  , change Me');\n    },\n    selectOK: function selectOK(data) {\n      this.selectList = data;\n    },\n    handleSelect: function handleSelect() {\n      this.$refs.selectDemoModal.add();\n    },\n    selectReset: function selectReset() {\n      this.selectList = [];\n    },\n    //通过组织机构筛选选择用户\n    onSearchDepUser: function onSearchDepUser() {\n      this.$refs.JSearchUserByDep.showModal();\n      this.selectedDepUsers = '';\n      this.$refs.JSearchUserByDep.title = '根据部门查询用户';\n    },\n    onSearchDepUserCallBack: function onSearchDepUserCallBack(selectedDepUsers) {\n      this.selectedDepUsers = selectedDepUsers;\n    },\n    handleJSliderSuccess: function handleJSliderSuccess(value) {\n      this.jslider.value = value;\n    },\n    setCorn: function setCorn(data) {\n      var _this = this;\n      this.$nextTick(function () {\n        _this.form.cronExpression = data;\n      });\n    },\n    handleCloseCurrentPage: function handleCloseCurrentPage() {\n      // 注意：以下代码必须存在\n      // inject:['closeCurrent'],\n      this.closeCurrent();\n    }\n  }\n};", {"version": 3, "names": ["JDictSelectTag", "JSelectDepart", "JSelectUserByDep", "JSelectMultiUser", "JSelectRole", "JCheckbox", "JCodeEditor", "JDate", "JEditor", "JElli<PERSON>", "JSlider", "JSelectMultiple", "JTreeDict", "JCron", "JTreeSelect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "JUpload", "JImageUpload", "JSelectPosition", "JCategorySelect", "JMultiSelectTag", "JInput", "JAreaLinkage", "JSearchSelectTag", "name", "inject", "components", "data", "selectList", "selectedDepUsers", "formData", "areaLinkage1", "areaLinkage2", "sex", "form", "$form", "createForm", "departId", "userIds", "multiUser", "jcheckbox", "values", "options", "label", "value", "disabled", "jcodedditor", "jdate", "jeditor", "jellipsis", "jslider", "jselectMultiple", "text", "modal", "title", "visible", "fullscreen", "switchFullscreen", "cron", "superQuery", "fieldList", "type", "dictCode", "fileList", "imgList", "jInput", "content", "searchOptions", "computed", "nameList", "names", "a", "length", "push", "methods", "handleChange", "getDepartIdValue", "getFieldValue", "changeMe", "console", "log", "selectOK", "handleSelect", "$refs", "selectDemoModal", "add", "selectReset", "onSearchDepUser", "JSearchUserByDep", "showModal", "onSearchDepUserCallBack", "handleJSliderSuccess", "setCorn", "_this", "$nextTick", "cronExpression", "handleCloseCurrentPage", "closeCurrent"], "sources": ["src/views/jeecg/SelectDemo.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\" style=\"height:100%;padding-bottom:200px; \">\n\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" :form=\"form\">\n\n\n        <!--  字典下拉 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"性别\">\n              <j-dict-select-tag v-model=\"formData.sex\" title=\"性别\" dictCode=\"sex\" placeholder=\"请选择性别\"/>\n              <!--  <j-dict-select-tag title=\"性别\" dictCode=\"sex\" disabled/>-->\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.sex}}</a-col>\n        </a-row>\n\n        <!--  字典表下拉 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"字典表下拉\">\n              <j-dict-select-tag v-model=\"formData.user\" placeholder=\"请选择用户\" dictCode=\"sys_user,realname,id\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.user}}</a-col>\n        </a-row>\n\n        <!--  带条件字典表下拉 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"字典表下拉(带条件)\">\n              <j-dict-select-tag v-model=\"formData.user2\" placeholder=\"请选择用户\" dictCode=\"sys_user,realname,id,username!='admin' order by create_time\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.user2}}</a-col>\n        </a-row>\n\n\n        <!-- 字典搜索  -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"字典搜索(同步)\">\n              <j-search-select-tag placeholder=\"请做出你的选择\" v-model=\"formData.searchValue\" :dictOptions=\"searchOptions\">\n              </j-search-select-tag>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.searchValue}}</a-col>\n        </a-row>\n\n        <!--  字典搜索 异步加载 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"字典搜索(异步)\">\n              <j-search-select-tag\n                placeholder=\"请做出你的选择\"\n                v-model=\"formData.asyncSelectValue\"\n                dict=\"sys_depart,depart_name,id\"\n                :async=\"true\">\n              </j-search-select-tag>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.asyncSelectValue}}</a-col>\n        </a-row>\n\n        <!--  JMultiSelectTag -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"字典下拉(多选)\">\n              <j-multi-select-tag\n                v-model=\"formData.selMuti\"\n                dictCode=\"sex\"\n                placeholder=\"请选择\">\n              </j-multi-select-tag>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">多选组合(v-model)：{{ formData.selMuti }}</a-col>\n        </a-row>\n\n        <!--  部门选择控件 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择部门 自定义返回值\">\n              <j-select-depart v-decorator=\"['departId']\" :trigger-change=\"true\" customReturnField=\"departName\"></j-select-depart>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的部门ID(v-decorator):{{ getDepartIdValue() }}</a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择部门\">\n              <j-select-depart v-model=\"departId\" :multi=\"true\"></j-select-depart>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的部门ID(v-model):{{ departId }}</a-col>\n        </a-row>\n\n        <!--  通过部门选择用户控件 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择用户\">\n              <j-select-user-by-dep v-model=\"userIds\" :multi=\"true\"></j-select-user-by-dep>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的用户(v-model):{{ userIds }}</a-col>\n        </a-row>\n\n        <!--  用户选择控件 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择用户\">\n              <j-select-multi-user v-model=\"multiUser\" ></j-select-multi-user>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的用户(v-model):{{ multiUser }}</a-col>\n        </a-row>\n\n        <!-- 角色选择 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择角色\">\n              <j-select-role v-model=\"formData.selectRole\" @change=\"changeMe\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.selectRole}}</a-col>\n        </a-row>\n\n        <!-- 职务选择 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"选择职务\">\n              <j-select-position  v-model=\"formData.selectPosition\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中值：{{ formData.selectPosition}}</a-col>\n        </a-row>\n\n        <!--  JCheckbox -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"多选组合\">\n              <j-checkbox\n                v-model=\"jcheckbox.values\"\n                :options=\"jcheckbox.options\"\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">多选组合(v-model)：{{ jcheckbox.values }}</a-col>\n        </a-row>\n\n        <!--  JCodeEditor -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"代码输入框\" style=\"min-height: 120px\">\n              <j-code-editor\n                language=\"javascript\"\n                v-model=\"jcodedditor.value\"\n                :fullScreen=\"true\"\n                style=\"min-height: 100px\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">代码输入框(v-model)：{{ jcodedditor.value }}</a-col>\n        </a-row>\n\n        <!--  JDate -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"日期选择框\">\n              <j-date v-model=\"jdate.value\" :showTime=\"true\" dateFormat=\"YYYY-MM-DD HH:mm:ss\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">日期选择框(v-model)：{{ jdate.value }}</a-col>\n        </a-row>\n\n        <!-- JEditor -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"富文本编辑器\" style=\"min-height: 300px\">\n              <j-editor v-model=\"jeditor.value\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">富文本编辑器(v-model)：{{ jeditor.value }}</a-col>\n        </a-row>\n\n        <!-- JEllipsis -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"过长剪切\">\n              <j-ellipsis :value=\"jellipsis.value\" :length=\"30\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">过长剪切：{{ jellipsis.value }}</a-col>\n        </a-row>\n\n        <!-- JSlider -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"滑块验证码\">\n              <j-slider @onSuccess=\"handleJSliderSuccess\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">滑块验证码验证通过：{{ jslider.value }}</a-col>\n        </a-row>\n\n        <!-- JSelectMultiple -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"多选下拉框\">\n              <j-select-multiple v-model=\"jselectMultiple.value\" :options=\"jselectMultiple.options\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">多选下拉框(v-model)：{{ jselectMultiple.value }}</a-col>\n        </a-row>\n\n        <!-- JSelectMultiple -->\n        <a-row :gutter=\"24\">\n          <a-col>\n\n            <a-form-item label=\"JModal弹窗\">\n              <a-button style=\"margin-right: 8px;\" @click=\"()=>modal.visible=true\">点击弹出JModal</a-button>\n              <span style=\"margin-right: 8px;\">全屏化：<a-switch v-model=\"modal.fullscreen\"/></span>\n              <span style=\"margin-right: 8px;\">允许切换全屏：<a-switch v-model=\"modal.switchFullscreen\"/></span>\n\n            </a-form-item>\n\n            <j-modal\n              :visible.sync=\"modal.visible\"\n              :width=\"1200\"\n              :title=\"modal.title\"\n              :fullscreen.sync=\"modal.fullscreen\"\n              :switchFullscreen=\"modal.switchFullscreen\"\n            >\n\n              <template v-for=\"(i,k) of 30\">\n                <p :key=\"k\">这是主体内容，高度是自适应的</p>\n              </template>\n\n            </j-modal>\n\n          </a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"树字典\">\n              <j-tree-dict v-model=\"formData.treeDict\" placeholder=\"请选择树字典\" parentCode=\"B01\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的值(v-model)：{{ formData.treeDict }}</a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"下拉树选择\">\n              <j-tree-select\n                v-model=\"formData.treeSelect\"\n                placeholder=\"请选择菜单\"\n                dict=\"sys_permission,name,id\"\n                pidField=\"parent_id\"\n                pidValue=\"\"\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的值(v-model)：{{ formData.treeSelect }}</a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"下拉树多选\">\n              <j-tree-select\n                v-model=\"formData.treeSelectMultiple\"\n                placeholder=\"请选择菜单\"\n                dict=\"sys_permission,name,id\"\n                pidField=\"parent_id\"\n                pidValue=\"\"\n                multiple\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的值(v-model)：{{ formData.treeSelectMultiple }}</a-col>\n        </a-row>\n\n        <!-- 分类字典树 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"分类字典树\">\n              <j-category-select v-model=\"formData.selectCategory\" pcode=\"A01\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的值(v-model)：{{ formData.selectCategory }}</a-col>\n        </a-row>\n\n        <!-- VueCron -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"cron表达式\">\n              <j-cron ref=\"innerVueCron\" v-decorator=\"['cronExpression', { initialValue: '* * * * * ? *' }]\" @change=\"setCorn\"></j-cron>\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"高级查询\">\n              <j-super-query :fieldList=\"superQuery.fieldList\" />\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"高级查询（自定义按钮）\">\n              <j-super-query :fieldList=\"superQuery.fieldList\">\n                <!--\n                    v-slot:button 可以更高自由的定制按钮\n                    参数介绍：\n                      isActive: 是否是激活状态（已有高级查询条件生效）\n                      isMobile: 当前是否是移动端，可针对移动端展示不同的样式\n                          open: 打开弹窗，一个方法，可绑定点击事件\n                         reset: 重置所有查询条件，一个方法，可绑定点击事件\n                -->\n                <template v-slot:button=\"{isActive,isMobile,open,reset}\">\n                  <!-- 定义的是普通状态下的按钮 -->\n                  <a-button v-if=\"!isActive\" type=\"primary\" ghost icon=\"clock-circle\" @click=\"open()\">高级查询</a-button>\n                  <!-- 定义的当有高级查询条件生效状态下的按钮 -->\n                  <a-button-group v-else>\n                    <a-button type=\"primary\" ghost @click=\"open()\">\n                      <a-icon type=\"plus-circle\" spin/>\n                      <span>高级查询</span>\n                    </a-button>\n                    <a-button v-if=\"isMobile\" type=\"primary\" ghost icon=\"delete\" @click=\"reset()\"/>\n                  </a-button-group>\n                </template>\n              </j-super-query>\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"图片上传\">\n              <j-image-upload v-model=\"imgList\"></j-image-upload>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选中的值(v-model)：{{ imgList }}</a-col>\n        </a-row>\n        <a-row :gutter=\"24\" style=\"margin-top: 65px;margin-bottom:50px;\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"文件上传\">\n              <j-upload v-model=\"fileList\"></j-upload>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">\n            选中的值(v-model)：\n            <j-ellipsis :value=\"fileList\" :length=\"30\" v-if=\"fileList.length>0\"/>\n          </a-col>\n        </a-row>\n\n        <!-- 特殊查询组件 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"特殊查询组件\">\n              <a-row>\n                <a-col :span=\"16\">\n                  <j-input v-model=\"formData.jInput\" :type=\"jInput.type\"/>\n                </a-col>\n                <a-col :span=\"3\" style=\"text-align: right;\" >查询类型：</a-col>\n                <a-col :span=\"5\">\n                  <a-select v-model=\"jInput.type\" :options=\"jInput.options\"></a-select>\n                </a-col>\n              </a-row>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">输入的值(v-model)：{{ formData.jInput }}</a-col>\n        </a-row>\n       <!-- <a-row :gutter=\"24\">\n          <a-col :span=\"15\">\n            <a-form-item label=\"MarkdownEditor\" style=\"min-height: 300px\">\n              <j-markdown-editor v-model=\"content\"></j-markdown-editor>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"9\">\n            输入的值(v-model)：{{ content }}\n          </a-col>\n        </a-row>-->\n\n        <!-- 省市县级联 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"省市县级联\">\n              <j-area-linkage v-model=\"formData.areaLinkage1\" type=\"cascader\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">输入的值(v-model)：{{ formData.areaLinkage1 }}</a-col>\n        </a-row>\n\n\n        <!-- 省市县级联 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"省市县级联\">\n              <j-area-linkage v-model=\"formData.areaLinkage2\" type=\"select\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">输入的值(v-model)：{{ formData.areaLinkage2 }}</a-col>\n        </a-row>\n\n        <!-- 功能示例：关闭当前页面 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"功能示例：关闭当前页面\">\n              <a-button type=\"primary\" @click=\"handleCloseCurrentPage\">点击关闭当前页面</a-button>\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n        <!-- JPopup示例 -->\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <a-form-item label=\"JPopup示例\">\n              <j-popup v-model=\"formData.jPopup\" code=\"demo\" field=\"name\" orgFields=\"name\" destFields=\"name\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">选择的值(v-model)：{{ formData.jPopup }}</a-col>\n        </a-row>\n\n      </a-form>\n    </div>\n\n  </a-card>\n</template>\n\n<script>\n\n  import JDictSelectTag from '../../components/dict/JDictSelectTag.vue'\n  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'\n  import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'\n  import JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser'\n  import JSelectRole from '@/components/jeecgbiz/JSelectRole'\n  import JCheckbox from '@/components/jeecg/JCheckbox'\n  import JCodeEditor from '@/components/jeecg/JCodeEditor'\n  import JDate from '@/components/jeecg/JDate'\n  import JEditor from '@/components/jeecg/JEditor'\n  import JEllipsis from '@/components/jeecg/JEllipsis'\n  import JSlider from '@/components/jeecg/JSlider'\n  import JSelectMultiple from '@/components/jeecg/JSelectMultiple'\n  import JTreeDict from \"../../components/jeecg/JTreeDict.vue\";\n  import JCron from \"@/components/jeecg/JCron.vue\";\n  import JTreeSelect from '@/components/jeecg/JTreeSelect'\n  import JSuperQuery from '@/components/jeecg/JSuperQuery'\n  import JUpload from '@/components/jeecg/JUpload'\n  import JImageUpload from '@/components/jeecg/JImageUpload'\n  import JSelectPosition from '@comp/jeecgbiz/JSelectPosition'\n  import JCategorySelect from '@comp/jeecg/JCategorySelect'\n  import JMultiSelectTag from '@comp/dict/JMultiSelectTag'\n  import JInput from '@comp/jeecg/JInput'\n  import JAreaLinkage from '@comp/jeecg/JAreaLinkage'\n  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'\n\n  export default {\n    name: 'SelectDemo',\n    inject:['closeCurrent'],\n    components: {\n      JAreaLinkage,\n      JInput,\n      JCategorySelect,\n      JSelectPosition,\n      JImageUpload,\n      JUpload,\n      JTreeDict,\n      JDictSelectTag,\n      JSelectDepart,\n      JSelectUserByDep,\n      JSelectMultiUser,\n      JSelectRole,\n      JCheckbox,\n      JCodeEditor,\n      JDate, JEditor, JEllipsis, JSlider, JSelectMultiple,\n      JCron, JTreeSelect, JSuperQuery, JMultiSelectTag,\n      JSearchSelectTag\n    },\n    data() {\n      return {\n        selectList: [],\n        selectedDepUsers: '',\n        formData: {\n          areaLinkage1: '110105',\n          areaLinkage2: '140221',\n          sex: 1\n        },\n        form: this.$form.createForm(this),\n        departId: '4f1765520d6346f9bd9c79e2479e5b12,57197590443c44f083d42ae24ef26a2c',\n        userIds: 'admin',\n        multiUser: 'admin,jeecg',\n        jcheckbox: {\n          values: 'spring,jeecgboot',\n          options: [\n            { label: 'Jeecg', value: 'jeecg' },\n            { label: 'Jeecg-Boot', value: 'jeecgboot' },\n            { label: 'Spring', value: 'spring', disabled: true },\n            { label: 'MyBaits', value: 'mybatis' }\n          ]\n        },\n        jcodedditor: {\n          value: `function sayHi(word) {\n  alert(word)\n}\nsayHi('hello, world!')`\n        },\n        jdate: {\n          value: '2019-5-10 15:33:06'\n        },\n        jeditor: {\n          value: '<h2 style=\"text-align: center;\">富文本编辑器</h2> <p>这里是富文本编辑器。</p>'\n        },\n        jellipsis: {\n          value: '这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。'\n        },\n        jslider: {\n          value: false\n        },\n        jselectMultiple: {\n          options: [\n            { text: '字符串', value: 'String' },\n            { text: '整数型', value: 'Integer' },\n            { text: '浮点型', value: 'Double' },\n            { text: '布尔型', value: 'Boolean' }\n          ],\n          value: 'Integer,Boolean'\n        },\n        modal: {\n          title: '这里是标题',\n          visible: false,\n          fullscreen: true,\n          switchFullscreen: true,\n        },\n        cron: '',\n        superQuery: {\n          fieldList: [\n            { type: 'input', value: 'name', text: '姓名', },\n            { type: 'select', value: 'sex', text: '性别', dictCode: 'sex' },\n            { type: 'number', value: 'age', text: '年龄', },\n            {\n              type: 'select', value: 'hobby', text: '爱好',\n              options: [\n                { label: '音乐', value: '1' },\n                { label: '游戏', value: '2' },\n                { label: '电影', value: '3' },\n                { label: '读书', value: '4' },\n              ]\n            },\n          ]\n        },\n        fileList:[],\n        imgList:[],\n        jInput: {\n          type: 'like',\n          options: [\n            { value: 'like', label: '模糊（like）' },\n            { value: 'ne', label: '不等于（ne）' },\n            { value: 'ge', label: '大于等于（ge）' },\n            { value: 'le', label: '小于等于（le)' },\n          ],\n        },\n        content: '',\n        searchOptions:[{\n          text:\"选项一\",\n          value:\"1\"\n        },{\n          text:\"选项二\",\n          value:\"2\"\n        },{\n          text:\"选项三\",\n          value:\"3\"\n        }],\n\n      }\n    },\n    computed: {\n      nameList: function() {\n\n        var names = []\n        for (var a = 0; a < this.selectList.length; a++) {\n          names.push(this.selectList[a].name)\n        }\n        return names\n      }\n    },\n    methods: {\n      handleChange() {\n      },\n      getDepartIdValue() {\n        return this.form.getFieldValue('departId')\n      },\n      changeMe() {\n        console.log('you so ...  , change Me')\n      },\n      selectOK: function(data) {\n        this.selectList = data\n      },\n      handleSelect: function() {\n        this.$refs.selectDemoModal.add()\n      },\n      selectReset() {\n        this.selectList = []\n      },\n      //通过组织机构筛选选择用户\n      onSearchDepUser() {\n        this.$refs.JSearchUserByDep.showModal()\n        this.selectedDepUsers = ''\n        this.$refs.JSearchUserByDep.title = '根据部门查询用户'\n      },\n      onSearchDepUserCallBack(selectedDepUsers) {\n        this.selectedDepUsers = selectedDepUsers\n      },\n      handleJSliderSuccess(value) {\n        this.jslider.value = value\n      },\n      setCorn(data){\n        this.$nextTick(() => {\n          this.form.cronExpression = data;\n        })\n      },\n\n      handleCloseCurrentPage() {\n        // 注意：以下代码必须存在\n        // inject:['closeCurrent'],\n        this.closeCurrent()\n      },\n\n    }\n  }\n</script>\n<style lang=\"less\" scoped>\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n</style>"], "mappings": "AAmbA,OAAAA,cAAA;AACA,OAAAC,aAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,WAAA;AACA,OAAAC,SAAA;AACA,OAAAC,WAAA;AACA,OAAAC,KAAA;AACA,OAAAC,OAAA;AACA,OAAAC,SAAA;AACA,OAAAC,OAAA;AACA,OAAAC,eAAA;AACA,OAAAC,SAAA;AACA,OAAAC,KAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA,OAAAC,OAAA;AACA,OAAAC,YAAA;AACA,OAAAC,eAAA;AACA,OAAAC,eAAA;AACA,OAAAC,eAAA;AACA,OAAAC,MAAA;AACA,OAAAC,YAAA;AACA,OAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,MAAA;EACAC,UAAA;IACAJ,YAAA,EAAAA,YAAA;IACAD,MAAA,EAAAA,MAAA;IACAF,eAAA,EAAAA,eAAA;IACAD,eAAA,EAAAA,eAAA;IACAD,YAAA,EAAAA,YAAA;IACAD,OAAA,EAAAA,OAAA;IACAJ,SAAA,EAAAA,SAAA;IACAZ,cAAA,EAAAA,cAAA;IACAC,aAAA,EAAAA,aAAA;IACAC,gBAAA,EAAAA,gBAAA;IACAC,gBAAA,EAAAA,gBAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,KAAA,EAAAA,KAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,SAAA,EAAAA,SAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,eAAA,EAAAA,eAAA;IACAE,KAAA,EAAAA,KAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAK,eAAA,EAAAA,eAAA;IACAG,gBAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,gBAAA;MACAC,QAAA;QACAC,YAAA;QACAC,YAAA;QACAC,GAAA;MACA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,QAAA;MACAC,OAAA;MACAC,SAAA;MACAC,SAAA;QACAC,MAAA;QACAC,OAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA;MAEA;MACAE,WAAA;QACAF,KAAA;MAIA;MACAG,KAAA;QACAH,KAAA;MACA;MACAI,OAAA;QACAJ,KAAA;MACA;MACAK,SAAA;QACAL,KAAA;MACA;MACAM,OAAA;QACAN,KAAA;MACA;MACAO,eAAA;QACAT,OAAA,GACA;UAAAU,IAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,IAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,IAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,IAAA;UAAAR,KAAA;QAAA,EACA;QACAA,KAAA;MACA;MACAS,KAAA;QACAC,KAAA;QACAC,OAAA;QACAC,UAAA;QACAC,gBAAA;MACA;MACAC,IAAA;MACAC,UAAA;QACAC,SAAA,GACA;UAAAC,IAAA;UAAAjB,KAAA;UAAAQ,IAAA;QAAA,GACA;UAAAS,IAAA;UAAAjB,KAAA;UAAAQ,IAAA;UAAAU,QAAA;QAAA,GACA;UAAAD,IAAA;UAAAjB,KAAA;UAAAQ,IAAA;QAAA,GACA;UACAS,IAAA;UAAAjB,KAAA;UAAAQ,IAAA;UACAV,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;MAEA;MACAmB,QAAA;MACAC,OAAA;MACAC,MAAA;QACAJ,IAAA;QACAnB,OAAA,GACA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAC,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAC,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAC,KAAA;UAAAD,KAAA;QAAA;MAEA;MACAuB,OAAA;MACAC,aAAA;QACAf,IAAA;QACAR,KAAA;MACA;QACAQ,IAAA;QACAR,KAAA;MACA;QACAQ,IAAA;QACAR,KAAA;MACA;IAEA;EACA;EACAwB,QAAA;IACAC,QAAA,WAAAA,SAAA;MAEA,IAAAC,KAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAA3C,UAAA,CAAA4C,MAAA,EAAAD,CAAA;QACAD,KAAA,CAAAG,IAAA,MAAA7C,UAAA,CAAA2C,CAAA,EAAA/C,IAAA;MACA;MACA,OAAA8C,KAAA;IACA;EACA;EACAI,OAAA;IACAC,YAAA,WAAAA,aAAA,GACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,YAAA1C,IAAA,CAAA2C,aAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACAC,OAAA,CAAAC,GAAA;IACA;IACAC,QAAA,WAAAA,SAAAtD,IAAA;MACA,KAAAC,UAAA,GAAAD,IAAA;IACA;IACAuD,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,GAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA1D,UAAA;IACA;IACA;IACA2D,eAAA,WAAAA,gBAAA;MACA,KAAAJ,KAAA,CAAAK,gBAAA,CAAAC,SAAA;MACA,KAAA5D,gBAAA;MACA,KAAAsD,KAAA,CAAAK,gBAAA,CAAAlC,KAAA;IACA;IACAoC,uBAAA,WAAAA,wBAAA7D,gBAAA;MACA,KAAAA,gBAAA,GAAAA,gBAAA;IACA;IACA8D,oBAAA,WAAAA,qBAAA/C,KAAA;MACA,KAAAM,OAAA,CAAAN,KAAA,GAAAA,KAAA;IACA;IACAgD,OAAA,WAAAA,QAAAjE,IAAA;MAAA,IAAAkE,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAA3D,IAAA,CAAA6D,cAAA,GAAApE,IAAA;MACA;IACA;IAEAqE,sBAAA,WAAAA,uBAAA;MACA;MACA;MACA,KAAAC,YAAA;IACA;EAEA;AACA", "ignoreList": []}]}