{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\CountDown\\CountDown.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\CountDown\\CountDown.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  function fixedZero(val) {\n    return val * 1 < 10 ? `0${val}` : val;\n  }\n\n  export default {\n    name: \"CountDown\",\n    props: {\n      format: {\n        type: Function,\n        default: undefined\n      },\n      target: {\n        type: [Date, Number],\n        required: true,\n      },\n      onEnd: {\n        type: Function,\n        default: () => {\n        }\n      }\n    },\n    data() {\n      return {\n        dateTime: '0',\n        originTargetTime: 0,\n        lastTime: 0,\n        timer: 0,\n        interval: 1000\n      }\n    },\n    filters: {\n      format(time) {\n        const hours = 60 * 60 * 1000;\n        const minutes = 60 * 1000;\n\n        const h = Math.floor(time / hours);\n        const m = Math.floor((time - h * hours) / minutes);\n        const s = Math.floor((time - h * hours - m * minutes) / 1000);\n        return `${fixedZero(h)}:${fixedZero(m)}:${fixedZero(s)}`\n      }\n    },\n    created() {\n      this.initTime()\n      this.tick()\n    },\n    methods: {\n      initTime() {\n        let lastTime = 0;\n        let targetTime = 0;\n        this.originTargetTime = this.target\n        try {\n          if (Object.prototype.toString.call(this.target) === '[object Date]') {\n            targetTime = this.target\n          } else {\n            targetTime = new Date(this.target).getTime()\n          }\n        } catch (e) {\n          throw new Error('invalid target prop')\n        }\n\n        lastTime = targetTime - new Date().getTime();\n\n        this.lastTime = lastTime < 0 ? 0 : lastTime\n      },\n      tick() {\n        const {onEnd} = this\n\n        this.timer = setTimeout(() => {\n          if (this.lastTime < this.interval) {\n            clearTimeout(this.timer)\n            this.lastTime = 0\n            if (typeof onEnd === 'function') {\n              onEnd();\n            }\n          } else {\n            this.lastTime -= this.interval\n            this.tick()\n          }\n        }, this.interval)\n      }\n    },\n    beforeUpdate () {\n      if (this.originTargetTime !== this.target) {\n        this.initTime()\n      }\n    },\n    beforeDestroy() {\n      clearTimeout(this.timer)\n    }\n  }\n", {"version": 3, "sources": ["CountDown.vue"], "names": [], "mappings": ";;AAQA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CountDown.vue", "sourceRoot": "src/components/CountDown", "sourcesContent": ["<template>\n  <span>\n    {{ lastTime | format }}\n  </span>\n</template>\n\n<script>\n\n  function fixedZero(val) {\n    return val * 1 < 10 ? `0${val}` : val;\n  }\n\n  export default {\n    name: \"CountDown\",\n    props: {\n      format: {\n        type: Function,\n        default: undefined\n      },\n      target: {\n        type: [Date, Number],\n        required: true,\n      },\n      onEnd: {\n        type: Function,\n        default: () => {\n        }\n      }\n    },\n    data() {\n      return {\n        dateTime: '0',\n        originTargetTime: 0,\n        lastTime: 0,\n        timer: 0,\n        interval: 1000\n      }\n    },\n    filters: {\n      format(time) {\n        const hours = 60 * 60 * 1000;\n        const minutes = 60 * 1000;\n\n        const h = Math.floor(time / hours);\n        const m = Math.floor((time - h * hours) / minutes);\n        const s = Math.floor((time - h * hours - m * minutes) / 1000);\n        return `${fixedZero(h)}:${fixedZero(m)}:${fixedZero(s)}`\n      }\n    },\n    created() {\n      this.initTime()\n      this.tick()\n    },\n    methods: {\n      initTime() {\n        let lastTime = 0;\n        let targetTime = 0;\n        this.originTargetTime = this.target\n        try {\n          if (Object.prototype.toString.call(this.target) === '[object Date]') {\n            targetTime = this.target\n          } else {\n            targetTime = new Date(this.target).getTime()\n          }\n        } catch (e) {\n          throw new Error('invalid target prop')\n        }\n\n        lastTime = targetTime - new Date().getTime();\n\n        this.lastTime = lastTime < 0 ? 0 : lastTime\n      },\n      tick() {\n        const {onEnd} = this\n\n        this.timer = setTimeout(() => {\n          if (this.lastTime < this.interval) {\n            clearTimeout(this.timer)\n            this.lastTime = 0\n            if (typeof onEnd === 'function') {\n              onEnd();\n            }\n          } else {\n            this.lastTime -= this.interval\n            this.tick()\n          }\n        }, this.interval)\n      }\n    },\n    beforeUpdate () {\n      if (this.originTargetTime !== this.target) {\n        this.initTime()\n      }\n    },\n    beforeDestroy() {\n      clearTimeout(this.timer)\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}