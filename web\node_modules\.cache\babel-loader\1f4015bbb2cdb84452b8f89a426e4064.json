{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue", "mtime": 1753194711604}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nexport default {\n  name: 'PracticeStatusBar',\n  props: {\n    // 基本状态\n    isFullScreen: {\n      type: Boolean,\n      default: false\n    },\n    isReviewMode: {\n      type: Boolean,\n      default: false\n    },\n    showAnswer: {\n      type: Boolean,\n      default: false\n    },\n    // 练习配置\n    practiseMode: {\n      type: String,\n      default: ''\n    },\n    practiseCount: {\n      type: Number,\n      default: 10\n    },\n    practiceTitle: {\n      type: String,\n      default: ''\n    },\n    remainingTimeText: {\n      type: String,\n      default: '00:00'\n    },\n    // 题目数据\n    questionList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    currentQuestionIndex: {\n      type: Number,\n      default: 1\n    },\n    answeredQuestions: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    userAnswersMap: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    // 收藏状态\n    isCollected: {\n      type: Boolean,\n      default: false\n    },\n    collectLoading: {\n      type: Boolean,\n      default: false\n    },\n    // 错题练习模式\n    isWrongRecordsMode: {\n      type: Boolean,\n      default: false\n    },\n    wrongRecordsPracticeMode: {\n      type: String,\n      default: ''\n    }\n  },\n  methods: {\n    // 获取题目类型文本\n    getQuestionTypeText: function getQuestionTypeText(type) {\n      var typeMap = {\n        1: '单选题',\n        2: '判断题',\n        3: '编程题'\n      };\n      return typeMap[type] || '未知题型';\n    },\n    // 判断答案是否正确\n    isAnswerCorrect: function isAnswerCorrect(question) {\n      this.$emit('check-answer-correct', question);\n    },\n    // 跳转到指定题目\n    jumpToQuestion: function jumpToQuestion(index) {\n      this.$emit('jump-to-question', index);\n    },\n    // 获取按钮类型\n    getButtonType: function getButtonType(question, index) {\n      if (this.isReviewMode) {\n        // 查阅模式下，所有按钮都使用default类型，通过CSS类来控制样式\n        return 'default';\n      } else {\n        // 非查阅模式下的原有逻辑\n        return this.currentQuestionIndex === index + 1 ? 'primary' : 'default';\n      }\n    },\n    // 获取按钮样式类\n    getButtonClass: function getButtonClass(question, index) {\n      var classes = {};\n      if (this.isReviewMode) {\n        // 查阅模式下的样式类\n        var isAnswered = this.answeredQuestions.includes(question.id);\n        var isCorrect = isAnswered && this.checkAnswerCorrect(question);\n        if (isAnswered) {\n          if (isCorrect) {\n            classes['review-correct'] = true;\n          } else {\n            classes['review-incorrect'] = true;\n          }\n        } else {\n          classes['review-unfinished'] = true;\n        }\n      } else {\n        // 非查阅模式下的原有逻辑\n        var isObjectiveAnswered = question.questionType !== 3 && this.userAnswersMap[question.id];\n        var isProgrammingAnswered = question.questionType === 3 && this.userAnswersMap[question.id] && _typeof(this.userAnswersMap[question.id]) === 'object' && this.userAnswersMap[question.id].submitted === true;\n        classes['answered'] = (isObjectiveAnswered || isProgrammingAnswered) && this.currentQuestionIndex !== index + 1;\n        classes['current-answered'] = this.currentQuestionIndex === index + 1 && (isObjectiveAnswered || isProgrammingAnswered);\n      }\n      return classes;\n    },\n    // 检查答案是否正确（内部方法）\n    checkAnswerCorrect: function checkAnswerCorrect(question) {\n      if (!question || !question.content) return false;\n      var userAnswer = this.userAnswersMap[question.id];\n      if (!userAnswer) return false;\n\n      // 根据题型判断答案是否正确\n      if (question.questionType === 1) {\n        // 单选题\n        return userAnswer === question.content.answer;\n      } else if (question.questionType === 2) {\n        // 判断题\n        var correctAnswer = question.content.answer;\n        // 处理不同格式的答案\n        if (correctAnswer === 'T' || correctAnswer === 'true') {\n          return userAnswer === 'T' || userAnswer === 'true' || userAnswer === true;\n        } else if (correctAnswer === 'F' || correctAnswer === 'false') {\n          return userAnswer === 'F' || userAnswer === 'false' || userAnswer === false;\n        }\n        return userAnswer === correctAnswer;\n      } else if (question.questionType === 3) {\n        // 编程题 - 检查是否有通过的提交\n        return userAnswer && _typeof(userAnswer) === 'object' && userAnswer.submitted === true && userAnswer.status === 'Accepted';\n      }\n      return false;\n    },\n    // 退出查阅模式\n    exitReviewMode: function exitReviewMode() {\n      this.$emit('exit-review-mode');\n    },\n    // 切换显示答案\n    toggleShowAnswer: function toggleShowAnswer() {\n      this.$emit('toggle-show-answer');\n    },\n    // 收藏题目\n    collectQuestion: function collectQuestion() {\n      this.$emit('collect-question');\n    },\n    // 退出刷题\n    exitPractise: function exitPractise() {\n      this.$emit('exit-practise');\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "isFullScreen", "type", "Boolean", "default", "isReviewMode", "showAnswer", "practiseMode", "String", "practiseCount", "Number", "practiceTitle", "remainingTimeText", "questionList", "Array", "_default", "currentQuestionIndex", "answeredQuestions", "userAnswersMap", "Object", "isCollected", "collectLoading", "isWrongRecordsMode", "wrongRecordsPracticeMode", "methods", "getQuestionTypeText", "typeMap", "isAnswerCorrect", "question", "$emit", "jumpToQuestion", "index", "getButtonType", "getButtonClass", "classes", "isAnswered", "includes", "id", "isCorrect", "checkAnswerCorrect", "isObjectiveAnswered", "questionType", "isProgrammingAnswered", "_typeof", "submitted", "content", "userAnswer", "answer", "<PERSON><PERSON><PERSON><PERSON>", "status", "exitReviewMode", "toggleShowAnswer", "collectQuestion", "exitPractise"], "sources": ["src/views/examSystem/components/PracticeStatusBar.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\" class=\"practice-status\" :class=\"{'status-card-fullscreen': isFullScreen}\">\n    <a-row type=\"flex\" align=\"middle\">\n      <!-- 进度显示区域 -->\n      <a-col :span=\"isFullScreen ? 24 : 16\">\n        <!-- 全屏模式下的题目模式标签显示位置 -->\n        <div v-if=\"isFullScreen && practiseMode === 'count'\" class=\"fullscreen-mode-tag\">\n          <a-tag color=\"green\">\n            <a-icon type=\"ordered-list\" /> 题目模式: {{ practiseCount }}题\n          </a-tag>\n        </div>\n        <!-- 全屏模式下的错题练习标识 -->\n        <div v-if=\"isFullScreen && isWrongRecordsMode\" class=\"fullscreen-mode-tag\">\n          <a-tag color=\"red\">\n            <a-icon type=\"book\" />\n            <span v-if=\"wrongRecordsPracticeMode === 'single'\">单题错题练习</span>\n            <span v-else-if=\"wrongRecordsPracticeMode === 'selected'\">选中错题练习</span>\n            <span v-else-if=\"wrongRecordsPracticeMode === 'wrongRecords'\">全部错题练习</span>\n          </a-tag>\n        </div>\n        <div class=\"practice-progress\" :class=\"{'full-width-progress': isFullScreen}\">\n          <div class=\"progress-header\">\n            <span class=\"progress-text\">\n              <a-icon type=\"dashboard\" /> 进度: {{ answeredQuestions.length }}/{{ questionList.length }}\n            </span>\n            <span class=\"progress-percent\">\n              {{ Math.round(answeredQuestions.length / questionList.length * 100) }}%\n            </span>\n          </div>\n          <a-progress \n            :percent=\"answeredQuestions.length / questionList.length * 100\" \n            :showInfo=\"false\" \n            status=\"active\"\n            strokeColor=\"linear-gradient(to right, #108ee9, #2db7f5)\"\n          />\n        </div>\n      </a-col>\n      \n      <!-- 非全屏模式下才显示右侧区域 -->\n      <a-col :span=\"8\" v-if=\"!isFullScreen\" style=\"text-align: right\">\n        <div class=\"practice-timer\" v-if=\"practiseMode === 'time'\">\n          <a-badge status=\"processing\" />\n          <a-statistic\n            :value=\"remainingTimeText\"\n            :valueStyle=\"{fontSize: '24px', color: '#ff4d4f', background: '#fff2f0', padding: '4px 12px', borderRadius: '8px', fontWeight: 'bold'}\"\n            suffix=\"剩余\"\n          />\n        </div>\n        <a-tag v-if=\"practiseMode === 'free'\" color=\"blue\">\n          <a-icon type=\"unlock\" /> 自由模式\n        </a-tag>\n        <!-- 非全屏模式下的题目模式标签显示位置 -->\n        <a-tag v-if=\"practiseMode === 'count'\" color=\"green\">\n          <a-icon type=\"ordered-list\" /> 题目模式: {{ practiseCount }}题\n        </a-tag>\n        <!-- 错题练习模式标识 -->\n        <div v-if=\"practiseMode === 'wrong'\" class=\"wrong-practice-info\">\n          <div class=\"practice-title-tag\">\n            <a-tag color=\"red\">\n              <a-icon type=\"book\" />\n              {{ practiceTitle || '错题练习' }}\n            </a-tag>\n          </div>\n        </div>\n        <!-- 原有的错题练习模式标识（保持兼容性） -->\n        <a-tag v-if=\"isWrongRecordsMode\" color=\"red\">\n          <a-icon type=\"book\" />\n          <span v-if=\"wrongRecordsPracticeMode === 'single'\">单题错题练习</span>\n          <span v-else-if=\"wrongRecordsPracticeMode === 'selected'\">选中错题练习</span>\n          <span v-else-if=\"wrongRecordsPracticeMode === 'wrongRecords'\">全部错题练习</span>\n        </a-tag>\n      </a-col>\n    </a-row>\n    \n    <!-- 全屏模式下的计时器显示 -->\n    <div v-if=\"isFullScreen && practiseMode === 'time'\" class=\"fullscreen-timer\">\n      <a-badge status=\"processing\" />\n      <a-statistic \n        :value=\"remainingTimeText\"\n        :valueStyle=\"{fontSize: '24px', color: '#ff4d4f', background: '#fff2f0', padding: '4px 12px', borderRadius: '8px', fontWeight: 'bold'}\"\n        suffix=\"剩余\"\n      />\n    </div>\n    \n    <!-- 全屏模式下的自由模式标签 -->\n    <div v-if=\"isFullScreen && practiseMode === 'free'\" class=\"fullscreen-mode-tag\">\n      <a-tag color=\"blue\">\n        <a-icon type=\"unlock\" /> 自由模式\n      </a-tag>\n    </div>\n    \n    <!-- 题目导航 -->\n    <div class=\"navigator-container\">\n      <div class=\"navigator-header\">\n        <span><a-icon type=\"bars\" /> 题目导航</span>\n      </div>\n      <div class=\"navigator-buttons\">\n        <a-tooltip v-for=\"(q, index) in questionList\" :key=\"index\" :title=\"getQuestionTypeText(q.questionType)\">\n          <a-button\n            :type=\"getButtonType(q, index)\"\n            shape=\"circle\"\n            size=\"small\"\n            :class=\"getButtonClass(q, index)\"\n            @click=\"jumpToQuestion(index + 1)\"\n          >\n            {{ index + 1 }}\n          </a-button>\n        </a-tooltip>\n      </div>\n    </div>\n    \n    <!-- 操作按钮区域 -->\n    <div class=\"practice-actions\">\n      <!-- 根据模式显示不同的操作按钮 -->\n      <template v-if=\"isReviewMode\">\n        <a-button type=\"danger\" @click=\"exitReviewMode\">\n          <a-icon type=\"rollback\" /> 退出查阅\n        </a-button>\n        <a-button \n          @click=\"toggleShowAnswer\"\n          :type=\"showAnswer ? 'dashed' : 'default'\"\n        >\n          <a-icon :type=\"showAnswer ? 'eye-invisible' : 'eye'\" />\n          {{ showAnswer ? '隐藏答案' : '显示答案' }}\n        </a-button>\n        <a-button \n          @click=\"collectQuestion\"\n          :type=\"isCollected ? 'primary' : 'default'\"\n          :loading=\"collectLoading\"\n          class=\"collect-button\"\n        >\n          <a-icon :type=\"isCollected ? 'star' : 'star-o'\" :theme=\"isCollected ? 'filled' : 'outlined'\" />\n          {{ isCollected ? '已收藏' : '收藏题目' }}\n        </a-button>\n      </template>\n      <template v-else>\n        <a-button type=\"danger\" @click=\"exitPractise\">\n          <a-icon type=\"rollback\" /> {{ practiseMode === 'wrong' ? '退出错题练习' : '退出刷题' }}\n        </a-button>\n        <a-button\n          @click=\"collectQuestion\"\n          :type=\"isCollected ? 'primary' : 'default'\"\n          :loading=\"collectLoading\"\n          class=\"collect-button\"\n        >\n          <a-icon :type=\"isCollected ? 'star' : 'star-o'\" :theme=\"isCollected ? 'filled' : 'outlined'\" />\n          {{ isCollected ? '已收藏' : '收藏题目' }}\n        </a-button>\n      </template>\n    </div>\n  </a-card>\n</template>\n\n<script>\nexport default {\n  name: 'PracticeStatusBar',\n  props: {\n    // 基本状态\n    isFullScreen: {\n      type: Boolean,\n      default: false\n    },\n    isReviewMode: {\n      type: Boolean,\n      default: false\n    },\n    showAnswer: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 练习配置\n    practiseMode: {\n      type: String,\n      default: ''\n    },\n    practiseCount: {\n      type: Number,\n      default: 10\n    },\n    practiceTitle: {\n      type: String,\n      default: ''\n    },\n    remainingTimeText: {\n      type: String,\n      default: '00:00'\n    },\n    \n    // 题目数据\n    questionList: {\n      type: Array,\n      default: () => []\n    },\n    currentQuestionIndex: {\n      type: Number,\n      default: 1\n    },\n    answeredQuestions: {\n      type: Array,\n      default: () => []\n    },\n    userAnswersMap: {\n      type: Object,\n      default: () => ({})\n    },\n    \n    // 收藏状态\n    isCollected: {\n      type: Boolean,\n      default: false\n    },\n    collectLoading: {\n      type: Boolean,\n      default: false\n    },\n\n    // 错题练习模式\n    isWrongRecordsMode: {\n      type: Boolean,\n      default: false\n    },\n    wrongRecordsPracticeMode: {\n      type: String,\n      default: ''\n    }\n  },\n  methods: {\n    // 获取题目类型文本\n    getQuestionTypeText(type) {\n      const typeMap = {\n        1: '单选题',\n        2: '判断题',\n        3: '编程题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    \n    // 判断答案是否正确\n    isAnswerCorrect(question) {\n      this.$emit('check-answer-correct', question)\n    },\n    \n    // 跳转到指定题目\n    jumpToQuestion(index) {\n      this.$emit('jump-to-question', index)\n    },\n\n    // 获取按钮类型\n    getButtonType(question, index) {\n      if (this.isReviewMode) {\n        // 查阅模式下，所有按钮都使用default类型，通过CSS类来控制样式\n        return 'default';\n      } else {\n        // 非查阅模式下的原有逻辑\n        return this.currentQuestionIndex === index + 1 ? 'primary' : 'default';\n      }\n    },\n\n    // 获取按钮样式类\n    getButtonClass(question, index) {\n      const classes = {};\n\n      if (this.isReviewMode) {\n        // 查阅模式下的样式类\n        const isAnswered = this.answeredQuestions.includes(question.id);\n        const isCorrect = isAnswered && this.checkAnswerCorrect(question);\n\n        if (isAnswered) {\n          if (isCorrect) {\n            classes['review-correct'] = true;\n          } else {\n            classes['review-incorrect'] = true;\n          }\n        } else {\n          classes['review-unfinished'] = true;\n        }\n      } else {\n        // 非查阅模式下的原有逻辑\n        const isObjectiveAnswered = question.questionType !== 3 && this.userAnswersMap[question.id];\n        const isProgrammingAnswered = question.questionType === 3 &&\n                                    this.userAnswersMap[question.id] &&\n                                    typeof this.userAnswersMap[question.id] === 'object' &&\n                                    this.userAnswersMap[question.id].submitted === true;\n\n        classes['answered'] = ((isObjectiveAnswered || isProgrammingAnswered) &&\n                              this.currentQuestionIndex !== index + 1);\n        classes['current-answered'] = (this.currentQuestionIndex === index + 1 &&\n                                     (isObjectiveAnswered || isProgrammingAnswered));\n      }\n\n      return classes;\n    },\n\n    // 检查答案是否正确（内部方法）\n    checkAnswerCorrect(question) {\n      if (!question || !question.content) return false;\n\n      const userAnswer = this.userAnswersMap[question.id];\n      if (!userAnswer) return false;\n\n      // 根据题型判断答案是否正确\n      if (question.questionType === 1) {\n        // 单选题\n        return userAnswer === question.content.answer;\n      } else if (question.questionType === 2) {\n        // 判断题\n        const correctAnswer = question.content.answer;\n        // 处理不同格式的答案\n        if (correctAnswer === 'T' || correctAnswer === 'true') {\n          return userAnswer === 'T' || userAnswer === 'true' || userAnswer === true;\n        } else if (correctAnswer === 'F' || correctAnswer === 'false') {\n          return userAnswer === 'F' || userAnswer === 'false' || userAnswer === false;\n        }\n        return userAnswer === correctAnswer;\n      } else if (question.questionType === 3) {\n        // 编程题 - 检查是否有通过的提交\n        return userAnswer &&\n               typeof userAnswer === 'object' &&\n               userAnswer.submitted === true &&\n               userAnswer.status === 'Accepted';\n      }\n\n      return false;\n    },\n    \n    // 退出查阅模式\n    exitReviewMode() {\n      this.$emit('exit-review-mode')\n    },\n    \n    // 切换显示答案\n    toggleShowAnswer() {\n      this.$emit('toggle-show-answer')\n    },\n    \n    // 收藏题目\n    collectQuestion() {\n      this.$emit('collect-question')\n    },\n    \n    // 退出刷题\n    exitPractise() {\n      this.$emit('exit-practise')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.practice-status {\n  margin-bottom: 12px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  background: linear-gradient(to right, #fcfcfc, #f8f8f8);\n  transition: all 0.3s ease;\n\n  .practice-progress {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 6px;\n\n    .progress-header {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n\n      .progress-text {\n        font-size: 16px;\n        font-weight: 500;\n        color: #262626;\n\n        .anticon {\n          color: #1890ff;\n          margin-right: 6px;\n        }\n      }\n\n      .progress-percent {\n        font-size: 14px;\n        font-weight: 600;\n        color: #1890ff;\n        background: rgba(24, 144, 255, 0.1);\n        padding: 2px 8px;\n        border-radius: 12px;\n      }\n    }\n\n    .ant-progress-bg {\n      height: 8px !important;\n      border-radius: 4px !important;\n    }\n\n    // 全屏模式下的进度条容器宽度\n    &.full-width-progress {\n      width: 100%;\n    }\n\n    @media screen and (max-width: 768px) {\n      .progress-header {\n        .progress-text {\n          font-size: 14px;\n        }\n\n        .progress-percent {\n          font-size: 12px;\n        }\n      }\n    }\n  }\n\n  .practice-timer {\n    font-size: 28px;\n    font-weight: bold;\n    display: flex;\n    justify-content: flex-end;\n    align-items: center;\n\n    .ant-badge-status-processing {\n      width: 10px;\n      height: 10px;\n    }\n\n    .ant-badge-status-dot {\n      width: 8px;\n      height: 8px;\n      animation: pulse 1.5s infinite;\n    }\n\n    .ant-statistic-content {\n      color: #ff4d4f;\n      background: rgba(255, 77, 79, 0.05);\n      padding: 4px 12px;\n      border-radius: 8px;\n      display: inline-block;\n      margin-left: 8px;\n    }\n\n    @media screen and (max-width: 768px) {\n      font-size: 20px;\n\n      .ant-statistic-content {\n        font-size: 18px;\n      }\n    }\n  }\n\n  .ant-tag {\n    font-size: 14px;\n    padding: 5px 12px;\n    border-radius: 16px;\n    border: none;\n    font-weight: 600;\n    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n\n    .anticon {\n      margin-right: 4px;\n    }\n  }\n\n  // 错题练习信息区域样式\n  .wrong-practice-info {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-end;\n    gap: 8px;\n\n    .practice-title-tag {\n      .ant-tag {\n        font-size: 14px;\n        font-weight: 600;\n        max-width: 200px;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n    }\n\n    .practice-count-tag {\n      .ant-tag {\n        font-size: 12px;\n        font-weight: 500;\n      }\n    }\n  }\n\n  // 全屏模式下的特殊样式\n  &.status-card-fullscreen {\n    display: none; // 隐藏左侧状态区\n    width: 25%;\n    position: fixed;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    height: 100vh;\n    overflow-y: auto;\n    z-index: 1001;\n    margin: 0;\n    border-radius: 0;\n    background-color: #f9f9fb;\n    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);\n    padding: 16px;\n\n    // 全屏模式下的题目模式标签样式\n    .fullscreen-mode-tag {\n      text-align: center;\n      margin-bottom: 16px;\n      width: 100%;\n\n      .ant-tag {\n        display: inline-block;\n        padding: 4px 12px;\n        font-size: 14px;\n        font-weight: 600;\n        border: none;\n      }\n    }\n\n    // 全屏模式下的计时器样式\n    .fullscreen-timer {\n      text-align: center;\n      margin-bottom: 16px;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .ant-statistic {\n        display: inline-flex;\n        align-items: center;\n\n        .ant-statistic-content {\n          font-size: 20px !important;\n          background: rgba(255, 77, 79, 0.08);\n          padding: 4px 10px;\n          border-radius: 6px;\n        }\n      }\n    }\n\n    // 优化状态区内部布局\n    .ant-row {\n      margin-bottom: 16px;\n      width: 100%;\n    }\n  }\n\n  // 导航容器样式\n  .navigator-container {\n    margin: 8px 0;\n\n    .navigator-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 10px;\n\n      span {\n        font-weight: 500;\n        color: #262626;\n        font-size: 14px;\n\n        .anticon {\n          color: #1890ff;\n          margin-right: 4px;\n        }\n      }\n    }\n\n    .navigator-buttons {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 6px;\n      padding: 0;\n\n      button {\n        width: 28px;\n        height: 28px;\n        font-weight: 500;\n        transition: all 0.3s;\n        font-size: 12px;\n\n        &:hover:not(.ant-btn-primary) {\n          background: #f0f7ff;\n          color: #1890ff;\n          border-color: #91d5ff;\n          transform: translateY(-2px);\n        }\n\n        &.ant-btn-primary {\n          background: linear-gradient(135deg, #1890ff, #096dd9);\n          border: none;\n          box-shadow: 0 3px 6px rgba(24, 144, 255, 0.2);\n\n          &:hover {\n            box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n          }\n        }\n\n        &.ant-btn-dashed {\n          border-style: dashed;\n          background: #f9f9f9;\n        }\n\n        // 已答题状态样式：绿色背景、白色数字\n        &.answered {\n          background-color: #52c41a;\n          color: white;\n          border-color: #52c41a;\n          position: relative;\n        }\n\n        // 选中且已答题状态：蓝色背景、绿色指示器\n        &.current-answered {\n          background-color: #1890ff;\n          color: white;\n          border-color: #1890ff;\n          position: relative;\n\n          // 添加绿色指示器，表示已答\n          &:after {\n            content: '';\n            position: absolute;\n            right: 0;\n            top: 0;\n            width: 8px;\n            height: 8px;\n            background-color: #52c41a;\n            border-radius: 50%;\n            border: 1px solid white;\n          }\n        }\n\n        // 查阅模式相关样式（按钮样式）\n        // 正确题目样式 - 绿色背景\n        &.review-correct {\n          background-color: #52c41a !important;\n          color: white !important;\n          border-color: #52c41a !important;\n\n          &:hover, &:focus, &:active {\n            background-color: #73d13d !important;\n            border-color: #73d13d !important;\n            color: white !important;\n          }\n        }\n\n        // 错误题目样式 - 红色背景\n        &.review-incorrect {\n          background-color: #ff4d4f !important;\n          color: white !important;\n          border-color: #ff4d4f !important;\n\n          &:hover, &:focus, &:active {\n            background-color: #ff7875 !important;\n            border-color: #ff7875 !important;\n            color: white !important;\n          }\n        }\n\n        // 未完成题目样式 - 灰色背景\n        &.review-unfinished {\n          background-color: #f0f0f0 !important;\n          color: #8c8c8c !important;\n          border-color: #d9d9d9 !important;\n\n          &:hover, &:focus, &:active {\n            background-color: #fafafa !important;\n            border-color: #d9d9d9 !important;\n            color: #595959 !important;\n          }\n        }\n      }\n\n      @media screen and (max-width: 768px) {\n        gap: 6px;\n\n        button {\n          width: 24px;\n          height: 24px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n\n\n\n  // 操作按钮样式\n  .practice-actions {\n    display: flex;\n    justify-content: flex-end;\n    margin-top: 8px;\n    gap: 8px;\n\n    button {\n      border-radius: 8px;\n      font-weight: 500;\n      transition: all 0.3s;\n\n      &:hover {\n        transform: translateY(-2px);\n      }\n\n      &.ant-btn-danger {\n        background: linear-gradient(135deg, #ff4d4f, #cf1322);\n        border: none;\n        box-shadow: 0 3px 8px rgba(255, 77, 79, 0.2);\n\n        &:hover {\n          box-shadow: 0 5px 12px rgba(255, 77, 79, 0.3);\n        }\n      }\n\n      &.ant-btn-primary {\n        background: linear-gradient(135deg, #1890ff, #096dd9);\n        border: none;\n        box-shadow: 0 3px 8px rgba(24, 144, 255, 0.2);\n\n        &:hover {\n          box-shadow: 0 5px 12px rgba(24, 144, 255, 0.3);\n        }\n      }\n    }\n\n    @media screen and (max-width: 768px) {\n      button {\n        padding: 0 8px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  // 收藏按钮相关样式\n  .collect-button {\n    position: relative;\n    overflow: visible;\n\n    .anticon {\n      transition: all 0.3s;\n    }\n\n    &:hover .anticon {\n      transform: scale(1.2);\n    }\n  }\n}\n\n// 动画效果\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);\n  }\n  70% {\n    box-shadow: 0 0 0 8px rgba(255, 77, 79, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);\n  }\n}\n</style>\n"], "mappings": ";AA0JA;EACAA,IAAA;EACAC,KAAA;IACA;IACAC,YAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAE,UAAA;MACAJ,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IAEA;IACAG,YAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAK,aAAA;MACAP,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACAO,aAAA;MACAT,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAQ,iBAAA;MACAV,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IAEA;IACAS,YAAA;MACAX,IAAA,EAAAY,KAAA;MACAV,OAAA,WAAAW,SAAA;QAAA;MAAA;IACA;IACAC,oBAAA;MACAd,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACAa,iBAAA;MACAf,IAAA,EAAAY,KAAA;MACAV,OAAA,WAAAW,SAAA;QAAA;MAAA;IACA;IACAG,cAAA;MACAhB,IAAA,EAAAiB,MAAA;MACAf,OAAA,WAAAW,SAAA;QAAA;MAAA;IACA;IAEA;IACAK,WAAA;MACAlB,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAiB,cAAA;MACAnB,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IAEA;IACAkB,kBAAA;MACApB,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAmB,wBAAA;MACArB,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAoB,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAvB,IAAA;MACA,IAAAwB,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAxB,IAAA;IACA;IAEA;IACAyB,eAAA,WAAAA,gBAAAC,QAAA;MACA,KAAAC,KAAA,yBAAAD,QAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAF,KAAA,qBAAAE,KAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAJ,QAAA,EAAAG,KAAA;MACA,SAAA1B,YAAA;QACA;QACA;MACA;QACA;QACA,YAAAW,oBAAA,KAAAe,KAAA;MACA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAL,QAAA,EAAAG,KAAA;MACA,IAAAG,OAAA;MAEA,SAAA7B,YAAA;QACA;QACA,IAAA8B,UAAA,QAAAlB,iBAAA,CAAAmB,QAAA,CAAAR,QAAA,CAAAS,EAAA;QACA,IAAAC,SAAA,GAAAH,UAAA,SAAAI,kBAAA,CAAAX,QAAA;QAEA,IAAAO,UAAA;UACA,IAAAG,SAAA;YACAJ,OAAA;UACA;YACAA,OAAA;UACA;QACA;UACAA,OAAA;QACA;MACA;QACA;QACA,IAAAM,mBAAA,GAAAZ,QAAA,CAAAa,YAAA,eAAAvB,cAAA,CAAAU,QAAA,CAAAS,EAAA;QACA,IAAAK,qBAAA,GAAAd,QAAA,CAAAa,YAAA,UACA,KAAAvB,cAAA,CAAAU,QAAA,CAAAS,EAAA,KACAM,OAAA,MAAAzB,cAAA,CAAAU,QAAA,CAAAS,EAAA,mBACA,KAAAnB,cAAA,CAAAU,QAAA,CAAAS,EAAA,EAAAO,SAAA;QAEAV,OAAA,gBAAAM,mBAAA,IAAAE,qBAAA,KACA,KAAA1B,oBAAA,KAAAe,KAAA;QACAG,OAAA,4BAAAlB,oBAAA,KAAAe,KAAA,SACAS,mBAAA,IAAAE,qBAAA;MACA;MAEA,OAAAR,OAAA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAAX,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAAiB,OAAA;MAEA,IAAAC,UAAA,QAAA5B,cAAA,CAAAU,QAAA,CAAAS,EAAA;MACA,KAAAS,UAAA;;MAEA;MACA,IAAAlB,QAAA,CAAAa,YAAA;QACA;QACA,OAAAK,UAAA,KAAAlB,QAAA,CAAAiB,OAAA,CAAAE,MAAA;MACA,WAAAnB,QAAA,CAAAa,YAAA;QACA;QACA,IAAAO,aAAA,GAAApB,QAAA,CAAAiB,OAAA,CAAAE,MAAA;QACA;QACA,IAAAC,aAAA,YAAAA,aAAA;UACA,OAAAF,UAAA,YAAAA,UAAA,eAAAA,UAAA;QACA,WAAAE,aAAA,YAAAA,aAAA;UACA,OAAAF,UAAA,YAAAA,UAAA,gBAAAA,UAAA;QACA;QACA,OAAAA,UAAA,KAAAE,aAAA;MACA,WAAApB,QAAA,CAAAa,YAAA;QACA;QACA,OAAAK,UAAA,IACAH,OAAA,CAAAG,UAAA,kBACAA,UAAA,CAAAF,SAAA,aACAE,UAAA,CAAAG,MAAA;MACA;MAEA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAArB,KAAA;IACA;IAEA;IACAsB,gBAAA,WAAAA,iBAAA;MACA,KAAAtB,KAAA;IACA;IAEA;IACAuB,eAAA,WAAAA,gBAAA;MACA,KAAAvB,KAAA;IACA;IAEA;IACAwB,YAAA,WAAAA,aAAA;MACA,KAAAxB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}