{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  //option {label:,value:}\n  export default {\n    name: 'JSelectMultiple',\n    props: {\n      placeholder:{\n        type: String,\n        default:'',\n        required: false\n      },\n      value:{\n        type: String,\n        required: false\n      },\n      readOnly:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type: Array,\n        required: true\n      },\n      triggerChange:{\n        type: Boolean,\n        required: false,\n        default: false\n      }\n    },\n    data(){\n      return {\n        arrayValue:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.arrayValue = []\n        }else{\n          this.arrayValue = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (selectedValue) {\n        if(this.triggerChange){\n          this.$emit('change', selectedValue.join(\",\"));\n        }else{\n          this.$emit('input', selectedValue.join(\",\"));\n        }\n      },\n    },\n\n  }\n", {"version": 3, "sources": ["JSelectMultiple.vue"], "names": [], "mappings": ";AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "JSelectMultiple.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-select :value=\"arrayValue\" @change=\"onChange\" mode=\"multiple\" :placeholder=\"placeholder\">\n    <a-select-option\n      v-for=\"(item,index) in options\"\n      :key=\"index\"\n      :value=\"item.value\">\n      {{ item.text || item.label }}\n    </a-select-option>\n  </a-select>\n</template>\n\n<script>\n  //option {label:,value:}\n  export default {\n    name: 'JSelectMultiple',\n    props: {\n      placeholder:{\n        type: String,\n        default:'',\n        required: false\n      },\n      value:{\n        type: String,\n        required: false\n      },\n      readOnly:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type: Array,\n        required: true\n      },\n      triggerChange:{\n        type: Boolean,\n        required: false,\n        default: false\n      }\n    },\n    data(){\n      return {\n        arrayValue:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.arrayValue = []\n        }else{\n          this.arrayValue = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (selectedValue) {\n        if(this.triggerChange){\n          this.$emit('change', selectedValue.join(\",\"));\n        }else{\n          this.$emit('input', selectedValue.join(\",\"));\n        }\n      },\n    },\n\n  }\n</script>\n"]}]}