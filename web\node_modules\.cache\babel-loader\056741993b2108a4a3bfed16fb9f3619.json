{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getAction } from '@/api/manage';\nexport default {\n  props: ['sex', 'name'],\n  data: function data() {\n    return {\n      formLayout: 'horizontal',\n      form: this.$form.createForm(this),\n      areaOptions: []\n    };\n  },\n  methods: {\n    handleSubmit: function handleSubmit(e) {\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          console.log('Received values of form: ', values);\n        }\n      });\n    },\n    handleSelectChange: function handleSelectChange(value) {\n      console.log(value);\n      this.form.setFieldsValue({\n        note: \"Hi, \".concat(value === 'male' ? 'man' : 'lady', \"!\")\n      });\n    },\n    onChange: function onChange(value, selectedOptions) {\n      console.log(value, selectedOptions);\n    },\n    filter: function filter(inputValue, path) {\n      return path.some(function (option) {\n        return option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1;\n      });\n    }\n  },\n  created: function created() {\n    var _this = this;\n    console.log('============= online href common props ============= ');\n    console.log('props sex: ', this.sex);\n    console.log('props name: ', this.name);\n    getAction('/api/area').then(function (res) {\n      console.log(\"------------\");\n      console.log(res);\n      _this.areaOptions = res;\n    });\n  },\n  watch: {\n    $route: {\n      immediate: true,\n      handler: function handler() {\n        console.log('============= online href  $route props ============= ');\n        var sex = this.$route.query.sex;\n        console.log('$route sex: ', sex);\n      }\n    }\n  }\n};", {"version": 3, "names": ["getAction", "props", "data", "formLayout", "form", "$form", "createForm", "areaOptions", "methods", "handleSubmit", "e", "preventDefault", "validateFields", "err", "values", "console", "log", "handleSelectChange", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "note", "concat", "onChange", "selectedOptions", "filter", "inputValue", "path", "some", "option", "label", "toLowerCase", "indexOf", "created", "_this", "sex", "name", "then", "res", "watch", "$route", "immediate", "handler", "query"], "sources": ["src/views/jeecg/helloworld.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-form @submit=\"handleSubmit\" :form=\"form\">\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Note\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-input v-decorator=\"['note',{rules: [{ required: true, message: 'Please input your note!' }]}]\"/>\n      </a-form-item>\n      </a-col>\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Gender\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-select v-decorator=\"['gender',{rules: [{ required: true, message: 'Please select your gender!' }]}]\" placeholder=\"Select a option and change input text above\" @change=\"this.handleSelectChange\">\n          <a-select-option value=\"male\">male</a-select-option>\n          <a-select-option value=\"female\">female</a-select-option>\n        </a-select>\n      </a-form-item>\n      </a-col>\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Gender\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-cascader :options=\"areaOptions\" @change=\"onChange\" :showSearch=\"{filter}\" placeholder=\"Please select\" />\n      </a-form-item>\n      </a-col>\n      <a-form-item :wrapperCol=\"{ span: 12, offset: 5 }\">\n        <a-col :md=\"24\" :sm=\"24\">\n          <a-form-item :wrapperCol=\"{ span: 12, offset: 5 }\">\n            <a-button type=\"primary\" htmlType=\"submit\">Submit</a-button>\n          </a-form-item>\n        </a-col>\n      </a-form-item>\n    </a-form>\n  </a-card>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n  export default {\n    props: ['sex','name'],\n    data () {\n      return {\n        formLayout: 'horizontal',\n        form: this.$form.createForm(this),\n        areaOptions:[]\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            console.log('Received values of form: ', values)\n          }\n        })\n      },\n      handleSelectChange (value) {\n        console.log(value)\n        this.form.setFieldsValue({\n          note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,\n        })\n      },\n      onChange(value, selectedOptions) {\n        console.log(value, selectedOptions);\n      },\n      filter(inputValue, path) {\n        return (path.some(option => (option.label).toLowerCase().indexOf(inputValue.toLowerCase()) > -1));\n      },\n    },\n    created (){\n      console.log('============= online href common props ============= ');\n      console.log('props sex: ',this.sex);\n      console.log('props name: ',this.name);\n\n      getAction('/api/area').then((res) => {\n          console.log(\"------------\")\n          console.log(res)\n          this.areaOptions = res;\n      })\n    },\n    watch: {\n      $route: {\n        immediate: true,\n        handler() {\n          console.log('============= online href  $route props ============= ');\n          let sex = this.$route.query.sex\n          console.log('$route sex: ', sex);\n        }\n      }\n    },\n  }\n</script>"], "mappings": "AAiCA,SAAAA,SAAA;AACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MACAA,CAAA,CAAAC,cAAA;MACA,KAAAP,IAAA,CAAAQ,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAE,OAAA,CAAAC,GAAA,8BAAAF,MAAA;QACA;MACA;IACA;IACAG,kBAAA,WAAAA,mBAAAC,KAAA;MACAH,OAAA,CAAAC,GAAA,CAAAE,KAAA;MACA,KAAAd,IAAA,CAAAe,cAAA;QACAC,IAAA,SAAAC,MAAA,CAAAH,KAAA;MACA;IACA;IACAI,QAAA,WAAAA,SAAAJ,KAAA,EAAAK,eAAA;MACAR,OAAA,CAAAC,GAAA,CAAAE,KAAA,EAAAK,eAAA;IACA;IACAC,MAAA,WAAAA,OAAAC,UAAA,EAAAC,IAAA;MACA,OAAAA,IAAA,CAAAC,IAAA,WAAAC,MAAA;QAAA,OAAAA,MAAA,CAAAC,KAAA,CAAAC,WAAA,GAAAC,OAAA,CAAAN,UAAA,CAAAK,WAAA;MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAlB,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,qBAAAkB,GAAA;IACAnB,OAAA,CAAAC,GAAA,sBAAAmB,IAAA;IAEAnC,SAAA,cAAAoC,IAAA,WAAAC,GAAA;MACAtB,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,CAAAqB,GAAA;MACAJ,KAAA,CAAA1B,WAAA,GAAA8B,GAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAA;QACA1B,OAAA,CAAAC,GAAA;QACA,IAAAkB,GAAA,QAAAK,MAAA,CAAAG,KAAA,CAAAR,GAAA;QACAnB,OAAA,CAAAC,GAAA,iBAAAkB,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}