{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue?vue&type=template&id=ae97dc28", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-checkbox-group\", _vm._b({\n    attrs: {\n      options: _vm.options,\n      value: _vm.checkboxArray\n    },\n    on: {\n      change: _vm.onChange\n    }\n  }, \"a-checkbox-group\", _vm.$attrs, false));\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "_b", "attrs", "options", "value", "checkboxArray", "on", "change", "onChange", "$attrs", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/jeecg/JCheckbox.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-checkbox-group\",\n    _vm._b(\n      {\n        attrs: { options: _vm.options, value: _vm.checkboxArray },\n        on: { change: _vm.onChange },\n      },\n      \"a-checkbox-group\",\n      _vm.$attrs,\n      false\n    )\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,kBAAkB,EAClBD,GAAG,CAACG,EAAE,CACJ;IACEC,KAAK,EAAE;MAAEC,OAAO,EAAEL,GAAG,CAACK,OAAO;MAAEC,KAAK,EAAEN,GAAG,CAACO;IAAc,CAAC;IACzDC,EAAE,EAAE;MAAEC,MAAM,EAAET,GAAG,CAACU;IAAS;EAC7B,CAAC,EACD,kBAAkB,EAClBV,GAAG,CAACW,MAAM,EACV,KACF,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBb,MAAM,CAACc,aAAa,GAAG,IAAI;AAE3B,SAASd,MAAM,EAAEa,eAAe", "ignoreList": []}]}