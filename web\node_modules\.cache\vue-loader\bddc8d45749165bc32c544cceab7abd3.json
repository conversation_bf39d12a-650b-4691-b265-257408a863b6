{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Security.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Security.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n  export default {\n    data () {\n      return {\n        data: [\n          { title: '账户密码' , description: '当前密码强度', value: '强', actions: { title: '修改', callback: () => { this.$message.info('This is a normal message'); } } },\n          { title: '密保手机' , description: '已绑定手机', value: '138****8293', actions: { title: '修改', callback: () => { this.$message.success('This is a message of success'); } }  },\n          { title: '密保问题' , description: '未设置密保问题，密保问题可有效保护账户安全', value: '', actions: { title: '设置', callback: () => { this.$message.error('This is a message of error'); } }  },\n          { title: '备用邮箱' , description: '已绑定邮箱', value: 'ant***sign.com', actions: { title: '修改', callback: () => { this.$message.warning('This is message of warning'); } }  },\n          { title: 'MFA 设备' , description: '未绑定 MFA 设备，绑定后，可以进行二次确认', value: '', actions: { title: '绑定', callback: () => { this.$message.info('This is a normal message'); } }  },\n        ]\n      }\n    }\n  }\n", {"version": 3, "sources": ["Security.vue"], "names": [], "mappings": ";AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Security.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <a-list\n    itemLayout=\"horizontal\"\n    :dataSource=\"data\"\n  >\n    <a-list-item slot=\"renderItem\" slot-scope=\"item, index\" :key=\"index\">\n      <a-list-item-meta>\n        <a slot=\"title\">{{ item.title }}</a>\n        <span slot=\"description\">\n          <span class=\"security-list-description\">{{ item.description }}</span>\n          <span v-if=\"item.value\"> : </span>\n          <span class=\"security-list-value\">{{ item.value }}</span>\n        </span>\n      </a-list-item-meta>\n      <template v-if=\"item.actions\">\n        <a slot=\"actions\" @click=\"item.actions.callback\">{{ item.actions.title }}</a>\n      </template>\n\n    </a-list-item>\n  </a-list>\n</template>\n\n<script>\n  export default {\n    data () {\n      return {\n        data: [\n          { title: '账户密码' , description: '当前密码强度', value: '强', actions: { title: '修改', callback: () => { this.$message.info('This is a normal message'); } } },\n          { title: '密保手机' , description: '已绑定手机', value: '138****8293', actions: { title: '修改', callback: () => { this.$message.success('This is a message of success'); } }  },\n          { title: '密保问题' , description: '未设置密保问题，密保问题可有效保护账户安全', value: '', actions: { title: '设置', callback: () => { this.$message.error('This is a message of error'); } }  },\n          { title: '备用邮箱' , description: '已绑定邮箱', value: 'ant***sign.com', actions: { title: '修改', callback: () => { this.$message.warning('This is message of warning'); } }  },\n          { title: 'MFA 设备' , description: '未绑定 MFA 设备，绑定后，可以进行二次确认', value: '', actions: { title: '绑定', callback: () => { this.$message.info('This is a normal message'); } }  },\n        ]\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}