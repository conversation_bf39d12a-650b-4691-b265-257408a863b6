{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameCard.vue?vue&type=style&index=0&id=38acdad0&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameCard.vue", "mtime": 1749742410119}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.game-card {\n  background-color: #ffffff;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  position: relative;\n  cursor: pointer;\n  border: 4px solid #e0e6ff;\n  animation: float 6s ease-in-out infinite;\n}\n\n.game-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);\n  border-color: #ffd86f;\n}\n\n.game-card:nth-child(odd) {\n  animation-delay: 0.5s;\n}\n\n.game-card:nth-child(even) {\n  animation-delay: 1s;\n}\n\n.game-cost {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background: linear-gradient(135deg, #ff9a9e, #fad0c4);\n  color: white;\n  padding: 5px 12px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 14px;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n  border: 2px solid #ffffff;\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 18px;\n  height: 18px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 5px;\n}\n\n.cost-text {\n  font-size: 14px;\n}\n\n.game-image {\n  height: 180px;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n  transition: all 0.5s ease;\n}\n\n.game-image::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 40%;\n  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\n}\n\n.game-card:hover .game-image {\n  transform: scale(1.1);\n}\n\n.game-content {\n  padding: 20px;\n  position: relative;\n  background-color: #ffffff;\n}\n\n.game-title {\n  font-size: 1.4rem;\n  margin: 0 0 10px;\n  color: #4e54c8;\n  font-weight: bold;\n  position: relative;\n  display: inline-block;\n}\n\n.game-title::after {\n  content: '';\n  position: absolute;\n  bottom: -3px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background: linear-gradient(90deg, #ff9a9e, #fad0c4);\n  border-radius: 10px;\n  transform: scaleX(0);\n  transform-origin: left;\n  transition: transform 0.3s ease;\n}\n\n.game-card:hover .game-title::after {\n  transform: scaleX(1);\n}\n\n.game-description {\n  font-size: 0.95rem;\n  color: #666;\n  margin-bottom: 20px;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.play-button {\n  background: linear-gradient(135deg, #4e54c8, #8f94fb);\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 30px;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 10px rgba(78, 84, 200, 0.3);\n  width: 100%;\n}\n\n.play-icon {\n  margin-right: 8px;\n  font-size: 18px;\n}\n\n.play-button:hover {\n  transform: scale(1.05);\n  box-shadow: 0 6px 15px rgba(78, 84, 200, 0.4);\n}\n\n/* 添加动画效果 */\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n  100% { transform: translateY(0px); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n", {"version": 3, "sources": ["GameCard.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "GameCard.vue", "sourceRoot": "src/views/game/components", "sourcesContent": ["<template>\n  <div class=\"game-card\" @click=\"playGame\">\n    <div class=\"game-cost\">\n      <span class=\"coin-icon\"></span>\n      <span class=\"cost-text\">{{ game.costDescription }}</span>\n    </div>\n    <div class=\"game-image\" :style=\"{ backgroundImage: `url(${game.imageUrl})` }\"></div>\n    <div class=\"game-content\">\n      <h3 class=\"game-title\">{{ game.title }}</h3>\n      <p class=\"game-description\">{{ game.description }}</p>\n      <button class=\"play-button\">\n        <span class=\"play-icon\">▶</span>\n        开始游戏\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'GameCard',\n  props: {\n    game: {\n      type: Object,\n      required: true\n    }\n  },\n  methods: {\n    playGame() {\n      this.$emit('play-game', this.game);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.game-card {\n  background-color: #ffffff;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  position: relative;\n  cursor: pointer;\n  border: 4px solid #e0e6ff;\n  animation: float 6s ease-in-out infinite;\n}\n\n.game-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);\n  border-color: #ffd86f;\n}\n\n.game-card:nth-child(odd) {\n  animation-delay: 0.5s;\n}\n\n.game-card:nth-child(even) {\n  animation-delay: 1s;\n}\n\n.game-cost {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background: linear-gradient(135deg, #ff9a9e, #fad0c4);\n  color: white;\n  padding: 5px 12px;\n  border-radius: 20px;\n  font-weight: bold;\n  font-size: 14px;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n  border: 2px solid #ffffff;\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 18px;\n  height: 18px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 5px;\n}\n\n.cost-text {\n  font-size: 14px;\n}\n\n.game-image {\n  height: 180px;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n  transition: all 0.5s ease;\n}\n\n.game-image::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 40%;\n  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\n}\n\n.game-card:hover .game-image {\n  transform: scale(1.1);\n}\n\n.game-content {\n  padding: 20px;\n  position: relative;\n  background-color: #ffffff;\n}\n\n.game-title {\n  font-size: 1.4rem;\n  margin: 0 0 10px;\n  color: #4e54c8;\n  font-weight: bold;\n  position: relative;\n  display: inline-block;\n}\n\n.game-title::after {\n  content: '';\n  position: absolute;\n  bottom: -3px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background: linear-gradient(90deg, #ff9a9e, #fad0c4);\n  border-radius: 10px;\n  transform: scaleX(0);\n  transform-origin: left;\n  transition: transform 0.3s ease;\n}\n\n.game-card:hover .game-title::after {\n  transform: scaleX(1);\n}\n\n.game-description {\n  font-size: 0.95rem;\n  color: #666;\n  margin-bottom: 20px;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.play-button {\n  background: linear-gradient(135deg, #4e54c8, #8f94fb);\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 30px;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 10px rgba(78, 84, 200, 0.3);\n  width: 100%;\n}\n\n.play-icon {\n  margin-right: 8px;\n  font-size: 18px;\n}\n\n.play-button:hover {\n  transform: scale(1.05);\n  box-shadow: 0 6px 15px rgba(78, 84, 200, 0.4);\n}\n\n/* 添加动画效果 */\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n  100% { transform: translateY(0px); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n</style> "]}]}