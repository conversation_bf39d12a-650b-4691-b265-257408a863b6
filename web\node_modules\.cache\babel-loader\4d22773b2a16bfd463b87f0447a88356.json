{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTestModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTestModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { httpAction } from '@/api/manage';\nexport default {\n  name: \"SysMessageTestModal\",\n  data: function data() {\n    return {\n      title: \"操作\",\n      visible: false,\n      model: {},\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      confirmLoading: false,\n      url: {\n        send: \"/message/sysMessageTemplate/sendMsg\"\n      },\n      templateName: \"\",\n      templateContent: \"\",\n      receiver: \"\",\n      msgType: \"\",\n      testData: \"\",\n      sendParams: {}\n    };\n  },\n  methods: {\n    open: function open(record) {\n      this.sendParams.templateCode = record.templateCode;\n      this.templateName = record.templateName;\n      this.templateContent = record.templateContent;\n      this.testData = record.templateTestJson;\n      this.visible = true;\n    },\n    close: function close() {\n      this.receiver = \"\";\n      this.msgType = \"\";\n      this.sendParams = {};\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      var _this = this;\n      var httpurl = this.url.send;\n      var method = 'post';\n      this.sendParams.testData = this.testData;\n      this.sendParams.receiver = this.receiver;\n      this.sendParams.msgType = this.msgType;\n      httpAction(httpurl, this.sendParams, method).then(function (res) {\n        if (res.success) {\n          _this.$message.success(res.message);\n        } else {\n          _this.$message.warning(res.message);\n        }\n      }).finally(function () {\n        _this.confirmLoading = false;\n        _this.close();\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "name", "data", "title", "visible", "model", "labelCol", "xs", "span", "sm", "wrapperCol", "confirmLoading", "url", "send", "templateName", "templateContent", "receiver", "msgType", "testData", "sendParams", "methods", "open", "record", "templateCode", "templateTestJson", "close", "handleOk", "_this", "httpurl", "method", "then", "res", "success", "$message", "message", "warning", "finally", "handleCancel"], "sources": ["src/views/modules/message/modules/SysMessageTestModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"模板标题\">\n          <a-input disabled v-model=\"templateName\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"模板内容\">\n          <a-textarea disabled v-model=\"templateContent\" :autosize=\"{ minRows: 5, maxRows: 8 }\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"测试数据\">\n          <a-textarea placeholder=\"请输入json格式测试数据\" v-model=\"testData\" :autosize=\"{ minRows: 5, maxRows: 8 }\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"消息类型\">\n          <j-dict-select-tag\n            v-model=\"msgType\"\n            placeholder=\"请选择消息类型\"\n            dictCode=\"msgType\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"消息接收方\">\n          <a-input placeholder=\"请输入消息接收方\" v-model=\"receiver\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n\n  export default {\n    name: \"SysMessageTestModal\",\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n\n        confirmLoading: false,\n        url: {\n          send: \"/message/sysMessageTemplate/sendMsg\",\n        },\n        templateName: \"\",\n        templateContent: \"\",\n        receiver: \"\",\n        msgType: \"\",\n        testData: \"\",\n        sendParams: {}\n      }\n    },\n    methods: {\n      open(record) {\n        this.sendParams.templateCode = record.templateCode;\n        this.templateName = record.templateName;\n        this.templateContent = record.templateContent;\n        this.testData = record.templateTestJson;\n        this.visible = true;\n      },\n      close() {\n        this.receiver = \"\";\n        this.msgType = \"\";\n        this.sendParams = {};\n        this.visible = false;\n      },\n      handleOk() {\n        let httpurl = this.url.send;\n        let method = 'post';\n        this.sendParams.testData = this.testData;\n        this.sendParams.receiver = this.receiver;\n        this.sendParams.msgType = this.msgType;\n        httpAction(httpurl, this.sendParams, method).then((res) => {\n          if (res.success) {\n            this.$message.success(res.message);\n          } else {\n            this.$message.warning(res.message);\n          }\n        }).finally(() => {\n          this.confirmLoading = false;\n          this.close();\n        })\n      },\n      handleCancel() {\n        this.close()\n      },\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAmDA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MAEAG,cAAA;MACAC,GAAA;QACAC,IAAA;MACA;MACAC,YAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,MAAA;MACA,KAAAH,UAAA,CAAAI,YAAA,GAAAD,MAAA,CAAAC,YAAA;MACA,KAAAT,YAAA,GAAAQ,MAAA,CAAAR,YAAA;MACA,KAAAC,eAAA,GAAAO,MAAA,CAAAP,eAAA;MACA,KAAAG,QAAA,GAAAI,MAAA,CAAAE,gBAAA;MACA,KAAApB,OAAA;IACA;IACAqB,KAAA,WAAAA,MAAA;MACA,KAAAT,QAAA;MACA,KAAAC,OAAA;MACA,KAAAE,UAAA;MACA,KAAAf,OAAA;IACA;IACAsB,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,OAAA,QAAAhB,GAAA,CAAAC,IAAA;MACA,IAAAgB,MAAA;MACA,KAAAV,UAAA,CAAAD,QAAA,QAAAA,QAAA;MACA,KAAAC,UAAA,CAAAH,QAAA,QAAAA,QAAA;MACA,KAAAG,UAAA,CAAAF,OAAA,QAAAA,OAAA;MACAjB,UAAA,CAAA4B,OAAA,OAAAT,UAAA,EAAAU,MAAA,EAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAL,KAAA,CAAAM,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAG,OAAA;QACA;UACAP,KAAA,CAAAM,QAAA,CAAAE,OAAA,CAAAJ,GAAA,CAAAG,OAAA;QACA;MACA,GAAAE,OAAA;QACAT,KAAA,CAAAhB,cAAA;QACAgB,KAAA,CAAAF,KAAA;MACA;IACA;IACAY,YAAA,WAAAA,aAAA;MACA,KAAAZ,KAAA;IACA;EACA;AACA", "ignoreList": []}]}