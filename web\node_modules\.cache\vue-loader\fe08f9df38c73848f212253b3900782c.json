{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue?vue&type=template&id=17d86c04&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue", "mtime": 1753194711604}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\" class=\"practice-status\" :class=\"{'status-card-fullscreen': isFullScreen}\">\n  <a-row type=\"flex\" align=\"middle\">\n    <!-- 进度显示区域 -->\n    <a-col :span=\"isFullScreen ? 24 : 16\">\n      <!-- 全屏模式下的题目模式标签显示位置 -->\n      <div v-if=\"isFullScreen && practiseMode === 'count'\" class=\"fullscreen-mode-tag\">\n        <a-tag color=\"green\">\n          <a-icon type=\"ordered-list\" /> 题目模式: {{ practiseCount }}题\n        </a-tag>\n      </div>\n      <!-- 全屏模式下的错题练习标识 -->\n      <div v-if=\"isFullScreen && isWrongRecordsMode\" class=\"fullscreen-mode-tag\">\n        <a-tag color=\"red\">\n          <a-icon type=\"book\" />\n          <span v-if=\"wrongRecordsPracticeMode === 'single'\">单题错题练习</span>\n          <span v-else-if=\"wrongRecordsPracticeMode === 'selected'\">选中错题练习</span>\n          <span v-else-if=\"wrongRecordsPracticeMode === 'wrongRecords'\">全部错题练习</span>\n        </a-tag>\n      </div>\n      <div class=\"practice-progress\" :class=\"{'full-width-progress': isFullScreen}\">\n        <div class=\"progress-header\">\n          <span class=\"progress-text\">\n            <a-icon type=\"dashboard\" /> 进度: {{ answeredQuestions.length }}/{{ questionList.length }}\n          </span>\n          <span class=\"progress-percent\">\n            {{ Math.round(answeredQuestions.length / questionList.length * 100) }}%\n          </span>\n        </div>\n        <a-progress \n          :percent=\"answeredQuestions.length / questionList.length * 100\" \n          :showInfo=\"false\" \n          status=\"active\"\n          strokeColor=\"linear-gradient(to right, #108ee9, #2db7f5)\"\n        />\n      </div>\n    </a-col>\n    \n    <!-- 非全屏模式下才显示右侧区域 -->\n    <a-col :span=\"8\" v-if=\"!isFullScreen\" style=\"text-align: right\">\n      <div class=\"practice-timer\" v-if=\"practiseMode === 'time'\">\n        <a-badge status=\"processing\" />\n        <a-statistic\n          :value=\"remainingTimeText\"\n          :valueStyle=\"{fontSize: '24px', color: '#ff4d4f', background: '#fff2f0', padding: '4px 12px', borderRadius: '8px', fontWeight: 'bold'}\"\n          suffix=\"剩余\"\n        />\n      </div>\n      <a-tag v-if=\"practiseMode === 'free'\" color=\"blue\">\n        <a-icon type=\"unlock\" /> 自由模式\n      </a-tag>\n      <!-- 非全屏模式下的题目模式标签显示位置 -->\n      <a-tag v-if=\"practiseMode === 'count'\" color=\"green\">\n        <a-icon type=\"ordered-list\" /> 题目模式: {{ practiseCount }}题\n      </a-tag>\n      <!-- 错题练习模式标识 -->\n      <div v-if=\"practiseMode === 'wrong'\" class=\"wrong-practice-info\">\n        <div class=\"practice-title-tag\">\n          <a-tag color=\"red\">\n            <a-icon type=\"book\" />\n            {{ practiceTitle || '错题练习' }}\n          </a-tag>\n        </div>\n      </div>\n      <!-- 原有的错题练习模式标识（保持兼容性） -->\n      <a-tag v-if=\"isWrongRecordsMode\" color=\"red\">\n        <a-icon type=\"book\" />\n        <span v-if=\"wrongRecordsPracticeMode === 'single'\">单题错题练习</span>\n        <span v-else-if=\"wrongRecordsPracticeMode === 'selected'\">选中错题练习</span>\n        <span v-else-if=\"wrongRecordsPracticeMode === 'wrongRecords'\">全部错题练习</span>\n      </a-tag>\n    </a-col>\n  </a-row>\n  \n  <!-- 全屏模式下的计时器显示 -->\n  <div v-if=\"isFullScreen && practiseMode === 'time'\" class=\"fullscreen-timer\">\n    <a-badge status=\"processing\" />\n    <a-statistic \n      :value=\"remainingTimeText\"\n      :valueStyle=\"{fontSize: '24px', color: '#ff4d4f', background: '#fff2f0', padding: '4px 12px', borderRadius: '8px', fontWeight: 'bold'}\"\n      suffix=\"剩余\"\n    />\n  </div>\n  \n  <!-- 全屏模式下的自由模式标签 -->\n  <div v-if=\"isFullScreen && practiseMode === 'free'\" class=\"fullscreen-mode-tag\">\n    <a-tag color=\"blue\">\n      <a-icon type=\"unlock\" /> 自由模式\n    </a-tag>\n  </div>\n  \n  <!-- 题目导航 -->\n  <div class=\"navigator-container\">\n    <div class=\"navigator-header\">\n      <span><a-icon type=\"bars\" /> 题目导航</span>\n    </div>\n    <div class=\"navigator-buttons\">\n      <a-tooltip v-for=\"(q, index) in questionList\" :key=\"index\" :title=\"getQuestionTypeText(q.questionType)\">\n        <a-button\n          :type=\"getButtonType(q, index)\"\n          shape=\"circle\"\n          size=\"small\"\n          :class=\"getButtonClass(q, index)\"\n          @click=\"jumpToQuestion(index + 1)\"\n        >\n          {{ index + 1 }}\n        </a-button>\n      </a-tooltip>\n    </div>\n  </div>\n  \n  <!-- 操作按钮区域 -->\n  <div class=\"practice-actions\">\n    <!-- 根据模式显示不同的操作按钮 -->\n    <template v-if=\"isReviewMode\">\n      <a-button type=\"danger\" @click=\"exitReviewMode\">\n        <a-icon type=\"rollback\" /> 退出查阅\n      </a-button>\n      <a-button \n        @click=\"toggleShowAnswer\"\n        :type=\"showAnswer ? 'dashed' : 'default'\"\n      >\n        <a-icon :type=\"showAnswer ? 'eye-invisible' : 'eye'\" />\n        {{ showAnswer ? '隐藏答案' : '显示答案' }}\n      </a-button>\n      <a-button \n        @click=\"collectQuestion\"\n        :type=\"isCollected ? 'primary' : 'default'\"\n        :loading=\"collectLoading\"\n        class=\"collect-button\"\n      >\n        <a-icon :type=\"isCollected ? 'star' : 'star-o'\" :theme=\"isCollected ? 'filled' : 'outlined'\" />\n        {{ isCollected ? '已收藏' : '收藏题目' }}\n      </a-button>\n    </template>\n    <template v-else>\n      <a-button type=\"danger\" @click=\"exitPractise\">\n        <a-icon type=\"rollback\" /> {{ practiseMode === 'wrong' ? '退出错题练习' : '退出刷题' }}\n      </a-button>\n      <a-button\n        @click=\"collectQuestion\"\n        :type=\"isCollected ? 'primary' : 'default'\"\n        :loading=\"collectLoading\"\n        class=\"collect-button\"\n      >\n        <a-icon :type=\"isCollected ? 'star' : 'star-o'\" :theme=\"isCollected ? 'filled' : 'outlined'\" />\n        {{ isCollected ? '已收藏' : '收藏题目' }}\n      </a-button>\n    </template>\n  </div>\n</a-card>\n", null]}