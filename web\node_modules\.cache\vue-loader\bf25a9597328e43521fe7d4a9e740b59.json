{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\TableTotal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\TableTotal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  export default {\n    name: 'TableTotal',\n    data() {\n      return {\n        columns: [\n          {\n            title: '#',\n            width: '180px',\n            align: 'center',\n            dataIndex: 'rowIndex',\n            customRender: function (text, r, index) {\n              return (text !== '合计') ? (parseInt(index) + 1) : text\n            }\n          },\n          {\n            title: '姓名',\n            dataIndex: 'name',\n          },\n          {\n            title: '贡献点',\n            dataIndex: 'point',\n          },\n          {\n            title: '等级',\n            dataIndex: 'level',\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updateTime',\n          },\n        ],\n        dataSource: [\n          { name: '张三', point: 23, level: 3, updateTime: '2019-8-14' },\n          { name: '小王', point: 6, level: 1, updateTime: '2019-8-13' },\n          { name: '李四', point: 53, level: 8, updateTime: '2019-8-12' },\n          { name: '小红', point: 44, level: 5, updateTime: '2019-8-11' },\n          { name: '王五', point: 97, level: 10, updateTime: '2019-8-10' },\n          { name: '小明', point: 33, level: 2, updateTime: '2019-8-10' },\n        ]\n      }\n    },\n    mounted() {\n      this.tableAddTotalRow(this.columns, this.dataSource)\n    },\n    methods: {\n\n      /** 表格增加合计行 */\n      tableAddTotalRow(columns, dataSource) {\n        let numKey = 'rowIndex'\n        let totalRow = { [numKey]: '合计' }\n        columns.forEach(column => {\n          let { key, dataIndex } = column\n          if (![key, dataIndex].includes(numKey)) {\n\n            let total = 0\n            dataSource.forEach(data => {\n              total += /^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN\n              console.log(data[dataIndex], ':', (/^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN))\n            })\n\n            if (Number.isNaN(total)) {\n              total = '-'\n            }\n            totalRow[dataIndex] = total\n          }\n        })\n\n        dataSource.push(totalRow)\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["TableTotal.vue"], "names": [], "mappings": ";AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA", "file": "TableTotal.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-table\n      rowKey=\"id\"\n      bordered\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"false\"\n    >\n    </a-table>\n  </a-card>\n</template>\n\n<script>\n  export default {\n    name: 'TableTotal',\n    data() {\n      return {\n        columns: [\n          {\n            title: '#',\n            width: '180px',\n            align: 'center',\n            dataIndex: 'rowIndex',\n            customRender: function (text, r, index) {\n              return (text !== '合计') ? (parseInt(index) + 1) : text\n            }\n          },\n          {\n            title: '姓名',\n            dataIndex: 'name',\n          },\n          {\n            title: '贡献点',\n            dataIndex: 'point',\n          },\n          {\n            title: '等级',\n            dataIndex: 'level',\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updateTime',\n          },\n        ],\n        dataSource: [\n          { name: '张三', point: 23, level: 3, updateTime: '2019-8-14' },\n          { name: '小王', point: 6, level: 1, updateTime: '2019-8-13' },\n          { name: '李四', point: 53, level: 8, updateTime: '2019-8-12' },\n          { name: '小红', point: 44, level: 5, updateTime: '2019-8-11' },\n          { name: '王五', point: 97, level: 10, updateTime: '2019-8-10' },\n          { name: '小明', point: 33, level: 2, updateTime: '2019-8-10' },\n        ]\n      }\n    },\n    mounted() {\n      this.tableAddTotalRow(this.columns, this.dataSource)\n    },\n    methods: {\n\n      /** 表格增加合计行 */\n      tableAddTotalRow(columns, dataSource) {\n        let numKey = 'rowIndex'\n        let totalRow = { [numKey]: '合计' }\n        columns.forEach(column => {\n          let { key, dataIndex } = column\n          if (![key, dataIndex].includes(numKey)) {\n\n            let total = 0\n            dataSource.forEach(data => {\n              total += /^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN\n              console.log(data[dataIndex], ':', (/^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN))\n            })\n\n            if (Number.isNaN(total)) {\n              total = '-'\n            }\n            totalRow[dataIndex] = total\n          }\n        })\n\n        dataSource.push(totalRow)\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}