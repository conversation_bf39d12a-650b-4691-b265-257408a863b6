{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Radar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Radar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var axis1Opts = {\n  dataKey: 'item',\n  line: null,\n  tickLine: null,\n  grid: {\n    lineStyle: {\n      lineDash: null\n    },\n    hideFirstLine: false\n  }\n};\nvar axis2Opts = {\n  dataKey: 'score',\n  line: null,\n  tickLine: null,\n  grid: {\n    type: 'polygon',\n    lineStyle: {\n      lineDash: null\n    }\n  }\n};\nvar scale = [{\n  dataKey: 'score',\n  min: 0,\n  max: 100\n}, {\n  dataKey: 'user',\n  alias: '类型'\n}];\nvar sourceData = [{\n  item: '示例一',\n  score: 40\n}, {\n  item: '示例二',\n  score: 20\n}, {\n  item: '示例三',\n  score: 67\n}, {\n  item: '示例四',\n  score: 43\n}, {\n  item: '示例五',\n  score: 90\n}];\nexport default {\n  name: 'Radar',\n  props: {\n    height: {\n      type: Number,\n      default: 254\n    },\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      axis1Opts: axis1Opts,\n      axis2Opts: axis2Opts,\n      scale: scale,\n      data: sourceData\n    };\n  },\n  watch: {\n    dataSource: function dataSource(newVal) {\n      if (newVal.length === 0) {\n        this.data = sourceData;\n      } else {\n        this.data = newVal;\n      }\n    }\n  }\n};", {"version": 3, "names": ["axis1Opts", "dataKey", "line", "tickLine", "grid", "lineStyle", "lineDash", "hideFirstLine", "axis2Opts", "type", "scale", "min", "max", "alias", "sourceData", "item", "score", "name", "props", "height", "Number", "default", "dataSource", "Array", "_default", "data", "watch", "newVal", "length"], "sources": ["src/components/chart/Radar.vue"], "sourcesContent": ["<template>\n  <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\" :padding=\"[20, 20, 95, 20]\" :scale=\"scale\">\n    <v-tooltip></v-tooltip>\n    <v-axis :dataKey=\"axis1Opts.dataKey\" :line=\"axis1Opts.line\" :tickLine=\"axis1Opts.tickLine\" :grid=\"axis1Opts.grid\"/>\n    <v-axis :dataKey=\"axis2Opts.dataKey\" :line=\"axis2Opts.line\" :tickLine=\"axis2Opts.tickLine\" :grid=\"axis2Opts.grid\"/>\n    <v-legend dataKey=\"user\" marker=\"circle\" :offset=\"30\"/>\n    <v-coord type=\"polar\" radius=\"0.8\"/>\n    <v-line position=\"item*score\" color=\"user\" :size=\"2\"/>\n    <v-point position=\"item*score\" color=\"user\" :size=\"4\" shape=\"circle\"/>\n  </v-chart>\n</template>\n\n<script>\n  const axis1Opts = {\n    dataKey: 'item',\n    line: null,\n    tickLine: null,\n    grid: {\n      lineStyle: {\n        lineDash: null\n      },\n      hideFirstLine: false\n    }\n  }\n  const axis2Opts = {\n    dataKey: 'score',\n    line: null,\n    tickLine: null,\n    grid: {\n      type: 'polygon',\n      lineStyle: {\n        lineDash: null\n      }\n    }\n  }\n\n  const scale = [\n    {\n      dataKey: 'score',\n      min: 0,\n      max: 100\n    }, {\n      dataKey: 'user',\n      alias: '类型'\n    }\n  ]\n\n  const sourceData = [\n    { item: '示例一', score: 40 },\n    { item: '示例二', score: 20 },\n    { item: '示例三', score: 67 },\n    { item: '示例四', score: 43 },\n    { item: '示例五', score: 90 }\n  ]\n\n  export default {\n    name: 'Radar',\n    props: {\n      height: {\n        type: Number,\n        default: 254\n      },\n      dataSource: {\n        type: Array,\n        default: () => []\n      }\n    },\n    data() {\n      return {\n        axis1Opts,\n        axis2Opts,\n        scale,\n        data: sourceData\n      }\n    },\n    watch: {\n      dataSource(newVal) {\n        if (newVal.length === 0) {\n          this.data = sourceData\n        } else {\n          this.data = newVal\n        }\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAaA,IAAAA,SAAA;EACAC,OAAA;EACAC,IAAA;EACAC,QAAA;EACAC,IAAA;IACAC,SAAA;MACAC,QAAA;IACA;IACAC,aAAA;EACA;AACA;AACA,IAAAC,SAAA;EACAP,OAAA;EACAC,IAAA;EACAC,QAAA;EACAC,IAAA;IACAK,IAAA;IACAJ,SAAA;MACAC,QAAA;IACA;EACA;AACA;AAEA,IAAAI,KAAA,IACA;EACAT,OAAA;EACAU,GAAA;EACAC,GAAA;AACA;EACAX,OAAA;EACAY,KAAA;AACA,EACA;AAEA,IAAAC,UAAA,IACA;EAAAC,IAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,IAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,IAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,IAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,IAAA;EAAAC,KAAA;AAAA,EACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAV,IAAA,EAAAW,MAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAb,IAAA,EAAAc,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAzB,SAAA,EAAAA,SAAA;MACAQ,SAAA,EAAAA,SAAA;MACAE,KAAA,EAAAA,KAAA;MACAe,IAAA,EAAAX;IACA;EACA;EACAY,KAAA;IACAJ,UAAA,WAAAA,WAAAK,MAAA;MACA,IAAAA,MAAA,CAAAC,MAAA;QACA,KAAAH,IAAA,GAAAX,UAAA;MACA;QACA,KAAAW,IAAA,GAAAE,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}