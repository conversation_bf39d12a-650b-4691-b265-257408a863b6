{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  export default {\n    name: \"HeaderNotic<PERSON>\",\n    props: {\n      tabs: {\n        type: Array,\n        default: null,\n        required: true\n      }\n    },\n    data () {\n      return {\n        loadding: false\n      }\n    },\n    methods: {\n      fetchNotice () {\n        if (this.loadding) {\n          this.loadding = false\n          return\n        }\n        this.loadding = true\n        setTimeout(() => {\n          this.loadding = false\n        }, 2000)\n      }\n    }\n  }\n", {"version": 3, "sources": ["SHeaderNotice.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SHeaderNotice.vue", "sourceRoot": "src/components/page", "sourcesContent": ["<template>\n  <a-popover trigger=\"click\" placement=\"bottomRight\" :overlayStyle=\"{ width: '300px' }\">\n    <template slot=\"content\">\n      <a-spin :spinning=\"loadding\">\n        <a-tabs>\n          <a-tab-pane v-for=\"(tab, k) in tabs\" :tab=\"tab.title\" :key=\"k\">\n\n          </a-tab-pane>\n        </a-tabs>\n      </a-spin>\n    </template>\n    <span @click=\"fetchNotice\" class=\"header-notice\">\n      <a-badge count=\"12\">\n        <a-icon style=\"font-size: 16px; padding: 4px\" type=\"bell\" />\n      </a-badge>\n    </span>\n  </a-popover>\n</template>\n\n<script>\n  export default {\n    name: \"HeaderNotice\",\n    props: {\n      tabs: {\n        type: Array,\n        default: null,\n        required: true\n      }\n    },\n    data () {\n      return {\n        loadding: false\n      }\n    },\n    methods: {\n      fetchNotice () {\n        if (this.loadding) {\n          this.loadding = false\n          return\n        }\n        this.loadding = true\n        setTimeout(() => {\n          this.loadding = false\n        }, 2000)\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .header-notice{\n    display: inline-block;\n    transition: all 0.3s;\n    span {\n      vertical-align: initial;\n    }\n  }\n</style>"]}]}