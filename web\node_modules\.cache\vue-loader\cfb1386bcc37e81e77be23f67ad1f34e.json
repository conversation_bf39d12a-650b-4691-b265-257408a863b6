{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue", "mtime": 1753197976376}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\r\nimport { getExamRecordList, getExamRecordDetail, getExamRecordPaperPreview } from '@/api/examSystem'\r\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\r\nimport ExamResultModal from './components/ExamResultModal'\r\nimport ExamPaperPreview from './components/ExamPaperPreview'\r\n\r\nexport default {\r\n  name: 'ExamRecords',\r\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\r\n  components: {\r\n    ExamResultModal,\r\n    ExamPaperPreview\r\n  },\r\n  data() {\r\n    return {\r\n      description: '考试记录页面',\r\n      // JeecgListMixin标准化API配置\r\n      url: {\r\n        list: '/teaching/examSystem/examRecord/list',\r\n        delete: '/teaching/examSystem/examRecord/delete',\r\n        deleteBatch: '/teaching/examSystem/examRecord/deleteBatch'\r\n      },\r\n      // 查询参数\r\n      queryParam: {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      },\r\n      // 表格列定义\r\n      columns: [\r\n        {\r\n          title: '试卷标题',\r\n          dataIndex: 'paperTitle',\r\n          width: '25%',\r\n          scopedSlots: { customRender: 'paperTitleSlot' }\r\n        },\r\n        {\r\n          title: '科目/级别',\r\n          dataIndex: 'subject',\r\n          customRender: (text, record) => {\r\n            return `${text} ${record.level}`\r\n          }\r\n        },\r\n        {\r\n          title: '开始时间',\r\n          dataIndex: 'startTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '结束时间',\r\n          dataIndex: 'endTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '用时',\r\n          dataIndex: 'duration',\r\n          customRender: (_, record) => {\r\n            // 使用VO中的getDuration方法计算的结果\r\n            const duration = record.duration\r\n            if (!duration) return '-'\r\n            // duration现在是秒数，直接格式化\r\n            return this.formatDuration(duration)\r\n          },\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '分数',\r\n          dataIndex: 'score',\r\n          sorter: true,\r\n          scopedSlots: { customRender: 'scoreSlot' }\r\n        },\r\n        {\r\n          title: '状态',\r\n          dataIndex: 'status',\r\n          scopedSlots: { customRender: 'statusSlot' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          scopedSlots: { customRender: 'actionSlot' }\r\n        }\r\n      ],\r\n      // 注意：dataSource, ipagination, loading 等状态\r\n      // 已由JeecgListMixin提供，无需重复定义\r\n\r\n      // 考试结果模态框相关\r\n      resultModalVisible: false,\r\n      currentExamResult: {},\r\n      currentPaperInfo: {},\r\n      currentExamQuestions: {\r\n        singleChoice: [],\r\n        judgment: [],\r\n        programming: []\r\n      },\r\n      currentExamDuration: 0,\r\n\r\n      // 试卷预览相关\r\n      paperPreviewVisible: false,\r\n      paperPreviewData: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 已完成考试数量\r\n    completedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1).length\r\n    },\r\n    // 通过考试数量\r\n    passedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1 && item.score >= 60).length\r\n    },\r\n    // 通过率\r\n    passRate() {\r\n      if (this.completedExamCount === 0) return 0\r\n      return (this.passedExamCount / this.completedExamCount) * 100\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadData()\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1\r\n      }\r\n      this.loading = true\r\n      const params = {...this.queryParam}\r\n      params.pageNo = this.ipagination.current\r\n      params.pageSize = this.ipagination.pageSize\r\n      \r\n      console.log('发送考试记录查询请求，参数:', params)\r\n\r\n      getExamRecordList(params).then((res) => {\r\n        console.log('考试记录API响应:', res)\r\n        if (res && res.success) {\r\n          // 处理分页数据结构\r\n          if (res.result && res.result.records) {\r\n            this.dataSource = res.result.records\r\n            this.ipagination.total = res.result.total\r\n          } else if (Array.isArray(res.result)) {\r\n            this.dataSource = res.result\r\n            this.ipagination.total = res.result.length\r\n          } else {\r\n            this.dataSource = []\r\n            this.ipagination.total = 0\r\n          }\r\n\r\n          console.log('处理后的数据源:', this.dataSource)\r\n          console.log('总数:', this.ipagination.total)\r\n        } else {\r\n          console.error('API返回失败:', res)\r\n          const errorMsg = res && res.message ? res.message : '获取考试记录失败'\r\n          this.$message.error(errorMsg)\r\n\r\n          // 如果是认证相关错误，可能需要重新登录\r\n          if (errorMsg.includes('token') || errorMsg.includes('登录')) {\r\n            this.$message.warning('请重新登录后再试')\r\n          }\r\n        }\r\n        this.loading = false\r\n      }).catch((err) => {\r\n        this.loading = false\r\n        console.error('获取考试记录网络错误:', err)\r\n\r\n        // 检查是否是网络错误或认证错误\r\n        if (err.response) {\r\n          console.error('HTTP错误状态:', err.response.status)\r\n          console.error('HTTP错误数据:', err.response.data)\r\n\r\n          if (err.response.status === 401) {\r\n            this.$message.error('认证失败，请重新登录')\r\n          } else if (err.response.status === 403) {\r\n            this.$message.error('权限不足')\r\n          } else {\r\n            const errorMessage = err.response.data && err.response.data.message ? err.response.data.message : err.message\r\n            this.$message.error('服务器错误：' + errorMessage)\r\n          }\r\n        } else {\r\n          this.$message.error('网络连接失败，请检查网络连接')\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 表格变化处理\r\n    handleTableChange(pagination, _, sorter) {\r\n      this.ipagination.current = pagination.current\r\n      \r\n      // 添加排序参数\r\n      if (sorter && sorter.field) {\r\n        this.queryParam.sortField = sorter.field\r\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\r\n      } else {\r\n        delete this.queryParam.sortField\r\n        delete this.queryParam.sortOrder\r\n      }\r\n      \r\n      this.loadData()\r\n    },\r\n    \r\n    // 重置查询条件\r\n    resetQuery() {\r\n      this.queryParam = {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      }\r\n      this.loadData(1)\r\n    },\r\n    \r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case 1: return 'success'\r\n        default: return 'default'\r\n      }\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 1: return '已提交'\r\n        default: return '未知'\r\n      }\r\n    },\r\n\r\n    // 获取级别选项\r\n    getLevelOptions() {\r\n      const subject = this.queryParam.subject\r\n      if (!subject) {\r\n        // 如果没有选择科目，返回所有级别\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      if (subject === 'Scratch') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' }\r\n        ]\r\n      } else if (subject === 'Python' || subject === 'C++') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      return []\r\n    },\r\n    \r\n\r\n    \r\n    // 查看结果\r\n    async viewResult(record) {\r\n      try {\r\n        this.loading = true\r\n\r\n        // 获取考试记录详情\r\n        const res = await getExamRecordDetail(record.id)\r\n\r\n        if (res.success) {\r\n          const examRecord = res.result\r\n\r\n          // 构建考试结果数据 - 使用后端返回的详细分数统计\r\n          let scoreDetails = {}\r\n          if (examRecord.scoreDetails) {\r\n            try {\r\n              scoreDetails = JSON.parse(examRecord.scoreDetails)\r\n              console.log('解析的分数详情:', scoreDetails)\r\n            } catch (e) {\r\n              console.error('解析分数详情失败:', e)\r\n              // 如果解析失败，使用默认值\r\n              scoreDetails = {\r\n                singleChoice: { score: 0, totalScore: 0 },\r\n                judgment: { score: 0, totalScore: 0 },\r\n                programming: { score: 0, totalScore: 0 }\r\n              }\r\n            }\r\n          } else {\r\n            // 如果没有详细分数统计，使用默认值\r\n            scoreDetails = {\r\n              singleChoice: { score: 0, totalScore: 0 },\r\n              judgment: { score: 0, totalScore: 0 },\r\n              programming: { score: 0, totalScore: 0 }\r\n            }\r\n          }\r\n\r\n          this.currentExamResult = {\r\n            score: examRecord.score || 0,\r\n            totalScore: examRecord.score || 0,\r\n            isPassed: (examRecord.score || 0) >= 60,\r\n            submitTime: examRecord.endTime,\r\n            paperTitle: examRecord.paperTitle,\r\n            details: scoreDetails\r\n          }\r\n\r\n          // 构建试卷信息\r\n          this.currentPaperInfo = {\r\n            title: examRecord.paperTitle,\r\n            subject: examRecord.subject,\r\n            level: examRecord.level,\r\n            difficulty: examRecord.difficulty,\r\n            type: examRecord.type,\r\n            year: examRecord.year,\r\n            examDuration: examRecord.examDuration\r\n          }\r\n\r\n          // 计算考试用时（秒）\r\n          if (examRecord.startTime && examRecord.endTime) {\r\n            this.currentExamDuration = Math.floor((new Date(examRecord.endTime).getTime() - new Date(examRecord.startTime).getTime()) / 1000)\r\n          } else {\r\n            this.currentExamDuration = 0\r\n          }\r\n\r\n          // 显示模态框\r\n          this.resultModalVisible = true\r\n\r\n        } else {\r\n          this.$message.error(res.message || '获取考试记录详情失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取考试记录详情失败:', error)\r\n        this.$message.error('获取考试记录详情失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 查看试卷\r\n    async viewPaper(record) {\r\n      console.log('查看试卷:', record)\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 调用API获取试卷预览数据\r\n        const res = await getExamRecordPaperPreview(record.id)\r\n\r\n        if (res && res.success) {\r\n          this.paperPreviewData = res.result\r\n          this.paperPreviewVisible = true\r\n          console.log('试卷预览数据:', this.paperPreviewData)\r\n        } else {\r\n          this.$message.error(res.message || '获取试卷预览失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取试卷预览失败:', error)\r\n        this.$message.error('获取试卷预览失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 格式化时长（精确显示）\r\n    formatDuration(duration) {\r\n      if (!duration) return '--'\r\n\r\n      const hours = Math.floor(duration / 3600)\r\n      const minutes = Math.floor((duration % 3600) / 60)\r\n      const seconds = duration % 60\r\n\r\n      if (hours > 0) {\r\n        // 超过1小时：显示 X时X分X秒\r\n        return `${hours}时${minutes}分${seconds}秒`\r\n      } else if (minutes > 0) {\r\n        // 1小时内但超过1分钟：显示 X分X秒\r\n        return `${minutes}分${seconds}秒`\r\n      } else {\r\n        // 1分钟内：只显示 X秒\r\n        return `${seconds}秒`\r\n      }\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["examRecords.vue"], "names": [], "mappings": ";AAoHA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "examRecords.vue", "sourceRoot": "src/views/examSystem", "sourcesContent": ["<template>\r\n  <a-card :bordered=\"false\">\r\n    <div class=\"table-page-search-wrapper\">\r\n      <a-form layout=\"inline\">\r\n        <a-row :gutter=\"24\">\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"科目\">\r\n              <a-select v-model=\"queryParam.subject\" placeholder=\"请选择科目\" allowClear>\r\n                <a-select-option value=\"Scratch\">Scratch</a-select-option>\r\n                <a-select-option value=\"Python\">Python</a-select-option>\r\n                <a-select-option value=\"C++\">C++</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"级别\">\r\n              <a-select v-model=\"queryParam.level\" placeholder=\"请选择级别\" allowClear>\r\n                <a-select-option v-for=\"option in getLevelOptions()\" :key=\"option.value\" :value=\"option.value\">{{ option.label }}</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"考试状态\">\r\n              <a-select v-model=\"queryParam.status\" placeholder=\"请选择状态\" allowClear>\r\n                <a-select-option :value=\"1\">已提交</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <span class=\"table-page-search-submitButtons\">\r\n              <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\r\n              <a-button style=\"margin-left: 8px\" @click=\"resetQuery\">重置</a-button>\r\n            </span>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n    </div>\r\n\r\n    <!-- 考试统计信息 -->\r\n    <a-card title=\"考试统计\" style=\"margin: 16px 0;\">\r\n      <a-row :gutter=\"16\">\r\n        <a-col :span=\"8\">\r\n          <a-statistic title=\"已完成考试\" :value=\"completedExamCount\" />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic title=\"通过考试\" :value=\"passedExamCount\" />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic\r\n            title=\"通过率\"\r\n            :value=\"passRate\"\r\n            :precision=\"2\"\r\n            suffix=\"%\"\r\n            :valueStyle=\"{ color: passRate >= 60 ? '#3f8600' : '#cf1322' }\"\r\n          />\r\n        </a-col>\r\n      </a-row>\r\n    </a-card>\r\n\r\n    <a-table\r\n      ref=\"table\"\r\n      size=\"middle\"\r\n      bordered\r\n      rowKey=\"id\"\r\n      :columns=\"columns\"\r\n      :dataSource=\"dataSource\"\r\n      :pagination=\"ipagination\"\r\n      :loading=\"loading\"\r\n      @change=\"handleTableChange\">\r\n\r\n      <template slot=\"statusSlot\" slot-scope=\"text\">\r\n        <a-badge :status=\"getStatusType(text)\" :text=\"getStatusText(text)\" />\r\n      </template>\r\n\r\n      <template slot=\"scoreSlot\" slot-scope=\"text, record\">\r\n        <span v-if=\"record.status === 1\">\r\n          <span :style=\"{ color: text >= 60 ? '#52c41a' : '#f5222d' }\">{{ text }}</span>\r\n          <span style=\"margin-left: 8px;\">{{ text >= 60 ? '(通过)' : '(未通过)' }}</span>\r\n        </span>\r\n        <span v-else>-</span>\r\n      </template>\r\n\r\n      <template slot=\"paperTitleSlot\" slot-scope=\"text, record\">\r\n        <span>{{ text }}</span>\r\n        <!-- 暂时移除paperType标签，因为后端VO中没有这个字段 -->\r\n      </template>\r\n\r\n      <template slot=\"actionSlot\" slot-scope=\"text, record\">\r\n        <div v-if=\"record.status === 1\">\r\n          <a @click=\"viewResult(record)\">查看结果</a>\r\n          <a-divider type=\"vertical\" />\r\n          <a @click=\"viewPaper(record)\">查看试卷</a>\r\n        </div>\r\n        <span v-else>-</span>\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- 考试结果模态框 -->\r\n    <exam-result-modal\r\n      v-model=\"resultModalVisible\"\r\n      :examResult=\"currentExamResult\"\r\n      :paperInfo=\"currentPaperInfo\"\r\n      :examQuestions=\"currentExamQuestions\"\r\n      :examDuration=\"currentExamDuration\"\r\n      :fromRecords=\"true\"\r\n    />\r\n\r\n    <!-- 试卷预览组件 -->\r\n    <exam-paper-preview\r\n      v-model=\"paperPreviewVisible\"\r\n      :paperData=\"paperPreviewData\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { getExamRecordList, getExamRecordDetail, getExamRecordPaperPreview } from '@/api/examSystem'\r\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\r\nimport ExamResultModal from './components/ExamResultModal'\r\nimport ExamPaperPreview from './components/ExamPaperPreview'\r\n\r\nexport default {\r\n  name: 'ExamRecords',\r\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\r\n  components: {\r\n    ExamResultModal,\r\n    ExamPaperPreview\r\n  },\r\n  data() {\r\n    return {\r\n      description: '考试记录页面',\r\n      // JeecgListMixin标准化API配置\r\n      url: {\r\n        list: '/teaching/examSystem/examRecord/list',\r\n        delete: '/teaching/examSystem/examRecord/delete',\r\n        deleteBatch: '/teaching/examSystem/examRecord/deleteBatch'\r\n      },\r\n      // 查询参数\r\n      queryParam: {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      },\r\n      // 表格列定义\r\n      columns: [\r\n        {\r\n          title: '试卷标题',\r\n          dataIndex: 'paperTitle',\r\n          width: '25%',\r\n          scopedSlots: { customRender: 'paperTitleSlot' }\r\n        },\r\n        {\r\n          title: '科目/级别',\r\n          dataIndex: 'subject',\r\n          customRender: (text, record) => {\r\n            return `${text} ${record.level}`\r\n          }\r\n        },\r\n        {\r\n          title: '开始时间',\r\n          dataIndex: 'startTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '结束时间',\r\n          dataIndex: 'endTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '用时',\r\n          dataIndex: 'duration',\r\n          customRender: (_, record) => {\r\n            // 使用VO中的getDuration方法计算的结果\r\n            const duration = record.duration\r\n            if (!duration) return '-'\r\n            // duration现在是秒数，直接格式化\r\n            return this.formatDuration(duration)\r\n          },\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '分数',\r\n          dataIndex: 'score',\r\n          sorter: true,\r\n          scopedSlots: { customRender: 'scoreSlot' }\r\n        },\r\n        {\r\n          title: '状态',\r\n          dataIndex: 'status',\r\n          scopedSlots: { customRender: 'statusSlot' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          scopedSlots: { customRender: 'actionSlot' }\r\n        }\r\n      ],\r\n      // 注意：dataSource, ipagination, loading 等状态\r\n      // 已由JeecgListMixin提供，无需重复定义\r\n\r\n      // 考试结果模态框相关\r\n      resultModalVisible: false,\r\n      currentExamResult: {},\r\n      currentPaperInfo: {},\r\n      currentExamQuestions: {\r\n        singleChoice: [],\r\n        judgment: [],\r\n        programming: []\r\n      },\r\n      currentExamDuration: 0,\r\n\r\n      // 试卷预览相关\r\n      paperPreviewVisible: false,\r\n      paperPreviewData: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 已完成考试数量\r\n    completedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1).length\r\n    },\r\n    // 通过考试数量\r\n    passedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1 && item.score >= 60).length\r\n    },\r\n    // 通过率\r\n    passRate() {\r\n      if (this.completedExamCount === 0) return 0\r\n      return (this.passedExamCount / this.completedExamCount) * 100\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadData()\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1\r\n      }\r\n      this.loading = true\r\n      const params = {...this.queryParam}\r\n      params.pageNo = this.ipagination.current\r\n      params.pageSize = this.ipagination.pageSize\r\n      \r\n      console.log('发送考试记录查询请求，参数:', params)\r\n\r\n      getExamRecordList(params).then((res) => {\r\n        console.log('考试记录API响应:', res)\r\n        if (res && res.success) {\r\n          // 处理分页数据结构\r\n          if (res.result && res.result.records) {\r\n            this.dataSource = res.result.records\r\n            this.ipagination.total = res.result.total\r\n          } else if (Array.isArray(res.result)) {\r\n            this.dataSource = res.result\r\n            this.ipagination.total = res.result.length\r\n          } else {\r\n            this.dataSource = []\r\n            this.ipagination.total = 0\r\n          }\r\n\r\n          console.log('处理后的数据源:', this.dataSource)\r\n          console.log('总数:', this.ipagination.total)\r\n        } else {\r\n          console.error('API返回失败:', res)\r\n          const errorMsg = res && res.message ? res.message : '获取考试记录失败'\r\n          this.$message.error(errorMsg)\r\n\r\n          // 如果是认证相关错误，可能需要重新登录\r\n          if (errorMsg.includes('token') || errorMsg.includes('登录')) {\r\n            this.$message.warning('请重新登录后再试')\r\n          }\r\n        }\r\n        this.loading = false\r\n      }).catch((err) => {\r\n        this.loading = false\r\n        console.error('获取考试记录网络错误:', err)\r\n\r\n        // 检查是否是网络错误或认证错误\r\n        if (err.response) {\r\n          console.error('HTTP错误状态:', err.response.status)\r\n          console.error('HTTP错误数据:', err.response.data)\r\n\r\n          if (err.response.status === 401) {\r\n            this.$message.error('认证失败，请重新登录')\r\n          } else if (err.response.status === 403) {\r\n            this.$message.error('权限不足')\r\n          } else {\r\n            const errorMessage = err.response.data && err.response.data.message ? err.response.data.message : err.message\r\n            this.$message.error('服务器错误：' + errorMessage)\r\n          }\r\n        } else {\r\n          this.$message.error('网络连接失败，请检查网络连接')\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 表格变化处理\r\n    handleTableChange(pagination, _, sorter) {\r\n      this.ipagination.current = pagination.current\r\n      \r\n      // 添加排序参数\r\n      if (sorter && sorter.field) {\r\n        this.queryParam.sortField = sorter.field\r\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\r\n      } else {\r\n        delete this.queryParam.sortField\r\n        delete this.queryParam.sortOrder\r\n      }\r\n      \r\n      this.loadData()\r\n    },\r\n    \r\n    // 重置查询条件\r\n    resetQuery() {\r\n      this.queryParam = {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      }\r\n      this.loadData(1)\r\n    },\r\n    \r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case 1: return 'success'\r\n        default: return 'default'\r\n      }\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 1: return '已提交'\r\n        default: return '未知'\r\n      }\r\n    },\r\n\r\n    // 获取级别选项\r\n    getLevelOptions() {\r\n      const subject = this.queryParam.subject\r\n      if (!subject) {\r\n        // 如果没有选择科目，返回所有级别\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      if (subject === 'Scratch') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' }\r\n        ]\r\n      } else if (subject === 'Python' || subject === 'C++') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      return []\r\n    },\r\n    \r\n\r\n    \r\n    // 查看结果\r\n    async viewResult(record) {\r\n      try {\r\n        this.loading = true\r\n\r\n        // 获取考试记录详情\r\n        const res = await getExamRecordDetail(record.id)\r\n\r\n        if (res.success) {\r\n          const examRecord = res.result\r\n\r\n          // 构建考试结果数据 - 使用后端返回的详细分数统计\r\n          let scoreDetails = {}\r\n          if (examRecord.scoreDetails) {\r\n            try {\r\n              scoreDetails = JSON.parse(examRecord.scoreDetails)\r\n              console.log('解析的分数详情:', scoreDetails)\r\n            } catch (e) {\r\n              console.error('解析分数详情失败:', e)\r\n              // 如果解析失败，使用默认值\r\n              scoreDetails = {\r\n                singleChoice: { score: 0, totalScore: 0 },\r\n                judgment: { score: 0, totalScore: 0 },\r\n                programming: { score: 0, totalScore: 0 }\r\n              }\r\n            }\r\n          } else {\r\n            // 如果没有详细分数统计，使用默认值\r\n            scoreDetails = {\r\n              singleChoice: { score: 0, totalScore: 0 },\r\n              judgment: { score: 0, totalScore: 0 },\r\n              programming: { score: 0, totalScore: 0 }\r\n            }\r\n          }\r\n\r\n          this.currentExamResult = {\r\n            score: examRecord.score || 0,\r\n            totalScore: examRecord.score || 0,\r\n            isPassed: (examRecord.score || 0) >= 60,\r\n            submitTime: examRecord.endTime,\r\n            paperTitle: examRecord.paperTitle,\r\n            details: scoreDetails\r\n          }\r\n\r\n          // 构建试卷信息\r\n          this.currentPaperInfo = {\r\n            title: examRecord.paperTitle,\r\n            subject: examRecord.subject,\r\n            level: examRecord.level,\r\n            difficulty: examRecord.difficulty,\r\n            type: examRecord.type,\r\n            year: examRecord.year,\r\n            examDuration: examRecord.examDuration\r\n          }\r\n\r\n          // 计算考试用时（秒）\r\n          if (examRecord.startTime && examRecord.endTime) {\r\n            this.currentExamDuration = Math.floor((new Date(examRecord.endTime).getTime() - new Date(examRecord.startTime).getTime()) / 1000)\r\n          } else {\r\n            this.currentExamDuration = 0\r\n          }\r\n\r\n          // 显示模态框\r\n          this.resultModalVisible = true\r\n\r\n        } else {\r\n          this.$message.error(res.message || '获取考试记录详情失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取考试记录详情失败:', error)\r\n        this.$message.error('获取考试记录详情失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 查看试卷\r\n    async viewPaper(record) {\r\n      console.log('查看试卷:', record)\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 调用API获取试卷预览数据\r\n        const res = await getExamRecordPaperPreview(record.id)\r\n\r\n        if (res && res.success) {\r\n          this.paperPreviewData = res.result\r\n          this.paperPreviewVisible = true\r\n          console.log('试卷预览数据:', this.paperPreviewData)\r\n        } else {\r\n          this.$message.error(res.message || '获取试卷预览失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取试卷预览失败:', error)\r\n        this.$message.error('获取试卷预览失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 格式化时长（精确显示）\r\n    formatDuration(duration) {\r\n      if (!duration) return '--'\r\n\r\n      const hours = Math.floor(duration / 3600)\r\n      const minutes = Math.floor((duration % 3600) / 60)\r\n      const seconds = duration % 60\r\n\r\n      if (hours > 0) {\r\n        // 超过1小时：显示 X时X分X秒\r\n        return `${hours}时${minutes}分${seconds}秒`\r\n      } else if (minutes > 0) {\r\n        // 1小时内但超过1分钟：显示 X分X秒\r\n        return `${minutes}分${seconds}秒`\r\n      } else {\r\n        // 1分钟内：只显示 X秒\r\n        return `${seconds}秒`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n</style> "]}]}