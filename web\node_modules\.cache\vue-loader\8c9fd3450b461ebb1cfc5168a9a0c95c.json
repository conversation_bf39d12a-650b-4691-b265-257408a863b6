{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelA.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelA.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import ImgTurnPage from '../ImgTurnPage'\n  export default {\n    name: \"SplitPanelAModal\",\n    components:{\n      ImgTurnPage\n    },\n    data () {\n      return {\n      }\n    },\n    created () {\n    },\n    methods: {\n    }\n  }\n", {"version": 3, "sources": ["SplitPanelA.vue"], "names": [], "mappings": ";AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SplitPanelA.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-card style=\"min-width: 500px;overflow-x: auto\">\n    <p>我是左侧页面</p>\n    <img-turn-page></img-turn-page>\n  </a-card>\n</template>\n\n<script>\n  import ImgTurnPage from '../ImgTurnPage'\n  export default {\n    name: \"SplitPanelAModal\",\n    components:{\n      ImgTurnPage\n    },\n    data () {\n      return {\n      }\n    },\n    created () {\n    },\n    methods: {\n    }\n  }\n</script>\n\n<style scoped>\n</style>"]}]}