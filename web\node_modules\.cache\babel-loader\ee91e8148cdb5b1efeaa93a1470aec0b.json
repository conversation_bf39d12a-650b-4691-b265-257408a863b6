{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\DashChartDemo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\DashChartDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { registerShape } from 'viser-vue';\nregisterShape('point', 'pointer', {\n  draw: function draw(cfg, container) {\n    var point = cfg.points[0];\n    point = this.parsePoint(point);\n    var center = this.parsePoint({\n      x: 0,\n      y: 0\n    });\n    container.addShape('line', {\n      attrs: {\n        x1: center.x,\n        y1: center.y,\n        x2: point.x,\n        y2: point.y + 15,\n        stroke: cfg.color,\n        lineWidth: 5,\n        lineCap: 'round'\n      }\n    });\n    return container.addShape('circle', {\n      attrs: {\n        x: center.x,\n        y: center.y,\n        r: 9.75,\n        stroke: cfg.color,\n        lineWidth: 4.5,\n        fill: '#fff'\n      }\n    });\n  }\n});\nvar scale = [{\n  dataKey: 'value',\n  min: 0,\n  max: 9,\n  tickInterval: 1,\n  nice: false\n}];\nvar data = [{\n  value: 7.0\n}];\nexport default {\n  name: \"DashChartDemo\",\n  props: {\n    datasource: {\n      type: Number,\n      default: 7\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  created: function created() {\n    if (!this.datasource) {\n      this.chartData = data;\n    } else {\n      this.chartData = [{\n        value: this.datasource\n      }];\n    }\n    this.getChartData();\n  },\n  watch: {\n    'datasource': function datasource(val) {\n      this.chartData = [{\n        value: val\n      }];\n      this.getChartData();\n    }\n  },\n  methods: {\n    getChartData: function getChartData() {\n      if (this.chartData && this.chartData.length > 0) {\n        this.abcd = this.chartData[0].value * 10;\n      } else {\n        this.abcd = 70;\n      }\n    },\n    getHtmlGuideHtml: function getHtmlGuideHtml() {\n      return '<div style=\"width: 300px;text-align: center;\">\\n' + '<p style=\"font-size: 14px;color: #545454;margin: 0;\">' + this.title + '</p>\\n' + '<p style=\"font-size: 36px;color: #545454;margin: 0;\">' + this.abcd + '%</p>\\n' + '</div>';\n    },\n    getArcGuide2End: function getArcGuide2End() {\n      return [this.chartData[0].value, 0.945];\n    }\n  },\n  data: function data() {\n    return {\n      chartData: [],\n      height: 400,\n      scale: scale,\n      abcd: 70,\n      axisLabel: {\n        offset: -16,\n        textStyle: {\n          fontSize: 18,\n          textAlign: 'center',\n          textBaseline: 'middle'\n        }\n      },\n      axisSubTickLine: {\n        length: -8,\n        stroke: '#fff',\n        strokeOpacity: 1\n      },\n      axisTickLine: {\n        length: -17,\n        stroke: '#fff',\n        strokeOpacity: 1\n      },\n      arcGuide1Start: [0, 0.945],\n      arcGuide1End: [9, 0.945],\n      arcGuide1Style: {\n        stroke: '#CBCBCB',\n        lineWidth: 18\n      },\n      arcGuide2Start: [0, 0.945],\n      arcGuide2Style: {\n        stroke: '#1890FF',\n        lineWidth: 18\n      },\n      htmlGuidePosition: ['50%', '100%'],\n      htmlGuideHtml: \"\\n        <div style=\\\"width: 300px;text-align: center;\\\">\\n          <p style=\\\"font-size: 14px;color: #545454;margin: 0;\\\">\".concat(this.title, \"</p>\\n          <p style=\\\"font-size: 36px;color: #545454;margin: 0;\\\">\").concat(this.abcd, \"%</p>\\n        </div>\\n      \")\n    };\n  }\n};", {"version": 3, "names": ["registerShape", "draw", "cfg", "container", "point", "points", "parsePoint", "center", "x", "y", "addShape", "attrs", "x1", "y1", "x2", "y2", "stroke", "color", "lineWidth", "lineCap", "r", "fill", "scale", "dataKey", "min", "max", "tickInterval", "nice", "data", "value", "name", "props", "datasource", "type", "Number", "default", "title", "String", "created", "chartData", "getChartData", "watch", "val", "methods", "length", "abcd", "getHtmlGuideHtml", "getArcGuide2End", "height", "axisLabel", "offset", "textStyle", "fontSize", "textAlign", "textBaseline", "axisSubTickLine", "strokeOpacity", "axisTickLine", "arcGuide1Start", "arcGuide1End", "arcGuide1Style", "arcGuide2Start", "arcGuide2Style", "htmlGuidePosition", "htmlGuideHtml", "concat"], "sources": ["src/components/chart/DashChartDemo.vue"], "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <v-chart :forceFit=\"true\" :height=\"300\" :data=\"chartData\" :scale=\"scale\">\n      <v-coord type=\"polar\" :startAngle=\"-202.5\" :endAngle=\"22.5\" :radius=\"0.75\"></v-coord>\n      <v-axis\n        dataKey=\"value\"\n        :zIndex=\"2\"\n        :line=\"null\"\n        :label=\"axisLabel\"\n        :subTickCount=\"4\"\n        :subTickLine=\"axisSubTickLine\"\n        :tickLine=\"axisTickLine\"\n        :grid=\"null\"\n      ></v-axis>\n      <v-axis dataKey=\"1\" :show=\"false\"></v-axis>\n      <v-series\n        gemo=\"point\"\n        position=\"value*1\"\n        shape=\"pointer\"\n        color=\"#1890FF\"\n        :active=\"false\"\n      ></v-series>\n      <v-guide\n        type=\"arc\"\n        :zIndex=\"0\"\n        :top=\"false\"\n        :start=\"arcGuide1Start\"\n        :end=\"arcGuide1End\"\n        :vStyle=\"arcGuide1Style\"\n      ></v-guide>\n      <v-guide\n        type=\"arc\"\n        :zIndex=\"1\"\n        :start=\"arcGuide2Start\"\n        :end=\"getArcGuide2End\"\n        :vStyle=\"arcGuide2Style\"\n      ></v-guide>\n      <v-guide\n        type=\"html\"\n        :position=\"htmlGuidePosition\"\n        :html=\"getHtmlGuideHtml()\"\n      ></v-guide>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { registerShape } from 'viser-vue';\n\n  registerShape('point', 'pointer', {\n    draw(cfg, container) {\n      let point = cfg.points[0];\n      point = this.parsePoint(point);\n      const center = this.parsePoint({\n        x: 0,\n        y: 0,\n      });\n      container.addShape('line', {\n        attrs: {\n          x1: center.x,\n          y1: center.y,\n          x2: point.x,\n          y2: point.y + 15,\n          stroke: cfg.color,\n          lineWidth: 5,\n          lineCap: 'round',\n        }\n      });\n      return container.addShape('circle', {\n        attrs: {\n          x: center.x,\n          y: center.y,\n          r: 9.75,\n          stroke: cfg.color,\n          lineWidth: 4.5,\n          fill: '#fff',\n        }\n      });\n    }\n  });\n\n  const scale = [{\n    dataKey: 'value',\n    min: 0,\n    max: 9,\n    tickInterval: 1,\n    nice: false,\n  }];\n\n  const data = [\n    { value: 7.0 },\n  ];\n\n  export default {\n    name:\"DashChartDemo\",\n    props:{\n      datasource:{\n        type: Number,\n        default:7\n      },\n      title: {\n        type: String,\n        default: ''\n      }\n    },\n    created(){\n      if(!this.datasource){\n        this.chartData = data;\n      }else{\n        this.chartData = [\n          { value: this.datasource },\n        ];\n      }\n      this.getChartData()\n    },\n    watch: {\n      'datasource': function (val) {\n        this.chartData = [\n          { value: val},\n        ];\n        this.getChartData();\n      }\n    },\n    methods:{\n      getChartData(){\n        if(this.chartData && this.chartData.length>0){\n          this.abcd = this.chartData[0].value * 10\n        }else{\n          this.abcd = 70\n        }\n      },\n      getHtmlGuideHtml(){\n        return '<div style=\"width: 300px;text-align: center;\">\\n' +\n          '<p style=\"font-size: 14px;color: #545454;margin: 0;\">'+this.title+'</p>\\n' +\n          '<p style=\"font-size: 36px;color: #545454;margin: 0;\">'+this.abcd+'%</p>\\n' +\n          '</div>'\n      },\n      getArcGuide2End(){\n        return [this.chartData[0].value, 0.945]\n      }\n    },\n    data() {\n      return {\n        chartData:[],\n        height: 400,\n        scale: scale,\n        abcd:70,\n        axisLabel: {\n          offset: -16,\n          textStyle: {\n            fontSize: 18,\n            textAlign: 'center',\n            textBaseline: 'middle'\n          }\n        },\n        axisSubTickLine: {\n          length: -8,\n          stroke: '#fff',\n          strokeOpacity: 1,\n        },\n        axisTickLine: {\n          length: -17,\n          stroke: '#fff',\n          strokeOpacity: 1,\n        },\n        arcGuide1Start: [0, 0.945],\n        arcGuide1End: [9, 0.945],\n        arcGuide1Style: {\n          stroke: '#CBCBCB',\n          lineWidth: 18,\n        },\n        arcGuide2Start: [0, 0.945],\n        arcGuide2Style: {\n          stroke: '#1890FF',\n          lineWidth: 18,\n        },\n        htmlGuidePosition: ['50%', '100%'],\n        htmlGuideHtml: `\n        <div style=\"width: 300px;text-align: center;\">\n          <p style=\"font-size: 14px;color: #545454;margin: 0;\">${this.title}</p>\n          <p style=\"font-size: 36px;color: #545454;margin: 0;\">${this.abcd}%</p>\n        </div>\n      `,\n      };\n    },\n  };\n</script>\n"], "mappings": "AA+CA,SAAAA,aAAA;AAEAA,aAAA;EACAC,IAAA,WAAAA,KAAAC,GAAA,EAAAC,SAAA;IACA,IAAAC,KAAA,GAAAF,GAAA,CAAAG,MAAA;IACAD,KAAA,QAAAE,UAAA,CAAAF,KAAA;IACA,IAAAG,MAAA,QAAAD,UAAA;MACAE,CAAA;MACAC,CAAA;IACA;IACAN,SAAA,CAAAO,QAAA;MACAC,KAAA;QACAC,EAAA,EAAAL,MAAA,CAAAC,CAAA;QACAK,EAAA,EAAAN,MAAA,CAAAE,CAAA;QACAK,EAAA,EAAAV,KAAA,CAAAI,CAAA;QACAO,EAAA,EAAAX,KAAA,CAAAK,CAAA;QACAO,MAAA,EAAAd,GAAA,CAAAe,KAAA;QACAC,SAAA;QACAC,OAAA;MACA;IACA;IACA,OAAAhB,SAAA,CAAAO,QAAA;MACAC,KAAA;QACAH,CAAA,EAAAD,MAAA,CAAAC,CAAA;QACAC,CAAA,EAAAF,MAAA,CAAAE,CAAA;QACAW,CAAA;QACAJ,MAAA,EAAAd,GAAA,CAAAe,KAAA;QACAC,SAAA;QACAG,IAAA;MACA;IACA;EACA;AACA;AAEA,IAAAC,KAAA;EACAC,OAAA;EACAC,GAAA;EACAC,GAAA;EACAC,YAAA;EACAC,IAAA;AACA;AAEA,IAAAC,IAAA,IACA;EAAAC,KAAA;AAAA,EACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,UAAAN,UAAA;MACA,KAAAO,SAAA,GAAAX,IAAA;IACA;MACA,KAAAW,SAAA,IACA;QAAAV,KAAA,OAAAG;MAAA,EACA;IACA;IACA,KAAAQ,YAAA;EACA;EACAC,KAAA;IACA,uBAAAT,WAAAU,GAAA;MACA,KAAAH,SAAA,IACA;QAAAV,KAAA,EAAAa;MAAA,EACA;MACA,KAAAF,YAAA;IACA;EACA;EACAG,OAAA;IACAH,YAAA,WAAAA,aAAA;MACA,SAAAD,SAAA,SAAAA,SAAA,CAAAK,MAAA;QACA,KAAAC,IAAA,QAAAN,SAAA,IAAAV,KAAA;MACA;QACA,KAAAgB,IAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,4DACA,+DAAAV,KAAA,cACA,+DAAAS,IAAA,eACA;IACA;IACAE,eAAA,WAAAA,gBAAA;MACA,aAAAR,SAAA,IAAAV,KAAA;IACA;EACA;EACAD,IAAA,WAAAA,KAAA;IACA;MACAW,SAAA;MACAS,MAAA;MACA1B,KAAA,EAAAA,KAAA;MACAuB,IAAA;MACAI,SAAA;QACAC,MAAA;QACAC,SAAA;UACAC,QAAA;UACAC,SAAA;UACAC,YAAA;QACA;MACA;MACAC,eAAA;QACAX,MAAA;QACA5B,MAAA;QACAwC,aAAA;MACA;MACAC,YAAA;QACAb,MAAA;QACA5B,MAAA;QACAwC,aAAA;MACA;MACAE,cAAA;MACAC,YAAA;MACAC,cAAA;QACA5C,MAAA;QACAE,SAAA;MACA;MACA2C,cAAA;MACAC,cAAA;QACA9C,MAAA;QACAE,SAAA;MACA;MACA6C,iBAAA;MACAC,aAAA,kIAAAC,MAAA,CAEA,KAAA7B,KAAA,6EAAA6B,MAAA,CACA,KAAApB,IAAA;IAGA;EACA;AACA", "ignoreList": []}]}