{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\StackBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\StackBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var DataSet = require('@antv/data-set');\nexport default {\n  name: 'StackBar',\n  props: {\n    dataSource: {\n      type: Array,\n      required: true,\n      default: function _default() {\n        return [{\n          'State': '请假',\n          '流转中': 25,\n          '已归档': 18\n        }, {\n          'State': '出差',\n          '流转中': 30,\n          '已归档': 20\n        }, {\n          'State': '加班',\n          '流转中': 38,\n          '已归档': 42\n        }, {\n          'State': '用车',\n          '流转中': 51,\n          '已归档': 67\n        }];\n      }\n    },\n    height: {\n      type: Number,\n      default: 254\n    }\n  },\n  data: function data() {\n    return {\n      label: {\n        offset: 12\n      }\n    };\n  },\n  computed: {\n    data: function data() {\n      var dv = new DataSet.View().source(this.dataSource);\n      dv.transform({\n        type: 'fold',\n        fields: ['流转中', '已归档'],\n        key: '流程状态',\n        value: '流程数量',\n        retains: ['State']\n      });\n      return dv.rows;\n    }\n  }\n};", {"version": 3, "names": ["DataSet", "require", "name", "props", "dataSource", "type", "Array", "required", "default", "_default", "height", "Number", "data", "label", "offset", "computed", "dv", "View", "source", "transform", "fields", "key", "value", "retains", "rows"], "sources": ["src/components/chart/StackBar.vue"], "sourcesContent": ["<template>\n  <div>\n    <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\">\n      <v-coord type=\"rect\" direction=\"LB\" />\n      <v-tooltip />\n      <v-legend />\n      <v-axis dataKey=\"State\" :label=\"label\" />\n      <v-stack-bar position=\"State*流程数量\"  color=\"流程状态\" />\n    </v-chart>\n  </div>\n\n</template>\n\n<script>\n  const DataSet = require('@antv/data-set');\n\n  export default {\n    name: 'StackBar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true,\n        default: () => [\n          { 'State': '请假', '流转中': 25, '已归档': 18 },\n          { 'State': '出差', '流转中': 30, '已归档': 20 },\n          { 'State': '加班', '流转中': 38, '已归档': 42},\n          { 'State': '用车', '流转中': 51, '已归档': 67}\n        ]\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        label: { offset: 12 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource);\n        dv.transform({\n          type: 'fold',\n          fields: ['流转中', '已归档'],\n          key: '流程状态',\n          value: '流程数量',\n          retains: ['State'],\n        });\n       return dv.rows;\n      }\n    }\n  }\n</script>"], "mappings": "AAcA,IAAAA,OAAA,GAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;MACAC,OAAA,WAAAC,SAAA;QAAA,QACA;UAAA;UAAA;UAAA;QAAA,GACA;UAAA;UAAA;UAAA;QAAA,GACA;UAAA;UAAA;UAAA;QAAA,GACA;UAAA;UAAA;UAAA;QAAA,EACA;MAAA;IACA;IACAC,MAAA;MACAL,IAAA,EAAAM,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QAAAC,MAAA;MAAA;IACA;EACA;EACAC,QAAA;IACAH,IAAA,WAAAA,KAAA;MACA,IAAAI,EAAA,OAAAhB,OAAA,CAAAiB,IAAA,GAAAC,MAAA,MAAAd,UAAA;MACAY,EAAA,CAAAG,SAAA;QACAd,IAAA;QACAe,MAAA;QACAC,GAAA;QACAC,KAAA;QACAC,OAAA;MACA;MACA,OAAAP,EAAA,CAAAQ,IAAA;IACA;EACA;AACA", "ignoreList": []}]}