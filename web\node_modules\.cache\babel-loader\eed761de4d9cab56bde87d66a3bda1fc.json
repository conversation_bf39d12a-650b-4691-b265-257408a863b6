{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue", "mtime": 1750046294782}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import GameCard from './components/GameCard.vue';\nimport GameModal from './components/GameModal.vue';\nimport ConfirmDialog from './components/ConfirmDialog.vue';\nimport { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage';\nexport default {\n  name: 'GameCenter',\n  components: {\n    GameCard: GameCard,\n    GameModal: GameModal,\n    ConfirmDialog: ConfirmDialog\n  },\n  data: function data() {\n    return {\n      // 用户信息\n      userName: '账号名',\n      userAvatar: '/logo.png',\n      // 用户金币数量\n      userCoins: 0,\n      // 游戏列表\n      games: [{\n        id: 1,\n        title: '合并与挖掘',\n        description: '将相同的方块合并升级，并挖掘获取资源，解锁更多有趣的内容。',\n        imageUrl: this.getGameImageUrl('merge-game.png'),\n        cost: 10,\n        costDescription: '10金币/10分钟',\n        url: 'https://www.crazygames.com/embed/merge-dig',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u5408\\u5E76\\u4E0E\\u6316\\u6398\\u300B\\u662F\\u4E00\\u6B3E\\u7ED3\\u5408\\u4E86\\u5408\\u5E76\\u5143\\u7D20\\u548C\\u6316\\u77FF\\u5143\\u7D20\\u7684\\u4F11\\u95F2\\u6E38\\u620F\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u70B9\\u51FB\\u76F8\\u540C\\u7684\\u65B9\\u5757\\u53EF\\u4EE5\\u5C06\\u5B83\\u4EEC\\u5408\\u5E76\\u4E3A\\u66F4\\u9AD8\\u7EA7\\u7684\\u65B9\\u5757</li>\\n              <li>\\u4F7F\\u7528\\u6316\\u6398\\u5DE5\\u5177\\u53EF\\u4EE5\\u83B7\\u53D6\\u77FF\\u77F3\\u548C\\u5B9D\\u77F3</li>\\n              <li>\\u901A\\u8FC7\\u5408\\u5E76\\u5347\\u7EA7\\u5DE5\\u5177\\uFF0C\\u63D0\\u9AD8\\u6316\\u6398\\u6548\\u7387</li>\\n              <li>\\u6536\\u96C6\\u8D44\\u6E90\\u6765\\u89E3\\u9501\\u65B0\\u7684\\u533A\\u57DF\\u548C\\u5185\\u5BB9</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u4F18\\u5148\\u5347\\u7EA7\\u6316\\u6398\\u5DE5\\u5177\\uFF0C\\u63D0\\u9AD8\\u6BCF\\u6B21\\u6316\\u6398\\u7684\\u6548\\u7387\\u3002\\u5408\\u7406\\u89C4\\u5212\\u65B9\\u5757\\u6446\\u653E\\u4F4D\\u7F6E\\uFF0C\\u4E3A\\u540E\\u7EED\\u5408\\u5E76\\u817E\\u51FA\\u7A7A\\u95F4\\u3002\\u5C3D\\u53EF\\u80FD\\u5B8C\\u6210\\u4EFB\\u52A1\\u76EE\\u6807\\u83B7\\u53D6\\u989D\\u5916\\u5956\\u52B1\\u3002</p>\\n          \"\n      }, {\n        id: 2,\n        title: '涂鸦之路',\n        description: '控制小球沿着不断变化的赛道前进，避开障碍物并收集硬币，享受刺激的奔跑体验。',\n        imageUrl: this.getGameImageUrl('doodle-road-game.png'),\n        cost: 10,\n        costDescription: '10金币/10分钟',\n        url: 'https://www.crazygames.com/embed/doodle-road',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u6D82\\u9E26\\u4E4B\\u8DEF\\u300B\\u662F\\u4E00\\u6B3E\\u9700\\u8981\\u73A9\\u5BB6\\u5728\\u4E0D\\u65AD\\u53D8\\u6362\\u7684\\u9053\\u8DEF\\u4E0A\\u63A7\\u5236\\u5C0F\\u8F66\\u524D\\u8FDB\\u7684\\u4F11\\u95F2\\u6E38\\u620F\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u70B9\\u51FB\\u5C4F\\u5E55\\u7ED8\\u5236\\u9053\\u8DEF\\uFF0C\\u8BA9\\u5C0F\\u8F66\\u80FD\\u591F\\u884C\\u9A76\\u524D\\u8FDB</li>\\n              <li>\\u907F\\u514D\\u5C0F\\u8F66\\u6389\\u51FA\\u8D5B\\u9053\\u6216\\u649E\\u5230\\u969C\\u788D\\u7269</li>\\n              <li>\\u6536\\u96C6\\u8DEF\\u4E0A\\u7684\\u91D1\\u5E01\\u4EE5\\u83B7\\u5F97\\u66F4\\u9AD8\\u5206\\u6570</li>\\n              <li>\\u901A\\u8FC7\\u5173\\u5361\\u540E\\u53EF\\u89E3\\u9501\\u66F4\\u591A\\u5185\\u5BB9</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u63D0\\u524D\\u89C4\\u5212\\u8DEF\\u7EBF\\uFF0C\\u6CE8\\u610F\\u89C2\\u5BDF\\u524D\\u65B9\\u7684\\u969C\\u788D\\u7269\\u548C\\u9677\\u9631\\u3002\\u5C3D\\u91CF\\u753B\\u51FA\\u5E73\\u7A33\\u7684\\u7EBF\\u8DEF\\u907F\\u514D\\u6025\\u8F6C\\u5F2F\\u3002\\u6536\\u96C6\\u91D1\\u5E01\\u65F6\\u6CE8\\u610F\\u4E0D\\u8981\\u4E3A\\u4E86\\u91D1\\u5E01\\u800C\\u5192\\u9669\\u8D70\\u9AD8\\u96BE\\u5EA6\\u8DEF\\u7EBF\\u3002</p>\\n          \"\n      }, {\n        id: 3,\n        title: '极限射击3D',\n        description: '在射击训练场体验各种枪械射击的乐趣，瞄准目标，提高射击精准度，享受沉浸式射击体验。',\n        imageUrl: this.getGameImageUrl('the-range-3d-game.png'),\n        cost: 20,\n        costDescription: '20金币/10分钟',\n        url: 'https://www.crazygames.com/embed/the-range-3d',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u6781\\u9650\\u5C04\\u51FB3D\\u300B\\u662F\\u4E00\\u6B3E\\u7B2C\\u4E00\\u4EBA\\u79F0\\u5C04\\u51FB\\u9776\\u573A\\u6A21\\u62DF\\u6E38\\u620F\\uFF0C\\u8BA9\\u73A9\\u5BB6\\u4F53\\u9A8C\\u5404\\u79CD\\u67AA\\u68B0\\u5C04\\u51FB\\u7684\\u4E50\\u8DA3\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u4F7F\\u7528\\u9F20\\u6807\\u7784\\u51C6\\u76EE\\u6807</li>\\n              <li>\\u70B9\\u51FB\\u9F20\\u6807\\u5DE6\\u952E\\u5C04\\u51FB</li>\\n              <li>R\\u952E\\u91CD\\u65B0\\u88C5\\u5F39</li>\\n              <li>\\u5B8C\\u6210\\u5C04\\u51FB\\u6311\\u6218\\u4EE5\\u83B7\\u53D6\\u79EF\\u5206\\u548C\\u89E3\\u9501\\u65B0\\u6B66\\u5668</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u4FDD\\u6301\\u7A33\\u5B9A\\u7684\\u7784\\u51C6\\u59FF\\u6001\\uFF0C\\u5B66\\u4E60\\u63A7\\u5236\\u540E\\u5750\\u529B\\u3002\\u6BCF\\u628A\\u6B66\\u5668\\u90FD\\u6709\\u81EA\\u5DF1\\u7684\\u7279\\u70B9\\uFF0C\\u82B1\\u65F6\\u95F4\\u719F\\u6089\\u4E0D\\u540C\\u6B66\\u5668\\u7684\\u5C04\\u51FB\\u611F\\u89C9\\u3002\\u5728\\u5C04\\u51FB\\u524D\\u6DF1\\u547C\\u5438\\u4EE5\\u63D0\\u9AD8\\u7CBE\\u51C6\\u5EA6\\u3002</p>\\n          \"\n      }, {\n        id: 4,\n        title: '篮球明星',\n        description: '体验精彩的篮球对决，选择你喜欢的球员，投篮得分并赢得比赛，成为真正的篮球明星。',\n        imageUrl: this.getGameImageUrl('basketball-game.png'),\n        cost: 20,\n        costDescription: '20金币/10分钟',\n        url: 'https://www.crazygames.com/embed/basketball-superstars',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u7BEE\\u7403\\u660E\\u661F\\u300B\\u662F\\u4E00\\u6B3E\\u8BA9\\u4F60\\u6210\\u4E3A\\u7BEE\\u7403\\u5DE8\\u661F\\u7684\\u4F53\\u80B2\\u6E38\\u620F\\uFF0C\\u901A\\u8FC7\\u6295\\u7BEE\\u6BD4\\u8D5B\\u8BA9\\u4F60\\u4F53\\u9A8C\\u7BEE\\u7403\\u7684\\u4E50\\u8DA3\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u70B9\\u51FB\\u5E76\\u62D6\\u52A8\\u5C4F\\u5E55\\u6765\\u7784\\u51C6\\u7BEE\\u7B50</li>\\n              <li>\\u677E\\u5F00\\u624B\\u6307\\u6295\\u7403</li>\\n              <li>\\u63A7\\u5236\\u529B\\u5EA6\\u548C\\u89D2\\u5EA6\\u4EE5\\u63D0\\u9AD8\\u6295\\u7BEE\\u6210\\u529F\\u7387</li>\\n              <li>\\u5B8C\\u6210\\u6311\\u6218\\u8D5A\\u53D6\\u79EF\\u5206\\u5E76\\u89E3\\u9501\\u65B0\\u7403\\u5458\\u548C\\u573A\\u5730</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u6CE8\\u610F\\u6295\\u7BEE\\u529B\\u5EA6\\u548C\\u89D2\\u5EA6\\u7684\\u638C\\u63E1\\uFF0C\\u6839\\u636E\\u8DDD\\u79BB\\u8C03\\u6574\\u6295\\u7BEE\\u5F3A\\u5EA6\\u3002\\u4E0D\\u540C\\u7684\\u7403\\u5458\\u6709\\u4E0D\\u540C\\u7684\\u7279\\u957F\\uFF0C\\u9009\\u62E9\\u9002\\u5408\\u81EA\\u5DF1\\u98CE\\u683C\\u7684\\u7403\\u5458\\u3002\\u5229\\u7528\\u7EC3\\u4E60\\u6A21\\u5F0F\\u719F\\u6089\\u6295\\u7BEE\\u624B\\u611F\\u3002</p>\\n          \"\n      }, {\n        id: 5,\n        title: '黄金矿工',\n        description: '操作采矿钩索，抓取金块和宝石，躲避石头和炸弹，在限定时间内完成采矿目标。',\n        imageUrl: this.getGameImageUrl('gold-miner-game.png'),\n        cost: 10,\n        costDescription: '10金币/10分钟',\n        url: 'https://www.crazygames.com/embed/gold-miner',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u9EC4\\u91D1\\u77FF\\u5DE5\\u300B\\u662F\\u4E00\\u6B3E\\u7ECF\\u5178\\u7684\\u6316\\u77FF\\u6E38\\u620F\\uFF0C\\u73A9\\u5BB6\\u9700\\u8981\\u63A7\\u5236\\u91C7\\u77FF\\u94A9\\u7D22\\u6293\\u53D6\\u91D1\\u5757\\u548C\\u5B9D\\u77F3\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u6309\\u4E0B\\u952E\\u76D8\\u6216\\u70B9\\u51FB\\u5C4F\\u5E55\\u53D1\\u5C04\\u94A9\\u7D22</li>\\n              <li>\\u94A9\\u7D22\\u4F1A\\u81EA\\u52A8\\u6446\\u52A8\\uFF0C\\u9700\\u8981\\u628A\\u63E1\\u65F6\\u673A\\u53D1\\u5C04</li>\\n              <li>\\u94A9\\u5B50\\u4F1A\\u6293\\u4F4F\\u89E6\\u78B0\\u5230\\u7684\\u7269\\u4F53\\u5E76\\u5C06\\u5176\\u62C9\\u56DE</li>\\n              <li>\\u4E0D\\u540C\\u7269\\u54C1\\u4EF7\\u503C\\u4E0D\\u540C\\uFF0C\\u91D1\\u5757\\u548C\\u5B9D\\u77F3\\u4EF7\\u503C\\u9AD8</li>\\n              <li>\\u5728\\u89C4\\u5B9A\\u65F6\\u95F4\\u5185\\u8FBE\\u5230\\u76EE\\u6807\\u91D1\\u989D\\u624D\\u80FD\\u8FDB\\u5165\\u4E0B\\u4E00\\u5173</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u4F18\\u5148\\u6293\\u53D6\\u4EF7\\u503C\\u9AD8\\u7684\\u91D1\\u5757\\u548C\\u5B9D\\u77F3\\u3002\\u77F3\\u5934\\u5F88\\u91CD\\u4F1A\\u51CF\\u6162\\u94A9\\u7D22\\u6536\\u56DE\\u7684\\u901F\\u5EA6\\u3002\\u5229\\u7528\\u9053\\u5177\\u53EF\\u4EE5\\u63D0\\u9AD8\\u6E38\\u620F\\u6548\\u7387\\uFF0C\\u6BD4\\u5982\\u70B8\\u836F\\u53EF\\u4EE5\\u70B8\\u6389\\u5CA9\\u77F3\\u3002\\u65F6\\u95F4\\u6709\\u9650\\uFF0C\\u8BF7\\u89C4\\u5212\\u597D\\u6BCF\\u4E00\\u6B21\\u6293\\u53D6\\u3002</p>\\n          \"\n      }, {\n        id: 6,\n        title: '跳跃小人',\n        description: '在充满障碍的关卡中奔跑和跳跃，躲避陷阱，超越对手，成为最后一个到达终点的胜利者。',\n        imageUrl: this.getGameImageUrl('jump-guys-game.png'),\n        cost: 20,\n        costDescription: '20金币/10分钟',\n        url: 'https://www.crazygames.com/embed/jump-guys',\n        tips: \"\\n            <h3>\\u6E38\\u620F\\u73A9\\u6CD5</h3>\\n            <p>\\u300A\\u8DF3\\u8DC3\\u5C0F\\u4EBA\\u300B\\u662F\\u4E00\\u6B3E\\u591A\\u4EBA\\u7ADE\\u6280\\u8DD1\\u9177\\u6E38\\u620F\\uFF0C\\u73A9\\u5BB6\\u9700\\u8981\\u64CD\\u63A7\\u89D2\\u8272\\u901A\\u8FC7\\u5404\\u79CD\\u969C\\u788D\\u7269\\u5230\\u8FBE\\u7EC8\\u70B9\\u3002</p>\\n            <h3>\\u57FA\\u672C\\u64CD\\u4F5C</h3>\\n            <ul>\\n              <li>\\u4F7F\\u7528\\u65B9\\u5411\\u952E\\u6216WASD\\u63A7\\u5236\\u89D2\\u8272\\u79FB\\u52A8</li>\\n              <li>\\u6309\\u7A7A\\u683C\\u952E\\u6216\\u4E0A\\u952E\\u8FDB\\u884C\\u8DF3\\u8DC3</li>\\n              <li>\\u907F\\u5F00\\u969C\\u788D\\u7269\\u5E76\\u5C1D\\u8BD5\\u63A8\\u6324\\u5176\\u4ED6\\u73A9\\u5BB6</li>\\n              <li>\\u52AA\\u529B\\u6210\\u4E3A\\u7B2C\\u4E00\\u4E2A\\u5230\\u8FBE\\u7EC8\\u70B9\\u7684\\u73A9\\u5BB6</li>\\n            </ul>\\n            <h3>\\u6E38\\u620F\\u6280\\u5DE7</h3>\\n            <p>\\u8DF3\\u8DC3\\u65F6\\u673A\\u5F88\\u91CD\\u8981\\uFF0C\\u4E0D\\u8981\\u8FC7\\u65E9\\u6216\\u8FC7\\u665A\\u8DF3\\u8DC3\\u3002\\u8BB0\\u4F4F\\u5173\\u5361\\u5E03\\u5C40\\u4EE5\\u4FBF\\u66F4\\u597D\\u5730\\u5E94\\u5BF9\\u969C\\u788D\\u3002\\u6709\\u65F6\\u5019\\u7B49\\u5F85\\u5176\\u4ED6\\u73A9\\u5BB6\\u5148\\u884C\\u52A8\\u53EF\\u4EE5\\u5B66\\u4E60\\u5982\\u4F55\\u907F\\u5F00\\u9677\\u9631\\u3002\\u5229\\u7528\\u7269\\u7406\\u78B0\\u649E\\u53EF\\u4EE5\\u5E72\\u6270\\u5176\\u4ED6\\u73A9\\u5BB6\\u7684\\u8FDB\\u5EA6\\u3002</p>\\n          \"\n      }],\n      // 模态窗口显示状态\n      showModal: false,\n      // 确认对话框显示状态\n      showConfirmDialog: false,\n      // 当前选择的游戏\n      currentGame: null,\n      // 确认对话框消息\n      confirmMessage: '',\n      // 确认回调函数\n      confirmCallback: null\n    };\n  },\n  beforeMount: function beforeMount() {\n    // 不需要在beforeMount阶段加载用户信息\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 在mounted阶段再次尝试加载用户信息\n    setTimeout(function () {\n      if (_this.userName === '账号名') {\n        _this.loadUserInfoDirectly();\n      }\n    }, 500);\n  },\n  created: function created() {\n    // 加载用户金币数量\n    this.loadUserCoins();\n\n    // 加载用户信息\n    this.loadUserInfoDirectly();\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 不需要移除事件监听\n  },\n  methods: {\n    // 尝试加载用户信息的统一方法\n    tryLoadUserInfo: function tryLoadUserInfo() {\n      console.log('尝试加载用户信息...');\n\n      // 方法1：从Vuex获取\n      this.loadUserInfoFromVuex();\n\n      // 方法2：从localStorage获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromLocalStorage();\n      }\n\n      // 方法3：从API获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromAPI();\n      }\n    },\n    // 从Vuex获取用户信息\n    loadUserInfoFromVuex: function loadUserInfoFromVuex() {\n      console.log('从Vuex获取用户信息');\n      console.log('Vuex用户状态:', this.$store.state.user);\n      try {\n        // 确保用户信息已加载\n        if (this.$store.state.user && this.$store.state.user.info) {\n          // 获取用户名\n          this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n          console.log('获取到用户名:', this.userName);\n\n          // 获取头像路径\n          var avatar = '';\n\n          // 先尝试从用户信息中获取头像\n          if (this.$store.state.user.info.avatar) {\n            avatar = this.$store.state.user.info.avatar;\n            console.log('从用户信息获取头像:', avatar);\n          }\n          // 再尝试从store.state.user.avatar获取\n          else if (this.$store.state.user.avatar) {\n            avatar = this.$store.state.user.avatar;\n            console.log('从store.user.avatar获取头像:', avatar);\n          }\n          // 最后使用系统默认头像\n          else if (this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n            avatar = this.$store.getters.sysConfig.avatar;\n            console.log('使用系统默认头像:', avatar);\n          }\n\n          // 处理头像路径\n          if (avatar && avatar !== '') {\n            // 如果头像路径不是http开头，则使用getFileAccessHttpUrl处理\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('处理后的头像路径:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('使用原始头像路径:', this.userAvatar);\n            }\n          } else {\n            // 使用默认头像\n            this.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('使用默认logo头像:', this.userAvatar);\n          }\n\n          // 如果成功获取了用户名，则不再尝试其他方法\n          if (this.userName !== '账号名') {\n            return true;\n          }\n        } else {\n          console.log('Vuex中用户信息未加载');\n        }\n      } catch (error) {\n        console.error('从Vuex获取用户信息出错:', error);\n      }\n      return false;\n    },\n    // 从localStorage获取用户信息\n    loadUserInfoFromLocalStorage: function loadUserInfoFromLocalStorage() {\n      console.log('从localStorage获取用户信息');\n      try {\n        // 尝试从localStorage获取用户信息\n        var userInfo = localStorage.getItem('pro__user');\n        if (userInfo) {\n          var parsedUser = JSON.parse(userInfo);\n          console.log('localStorage中的用户信息:', parsedUser);\n          if (parsedUser) {\n            // 获取用户名\n            if (parsedUser.realname || parsedUser.username) {\n              this.userName = parsedUser.realname || parsedUser.username;\n              console.log('从localStorage获取到用户名:', this.userName);\n            }\n\n            // 获取头像\n            if (parsedUser.avatar) {\n              var avatar = parsedUser.avatar;\n              if (!avatar.startsWith('http')) {\n                this.userAvatar = getFileAccessHttpUrl(avatar);\n                console.log('从localStorage处理后的头像路径:', this.userAvatar);\n              } else {\n                this.userAvatar = avatar;\n                console.log('从localStorage获取的原始头像路径:', this.userAvatar);\n              }\n              return true;\n            }\n          }\n        } else {\n          console.log('localStorage中没有用户信息');\n        }\n      } catch (error) {\n        console.error('从localStorage获取用户信息出错:', error);\n      }\n      return false;\n    },\n    // 从API获取用户信息\n    loadUserInfoFromAPI: function loadUserInfoFromAPI() {\n      var _this2 = this;\n      console.log('从API获取用户信息');\n\n      // 调用API获取用户信息\n      getAction('/sys/user/getUserInfo').then(function (res) {\n        if (res.success && res.result) {\n          console.log('API返回的用户信息:', res.result);\n\n          // 获取用户名\n          if (res.result.realname || res.result.username) {\n            _this2.userName = res.result.realname || res.result.username;\n            console.log('从API获取到用户名:', _this2.userName);\n          }\n\n          // 获取头像\n          if (res.result.avatar) {\n            var avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              _this2.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('从API处理后的头像路径:', _this2.userAvatar);\n            } else {\n              _this2.userAvatar = avatar;\n              console.log('从API获取的原始头像路径:', _this2.userAvatar);\n            }\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(function (error) {\n        console.error('API获取用户信息出错:', error);\n      });\n    },\n    // 加载用户信息（原方法，保留作为参考）\n    loadUserInfo: function loadUserInfo() {\n      this.tryLoadUserInfo();\n    },\n    // 获取游戏图片URL\n    getGameImageUrl: function getGameImageUrl(fileName) {\n      // 使用require导入assets目录中的图片\n      return require('@/assets/game/image/' + fileName);\n    },\n    // 加载用户金币数量\n    loadUserCoins: function loadUserCoins() {\n      var _this3 = this;\n      getAction('/teaching/coin/getUserCoin').then(function (res) {\n        if (res.success) {\n          _this3.userCoins = res.result || 0;\n        } else {\n          // 如果获取失败，尝试重新获取\n          setTimeout(function () {\n            _this3.loadUserCoins();\n          }, 1000);\n        }\n      }).catch(function () {\n        // 如果出错，尝试重新获取\n        setTimeout(function () {\n          _this3.loadUserCoins();\n        }, 1000);\n      });\n    },\n    // 消费金币\n    consumeCoins: function consumeCoins(amount, gameTitle) {\n      var _this4 = this;\n      // 使用AJAX请求扣减用户金币\n      postAction('/teaching/coin/consumeGameCoin', {\n        coinCount: amount,\n        gameTitle: gameTitle\n      }).then(function (res) {\n        if (res.success) {\n          _this4.userCoins -= amount;\n        } else {\n          _this4.closeGameModal();\n        }\n      }).catch(function () {\n        _this4.closeGameModal();\n      });\n    },\n    // 打开游戏模态窗口\n    openGameModal: function openGameModal(game) {\n      this.currentGame = game;\n      this.showConfirmDialog = true;\n      this.confirmMessage = \"\\u786E\\u8BA4\\u6D88\\u8D39 \".concat(game.cost, \" \\u91D1\\u5E01\\u5F00\\u59CB\\u6E38\\u620F10\\u5206\\u949F\\uFF1F\");\n    },\n    // 处理确认对话框确认操作\n    handleConfirm: function handleConfirm() {\n      if (this.userCoins >= this.currentGame.cost) {\n        this.showConfirmDialog = false;\n        this.showModal = true;\n      } else {\n        this.$message.error('金币不足，无法开始游戏！');\n        this.showConfirmDialog = false;\n      }\n    },\n    // 关闭游戏模态窗口\n    closeGameModal: function closeGameModal() {\n      this.showModal = false;\n      this.currentGame = null;\n    },\n    // 直接从localStorage获取用户信息\n    loadUserInfoDirectly: function loadUserInfoDirectly() {\n      var _this5 = this;\n      console.log('直接从localStorage获取用户信息');\n      try {\n        // 1. 尝试从localStorage获取用户信息 - 正确的键名是 Login_Userinfo\n        var userInfoStr = localStorage.getItem('pro__Login_Userinfo');\n        if (userInfoStr) {\n          var userInfoObj = JSON.parse(userInfoStr);\n          if (userInfoObj && userInfoObj.value) {\n            var userInfo = userInfoObj.value;\n            console.log('从localStorage获取到用户信息:', userInfo);\n\n            // 设置用户名\n            if (userInfo.realname || userInfo.username) {\n              this.userName = userInfo.realname || userInfo.username;\n              console.log('设置用户名为:', this.userName);\n            }\n\n            // 设置头像\n            if (userInfo.avatar) {\n              // 直接使用window全局函数处理头像URL\n              this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar);\n              console.log('设置头像URL为:', this.userAvatar);\n            } else {\n              // 使用默认头像\n              this.userAvatar = window.getFileAccessHttpUrl('/logo.png');\n              console.log('使用默认头像');\n            }\n            return;\n          }\n        }\n\n        // 2. 如果上面的方法失败，尝试从Vuex获取\n        console.log('尝试从Vuex获取用户信息');\n        if (this.$store.state.user && this.$store.state.user.info) {\n          var _userInfo = this.$store.state.user.info;\n\n          // 设置用户名\n          this.userName = _userInfo.realname || _userInfo.username || '账号名';\n          console.log('从Vuex设置用户名为:', this.userName);\n\n          // 设置头像\n          if (this.$store.state.user.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(this.$store.state.user.avatar);\n            console.log('从Vuex设置头像URL为:', this.userAvatar);\n          } else if (_userInfo.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(_userInfo.avatar);\n            console.log('从Vuex用户信息设置头像URL为:', this.userAvatar);\n          } else {\n            this.userAvatar = window.getFileAccessHttpUrl('/logo.png');\n            console.log('使用默认头像');\n          }\n          return;\n        }\n\n        // 3. 如果前两种方法都失败，直接调用API获取\n        console.log('从API获取用户信息');\n        getAction('/sys/user/getUserInfo').then(function (res) {\n          if (res.success && res.result) {\n            var _userInfo2 = res.result;\n            console.log('API返回用户信息:', _userInfo2);\n\n            // 设置用户名\n            _this5.userName = _userInfo2.realname || _userInfo2.username || '账号名';\n            console.log('从API设置用户名为:', _this5.userName);\n\n            // 设置头像\n            if (_userInfo2.avatar) {\n              _this5.userAvatar = window.getFileAccessHttpUrl(_userInfo2.avatar);\n              console.log('从API设置头像URL为:', _this5.userAvatar);\n            } else {\n              _this5.userAvatar = window.getFileAccessHttpUrl('/logo.png');\n              console.log('使用默认头像');\n            }\n          }\n        }).catch(function (error) {\n          console.error('API获取用户信息失败:', error);\n        });\n      } catch (error) {\n        console.error('获取用户信息出错:', error);\n        // 使用默认值\n        this.userName = '账号名';\n        this.userAvatar = window.getFileAccessHttpUrl('/logo.png');\n      }\n    },\n    // 采用与ShoppingModal相同的方式获取用户信息\n    getShoppingStyleUserInfo: function getShoppingStyleUserInfo() {\n      console.log('采用ShoppingModal方式获取用户信息');\n\n      // 获取用户信息\n      if (this.$store.state.user && this.$store.state.user.info) {\n        this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n        console.log('获取到用户名:', this.userName);\n\n        // 使用getFileAccessHttpUrl处理头像路径\n        var avatar = this.$store.state.user.avatar || '/logo.png';\n        console.log('原始头像路径:', avatar);\n\n        // 尝试从用户信息中获取头像路径\n        if (avatar) {\n          this.userAvatar = getFileAccessHttpUrl(avatar);\n          console.log('处理后头像路径:', this.userAvatar);\n        } else if (this.$store.getters && this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n          // 如果用户没有头像，使用默认头像\n          var defaultAvatar = this.$store.getters.sysConfig.avatar;\n          this.userAvatar = getFileAccessHttpUrl(defaultAvatar);\n          console.log('使用默认头像:', this.userAvatar);\n        } else {\n          // 使用静态默认头像\n          this.userAvatar = getFileAccessHttpUrl('/logo.png');\n          console.log('使用静态默认头像:', this.userAvatar);\n        }\n      } else {\n        console.log('用户信息未加载，使用默认值');\n      }\n    },\n    // 直接从API获取用户信息\n    getUserInfoFromAPI: function getUserInfoFromAPI() {\n      var _this6 = this;\n      console.log('直接从API获取用户信息');\n      getAction('/sys/user/getUserInfo').then(function (res) {\n        if (res.success && res.result) {\n          console.log('API返回用户信息:', res.result);\n\n          // 设置用户名\n          if (res.result.realname || res.result.username) {\n            _this6.userName = res.result.realname || res.result.username;\n            console.log('API获取到用户名:', _this6.userName);\n          }\n\n          // 设置头像\n          if (res.result.avatar) {\n            var avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              _this6.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('API获取头像处理后:', _this6.userAvatar);\n            } else {\n              _this6.userAvatar = avatar;\n              console.log('API获取原始头像:', _this6.userAvatar);\n            }\n          } else {\n            _this6.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('API无头像，使用默认头像');\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(function (err) {\n        console.error('API获取用户信息出错:', err);\n      });\n    }\n  }\n};", {"version": 3, "names": ["GameCard", "GameModal", "ConfirmDialog", "getAction", "postAction", "getFileAccessHttpUrl", "name", "components", "data", "userName", "userAvatar", "userCoins", "games", "id", "title", "description", "imageUrl", "getGameImageUrl", "cost", "costDescription", "url", "tips", "showModal", "showConfirmDialog", "currentGame", "confirmMessage", "<PERSON><PERSON><PERSON><PERSON>", "beforeMount", "mounted", "_this", "setTimeout", "loadUserInfoDirectly", "created", "loadUserCoins", "<PERSON><PERSON><PERSON><PERSON>", "methods", "tryLoadUserInfo", "console", "log", "loadUserInfoFromVuex", "loadUserInfoFromLocalStorage", "loadUserInfoFromAPI", "$store", "state", "user", "info", "realname", "username", "avatar", "getters", "sysConfig", "startsWith", "error", "userInfo", "localStorage", "getItem", "parsedUser", "JSON", "parse", "_this2", "then", "res", "success", "result", "catch", "loadUserInfo", "fileName", "require", "_this3", "consumeCoins", "amount", "gameTitle", "_this4", "coinCount", "closeGameModal", "openGameModal", "game", "concat", "handleConfirm", "$message", "_this5", "userInfoStr", "userInfoObj", "value", "window", "getShoppingStyleUserInfo", "defaultAvatar", "getUserInfoFromAPI", "_this6", "err"], "sources": ["src/views/game/GameCenter.vue"], "sourcesContent": ["<template>\n  <div class=\"game-center\">\n    <div class=\"container\">\n      <header>\n        <div class=\"header-content\">\n          <div class=\"title-area\">\n            <h1>休闲游戏中心</h1>\n            <p class=\"subtitle\">放松心情，享受游戏乐趣</p>\n          </div>\n          <div class=\"user-info\">\n            <img :src=\"userAvatar\" alt=\"用户头像\" class=\"user-avatar\"/>\n            <span class=\"user-name\">{{ userName }}</span>\n            <div class=\"coin-display\">\n              <span class=\"coin-icon\"></span>\n              <span class=\"coin-amount\">{{ userCoins }}</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div class=\"game-grid\">\n        <!-- 游戏卡片列表 -->\n        <game-card\n          v-for=\"game in games\"\n          :key=\"game.id\"\n          :game=\"game\"\n          @play-game=\"openGameModal\"\n        />\n      </div>\n\n      <footer>\n        <p>&copy; 2025 休闲游戏中心 | CFish科技少儿编程</p>\n      </footer>\n    </div>\n\n    <!-- 游戏模态窗口 -->\n    <game-modal\n      v-if=\"showModal\"\n      :game=\"currentGame\"\n      :user-coins=\"userCoins\"\n      :user-name=\"userName\"\n      :user-avatar=\"userAvatar\"\n      @close=\"closeGameModal\"\n      @consume-coins=\"consumeCoins\"\n    />\n\n    <!-- 确认弹窗 -->\n    <confirm-dialog\n      v-if=\"showConfirmDialog\"\n      :message=\"confirmMessage\"\n      :game-cost=\"currentGame ? currentGame.cost : 0\"\n      @confirm=\"handleConfirm\"\n      @cancel=\"showConfirmDialog = false\"\n    />\n  </div>\n</template>\n\n<script>\nimport GameCard from './components/GameCard.vue'\nimport GameModal from './components/GameModal.vue'\nimport ConfirmDialog from './components/ConfirmDialog.vue'\nimport { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'GameCenter',\n  components: {\n    GameCard,\n    GameModal,\n    ConfirmDialog\n  },\n  data() {\n    return {\n      // 用户信息\n      userName: '账号名',\n      userAvatar: '/logo.png',\n      // 用户金币数量\n      userCoins: 0,\n      // 游戏列表\n      games: [\n        {\n          id: 1,\n          title: '合并与挖掘',\n          description: '将相同的方块合并升级，并挖掘获取资源，解锁更多有趣的内容。',\n          imageUrl: this.getGameImageUrl('merge-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/merge-dig',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《合并与挖掘》是一款结合了合并元素和挖矿元素的休闲游戏。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击相同的方块可以将它们合并为更高级的方块</li>\n              <li>使用挖掘工具可以获取矿石和宝石</li>\n              <li>通过合并升级工具，提高挖掘效率</li>\n              <li>收集资源来解锁新的区域和内容</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>优先升级挖掘工具，提高每次挖掘的效率。合理规划方块摆放位置，为后续合并腾出空间。尽可能完成任务目标获取额外奖励。</p>\n          `\n        },\n        {\n          id: 2,\n          title: '涂鸦之路',\n          description: '控制小球沿着不断变化的赛道前进，避开障碍物并收集硬币，享受刺激的奔跑体验。',\n          imageUrl: this.getGameImageUrl('doodle-road-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/doodle-road',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《涂鸦之路》是一款需要玩家在不断变换的道路上控制小车前进的休闲游戏。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击屏幕绘制道路，让小车能够行驶前进</li>\n              <li>避免小车掉出赛道或撞到障碍物</li>\n              <li>收集路上的金币以获得更高分数</li>\n              <li>通过关卡后可解锁更多内容</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>提前规划路线，注意观察前方的障碍物和陷阱。尽量画出平稳的线路避免急转弯。收集金币时注意不要为了金币而冒险走高难度路线。</p>\n          `\n        },\n        {\n          id: 3,\n          title: '极限射击3D',\n          description: '在射击训练场体验各种枪械射击的乐趣，瞄准目标，提高射击精准度，享受沉浸式射击体验。',\n          imageUrl: this.getGameImageUrl('the-range-3d-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/the-range-3d',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《极限射击3D》是一款第一人称射击靶场模拟游戏，让玩家体验各种枪械射击的乐趣。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>使用鼠标瞄准目标</li>\n              <li>点击鼠标左键射击</li>\n              <li>R键重新装弹</li>\n              <li>完成射击挑战以获取积分和解锁新武器</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>保持稳定的瞄准姿态，学习控制后坐力。每把武器都有自己的特点，花时间熟悉不同武器的射击感觉。在射击前深呼吸以提高精准度。</p>\n          `\n        },\n        {\n          id: 4,\n          title: '篮球明星',\n          description: '体验精彩的篮球对决，选择你喜欢的球员，投篮得分并赢得比赛，成为真正的篮球明星。',\n          imageUrl: this.getGameImageUrl('basketball-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/basketball-superstars',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《篮球明星》是一款让你成为篮球巨星的体育游戏，通过投篮比赛让你体验篮球的乐趣。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击并拖动屏幕来瞄准篮筐</li>\n              <li>松开手指投球</li>\n              <li>控制力度和角度以提高投篮成功率</li>\n              <li>完成挑战赚取积分并解锁新球员和场地</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>注意投篮力度和角度的掌握，根据距离调整投篮强度。不同的球员有不同的特长，选择适合自己风格的球员。利用练习模式熟悉投篮手感。</p>\n          `\n        },\n        {\n          id: 5,\n          title: '黄金矿工',\n          description: '操作采矿钩索，抓取金块和宝石，躲避石头和炸弹，在限定时间内完成采矿目标。',\n          imageUrl: this.getGameImageUrl('gold-miner-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/gold-miner',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《黄金矿工》是一款经典的挖矿游戏，玩家需要控制采矿钩索抓取金块和宝石。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>按下键盘或点击屏幕发射钩索</li>\n              <li>钩索会自动摆动，需要把握时机发射</li>\n              <li>钩子会抓住触碰到的物体并将其拉回</li>\n              <li>不同物品价值不同，金块和宝石价值高</li>\n              <li>在规定时间内达到目标金额才能进入下一关</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>优先抓取价值高的金块和宝石。石头很重会减慢钩索收回的速度。利用道具可以提高游戏效率，比如炸药可以炸掉岩石。时间有限，请规划好每一次抓取。</p>\n          `\n        },\n        {\n          id: 6,\n          title: '跳跃小人',\n          description: '在充满障碍的关卡中奔跑和跳跃，躲避陷阱，超越对手，成为最后一个到达终点的胜利者。',\n          imageUrl: this.getGameImageUrl('jump-guys-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/jump-guys',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《跳跃小人》是一款多人竞技跑酷游戏，玩家需要操控角色通过各种障碍物到达终点。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>使用方向键或WASD控制角色移动</li>\n              <li>按空格键或上键进行跳跃</li>\n              <li>避开障碍物并尝试推挤其他玩家</li>\n              <li>努力成为第一个到达终点的玩家</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>跳跃时机很重要，不要过早或过晚跳跃。记住关卡布局以便更好地应对障碍。有时候等待其他玩家先行动可以学习如何避开陷阱。利用物理碰撞可以干扰其他玩家的进度。</p>\n          `\n        }\n      ],\n      // 模态窗口显示状态\n      showModal: false,\n      // 确认对话框显示状态\n      showConfirmDialog: false,\n      // 当前选择的游戏\n      currentGame: null,\n      // 确认对话框消息\n      confirmMessage: '',\n      // 确认回调函数\n      confirmCallback: null\n    }\n  },\n  beforeMount() {\n    // 不需要在beforeMount阶段加载用户信息\n  },\n  mounted() {\n    // 在mounted阶段再次尝试加载用户信息\n    setTimeout(() => {\n      if (this.userName === '账号名') {\n        this.loadUserInfoDirectly()\n      }\n    }, 500)\n  },\n  created() {\n    // 加载用户金币数量\n    this.loadUserCoins()\n    \n    // 加载用户信息\n    this.loadUserInfoDirectly()\n  },\n  beforeDestroy() {\n    // 不需要移除事件监听\n  },\n  methods: {\n    // 尝试加载用户信息的统一方法\n    tryLoadUserInfo() {\n      console.log('尝试加载用户信息...');\n      \n      // 方法1：从Vuex获取\n      this.loadUserInfoFromVuex();\n      \n      // 方法2：从localStorage获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromLocalStorage();\n      }\n      \n      // 方法3：从API获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromAPI();\n      }\n    },\n    \n    // 从Vuex获取用户信息\n    loadUserInfoFromVuex() {\n      console.log('从Vuex获取用户信息');\n      console.log('Vuex用户状态:', this.$store.state.user);\n      \n      try {\n        // 确保用户信息已加载\n        if (this.$store.state.user && this.$store.state.user.info) {\n          // 获取用户名\n          this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n          console.log('获取到用户名:', this.userName);\n          \n          // 获取头像路径\n          let avatar = '';\n          \n          // 先尝试从用户信息中获取头像\n          if (this.$store.state.user.info.avatar) {\n            avatar = this.$store.state.user.info.avatar;\n            console.log('从用户信息获取头像:', avatar);\n          } \n          // 再尝试从store.state.user.avatar获取\n          else if (this.$store.state.user.avatar) {\n            avatar = this.$store.state.user.avatar;\n            console.log('从store.user.avatar获取头像:', avatar);\n          }\n          // 最后使用系统默认头像\n          else if (this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n            avatar = this.$store.getters.sysConfig.avatar;\n            console.log('使用系统默认头像:', avatar);\n          }\n          \n          // 处理头像路径\n          if (avatar && avatar !== '') {\n            // 如果头像路径不是http开头，则使用getFileAccessHttpUrl处理\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('处理后的头像路径:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('使用原始头像路径:', this.userAvatar);\n            }\n          } else {\n            // 使用默认头像\n            this.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('使用默认logo头像:', this.userAvatar);\n          }\n          \n          // 如果成功获取了用户名，则不再尝试其他方法\n          if (this.userName !== '账号名') {\n            return true;\n          }\n        } else {\n          console.log('Vuex中用户信息未加载');\n        }\n      } catch (error) {\n        console.error('从Vuex获取用户信息出错:', error);\n      }\n      \n      return false;\n    },\n    \n    // 从localStorage获取用户信息\n    loadUserInfoFromLocalStorage() {\n      console.log('从localStorage获取用户信息');\n      try {\n        // 尝试从localStorage获取用户信息\n        const userInfo = localStorage.getItem('pro__user');\n        if (userInfo) {\n          const parsedUser = JSON.parse(userInfo);\n          console.log('localStorage中的用户信息:', parsedUser);\n          \n          if (parsedUser) {\n            // 获取用户名\n            if (parsedUser.realname || parsedUser.username) {\n              this.userName = parsedUser.realname || parsedUser.username;\n              console.log('从localStorage获取到用户名:', this.userName);\n            }\n            \n            // 获取头像\n            if (parsedUser.avatar) {\n              const avatar = parsedUser.avatar;\n              if (!avatar.startsWith('http')) {\n                this.userAvatar = getFileAccessHttpUrl(avatar);\n                console.log('从localStorage处理后的头像路径:', this.userAvatar);\n              } else {\n                this.userAvatar = avatar;\n                console.log('从localStorage获取的原始头像路径:', this.userAvatar);\n              }\n              \n              return true;\n            }\n          }\n        } else {\n          console.log('localStorage中没有用户信息');\n        }\n      } catch (error) {\n        console.error('从localStorage获取用户信息出错:', error);\n      }\n      \n      return false;\n    },\n    \n    // 从API获取用户信息\n    loadUserInfoFromAPI() {\n      console.log('从API获取用户信息');\n      \n      // 调用API获取用户信息\n      getAction('/sys/user/getUserInfo').then(res => {\n        if (res.success && res.result) {\n          console.log('API返回的用户信息:', res.result);\n          \n          // 获取用户名\n          if (res.result.realname || res.result.username) {\n            this.userName = res.result.realname || res.result.username;\n            console.log('从API获取到用户名:', this.userName);\n          }\n          \n          // 获取头像\n          if (res.result.avatar) {\n            const avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('从API处理后的头像路径:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('从API获取的原始头像路径:', this.userAvatar);\n            }\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(error => {\n        console.error('API获取用户信息出错:', error);\n      });\n    },\n    \n    // 加载用户信息（原方法，保留作为参考）\n    loadUserInfo() {\n      this.tryLoadUserInfo();\n    },\n    \n    // 获取游戏图片URL\n    getGameImageUrl(fileName) {\n      // 使用require导入assets目录中的图片\n      return require('@/assets/game/image/' + fileName);\n    },\n    \n    // 加载用户金币数量\n    loadUserCoins() {\n      getAction('/teaching/coin/getUserCoin')\n        .then(res => {\n          if (res.success) {\n            this.userCoins = res.result || 0\n          } else {\n            // 如果获取失败，尝试重新获取\n            setTimeout(() => {\n              this.loadUserCoins()\n            }, 1000)\n          }\n        })\n        .catch(() => {\n          // 如果出错，尝试重新获取\n          setTimeout(() => {\n            this.loadUserCoins()\n          }, 1000)\n        })\n    },\n    \n    // 消费金币\n    consumeCoins(amount, gameTitle) {\n      // 使用AJAX请求扣减用户金币\n      postAction('/teaching/coin/consumeGameCoin', {\n        coinCount: amount,\n        gameTitle: gameTitle\n      })\n      .then(res => {\n        if (res.success) {\n          this.userCoins -= amount\n        } else {\n          this.closeGameModal()\n        }\n      })\n      .catch(() => {\n        this.closeGameModal()\n      })\n    },\n    \n    // 打开游戏模态窗口\n    openGameModal(game) {\n      this.currentGame = game\n      this.showConfirmDialog = true\n      this.confirmMessage = `确认消费 ${game.cost} 金币开始游戏10分钟？`\n    },\n    \n    // 处理确认对话框确认操作\n    handleConfirm() {\n      if (this.userCoins >= this.currentGame.cost) {\n        this.showConfirmDialog = false\n        this.showModal = true\n      } else {\n        this.$message.error('金币不足，无法开始游戏！')\n        this.showConfirmDialog = false\n      }\n    },\n    \n    // 关闭游戏模态窗口\n    closeGameModal() {\n      this.showModal = false\n      this.currentGame = null\n    },\n    \n    // 直接从localStorage获取用户信息\n    loadUserInfoDirectly() {\n      console.log('直接从localStorage获取用户信息')\n      \n      try {\n        // 1. 尝试从localStorage获取用户信息 - 正确的键名是 Login_Userinfo\n        const userInfoStr = localStorage.getItem('pro__Login_Userinfo')\n        if (userInfoStr) {\n          const userInfoObj = JSON.parse(userInfoStr)\n          if (userInfoObj && userInfoObj.value) {\n            const userInfo = userInfoObj.value\n            console.log('从localStorage获取到用户信息:', userInfo)\n            \n            // 设置用户名\n            if (userInfo.realname || userInfo.username) {\n              this.userName = userInfo.realname || userInfo.username\n              console.log('设置用户名为:', this.userName)\n            }\n            \n            // 设置头像\n            if (userInfo.avatar) {\n              // 直接使用window全局函数处理头像URL\n              this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n              console.log('设置头像URL为:', this.userAvatar)\n            } else {\n              // 使用默认头像\n              this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n              console.log('使用默认头像')\n            }\n            \n            return\n          }\n        }\n        \n        // 2. 如果上面的方法失败，尝试从Vuex获取\n        console.log('尝试从Vuex获取用户信息')\n        if (this.$store.state.user && this.$store.state.user.info) {\n          const userInfo = this.$store.state.user.info\n          \n          // 设置用户名\n          this.userName = userInfo.realname || userInfo.username || '账号名'\n          console.log('从Vuex设置用户名为:', this.userName)\n          \n          // 设置头像\n          if (this.$store.state.user.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(this.$store.state.user.avatar)\n            console.log('从Vuex设置头像URL为:', this.userAvatar)\n          } else if (userInfo.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n            console.log('从Vuex用户信息设置头像URL为:', this.userAvatar)\n          } else {\n            this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n            console.log('使用默认头像')\n          }\n          \n          return\n        }\n        \n        // 3. 如果前两种方法都失败，直接调用API获取\n        console.log('从API获取用户信息')\n        getAction('/sys/user/getUserInfo')\n          .then(res => {\n            if (res.success && res.result) {\n              const userInfo = res.result\n              console.log('API返回用户信息:', userInfo)\n              \n              // 设置用户名\n              this.userName = userInfo.realname || userInfo.username || '账号名'\n              console.log('从API设置用户名为:', this.userName)\n              \n              // 设置头像\n              if (userInfo.avatar) {\n                this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n                console.log('从API设置头像URL为:', this.userAvatar)\n              } else {\n                this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n                console.log('使用默认头像')\n              }\n            }\n          })\n          .catch(error => {\n            console.error('API获取用户信息失败:', error)\n          })\n        \n      } catch (error) {\n        console.error('获取用户信息出错:', error)\n        // 使用默认值\n        this.userName = '账号名'\n        this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n      }\n    },\n    \n    // 采用与ShoppingModal相同的方式获取用户信息\n    getShoppingStyleUserInfo() {\n      console.log('采用ShoppingModal方式获取用户信息');\n      \n      // 获取用户信息\n      if (this.$store.state.user && this.$store.state.user.info) {\n        this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n        console.log('获取到用户名:', this.userName);\n        \n        // 使用getFileAccessHttpUrl处理头像路径\n        const avatar = this.$store.state.user.avatar || '/logo.png';\n        console.log('原始头像路径:', avatar);\n        \n        // 尝试从用户信息中获取头像路径\n        if (avatar) {\n          this.userAvatar = getFileAccessHttpUrl(avatar);\n          console.log('处理后头像路径:', this.userAvatar);\n        } else if (this.$store.getters && this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n          // 如果用户没有头像，使用默认头像\n          const defaultAvatar = this.$store.getters.sysConfig.avatar;\n          this.userAvatar = getFileAccessHttpUrl(defaultAvatar);\n          console.log('使用默认头像:', this.userAvatar);\n        } else {\n          // 使用静态默认头像\n          this.userAvatar = getFileAccessHttpUrl('/logo.png');\n          console.log('使用静态默认头像:', this.userAvatar);\n        }\n      } else {\n        console.log('用户信息未加载，使用默认值');\n      }\n    },\n    \n    // 直接从API获取用户信息\n    getUserInfoFromAPI() {\n      console.log('直接从API获取用户信息');\n      \n      getAction('/sys/user/getUserInfo').then(res => {\n        if (res.success && res.result) {\n          console.log('API返回用户信息:', res.result);\n          \n          // 设置用户名\n          if (res.result.realname || res.result.username) {\n            this.userName = res.result.realname || res.result.username;\n            console.log('API获取到用户名:', this.userName);\n          }\n          \n          // 设置头像\n          if (res.result.avatar) {\n            const avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('API获取头像处理后:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('API获取原始头像:', this.userAvatar);\n            }\n          } else {\n            this.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('API无头像，使用默认头像');\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(err => {\n        console.error('API获取用户信息出错:', err);\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.game-center {\n  font-family: 'Comic Sans MS', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n  background: linear-gradient(135deg, #7579ff, #b224ef);\n  background-attachment: fixed;\n  color: #333;\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 添加背景装饰元素 */\n.game-center::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.2) 5px, transparent 6px),\n    radial-gradient(circle at 30% 60%, rgba(255,255,255,0.2) 8px, transparent 9px),\n    radial-gradient(circle at 70% 40%, rgba(255,255,255,0.2) 10px, transparent 11px),\n    radial-gradient(circle at 90% 80%, rgba(255,255,255,0.2) 7px, transparent 8px);\n  background-size: 300px 300px;\n  z-index: 0;\n  animation: floatingBubbles 30s infinite linear;\n}\n\n@keyframes floatingBubbles {\n  0% { background-position: 0 0; }\n  100% { background-position: 300px 300px; }\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  position: relative;\n  z-index: 1;\n}\n\nheader {\n  background: linear-gradient(135deg, #4e54c8, #8f94fb);\n  color: white;\n  padding: 25px;\n  text-align: center;\n  border-radius: 20px;\n  margin-bottom: 30px;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 4px solid #ffffff;\n}\n\n/* 添加彩色边框动画 */\n@keyframes borderAnimation {\n  0% { border-color: #ff9a9e; }\n  25% { border-color: #fad0c4; }\n  50% { border-color: #a1c4fd; }\n  75% { border-color: #c2e9fb; }\n  100% { border-color: #ff9a9e; }\n}\n\nheader:hover {\n  transform: translateY(-5px);\n  animation: borderAnimation 3s infinite;\n}\n\n/* 添加装饰元素 */\nheader::before {\n  content: '🎮';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  top: -15px;\n  left: 20px;\n  transform: rotate(-15deg);\n}\n\nheader::after {\n  content: '🎯';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  bottom: -15px;\n  right: 20px;\n  transform: rotate(15deg);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.title-area {\n  text-align: left;\n}\n\nh1 {\n  font-size: 2.8rem;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  font-weight: bold;\n  color: #fff;\n  letter-spacing: 1px;\n  position: relative;\n  display: inline-block;\n}\n\n/* 添加标题装饰 */\nh1::after {\n  content: '';\n  position: absolute;\n  bottom: -5px;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(90deg, #ff9a9e, #fad0c4, #a1c4fd);\n  border-radius: 10px;\n}\n\n.subtitle {\n  font-size: 1.4rem;\n  font-weight: 300;\n  color: #f0f0f0;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.3);\n  padding: 10px 15px;\n  border-radius: 30px;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n}\n\n.user-avatar {\n  width: 45px;\n  height: 45px;\n  border-radius: 50%;\n  border: 3px solid #fff;\n  margin-right: 12px;\n  object-fit: cover;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n\n.user-name {\n  font-weight: bold;\n  margin-right: 15px;\n  color: #fff;\n  font-size: 1.1rem;\n}\n\n.coin-display {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #ffd86f, #fc6076);\n  padding: 8px 15px;\n  border-radius: 20px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n  transition: all 0.3s ease;\n}\n\n.coin-display:hover {\n  transform: scale(1.05);\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 8px;\n  animation: spin 10s infinite linear;\n}\n\n@keyframes spin {\n  0% { transform: rotateY(0deg); }\n  100% { transform: rotateY(360deg); }\n}\n\n.coin-amount {\n  font-weight: bold;\n  font-size: 18px;\n  color: #fff;\n}\n\n.game-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 25px;\n  margin-top: 30px;\n}\n\nfooter {\n  margin-top: 50px;\n  text-align: center;\n  color: #fff;\n  padding: 20px 0;\n  border-top: 2px dashed rgba(255, 255, 255, 0.3);\n  font-size: 1rem;\n  position: relative;\n}\n\n/* 添加页脚装饰 */\nfooter::before {\n  content: '🚀';\n  position: absolute;\n  left: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\nfooter::after {\n  content: '🌟';\n  position: absolute;\n  right: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n  }\n  \n  .title-area {\n    margin-bottom: 15px;\n    text-align: center;\n  }\n  \n  .user-info {\n    width: 100%;\n    justify-content: center;\n    margin-top: 15px;\n  }\n  \n  .game-grid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  }\n  \n  h1 {\n    font-size: 2.2rem;\n  }\n  \n  .subtitle {\n    font-size: 1.2rem;\n  }\n}\n\n/* 游戏图片样式 */\n.game-image-bg {\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n/* 添加动画效果 */\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n  100% { transform: translateY(0px); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n</style> "], "mappings": "AA0DA,OAAAA,QAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,SAAAC,SAAA,EAAAC,UAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAP,QAAA,EAAAA,QAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,aAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA;MACAC,UAAA;MACA;MACAC,SAAA;MACA;MACAC,KAAA,GACA;QACAC,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAaA,GACA;QACAR,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAaA,GACA;QACAR,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAaA,GACA;QACAR,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAaA,GACA;QACAR,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAcA,GACA;QACAR,EAAA;QACAC,KAAA;QACAC,WAAA;QACAC,QAAA,OAAAC,eAAA;QACAC,IAAA;QACAC,eAAA;QACAC,GAAA;QACAC,IAAA;MAaA,EACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;IACA;EACA;EACAC,WAAA,WAAAA,YAAA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACAC,UAAA;MACA,IAAAD,KAAA,CAAApB,QAAA;QACAoB,KAAA,CAAAE,oBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,aAAA;;IAEA;IACA,KAAAF,oBAAA;EACA;EACAG,aAAA,WAAAA,cAAA;IACA;EAAA,CACA;EACAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACAC,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAC,oBAAA;;MAEA;MACA,SAAA9B,QAAA;QACA,KAAA+B,4BAAA;MACA;;MAEA;MACA,SAAA/B,QAAA;QACA,KAAAgC,mBAAA;MACA;IACA;IAEA;IACAF,oBAAA,WAAAA,qBAAA;MACAF,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,mBAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA;MAEA;QACA;QACA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,IAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA;UACA;UACA,KAAApC,QAAA,QAAAiC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAC,QAAA,SAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAE,QAAA;UACAV,OAAA,CAAAC,GAAA,iBAAA7B,QAAA;;UAEA;UACA,IAAAuC,MAAA;;UAEA;UACA,SAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAG,MAAA;YACAA,MAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAG,MAAA;YACAX,OAAA,CAAAC,GAAA,eAAAU,MAAA;UACA;UACA;UAAA,KACA,SAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAI,MAAA;YACAA,MAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAI,MAAA;YACAX,OAAA,CAAAC,GAAA,4BAAAU,MAAA;UACA;UACA;UAAA,KACA,SAAAN,MAAA,CAAAO,OAAA,CAAAC,SAAA,SAAAR,MAAA,CAAAO,OAAA,CAAAC,SAAA,CAAAF,MAAA;YACAA,MAAA,QAAAN,MAAA,CAAAO,OAAA,CAAAC,SAAA,CAAAF,MAAA;YACAX,OAAA,CAAAC,GAAA,cAAAU,MAAA;UACA;;UAEA;UACA,IAAAA,MAAA,IAAAA,MAAA;YACA;YACA,KAAAA,MAAA,CAAAG,UAAA;cACA,KAAAzC,UAAA,GAAAL,oBAAA,CAAA2C,MAAA;cACAX,OAAA,CAAAC,GAAA,mBAAA5B,UAAA;YACA;cACA,KAAAA,UAAA,GAAAsC,MAAA;cACAX,OAAA,CAAAC,GAAA,mBAAA5B,UAAA;YACA;UACA;YACA;YACA,KAAAA,UAAA,GAAAL,oBAAA;YACAgC,OAAA,CAAAC,GAAA,qBAAA5B,UAAA;UACA;;UAEA;UACA,SAAAD,QAAA;YACA;UACA;QACA;UACA4B,OAAA,CAAAC,GAAA;QACA;MACA,SAAAc,KAAA;QACAf,OAAA,CAAAe,KAAA,mBAAAA,KAAA;MACA;MAEA;IACA;IAEA;IACAZ,4BAAA,WAAAA,6BAAA;MACAH,OAAA,CAAAC,GAAA;MACA;QACA;QACA,IAAAe,QAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,IAAAF,QAAA;UACA,IAAAG,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,QAAA;UACAhB,OAAA,CAAAC,GAAA,wBAAAkB,UAAA;UAEA,IAAAA,UAAA;YACA;YACA,IAAAA,UAAA,CAAAV,QAAA,IAAAU,UAAA,CAAAT,QAAA;cACA,KAAAtC,QAAA,GAAA+C,UAAA,CAAAV,QAAA,IAAAU,UAAA,CAAAT,QAAA;cACAV,OAAA,CAAAC,GAAA,8BAAA7B,QAAA;YACA;;YAEA;YACA,IAAA+C,UAAA,CAAAR,MAAA;cACA,IAAAA,MAAA,GAAAQ,UAAA,CAAAR,MAAA;cACA,KAAAA,MAAA,CAAAG,UAAA;gBACA,KAAAzC,UAAA,GAAAL,oBAAA,CAAA2C,MAAA;gBACAX,OAAA,CAAAC,GAAA,gCAAA5B,UAAA;cACA;gBACA,KAAAA,UAAA,GAAAsC,MAAA;gBACAX,OAAA,CAAAC,GAAA,iCAAA5B,UAAA;cACA;cAEA;YACA;UACA;QACA;UACA2B,OAAA,CAAAC,GAAA;QACA;MACA,SAAAc,KAAA;QACAf,OAAA,CAAAe,KAAA,2BAAAA,KAAA;MACA;MAEA;IACA;IAEA;IACAX,mBAAA,WAAAA,oBAAA;MAAA,IAAAkB,MAAA;MACAtB,OAAA,CAAAC,GAAA;;MAEA;MACAnC,SAAA,0BAAAyD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,MAAA;UACA1B,OAAA,CAAAC,GAAA,gBAAAuB,GAAA,CAAAE,MAAA;;UAEA;UACA,IAAAF,GAAA,CAAAE,MAAA,CAAAjB,QAAA,IAAAe,GAAA,CAAAE,MAAA,CAAAhB,QAAA;YACAY,MAAA,CAAAlD,QAAA,GAAAoD,GAAA,CAAAE,MAAA,CAAAjB,QAAA,IAAAe,GAAA,CAAAE,MAAA,CAAAhB,QAAA;YACAV,OAAA,CAAAC,GAAA,gBAAAqB,MAAA,CAAAlD,QAAA;UACA;;UAEA;UACA,IAAAoD,GAAA,CAAAE,MAAA,CAAAf,MAAA;YACA,IAAAA,MAAA,GAAAa,GAAA,CAAAE,MAAA,CAAAf,MAAA;YACA,KAAAA,MAAA,CAAAG,UAAA;cACAQ,MAAA,CAAAjD,UAAA,GAAAL,oBAAA,CAAA2C,MAAA;cACAX,OAAA,CAAAC,GAAA,kBAAAqB,MAAA,CAAAjD,UAAA;YACA;cACAiD,MAAA,CAAAjD,UAAA,GAAAsC,MAAA;cACAX,OAAA,CAAAC,GAAA,mBAAAqB,MAAA,CAAAjD,UAAA;YACA;UACA;QACA;UACA2B,OAAA,CAAAC,GAAA;QACA;MACA,GAAA0B,KAAA,WAAAZ,KAAA;QACAf,OAAA,CAAAe,KAAA,iBAAAA,KAAA;MACA;IACA;IAEA;IACAa,YAAA,WAAAA,aAAA;MACA,KAAA7B,eAAA;IACA;IAEA;IACAnB,eAAA,WAAAA,gBAAAiD,QAAA;MACA;MACA,OAAAC,OAAA,0BAAAD,QAAA;IACA;IAEA;IACAjC,aAAA,WAAAA,cAAA;MAAA,IAAAmC,MAAA;MACAjE,SAAA,+BACAyD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAM,MAAA,CAAAzD,SAAA,GAAAkD,GAAA,CAAAE,MAAA;QACA;UACA;UACAjC,UAAA;YACAsC,MAAA,CAAAnC,aAAA;UACA;QACA;MACA,GACA+B,KAAA;QACA;QACAlC,UAAA;UACAsC,MAAA,CAAAnC,aAAA;QACA;MACA;IACA;IAEA;IACAoC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,SAAA;MAAA,IAAAC,MAAA;MACA;MACApE,UAAA;QACAqE,SAAA,EAAAH,MAAA;QACAC,SAAA,EAAAA;MACA,GACAX,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAU,MAAA,CAAA7D,SAAA,IAAA2D,MAAA;QACA;UACAE,MAAA,CAAAE,cAAA;QACA;MACA,GACAV,KAAA;QACAQ,MAAA,CAAAE,cAAA;MACA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAC,IAAA;MACA,KAAApD,WAAA,GAAAoD,IAAA;MACA,KAAArD,iBAAA;MACA,KAAAE,cAAA,+BAAAoD,MAAA,CAAAD,IAAA,CAAA1D,IAAA;IACA;IAEA;IACA4D,aAAA,WAAAA,cAAA;MACA,SAAAnE,SAAA,SAAAa,WAAA,CAAAN,IAAA;QACA,KAAAK,iBAAA;QACA,KAAAD,SAAA;MACA;QACA,KAAAyD,QAAA,CAAA3B,KAAA;QACA,KAAA7B,iBAAA;MACA;IACA;IAEA;IACAmD,cAAA,WAAAA,eAAA;MACA,KAAApD,SAAA;MACA,KAAAE,WAAA;IACA;IAEA;IACAO,oBAAA,WAAAA,qBAAA;MAAA,IAAAiD,MAAA;MACA3C,OAAA,CAAAC,GAAA;MAEA;QACA;QACA,IAAA2C,WAAA,GAAA3B,YAAA,CAAAC,OAAA;QACA,IAAA0B,WAAA;UACA,IAAAC,WAAA,GAAAzB,IAAA,CAAAC,KAAA,CAAAuB,WAAA;UACA,IAAAC,WAAA,IAAAA,WAAA,CAAAC,KAAA;YACA,IAAA9B,QAAA,GAAA6B,WAAA,CAAAC,KAAA;YACA9C,OAAA,CAAAC,GAAA,0BAAAe,QAAA;;YAEA;YACA,IAAAA,QAAA,CAAAP,QAAA,IAAAO,QAAA,CAAAN,QAAA;cACA,KAAAtC,QAAA,GAAA4C,QAAA,CAAAP,QAAA,IAAAO,QAAA,CAAAN,QAAA;cACAV,OAAA,CAAAC,GAAA,iBAAA7B,QAAA;YACA;;YAEA;YACA,IAAA4C,QAAA,CAAAL,MAAA;cACA;cACA,KAAAtC,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA,CAAAgD,QAAA,CAAAL,MAAA;cACAX,OAAA,CAAAC,GAAA,mBAAA5B,UAAA;YACA;cACA;cACA,KAAAA,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA;cACAgC,OAAA,CAAAC,GAAA;YACA;YAEA;UACA;QACA;;QAEA;QACAD,OAAA,CAAAC,GAAA;QACA,SAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA;UACA,IAAAQ,SAAA,QAAAX,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA;;UAEA;UACA,KAAApC,QAAA,GAAA4C,SAAA,CAAAP,QAAA,IAAAO,SAAA,CAAAN,QAAA;UACAV,OAAA,CAAAC,GAAA,sBAAA7B,QAAA;;UAEA;UACA,SAAAiC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAI,MAAA;YACA,KAAAtC,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA,MAAAqC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAI,MAAA;YACAX,OAAA,CAAAC,GAAA,wBAAA5B,UAAA;UACA,WAAA2C,SAAA,CAAAL,MAAA;YACA,KAAAtC,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA,CAAAgD,SAAA,CAAAL,MAAA;YACAX,OAAA,CAAAC,GAAA,4BAAA5B,UAAA;UACA;YACA,KAAAA,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA;YACAgC,OAAA,CAAAC,GAAA;UACA;UAEA;QACA;;QAEA;QACAD,OAAA,CAAAC,GAAA;QACAnC,SAAA,0BACAyD,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,MAAA;YACA,IAAAV,UAAA,GAAAQ,GAAA,CAAAE,MAAA;YACA1B,OAAA,CAAAC,GAAA,eAAAe,UAAA;;YAEA;YACA2B,MAAA,CAAAvE,QAAA,GAAA4C,UAAA,CAAAP,QAAA,IAAAO,UAAA,CAAAN,QAAA;YACAV,OAAA,CAAAC,GAAA,gBAAA0C,MAAA,CAAAvE,QAAA;;YAEA;YACA,IAAA4C,UAAA,CAAAL,MAAA;cACAgC,MAAA,CAAAtE,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA,CAAAgD,UAAA,CAAAL,MAAA;cACAX,OAAA,CAAAC,GAAA,kBAAA0C,MAAA,CAAAtE,UAAA;YACA;cACAsE,MAAA,CAAAtE,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA;cACAgC,OAAA,CAAAC,GAAA;YACA;UACA;QACA,GACA0B,KAAA,WAAAZ,KAAA;UACAf,OAAA,CAAAe,KAAA,iBAAAA,KAAA;QACA;MAEA,SAAAA,KAAA;QACAf,OAAA,CAAAe,KAAA,cAAAA,KAAA;QACA;QACA,KAAA3C,QAAA;QACA,KAAAC,UAAA,GAAA0E,MAAA,CAAA/E,oBAAA;MACA;IACA;IAEA;IACAgF,wBAAA,WAAAA,yBAAA;MACAhD,OAAA,CAAAC,GAAA;;MAEA;MACA,SAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA;QACA,KAAApC,QAAA,QAAAiC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAC,QAAA,SAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA,CAAAE,QAAA;QACAV,OAAA,CAAAC,GAAA,iBAAA7B,QAAA;;QAEA;QACA,IAAAuC,MAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAI,MAAA;QACAX,OAAA,CAAAC,GAAA,YAAAU,MAAA;;QAEA;QACA,IAAAA,MAAA;UACA,KAAAtC,UAAA,GAAAL,oBAAA,CAAA2C,MAAA;UACAX,OAAA,CAAAC,GAAA,kBAAA5B,UAAA;QACA,gBAAAgC,MAAA,CAAAO,OAAA,SAAAP,MAAA,CAAAO,OAAA,CAAAC,SAAA,SAAAR,MAAA,CAAAO,OAAA,CAAAC,SAAA,CAAAF,MAAA;UACA;UACA,IAAAsC,aAAA,QAAA5C,MAAA,CAAAO,OAAA,CAAAC,SAAA,CAAAF,MAAA;UACA,KAAAtC,UAAA,GAAAL,oBAAA,CAAAiF,aAAA;UACAjD,OAAA,CAAAC,GAAA,iBAAA5B,UAAA;QACA;UACA;UACA,KAAAA,UAAA,GAAAL,oBAAA;UACAgC,OAAA,CAAAC,GAAA,mBAAA5B,UAAA;QACA;MACA;QACA2B,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAiD,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACAnD,OAAA,CAAAC,GAAA;MAEAnC,SAAA,0BAAAyD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,MAAA;UACA1B,OAAA,CAAAC,GAAA,eAAAuB,GAAA,CAAAE,MAAA;;UAEA;UACA,IAAAF,GAAA,CAAAE,MAAA,CAAAjB,QAAA,IAAAe,GAAA,CAAAE,MAAA,CAAAhB,QAAA;YACAyC,MAAA,CAAA/E,QAAA,GAAAoD,GAAA,CAAAE,MAAA,CAAAjB,QAAA,IAAAe,GAAA,CAAAE,MAAA,CAAAhB,QAAA;YACAV,OAAA,CAAAC,GAAA,eAAAkD,MAAA,CAAA/E,QAAA;UACA;;UAEA;UACA,IAAAoD,GAAA,CAAAE,MAAA,CAAAf,MAAA;YACA,IAAAA,MAAA,GAAAa,GAAA,CAAAE,MAAA,CAAAf,MAAA;YACA,KAAAA,MAAA,CAAAG,UAAA;cACAqC,MAAA,CAAA9E,UAAA,GAAAL,oBAAA,CAAA2C,MAAA;cACAX,OAAA,CAAAC,GAAA,gBAAAkD,MAAA,CAAA9E,UAAA;YACA;cACA8E,MAAA,CAAA9E,UAAA,GAAAsC,MAAA;cACAX,OAAA,CAAAC,GAAA,eAAAkD,MAAA,CAAA9E,UAAA;YACA;UACA;YACA8E,MAAA,CAAA9E,UAAA,GAAAL,oBAAA;YACAgC,OAAA,CAAAC,GAAA;UACA;QACA;UACAD,OAAA,CAAAC,GAAA;QACA;MACA,GAAA0B,KAAA,WAAAyB,GAAA;QACApD,OAAA,CAAAe,KAAA,iBAAAqC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}