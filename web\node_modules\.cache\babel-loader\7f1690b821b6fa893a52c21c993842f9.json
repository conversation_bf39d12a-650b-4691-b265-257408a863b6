{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import moment from 'dayjs';\nvar sourceData = [];\nvar beginDay = new Date().getTime();\nfor (var i = 0; i < 10; i++) {\n  sourceData.push({\n    x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n    y: Math.round(Math.random() * 10)\n  });\n}\nexport default {\n  name: 'MiniArea',\n  props: {\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    // x 轴别名\n    x: {\n      type: String,\n      default: 'x'\n    },\n    // y 轴别名\n    y: {\n      type: String,\n      default: 'y'\n    }\n  },\n  data: function data() {\n    return {\n      data: [],\n      height: 100\n    };\n  },\n  computed: {\n    scale: function scale() {\n      return [{\n        dataKey: 'x',\n        title: this.x,\n        alias: this.x\n      }, {\n        dataKey: 'y',\n        title: this.y,\n        alias: this.y\n      }];\n    }\n  },\n  created: function created() {\n    if (this.dataSource.length === 0) {\n      this.data = sourceData;\n    } else {\n      this.data = this.dataSource;\n    }\n  }\n};", {"version": 3, "names": ["moment", "sourceData", "beginDay", "Date", "getTime", "i", "push", "x", "format", "y", "Math", "round", "random", "name", "props", "dataSource", "type", "Array", "default", "_default", "String", "data", "height", "computed", "scale", "dataKey", "title", "alias", "created", "length"], "sources": ["src/components/chart/MiniArea.vue"], "sourcesContent": ["<template>\n  <div class=\"antv-chart-mini\">\n    <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\n      <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :padding=\"[36, 0, 18, 0]\">\n        <v-tooltip/>\n        <v-smooth-area position=\"x*y\"/>\n      </v-chart>\n    </div>\n  </div>\n</template>\n\n<script>\n  import moment from 'dayjs'\n\n  const sourceData = []\n  const beginDay = new Date().getTime()\n\n  for (let i = 0; i < 10; i++) {\n    sourceData.push({\n      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n      y: Math.round(Math.random() * 10)\n    })\n  }\n\n  export default {\n    name: 'MiniArea',\n    props: {\n      dataSource: {\n        type: Array,\n        default: () => []\n      },\n      // x 轴别名\n      x: {\n        type: String,\n        default: 'x'\n      },\n      // y 轴别名\n      y: {\n        type: String,\n        default: 'y'\n      }\n    },\n    data() {\n      return {\n        data: [],\n        height: 100\n      }\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      if (this.dataSource.length === 0) {\n        this.data = sourceData\n      } else {\n        this.data = this.dataSource\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"chart\";\n</style>"], "mappings": "AAYA,OAAAA,MAAA;AAEA,IAAAC,UAAA;AACA,IAAAC,QAAA,OAAAC,IAAA,GAAAC,OAAA;AAEA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;EACAJ,UAAA,CAAAK,IAAA;IACAC,CAAA,EAAAP,MAAA,KAAAG,IAAA,CAAAD,QAAA,yBAAAG,CAAA,GAAAG,MAAA;IACAC,CAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;EACA;AACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAZ,CAAA;MACAS,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAT,CAAA;MACAO,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,QACA;QAAAC,OAAA;QAAAC,KAAA,OAAAnB,CAAA;QAAAoB,KAAA,OAAApB;MAAA,GACA;QAAAkB,OAAA;QAAAC,KAAA,OAAAjB,CAAA;QAAAkB,KAAA,OAAAlB;MAAA,EACA;IACA;EACA;EACAmB,OAAA,WAAAA,QAAA;IACA,SAAAb,UAAA,CAAAc,MAAA;MACA,KAAAR,IAAA,GAAApB,UAAA;IACA;MACA,KAAAoB,IAAA,QAAAN,UAAA;IACA;EACA;AACA", "ignoreList": []}]}