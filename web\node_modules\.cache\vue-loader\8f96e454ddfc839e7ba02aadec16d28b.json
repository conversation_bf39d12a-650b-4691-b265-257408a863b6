{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\ExamPaperPreview.vue?vue&type=style&index=0&id=a3abb3e6&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\ExamPaperPreview.vue", "mtime": 1753586761732}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.exam-paper-preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.exam-paper-preview-container {\n  width: 100vw;\n  height: 100vh;\n  background-color: #f0f2f5;\n  display: flex;\n  overflow: hidden;\n}\n\n/* 左侧答题卡样式 */\n.preview-answer-card {\n  width: 300px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.preview-card-title {\n  padding: 16px;\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n  border-bottom: 1px solid #e8e8e8;\n  background-color: #fafafa;\n}\n\n.preview-exam-info {\n  padding: 16px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.preview-exam-name {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 12px;\n  color: #1890ff;\n  text-align: center;\n}\n\n.preview-exam-detail {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 8px;\n\n    .label {\n      color: #666;\n    }\n\n    .value {\n      font-weight: bold;\n\n      &.score {\n        &.passed {\n          color: #52c41a;\n        }\n\n        &.failed {\n          color: #f5222d;\n        }\n      }\n    }\n  }\n}\n\n.preview-question-section-container {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n}\n\n.preview-question-section {\n  margin-bottom: 20px;\n}\n\n.preview-section-header {\n  font-size: 14px;\n  font-weight: bold;\n  margin-bottom: 12px;\n  color: #333;\n}\n\n.preview-question-numbers {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.preview-question-number {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #1890ff;\n  }\n\n  &.selected {\n    border-color: #1890ff;\n    background-color: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.correct {\n    background-color: #f6ffed;\n    border-color: #52c41a;\n    color: #52c41a;\n\n    &.selected {\n      background-color: #52c41a;\n      color: #fff;\n    }\n  }\n\n  &.incorrect {\n    background-color: #fff2f0;\n    border-color: #f5222d;\n    color: #f5222d;\n\n    &.selected {\n      background-color: #f5222d;\n      color: #fff;\n    }\n  }\n\n  &.unanswered {\n    background-color: #fafafa;\n    border-color: #d9d9d9;\n    color: #999;\n\n    &.selected {\n      background-color: #1890ff;\n      border-color: #1890ff;\n      color: #fff;\n    }\n  }\n}\n\n.preview-exit-area {\n  padding: 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n/* 右侧内容区域样式 */\n.preview-content-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.preview-question-content-card {\n  flex: 1;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n\n  /deep/ .ant-card-body {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    padding: 0;\n  }\n}\n\n.preview-exam-tabs {\n  border-bottom: 1px solid #e8e8e8;\n  background-color: #fafafa;\n}\n\n.preview-tab-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 24px;\n}\n\n.preview-tab-items {\n  display: flex;\n}\n\n.preview-tab-item {\n  padding: 16px 20px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n  }\n\n  &.tab-active {\n    color: #1890ff;\n    border-bottom-color: #1890ff;\n  }\n}\n\n.preview-tab-content {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.preview-exam-content {\n  padding: 24px;\n}\n\n.preview-exam-section {\n  margin-bottom: 32px;\n}\n\n.preview-section-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 20px;\n  color: #333;\n  border-bottom: 1px solid #e8e8e8;\n  padding-bottom: 8px;\n}\n\n.preview-exam-question {\n  margin-bottom: 24px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 6px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.preview-question-title {\n  font-size: 14px;\n  font-weight: bold;\n  margin-bottom: 16px;\n  line-height: 1.6;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n// 题目状态标签样式 - 参考QuestionDisplay.vue查阅模式样式\n.status-tag {\n  display: inline-block;\n  vertical-align: baseline;\n  margin-left: 10px;\n  padding: 0 6px;\n  font-size: inherit;\n  font-weight: 600;\n  line-height: inherit;\n\n  .anticon {\n    margin-right: 4px;\n    font-size: 0.9em;\n  }\n\n  // 正确状态样式\n  &.correct {\n    color: #52c41a;\n  }\n\n  // 错误状态样式\n  &.incorrect {\n    color: #ff4d4f;\n  }\n\n  // 未完成状态样式\n  &.unanswered {\n    color: #1890ff;\n  }\n}\n\n.preview-question-options {\n  margin-bottom: 16px;\n}\n\n.preview-question-option {\n  margin-bottom: 8px;\n  display: flex;\n  align-items: flex-start;\n  padding: 8px 12px;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  transition: all 0.3s;\n  border: 1px solid transparent;\n\n  &:hover {\n    background-color: #f0f7ff;\n  }\n\n  .option-label {\n    margin-right: 8px;\n    font-weight: bold;\n    min-width: 20px;\n  }\n\n  .option-content {\n    flex: 1;\n    line-height: 1.5;\n  }\n\n  // 用户错误选择的选项\n  &.review-error-option {\n    background-color: #fff1f0 !important;\n    border-color: #ffa39e !important;\n\n    .option-label, .option-content {\n      color: #f5222d !important;\n    }\n  }\n\n  // 正确答案选项\n  &.review-correct-option {\n    background-color: #f6ffed !important;\n    border-color: #b7eb8f !important;\n\n    .option-label, .option-content {\n      color: #52c41a !important;\n    }\n  }\n}\n\n.preview-answer-info {\n  background-color: #fafafa;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #1890ff;\n}\n\n.answer-row {\n  display: flex;\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.answer-label {\n  min-width: 50px;\n  color: #666;\n  font-weight: bold;\n  margin-right: 4px;\n}\n\n.answer-value {\n  flex: 1;\n\n  &.user-answer {\n    &.correct {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.incorrect {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n\n  &.correct-answer {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.score-value {\n    &.full-score {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.zero-score {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n\n  &.analysis-content {\n    color: #666;\n    line-height: 1.6;\n    font-style: italic;\n  }\n}\n\n/* 编程题特殊样式 */\n.preview-programming-question {\n  .preview-title-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n  }\n\n  .preview-title-left {\n    display: flex;\n    align-items: center;\n\n    .preview-question-number {\n      background-color: #1890ff;\n      color: #fff;\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      margin-right: 12px;\n    }\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n    }\n  }\n\n\n}\n\n.preview-coding-question-container {\n  margin-top: 12px;\n\n  // 为左侧题目描述区域添加固定高度和滚动条\n  .ant-col-md-10 {\n    max-height: 700px;\n    overflow-y: auto;\n    padding-right: 10px;\n    border-right: 1px solid #f0f0f0;\n  }\n\n  .preview-problem-section {\n    margin-bottom: 12px;\n\n    strong {\n      font-size: 16px;\n      color: #262626;\n    }\n  }\n\n  .preview-limit-info {\n    display: flex;\n    gap: 16px;\n    margin-top: 8px;\n\n    .preview-limit-item {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      color: #666;\n      font-size: 14px;\n\n      .anticon {\n        color: #1890ff;\n      }\n    }\n  }\n\n  .preview-sample-container {\n    display: flex;\n    gap: 16px;\n    margin-top: 8px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .preview-sample-input, .preview-sample-output {\n      flex: 1;\n      background: #f8f9fa;\n      border-radius: 8px;\n      overflow: hidden;\n\n      .preview-sample-header {\n        padding: 8px 12px;\n        background: #e6f7ff;\n        color: #1890ff;\n        font-weight: 600;\n      }\n\n      pre {\n        margin: 0;\n        padding: 12px;\n        background: transparent;\n        border-radius: 0;\n      }\n    }\n  }\n\n  .preview-problem-section {\n    margin-bottom: 16px;\n\n    p {\n      margin-bottom: 8px;\n      font-weight: bold;\n    }\n  }\n\n  .preview-limit-info {\n    display: flex;\n    gap: 16px;\n  }\n\n  .preview-limit-item {\n    display: flex;\n    align-items: center;\n    gap: 4px;\n    color: #666;\n    font-size: 12px;\n  }\n\n  .preview-sample-container {\n    background-color: #f8f9fa;\n    border-radius: 4px;\n    overflow: hidden;\n    border: 1px solid #e8e8e8;\n  }\n\n  .preview-sample-input,\n  .preview-sample-output {\n    .preview-sample-header {\n      background-color: #e8e8e8;\n      padding: 8px 12px;\n      font-weight: bold;\n      font-size: 12px;\n      color: #666;\n    }\n\n    pre {\n      margin: 0;\n      padding: 12px;\n      background-color: #fff;\n      border: none;\n      font-family: 'Courier New', monospace;\n      font-size: 12px;\n      line-height: 1.4;\n      white-space: pre-wrap;\n      word-wrap: break-word;\n    }\n  }\n}\n\n.preview-code-section {\n  background-color: #fff;\n  border-radius: 4px;\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.preview-code-header {\n  background-color: #fafafa;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .preview-code-title {\n    font-weight: bold;\n    color: #333;\n  }\n\n  .preview-code-language {\n    background-color: #1890ff;\n    color: #fff;\n    padding: 2px 8px;\n    border-radius: 12px;\n    font-size: 12px;\n  }\n}\n\n.preview-code-content {\n  max-height: 400px;\n  overflow-y: auto;\n\n  .preview-code-display {\n    margin: 0;\n    padding: 16px;\n    background-color: #f8f9fa;\n    border: none;\n    font-family: 'Courier New', monospace;\n    font-size: 18px;\n    line-height: 1.4;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    color: #333;\n  }\n}\n\n.preview-code-result {\n  background-color: #fafafa;\n  padding: 12px 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n.result-row {\n  display: flex;\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.result-label {\n  min-width: 80px;\n  color: #666;\n  font-weight: bold;\n}\n\n.result-value {\n  flex: 1;\n\n  &.submitted {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.not-submitted {\n    color: #999;\n  }\n\n  &.correct {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.incorrect {\n    color: #f5222d;\n    font-weight: bold;\n  }\n\n  &.score-value {\n    &.full-score {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.zero-score {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .preview-answer-card {\n    width: 250px;\n  }\n\n  .preview-exam-content {\n    padding: 16px;\n  }\n\n  .preview-coding-question-container {\n    .ant-row {\n      flex-direction: column;\n    }\n\n    .ant-col {\n      width: 100% !important;\n      margin-bottom: 16px;\n    }\n  }\n}\n", {"version": 3, "sources": ["ExamPaperPreview.vue"], "names": [], "mappings": ";AAip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file": "ExamPaperPreview.vue", "sourceRoot": "src/views/examSystem/components", "sourcesContent": ["<template>\n  <div v-if=\"visible\" class=\"exam-paper-preview-overlay\">\n    <div class=\"exam-paper-preview-container\">\n      <!-- 左侧答题卡 -->\n      <div class=\"preview-answer-card\">\n        <div class=\"preview-card-title\">答题卡-预览界面</div>\n\n        <!-- 考试信息 -->\n        <div class=\"preview-exam-info\" v-if=\"paperData\">\n          <div class=\"preview-exam-name\">{{ paperData.paperTitle }}</div>\n          <div class=\"preview-exam-detail\">\n            <div class=\"info-item\">\n              <span class=\"label\">考试时长：</span>\n              <span class=\"value\">{{ paperData.examDuration }}分钟</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">考试用时：</span>\n              <span class=\"value\">{{ formatDurationFromSeconds(paperData.examDurationSeconds) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">试卷得分：</span>\n              <span class=\"value score\" :class=\"{ 'passed': paperData.score >= 60, 'failed': paperData.score < 60 }\">\n                {{ paperData.score }}分\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 题目导航区域 -->\n        <div class=\"preview-question-section-container\" v-if=\"paperData\">\n          <!-- 单选题区域 -->\n          <div class=\"preview-question-section\" v-if=\"paperData.singleChoiceQuestions && paperData.singleChoiceQuestions.length > 0\">\n            <div class=\"preview-section-header\">一、单选题（每题 {{ getSingleChoiceScore() }} 分，共 {{ paperData.singleChoiceQuestions.length * getSingleChoiceScore() }} 分）</div>\n            <div class=\"preview-question-numbers\">\n              <div\n                v-for=\"(question, index) in paperData.singleChoiceQuestions\"\n                :key=\"'sc-'+index\"\n                :class=\"[\n                  'preview-question-number',\n                  getQuestionStatusClass(question),\n                  {'selected': currentSelectedQuestion === 'single-'+index}\n                ]\"\n                @click=\"selectQuestion('single-'+index)\"\n              >\n                {{ index + 1 }}\n              </div>\n            </div>\n          </div>\n\n          <!-- 判断题区域 -->\n          <div class=\"preview-question-section\" v-if=\"paperData.judgmentQuestions && paperData.judgmentQuestions.length > 0\">\n            <div class=\"preview-section-header\">二、判断题（每题 {{ getJudgmentScore() }} 分，共 {{ paperData.judgmentQuestions.length * getJudgmentScore() }} 分）</div>\n            <div class=\"preview-question-numbers\">\n              <div\n                v-for=\"(question, index) in paperData.judgmentQuestions\"\n                :key=\"'jg-'+index\"\n                :class=\"[\n                  'preview-question-number',\n                  getQuestionStatusClass(question),\n                  {'selected': currentSelectedQuestion === 'judgment-'+index}\n                ]\"\n                @click=\"selectQuestion('judgment-'+index)\"\n              >\n                {{ index + 1 }}\n              </div>\n            </div>\n          </div>\n\n          <!-- 编程题区域 -->\n          <div class=\"preview-question-section\" v-if=\"paperData.programmingQuestions && paperData.programmingQuestions.length > 0\">\n            <div class=\"preview-section-header\">三、编程题（每题 {{ getProgrammingScore() }} 分，共 {{ paperData.programmingQuestions.length * getProgrammingScore() }} 分）</div>\n            <div class=\"preview-question-numbers\">\n              <div\n                v-for=\"(question, index) in paperData.programmingQuestions\"\n                :key=\"'prog-'+index\"\n                :class=\"[\n                  'preview-question-number',\n                  getQuestionStatusClass(question),\n                  {'selected': currentSelectedQuestion === 'prog-'+index}\n                ]\"\n                @click=\"selectQuestion('prog-'+index)\"\n              >\n                {{ index + 1 }}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 退出按钮 -->\n        <div class=\"preview-exit-area\">\n          <a-button\n            type=\"primary\"\n            size=\"large\"\n            @click=\"handleExit\"\n            block\n          >\n            退出界面\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 右侧答题页面 -->\n      <div class=\"preview-content-wrapper\">\n        <a-card class=\"preview-question-content-card\" :bordered=\"false\">\n          <!-- 标签页导航 -->\n          <div class=\"preview-exam-tabs\">\n            <div class=\"preview-tab-header\">\n              <div class=\"preview-tab-items\">\n                <div\n                  class=\"preview-tab-item\"\n                  :class=\"{'tab-active': activeName === 'objectiveQuestions'}\"\n                  @click=\"switchToObjectiveQuestions\"\n                >\n                  <a-icon type=\"form\" /> 客观题\n                </div>\n                <div\n                  class=\"preview-tab-item\"\n                  :class=\"{'tab-active': activeName === 'programmingQuestions'}\"\n                  @click=\"switchToProgrammingQuestions\"\n                >\n                  <a-icon type=\"code\" /> 编程题\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 客观题内容 -->\n          <div v-show=\"activeName === 'objectiveQuestions'\" class=\"preview-tab-content\">\n            <div class=\"preview-exam-content\">\n              <!-- 单选题区域 -->\n              <div v-if=\"paperData && paperData.singleChoiceQuestions && paperData.singleChoiceQuestions.length > 0\" class=\"preview-exam-section\">\n                <h3 class=\"preview-section-title\">一、单选题（每题 {{ getSingleChoiceScore() }} 分，共 {{ paperData.singleChoiceQuestions.length * getSingleChoiceScore() }} 分）</h3>\n                <div v-for=\"(question, index) in paperData.singleChoiceQuestions\" :key=\"'single-'+index\" class=\"preview-exam-question\" :id=\"'single-'+index\">\n                  <div class=\"preview-question-title\">\n                    <span v-html=\"(index+1) + '. ' + question.title\"></span>\n                    <!-- 题目状态标签 -->\n                    <span :class=\"['status-tag', getQuestionStatusClass(question)]\">\n                      <a-icon :type=\"getStatusIcon(question)\" />\n                      {{ getStatusText(question) }}\n                    </span>\n                  </div>\n                  <div class=\"preview-question-options\">\n                    <div v-for=\"option in question.options\" :key=\"option.label\"\n                         class=\"preview-question-option\"\n                         :class=\"{\n                           'review-error-option': question.userAnswer === option.label && question.userAnswer !== question.correctAnswer,\n                           'review-correct-option': option.label === question.correctAnswer\n                         }\">\n                      <span class=\"option-label\">{{ option.label }}.</span>\n                      <span class=\"option-content\" v-html=\"option.content\"></span>\n                    </div>\n                  </div>\n                  <div class=\"preview-answer-info\">\n                    <div class=\"answer-row\">\n                      <span class=\"answer-label\">得分：</span>\n                      <span class=\"answer-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                        {{ question.actualScore }} 分\n                      </span>\n                    </div>\n                    <div class=\"answer-row\">\n                      <span class=\"answer-label\">答案：</span>\n                      <span class=\"answer-value correct-answer\">{{ question.correctAnswer }}</span>\n                    </div>\n                    <div class=\"answer-row\" v-if=\"question.analysis\">\n                      <span class=\"answer-label\">解析：</span>\n                      <span class=\"answer-value analysis-content\" v-html=\"question.analysis\"></span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 判断题区域 -->\n              <div v-if=\"paperData && paperData.judgmentQuestions && paperData.judgmentQuestions.length > 0\" class=\"preview-exam-section\">\n                <h3 class=\"preview-section-title\">二、判断题（每题 {{ getJudgmentScore() }} 分，共 {{ paperData.judgmentQuestions.length * getJudgmentScore() }} 分）</h3>\n                <div v-for=\"(question, index) in paperData.judgmentQuestions\" :key=\"'judgment-'+index\" class=\"preview-exam-question\" :id=\"'judgment-'+index\">\n                  <div class=\"preview-question-title\">\n                    <span v-html=\"(index+1) + '. ' + question.title\"></span>\n                    <!-- 题目状态标签 -->\n                    <span :class=\"['status-tag', getQuestionStatusClass(question)]\">\n                      <a-icon :type=\"getStatusIcon(question)\" />\n                      {{ getStatusText(question) }}\n                    </span>\n                  </div>\n                  <div class=\"preview-question-options\">\n                    <div class=\"preview-question-option\"\n                         :class=\"{\n                           'review-error-option': question.userAnswer === 'T' && question.userAnswer !== question.correctAnswer,\n                           'review-correct-option': question.correctAnswer === 'T'\n                         }\">\n                      <span class=\"option-label\">正确</span>\n                    </div>\n                    <div class=\"preview-question-option\"\n                         :class=\"{\n                           'review-error-option': question.userAnswer === 'F' && question.userAnswer !== question.correctAnswer,\n                           'review-correct-option': question.correctAnswer === 'F'\n                         }\">\n                      <span class=\"option-label\">错误</span>\n                    </div>\n                  </div>\n                  <div class=\"preview-answer-info\">\n                    <div class=\"answer-row\">\n                      <span class=\"answer-label\">得分：</span>\n                      <span class=\"answer-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                        {{ question.actualScore }} 分\n                      </span>\n                    </div>\n                    <div class=\"answer-row\">\n                      <span class=\"answer-label\">正确答案：</span>\n                      <span class=\"answer-value correct-answer\">{{ getJudgmentAnswerText(question.correctAnswer) }}</span>\n                    </div>\n                    <div class=\"answer-row\" v-if=\"question.analysis\">\n                      <span class=\"answer-label\">解析：</span>\n                      <span class=\"answer-value analysis-content\" v-html=\"question.analysis\"></span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 编程题内容 -->\n          <div v-show=\"activeName === 'programmingQuestions'\" class=\"preview-tab-content\">\n            <div class=\"preview-exam-content\">\n              <!-- 编程题区域 -->\n              <div v-if=\"paperData && paperData.programmingQuestions && paperData.programmingQuestions.length > 0\" class=\"preview-exam-section\">\n                <h3 class=\"preview-section-title\">三、编程题（每题 {{ getProgrammingScore() }} 分，共 {{ paperData.programmingQuestions.length * getProgrammingScore() }} 分）</h3>\n                <div\n                  v-for=\"(question, index) in paperData.programmingQuestions\"\n                  :key=\"'prog-'+index\"\n                  class=\"preview-exam-question preview-programming-question\"\n                  :id=\"'prog-'+index\"\n                  v-show=\"currentProgrammingQuestion === 'prog-'+index || (currentProgrammingQuestion === null && index === 0)\"\n                >\n                  <!-- 编程题组件 -->\n                  <div class=\"preview-coding-question-wrapper\">\n                    <!-- 题目标题 -->\n                    <div class=\"preview-question-title\">\n                      <div class=\"preview-title-header\">\n                        <div class=\"preview-title-left\">\n                          <span class=\"preview-question-number\">{{ index + 1 }}</span>\n                          <h3>{{ question.title }}</h3>\n                        </div>\n\n                      </div>\n                    </div>\n\n                    <!-- 编程题内容布局 -->\n                    <div class=\"preview-coding-question-container\">\n                      <a-row :gutter=\"20\">\n                        <!-- 题目描述区域 -->\n                        <a-col :md=\"10\" :sm=\"24\">\n                          <div class=\"preview-problem-description\">\n                            <!-- 题目描述 -->\n                            <div v-if=\"question.description\" class=\"preview-problem-section\">\n                              <div v-html=\"markdownToHtml(question.description)\"></div>\n                            </div>\n\n                            <!-- 时间和内存限制 -->\n                            <div v-if=\"question.timeLimit || question.memoryLimit\" class=\"preview-problem-section\">\n                              <p><strong>限制条件：</strong></p>\n                              <div class=\"preview-limit-info\">\n                                <div v-if=\"question.timeLimit\" class=\"preview-limit-item\">\n                                  <a-icon type=\"clock-circle\" /> 时间限制：{{ question.timeLimit }}ms\n                                </div>\n                                <div v-if=\"question.memoryLimit\" class=\"preview-limit-item\">\n                                  <a-icon type=\"database\" /> 内存限制：{{ question.memoryLimit }}KB\n                                </div>\n                              </div>\n                            </div>\n\n                            <a-divider v-if=\"question.inputFormat || question.outputFormat || (question.samples && question.samples.length > 0)\" />\n\n                            <!-- 输入格式 -->\n                            <div v-if=\"question.inputFormat\" class=\"preview-problem-section\">\n                              <p><strong>输入格式：</strong></p>\n                              <div v-html=\"markdownToHtml(question.inputFormat)\"></div>\n                            </div>\n\n                            <a-divider v-if=\"question.outputFormat || (question.samples && question.samples.length > 0)\" />\n\n                            <!-- 输出格式 -->\n                            <div v-if=\"question.outputFormat\" class=\"preview-problem-section\">\n                              <p><strong>输出格式：</strong></p>\n                              <div v-html=\"markdownToHtml(question.outputFormat)\"></div>\n                            </div>\n\n                            <a-divider v-if=\"question.samples && question.samples.length > 0\" />\n\n                            <!-- 样例 -->\n                            <div v-for=\"(sample, sampleIndex) in question.samples\" :key=\"sampleIndex\" v-if=\"question.samples && question.samples.length > 0\" class=\"preview-problem-section\">\n                              <p><strong>样例 {{ sampleIndex + 1 }}：</strong></p>\n                              <div class=\"preview-sample-container\">\n                                <div class=\"preview-sample-input\">\n                                  <div class=\"preview-sample-header\">输入：</div>\n                                  <pre>{{ sample.input }}</pre>\n                                </div>\n                                <div class=\"preview-sample-output\">\n                                  <div class=\"preview-sample-header\">输出：</div>\n                                  <pre>{{ sample.output }}</pre>\n                                </div>\n                              </div>\n                            </div>\n\n                            <!-- 提示 -->\n                            <div v-if=\"question.hint\" class=\"preview-problem-section\">\n                              <a-divider />\n                              <p><strong>提示：</strong></p>\n                              <div v-html=\"markdownToHtml(question.hint)\"></div>\n                            </div>\n\n                            <!-- 题目解析 -->\n                            <div v-if=\"question.analysis\" class=\"preview-problem-section\">\n                              <a-divider />\n                              <p><strong>解析：</strong></p>\n                              <div v-html=\"markdownToHtml(question.analysis)\" class=\"analysis-content\"></div>\n                            </div>\n                          </div>\n                        </a-col>\n\n                        <!-- 代码展示区域 -->\n                        <a-col :md=\"14\" :sm=\"24\">\n                          <div class=\"preview-code-section\">\n                            <div class=\"preview-code-header\">\n                              <span class=\"preview-code-title\">提交的代码</span>\n                              <span class=\"preview-code-language\">{{ question.language }}</span>\n                            </div>\n                            <div class=\"preview-code-content\">\n                              <pre class=\"preview-code-display\">{{ question.userCode || '未提交代码' }}</pre>\n                            </div>\n                            <div class=\"preview-code-result\">\n                              <div class=\"result-row\">\n                                <span class=\"result-label\">提交状态：</span>\n                                <span class=\"result-value\" :class=\"{ 'submitted': question.submitted, 'not-submitted': !question.submitted }\">\n                                  {{ question.submitted ? '已提交' : '未提交' }}\n                                </span>\n                              </div>\n                              <div class=\"result-row\">\n                                <span class=\"result-label\">判题结果：</span>\n                                <span class=\"result-value\" :class=\"{ 'correct': question.isCorrect, 'incorrect': !question.isCorrect && question.submitted }\">\n                                  {{ getJudgeResultText(question) }}\n                                </span>\n                              </div>\n                              <div class=\"result-row\">\n                                <span class=\"result-label\">得分：</span>\n                                <span class=\"result-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                                  {{ question.actualScore }} 分\n                                </span>\n                              </div>\n                            </div>\n                          </div>\n                        </a-col>\n                      </a-row>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </a-card>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ExamPaperPreview',\n  props: {\n    value: {\n      type: Boolean,\n      default: false\n    },\n    paperData: {\n      type: Object,\n      default: null\n    }\n  },\n  data() {\n    return {\n      activeName: 'objectiveQuestions',\n      currentSelectedQuestion: null,\n      currentProgrammingQuestion: null\n    }\n  },\n  computed: {\n    visible() {\n      return this.value\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.initPreview()\n        // 禁用body滚动\n        document.body.style.overflow = 'hidden'\n      } else {\n        // 恢复body滚动\n        document.body.style.overflow = ''\n      }\n    },\n    paperData: {\n      handler(newVal) {\n        if (newVal) {\n          this.initPreview()\n        }\n      },\n      immediate: true\n    }\n  },\n  beforeDestroy() {\n    // 组件销毁时恢复body滚动\n    document.body.style.overflow = ''\n  },\n  methods: {\n    initPreview() {\n      // 初始化预览状态\n      this.activeName = 'objectiveQuestions'\n      this.currentSelectedQuestion = null\n      this.currentProgrammingQuestion = null\n      \n      // 如果有客观题，默认选择第一题\n      if (this.paperData) {\n        if (this.paperData.singleChoiceQuestions && this.paperData.singleChoiceQuestions.length > 0) {\n          this.currentSelectedQuestion = 'single-0'\n        } else if (this.paperData.judgmentQuestions && this.paperData.judgmentQuestions.length > 0) {\n          this.currentSelectedQuestion = 'judgment-0'\n        }\n        \n        // 如果有编程题，设置默认编程题\n        if (this.paperData.programmingQuestions && this.paperData.programmingQuestions.length > 0) {\n          this.currentProgrammingQuestion = 'prog-0'\n        }\n      }\n    },\n\n    // 退出预览\n    handleExit() {\n      this.$emit('input', false)\n    },\n\n    // 切换到客观题\n    switchToObjectiveQuestions() {\n      this.activeName = 'objectiveQuestions'\n      // 自动选择第一个客观题\n      if (this.paperData) {\n        if (this.paperData.singleChoiceQuestions && this.paperData.singleChoiceQuestions.length > 0) {\n          this.selectQuestion('single-0')\n        } else if (this.paperData.judgmentQuestions && this.paperData.judgmentQuestions.length > 0) {\n          this.selectQuestion('judgment-0')\n        }\n      }\n    },\n\n    // 切换到编程题\n    switchToProgrammingQuestions() {\n      this.activeName = 'programmingQuestions'\n      if (this.paperData && this.paperData.programmingQuestions && this.paperData.programmingQuestions.length > 0) {\n        this.currentProgrammingQuestion = 'prog-0'\n        this.currentSelectedQuestion = 'prog-0'\n      }\n    },\n\n    // 选择题目\n    selectQuestion(questionKey) {\n      this.currentSelectedQuestion = questionKey\n\n      // 如果是编程题，同时设置当前编程题\n      if (questionKey.startsWith('prog-')) {\n        this.currentProgrammingQuestion = questionKey\n        this.activeName = 'programmingQuestions'\n      } else {\n        this.activeName = 'objectiveQuestions'\n      }\n\n      // 滚动到对应题目\n      this.$nextTick(() => {\n        const element = document.getElementById(questionKey)\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth', block: 'start' })\n        }\n      })\n    },\n\n\n\n    // 获取题目状态样式类\n    getQuestionStatusClass(question) {\n      if (question.userAnswer || question.userCode) {\n        // 有答案\n        if (question.isCorrect) {\n          return 'correct' // 正确 - 绿色\n        } else {\n          return 'incorrect' // 错误 - 红色\n        }\n      } else {\n        return 'unanswered' // 未作答 - 灰色\n      }\n    },\n\n    // 获取选项样式类\n    getOptionClass(optionLabel, question) {\n      const classes = []\n\n      // 用户选择的答案\n      if (question.userAnswer === optionLabel) {\n        classes.push('user-selected')\n        if (question.isCorrect) {\n          classes.push('correct-selected')\n        } else {\n          classes.push('incorrect-selected')\n        }\n      }\n\n      // 正确答案\n      if (question.correctAnswer === optionLabel) {\n        classes.push('correct-answer')\n      }\n\n      return classes\n    },\n\n    // 获取判断题选项样式类\n    getJudgmentOptionClass(optionValue, question) {\n      const classes = []\n\n      // 用户选择的答案\n      if (question.userAnswer === optionValue) {\n        classes.push('user-selected')\n        if (question.isCorrect) {\n          classes.push('correct-selected')\n        } else {\n          classes.push('incorrect-selected')\n        }\n      }\n\n      // 正确答案\n      if (question.correctAnswer === optionValue) {\n        classes.push('correct-answer')\n      }\n\n      return classes\n    },\n\n    // 获取判断题答案文本\n    getJudgmentAnswerText(answer) {\n      if (answer === 'T') return '正确'\n      if (answer === 'F') return '错误'\n      return '未作答'\n    },\n\n    // 获取判题结果文本\n    getJudgeResultText(question) {\n      if (!question.submitted) {\n        return '未提交'\n      }\n      return question.isCorrect ? '通过' : '未通过'\n    },\n\n    // 获取单选题分数\n    getSingleChoiceScore() {\n      if (this.paperData && this.paperData.singleChoiceQuestions && this.paperData.singleChoiceQuestions.length > 0) {\n        return this.paperData.singleChoiceQuestions[0].score || 2\n      }\n      return 2\n    },\n\n    // 获取判断题分数\n    getJudgmentScore() {\n      if (this.paperData && this.paperData.judgmentQuestions && this.paperData.judgmentQuestions.length > 0) {\n        return this.paperData.judgmentQuestions[0].score || 2\n      }\n      return 2\n    },\n\n    // 获取编程题分数\n    getProgrammingScore() {\n      if (this.paperData && this.paperData.programmingQuestions && this.paperData.programmingQuestions.length > 0) {\n        return this.paperData.programmingQuestions[0].score || 25\n      }\n      return 25\n    },\n\n    // 格式化时长（精确显示）- 从分钟数转换\n    formatDuration(durationMinutes) {\n      if (!durationMinutes) return '--'\n\n      // 将分钟数转换为秒数\n      const durationSeconds = durationMinutes * 60\n\n      const hours = Math.floor(durationSeconds / 3600)\n      const minutes = Math.floor((durationSeconds % 3600) / 60)\n      const seconds = durationSeconds % 60\n\n      if (hours > 0) {\n        // 超过1小时：显示 X时X分X秒\n        return `${hours}时${minutes}分${seconds}秒`\n      } else if (minutes > 0) {\n        // 1小时内但超过1分钟：显示 X分X秒\n        return `${minutes}分${seconds}秒`\n      } else {\n        // 1分钟内：只显示 X秒\n        return `${seconds}秒`\n      }\n    },\n\n    // 格式化时长（精确显示）- 直接从秒数处理\n    formatDurationFromSeconds(durationSeconds) {\n      if (!durationSeconds) return '--'\n\n      const hours = Math.floor(durationSeconds / 3600)\n      const minutes = Math.floor((durationSeconds % 3600) / 60)\n      const seconds = durationSeconds % 60\n\n      if (hours > 0) {\n        // 超过1小时：显示 X时X分X秒\n        return `${hours}时${minutes}分${seconds}秒`\n      } else if (minutes > 0) {\n        // 1小时内但超过1分钟：显示 X分X秒\n        return `${minutes}分${seconds}秒`\n      } else {\n        // 1分钟内：只显示 X秒\n        return `${seconds}秒`\n      }\n    },\n\n    // Markdown转HTML（简单实现）\n    markdownToHtml(markdown) {\n      if (!markdown) return ''\n\n      // 简单的Markdown转换\n      return markdown\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n        .replace(/`(.*?)`/g, '<code>$1</code>')\n        .replace(/\\n/g, '<br>')\n    },\n\n    // 获取状态图标\n    getStatusIcon(question) {\n      if (!question.userAnswer) {\n        return 'minus-circle'\n      }\n      return question.isCorrect ? 'check-circle' : 'close-circle'\n    },\n\n    // 获取状态文本\n    getStatusText(question) {\n      if (!question.userAnswer) {\n        return '未完成'\n      }\n      return question.isCorrect ? '正确' : '错误'\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.exam-paper-preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.exam-paper-preview-container {\n  width: 100vw;\n  height: 100vh;\n  background-color: #f0f2f5;\n  display: flex;\n  overflow: hidden;\n}\n\n/* 左侧答题卡样式 */\n.preview-answer-card {\n  width: 300px;\n  background-color: #fff;\n  border-right: 1px solid #e8e8e8;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.preview-card-title {\n  padding: 16px;\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n  border-bottom: 1px solid #e8e8e8;\n  background-color: #fafafa;\n}\n\n.preview-exam-info {\n  padding: 16px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.preview-exam-name {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 12px;\n  color: #1890ff;\n  text-align: center;\n}\n\n.preview-exam-detail {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 8px;\n\n    .label {\n      color: #666;\n    }\n\n    .value {\n      font-weight: bold;\n\n      &.score {\n        &.passed {\n          color: #52c41a;\n        }\n\n        &.failed {\n          color: #f5222d;\n        }\n      }\n    }\n  }\n}\n\n.preview-question-section-container {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n}\n\n.preview-question-section {\n  margin-bottom: 20px;\n}\n\n.preview-section-header {\n  font-size: 14px;\n  font-weight: bold;\n  margin-bottom: 12px;\n  color: #333;\n}\n\n.preview-question-numbers {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.preview-question-number {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #1890ff;\n  }\n\n  &.selected {\n    border-color: #1890ff;\n    background-color: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.correct {\n    background-color: #f6ffed;\n    border-color: #52c41a;\n    color: #52c41a;\n\n    &.selected {\n      background-color: #52c41a;\n      color: #fff;\n    }\n  }\n\n  &.incorrect {\n    background-color: #fff2f0;\n    border-color: #f5222d;\n    color: #f5222d;\n\n    &.selected {\n      background-color: #f5222d;\n      color: #fff;\n    }\n  }\n\n  &.unanswered {\n    background-color: #fafafa;\n    border-color: #d9d9d9;\n    color: #999;\n\n    &.selected {\n      background-color: #1890ff;\n      border-color: #1890ff;\n      color: #fff;\n    }\n  }\n}\n\n.preview-exit-area {\n  padding: 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n/* 右侧内容区域样式 */\n.preview-content-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.preview-question-content-card {\n  flex: 1;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n\n  /deep/ .ant-card-body {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    padding: 0;\n  }\n}\n\n.preview-exam-tabs {\n  border-bottom: 1px solid #e8e8e8;\n  background-color: #fafafa;\n}\n\n.preview-tab-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 24px;\n}\n\n.preview-tab-items {\n  display: flex;\n}\n\n.preview-tab-item {\n  padding: 16px 20px;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n  }\n\n  &.tab-active {\n    color: #1890ff;\n    border-bottom-color: #1890ff;\n  }\n}\n\n.preview-tab-content {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.preview-exam-content {\n  padding: 24px;\n}\n\n.preview-exam-section {\n  margin-bottom: 32px;\n}\n\n.preview-section-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 20px;\n  color: #333;\n  border-bottom: 1px solid #e8e8e8;\n  padding-bottom: 8px;\n}\n\n.preview-exam-question {\n  margin-bottom: 24px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 6px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.preview-question-title {\n  font-size: 14px;\n  font-weight: bold;\n  margin-bottom: 16px;\n  line-height: 1.6;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n// 题目状态标签样式 - 参考QuestionDisplay.vue查阅模式样式\n.status-tag {\n  display: inline-block;\n  vertical-align: baseline;\n  margin-left: 10px;\n  padding: 0 6px;\n  font-size: inherit;\n  font-weight: 600;\n  line-height: inherit;\n\n  .anticon {\n    margin-right: 4px;\n    font-size: 0.9em;\n  }\n\n  // 正确状态样式\n  &.correct {\n    color: #52c41a;\n  }\n\n  // 错误状态样式\n  &.incorrect {\n    color: #ff4d4f;\n  }\n\n  // 未完成状态样式\n  &.unanswered {\n    color: #1890ff;\n  }\n}\n\n.preview-question-options {\n  margin-bottom: 16px;\n}\n\n.preview-question-option {\n  margin-bottom: 8px;\n  display: flex;\n  align-items: flex-start;\n  padding: 8px 12px;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  transition: all 0.3s;\n  border: 1px solid transparent;\n\n  &:hover {\n    background-color: #f0f7ff;\n  }\n\n  .option-label {\n    margin-right: 8px;\n    font-weight: bold;\n    min-width: 20px;\n  }\n\n  .option-content {\n    flex: 1;\n    line-height: 1.5;\n  }\n\n  // 用户错误选择的选项\n  &.review-error-option {\n    background-color: #fff1f0 !important;\n    border-color: #ffa39e !important;\n\n    .option-label, .option-content {\n      color: #f5222d !important;\n    }\n  }\n\n  // 正确答案选项\n  &.review-correct-option {\n    background-color: #f6ffed !important;\n    border-color: #b7eb8f !important;\n\n    .option-label, .option-content {\n      color: #52c41a !important;\n    }\n  }\n}\n\n.preview-answer-info {\n  background-color: #fafafa;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #1890ff;\n}\n\n.answer-row {\n  display: flex;\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.answer-label {\n  min-width: 50px;\n  color: #666;\n  font-weight: bold;\n  margin-right: 4px;\n}\n\n.answer-value {\n  flex: 1;\n\n  &.user-answer {\n    &.correct {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.incorrect {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n\n  &.correct-answer {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.score-value {\n    &.full-score {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.zero-score {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n\n  &.analysis-content {\n    color: #666;\n    line-height: 1.6;\n    font-style: italic;\n  }\n}\n\n/* 编程题特殊样式 */\n.preview-programming-question {\n  .preview-title-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n  }\n\n  .preview-title-left {\n    display: flex;\n    align-items: center;\n\n    .preview-question-number {\n      background-color: #1890ff;\n      color: #fff;\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      margin-right: 12px;\n    }\n\n    h3 {\n      margin: 0;\n      font-size: 16px;\n    }\n  }\n\n\n}\n\n.preview-coding-question-container {\n  margin-top: 12px;\n\n  // 为左侧题目描述区域添加固定高度和滚动条\n  .ant-col-md-10 {\n    max-height: 700px;\n    overflow-y: auto;\n    padding-right: 10px;\n    border-right: 1px solid #f0f0f0;\n  }\n\n  .preview-problem-section {\n    margin-bottom: 12px;\n\n    strong {\n      font-size: 16px;\n      color: #262626;\n    }\n  }\n\n  .preview-limit-info {\n    display: flex;\n    gap: 16px;\n    margin-top: 8px;\n\n    .preview-limit-item {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      color: #666;\n      font-size: 14px;\n\n      .anticon {\n        color: #1890ff;\n      }\n    }\n  }\n\n  .preview-sample-container {\n    display: flex;\n    gap: 16px;\n    margin-top: 8px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .preview-sample-input, .preview-sample-output {\n      flex: 1;\n      background: #f8f9fa;\n      border-radius: 8px;\n      overflow: hidden;\n\n      .preview-sample-header {\n        padding: 8px 12px;\n        background: #e6f7ff;\n        color: #1890ff;\n        font-weight: 600;\n      }\n\n      pre {\n        margin: 0;\n        padding: 12px;\n        background: transparent;\n        border-radius: 0;\n      }\n    }\n  }\n\n  .preview-problem-section {\n    margin-bottom: 16px;\n\n    p {\n      margin-bottom: 8px;\n      font-weight: bold;\n    }\n  }\n\n  .preview-limit-info {\n    display: flex;\n    gap: 16px;\n  }\n\n  .preview-limit-item {\n    display: flex;\n    align-items: center;\n    gap: 4px;\n    color: #666;\n    font-size: 12px;\n  }\n\n  .preview-sample-container {\n    background-color: #f8f9fa;\n    border-radius: 4px;\n    overflow: hidden;\n    border: 1px solid #e8e8e8;\n  }\n\n  .preview-sample-input,\n  .preview-sample-output {\n    .preview-sample-header {\n      background-color: #e8e8e8;\n      padding: 8px 12px;\n      font-weight: bold;\n      font-size: 12px;\n      color: #666;\n    }\n\n    pre {\n      margin: 0;\n      padding: 12px;\n      background-color: #fff;\n      border: none;\n      font-family: 'Courier New', monospace;\n      font-size: 12px;\n      line-height: 1.4;\n      white-space: pre-wrap;\n      word-wrap: break-word;\n    }\n  }\n}\n\n.preview-code-section {\n  background-color: #fff;\n  border-radius: 4px;\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.preview-code-header {\n  background-color: #fafafa;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .preview-code-title {\n    font-weight: bold;\n    color: #333;\n  }\n\n  .preview-code-language {\n    background-color: #1890ff;\n    color: #fff;\n    padding: 2px 8px;\n    border-radius: 12px;\n    font-size: 12px;\n  }\n}\n\n.preview-code-content {\n  max-height: 400px;\n  overflow-y: auto;\n\n  .preview-code-display {\n    margin: 0;\n    padding: 16px;\n    background-color: #f8f9fa;\n    border: none;\n    font-family: 'Courier New', monospace;\n    font-size: 18px;\n    line-height: 1.4;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    color: #333;\n  }\n}\n\n.preview-code-result {\n  background-color: #fafafa;\n  padding: 12px 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n.result-row {\n  display: flex;\n  margin-bottom: 4px;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.result-label {\n  min-width: 80px;\n  color: #666;\n  font-weight: bold;\n}\n\n.result-value {\n  flex: 1;\n\n  &.submitted {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.not-submitted {\n    color: #999;\n  }\n\n  &.correct {\n    color: #52c41a;\n    font-weight: bold;\n  }\n\n  &.incorrect {\n    color: #f5222d;\n    font-weight: bold;\n  }\n\n  &.score-value {\n    &.full-score {\n      color: #52c41a;\n      font-weight: bold;\n    }\n\n    &.zero-score {\n      color: #f5222d;\n      font-weight: bold;\n    }\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .preview-answer-card {\n    width: 250px;\n  }\n\n  .preview-exam-content {\n    padding: 16px;\n  }\n\n  .preview-coding-question-container {\n    .ant-row {\n      flex-direction: column;\n    }\n\n    .ant-col {\n      width: 100% !important;\n      margin-bottom: 16px;\n    }\n  }\n}\n</style>\n"]}]}