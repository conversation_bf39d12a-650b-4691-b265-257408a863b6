{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlinePractise.vue?vue&type=style&index=0&id=19f7cb75&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlinePractise.vue", "mtime": 1753195975822}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.practice-container {\n  @media screen and (max-width: 768px) {\n    padding: 8px;\n  }\n\n\n  // 全屏模式样式\n  .full-screen-mode {\n    position: fixed !important;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    width: 100%;\n    height: 100vh;\n    z-index: 1000;\n    margin: 0;\n    overflow-y: auto;\n    background-color: #f0f2f5;\n  }\n\n  // 水平布局样式（全屏模式下的刷题界面）\n  .horizontal-layout {\n    display: flex;\n    flex-direction: column;\n\n    .question-container {\n      width: 100%;\n      height: 100vh;\n      overflow-y: auto;\n      background-color: #ffffff;\n      padding: 16px;\n    }\n  }\n}\n", {"version": 3, "sources": ["onlinePractise.vue"], "names": [], "mappings": ";AAs0DA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "onlinePractise.vue", "sourceRoot": "src/views/examSystem", "sourcesContent": ["<template>\n  <div class=\"practice-container\">\n    <!-- 刷题模式选择 -->\n    <practice-mode-selector\n      v-if=\"!practiseStarted\"\n      :loading=\"loading\"\n      :hasSavedProgress=\"hasSavedProgress\"\n      @start-practise=\"handleStartPractise\"\n      @start-quick-practise=\"handleStartQuickPractise\"\n      @continue-practise=\"handleContinuePractise\"\n      @reset-query=\"handleResetQuery\"\n      ref=\"modeSelector\"\n    />\n\n    <!-- 刷题界面 -->\n    <div v-if=\"practiseStarted\" class=\"practice-area\" :class=\"{'full-screen-mode': isFullScreen, 'horizontal-layout': isFullScreen}\">\n      <!-- 刷题状态区 -->\n      <practice-status-bar\n        :isFullScreen=\"isFullScreen\"\n        :isReviewMode=\"isReviewMode\"\n        :showAnswer=\"showAnswer\"\n        :practiseMode=\"practiseMode\"\n        :practiseCount=\"practiseCount\"\n        :remainingTimeText=\"remainingTimeText\"\n        :questionList=\"questionList\"\n        :currentQuestionIndex=\"currentQuestionIndex\"\n        :answeredQuestions=\"answeredQuestions\"\n        :userAnswersMap=\"userAnswersMap\"\n        :isCollected=\"isCollected\"\n        :collectLoading=\"collectLoading\"\n        @check-answer-correct=\"isAnswerCorrect\"\n        @jump-to-question=\"jumpToQuestion\"\n        @exit-review-mode=\"exitReviewMode\"\n        @toggle-show-answer=\"showAnswer = !showAnswer\"\n        @collect-question=\"collectQuestion\"\n        @exit-practise=\"exitPractise\"\n      />\n\n      <!-- 题目与答题区 -->\n      <a-card class=\"question-container\" :bordered=\"false\">\n        <!-- 客观题显示 -->\n        <question-display\n          v-if=\"currentQuestion.questionType !== 3\"\n          :currentQuestion=\"currentQuestion\"\n          :currentQuestionIndex=\"currentQuestionIndex\"\n          :questionList=\"questionList\"\n          :userAnswer.sync=\"userAnswer\"\n          :userAnswersMap=\"userAnswersMap\"\n          :isReviewMode=\"isReviewMode\"\n          :showAnswer=\"showAnswer\"\n          :currentQuestionStatus=\"currentQuestionStatus\"\n          @prev-question=\"prevQuestion\"\n          @next-question=\"nextQuestion\"\n        />\n\n        <!-- 编程题显示 -->\n        <coding-question\n          v-else\n          :currentQuestion=\"currentQuestion\"\n          :currentQuestionIndex=\"currentQuestionIndex\"\n          :questionList=\"questionList\"\n          :showAnswer=\"showAnswer\"\n          :isFullScreen=\"isFullScreen\"\n          :isReviewMode=\"isReviewMode\"\n          :currentQuestionStatus=\"currentQuestionStatus\"\n          :code.sync=\"code\"\n          :selectedLanguage=\"selectedLanguage\"\n          :supportedLanguages=\"supportedLanguages\"\n          :editorHeight=\"editorHeight\"\n          :editorTheme.sync=\"editorTheme\"\n          :editorFontSize.sync=\"editorFontSize\"\n          :editorTabSize=\"editorTabSize\"\n          :openTestCaseDrawer.sync=\"openTestCaseDrawer\"\n          :testInputMap=\"testInputMap\"\n          :testResultMap=\"testResultMap\"\n          :activeTestCaseIndexMap=\"activeTestCaseIndexMap\"\n          :isSubmitting=\"isSubmitting\"\n          @prev-question=\"prevQuestion\"\n          @next-question=\"nextQuestion\"\n          @language-change=\"handleLanguageChange\"\n          @reset-code=\"resetCode\"\n          @get-last-accepted-code=\"getUserLastAcceptedCode\"\n          @switch-focus-mode=\"switchFocusMode\"\n          @formal-submission=\"handleFormalSubmission\"\n          @update-test-input=\"updateTestInput\"\n          @update-test-result=\"updateTestResult\"\n          @update-active-test-case-index=\"updateActiveTestCaseIndex\"\n          ref=\"codeMirror\"\n        />\n      </a-card>\n\n      <!-- 练习结果弹窗 -->\n      <practice-result-modal\n        v-model=\"practiseCompleted\"\n        :correctCount=\"correctCount\"\n        :incorrectCount=\"incorrectCount\"\n        :unfinishedCount=\"unfinishedCount\"\n        :totalCount=\"questionList.length\"\n        :hasAnsweredQuestions=\"answeredQuestions.length > 0\"\n        @start-new-practise=\"startNewPractise\"\n        @enter-review-mode=\"enterReviewMode\"\n        @close=\"handlePracticeSummaryClose\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getQuestions, getRandomQuestions, getLastAcceptedCode, addWrongRecord, addCollection, deleteCollection, checkCollection } from '@/api/examSystem'\nimport { submitTestJudge, getTestJudgeResult } from '@/api/manage'\nimport { getCodeTemplate } from '@/utils/codeTemplates'\nimport PracticeModeSelector from './components/PracticeModeSelector.vue'\nimport PracticeStatusBar from './components/PracticeStatusBar.vue'\nimport QuestionDisplay from './components/QuestionDisplay.vue'\nimport CodingQuestion from './components/CodingQuestion.vue'\nimport PracticeResultModal from './components/PracticeResultModal.vue'\nimport { AntiMisoperationMixin } from '@/mixins/AntiMisoperationMixin'\n\nexport default {\n  name: 'OnlinePractise',\n  mixins: [AntiMisoperationMixin],\n  components: {\n    PracticeModeSelector,\n    PracticeStatusBar,\n    QuestionDisplay,\n    CodingQuestion,\n    PracticeResultModal\n  },\n  data () {\n    return {\n      // 查询参数\n      queryParam: {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined,\n        difficulty: undefined\n      },\n      // 题库数量\n      questionBankCount: 0,\n      // 刷题设置\n      practiseCount: 10,\n      // 将默认刷题模式设为undefined，初始状态不选择任何模式\n      practiseMode: undefined, // 修改：移除默认值'count'\n      practiseStarted: false, // 是否开始刷题\n      loading: false, // 加载状态\n      form: {}, // 表单引用\n      questionTypes: ['1', '2', '3'], // 默认选择所有题型\n      difficultyRange: [1, 3], // 难度范围\n      timeLimit: 30, // 时间限制(分钟)\n      deadline: 0, // 倒计时截止时间\n      // 添加响应式数据用于显示计时器\n      remainingTimeText: '00:00', // 显示的时间文本\n      difficultyMarks: {\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      },\n      // 全屏模式状态\n      isFullScreen: false,\n      \n      // 题目列表\n      questionList: [],\n      currentQuestionIndex: 1,\n      // 用户答案\n      userAnswer: '',\n      answeredQuestions: [], // 已回答的题目ID列表\n      // 添加用户答案映射，用于存储每道题的答案\n      userAnswersMap: {},\n      // 代码相关\n      selectedLanguage: 'C++',\n      code: '',\n      // 编辑器配置\n      editorHeight: 600,\n      editorTheme: 'solarized',\n      editorFontSize: '14px',\n      editorTabSize: 4,\n      openTestCaseDrawer: false,\n      // 支持的语言列表\n      supportedLanguages: ['C', 'C++', 'Java', 'Python3', 'Python2'],\n      // 结果相关\n      showResult: false,\n      isCorrect: false,\n      showAnswer: false, // 是否显示答案\n      // 练习状态\n      practiseCompleted: false,\n      timer: null, // 倒计时定时器\n      // 添加标志位，表示是否有保存的答题进度\n      hasSavedProgress: false,\n      \n      // 新增：测试数据状态管理 - 按题目ID存储测试输入和结果\n      testInputMap: {},   // 存储各题目的测试输入 {questionId: userInput}\n      testResultMap: {},  // 存储各题目的测试结果 {questionId: testJudgeRes}\n      activeTestCaseIndexMap: {}, // 存储各题目当前激活的测试用例索引 {questionId: activeIndex}\n      \n      // 新增：提交状态管理\n      isSubmitting: false, // 是否正在提交评测\n      \n      // // 新增：查阅答题模式相关状态\n      isReviewMode: false, // 是否处于查阅答题模式\n      firstErrorIndex: 1, // 第一个错误题目的索引\n      // // 新增：正确率显示的颜色配置\n      accuracyStrokeColor: { \n        '0%': '#ff4d4f',\n        '50%': '#faad14',\n        '100%': '#52c41a'\n      },\n      // 新增：标记当前使用的是哪种刷题模式（普通/快速）\n      isQuickMode: false,\n      \n      collectionStatusMap: {}, // 新增：用于存储题目的收藏状态 { questionId: collectionId }\n      collectLoading: false, // 收藏操作的加载状态\n    }\n  },\n  // 添加watch选项，监听userAnswer变化\n  watch: {\n    userAnswer(newVal) {\n      // 只有当新值不为空时才处理（即用户做出了选择）\n      if (newVal && newVal !== '') {\n        // 获取当前题目ID\n        const currentQuestionId = this.currentQuestion.id;\n        \n        // 检查题目是否已经回答过（修改已答题时不自动切换）\n        const alreadyAnswered = this.answeredQuestions.includes(currentQuestionId);\n        \n        // 将当前题目ID添加到已回答列表(如果还未添加)\n        if (!alreadyAnswered) {\n          this.answeredQuestions.push(currentQuestionId);\n          \n          // 保存用户答案\n          this.userAnswersMap[currentQuestionId] = newVal;\n          \n          // 如果不是最后一题，则自动切换到下一题\n          if (this.currentQuestionIndex < this.questionList.length) {\n            setTimeout(() => {\n              this.nextQuestion();\n            }, 500);\n          }\n        }\n      }\n    }\n  },\n  computed: {\n    currentQuestion() {\n      if (!this.questionList.length) return {}\n      return this.questionList[this.currentQuestionIndex - 1] || {}\n    },\n    // 判断当前题目是否被收藏\n    isCollected() {\n      return this.currentQuestion && !!this.collectionStatusMap[this.currentQuestion.id];\n    },\n    // 计算正确题数\n    correctCount() {\n      return this.questionList.filter(q => \n        this.answeredQuestions.includes(q.id) && \n        this.isAnswerCorrect(q)\n      ).length;\n    },\n    // 计算错误题数\n    incorrectCount() {\n      return this.answeredQuestions.length - this.correctCount;\n    },\n    // // 新增：计算未完成题目数量\n    unfinishedCount() {\n      return this.questionList.length - this.answeredQuestions.length;\n    },\n    // // 新增：计算正确率百分比\n    accuracyPercent() {\n      return this.questionList.length > 0 \n        ? Math.round((this.correctCount / this.questionList.length) * 100) \n        : 0;\n    },\n    // 获取当前题目的测试用例，供CodeMirror组件使用\n    getProblemTestCases() {\n      if (!this.currentQuestion || !this.currentQuestion.content || !this.currentQuestion.content.sample_cases) {\n        return [];\n      }\n      \n      return this.currentQuestion.content.sample_cases.map((sample, index) => {\n        return {\n          input: sample.input,\n          output: sample.output,\n          active: false\n        }\n      });\n    },\n    // 添加计算属性：判断当前题目是否为已答题目\n    isCurrentQuestionAnswered() {\n      return this.currentQuestion && this.answeredQuestions.includes(this.currentQuestion.id);\n    },\n    // 添加计算属性：判断是否为最后一题\n    isLastQuestion() {\n      return this.currentQuestionIndex === this.questionList.length;\n    },\n    // // 新增：计算当前题目的状态（用于查阅答题模式）\n    currentQuestionStatus() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return 'unfinished';\n      \n      if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n        return 'unfinished'; // 未完成\n      }\n      \n      return this.isAnswerCorrect(this.currentQuestion) ? 'correct' : 'incorrect';\n    }\n  },\n  created() {\n    // 添加ESC键监听器\n    document.addEventListener('keydown', this.handleKeyDown);\n\n    // 检查是否有保存的答题进度\n    this.checkSavedProgress();\n  },\n  mounted() {\n    // 初始化定时器（仅在首次挂载时创建，activated会处理恢复）\n    if (!this.timer) {\n      this.timer = setInterval(() => {\n        // 更新时间显示\n        this.updateRemainingTime();\n      }, 1000);\n    }\n\n    // 添加页面刷新或关闭前的保护（仅在首次挂载时添加，activated会处理恢复）\n    window.addEventListener('beforeunload', this.handleBeforeUnload);\n  },\n  beforeDestroy() {\n    // 组件销毁前清除计时器\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n\n    // 移除ESC键监听器\n    document.removeEventListener('keydown', this.handleKeyDown);\n\n    // 移除页面刷新或关闭前的保护\n    window.removeEventListener('beforeunload', this.handleBeforeUnload);\n\n    // 确保退出时移除对body的控制\n    document.body.style.overflow = '';\n  },\n\n  // ✅ 组件激活时（从keep-alive缓存中恢复）\n  activated() {\n    console.log('在线刷题组件被激活，当前状态：', {\n      practiseStarted: this.practiseStarted,\n      currentQuestionIndex: this.currentQuestionIndex,\n      questionListLength: this.questionList.length\n    })\n\n    // 如果正在刷题且是限时模式，恢复定时器\n    if (this.practiseStarted && this.practiseMode === 'time' && this.deadline > 0) {\n      // 重新计算剩余时间（考虑页面切换期间经过的时间）\n      const now = Date.now()\n      const remaining = Math.max(0, this.deadline - now)\n\n      if (remaining > 0) {\n        // 更新剩余时间并重启定时器\n        this.updateRemainingTime()\n        if (!this.timer) {\n          this.timer = setInterval(() => {\n            this.updateRemainingTime()\n          }, 1000)\n        }\n      } else {\n        // 时间已到，触发时间到处理\n        this.handleTimeUp()\n      }\n    }\n\n    // 重新添加键盘事件监听（如果正在刷题）\n    if (this.practiseStarted) {\n      document.addEventListener('keydown', this.handleKeyDown)\n    }\n\n    // 重新添加页面刷新保护（如果正在刷题）\n    if (this.practiseStarted) {\n      window.addEventListener('beforeunload', this.handleBeforeUnload)\n    }\n  },\n\n  // ✅ 组件失活时（被keep-alive缓存）\n  deactivated() {\n    console.log('在线刷题组件被缓存，保存状态')\n\n    // 暂停定时器（避免后台继续计时）\n    if (this.timer) {\n      clearInterval(this.timer)\n      this.timer = null\n    }\n\n    // 移除键盘事件监听（避免影响其他页面）\n    document.removeEventListener('keydown', this.handleKeyDown)\n\n    // 移除页面刷新保护（其他页面不需要这个保护）\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 自动保存进度（如果正在刷题）\n    if (this.practiseStarted && this.questionList.length > 0) {\n      this.saveProgress()\n    }\n\n    // 确保退出时移除对body的控制\n    document.body.style.overflow = ''\n  },\n  methods: {\n    // 处理模式选择器事件\n    handleStartPractise(formData) {\n      this.updateFormData(formData)\n      this.startPractise()\n    },\n\n    handleStartQuickPractise(formData) {\n      this.updateFormData(formData)\n      this.startQuickPractise()\n    },\n\n    handleContinuePractise() {\n      this.continuePractise()\n    },\n\n    handleResetQuery() {\n      this.resetQuery()\n      if (this.$refs.modeSelector) {\n        this.$refs.modeSelector.resetFormData()\n      }\n    },\n\n    // 更新表单数据\n    updateFormData(formData) {\n      this.queryParam = { ...formData.queryParam }\n      this.practiseCount = formData.practiseCount\n      this.practiseMode = formData.practiseMode\n      this.questionTypes = [...formData.questionTypes]\n      this.difficultyRange = [...formData.difficultyRange]\n      this.timeLimit = formData.timeLimit\n    },\n\n    // 处理键盘事件\n    handleKeyDown(event) {\n      // 按下ESC键退出全屏\n      if (event.key === 'Escape' && this.isFullScreen) {\n        this.isFullScreen = false;\n        \n        // 通知编辑器组件专注模式已退出\n        // 注意：在这里使用$refs访问子组件\n        if (this.$refs.codeMirror) {\n          // 不直接修改子组件属性，而是通过正确的方式触发切换\n          this.$refs.codeMirror.switchFocusMode(false);\n        }\n        \n        // 恢复body滚动条\n        document.body.style.overflow = '';\n        \n        this.$message.success('已退出专注模式');\n      }\n    },\n    \n    // 切换全屏模式\n    toggleFullScreen() {\n      this.isFullScreen = !this.isFullScreen;\n      \n      // 控制body的滚动条\n      if (this.isFullScreen) {\n        document.body.style.overflow = 'hidden';\n      } else {\n        document.body.style.overflow = '';\n      }\n    },\n    \n    // 开始刷题\n    startPractise() {\n      // 参数验证\n      if (!this.queryParam.subject) {\n        this.$message.warning('请选择科目');\n        return;\n      }\n      \n      // 添加级别验证，使级别成为必选参数\n      if (!this.queryParam.level) {\n        this.$message.warning('请选择级别');\n        return;\n      }\n      \n      if (this.questionTypes.length === 0) {\n        this.$message.warning('请至少选择一种题型');\n        return;\n      }\n      \n      // 验证刷题模式是否已选择\n      if (!this.practiseMode) {\n        this.$message.warning('请选择刷题模式');\n        return;\n      }\n      \n      // 检查是否有保存的进度\n      if (this.hasSavedProgress) {\n        this.$confirm({\n          title: '有未完成的答题进度',\n          content: '开始新的刷题将放弃上次的答题进度，是否继续？',\n          okText: '继续',\n          cancelText: '取消',\n          onOk: () => {\n            // 清除已保存的进度\n            this.clearSavedProgress();\n            // 开始新的刷题\n            this.doStartPractise();\n          }\n        });\n      } else {\n        // 没有保存的进度，直接开始新的刷题\n        this.doStartPractise();\n      }\n    },\n    \n    // 实际执行开始刷题的操作\n    doStartPractise() {\n      this.loading = true;\n      // 标记为普通刷题模式\n      this.isQuickMode = false;\n      \n      // 转换参数格式\n      const params = { \n        ...this.queryParam,\n        count: this.practiseCount,\n        questionType: this.questionTypes.length === 1 ? Number(this.questionTypes[0]) : undefined,\n        difficultyStart: this.difficultyRange[0],\n        difficultyEnd: this.difficultyRange[1]\n      };\n      \n      getQuestions(params).then(res => {\n        if (res.success) {\n          // 添加数据验证确保题目数据完整\n          this.questionList = res.result || [];\n          \n          // 验证题目数据的完整性\n          if (this.questionList.length > 0) {\n            // 检查和处理题目数据\n            this.questionList = this.questionList.map(question => {\n              // 处理content字段\n              try {\n                // 检查content是否为字符串(JSON)，如果是则解析为对象\n                if (typeof question.content === 'string' && question.content) {\n                  try {\n                    question.content = JSON.parse(question.content);\n                  } catch (jsonError) {\n                    console.error('解析题目content JSON失败:', jsonError);\n                    // 解析失败，设置为空对象避免后续错误\n                    question.content = {};\n                  }\n                } else if (!question.content) {\n                  // 如果content不存在或为null，初始化为空对象\n                  question.content = {};\n                }\n                \n                // 针对不同题型进行数据补充和验证\n                if (question.questionType === 1) { // 单选题\n                  // 确保选项数组存在\n                  if (!question.content.options || !Array.isArray(question.content.options)) {\n                    question.content.options = [];\n                  }\n                  // 确保答案存在\n                  if (!question.content.answer) {\n                    question.content.answer = '';\n                  }\n                } else if (question.questionType === 2) { // 判断题\n                  // 确保答案格式正确\n                  if (!['true', 'false', '正确', '错误', 'T', 'F'].includes(question.content.answer)) {\n                    question.content.answer = '';\n                  }\n                } else if (question.questionType === 3) { // 编程题\n                  // 确保描述字段存在\n                  if (!question.content.description) question.content.description = '';\n                  if (!question.content.input_format) question.content.input_format = '';\n                  if (!question.content.output_format) question.content.output_format = '';\n                  \n                  // 确保样例数组存在且格式正确\n                  if (!question.content.sample_cases || !Array.isArray(question.content.sample_cases)) {\n                    question.content.sample_cases = [];\n                  } else {\n                    // 确保每个样例的输入输出都是字符串\n                    question.content.sample_cases = question.content.sample_cases.map(sample => {\n                      return {\n                        input: typeof sample.input === 'string' ? sample.input : String(sample.input || ''),\n                        output: typeof sample.output === 'string' ? sample.output : String(sample.output || '')\n                      };\n                    });\n                  }\n                }\n                \n                // 确保解析字段存在\n                if (!question.content.analysis) {\n                  question.content.analysis = '';\n                }\n              } catch (error) {\n                console.error('处理题目数据时出错:', error, '问题数据:', question);\n                // 发生错误时设置默认content\n                question.content = {\n                  answer: '',\n                  options: [],\n                  description: '题目数据处理失败',\n                  analysis: '',\n                  sample_cases: []\n                };\n              }\n              \n              return question;\n            });\n            \n            // 按题型优先级排序题目：单选题 > 判断题 > 编程题\n            this.questionList = this.sortQuestionsByType(this.questionList);\n          }\n          \n          if (this.questionList.length === 0) {\n            this.$message.warning('没有找到符合条件的题目，请调整筛选条件后重试');\n            this.loading = false;\n            return;\n          }\n          \n          this.currentQuestionIndex = 1;\n          this.resetAnswerState();\n          this.practiseStarted = true;\n          this.answeredQuestions = [];\n          this.userAnswersMap = {};\n\n          // 如果是限时模式，设置倒计时\n          if (this.practiseMode === 'time') {\n            this.deadline = Date.now() + this.timeLimit * 60 * 1000;\n            // 立即更新一次时间显示\n            this.updateRemainingTime();\n          }\n\n          // 启用防误触保护（刷题优先级较低）\n          this.enableAntiMisoperation({\n            confirmMessage: '您正在进行刷题练习，确定要离开吗？离开后当前进度将会保存。',\n            priority: 5, // 刷题优先级较低\n            preventRefresh: true,\n            preventNavigation: true\n          });\n\n          // 显示开始提示\n          this.$notification.success({\n            message: '开始刷题',\n            description: `已准备好${this.questionList.length}道题目，祝您学习愉快！`,\n            duration: 3\n          });\n          \n          // 添加代码：确保DOM更新后，滚动到顶部状态区\n          this.$nextTick(() => {\n            const statusElement = document.querySelector('.practice-status');\n            if (statusElement) {\n              statusElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\n              // 额外添加window.scrollTo确保页面顶部对齐\n              window.scrollTo(0, 0);\n            }\n          });\n          \n          // 检查第一题的收藏状态\n          this.$nextTick(() => {\n            this.checkCurrentQuestionCollection();\n          });\n        } else {\n          this.$message.error(res.message || '获取题目失败');\n        }\n        this.loading = false;\n      }).catch(err => {\n        this.loading = false;\n        console.error('获取题目出错：', err);\n        this.$message.error('获取题目失败，请稍后重试');\n      });\n    },\n    \n\n    \n    // 重置查询条件\n    resetQuery() {\n      // 添加确认对话框\n      this.$confirm({\n        title: '重置全部配置',\n        content: '确定要重置所有刷题配置和已保存的答题进度吗？',\n        okText: '确定',\n        cancelText: '取消',\n        onOk: () => {\n          // 重置所有选项\n          this.queryParam = {\n            subject: undefined,\n            level: undefined,\n            questionType: undefined,\n            difficulty: undefined\n          };\n          this.practiseCount = 10;\n          // 重置刷题模式为undefined，使用户必须重新选择\n          this.practiseMode = undefined;\n          this.questionTypes = ['1', '2', '3'];\n          this.difficultyRange = [1, 3];\n          this.timeLimit = 30;\n          \n          // 清除保存的答题进度\n          this.clearSavedProgress();\n          \n          // 显示重置成功的消息\n          this.$message.success('已重置所有配置和答题进度');\n        }\n      });\n    },\n    \n    // 重置刷题\n    resetPractise() {\n      this.questionList = [];\n      this.currentQuestionIndex = 1;\n      this.practiseCompleted = false;\n      this.practiseStarted = false;\n      this.resetQuery();\n    },\n    \n    // 重置答案状态\n    resetAnswerState() {\n      this.userAnswer = '';\n      this.code = '';\n      this.testInput = '';\n      this.testResult = null;\n      this.showResult = false;\n    },\n    \n    // 获取题目类型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        1: 'blue',    // 单选题\n        2: 'green',   // 判断题\n        3: 'purple'   // 编程题\n      };\n      return colorMap[type] || 'blue';\n    },\n    \n    // 获取题目类型文本\n    getQuestionTypeText(type) {\n      const typeMap = {\n        1: '单选题',\n        2: '判断题',\n        3: '编程题'\n      };\n      return typeMap[type] || '未知题型';\n    },\n    \n    // 获取题目类型图标\n    getQuestionTypeIcon(type) {\n      const iconMap = {\n        1: 'check-square',  // 单选题\n        2: 'check-circle',  // 判断题\n        3: 'code'           // 编程题\n      };\n      return iconMap[type];\n    },\n    \n    // 判断答案是否正确\n    isAnswerCorrect(question) {\n      if (!question) return false;\n      \n      // 获取用户答案\n      const userAns = this.userAnswersMap[question.id];\n      \n      // 获取正确答案，确保答案存在\n      const correctAns = question.content && question.content.answer ? question.content.answer : '';\n      \n      if (question.questionType === 1) { // 单选题\n        return userAns === correctAns;\n      } else if (question.questionType === 2) { // 判断题\n        // 统一转换成T/F格式进行比较\n        let standardUserAns = userAns;\n        if (userAns === 'true') standardUserAns = 'T';\n        if (userAns === 'false') standardUserAns = 'F';\n        \n        // 统一处理判断题答案的多种可能格式\n        const isCorrectAnswerTrue = ['true', '正确', 'T'].includes(correctAns);\n        const isUserAnswerTrue = ['true', '正确', 'T'].includes(standardUserAns);\n        \n        return isCorrectAnswerTrue === isUserAnswerTrue;\n      } else if (question.questionType === 3) { // 编程题\n        // 编程题的正确性需要通过判题服务判断，不能在前端判断\n        // 检查是否已经提交评测并存储了结果\n        return userAns && typeof userAns === 'object' && \n               userAns.submitted === true && userAns.isCorrect === true;\n      }\n      \n      return false;\n    },\n    \n    // 添加：处理代码提交评测\n    handleFormalSubmission(data) {\n      // 防止重复提交 - 如果已经在提交中，直接返回\n      if (this.isSubmitting) {\n        this.$message.warning('正在处理您的提交，请稍候...');\n        return;\n      }\n      \n      // 设置提交状态为true\n      this.isSubmitting = true;\n      \n      // 显示提交中的加载提示\n      this.$message.loading({ content: '正在提交评测...', key: 'submitting' });\n      \n      const params = {\n        ...data,\n        isSubmit: true // 标记为正式提交\n      };\n\n      submitTestJudge(params).then(res => {\n        if (res.success && res.result && res.result.submissionId) {\n          const submissionKey = res.result.submissionId;\n          this.$message.success({ content: '提交成功，开始获取结果...', key: 'submitting', duration: 2 });\n          // isSubmit = true, 表示这是正式提交，需要记录错题、提示等\n          this.pollJudgeResult(submissionKey, 0, true);\n        } else if (res.success && res.result && res.result.submissionId === null) {\n          // 处理提交成功但无法获取判题ID的情况\n          this.$message.warning({ \n            content: '系统已接收您的提交，但由于短时间内提交过于频繁，暂时无法查看结果，请稍后再试', \n            key: 'submitting', \n            duration: 3 \n          });\n        } else {\n          this.$message.error({ content: `提交失败: ${res.message || '未能获取到判题ID'}`, key: 'submitting', duration: 3 });\n        }\n      }).catch(err => {\n        this.$message.error({ content: `提交异常: ${err.message || '网络错误'}`, key: 'submitting', duration: 3 });\n      }).finally(() => {\n        // 1秒后重置提交状态，防止频繁提交\n        setTimeout(() => {\n          this.isSubmitting = false;\n        }, 1000);\n      });\n    },\n    \n    // 新增：更新测试输入\n    updateTestInput(input) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.testInputMap, this.currentQuestion.id, input);\n      }\n    },\n    \n    // 新增：更新测试结果\n    updateTestResult(result) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.testResultMap, this.currentQuestion.id, result);\n      }\n    },\n    \n    // 新增：更新当前激活的测试用例索引\n    updateActiveTestCaseIndex(index) {\n      if (this.currentQuestion && this.currentQuestion.id) {\n        this.$set(this.activeTestCaseIndexMap, this.currentQuestion.id, index);\n      }\n    },\n    \n    // 清空代码\n    resetCode() {\n      // 获取当前语言的模板代码\n      const templateCode = getCodeTemplate(this.selectedLanguage);\n\n      // 如果题目有自定义模板，优先使用题目模板\n      if (this.currentQuestion &&\n          this.currentQuestion.content &&\n          this.currentQuestion.content.template &&\n          this.currentQuestion.content.template[this.selectedLanguage]) {\n        this.code = this.currentQuestion.content.template[this.selectedLanguage];\n      } else {\n        // 使用语言默认模板\n        this.code = templateCode;\n      }\n      // 注意：消息提示已在CodeMirror组件中处理，这里不再重复显示\n    },\n    \n    // 轮询判题结果\n    pollJudgeResult(submissionKey, times = 0, isSubmit = false) {\n      if (times > 20) {\n        this.testLoading = false;\n        this.$message.error('判题超时，请稍后再试');\n        return;\n      }\n      \n      setTimeout(() => {\n        getTestJudgeResult(submissionKey).then(res => {\n          if (res.success) {\n            if (res.result.status === 'PENDING' || res.result.status === 'Judging') {\n              this.pollJudgeResult(submissionKey, times + 1, isSubmit);\n            } else {\n              this.testResult = res.result;\n              this.testLoading = false;\n              \n              if (isSubmit) {\n                // 将当前题目ID添加到已回答列表\n                if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                  this.answeredQuestions.push(this.currentQuestion.id);\n                }\n                \n                // **优化点：重新定义需要立即报错的类型**\n                const immediateErrorStatus = ['Compile Error', 'System Error', 'Runtime Error'];\n                console.log(res.result.status);\n\n                if (immediateErrorStatus.includes(res.result.status)) {\n                  // 对于编译错误、系统错误等，立即弹出失败提示\n                  this.$notification.error({\n                    message: '提交失败',\n                    description: `状态: ${res.result.status}，这通常不是代码逻辑问题，请检查您的代码语法或环境。`,\n                    duration: 5\n                  });\n                   // 记录答案，但标记为错误\n                  this.userAnswersMap[this.currentQuestion.id] = {\n                    code: this.code,\n                    isCorrect: false,\n                    submitted: true\n                  };\n\n                  // 将当前题目添加到已答题列表(如果还未添加)\n                  if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                    this.answeredQuestions.push(this.currentQuestion.id);\n                  }\n                } else {\n                  // 对于Accepted, Wrong Answer等所有正常评测结果\n                  // 统一提示\"评测完成\"，不透露具体结果\n                  this.$notification.success({\n                    message: '评测完成',\n                    description: '您的代码已执行完毕，结果已记录。',\n                    duration: 3\n                  });\n                  \n                  // 根据结果静默记录状态\n                  this.userAnswersMap[this.currentQuestion.id] = {\n                    code: this.code,\n                    isCorrect: res.result.status === 'Accepted',\n                    submitted: true\n                  };\n                  \n                  // 将当前题目添加到已答题列表(如果还未添加)\n                  if (!this.answeredQuestions.includes(this.currentQuestion.id)) {\n                    this.answeredQuestions.push(this.currentQuestion.id);\n                  }\n                  // 自动切换到下一题\n                  if (this.currentQuestionIndex < this.questionList.length) {\n                    setTimeout(() => {\n                      this.nextQuestion();\n                    }, 500);\n                  }\n                }\n              } else {\n                // （非正式提交的）测试运行逻辑保持不变\n                this.$message.success('测试运行完成');\n              }\n            }\n          } else {\n            this.testLoading = false;\n            this.$message.error(res.message || '获取判题结果失败');\n          }\n        });\n      }, 2000); // 轮询间隔调整为2秒\n    },\n    \n    // 记录错题\n    recordWrongQuestion(questionId, answer) {\n      const params = {\n        questionId: questionId,\n        lastAnswer: answer\n      };\n      \n      // 调用添加错题记录API\n      addWrongRecord(params).then(res => {\n        if (!res.success) {\n          console.error('记录错题失败:', res.message);\n        }\n      }).catch(err => {\n        console.error('记录错题出错:', err);\n      });\n    },\n    \n    // 处理时间到\n    handleTimeUp() {\n      this.$notification.warning({\n        message: '时间到',\n        description: '已达到设定的时间限制，系统将自动结束本次练习。',\n        duration: 5\n      });\n      // 禁用防误触保护（时间到自动结束）\n      this.disableAntiMisoperation();\n      // 记录错题\n      this.submitWrongRecords();\n      // 时间到时清除保存的进度\n      this.clearSavedProgress();\n      this.practiseCompleted = true;\n    },\n    \n    // 下一题 - 修改为保存答案\n    nextQuestion() {\n      // 保存当前题目答案\n      this.saveCurrentAnswer();\n\n      if (this.currentQuestionIndex < this.questionList.length) {\n        this.currentQuestionIndex++;\n        // 加载已保存的答案\n        this.loadSavedAnswer();\n\n        // 关闭测试抽屉\n        this.openTestCaseDrawer = false;\n      } else {\n        // // 修改：检查是否有未完成的题目，有则提示确认\n        if (this.answeredQuestions.length < this.questionList.length) {\n          this.$confirm({\n            title: '未完成全部题目',\n            content: `您还有 ${this.unfinishedCount} 道题目未完成，确定要结束本次练习吗？`,\n            okText: '仍要结束',\n            cancelText: '我再想想',\n            onOk: () => {\n              // 禁用防误触保护（练习完成）\n              this.disableAntiMisoperation();\n              // 记录错题\n              this.submitWrongRecords();\n              // 练习完成时清除保存的进度\n              this.clearSavedProgress();\n              this.practiseCompleted = true;\n            }\n          });\n        } else {\n          // 禁用防误触保护（练习完成）\n          this.disableAntiMisoperation();\n          // 记录错题\n          this.submitWrongRecords();\n          // 练习完成时清除保存的进度\n          this.clearSavedProgress();\n          this.practiseCompleted = true;\n        }\n      }\n\n      // 在末尾添加检查收藏状态\n      this.checkCurrentQuestionCollection();\n    },\n    \n    // 上一题 - 修改为保存答案\n    prevQuestion() {\n      // 保存当前题目答案\n      this.saveCurrentAnswer();\n      \n      if (this.currentQuestionIndex > 1) {\n        this.currentQuestionIndex--;\n        // 加载已保存的答案\n        this.loadSavedAnswer();\n        \n        // 关闭测试抽屉\n        this.openTestCaseDrawer = false;\n      }\n      \n      // 在末尾添加检查收藏状态\n      this.checkCurrentQuestionCollection();\n    },\n    \n    // 跳转到指定题目 - 修改为保存答案\n    jumpToQuestion(index) {\n      if (index >= 1 && index <= this.questionList.length) {\n        // 保存当前题目答案\n        this.saveCurrentAnswer();\n        \n        this.currentQuestionIndex = index;\n        // 加载已保存的答案\n        this.loadSavedAnswer();\n        \n        // 关闭测试抽屉\n        this.openTestCaseDrawer = false;\n      }\n      \n      // 在末尾添加检查收藏状态\n      this.checkCurrentQuestionCollection();\n    },\n    \n    // 收藏题目\n    collectQuestion() {\n      if (!this.currentQuestion || !this.currentQuestion.id) {\n        this.$message.warning('当前题目信息不完整，无法操作');\n        return;\n      }\n      \n      if (this.collectLoading) return; // 防止重复点击\n      this.collectLoading = true;\n      \n      const questionId = this.currentQuestion.id;\n      \n      if (this.isCollected) {\n        console.log('取消收藏');\n        // 取消收藏\n        const collectionId = this.collectionStatusMap[questionId];\n        deleteCollection({ id: collectionId }).then(res => {\n          console.log(res);\n          if (res.success) {\n            this.$message.success('已取消收藏');\n            this.$delete(this.collectionStatusMap, questionId);\n          } else {\n            this.$message.warning(res.message || '取消收藏失败');\n          }\n        }).finally(() => {\n          this.collectLoading = false;\n        });\n      } else {\n        console.log('收藏成功');\n        // 添加收藏\n        addCollection({ questionId }).then(res => {\n          console.log(res);\n          if (res.success) {\n            // API调用成功\n            this.$message.success('收藏成功');\n            this.$set(this.collectionStatusMap, questionId, res.result.id);\n          } else {\n            // 其他错误\n            this.$message.warning(res.message || '收藏失败');\n          }\n        }).finally(() => {\n          this.collectLoading = false;\n        });\n      }\n    },\n    \n    // 退出刷题\n    exitPractise() {\n      this.$confirm({\n        title: '确定要退出刷题吗?',\n        content: '当前答题进度将会保存',\n        onOk: () => {\n          // 禁用防误触保护（用户主动退出）\n          this.disableAntiMisoperation();\n          // 先保存当前题目的答案，再保存整体进度\n          this.saveCurrentAnswer();\n          // 保存答题进度\n          this.saveProgress();\n          this.practiseStarted = false;\n        }\n      });\n    },\n    \n    // 更新代码 - 确保将代码更新到内部状态\n    updateCode(newCode) {\n      this.code = newCode;\n    },\n    \n    // 切换测试抽屉\n    toggleTestDrawer() {\n      this.openTestCaseDrawer = !this.openTestCaseDrawer;\n    },\n    \n    // 处理语言切换\n    handleLanguageChange(newLang) {\n      this.selectedLanguage = newLang;\n    },\n    \n    // 处理测试抽屉状态更新\n    handleDrawerUpdate(newVal) {\n      this.openTestCaseDrawer = newVal;\n    },\n    \n    // 获取用户最近通过的代码\n    getUserLastAcceptedCode() {\n      // 获取用户最近通过的代码\n      if (!this.currentQuestion || !this.currentQuestion.id) {\n        this.$message.warning('无法获取题目信息');\n        return;\n      }\n      \n      const params = {\n        pid: this.currentQuestion.id,\n        language: this.selectedLanguage\n      };\n      \n      getLastAcceptedCode(params).then(res => {\n        if (res.success && res.result) {\n          this.code = res.result.code;\n          this.$message.success('成功获取最近通过的代码');\n        } else {\n          this.$message.warning(res.message || '您还未通过该题目，无法获取最近通过的代码');\n        }\n      }).catch(err => {\n        console.error('获取最近通过代码出错：', err);\n        this.$message.error('获取最近通过代码失败');\n      });\n    },\n    \n    // Markdown转HTML - 修复marked使用方式\n    markdownToHtml(text) {\n      if (!text) return '';\n      try {\n        // 确保使用正确的API调用方式\n        return marked(text, {\n          gfm: true, // 启用GitHub风格Markdown\n          breaks: true // 启用换行符转换\n        });\n      } catch (error) {\n        console.error('Markdown转换错误：', error);\n        return text; // 转换失败时返回原文本\n      }\n    },\n\n    // 快速刷题\n    startQuickPractise() {\n      // 检查是否选择了科目\n      if (!this.queryParam.subject) {\n        this.$message.warning('请先选择科目');\n        return;\n      }\n      \n      // 添加对刷题模式的验证，确保用户已选择刷题模式\n      if (!this.practiseMode) {\n        this.$message.warning('请选择刷题模式');\n        return;\n      }\n      \n      // 检查是否有保存的进度\n      if (this.hasSavedProgress) {\n        this.$confirm({\n          title: '有未完成的答题进度',\n          content: '开始快速刷题将放弃上次的答题进度，是否继续？',\n          okText: '继续',\n          cancelText: '取消',\n          onOk: () => {\n            // 清除已保存的进度\n            this.clearSavedProgress();\n            // 开始新的刷题\n            this.doStartQuickPractise();\n          }\n        });\n      } else {\n        // 没有保存的进度，直接开始快速刷题\n        this.doStartQuickPractise();\n      }\n    },\n    \n    // 实际执行快速刷题的操作\n    doStartQuickPractise() {\n      this.loading = true;\n      // 标记为快速刷题模式\n      this.isQuickMode = true;\n      \n      // 使用随机选题API\n      const params = {\n        subject: this.queryParam.subject,\n        count: this.practiseCount,\n        mode: this.practiseMode\n      };\n      \n      getRandomQuestions(params).then(res => {\n        if (res.success) {\n          // 处理返回的题目数据\n          this.questionList = res.result || [];\n          \n          // 验证题目数据的完整性\n          if (this.questionList.length > 0) {\n            // 检查和处理题目数据\n            this.questionList = this.questionList.map(question => {\n              // 处理content字段\n              try {\n                // 检查content是否为字符串(JSON)，如果是则解析为对象\n                if (typeof question.content === 'string' && question.content) {\n                  try {\n                    question.content = JSON.parse(question.content);\n                  } catch (jsonError) {\n                    console.error('解析题目content JSON失败:', jsonError);\n                    question.content = {};\n                  }\n                } else if (!question.content) {\n                  question.content = {};\n                }\n                \n                // 针对不同题型进行数据补充和验证\n                if (question.questionType === 1) { // 单选题\n                  if (!question.content.options || !Array.isArray(question.content.options)) {\n                    question.content.options = [];\n                  }\n                  if (!question.content.answer) {\n                    question.content.answer = '';\n                  }\n                } else if (question.questionType === 2) { // 判断题\n                  if (!['true', 'false', '正确', '错误', 'T', 'F'].includes(question.content.answer)) {\n                    question.content.answer = '';\n                  }\n                } else if (question.questionType === 3) { // 编程题\n                  if (!question.content.description) question.content.description = '';\n                  if (!question.content.input_format) question.content.input_format = '';\n                  if (!question.content.output_format) question.content.output_format = '';\n                  \n                  if (!question.content.sample_cases || !Array.isArray(question.content.sample_cases)) {\n                    question.content.sample_cases = [];\n                  } else {\n                    question.content.sample_cases = question.content.sample_cases.map(sample => {\n                      return {\n                        input: typeof sample.input === 'string' ? sample.input : String(sample.input || ''),\n                        output: typeof sample.output === 'string' ? sample.output : String(sample.output || '')\n                      };\n                    });\n                  }\n                }\n                \n                if (!question.content.analysis) {\n                  question.content.analysis = '';\n                }\n              } catch (error) {\n                console.error('处理题目数据时出错:', error, '问题数据:', question);\n                question.content = {\n                  answer: '',\n                  options: [],\n                  description: '题目数据处理失败',\n                  analysis: '',\n                  sample_cases: []\n                };\n              }\n              \n              return question;\n            });\n            \n            if (this.questionList.length > 0) {\n              // 按题型优先级排序题目：单选题 > 判断题 > 编程题\n              this.questionList = this.sortQuestionsByType(this.questionList);\n              \n              this.currentQuestionIndex = 1;\n              this.resetAnswerState();\n              this.practiseStarted = true;\n              this.answeredQuestions = [];\n              this.userAnswersMap = {};\n\n              // 如果是限时模式，设置倒计时\n              if (this.practiseMode === 'time') {\n                this.deadline = Date.now() + this.timeLimit * 60 * 1000;\n                // 立即更新一次时间显示\n                this.updateRemainingTime();\n              }\n\n              // 启用防误触保护（快速刷题优先级较低）\n              this.enableAntiMisoperation({\n                confirmMessage: '您正在进行快速刷题练习，确定要离开吗？离开后当前进度将会保存。',\n                priority: 5, // 快速刷题优先级较低\n                preventRefresh: true,\n                preventNavigation: true\n              });\n\n              // 显示开始提示\n              this.$notification.success({\n                message: '快速刷题启动成功',\n                description: `已为您准备好${this.questionList.length}道题目，祝您学习愉快！`,\n                duration: 3\n              });\n              \n              // 添加代码：确保DOM更新后，滚动到顶部状态区\n              this.$nextTick(() => {\n                const statusElement = document.querySelector('.practice-status');\n                if (statusElement) {\n                  statusElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\n                  // 额外添加window.scrollTo确保页面顶部对齐\n                  window.scrollTo(0, 0);\n                }\n              });\n              \n              // 检查第一题的收藏状态\n              this.$nextTick(() => {\n                this.checkCurrentQuestionCollection();\n              });\n            } else {\n              this.$message.warning('获取题目失败，请调整筛选条件后重试');\n            }\n          } else {\n            this.$message.warning('没有找到符合条件的题目，请调整筛选条件后重试');\n          }\n        } else {\n          this.$message.error(res.message || '获取题目失败');\n        }\n        this.loading = false;\n      }).catch(err => {\n        this.loading = false;\n        console.error('获取题目出错：', err);\n        this.$message.error('获取题目失败，请稍后重试');\n      });\n    },\n    \n    // 保存当前题目答案\n    saveCurrentAnswer() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return;\n      \n      const questionId = this.currentQuestion.id;\n      \n      // 根据题型保存不同类型的答案\n      if (this.currentQuestion.questionType === 3) {\n        // 编程题，保存代码\n        if (this.code && this.code.trim() !== '') {\n          // 检查是否已经有保存的答案对象\n          const existingAnswer = this.userAnswersMap[questionId];\n          \n          // 如果已有答案且是对象类型(已提交评测)，则只更新代码，保留其他属性\n          if (existingAnswer && typeof existingAnswer === 'object') {\n            this.userAnswersMap[questionId] = {\n              ...existingAnswer,\n              code: this.code\n            };\n          } else {\n            // 否则初始化为未提交状态的对象\n            this.userAnswersMap[questionId] = {\n              code: this.code,\n              submitted: false\n            };\n          }\n          \n          // 如果不在已答题列表中，不添加\n          // 编程题只有提交评测后才算已答题\n        }\n      } else {\n        // 选择题或判断题，保存选项\n        if (this.userAnswer) {\n          this.userAnswersMap[questionId] = this.userAnswer;\n          \n          // 如果不在已答题列表中，则添加\n          if (!this.answeredQuestions.includes(questionId)) {\n            this.answeredQuestions.push(questionId);\n          }\n        }\n      }\n    },\n    \n    // 加载已保存的答案\n    loadSavedAnswer() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return;\n      \n      const questionId = this.currentQuestion.id;\n      const savedAnswer = this.userAnswersMap[questionId];\n      \n      if (savedAnswer) {\n        if (this.currentQuestion.questionType === 3) {\n          // 编程题，加载保存的代码\n          // 处理不同类型的答案数据格式\n          if (typeof savedAnswer === 'object' && savedAnswer.code) {\n            // 如果是对象格式，从code字段获取代码\n            this.code = savedAnswer.code;\n          } else if (typeof savedAnswer === 'string') {\n            // 兼容旧版本格式，直接使用字符串\n            this.code = savedAnswer;\n          } else {\n            // 未知格式，重置代码\n            this.code = '';\n          }\n        } else {\n          // 选择题或判断题，加载保存的选项\n          this.userAnswer = savedAnswer;\n        }\n      } else {\n        // 没有保存的答案，重置\n        this.resetAnswerState();\n      }\n    },\n    \n    // 切换焦点模式\n    switchFocusMode(isOpen) {\n      // 使用全屏模式状态来切换焦点模式\n      this.isFullScreen = isOpen; \n      \n      // 控制body滚动条\n      if (isOpen) {\n        document.body.style.overflow = 'hidden';\n        this.$message.success('已进入专注模式，按ESC可退出');\n      } else {\n        document.body.style.overflow = '';\n        this.$message.success('已退出专注模式');\n      }\n    },\n    // 按题型优先级排序题目：单选题 > 判断题 > 编程题\n    sortQuestionsByType(questions) {\n      if (!questions || !questions.length) return questions;\n      \n      return [...questions].sort((a, b) => {\n        // 为不同题型赋予优先级权重：单选题=1，判断题=2，编程题=3\n        const getPriorityWeight = (type) => {\n          switch(type) {\n            case 1: return 1; // 单选题最高优先级\n            case 2: return 2; // 判断题次之\n            case 3: return 3; // 编程题最低优先级\n            default: return 4; // 其他类型最低优先级\n          }\n        };\n        \n        // 按权重升序排序\n        return getPriorityWeight(a.questionType) - getPriorityWeight(b.questionType);\n      });\n    },\n    // 添加更新剩余时间的方法\n    updateRemainingTime() {\n      if (this.deadline > 0) {\n        const now = Date.now();\n        const remaining = this.deadline - now;\n        \n        if (remaining <= 0) {\n          this.remainingTimeText = '00:00';\n          this.handleTimeUp();\n          return;\n        }\n        \n        // 计算分钟和秒数\n        const minutes = Math.floor(remaining / 1000 / 60);\n        const seconds = Math.floor(remaining / 1000 % 60);\n        \n        // 更新显示的时间文本\n        this.remainingTimeText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      }\n    },\n    // 检查是否有保存的答题进度\n    checkSavedProgress() {\n      // 从localStorage中读取答题进度数据\n      const savedProgress = localStorage.getItem('gesp_saved_progress');\n      if (savedProgress) {\n        try {\n          const progressData = JSON.parse(savedProgress);\n          // 验证保存的数据是否有效\n          if (progressData && \n              progressData.queryParam && \n              progressData.questionList && \n              progressData.questionList.length > 0 &&\n              progressData.userAnswersMap &&\n              progressData.answeredQuestions) {\n            this.hasSavedProgress = true;\n          } else {\n            this.hasSavedProgress = false;\n          }\n        } catch (e) {\n          console.error('解析保存的答题进度失败:', e);\n          this.hasSavedProgress = false;\n        }\n      } else {\n        this.hasSavedProgress = false;\n      }\n    },\n    // 保存答题进度到本地存储\n    saveProgress() {\n      // 构建需要保存的进度数据\n      const progressData = {\n        queryParam: this.queryParam,\n        practiseMode: this.practiseMode,\n        practiseCount: this.practiseCount,\n        timeLimit: this.timeLimit,\n        questionTypes: this.questionTypes,\n        difficultyRange: this.difficultyRange,\n        questionList: this.questionList,\n        currentQuestionIndex: this.currentQuestionIndex,\n        answeredQuestions: this.answeredQuestions,\n        userAnswersMap: this.userAnswersMap,\n        // 新增：保存测试数据\n        testInputMap: this.testInputMap,\n        testResultMap: this.testResultMap,\n        activeTestCaseIndexMap: this.activeTestCaseIndexMap\n      };\n      \n      // 保存到localStorage\n      try {\n        localStorage.setItem('gesp_saved_progress', JSON.stringify(progressData));\n        this.hasSavedProgress = true;\n      } catch (e) {\n        console.error('保存答题进度失败:', e);\n        this.$message.error('保存答题进度失败');\n      }\n    },\n    \n    // 从本地存储加载答题进度\n    loadProgress() {\n      const savedProgress = localStorage.getItem('gesp_saved_progress');\n      if (savedProgress) {\n        try {\n          const progressData = JSON.parse(savedProgress);\n          \n          // 恢复刷题设置\n          this.queryParam = progressData.queryParam || this.queryParam;\n          this.practiseMode = progressData.practiseMode;\n          this.practiseCount = progressData.practiseCount;\n          this.timeLimit = progressData.timeLimit;\n          this.questionTypes = progressData.questionTypes;\n          this.difficultyRange = progressData.difficultyRange;\n          \n          // 恢复题目列表和作答状态\n          this.questionList = progressData.questionList;\n          this.currentQuestionIndex = progressData.currentQuestionIndex;\n          this.answeredQuestions = progressData.answeredQuestions;\n          this.userAnswersMap = progressData.userAnswersMap;\n          \n          // 新增：恢复测试数据\n          if (progressData.testInputMap) this.testInputMap = progressData.testInputMap;\n          if (progressData.testResultMap) this.testResultMap = progressData.testResultMap;\n          if (progressData.activeTestCaseIndexMap) this.activeTestCaseIndexMap = progressData.activeTestCaseIndexMap;\n          \n          // 开始刷题\n          this.practiseStarted = true;\n          \n          // 如果是限时模式且有剩余时间，重新设置倒计时\n          if (this.practiseMode === 'time') {\n            // 计算新的截止时间 - 简单设置为从当前时间起再加上timeLimit分钟\n            this.deadline = Date.now() + this.timeLimit * 60 * 1000;\n            this.updateRemainingTime();\n          }\n          \n          // 加载当前题目的答案\n          this.loadSavedAnswer();\n          \n          this.$message.success('已恢复上次答题进度');\n          return true;\n        } catch (e) {\n          console.error('加载答题进度失败:', e);\n          this.$message.error('加载答题进度失败，将开始新的答题');\n          return false;\n        }\n      }\n      return false;\n    },\n    \n    // 清除保存的答题进度\n    clearSavedProgress() {\n      localStorage.removeItem('gesp_saved_progress');\n      this.hasSavedProgress = false;\n    },\n    // 继续上次答题\n    continuePractise() {\n      // 加载保存的答题进度\n      const loadSuccess = this.loadProgress();\n\n      // 如果加载成功，启用防误触保护并滚动到顶部状态区\n      if (loadSuccess) {\n        // 启用防误触保护（继续刷题优先级较低）\n        this.enableAntiMisoperation({\n          confirmMessage: '您正在继续刷题练习，确定要离开吗？离开后当前进度将会保存。',\n          priority: 5, // 继续刷题优先级较低\n          preventRefresh: true,\n          preventNavigation: true\n        });\n\n        this.$nextTick(() => {\n          const statusElement = document.querySelector('.practice-status');\n          if (statusElement) {\n            statusElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\n            // 额外添加window.scrollTo确保页面顶部对齐\n            window.scrollTo(0, 0);\n          }\n        });\n      }\n    },\n    // 开始新一轮练习\n    startNewPractise() {\n      this.practiseCompleted = false;\n      // 根据之前的刷题方式决定调用哪个方法\n      if (this.isQuickMode) {\n        this.startQuickPractise();\n      } else {\n        this.startPractise();\n      }\n    },\n    // 关闭完成总结\n    handlePracticeSummaryClose() {\n      this.practiseCompleted = false;\n      // 禁用防误触保护（练习已结束）\n      this.disableAntiMisoperation();\n      // 关闭练习完成弹窗时清除保存的进度，因为练习已经结束\n      this.clearSavedProgress();\n      this.practiseStarted = false;\n    },\n    // 添加错题记录方法\n    submitWrongRecords() {\n      // 收集所有做错的题目\n      const wrongQuestions = this.questionList.filter(q => \n        this.answeredQuestions.includes(q.id) && !this.isAnswerCorrect(q)\n      );\n      \n      // 如果有做错的题目，则提交记录\n      if (wrongQuestions.length > 0) {\n        // 遍历每道错题，记录到数据库\n        wrongQuestions.forEach(question => {\n          const userAnswer = this.userAnswersMap[question.id];\n          // 根据题型不同，格式化用户答案\n          let formattedAnswer = '';\n          \n          if (question.questionType === 3) {\n            // 编程题：转为JSON字符串保存代码和语言\n            formattedAnswer = JSON.stringify({\n              code: userAnswer.code || '',\n              language: this.selectedLanguage\n            });\n          } else if (question.questionType === 2) {\n            // 判断题：统一转换为T/F格式\n            if (userAnswer === 'true') {\n              formattedAnswer = 'T';\n            } else if (userAnswer === 'false') {\n              formattedAnswer = 'F';\n            } else {\n              formattedAnswer = String(userAnswer);\n            }\n          } else {\n            // 其他客观题：直接保存用户选择\n            formattedAnswer = String(userAnswer);\n          }\n          \n          // 调用错题记录API\n          this.recordWrongQuestion(question.id, formattedAnswer);\n        });\n        \n        // 提示用户错题已记录\n        this.$message.success(`已将${wrongQuestions.length}道错题记录/更新到错题本`);\n      }\n    },\n    // // 新增：进入查阅答题模式\n    enterReviewMode() {\n      this.isReviewMode = true; // 设置为查阅答题模式\n      this.showAnswer = true; // 默认开启显示答案\n      this.practiseCompleted = false; // 关闭结果窗口\n      \n      // 查找第一个错误题目的索引\n      const errorQuestions = this.questionList.filter(q =>\n        this.answeredQuestions.includes(q.id) && !this.isAnswerCorrect(q)\n      );\n      \n      if (errorQuestions.length > 0) {\n        // 找到第一个错误题目，获取其在questionList中的索引\n        const firstErrorQuestion = errorQuestions[0];\n        this.firstErrorIndex = this.questionList.findIndex(q => q.id === firstErrorQuestion.id) + 1;\n        // 跳转到第一个错误题目\n        this.currentQuestionIndex = this.firstErrorIndex;\n      } else {\n        // 如果没有错误题目，则定位到第一题\n        this.currentQuestionIndex = 1;\n      }\n      \n      // 加载当前题目的答案状态\n      this.loadSavedAnswer();\n      \n      // 提示用户进入查阅模式\n      this.$notification.info({\n        message: '已进入查阅答题模式',\n        description: '您可以查看每道题的正确答案和解析，并复习您的答题情况。',\n        duration: 5\n      });\n    },\n    // // 新增：退出查阅答题模式\n    exitReviewMode() {\n      this.$confirm({\n        title: '确定要退出查阅答题模式吗?',\n        content: '退出后将返回到刷题主界面',\n        onOk: () => {\n          this.isReviewMode = false; // 关闭查阅答题模式\n          this.showAnswer = false; // 关闭显示答案\n          // 退出查阅模式时清除保存的进度，因为练习已经结束\n          this.clearSavedProgress();\n          this.practiseStarted = false; // 退出到主界面\n        }\n      });\n    },\n    getStatusColor(status) {\n      switch (status) {\n        case 'correct':\n          return 'success';\n        case 'incorrect':\n          return 'error';\n        case 'unfinished':\n          return 'warning';\n        default:\n          return 'default';\n      }\n    },\n\n    // 页面刷新或关闭前的保护\n    handleBeforeUnload(event) {\n      // 只有在刷题进行中时才提示\n      if (this.practiseStarted && !this.practiseCompleted) {\n        const message = '您正在进行刷题练习，刷新或关闭页面将丢失当前未保存的答题进度，确定要离开吗？';\n        event.returnValue = message;\n        return message;\n      }\n    },\n    getStatusIcon(status) {\n      switch (status) {\n        case 'correct':\n          return 'check-circle';\n        case 'incorrect':\n          return 'close-circle';\n        case 'unfinished':\n          return 'minus-circle';\n        default:\n          return 'question';\n      }\n    },\n    getStatusText(status) {\n      switch (status) {\n        case 'correct':\n          return '正确';\n        case 'incorrect':\n          return '错误';\n        case 'unfinished':\n          return '未完成';\n        default:\n          return '未知';\n      }\n    },\n    getStatusClass(status) {\n      switch (status) {\n        case 'correct':\n          return 'status-success';\n        case 'incorrect':\n          return 'status-error';\n        case 'unfinished':\n          return 'status-warning';\n        default:\n          return '';\n      }\n    },\n    // 新增：切换题目后检查收藏状态\n    checkCurrentQuestionCollection() {\n      if (!this.currentQuestion || !this.currentQuestion.id) return;\n      \n      const questionId = this.currentQuestion.id;\n      // 如果已经检查过这个题目的收藏状态，就不再重复请求\n      if (this.collectionStatusMap.hasOwnProperty(questionId)) {\n        return;\n      }\n      \n      // 设置一个临时状态，防止同一题目重复发送请求\n      this.$set(this.collectionStatusMap, questionId, undefined);\n      \n      checkCollection({ questionId }).then(res => {\n        if (res.success) {\n          // 如果题目已收藏，保存收藏ID\n          if (res.result && res.result.id) {\n            this.$set(this.collectionStatusMap, questionId, res.result.id);\n          } else {\n            // 未收藏\n            this.$set(this.collectionStatusMap, questionId, null);\n          }\n        } else {\n          // API调用失败，标记为未收藏\n          this.$set(this.collectionStatusMap, questionId, null);\n        }\n      }).catch(() => {\n        // 出错时标记为未收藏\n        this.$set(this.collectionStatusMap, questionId, null);\n      });\n    },\n  },\n\n  // // 路由守卫：防止用户意外离开页面导致进度丢失\n  // beforeRouteLeave(to, from, next) {\n  //   // 只有在刷题进行中时才需要确认\n  //   if (this.practiseStarted && !this.practiseCompleted) {\n  //     this.$confirm({\n  //       title: '确定要离开当前页面吗？',\n  //       content: '您正在进行刷题练习，离开前系统将自动保存当前进度。',\n  //       okText: '保存并离开',\n  //       cancelText: '继续刷题',\n  //       onOk: () => {\n  //         // 用户确认离开，先保存当前答案和进度\n  //         this.saveCurrentAnswer();\n  //         this.saveProgress();\n  //         next();\n  //       },\n  //       onCancel: () => {\n  //         // 用户取消离开\n  //         next(false);\n  //       }\n  //     });\n  //   } else {\n  //     // 没有在刷题或已完成，直接允许离开\n  //     next();\n  //   }\n  //}\n}\n</script>\n\n\n<style lang=\"less\" scoped>\n.practice-container {\n  @media screen and (max-width: 768px) {\n    padding: 8px;\n  }\n\n\n  // 全屏模式样式\n  .full-screen-mode {\n    position: fixed !important;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    width: 100%;\n    height: 100vh;\n    z-index: 1000;\n    margin: 0;\n    overflow-y: auto;\n    background-color: #f0f2f5;\n  }\n\n  // 水平布局样式（全屏模式下的刷题界面）\n  .horizontal-layout {\n    display: flex;\n    flex-direction: column;\n\n    .question-container {\n      width: 100%;\n      height: 100vh;\n      overflow-y: auto;\n      background-color: #ffffff;\n      padding: 16px;\n    }\n  }\n}\n</style>\n"]}]}