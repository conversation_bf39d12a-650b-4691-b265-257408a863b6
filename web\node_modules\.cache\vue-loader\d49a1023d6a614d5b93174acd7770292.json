{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue", "mtime": 1753093743655}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./PracticeResultModal.vue?vue&type=template&id=1c8278da&scoped=true\"\nimport script from \"./PracticeResultModal.vue?vue&type=script&lang=js\"\nexport * from \"./PracticeResultModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./PracticeResultModal.vue?vue&type=style&index=0&id=1c8278da&lang=less&scoped=true\"\nimport style1 from \"./PracticeResultModal.vue?vue&type=style&index=1&id=1c8278da&lang=less\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1c8278da\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1c8278da')) {\n      api.createRecord('1c8278da', component.options)\n    } else {\n      api.reload('1c8278da', component.options)\n    }\n    module.hot.accept(\"./PracticeResultModal.vue?vue&type=template&id=1c8278da&scoped=true\", function () {\n      api.rerender('1c8278da', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/examSystem/components/PracticeResultModal.vue\"\nexport default component.exports"]}