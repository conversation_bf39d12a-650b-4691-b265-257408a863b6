{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\Ellipsis.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\Ellipsis.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { cutStrByFullLength, getStrFullLength } from '@/components/_util/StringUtil'\n\n  export default {\n    name: 'Ellipsis',\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-ellipsis'\n      },\n      tooltip: {\n        type: Boolean,\n        default: true,\n      },\n      length: {\n        type: Number,\n        default: 25,\n      },\n      lines: {\n        type: Number,\n        default: 1\n      },\n      fullWidthRecognition: {\n        type: Boolean,\n        default: false\n      }\n    },\n    methods: {},\n    render() {\n      const { tooltip, length } = this.$props\n      let text = ''\n      // 处理没有default插槽时的特殊情况\n      if (this.$slots.default) {\n        text = this.$slots.default.map(vNode => vNode.text).join('')\n      }\n      // 判断是否显示 tooltip\n      if (tooltip && getStrFullLength(text) > length) {\n        return (\n          <a-tooltip>\n            <template slot=\"title\">{text}</template>\n            <span>{cutStrByFullLength(text, this.length) + '…'}</span>\n          </a-tooltip>\n        )\n      } else {\n        return (<span>{text}</span>)\n      }\n    }\n  }\n", {"version": 3, "sources": ["Ellipsis.vue"], "names": [], "mappings": ";AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Ellipsis.vue", "sourceRoot": "src/components/Ellipsis", "sourcesContent": ["<script>\n  import { cutStrByFullLength, getStrFullLength } from '@/components/_util/StringUtil'\n\n  export default {\n    name: 'Ellipsis',\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-ellipsis'\n      },\n      tooltip: {\n        type: Boolean,\n        default: true,\n      },\n      length: {\n        type: Number,\n        default: 25,\n      },\n      lines: {\n        type: Number,\n        default: 1\n      },\n      fullWidthRecognition: {\n        type: Boolean,\n        default: false\n      }\n    },\n    methods: {},\n    render() {\n      const { tooltip, length } = this.$props\n      let text = ''\n      // 处理没有default插槽时的特殊情况\n      if (this.$slots.default) {\n        text = this.$slots.default.map(vNode => vNode.text).join('')\n      }\n      // 判断是否显示 tooltip\n      if (tooltip && getStrFullLength(text) > length) {\n        return (\n          <a-tooltip>\n            <template slot=\"title\">{text}</template>\n            <span>{cutStrByFullLength(text, this.length) + '…'}</span>\n          </a-tooltip>\n        )\n      } else {\n        return (<span>{text}</span>)\n      }\n    }\n  }\n</script>"]}]}