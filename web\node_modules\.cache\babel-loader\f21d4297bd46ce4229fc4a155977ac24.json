{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue?vue&type=template&id=7d6a815f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return !_vm.reloading ? _c(\"div\", {\n    staticClass: \"j-area-linkage\"\n  }, [_vm._type === _vm.enums.type[0] ? _c(\"area-cascader\", _vm._g(_vm._b({\n    style: {\n      width: _vm.width\n    },\n    attrs: {\n      value: _vm.innerValue,\n      data: _vm.pcaa,\n      level: 1\n    },\n    on: {\n      change: _vm.handleChange\n    }\n  }, \"area-cascader\", _vm.$attrs, false), _vm._listeners)) : _vm._type === _vm.enums.type[1] ? _c(\"area-select\", _vm._g(_vm._b({\n    attrs: {\n      value: _vm.innerValue,\n      data: _vm.pcaa,\n      level: 2\n    },\n    on: {\n      change: _vm.handleChange\n    }\n  }, \"area-select\", _vm.$attrs, false), _vm._listeners)) : _c(\"div\", [_c(\"span\", {\n    staticStyle: {\n      color: \"red\"\n    }\n  }, [_vm._v(\" Bad type value: \" + _vm._s(_vm._type))])])], 1) : _vm._e();\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "reloading", "staticClass", "_type", "enums", "type", "_g", "_b", "style", "width", "attrs", "value", "innerValue", "data", "pcaa", "level", "on", "change", "handleChange", "$attrs", "_listeners", "staticStyle", "color", "_v", "_s", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/jeecg/JAreaLinkage.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return !_vm.reloading\n    ? _c(\n        \"div\",\n        { staticClass: \"j-area-linkage\" },\n        [\n          _vm._type === _vm.enums.type[0]\n            ? _c(\n                \"area-cascader\",\n                _vm._g(\n                  _vm._b(\n                    {\n                      style: { width: _vm.width },\n                      attrs: {\n                        value: _vm.innerValue,\n                        data: _vm.pcaa,\n                        level: 1,\n                      },\n                      on: { change: _vm.handleChange },\n                    },\n                    \"area-cascader\",\n                    _vm.$attrs,\n                    false\n                  ),\n                  _vm._listeners\n                )\n              )\n            : _vm._type === _vm.enums.type[1]\n            ? _c(\n                \"area-select\",\n                _vm._g(\n                  _vm._b(\n                    {\n                      attrs: {\n                        value: _vm.innerValue,\n                        data: _vm.pcaa,\n                        level: 2,\n                      },\n                      on: { change: _vm.handleChange },\n                    },\n                    \"area-select\",\n                    _vm.$attrs,\n                    false\n                  ),\n                  _vm._listeners\n                )\n              )\n            : _c(\"div\", [\n                _c(\"span\", { staticStyle: { color: \"red\" } }, [\n                  _vm._v(\" Bad type value: \" + _vm._s(_vm._type)),\n                ]),\n              ]),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAO,CAACD,GAAG,CAACG,SAAS,GACjBF,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEJ,GAAG,CAACK,KAAK,KAAKL,GAAG,CAACM,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,GAC3BN,EAAE,CACA,eAAe,EACfD,GAAG,CAACQ,EAAE,CACJR,GAAG,CAACS,EAAE,CACJ;IACEC,KAAK,EAAE;MAAEC,KAAK,EAAEX,GAAG,CAACW;IAAM,CAAC;IAC3BC,KAAK,EAAE;MACLC,KAAK,EAAEb,GAAG,CAACc,UAAU;MACrBC,IAAI,EAAEf,GAAG,CAACgB,IAAI;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEnB,GAAG,CAACoB;IAAa;EACjC,CAAC,EACD,eAAe,EACfpB,GAAG,CAACqB,MAAM,EACV,KACF,CAAC,EACDrB,GAAG,CAACsB,UACN,CACF,CAAC,GACDtB,GAAG,CAACK,KAAK,KAAKL,GAAG,CAACM,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,GAC/BN,EAAE,CACA,aAAa,EACbD,GAAG,CAACQ,EAAE,CACJR,GAAG,CAACS,EAAE,CACJ;IACEG,KAAK,EAAE;MACLC,KAAK,EAAEb,GAAG,CAACc,UAAU;MACrBC,IAAI,EAAEf,GAAG,CAACgB,IAAI;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEnB,GAAG,CAACoB;IAAa;EACjC,CAAC,EACD,aAAa,EACbpB,GAAG,CAACqB,MAAM,EACV,KACF,CAAC,EACDrB,GAAG,CAACsB,UACN,CACF,CAAC,GACDrB,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE;IAAEsB,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAAE,CAC5CxB,GAAG,CAACyB,EAAE,CAAC,mBAAmB,GAAGzB,GAAG,CAAC0B,EAAE,CAAC1B,GAAG,CAACK,KAAK,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,CACP,EACD,CACF,CAAC,GACDL,GAAG,CAAC2B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB7B,MAAM,CAAC8B,aAAa,GAAG,IAAI;AAE3B,SAAS9B,MAAM,EAAE6B,eAAe", "ignoreList": []}]}