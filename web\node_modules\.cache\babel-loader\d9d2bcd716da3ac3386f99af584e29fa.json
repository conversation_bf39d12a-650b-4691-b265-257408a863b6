{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue?vue&type=template&id=7035909e&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _this = this;\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"a-card\", {\n    staticClass: \"card\",\n    attrs: {\n      title: \"仓库管理\",\n      bordered: false\n    }\n  }, [_c(\"repository-form\", {\n    ref: \"repository\",\n    attrs: {\n      showSubmit: false\n    }\n  })], 1), _c(\"a-card\", {\n    staticClass: \"card\",\n    attrs: {\n      title: \"任务管理\",\n      bordered: false\n    }\n  }, [_c(\"task-form\", {\n    ref: \"task\",\n    attrs: {\n      showSubmit: false\n    }\n  })], 1), _c(\"a-card\", [_c(\"form\", {\n    attrs: {\n      autoFormCreate: function autoFormCreate(form) {\n        return _this.form = form;\n      }\n    }\n  }, [_c(\"a-table\", {\n    attrs: {\n      columns: _vm.columns,\n      dataSource: _vm.data,\n      pagination: false\n    },\n    scopedSlots: _vm._u([_vm._l([\"name\", \"workId\", \"department\"], function (col, i) {\n      return {\n        key: col,\n        fn: function fn(text, record, index) {\n          return [record.editable ? _c(\"a-input\", {\n            key: col,\n            staticStyle: {\n              margin: \"-5px 0\"\n            },\n            attrs: {\n              value: text,\n              placeholder: _vm.columns[i].title\n            },\n            on: {\n              change: function change(e) {\n                return _vm.handleChange(e.target.value, record.key, col);\n              }\n            }\n          }) : [_vm._v(_vm._s(text))]];\n        }\n      };\n    }), {\n      key: \"operation\",\n      fn: function fn(text, record, index) {\n        return [record.editable ? [record.isNew ? _c(\"span\", [_c(\"a\", {\n          on: {\n            click: function click($event) {\n              return _vm.saveRow(record.key);\n            }\n          }\n        }, [_vm._v(\"添加\")]), _c(\"a-divider\", {\n          attrs: {\n            type: \"vertical\"\n          }\n        }), _c(\"a-popconfirm\", {\n          attrs: {\n            title: \"是否要删除此行？\"\n          },\n          on: {\n            confirm: function confirm($event) {\n              return _vm.remove(record.key);\n            }\n          }\n        }, [_c(\"a\", [_vm._v(\"删除\")])])], 1) : _c(\"span\", [_c(\"a\", {\n          on: {\n            click: function click($event) {\n              return _vm.saveRow(record.key);\n            }\n          }\n        }, [_vm._v(\"保存\")]), _c(\"a-divider\", {\n          attrs: {\n            type: \"vertical\"\n          }\n        }), _c(\"a\", {\n          on: {\n            click: function click($event) {\n              return _vm.cancel(record.key);\n            }\n          }\n        }, [_vm._v(\"取消\")])], 1)] : _c(\"span\", [_c(\"a\", {\n          on: {\n            click: function click($event) {\n              return _vm.toggle(record.key);\n            }\n          }\n        }, [_vm._v(\"编辑\")]), _c(\"a-divider\", {\n          attrs: {\n            type: \"vertical\"\n          }\n        }), _c(\"a-popconfirm\", {\n          attrs: {\n            title: \"是否要删除此行？\"\n          },\n          on: {\n            confirm: function confirm($event) {\n              return _vm.remove(record.key);\n            }\n          }\n        }, [_c(\"a\", [_vm._v(\"删除\")])])], 1)];\n      }\n    }], null, true)\n  }), _c(\"a-button\", {\n    staticStyle: {\n      width: \"100%\",\n      \"margin-top\": \"16px\",\n      \"margin-bottom\": \"8px\"\n    },\n    attrs: {\n      type: \"dashed\",\n      icon: \"plus\"\n    },\n    on: {\n      click: _vm.newMember\n    }\n  }, [_vm._v(\"新增成员\")])], 1)]), _c(\"footer-tool-bar\", [_c(\"a-button\", {\n    attrs: {\n      type: \"primary\",\n      loading: _vm.loading\n    },\n    on: {\n      click: _vm.validate\n    }\n  }, [_vm._v(\"提交\")])], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_this", "_vm", "_c", "_self", "staticClass", "attrs", "title", "bordered", "ref", "showSubmit", "autoFormCreate", "form", "columns", "dataSource", "data", "pagination", "scopedSlots", "_u", "_l", "col", "i", "key", "fn", "text", "record", "index", "editable", "staticStyle", "margin", "value", "placeholder", "on", "change", "e", "handleChange", "target", "_v", "_s", "isNew", "click", "$event", "saveRow", "type", "confirm", "remove", "cancel", "toggle", "width", "icon", "newMember", "loading", "validate", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/form/advancedForm/AdvancedForm.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"a-card\",\n        { staticClass: \"card\", attrs: { title: \"仓库管理\", bordered: false } },\n        [\n          _c(\"repository-form\", {\n            ref: \"repository\",\n            attrs: { showSubmit: false },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"a-card\",\n        { staticClass: \"card\", attrs: { title: \"任务管理\", bordered: false } },\n        [_c(\"task-form\", { ref: \"task\", attrs: { showSubmit: false } })],\n        1\n      ),\n      _c(\"a-card\", [\n        _c(\n          \"form\",\n          { attrs: { autoFormCreate: (form) => (this.form = form) } },\n          [\n            _c(\"a-table\", {\n              attrs: {\n                columns: _vm.columns,\n                dataSource: _vm.data,\n                pagination: false,\n              },\n              scopedSlots: _vm._u(\n                [\n                  _vm._l([\"name\", \"workId\", \"department\"], function (col, i) {\n                    return {\n                      key: col,\n                      fn: function (text, record, index) {\n                        return [\n                          record.editable\n                            ? _c(\"a-input\", {\n                                key: col,\n                                staticStyle: { margin: \"-5px 0\" },\n                                attrs: {\n                                  value: text,\n                                  placeholder: _vm.columns[i].title,\n                                },\n                                on: {\n                                  change: (e) =>\n                                    _vm.handleChange(\n                                      e.target.value,\n                                      record.key,\n                                      col\n                                    ),\n                                },\n                              })\n                            : [_vm._v(_vm._s(text))],\n                        ]\n                      },\n                    }\n                  }),\n                  {\n                    key: \"operation\",\n                    fn: function (text, record, index) {\n                      return [\n                        record.editable\n                          ? [\n                              record.isNew\n                                ? _c(\n                                    \"span\",\n                                    [\n                                      _c(\n                                        \"a\",\n                                        {\n                                          on: {\n                                            click: function ($event) {\n                                              return _vm.saveRow(record.key)\n                                            },\n                                          },\n                                        },\n                                        [_vm._v(\"添加\")]\n                                      ),\n                                      _c(\"a-divider\", {\n                                        attrs: { type: \"vertical\" },\n                                      }),\n                                      _c(\n                                        \"a-popconfirm\",\n                                        {\n                                          attrs: { title: \"是否要删除此行？\" },\n                                          on: {\n                                            confirm: function ($event) {\n                                              return _vm.remove(record.key)\n                                            },\n                                          },\n                                        },\n                                        [_c(\"a\", [_vm._v(\"删除\")])]\n                                      ),\n                                    ],\n                                    1\n                                  )\n                                : _c(\n                                    \"span\",\n                                    [\n                                      _c(\n                                        \"a\",\n                                        {\n                                          on: {\n                                            click: function ($event) {\n                                              return _vm.saveRow(record.key)\n                                            },\n                                          },\n                                        },\n                                        [_vm._v(\"保存\")]\n                                      ),\n                                      _c(\"a-divider\", {\n                                        attrs: { type: \"vertical\" },\n                                      }),\n                                      _c(\n                                        \"a\",\n                                        {\n                                          on: {\n                                            click: function ($event) {\n                                              return _vm.cancel(record.key)\n                                            },\n                                          },\n                                        },\n                                        [_vm._v(\"取消\")]\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                            ]\n                          : _c(\n                              \"span\",\n                              [\n                                _c(\n                                  \"a\",\n                                  {\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.toggle(record.key)\n                                      },\n                                    },\n                                  },\n                                  [_vm._v(\"编辑\")]\n                                ),\n                                _c(\"a-divider\", {\n                                  attrs: { type: \"vertical\" },\n                                }),\n                                _c(\n                                  \"a-popconfirm\",\n                                  {\n                                    attrs: { title: \"是否要删除此行？\" },\n                                    on: {\n                                      confirm: function ($event) {\n                                        return _vm.remove(record.key)\n                                      },\n                                    },\n                                  },\n                                  [_c(\"a\", [_vm._v(\"删除\")])]\n                                ),\n                              ],\n                              1\n                            ),\n                      ]\n                    },\n                  },\n                ],\n                null,\n                true\n              ),\n            }),\n            _c(\n              \"a-button\",\n              {\n                staticStyle: {\n                  width: \"100%\",\n                  \"margin-top\": \"16px\",\n                  \"margin-bottom\": \"8px\",\n                },\n                attrs: { type: \"dashed\", icon: \"plus\" },\n                on: { click: _vm.newMember },\n              },\n              [_vm._v(\"新增成员\")]\n            ),\n          ],\n          1\n        ),\n      ]),\n      _c(\n        \"footer-tool-bar\",\n        [\n          _c(\n            \"a-button\",\n            {\n              attrs: { type: \"primary\", loading: _vm.loading },\n              on: { click: _vm.validate },\n            },\n            [_vm._v(\"提交\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAAA,IAAAC,KAAA;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAM;EAAE,CAAC,EAClE,CACEL,EAAE,CAAC,iBAAiB,EAAE;IACpBM,GAAG,EAAE,YAAY;IACjBH,KAAK,EAAE;MAAEI,UAAU,EAAE;IAAM;EAC7B,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDP,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAM;EAAE,CAAC,EAClE,CAACL,EAAE,CAAC,WAAW,EAAE;IAAEM,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;MAAEI,UAAU,EAAE;IAAM;EAAE,CAAC,CAAC,CAAC,EAChE,CACF,CAAC,EACDP,EAAE,CAAC,QAAQ,EAAE,CACXA,EAAE,CACA,MAAM,EACN;IAAEG,KAAK,EAAE;MAAEK,cAAc,EAAE,SAAAA,eAACC,IAAI;QAAA,OAAMX,KAAI,CAACW,IAAI,GAAGA,IAAI;MAAA;IAAE;EAAE,CAAC,EAC3D,CACET,EAAE,CAAC,SAAS,EAAE;IACZG,KAAK,EAAE;MACLO,OAAO,EAAEX,GAAG,CAACW,OAAO;MACpBC,UAAU,EAAEZ,GAAG,CAACa,IAAI;MACpBC,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAEf,GAAG,CAACgB,EAAE,CACjB,CACEhB,GAAG,CAACiB,EAAE,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE,UAAUC,GAAG,EAAEC,CAAC,EAAE;MACzD,OAAO;QACLC,GAAG,EAAEF,GAAG;QACRG,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;UACjC,OAAO,CACLD,MAAM,CAACE,QAAQ,GACXxB,EAAE,CAAC,SAAS,EAAE;YACZmB,GAAG,EAAEF,GAAG;YACRQ,WAAW,EAAE;cAAEC,MAAM,EAAE;YAAS,CAAC;YACjCvB,KAAK,EAAE;cACLwB,KAAK,EAAEN,IAAI;cACXO,WAAW,EAAE7B,GAAG,CAACW,OAAO,CAACQ,CAAC,CAAC,CAACd;YAC9B,CAAC;YACDyB,EAAE,EAAE;cACFC,MAAM,EAAE,SAAAA,OAACC,CAAC;gBAAA,OACRhC,GAAG,CAACiC,YAAY,CACdD,CAAC,CAACE,MAAM,CAACN,KAAK,EACdL,MAAM,CAACH,GAAG,EACVF,GACF,CAAC;cAAA;YACL;UACF,CAAC,CAAC,GACF,CAAClB,GAAG,CAACmC,EAAE,CAACnC,GAAG,CAACoC,EAAE,CAACd,IAAI,CAAC,CAAC,CAAC,CAC3B;QACH;MACF,CAAC;IACH,CAAC,CAAC,EACF;MACEF,GAAG,EAAE,WAAW;MAChBC,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;QACjC,OAAO,CACLD,MAAM,CAACE,QAAQ,GACX,CACEF,MAAM,CAACc,KAAK,GACRpC,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CACA,GAAG,EACH;UACE6B,EAAE,EAAE;YACFQ,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;cACvB,OAAOvC,GAAG,CAACwC,OAAO,CAACjB,MAAM,CAACH,GAAG,CAAC;YAChC;UACF;QACF,CAAC,EACD,CAACpB,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDlC,EAAE,CAAC,WAAW,EAAE;UACdG,KAAK,EAAE;YAAEqC,IAAI,EAAE;UAAW;QAC5B,CAAC,CAAC,EACFxC,EAAE,CACA,cAAc,EACd;UACEG,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAW,CAAC;UAC5ByB,EAAE,EAAE;YACFY,OAAO,EAAE,SAAAA,QAAUH,MAAM,EAAE;cACzB,OAAOvC,GAAG,CAAC2C,MAAM,CAACpB,MAAM,CAACH,GAAG,CAAC;YAC/B;UACF;QACF,CAAC,EACD,CAACnB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC1B,CAAC,CACF,EACD,CACF,CAAC,GACDlC,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CACA,GAAG,EACH;UACE6B,EAAE,EAAE;YACFQ,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;cACvB,OAAOvC,GAAG,CAACwC,OAAO,CAACjB,MAAM,CAACH,GAAG,CAAC;YAChC;UACF;QACF,CAAC,EACD,CAACpB,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDlC,EAAE,CAAC,WAAW,EAAE;UACdG,KAAK,EAAE;YAAEqC,IAAI,EAAE;UAAW;QAC5B,CAAC,CAAC,EACFxC,EAAE,CACA,GAAG,EACH;UACE6B,EAAE,EAAE;YACFQ,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;cACvB,OAAOvC,GAAG,CAAC4C,MAAM,CAACrB,MAAM,CAACH,GAAG,CAAC;YAC/B;UACF;QACF,CAAC,EACD,CAACpB,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACN,GACDlC,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CACA,GAAG,EACH;UACE6B,EAAE,EAAE;YACFQ,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;cACvB,OAAOvC,GAAG,CAAC6C,MAAM,CAACtB,MAAM,CAACH,GAAG,CAAC;YAC/B;UACF;QACF,CAAC,EACD,CAACpB,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDlC,EAAE,CAAC,WAAW,EAAE;UACdG,KAAK,EAAE;YAAEqC,IAAI,EAAE;UAAW;QAC5B,CAAC,CAAC,EACFxC,EAAE,CACA,cAAc,EACd;UACEG,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAW,CAAC;UAC5ByB,EAAE,EAAE;YACFY,OAAO,EAAE,SAAAA,QAAUH,MAAM,EAAE;cACzB,OAAOvC,GAAG,CAAC2C,MAAM,CAACpB,MAAM,CAACH,GAAG,CAAC;YAC/B;UACF;QACF,CAAC,EACD,CAACnB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC1B,CAAC,CACF,EACD,CACF,CAAC,CACN;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,IACF;EACF,CAAC,CAAC,EACFlC,EAAE,CACA,UAAU,EACV;IACEyB,WAAW,EAAE;MACXoB,KAAK,EAAE,MAAM;MACb,YAAY,EAAE,MAAM;MACpB,eAAe,EAAE;IACnB,CAAC;IACD1C,KAAK,EAAE;MAAEqC,IAAI,EAAE,QAAQ;MAAEM,IAAI,EAAE;IAAO,CAAC;IACvCjB,EAAE,EAAE;MAAEQ,KAAK,EAAEtC,GAAG,CAACgD;IAAU;EAC7B,CAAC,EACD,CAAChD,GAAG,CAACmC,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFlC,EAAE,CACA,iBAAiB,EACjB,CACEA,EAAE,CACA,UAAU,EACV;IACEG,KAAK,EAAE;MAAEqC,IAAI,EAAE,SAAS;MAAEQ,OAAO,EAAEjD,GAAG,CAACiD;IAAQ,CAAC;IAChDnB,EAAE,EAAE;MAAEQ,KAAK,EAAEtC,GAAG,CAACkD;IAAS;EAC5B,CAAC,EACD,CAAClD,GAAG,CAACmC,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIgB,eAAe,GAAG,EAAE;AACxBrD,MAAM,CAACsD,aAAa,GAAG,IAAI;AAE3B,SAAStD,MAAM,EAAEqD,eAAe", "ignoreList": []}]}