{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\App.vue?vue&type=template&id=419a116f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\App.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"app-list\">\n  <a-list\n    :grid=\"{ gutter: 24, lg: 3, md: 2, sm: 1, xs: 1 }\"\n    :dataSource=\"dataSource\">\n    <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\n      <a-card :hoverable=\"true\">\n        <a-card-meta>\n          <div style=\"margin-bottom: 3px\" slot=\"title\">{{ item.title }}</div>\n          <a-avatar class=\"card-avatar\" slot=\"avatar\" :src=\"item.avatar\" size=\"small\" icon=\"user\"/>\n          <div class=\"meta-cardInfo\" slot=\"description\">\n            <div>\n              <p>活跃用户</p>\n              <p>\n                <span>{{ item.activeUser }}<span>万</span></span>\n              </p>\n            </div>\n            <div>\n              <p>新增用户</p>\n              <p>{{ item.newUser | NumberFormat }}</p>\n            </div>\n          </div>\n        </a-card-meta>\n        <template class=\"ant-card-actions\" slot=\"actions\">\n          <a>\n            <a-icon type=\"download\"/>\n          </a>\n          <a>\n            <a-icon type=\"edit\"/>\n          </a>\n          <a>\n            <a-icon type=\"share-alt\"/>\n          </a>\n          <a>\n            <a-dropdown>\n              <a class=\"ant-dropdown-link\" href=\"javascript:;\">\n                <a-icon type=\"ellipsis\"/>\n              </a>\n              <a-menu slot=\"overlay\">\n                <a-menu-item>\n                  <a href=\"javascript:;\">1st menu item</a>\n                </a-menu-item>\n                <a-menu-item>\n                  <a href=\"javascript:;\">2nd menu item</a>\n                </a-menu-item>\n                <a-menu-item>\n                  <a href=\"javascript:;\">3rd menu item</a>\n                </a-menu-item>\n              </a-menu>\n            </a-dropdown>\n          </a>\n        </template>\n      </a-card>\n    </a-list-item>\n  </a-list>\n\n</div>\n", null]}