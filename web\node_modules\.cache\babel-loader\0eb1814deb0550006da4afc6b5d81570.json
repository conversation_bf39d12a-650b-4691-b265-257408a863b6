{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step1.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step1.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"Step1\",\n  methods: {\n    nextStep: function nextStep() {\n      this.$emit('nextStep');\n    }\n  }\n};", {"version": 3, "names": ["name", "methods", "nextStep", "$emit"], "sources": ["src/views/form/stepForm/Step1.vue"], "sourcesContent": ["<template>\n  <div>\n    <a-form style=\"max-width: 500px; margin: 40px auto 0;\">\n      <a-form-item\n        label=\"付款账户\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n      >\n        <a-select value=\"1\" placeholder=\"<EMAIL>\">\n          <a-select-option value=\"1\"><EMAIL></a-select-option>\n        </a-select>\n      </a-form-item>\n      <a-form-item\n        label=\"收款账户\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n      >\n        <a-input-group :compact=\"true\" style=\"display: inline-block; vertical-align: middle\">\n          <a-select defaultValue=\"alipay\" style=\"width: 100px\">\n            <a-select-option value=\"alipay\">支付宝</a-select-option>\n            <a-select-option value=\"wexinpay\">微信</a-select-option>\n          </a-select>\n          <a-input :style=\"{width: 'calc(100% - 100px)'}\" value=\"<EMAIL>\"/>\n        </a-input-group>\n      </a-form-item>\n      <a-form-item\n        label=\"收款人姓名\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n      >\n        <a-input value=\"Alex\" />\n      </a-form-item>\n      <a-form-item\n        label=\"转账金额\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n      >\n        <a-input prefix=\"￥\" value=\"5000\" />\n      </a-form-item>\n      <a-form-item :wrapperCol=\"{span: 19, offset: 5}\">\n        <a-button type=\"primary\" @click=\"nextStep\">下一步</a-button>\n      </a-form-item>\n    </a-form>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Step1\",\n    methods: {\n      nextStep () {\n        this.$emit('nextStep')\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AA+CA;EACAA,IAAA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}