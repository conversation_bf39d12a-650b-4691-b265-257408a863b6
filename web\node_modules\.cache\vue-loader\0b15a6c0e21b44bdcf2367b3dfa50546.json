{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  export default {\n    name: '<PERSON><PERSON><PERSON>',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      disabled:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type:Array,\n        required:false,\n        default:()=>['Y','N']\n      }\n    },\n    data () {\n      return {\n        checkStatus: false\n      }\n    },\n    watch: {\n      value:{\n        immediate: true,\n        handler(val){\n          if(!val){\n            this.checkStatus = false\n            this.$emit('change', this.options[1]);\n          }else{\n            if(this.options[0]==val){\n              this.checkStatus = true\n            }else{\n              this.checkStatus = false\n            }\n          }\n        }\n      }\n    },\n    methods: {\n      handleChange(checked){\n        let flag = checked===false?this.options[1]:this.options[0];\n        this.$emit('change', flag);\n      }\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n", {"version": 3, "sources": ["JSwitch.vue"], "names": [], "mappings": ";;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JSwitch.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-switch v-model=\"checkStatus\" :disabled=\"disabled\" @change=\"handleChange\"/>\n</template>\n<script>\n\n  export default {\n    name: 'JSwitch',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      disabled:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type:Array,\n        required:false,\n        default:()=>['Y','N']\n      }\n    },\n    data () {\n      return {\n        checkStatus: false\n      }\n    },\n    watch: {\n      value:{\n        immediate: true,\n        handler(val){\n          if(!val){\n            this.checkStatus = false\n            this.$emit('change', this.options[1]);\n          }else{\n            if(this.options[0]==val){\n              this.checkStatus = true\n            }else{\n              this.checkStatus = false\n            }\n          }\n        }\n      }\n    },\n    methods: {\n      handleChange(checked){\n        let flag = checked===false?this.options[1]:this.options[0];\n        this.$emit('change', flag);\n      }\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n</script>\n"]}]}