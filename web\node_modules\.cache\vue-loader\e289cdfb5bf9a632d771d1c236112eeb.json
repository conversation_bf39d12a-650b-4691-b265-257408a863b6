{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderCustomerModal.vue?vue&type=template&id=1efc7a5b&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderCustomerModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  :okButtonProps=\"{ props: {disabled: disableSubmit} }\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n\n  <!-- 编辑 -->\n  <a-spin :spinning=\"confirmLoading\" v-if=\"editStatus\">\n    <a-form :form=\"form\">\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"客户姓名\"\n        hasFeedback>\n        <a-input placeholder=\"请输入客户姓名\" v-decorator=\"['name', {rules: [{ required: true, message: '请输入客户姓名!' }]}]\"\n                 :readOnly=\"disableSubmit\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"性别\"\n        hasFeedback>\n        <a-select v-decorator=\"['sex', {}]\" placeholder=\"请选择性别\">\n          <a-select-option value=\"1\">男性</a-select-option>\n          <a-select-option value=\"2\">女性</a-select-option>\n        </a-select>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"身份证号码\"\n        hasFeedback>\n        <a-input placeholder=\"请输入身份证号码\" v-decorator=\"['idcard', validatorRules.idcard]\" :readOnly=\"disableSubmit\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"身份证扫描件\"\n        hasFeedback>\n        <j-image-upload text=\"上传\" v-model=\"fileList\" :isMultiple=\"true\"></j-image-upload>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"联系方式\"\n        hasFeedback>\n        <a-input v-decorator=\"[ 'telphone', validatorRules.telphone]\" :readOnly=\"disableSubmit\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单号码\"\n        v-model=\"this.orderId\"\n        :hidden=\"hiding\"\n        hasFeedback>\n        <a-input v-decorator=\"[ 'orderId', {}]\" disabled=\"disabled\"/>\n      </a-form-item>\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}