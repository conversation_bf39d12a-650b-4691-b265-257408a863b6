{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\PdfPreviewModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\PdfPreviewModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n\n  export default {\n    name: \"PdfPreviewModal\",\n    data () {\n      return {\n        url:  window._CONFIG['pdfDomainURL'],\n        id:\"pdfPreviewIframe\",\n        headers:{}\n      }\n    },\n    created () {\n      const token = Vue.ls.get(ACCESS_TOKEN);\n      this.headers = {\"X-Access-Token\":token}\n    },\n    computed:{\n\n    },\n    mounted(){\n      window.addEventListener('message', this.handleScanFileMessage);\n    },\n    methods: {\n      handleScanFileMessage (event) {\n        // 根据上面制定的结构来解析iframe内部发回来的数据\n        const data = event.data;\n         console.log(data);\n      },\n\n      previewFiles (title,token) {\n        var iframe = document.getElementById(\"pdfPreviewIframe\");\n        var json = {\"title\":title,\"token\":token};\n        iframe.contentWindow.postMessage(json, \"*\");\n      },\n\n    }\n  }\n", {"version": 3, "sources": ["PdfPreviewModal.vue"], "names": [], "mappings": ";AAcA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "PdfPreviewModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <div style=\"display: none\">\n    <iframe\n      :id=\"id\"\n      :src=\"url\"\n      frameborder=\"0\"\n      width=\"100%\"\n      height=\"550px\"\n      scrolling=\"auto\">\n    </iframe>\n  </div>\n</template>\n\n<script>\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n\n  export default {\n    name: \"PdfPreviewModal\",\n    data () {\n      return {\n        url:  window._CONFIG['pdfDomainURL'],\n        id:\"pdfPreviewIframe\",\n        headers:{}\n      }\n    },\n    created () {\n      const token = Vue.ls.get(ACCESS_TOKEN);\n      this.headers = {\"X-Access-Token\":token}\n    },\n    computed:{\n\n    },\n    mounted(){\n      window.addEventListener('message', this.handleScanFileMessage);\n    },\n    methods: {\n      handleScanFileMessage (event) {\n        // 根据上面制定的结构来解析iframe内部发回来的数据\n        const data = event.data;\n         console.log(data);\n      },\n\n      previewFiles (title,token) {\n        var iframe = document.getElementById(\"pdfPreviewIframe\");\n        var json = {\"title\":title,\"token\":token};\n        iframe.contentWindow.postMessage(json, \"*\");\n      },\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}