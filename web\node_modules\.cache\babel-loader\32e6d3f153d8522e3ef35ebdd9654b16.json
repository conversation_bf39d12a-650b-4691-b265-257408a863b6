{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue?vue&type=template&id=663767e8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"toolbar\"\n  }, [_c(\"div\", {\n    staticStyle: {\n      float: \"left\"\n    }\n  }, [_vm._t(\"extra\")], 2), _c(\"div\", {\n    staticStyle: {\n      float: \"right\"\n    }\n  }, [_vm._t(\"default\")], 2)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "float", "_t", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/tools/FooterToolBar.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"toolbar\" }, [\n    _c(\"div\", { staticStyle: { float: \"left\" } }, [_vm._t(\"extra\")], 2),\n    _c(\"div\", { staticStyle: { float: \"right\" } }, [_vm._t(\"default\")], 2),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO;EAAE,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACnEL,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAAE,CAAC,EAAE,CAACL,GAAG,CAACM,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBR,MAAM,CAACS,aAAa,GAAG,IAAI;AAE3B,SAAST,MAAM,EAAEQ,eAAe", "ignoreList": []}]}