{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\MyAdditionalWorkList.vue?vue&type=template&id=238f77ea&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\MyAdditionalWorkList.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div :class=\"['container']\">\n  <a-card class=\"search-card\" :bordered=\"false\">\n    <j-dict-select-tag\n      :defaultShowAll=\"true\"\n      type=\"radioButton\"\n      @change=\"handleChangeStatus\"\n      v-model=\"queryParam.status\"\n      :trigger-change=\"true\"\n      :hide-green-status=\"true\"\n      :defaultDictOptions=\"[{title: '全部', text: '全部', description:'', value: ''},\n                      {title: '未提交', text: '未提交', description:'', value: 'false'},\n                      {title: '已提交', text: '已提交', description:'', value: 'true'}]\"\n    />\n  </a-card>\n  <a-divider />\n  <a-card :bordered=\"false\">\n    <a-list item-layout=\"horizontal\" :dataSource=\"datasource\" :pagination=\"pagination\">\n      <a-list-item slot=\"renderItem\" slot-scope=\"work\">\n        <a-list-item-meta>\n          <img class=\"work-cover\" slot=\"avatar\" :src=\"work.workCover_url\" alt=\"\" />\n          <template slot=\"title\" class=\"title\" href=\"#\">\n            <h3>{{ work.workName }} <a-tag color=\"blue\">{{work.codeType_dictText}}</a-tag></h3>\n          </template>\n          <template slot=\"description\">\n            <pre class=\"work-desc\">{{work.workDesc}}</pre>\n            <div class=\"work-info\">\n              <a-tag>班级：{{ work.departName }}</a-tag>\n              <a-divider type=\"vertical\" />\n              <a-tag>老师：{{ work.createBy_dictText }}</a-tag>\n            </div>\n          </template>\n        </a-list-item-meta>\n        <div slot=\"extra\" class=\"btns\">\n          <a-tooltip>\n            <template slot=\"title\">\n              <p>{{work.comment}}</p>\n            </template>\n            <a-rate v-if=\"work.score\" :disabled=\"true\" :value=\"work.score\" />\n          </a-tooltip>\n          <a-button v-if=\"work.workDocumentUrl\" @click=\"openWorkFile(work.workDocumentUrl_url)\">作业资料</a-button>\n          <!-- <a-divider v-if=\"work.workDocumentUrl != null\" type=\"vertical\" /> -->\n          <a-button type=\"primary\" :disabled=\"work.mineWorkStatus > 1\" @click=\"toAdditionalWork(work, false)\"> {{work.mineWorkStatus==null?'去做作业':'修改作业'}} </a-button>\n          <a-divider v-if=\"work.mineWorkStatus != null && work.mineWorkStatus < 2\" type=\"vertical\" />\n          <a-button type=\"primary\" v-if=\"work.mineWorkStatus != null && work.mineWorkStatus < 2\" @click=\"toAdditionalWork(work, true)\"> 重做 </a-button>\n        </div>\n      </a-list-item>\n    </a-list>\n  </a-card>\n  <TeachingWorkSubmitModal ref=\"submitModal\"/>\n</div>\n", null]}