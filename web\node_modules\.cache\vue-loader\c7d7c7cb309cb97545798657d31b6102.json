{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import AreaChartTy from '@/components/chart/AreaChartTy'\n  import Bar from '@/components/chart/Bar'\n  import BarMultid from '@/components/chart/BarMultid'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import Liquid from '@/components/chart/Liquid'\n  import MiniBar from '@/components/chart/MiniBar'\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import Pie from '@/components/chart/Pie'\n  import Radar from '@/components/chart/Radar'\n  import RankList from '@/components/chart/RankList'\n  import TransferBar from '@/components/chart/TransferBar'\n  import Trend from '@/components/chart/Trend'\n  import BarAndLine from '@/components/chart/BarAndLine'\n\n  export default {\n    name: 'ViserChartDemo',\n    components: {\n      Bar, MiniBar, BarMultid, AreaChartTy, LineChartMultid,\n      Pie, Radar, DashChartDemo, MiniProgress, RankList,\n      TransferBar, Trend, Liquid, MiniArea, BarAndLine\n    },\n    data() {\n      return {\n        height: 420,\n        rankList: [],\n        barData: [],\n        areaData: []\n      }\n    },\n    created() {\n      setTimeout(() => {\n        this.loadBarData()\n        this.loadAreaData()\n        this.loadRankListData()\n      }, 100)\n    },\n    methods: {\n      loadData(x, y, max, min, before = '', after = '月') {\n        let data = []\n        for (let i = 0; i < 12; i += 1) {\n          data.push({\n            [x]: `${before}${i + 1}${after}`,\n            [y]: Math.floor(Math.random() * max) + min\n          })\n        }\n        return data\n      },\n      // 加载柱状图数据\n      loadBarData() {\n        this.barData = this.loadData('x', 'y', 1000, 200)\n      },\n      // 加载AreaChartTy的数据\n      loadAreaData() {\n        this.areaData = this.loadData('x', 'y', 500, 100)\n      },\n      loadRankListData() {\n        this.rankList = this.loadData('name', 'total', 2000, 100, '北京朝阳 ', ' 号店')\n      }\n    }\n  }\n", {"version": 3, "sources": ["ViserChartDemo.vue"], "names": [], "mappings": ";AA0EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ViserChartDemo.vue", "sourceRoot": "src/views/jeecg/report", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-tabs defaultActiveKey=\"1\">\n      <!-- 柱状图 -->\n      <a-tab-pane tab=\"柱状图\" key=\"1\">\n        <bar title=\"销售额排行\" :dataSource=\"barData\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 多列柱状图 -->\n      <a-tab-pane tab=\"多列柱状图\" key=\"2\">\n        <bar-multid title=\"多列柱状图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 迷你柱状图 -->\n      <a-tab-pane tab=\"迷你柱状图\" key=\"3\">\n        <mini-bar :dataSource=\"barData\" :width=\"400\" :height=\"200\"/>\n      </a-tab-pane>\n      <!-- 面积图 -->\n      <a-tab-pane tab=\"面积图\" key=\"4\">\n        <area-chart-ty title=\"销售额排行\" :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 迷你面积图 -->\n      <a-tab-pane tab=\"迷你面积图\" key=\"5\">\n        <div style=\"padding-top: 100px;width:600px;height:200px\">\n          <mini-area :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n        </div>\n      </a-tab-pane>\n      <!-- 多行折线图 -->\n      <a-tab-pane tab=\"多行折线图\" key=\"6\">\n        <line-chart-multid title=\"多行折线图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 饼图 -->\n      <a-tab-pane tab=\"饼图\" key=\"7\">\n        <pie title=\"饼图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 雷达图 -->\n      <a-tab-pane tab=\"雷达图\" key=\"8\">\n        <radar title=\"雷达图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 仪表盘 -->\n      <a-tab-pane tab=\"仪表盘\" key=\"9\">\n        <dash-chart-demo title=\"仪表盘\" :value=\"9\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 进度条 -->\n      <a-tab-pane tab=\"进度条\" key=\"10\">\n        <mini-progress :percentage=\"30\" :target=\"40\" :height=\"30\"/>\n        <mini-progress :percentage=\"51\" :target=\"60\" :height=\"30\" color=\"#FFA500\"/>\n        <mini-progress :percentage=\"66\" :target=\"80\" :height=\"30\" color=\"#1E90FF\"/>\n        <mini-progress :percentage=\"74\" :target=\"70\" :height=\"30\" color=\"#FF4500\"/>\n        <mini-progress :percentage=\"92\" :target=\"100\" :height=\"30\" color=\"#49CC49\"/>\n      </a-tab-pane>\n      <!-- 排名列表 -->\n      <a-tab-pane tab=\"排名列表\" key=\"11\">\n        <rank-list title=\"门店销售排行榜\" :list=\"rankList\" style=\"width: 600px;margin: 0 auto;\"/>\n      </a-tab-pane>\n      <!-- TransferBar -->\n      <a-tab-pane tab=\"TransferBar\" key=\"12\">\n        <transfer-bar title=\"年度消耗流量一览表\" :data=\"barData\" x=\"月份\" y=\"流量(Mb)\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- Trend -->\n      <a-tab-pane tab=\"Trend\" key=\"13\">\n        <trend title=\"Trend\" term=\"Trend：\" :percentage=\"30\"/>\n      </a-tab-pane>\n      <!-- Liquid -->\n      <a-tab-pane tab=\"Liquid\" key=\"14\">\n        <liquid :height=\"height\"/>\n      </a-tab-pane>\n      <!-- BarAndLine -->\n      <a-tab-pane tab=\"BarAndLine\" key=\"15\">\n        <bar-and-line :height=\"height\"/>\n      </a-tab-pane>\n    </a-tabs>\n  </a-card>\n</template>\n\n<script>\n  import AreaChartTy from '@/components/chart/AreaChartTy'\n  import Bar from '@/components/chart/Bar'\n  import BarMultid from '@/components/chart/BarMultid'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import Liquid from '@/components/chart/Liquid'\n  import MiniBar from '@/components/chart/MiniBar'\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import Pie from '@/components/chart/Pie'\n  import Radar from '@/components/chart/Radar'\n  import RankList from '@/components/chart/RankList'\n  import TransferBar from '@/components/chart/TransferBar'\n  import Trend from '@/components/chart/Trend'\n  import BarAndLine from '@/components/chart/BarAndLine'\n\n  export default {\n    name: 'ViserChartDemo',\n    components: {\n      Bar, MiniBar, BarMultid, AreaChartTy, LineChartMultid,\n      Pie, Radar, DashChartDemo, MiniProgress, RankList,\n      TransferBar, Trend, Liquid, MiniArea, BarAndLine\n    },\n    data() {\n      return {\n        height: 420,\n        rankList: [],\n        barData: [],\n        areaData: []\n      }\n    },\n    created() {\n      setTimeout(() => {\n        this.loadBarData()\n        this.loadAreaData()\n        this.loadRankListData()\n      }, 100)\n    },\n    methods: {\n      loadData(x, y, max, min, before = '', after = '月') {\n        let data = []\n        for (let i = 0; i < 12; i += 1) {\n          data.push({\n            [x]: `${before}${i + 1}${after}`,\n            [y]: Math.floor(Math.random() * max) + min\n          })\n        }\n        return data\n      },\n      // 加载柱状图数据\n      loadBarData() {\n        this.barData = this.loadData('x', 'y', 1000, 200)\n      },\n      // 加载AreaChartTy的数据\n      loadAreaData() {\n        this.areaData = this.loadData('x', 'y', 500, 100)\n      },\n      loadRankListData() {\n        this.rankList = this.loadData('name', 'total', 2000, 100, '北京朝阳 ', ' 号店')\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n"]}]}