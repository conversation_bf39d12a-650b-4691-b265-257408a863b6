{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\JeecgOrderMainListForJEditableTable.vue?vue&type=style&index=0&id=0c4c4167&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\JeecgOrderMainListForJEditableTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n.ant-card-body .table-operator {\n  margin-bottom: 18px;\n}\n\n.ant-table-tbody .ant-table-row td {\n  padding-top: 15px;\n  padding-bottom: 15px;\n}\n\n.anty-row-operator button {\n  margin: 0 5px\n}\n\n.ant-btn-danger {\n  background-color: #ffffff\n}\n\n.ant-modal-cust-warp {\n  height: 100%\n}\n\n.ant-modal-cust-warp .ant-modal-body {\n  height: calc(100% - 110px) !important;\n  overflow-y: auto\n}\n\n.ant-modal-cust-warp .ant-modal-content {\n  height: 90% !important;\n  overflow-y: hidden\n}\n\n", {"version": 3, "sources": ["JeecgOrderMainListForJEditableTable.vue"], "names": [], "mappings": ";;AA0NA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "JeecgOrderMainListForJEditableTable.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"24\">\n\n          <a-col :md=\"6\" :sm=\"24\">\n            <a-form-item label=\"订单号\">\n              <a-input placeholder=\"请输入订单号\" v-model=\"queryParam.orderCode\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"24\">\n            <a-form-item label=\"订单类型\">\n              <a-select placeholder=\"请输入订单类型\" v-model=\"queryParam.ctype\">\n                <a-select-option value=\"1\">国内订单</a-select-option>\n                <a-select-option value=\"2\">国际订单</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n\n          <a-col :md=\"6\" :sm=\"24\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n            </span>\n          </a-col>\n\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i>\n        <span>已选择</span>\n        <a style=\"font-weight: 600\">\n          {{ selectedRowKeys.length }}\n        </a>\n        <span>项</span>\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n\n          <a-divider type=\"vertical\"/>\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\"/></a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <!-- 表单区域 -->\n    <jeecg-order-modal-for-j-editable-table ref=\"modalForm\" @ok=\"modalFormOk\"/>\n\n  </a-card>\n</template>\n\n<script>\n\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import JeecgOrderModalForJEditableTable from './modules/JeecgOrderModalForJEditableTable'\n\n  export default {\n    name: 'JeecgOrderMainListForJEditableTable',\n    mixins: [JeecgListMixin],\n    components: {\n      JeecgOrderModalForJEditableTable\n    },\n    data() {\n      return {\n        description: '订单管理页面',\n        // 请求参数\n        url: {\n          list: '/test/jeecgOrderMain/list',\n          delete: '/test/jeecgOrderMain/delete',\n          deleteBatch: '/test/jeecgOrderMain/deleteBatch'\n        },\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key: 'rowIndex',\n            width: 60,\n            align: 'center',\n            customRender: function(t, r, index) {\n              return parseInt(index) + 1\n            }\n          },\n          {\n            title: '订单号',\n            align: 'center',\n            dataIndex: 'orderCode'\n          },\n          {\n            title: '订单类型',\n            align: 'center',\n            dataIndex: 'ctype',\n            customRender: (text) => {\n              let re = ''\n              if (text === '1') {\n                re = '国内订单'\n              } else if (text === '2') {\n                re = '国际订单'\n              }\n              return re\n            }\n          },\n          {\n            title: '订单日期',\n            align: 'center',\n            dataIndex: 'orderDate'\n          },\n          {\n            title: '订单金额',\n            align: 'center',\n            dataIndex: 'orderMoney'\n          },\n          {\n            title: '订单备注',\n            align: 'center',\n            dataIndex: 'content'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: 'center',\n            scopedSlots: { customRender: 'action' }\n          }\n        ]\n      }\n    },\n    methods: {\n\n      initDictConfig() {\n      }\n\n    }\n  }\n</script>\n<style scoped>\n\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n\n</style>"]}]}