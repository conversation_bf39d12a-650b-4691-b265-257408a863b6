{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue?vue&type=style&index=0&id=7fb21df8&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.video-area {\n  video {\n    width: 100%;\n    max-height: 500px;\n  }\n}\n/* 注释掉案例播放器样式\n#player {\n  border: none;\n  width: 600px;\n  height: 500px;\n  margin: auto;\n  display: block;\n}\n*/\n.homework-buttons {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 8px;\n}\n// 新增作业选择弹窗样式\n.homework-options {\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n  \n  .ant-btn {\n    min-width: 120px;\n    height: 45px;\n    font-size: 16px;\n  }\n}\n", {"version": 3, "sources": ["UnitViewModal.vue"], "names": [], "mappings": ";AAkRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "UnitViewModal.vue", "sourceRoot": "src/views/account/course/modules", "sourcesContent": ["<template>\n  <div>\n    <j-modal\n      :title=\"'单元 - ' + unit.unitName\"\n      :visible=\"visible\"\n      :width=\"1000\"\n      :fullscreen=\"false\"\n      switchFullscreen\n      @ok=\"handleCancel\"\n      @cancel=\"handleCancel\"\n    >\n      <div class=\"video-area\">\n        <a-tabs v-if=\"unit.courseVideo || unit.courseCase || unit.mediaContent\">\n          <a-tab-pane key=\"video\" tab=\"视频\" v-if=\"unit.courseVideo\">\n            <video v-if=\"unit.courseVideoSource==1\" :src=\"unit.courseVideo_url\" controls=\"true\" controlsList='nodownload noremote footbar' oncontextmenu=\"return false;\"></video>\n            <video v-if=\"unit.courseVideoSource==2\" :src=\"unit.courseVideo\" controls=\"true\" controlsList='nodownload noremote footbar' oncontextmenu=\"return false;\"></video>\n            <div v-if=\"unit.courseVideoSource==3\" v-html=\"unit.courseVideo\"></div>\n          </a-tab-pane>\n          <!-- 注释掉案例Tab页 \n          <a-tab-pane key=\"scratch\" tab=\"案例\" v-if=\"unit.courseCase\">\n            <iframe id=\"player\" :src=\"previewCourseCase(unit)\" scrolling=\"yes\"></iframe>\n          </a-tab-pane>\n          -->\n          <a-tab-pane key=\"media\" tab=\"课程内容\" v-if=\"unit.mediaContent\">\n            <div v-html=\"unit.mediaContent\"></div>\n          </a-tab-pane>\n        </a-tabs>\n      </div>\n      <a-divider>本节课资料</a-divider>\n      <div>\n        <a-row :gutter=\"24\">\n          <a-col :span=\"16\">\n            <a-card size=\"small\" title=\"课程说明\">\n              <div v-html=\"unit.unitIntro ? unit.unitIntro.replace(/\\n/g, '<br>') : ''\"></div>\n            </a-card>\n          </a-col>\n          <a-col :span=\"8\">\n            <a-collapse defaultActiveKey=\"0\" :bordered=\"false\">\n              <template v-slot:expandIcon=\"props\">\n                <a-icon type=\"caret-right\" :rotate=\"props.isActive ? 90 : 0\" />\n              </template>\n              <a-collapse-panel v-if=\"unit.courseWork_url\" key=\"0\" :header=\"'课后作业'\" :style=\"customStyle\">\n                <div class=\"homework-buttons\">\n                  <a-button @click=\"showHomeworkOptions(unit)\" type=\"primary\" icon=\"edit\">去做作业</a-button>\n                  <!-- 暂时隐藏下载作业按钮 -->\n                  <!-- <a-button v-if=\"unit.courseWork_url\" @click=\"downloadWorkDoc(unit.courseWork_url)\" icon=\"download\">下载作业</a-button> -->\n                </div>\n              </a-collapse-panel>\n              <a-collapse-panel v-if=\"unit.coursePpt_url\" :header=\"'课程讲义'\" :style=\"customStyle\">\n                <div v-for=\"(u,i) in unit.coursePpt_url.split(',')\" :key=\"i\">\n                  <!-- <a v-else-if=\"u.endsWith('ppt')||u.endsWith('pptx')\" target=\"_blank\" :href=\"'https://view.officeapps.live.com/op/embed.aspx?src='+u\"\n                    ><a-icon type=\"file-ppt\" /> {{(i+1)}}. 查看PPT </a\n                  > -->\n                  <a target=\"_blank\" :href=\"u\"\n                    ><a-icon type=\"file-pdf\" /> {{(i+1)}}. 下载讲义</a\n                  >\n                </div>\n              </a-collapse-panel>\n              <a-collapse-panel v-if=\"unit.coursePlan_url\" :header=\"'课程代码'\" :style=\"customStyle\">\n                <div v-for=\"(u,i) in unit.coursePlan_url.split(',')\" :key=\"i\">\n                 <a v-if=\"u.endsWith('sb3')\" target=\"_blank\" :href=\"'/scratch3/index.html?scene=create&workFile='+u\"\n                    ><a-icon type=\"code\" /> {{(i+1)}}. 查看Scratch代码 </a\n                  >\n                 <!-- 添加对Python文件的支持 -->\n                 <a v-else-if=\"u.endsWith('py')\" target=\"_blank\" :href=\"'/python/index.html?lang=python&url='+u\"\n                    ><a-icon type=\"code\" /> {{(i+1)}}. 查看Python代码 </a\n                  >\n                 <!-- 添加对C++文件的支持 -->\n                 <a v-else-if=\"u.endsWith('cpp')\" target=\"_blank\" :href=\"'/cpp/index.html?url='+u\"\n                    ><a-icon type=\"code\" /> {{(i+1)}}. 查看C++代码 </a\n                  >\n                </div>\n              </a-collapse-panel>\n            </a-collapse>\n          </a-col>\n        </a-row>\n      </div>\n    </j-modal>\n\n    <!-- 新增的作业选择弹窗 -->\n    <a-modal\n      title=\"选择作业类型\"\n      :visible=\"homeworkOptionsVisible\"\n      :footer=\"null\"\n      @cancel=\"homeworkOptionsVisible = false\"\n      width=\"400px\"\n      centered\n    >\n      <div class=\"homework-options\">\n        <a-button type=\"primary\" size=\"large\" icon=\"form\" @click=\"handleObjectiveExercise\">客观题</a-button>\n        <a-button type=\"primary\" size=\"large\" icon=\"code\" disabled @click=\"handleProgrammingExercise\">\n          编程题\n          <a-tooltip placement=\"bottom\">\n            <template slot=\"title\">\n              <span>该功能暂未开放</span>\n            </template>\n            <a-icon type=\"info-circle\" style=\"margin-left: 5px;\" />\n          </a-tooltip>\n        </a-button>\n      </div>\n    </a-modal>\n    \n    <!-- 客观题弹窗组件 -->\n    <objective-quiz-modal \n      :visible=\"objectiveQuizVisible\"\n      :unitId=\"currentUnitId\"\n      :courseId=\"currentCourseId\"\n      @close=\"objectiveQuizVisible = false\"\n    />\n    \n    <!-- 客观题答题记录详情弹窗 -->\n    <objective-quiz-record-detail-modal ref=\"recordDetailModal\" />\n  </div>\n</template>\n<script>\n\nimport { getFileAccessHttpUrl } from '@/api/manage'\nimport { getAction } from '@/api/manage'\nimport ObjectiveQuizModal from '@/views/teaching/ObjectiveQuizModal'\nimport ObjectiveQuizRecordDetailModal from '@/views/teaching/modules/ObjectiveQuizRecordDetailModal'\n\nexport default {\n  name: 'UnitViewModal',\n  components: {\n    ObjectiveQuizModal,\n    ObjectiveQuizRecordDetailModal\n  },\n  data() {\n    return {\n      customStyle: 'background: #f7f7f7;border-radius: 4px;margin-bottom: 24px;border: 0;overflow: hidden',\n      visible: false,\n      unit: {},\n      scratchFrameHref: '',\n      homeworkOptionsVisible: false, // 新增作业选择弹窗的显示状态\n      currentUnit: null, // 新增当前单元对象\n      objectiveQuizVisible: false, // 客观题弹窗显示状态\n      currentUnitId: '', // 当前单元ID\n      currentCourseId: '', // 当前课程ID\n      hasCompletedQuiz: false, // 是否已完成客观题作业\n      checkingCompletion: false // 是否正在检查完成状态\n    }\n  },\n  mounted(){\n    //scratch全屏\n    document.addEventListener('scratchFullScreen', function (e) {\n      window.launchIntoFullscreen(document.getElementById('player'))\n    })\n    //scratch退出全屏\n    document.addEventListener('scratchUnFullScreen', function (e) {\n      window.exitFullscreen()\n    })\n    document.addEventListener('scratchInit', function (e) {\n      var p = document.getElementById('player')\n      var s = p.contentDocument.getElementById('scratch')\n      s.addEventListener('click', () => {\n        p.focus()\n      })\n    })\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    handleCancel(e) {\n      this.unit = {}\n      this.visible = false\n    },\n    /* 注释掉课程案例预览相关方法\n    previewCourseCase(unit) {\n      let url = this.getFileAccessHttpUrl(unit.courseCase)\n      switch(unit.courseWorkType){\n        case 1:\n          return '/scratch3/player.html?workUrl=' + url\n        case 2:\n          return '/scratch3/player.html?workUrl=' + url\n        case 3:\n          return '/scratchjr/editor.html?mode=edit&workFile=' + url\n        case 4:\n          return '/python/player.html?lang=turtle&url='+ url\n        case 5: // 添加C++支持\n          return '/cpp/player.html?url='+ url\n      }\n    },\n    */\n    // 显示作业选择弹窗\n    showHomeworkOptions(unit) {\n      this.currentUnit = unit;\n      this.homeworkOptionsVisible = true;\n    },\n    // 处理客观题按钮点击\n    handleObjectiveExercise() {\n      this.homeworkOptionsVisible = false;\n      \n      // 获取该单元的信息\n      this.currentUnitId = this.currentUnit.id;\n      this.currentCourseId = this.currentUnit.courseId;\n      \n      // 检查是否已完成该单元的客观题作业\n      this.checkQuizCompletion(this.currentUnitId, this.currentCourseId);\n    },\n    // 检查是否已完成客观题作业\n    checkQuizCompletion(unitId, courseId) {\n      if (this.checkingCompletion) return;\n      \n      this.checkingCompletion = true;\n      \n      getAction(\"/teaching/objectiveQuizRecord/listByStudent\", {\n        unitId: unitId,\n        courseId: courseId,\n        pageNo: 1,\n        pageSize: 1\n      }).then(res => {\n        if (res.success && res.result.records && res.result.records.length > 0) {\n          // 已完成，显示答题记录详情\n          this.hasCompletedQuiz = true;\n          this.showQuizRecordDetail(unitId, courseId);\n        } else {\n          // 未完成，显示答题弹窗\n          this.hasCompletedQuiz = false;\n          this.objectiveQuizVisible = true;\n        }\n      }).catch(err => {\n        console.error(\"检查客观题完成状态失败\", err);\n        // 出错时默认显示答题弹窗\n        this.objectiveQuizVisible = true;\n      }).finally(() => {\n        this.checkingCompletion = false;\n      });\n    },\n    // 显示答题记录详情\n    showQuizRecordDetail(unitId, courseId) {\n      this.$refs.recordDetailModal.show(unitId, courseId);\n    },\n    // 处理编程题按钮点击 - 复用原有逻辑\n    handleProgrammingExercise() {\n      // 功能暂时禁用，不执行任何操作\n      this.$message.warning('编程题功能暂未开放，敬请期待！');\n      // 不关闭弹窗，让用户选择其他选项\n      // this.homeworkOptionsVisible = false;\n      // if (this.currentUnit) {\n      //   this.handleViewCode(this.currentUnit);\n      // }\n    },\n    // 原有的作业处理逻辑保持不变\n    handleViewCode(unit) {\n      switch (unit.courseWorkType) {\n        case 1:\n          window.open('/scratch3/index.html?scene=course&unitId='+unit.id)\n          break\n        case 2:\n          window.open('/scratch3/index.html?scene=course&unitId='+unit.id)\n          break\n        case 3:\n          window.open('/scratchjr/editor.html?scene=course&mode=edit&workFile=' + this.getFileAccessHttpUrl(unit.courseWork))\n          break\n        case 4:\n          window.open('/python/index.html?scene=course&lang=turtle&unitId='+unit.id + \"&url=\" + this.getFileAccessHttpUrl(unit.courseWork))\n          break\n        case 5: // 添加C++支持\n          window.open('/cpp/index.html?scene=course&unitId='+unit.id + \"&url=\" + this.getFileAccessHttpUrl(unit.courseWork))\n          break\n        default:\n          window.open(this.getFileAccessHttpUrl(unit.mediaPath))\n      }\n    },\n    // downloadWorkDoc(url) {\n    //   if(url.endsWith('ppt')||url.endsWith('pptx')){\n    //     window.open('https://view.officeapps.live.com/op/embed.aspx?src='+url)\n    //   } else {\n    //     window.open(url)\n    //   }\n    // }\n  },\n}\n</script>\n<style lang=\"less\" scoped>\n.video-area {\n  video {\n    width: 100%;\n    max-height: 500px;\n  }\n}\n/* 注释掉案例播放器样式\n#player {\n  border: none;\n  width: 600px;\n  height: 500px;\n  margin: auto;\n  display: block;\n}\n*/\n.homework-buttons {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 8px;\n}\n// 新增作业选择弹窗样式\n.homework-options {\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n  \n  .ant-btn {\n    min-width: 120px;\n    height: 45px;\n    font-size: 16px;\n  }\n}\n</style>\n"]}]}