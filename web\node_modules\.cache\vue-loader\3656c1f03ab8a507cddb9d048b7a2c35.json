{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { httpAction } from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgDemoModal\",\n    data () {\n      return {\n        title:\"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules:{\n        },\n        url: {\n          add: \"/test/jeecgDemo/add\",\n          edit: \"/test/jeecgDemo/edit\",\n        },\n      }\n    },\n    created () {\n    },\n    methods: {\n      add () {\n        this.edit({});\n      },\n      edit (record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model,'name','keyWord','sex','age','email','content'))\n\t\t      //时间格式化\n          this.form.setFieldsValue({punchTime:this.model.punchTime?moment(this.model.punchTime,'YYYY-MM-DD HH:mm:ss'):null})\n          this.form.setFieldsValue({birthday:this.model.birthday?moment(this.model.birthday):null})\n        });\n\n      },\n      close () {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if(!this.model.id){\n              httpurl+=this.url.add;\n              method = 'post';\n            }else{\n              httpurl+=this.url.edit;\n               method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.punchTime = formData.punchTime?formData.punchTime.format('YYYY-MM-DD HH:mm:ss'):null;\n            formData.birthday = formData.birthday?formData.birthday.format():null;\n            \n            console.log(formData)\n            httpAction(httpurl,formData,method).then((res)=>{\n              if(res.success){\n                that.$message.success(res.message);\n                that.$emit('ok');\n              }else{\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n\n          }\n        })\n      },\n      handleCancel () {\n        this.close()\n      },\n\n\n    }\n  }\n", {"version": 3, "sources": ["JeecgDemoModal.vue"], "names": [], "mappings": ";AAgFA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA", "file": "JeecgDemoModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n    \n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n      \n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"姓名\"\n          hasFeedback >\n          <a-input placeholder=\"请输入姓名\" v-decorator=\"['name', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"关键词\"\n          hasFeedback >\n          <a-input placeholder=\"请输入关键词\" v-decorator=\"['keyWord', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"打卡时间\"\n          hasFeedback >\n          <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" v-decorator=\"[ 'punchTime', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"性别\">\n         <!-- <a-select v-decorator=\"['sex', {}]\" placeholder=\"请选择性别\">\n            <a-select-option value=\"\">请选择</a-select-option>\n            <a-select-option value=\"1\">男</a-select-option>\n            <a-select-option value=\"2\">女</a-select-option>\n          </a-select>-->\n          <j-dict-select-tag type=\"radio\" v-decorator=\"['sex', {}]\" :trigger-change=\"true\" dictCode=\"sex\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"年龄\"\n          hasFeedback >\n          <a-input placeholder=\"请输入年龄\" v-decorator=\"['age', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"生日\"\n          hasFeedback >\n          <a-date-picker v-decorator=\"[ 'birthday', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"邮箱\"\n          hasFeedback >\n          <a-input placeholder=\"请输入邮箱\" v-decorator=\"['email', {}]\" />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"个人简介\"\n          hasFeedback >\n          <a-input placeholder=\"请输入个人简介\" v-decorator=\"['content', {}]\" />\n        </a-form-item>\n\t\t\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { httpAction } from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgDemoModal\",\n    data () {\n      return {\n        title:\"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules:{\n        },\n        url: {\n          add: \"/test/jeecgDemo/add\",\n          edit: \"/test/jeecgDemo/edit\",\n        },\n      }\n    },\n    created () {\n    },\n    methods: {\n      add () {\n        this.edit({});\n      },\n      edit (record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model,'name','keyWord','sex','age','email','content'))\n\t\t      //时间格式化\n          this.form.setFieldsValue({punchTime:this.model.punchTime?moment(this.model.punchTime,'YYYY-MM-DD HH:mm:ss'):null})\n          this.form.setFieldsValue({birthday:this.model.birthday?moment(this.model.birthday):null})\n        });\n\n      },\n      close () {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if(!this.model.id){\n              httpurl+=this.url.add;\n              method = 'post';\n            }else{\n              httpurl+=this.url.edit;\n               method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.punchTime = formData.punchTime?formData.punchTime.format('YYYY-MM-DD HH:mm:ss'):null;\n            formData.birthday = formData.birthday?formData.birthday.format():null;\n            \n            console.log(formData)\n            httpAction(httpurl,formData,method).then((res)=>{\n              if(res.success){\n                that.$message.success(res.message);\n                that.$emit('ok');\n              }else{\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n\n          }\n        })\n      },\n      handleCancel () {\n        this.close()\n      },\n\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}