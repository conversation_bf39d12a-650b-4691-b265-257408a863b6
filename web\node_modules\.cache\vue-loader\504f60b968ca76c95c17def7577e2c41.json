{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\IndexBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\IndexBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  const data = []\n  for (let i = 0; i < 12; i += 1) {\n    data.push({\n      x: `${i + 1}月`,\n      y: Math.floor(Math.random() * 1000) + 200\n    })\n  }\n  const tooltip = [\n    'x*y',\n    (x, y) => ({\n      name: x,\n      value: y\n    })\n  ]\n  const scale = [{\n    dataKey: 'x',\n    min: 2\n  }, {\n    dataKey: 'y',\n    title: '时间',\n    min: 1,\n    max: 22\n  }]\n\n  export default {\n    name: \"Bar\",\n    props: {\n      title: {\n        type: String,\n        default: ''\n      }\n    },\n    mounted(){\n      this.datasource = data\n    },\n    data () {\n      return {\n        datasource:[],\n        scale,\n        tooltip\n      }\n    }\n  }\n", {"version": 3, "sources": ["IndexBar.vue"], "names": [], "mappings": ";;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexBar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart\n      height=\"254\"\n      :data=\"datasource\"\n      :forceFit=\"true\"\n      :padding=\"['auto', 'auto', '40', '50']\">\n      <v-tooltip />\n      <v-axis />\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n\n  const data = []\n  for (let i = 0; i < 12; i += 1) {\n    data.push({\n      x: `${i + 1}月`,\n      y: Math.floor(Math.random() * 1000) + 200\n    })\n  }\n  const tooltip = [\n    'x*y',\n    (x, y) => ({\n      name: x,\n      value: y\n    })\n  ]\n  const scale = [{\n    dataKey: 'x',\n    min: 2\n  }, {\n    dataKey: 'y',\n    title: '时间',\n    min: 1,\n    max: 22\n  }]\n\n  export default {\n    name: \"Bar\",\n    props: {\n      title: {\n        type: String,\n        default: ''\n      }\n    },\n    mounted(){\n      this.datasource = data\n    },\n    data () {\n      return {\n        datasource:[],\n        scale,\n        tooltip\n      }\n    }\n  }\n</script>"]}]}