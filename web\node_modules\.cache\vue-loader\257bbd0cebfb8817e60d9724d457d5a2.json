{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\Keyboard.vue?vue&type=style&index=0&id=16bad24b&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\Keyboard.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n/* 游戏键盘 */\n.keyboard {\n  /* display: flex; */\n  /* height: 5rem; */\n  margin: 0 0.6rem;\n  border-radius: 5px;\n}\n\n.control_direct {\n  -webkit-box-flex: 1.5;\n  /* OLD - iOS 6-, Safari 3.1-6 */\n  -moz-box-flex: 1.5;\n  /* OLD - Firefox 19- */\n  -webkit-flex: 1.5;\n  /* Chrome */\n  -ms-flex: 1.5;\n  flex: 1.5;\n}\n\n.control_space {\n  -webkit-box-flex: 1;\n  /* OLD - iOS 6-, Safari 3.1-6 */\n  -moz-box-flex: 1;\n  /* OLD - Firefox 19- */\n  -webkit-flex: 1;\n  /* Chrome */\n  -ms-flex: 1;\n  flex: 1;\n  text-align: center;\n  vertical-align: middle;\n}\n\n.control_direct div {\n  text-align: center;\n  height: 2.25rem;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.key {\n  width: 2.25rem;\n  height: 2.25rem;\n  display: inline-block;\n  background-size: 100% 100% !important;\n}\n\n.control_direct div:nth-of-type(1) {\n  margin-top: 0.18rem;\n}\n\n.control_direct div:nth-of-type(3) {\n  margin-bottom: 1rem;\n}\n\n.control_direct div:nth-of-type(2) p:nth-of-type(1) {\n  margin-right: 2rem;\n}\n\n.rotate_left {\n  transform: rotate(-90deg);\n  -webkit-transform: rotate(-90deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(-90deg);\n  /* for Firefox */\n  -ms-transform: rotate(-90deg);\n  /* for IE */\n  -o-transform: rotate(-90deg);\n  /* for Opera */\n}\n\n.rotate_right {\n  transform: rotate(90deg);\n  -webkit-transform: rotate(90deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(90deg);\n  /* for Firefox */\n  -ms-transform: rotate(90deg);\n  /* for IE */\n  -o-transform: rotate(90deg);\n  /* for Opera */\n}\n\n.rotate_down {\n  transform: rotate(180deg);\n  -webkit-transform: rotate(180deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(180deg);\n  /* for Firefox */\n  -ms-transform: rotate(180deg);\n  /* for IE */\n  -o-transform: rotate(180deg);\n  /* for Opera */\n}\n\n.control_space {\n  text-align: center;\n}\n\n.control_space p {\n  display: inline-block;\n  background: url(\"/scratch3/image/space.png\") no-repeat center center;\n  background-size: contain;\n  width: 5.67rem;\n  height: 5.67rem;\n  margin-top: 1.3rem;\n}\n", {"version": 3, "sources": ["Keyboard.vue"], "names": [], "mappings": ";AAoKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Keyboard.vue", "sourceRoot": "src/components/tools", "sourcesContent": ["<template>\n  <div class=\"keyboard\">\n    <a-row class=\"control_A flex\" v-if=\"type == 1\">\n      <a-col :span=\"12\" class=\"control_direct\">\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowUp', 38)\"\n            @touchend=\"keyup($event, 'ArrowUp', 38)\"\n            class=\"key button_up\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowLeft', 37)\"\n            @touchend=\"keyup($event, 'ArrowLeft', 37)\"\n            class=\"key rotate_left button_left\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n          <p\n            @touchstart=\"keydown($event, 'ArrowRight', 39)\"\n            @touchend=\"keyup($event, 'ArrowRight', 39)\"\n            class=\"key rotate_right button_right\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowDown', 40)\"\n            @touchend=\"keyup($event, 'ArrowDown', 40)\"\n            class=\"key rotate_down button_down\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n      </a-col>\n      <a-col :span=\"12\" class=\"control_space\">\n        <p\n          @touchstart=\"keydown($event, ' ', 32)\"\n          @touchend=\"keyup($event, ' ', 32)\"\n          class=\"space button_space\"\n          id=\"\"\n        ></p>\n      </a-col>\n    </a-row>\n    <a-row class=\"control_B flex\" v-if=\"type == 2\">\n      <a-col :span=\"12\" class=\"control_direct\">\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'w', 87)\"\n            @touchend=\"keyup($event, 'w', 87)\"\n            class=\"key button_w\"\n            id=\"\"\n            style=\"background: url('/scratch3/image/w.png') no-repeat left top\"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'a', 65)\"\n            @touchend=\"keyup($event, 'a', 65)\"\n            class=\"key button_a\"\n            id=\"\"\n            style=\"background: url('/scratch3/image/a.png') no-repeat left top\"\n          ></p>\n          <p\n            @touchstart=\"keydown($event, 'd', 68)\"\n            @touchend=\"keyup($event, 'd', 68)\"\n            class=\"key button_d\"\n            id=\"\"\n            style=\"background: url('/scratch3/image/d.png') no-repeat left top\"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 's', 83)\"\n            @touchend=\"keyup($event, 's', 83)\"\n            class=\"key button_s\"\n            id=\"\"\n            style=\"background: url('/scratch3/image/s.png') no-repeat left top\"\n          ></p>\n        </div>\n      </a-col>\n      <a-col :span=\"12\" class=\"control_direct\">\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowUp', 38)\"\n            @touchend=\"keyup($event, 'ArrowUp', 38)\"\n            class=\"key button_up\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowLeft', 37)\"\n            @touchend=\"keyup($event, 'ArrowLeft', 37)\"\n            class=\"key rotate_left button_left\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n          <p\n            @touchstart=\"keydown($event, 'ArrowRight', 39)\"\n            @touchend=\"keyup($event, 'ArrowRight', 39)\"\n            class=\"key rotate_right button_right\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n        <div>\n          <p\n            @touchstart=\"keydown($event, 'ArrowDown', 40)\"\n            @touchend=\"keyup($event, 'ArrowDown', 40)\"\n            class=\"key rotate_down button_down\"\n            id=\"\"\n            style=\"\n              background: url('/scratch3/image/arrow.png') no-repeat left top;\n            \"\n          ></p>\n        </div>\n      </a-col>\n    </a-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Keyboard\",\n  props: {\n    type: {\n      type: Number,\n      default: 1,\n    },\n  },\n  methods: {\n    keydown(e, key, keyCode) {\n      this.$emit('event', key, keyCode, true)\n      e.preventDefault();\n    },\n    keyup(e, key, keyCode) {\n      this.$emit('event', key, keyCode, false)\n      e.preventDefault();\n    },\n  },\n};\n</script>\n\n<style scoped>\n/* 游戏键盘 */\n.keyboard {\n  /* display: flex; */\n  /* height: 5rem; */\n  margin: 0 0.6rem;\n  border-radius: 5px;\n}\n\n.control_direct {\n  -webkit-box-flex: 1.5;\n  /* OLD - iOS 6-, Safari 3.1-6 */\n  -moz-box-flex: 1.5;\n  /* OLD - Firefox 19- */\n  -webkit-flex: 1.5;\n  /* Chrome */\n  -ms-flex: 1.5;\n  flex: 1.5;\n}\n\n.control_space {\n  -webkit-box-flex: 1;\n  /* OLD - iOS 6-, Safari 3.1-6 */\n  -moz-box-flex: 1;\n  /* OLD - Firefox 19- */\n  -webkit-flex: 1;\n  /* Chrome */\n  -ms-flex: 1;\n  flex: 1;\n  text-align: center;\n  vertical-align: middle;\n}\n\n.control_direct div {\n  text-align: center;\n  height: 2.25rem;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.key {\n  width: 2.25rem;\n  height: 2.25rem;\n  display: inline-block;\n  background-size: 100% 100% !important;\n}\n\n.control_direct div:nth-of-type(1) {\n  margin-top: 0.18rem;\n}\n\n.control_direct div:nth-of-type(3) {\n  margin-bottom: 1rem;\n}\n\n.control_direct div:nth-of-type(2) p:nth-of-type(1) {\n  margin-right: 2rem;\n}\n\n.rotate_left {\n  transform: rotate(-90deg);\n  -webkit-transform: rotate(-90deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(-90deg);\n  /* for Firefox */\n  -ms-transform: rotate(-90deg);\n  /* for IE */\n  -o-transform: rotate(-90deg);\n  /* for Opera */\n}\n\n.rotate_right {\n  transform: rotate(90deg);\n  -webkit-transform: rotate(90deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(90deg);\n  /* for Firefox */\n  -ms-transform: rotate(90deg);\n  /* for IE */\n  -o-transform: rotate(90deg);\n  /* for Opera */\n}\n\n.rotate_down {\n  transform: rotate(180deg);\n  -webkit-transform: rotate(180deg);\n  /* for Chrome || Safari */\n  -moz-transform: rotate(180deg);\n  /* for Firefox */\n  -ms-transform: rotate(180deg);\n  /* for IE */\n  -o-transform: rotate(180deg);\n  /* for Opera */\n}\n\n.control_space {\n  text-align: center;\n}\n\n.control_space p {\n  display: inline-block;\n  background: url(\"/scratch3/image/space.png\") no-repeat center center;\n  background-size: contain;\n  width: 5.67rem;\n  height: 5.67rem;\n  margin-top: 1.3rem;\n}\n</style>"]}]}