{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue", "mtime": 1753199398217}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { mapActions, mapGetters } from 'vuex';\nimport TMenu from '@/components/menu/tmenu';\nimport { getFileAccessHttpUrl } from \"@/api/manage\";\nimport SoftwareDownload from './SoftwareDownload';\nimport ShoppingModal from './ShoppingModal';\nimport HeaderNotice from '@/components/tools/HeaderNotice';\nexport default {\n  components: {\n    TMenu: TMenu,\n    SoftwareDownload: SoftwareDownload,\n    ShoppingModal: ShoppingModal,\n    HeaderNotice: HeaderNotice\n  },\n  data: function data() {\n    return {\n      menus: [],\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      menuFixed: false\n    };\n  },\n  created: function created() {\n    this.menus = this.$store.getters.menuList;\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo;\n      this.avatarUrl = this.logo;\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2;\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n      this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar;\n    }\n    if (this.getFileAccessHttpUrl(this.avatar())) {\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar());\n    }\n\n    // 监听软件下载菜单点击事件\n    window.addEventListener('showSoftwareDownload', this.showSoftwareDownload);\n    // 监听商城菜单点击事件\n    window.addEventListener('showShoppingModal', this.showShoppingModal);\n  },\n  mounted: function mounted() {\n    window.addEventListener('scroll', this.handleScroll);\n  },\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n    // 移除软件下载菜单点击事件监听\n    window.removeEventListener('showSoftwareDownload', this.showSoftwareDownload);\n    // 移除商城菜单点击事件监听\n    window.removeEventListener('showShoppingModal', this.showShoppingModal);\n  },\n  methods: _objectSpread(_objectSpread(_objectSpread({}, mapActions([\"Logout\"])), mapGetters(['nickname', 'avatar', 'userInfo'])), {}, {\n    getFileAccessHttpUrl: getFileAccessHttpUrl,\n    handleScroll: function handleScroll() {\n      var scrollTop = document.documentElement.scrollTop;\n      if (scrollTop >= 105) {\n        this.menuFixed = true;\n      } else {\n        this.menuFixed = false;\n      }\n    },\n    enter: function enter() {\n      this.$router.push('/account/settings/base');\n    },\n    handleLogout: function handleLogout() {\n      var that = this;\n      this.$confirm({\n        title: \"提示\",\n        content: \"真的要注销登录吗 ?\",\n        onOk: function onOk() {\n          return that.Logout({}).then(function () {\n            window.location.reload();\n          }).catch(function (err) {\n            that.$message.error({\n              title: \"错误\",\n              description: err.message\n            });\n          });\n        },\n        onCancel: function onCancel() {}\n      });\n    },\n    showSoftwareDownload: function showSoftwareDownload() {\n      this.$refs.softwareDownload.showModal();\n    },\n    showShoppingModal: function showShoppingModal() {\n      this.$refs.shoppingModal.showModal();\n    }\n  })\n};", {"version": 3, "names": ["mapActions", "mapGetters", "TMenu", "getFileAccessHttpUrl", "SoftwareDownload", "ShoppingModal", "HeaderNotice", "components", "data", "menus", "logo", "logo2", "avatarUrl", "menuFixed", "created", "$store", "getters", "menuList", "sysConfig", "qiniuDomain", "avatar", "window", "addEventListener", "showSoftwareDownload", "showShoppingModal", "mounted", "handleScroll", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "_objectSpread", "scrollTop", "document", "documentElement", "enter", "$router", "push", "handleLogout", "that", "$confirm", "title", "content", "onOk", "Logout", "then", "location", "reload", "catch", "err", "$message", "error", "description", "message", "onCancel", "$refs", "softwareDownload", "showModal", "shoppingModal"], "sources": ["src/views/home/<USER>/Header.vue"], "sourcesContent": ["<template>\n  <div :class=\"['header', menuFixed?'menu-fixed':'']\">\n    <router-link :to=\"{ path: '/home' }\">\n      <img class=\"logo\" :src=\"logo\" alt=\"\" />\n    </router-link>\n    <t-menu class=\"menu\" mode=\"horizontal\" :menu=\"menus\"></t-menu>\n    <div class=\"header-avatar\">\n      <header-notice class=\"action\"/>\n      <img class=\"avatar\" :src=\"avatarUrl\" @click=\"enter\" alt=\"\" />\n      <span v-if=\"$store.state.user.info\">\n        <span @click=\"enter\">{{ $store.state.user.info.realname }}</span>\n        <a-divider type=\"vertical\" />\n        <span @click=\"handleLogout\">退出</span>\n      </span>\n      <span v-else>\n        <span @click=\"enter\">登录</span>\n        <a-divider type=\"vertical\" />\n        <span @click=\"enter\">注册</span>\n      </span>\n    </div>\n    \n    <software-download ref=\"softwareDownload\" />\n    <shopping-modal ref=\"shoppingModal\" />\n  </div>\n</template>\n<script>\nimport { mapActions, mapGetters } from 'vuex'\nimport TMenu from '@/components/menu/tmenu'\nimport { getFileAccessHttpUrl } from \"@/api/manage\"\nimport SoftwareDownload from './SoftwareDownload'\nimport ShoppingModal from './ShoppingModal'\nimport HeaderNotice from '@/components/tools/HeaderNotice'\n\nexport default {\n  components: {\n    TMenu,\n    SoftwareDownload,\n    ShoppingModal,\n    HeaderNotice\n  },\n  data() {\n    return {\n      menus: [],\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      menuFixed: false\n    }\n  },\n  created() {\n    this.menus = this.$store.getters.menuList\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo\n      this.avatarUrl = this.logo\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n       this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if(this.getFileAccessHttpUrl(this.avatar())){\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    } \n\n    // 监听软件下载菜单点击事件\n    window.addEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 监听商城菜单点击事件\n    window.addEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  mounted() {\n    window.addEventListener('scroll', this.handleScroll)\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n    // 移除软件下载菜单点击事件监听\n    window.removeEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 移除商城菜单点击事件监听\n    window.removeEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  methods:{\n    ...mapActions([\"Logout\"]),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getFileAccessHttpUrl,\n    handleScroll(){\n      let scrollTop = document.documentElement.scrollTop\n      if (scrollTop >= 105) {\n        this.menuFixed = true\n      } else {\n        this.menuFixed = false\n      }\n    },\n    enter() {\n      this.$router.push('/account/settings/base')\n    },\n    handleLogout() {\n      const that = this;\n      this.$confirm({\n        title: \"提示\",\n        content: \"真的要注销登录吗 ?\",\n        onOk() {\n          return that\n            .Logout({})\n            .then(() => {\n              window.location.reload()\n            })\n            .catch((err) => {\n              that.$message.error({\n                title: \"错误\",\n                description: err.message,\n              });\n            });\n        },\n        onCancel() {},\n      });\n    },\n    showSoftwareDownload() {\n      this.$refs.softwareDownload.showModal()\n    },\n    showShoppingModal() {\n      this.$refs.shoppingModal.showModal()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"less\">\n.header {\n  padding: 15px;\n  line-height: 30px;\n}\n.logo {\n  max-height: 50px;\n  width: auto;\n  margin-right: 20px;\n  display: inline-block;\n}\n.brand {\n  display: inline-block;\n  vertical-align: middle;\n}\n.brand-title {\n  color: white;\n  font-size: 30px;\n  text-shadow: 0 0 5px #282828;\n  margin-bottom: 10px;\n}\n.brand-desc {\n  color: white;\n  font-size: 18px;\n  font-style: italic;\n}\n.menu-fixed{\n    position: fixed;\n    top: 0px;\n    z-index: 99;\n    padding-bottom: 10px;\n    width: 100%;\n    background: radial-gradient(ellipse at top left, #005dff 10%, #23aeffd9 67%);\n}\n.menu {\n  display: inline-block;\n  background: transparent;\n  max-width: 1200px;\n  margin: auto;\n  .ant-menu-submenu,\n  /deep/.ant-menu-item > a,\n  /deep/.ant-menu-submenu-title > a {\n    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;\n    font-weight: 600;\n    font-size: 16px;\n    color: white;\n  }\n  .ant-menu-submenu-active,\n  .ant-menu-item-active {\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 15px;\n    border-bottom: none !important;\n  }\n}\n.ant-menu-horizontal {\n  border-bottom: none;\n}\n\n.header-avatar{\n  padding: 5px 20px;\n  float: right;\n  cursor: pointer;\n  .ant-avatar {\n    margin-right: 5px;\n  }\n  .avatar{\n    margin-right: 5px;\n    margin-bottom: 5px;\n    max-height: 30px;\n  }\n  span {\n    color: #fff;\n    font-weight: 700;\n  }\n  .action {\n    display: inline-block;\n    margin-right: 10px;\n    vertical-align: middle;\n    \n    /deep/ .header-notice .ant-badge-count {\n      box-shadow: 0 0 0 1px #fff;\n    }\n    \n    /deep/ .header-notice {\n      color: #fff;\n      font-size: 16px;\n      \n      .anticon {\n        padding: 4px;\n        font-size: 18px;\n      }\n      \n      &:hover {\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n      }\n    }\n  }\n}\n</style>"], "mappings": ";;;;;;AA0BA,SAAAA,UAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AACA,SAAAC,oBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,YAAA;AAEA;EACAC,UAAA;IACAL,KAAA,EAAAA,KAAA;IACAE,gBAAA,EAAAA,gBAAA;IACAC,aAAA,EAAAA,aAAA;IACAC,YAAA,EAAAA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAL,KAAA,QAAAM,MAAA,CAAAC,OAAA,CAAAC,QAAA;IACA,SAAAF,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAR,IAAA,SAAAK,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA;MACA,KAAAT,IAAA,QAAAK,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA,cAAAJ,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAR,IAAA;MACA,KAAAE,SAAA,QAAAF,IAAA;IACA;IACA,SAAAK,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAP,KAAA,SAAAI,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA;MACA,KAAAR,KAAA,QAAAI,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA,cAAAJ,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAP,KAAA;IACA;IACA,SAAAI,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAE,MAAA,SAAAL,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA;MACA,KAAAP,SAAA,QAAAG,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAC,WAAA,cAAAJ,MAAA,CAAAC,OAAA,CAAAE,SAAA,CAAAE,MAAA;IACA;IACA,SAAAjB,oBAAA,MAAAiB,MAAA;MACA,KAAAR,SAAA,QAAAT,oBAAA,MAAAiB,MAAA;IACA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,8BAAAC,oBAAA;IACA;IACAF,MAAA,CAAAC,gBAAA,2BAAAE,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACAJ,MAAA,CAAAC,gBAAA,gBAAAI,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAN,MAAA,CAAAO,mBAAA,gBAAAF,YAAA;IACA;IACAL,MAAA,CAAAO,mBAAA,8BAAAL,oBAAA;IACA;IACAF,MAAA,CAAAO,mBAAA,2BAAAJ,iBAAA;EACA;EACAK,OAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACA9B,UAAA,eACAC,UAAA;IACAE,oBAAA,EAAAA,oBAAA;IACAuB,YAAA,WAAAA,aAAA;MACA,IAAAK,SAAA,GAAAC,QAAA,CAAAC,eAAA,CAAAF,SAAA;MACA,IAAAA,SAAA;QACA,KAAAlB,SAAA;MACA;QACA,KAAAA,SAAA;MACA;IACA;IACAqB,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,IAAA;MACA,KAAAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA,WAAAA,KAAA;UACA,OAAAJ,IAAA,CACAK,MAAA,KACAC,IAAA;YACAvB,MAAA,CAAAwB,QAAA,CAAAC,MAAA;UACA,GACAC,KAAA,WAAAC,GAAA;YACAV,IAAA,CAAAW,QAAA,CAAAC,KAAA;cACAV,KAAA;cACAW,WAAA,EAAAH,GAAA,CAAAI;YACA;UACA;QACA;QACAC,QAAA,WAAAA,SAAA;MACA;IACA;IACA9B,oBAAA,WAAAA,qBAAA;MACA,KAAA+B,KAAA,CAAAC,gBAAA,CAAAC,SAAA;IACA;IACAhC,iBAAA,WAAAA,kBAAA;MACA,KAAA8B,KAAA,CAAAG,aAAA,CAAAD,SAAA;IACA;EAAA;AAEA", "ignoreList": []}]}