{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { getAction } from '@/api/manage';\nexport default {\n  name: 'JTreeTable',\n  props: {\n    rowKey: {\n      type: String,\n      default: 'id'\n    },\n    // 根据什么查询，如果传递 id 就根据 id 查询\n    queryKey: {\n      type: String,\n      default: 'parentId'\n    },\n    queryParams: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    // 查询顶级时的值，如果顶级为0，则传0\n    topValue: {\n      type: String,\n      default: null\n    },\n    columns: {\n      type: Array,\n      required: true\n    },\n    url: {\n      type: String,\n      required: true\n    },\n    childrenUrl: {\n      type: String,\n      default: null\n    },\n    tableProps: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    /** 是否在创建组件的时候就查询数据 */\n    immediateRequest: {\n      type: Boolean,\n      default: true\n    },\n    condition: {\n      type: String,\n      default: '',\n      required: false\n    }\n  },\n  data: function data() {\n    return {\n      dataSource: [],\n      expandedRowKeys: []\n    };\n  },\n  computed: {\n    getChildrenUrl: function getChildrenUrl() {\n      if (this.childrenUrl) {\n        return this.childrenUrl;\n      } else {\n        return this.url;\n      }\n    },\n    slots: function slots() {\n      var slots = [];\n      var _iterator = _createForOfIteratorHelper(this.columns),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var column = _step.value;\n          if (column.scopedSlots && column.scopedSlots.customRender) {\n            slots.push(column.scopedSlots.customRender);\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      return slots;\n    },\n    tableAttrs: function tableAttrs() {\n      return Object.assign(this.$attrs, this.tableProps);\n    }\n  },\n  watch: {\n    queryParams: {\n      deep: true,\n      handler: function handler() {\n        this.loadData();\n      }\n    }\n  },\n  created: function created() {\n    if (this.immediateRequest) this.loadData();\n  },\n  methods: {\n    /** 加载数据*/loadData: function loadData() {\n      var _this = this;\n      var id = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.topValue;\n      var first = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      var url = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : this.url;\n      this.$emit('requestBefore', {\n        first: first\n      });\n      if (first) {\n        this.expandedRowKeys = [];\n      }\n      var params = Object.assign({}, this.queryParams || {});\n      params[this.queryKey] = id;\n      if (this.condition && this.condition.length > 0) {\n        params['condition'] = this.condition;\n      }\n      return getAction(url, params).then(function (res) {\n        var list = [];\n        if (res.result instanceof Array) {\n          list = res.result;\n        } else if (res.result.records instanceof Array) {\n          list = res.result.records;\n        } else {\n          throw '返回数据类型不识别';\n        }\n        var dataSource = list.map(function (item) {\n          // 判断是否标记了带有子级\n          if (item.hasChildren === true) {\n            // 查找第一个带有dataIndex的值的列\n            var firstColumn;\n            var _iterator2 = _createForOfIteratorHelper(_this.columns),\n              _step2;\n            try {\n              for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n                var column = _step2.value;\n                firstColumn = column.dataIndex;\n                if (firstColumn) break;\n              }\n              // 定义默认展开时显示的loading子级，实际子级数据只在展开时加载\n            } catch (err) {\n              _iterator2.e(err);\n            } finally {\n              _iterator2.f();\n            }\n            var loadChild = _defineProperty(_defineProperty({\n              id: \"\".concat(item.id, \"_loadChild\")\n            }, firstColumn, 'loading...'), \"isLoading\", true);\n            item.children = [loadChild];\n          }\n          return item;\n        });\n        if (first) {\n          _this.dataSource = dataSource;\n        }\n        _this.$emit('requestSuccess', {\n          first: first,\n          dataSource: dataSource,\n          res: res\n        });\n        return Promise.resolve(dataSource);\n      }).finally(function () {\n        return _this.$emit('requestFinally', {\n          first: first\n        });\n      });\n    },\n    /** 点击展开图标时触发 */handleExpand: function handleExpand(expanded, record) {\n      // 判断是否是展开状态\n      if (expanded) {\n        // 判断子级的首个项的标记是否是“正在加载中”，如果是就加载数据\n        if (record.children[0].isLoading === true) {\n          this.loadData(record.id, false, this.getChildrenUrl).then(function (dataSource) {\n            // 处理好的数据可直接赋值给children\n            if (dataSource.length === 0) {\n              record.children = null;\n            } else {\n              record.children = dataSource;\n            }\n          });\n        }\n      }\n    }\n  }\n};", {"version": 3, "names": ["getAction", "name", "props", "<PERSON><PERSON><PERSON>", "type", "String", "default", "query<PERSON><PERSON>", "queryParams", "Object", "_default", "topValue", "columns", "Array", "required", "url", "childrenUrl", "tableProps", "immediateRequest", "Boolean", "condition", "data", "dataSource", "expandedRowKeys", "computed", "getChildrenUrl", "slots", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "column", "value", "scopedSlots", "customRender", "push", "err", "e", "f", "tableAttrs", "assign", "$attrs", "watch", "deep", "handler", "loadData", "created", "methods", "_this", "id", "arguments", "length", "undefined", "first", "$emit", "params", "then", "res", "list", "result", "records", "map", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstColumn", "_iterator2", "_step2", "dataIndex", "load<PERSON>hild", "_defineProperty", "concat", "children", "Promise", "resolve", "finally", "handleExpand", "expanded", "record", "isLoading"], "sources": ["src/components/jeecg/JTreeTable.vue"], "sourcesContent": ["<template>\n  <a-table\n    :rowKey=\"rowKey\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :expandedRowKeys=\"expandedRowKeys\"\n    v-bind=\"tableAttrs\"\n    v-on=\"$listeners\"\n    @expand=\"handleExpand\"\n    @expandedRowsChange=\"expandedRowKeys=$event\">\n\n    <template v-for=\"(slotItem) of slots\" :slot=\"slotItem\" slot-scope=\"text, record, index\">\n      <slot :name=\"slotItem\" v-bind=\"{text,record,index}\"></slot>\n    </template>\n\n  </a-table>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'JTreeTable',\n    props: {\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 根据什么查询，如果传递 id 就根据 id 查询\n      queryKey: {\n        type: String,\n        default: 'parentId'\n      },\n      queryParams: {\n        type: Object,\n        default: () => ({})\n      },\n      // 查询顶级时的值，如果顶级为0，则传0\n      topValue: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true\n      },\n      url: {\n        type: String,\n        required: true\n      },\n      childrenUrl: {\n        type: String,\n        default: null\n      },\n      tableProps: {\n        type: Object,\n        default: () => ({})\n      },\n      /** 是否在创建组件的时候就查询数据 */\n      immediateRequest: {\n        type: Boolean,\n        default: true\n      },\n      condition:{\n        type:String,\n        default:'',\n        required:false\n      }\n    },\n    data() {\n      return {\n        dataSource: [],\n        expandedRowKeys: []\n      }\n    },\n    computed: {\n      getChildrenUrl() {\n        if (this.childrenUrl) {\n          return this.childrenUrl\n        } else {\n          return this.url\n        }\n      },\n      slots() {\n        let slots = []\n        for (let column of this.columns) {\n          if (column.scopedSlots && column.scopedSlots.customRender) {\n            slots.push(column.scopedSlots.customRender)\n          }\n        }\n        return slots\n      },\n      tableAttrs() {\n        return Object.assign(this.$attrs, this.tableProps)\n      }\n    },\n    watch: {\n      queryParams: {\n        deep: true,\n        handler() {\n          this.loadData()\n        }\n      }\n    },\n    created() {\n      if (this.immediateRequest) this.loadData()\n    },\n    methods: {\n\n      /** 加载数据*/\n      loadData(id = this.topValue, first = true, url = this.url) {\n        this.$emit('requestBefore', { first })\n\n        if (first) {\n          this.expandedRowKeys = []\n        }\n\n        let params = Object.assign({}, this.queryParams || {})\n        params[this.queryKey] = id\n        if(this.condition && this.condition.length>0){\n          params['condition'] = this.condition\n        }\n\n        return getAction(url, params).then(res => {\n          let list = []\n          if (res.result instanceof Array) {\n            list = res.result\n          } else if (res.result.records instanceof Array) {\n            list = res.result.records\n          } else {\n            throw '返回数据类型不识别'\n          }\n          let dataSource = list.map(item => {\n            // 判断是否标记了带有子级\n            if (item.hasChildren === true) {\n              // 查找第一个带有dataIndex的值的列\n              let firstColumn\n              for (let column of this.columns) {\n                firstColumn = column.dataIndex\n                if (firstColumn) break\n              }\n              // 定义默认展开时显示的loading子级，实际子级数据只在展开时加载\n              let loadChild = { id: `${item.id}_loadChild`, [firstColumn]: 'loading...', isLoading: true }\n              item.children = [loadChild]\n            }\n            return item\n          })\n          if (first) {\n            this.dataSource = dataSource\n          }\n          this.$emit('requestSuccess', { first, dataSource, res })\n          return Promise.resolve(dataSource)\n        }).finally(() => this.$emit('requestFinally', { first }))\n      },\n\n      /** 点击展开图标时触发 */\n      handleExpand(expanded, record) {\n        // 判断是否是展开状态\n        if (expanded) {\n          // 判断子级的首个项的标记是否是“正在加载中”，如果是就加载数据\n          if (record.children[0].isLoading === true) {\n            this.loadData(record.id, false, this.getChildrenUrl).then(dataSource => {\n              // 处理好的数据可直接赋值给children\n              if (dataSource.length === 0) {\n                record.children = null\n              } else {\n                record.children = dataSource\n              }\n            })\n          }\n        }\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": ";;;;;;;AAmBA,SAAAA,SAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,WAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAI,SAAA;QAAA;MAAA;IACA;IACA;IACAC,QAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAM,OAAA;MACAR,IAAA,EAAAS,KAAA;MACAC,QAAA;IACA;IACAC,GAAA;MACAX,IAAA,EAAAC,MAAA;MACAS,QAAA;IACA;IACAE,WAAA;MACAZ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAW,UAAA;MACAb,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAI,SAAA;QAAA;MAAA;IACA;IACA;IACAQ,gBAAA;MACAd,IAAA,EAAAe,OAAA;MACAb,OAAA;IACA;IACAc,SAAA;MACAhB,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAQ,QAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MACA,SAAAT,WAAA;QACA,YAAAA,WAAA;MACA;QACA,YAAAD,GAAA;MACA;IACA;IACAW,KAAA,WAAAA,MAAA;MACA,IAAAA,KAAA;MAAA,IAAAC,SAAA,GAAAC,0BAAA,CACA,KAAAhB,OAAA;QAAAiB,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,MAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAD,MAAA,CAAAE,WAAA,IAAAF,MAAA,CAAAE,WAAA,CAAAC,YAAA;YACAV,KAAA,CAAAW,IAAA,CAAAJ,MAAA,CAAAE,WAAA,CAAAC,YAAA;UACA;QACA;MAAA,SAAAE,GAAA;QAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;MAAA;QAAAX,SAAA,CAAAa,CAAA;MAAA;MACA,OAAAd,KAAA;IACA;IACAe,UAAA,WAAAA,WAAA;MACA,OAAAhC,MAAA,CAAAiC,MAAA,MAAAC,MAAA,OAAA1B,UAAA;IACA;EACA;EACA2B,KAAA;IACApC,WAAA;MACAqC,IAAA;MACAC,OAAA,WAAAA,QAAA;QACA,KAAAC,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAA9B,gBAAA,OAAA6B,QAAA;EACA;EACAE,OAAA;IAEA,UACAF,QAAA,WAAAA,SAAA;MAAA,IAAAG,KAAA;MAAA,IAAAC,EAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,WAAAzC,QAAA;MAAA,IAAA4C,KAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAArC,GAAA,GAAAqC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,WAAArC,GAAA;MACA,KAAAyC,KAAA;QAAAD,KAAA,EAAAA;MAAA;MAEA,IAAAA,KAAA;QACA,KAAAhC,eAAA;MACA;MAEA,IAAAkC,MAAA,GAAAhD,MAAA,CAAAiC,MAAA,UAAAlC,WAAA;MACAiD,MAAA,MAAAlD,QAAA,IAAA4C,EAAA;MACA,SAAA/B,SAAA,SAAAA,SAAA,CAAAiC,MAAA;QACAI,MAAA,qBAAArC,SAAA;MACA;MAEA,OAAApB,SAAA,CAAAe,GAAA,EAAA0C,MAAA,EAAAC,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA;QACA,IAAAD,GAAA,CAAAE,MAAA,YAAAhD,KAAA;UACA+C,IAAA,GAAAD,GAAA,CAAAE,MAAA;QACA,WAAAF,GAAA,CAAAE,MAAA,CAAAC,OAAA,YAAAjD,KAAA;UACA+C,IAAA,GAAAD,GAAA,CAAAE,MAAA,CAAAC,OAAA;QACA;UACA;QACA;QACA,IAAAxC,UAAA,GAAAsC,IAAA,CAAAG,GAAA,WAAAC,IAAA;UACA;UACA,IAAAA,IAAA,CAAAC,WAAA;YACA;YACA,IAAAC,WAAA;YAAA,IAAAC,UAAA,GAAAvC,0BAAA,CACAsB,KAAA,CAAAtC,OAAA;cAAAwD,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAArC,CAAA,MAAAsC,MAAA,GAAAD,UAAA,CAAApC,CAAA,IAAAC,IAAA;gBAAA,IAAAC,MAAA,GAAAmC,MAAA,CAAAlC,KAAA;gBACAgC,WAAA,GAAAjC,MAAA,CAAAoC,SAAA;gBACA,IAAAH,WAAA;cACA;cACA;YAAA,SAAA5B,GAAA;cAAA6B,UAAA,CAAA5B,CAAA,CAAAD,GAAA;YAAA;cAAA6B,UAAA,CAAA3B,CAAA;YAAA;YACA,IAAA8B,SAAA,GAAAC,eAAA,CAAAA,eAAA;cAAApB,EAAA,KAAAqB,MAAA,CAAAR,IAAA,CAAAb,EAAA;YAAA,GAAAe,WAAA;YACAF,IAAA,CAAAS,QAAA,IAAAH,SAAA;UACA;UACA,OAAAN,IAAA;QACA;QACA,IAAAT,KAAA;UACAL,KAAA,CAAA5B,UAAA,GAAAA,UAAA;QACA;QACA4B,KAAA,CAAAM,KAAA;UAAAD,KAAA,EAAAA,KAAA;UAAAjC,UAAA,EAAAA,UAAA;UAAAqC,GAAA,EAAAA;QAAA;QACA,OAAAe,OAAA,CAAAC,OAAA,CAAArD,UAAA;MACA,GAAAsD,OAAA;QAAA,OAAA1B,KAAA,CAAAM,KAAA;UAAAD,KAAA,EAAAA;QAAA;MAAA;IACA;IAEA,gBACAsB,YAAA,WAAAA,aAAAC,QAAA,EAAAC,MAAA;MACA;MACA,IAAAD,QAAA;QACA;QACA,IAAAC,MAAA,CAAAN,QAAA,IAAAO,SAAA;UACA,KAAAjC,QAAA,CAAAgC,MAAA,CAAA5B,EAAA,cAAA1B,cAAA,EAAAiC,IAAA,WAAApC,UAAA;YACA;YACA,IAAAA,UAAA,CAAA+B,MAAA;cACA0B,MAAA,CAAAN,QAAA;YACA;cACAM,MAAA,CAAAN,QAAA,GAAAnD,UAAA;YACA;UACA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}