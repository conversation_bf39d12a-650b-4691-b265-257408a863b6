{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameCard.vue?vue&type=template&id=38acdad0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameCard.vue", "mtime": 1749742410119}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"game-card\",\n    on: {\n      click: _vm.playGame\n    }\n  }, [_c(\"div\", {\n    staticClass: \"game-cost\"\n  }, [_c(\"span\", {\n    staticClass: \"coin-icon\"\n  }), _c(\"span\", {\n    staticClass: \"cost-text\"\n  }, [_vm._v(_vm._s(_vm.game.costDescription))])]), _c(\"div\", {\n    staticClass: \"game-image\",\n    style: {\n      backgroundImage: \"url(\".concat(_vm.game.imageUrl, \")\")\n    }\n  }), _c(\"div\", {\n    staticClass: \"game-content\"\n  }, [_c(\"h3\", {\n    staticClass: \"game-title\"\n  }, [_vm._v(_vm._s(_vm.game.title))]), _c(\"p\", {\n    staticClass: \"game-description\"\n  }, [_vm._v(_vm._s(_vm.game.description))]), _vm._m(0)])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"button\", {\n    staticClass: \"play-button\"\n  }, [_c(\"span\", {\n    staticClass: \"play-icon\"\n  }, [_vm._v(\"▶\")]), _vm._v(\"\\n      开始游戏\\n    \")]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "click", "playGame", "_v", "_s", "game", "costDescription", "style", "backgroundImage", "concat", "imageUrl", "title", "description", "_m", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/game/components/GameCard.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"game-card\", on: { click: _vm.playGame } }, [\n    _c(\"div\", { staticClass: \"game-cost\" }, [\n      _c(\"span\", { staticClass: \"coin-icon\" }),\n      _c(\"span\", { staticClass: \"cost-text\" }, [\n        _vm._v(_vm._s(_vm.game.costDescription)),\n      ]),\n    ]),\n    _c(\"div\", {\n      staticClass: \"game-image\",\n      style: { backgroundImage: `url(${_vm.game.imageUrl})` },\n    }),\n    _c(\"div\", { staticClass: \"game-content\" }, [\n      _c(\"h3\", { staticClass: \"game-title\" }, [_vm._v(_vm._s(_vm.game.title))]),\n      _c(\"p\", { staticClass: \"game-description\" }, [\n        _vm._v(_vm._s(_vm.game.description)),\n      ]),\n      _vm._m(0),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"button\", { staticClass: \"play-button\" }, [\n      _c(\"span\", { staticClass: \"play-icon\" }, [_vm._v(\"▶\")]),\n      _vm._v(\"\\n      开始游戏\\n    \"),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE,WAAW;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAS;EAAE,CAAC,EAAE,CAC1EL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACvCH,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,IAAI,CAACC,eAAe,CAAC,CAAC,CACzC,CAAC,CACH,CAAC,EACFT,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,YAAY;IACzBQ,KAAK,EAAE;MAAEC,eAAe,SAAAC,MAAA,CAASb,GAAG,CAACS,IAAI,CAACK,QAAQ;IAAI;EACxD,CAAC,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EACzEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC3CH,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,IAAI,CAACO,WAAW,CAAC,CAAC,CACrC,CAAC,EACFhB,GAAG,CAACiB,EAAE,CAAC,CAAC,CAAC,CACV,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIlB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAClDF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACvDP,GAAG,CAACO,EAAE,CAAC,oBAAoB,CAAC,CAC7B,CAAC;AACJ,CAAC,CACF;AACDR,MAAM,CAACoB,aAAa,GAAG,IAAI;AAE3B,SAASpB,MAAM,EAAEmB,eAAe", "ignoreList": []}]}