{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\SysMessageTemplateList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\SysMessageTemplateList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import SysMessageTemplateModal from './modules/SysMessageTemplateModal'\n  import SysMessageTestModal from './modules/SysMessageTestModal'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n  import <PERSON><PERSON>lli<PERSON> from \"@/components/jeecg/JEllipsis\";\n\n  export default {\n    name: \"SysMessageTemplateList\",\n    mixins: [JeecgListMixin],\n    components: {\n      JEllipsis,\n      SysMessageTemplateModal,\n      SysMessageTestModal\n    },\n    data() {\n      return {\n        description: '消息模板管理页面',\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key: 'rowIndex',\n            width: 60,\n            align: \"center\",\n            customRender: function (t, r, index) {\n              return parseInt(index) + 1;\n            }\n          },\n          {\n            title: '模板CODE',\n            align: \"center\",\n            dataIndex: 'templateCode'\n          },\n          {\n            title: '模板标题',\n            align: \"center\",\n            dataIndex: 'templateName'\n          },\n          {\n            title: '模板内容',\n            align: \"center\",\n            dataIndex: 'templateContent',\n            scopedSlots: {customRender: 'templateContent'},\n          },\n          {\n            title: '模板类型',\n            align: \"center\",\n            dataIndex: 'templateType',\n            customRender: function (text) {\n              if(text=='1') {\n                return \"短信\";\n              }\n              if(text=='2') {\n                return \"邮件\";\n              }\n              if(text=='3') {\n                return \"微信\";\n              }\n              if(text=='4') {\n                return \"系统\";\n              }\n            }\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: \"center\",\n            scopedSlots: {customRender: 'action'},\n          }\n        ],\n        url: {\n          list: \"/message/sysMessageTemplate/list\",\n          delete: \"/message/sysMessageTemplate/delete\",\n          deleteBatch: \"/message/sysMessageTemplate/deleteBatch\",\n          exportXlsUrl: \"message/sysMessageTemplate/exportXls\",\n          importExcelUrl: \"message/sysMessageTemplate/importExcel\",\n        },\n      }\n    },\n    computed: {\n      importExcelUrl: function () {\n        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n      }\n    },\n    methods: {\n      handleTest(record){\n        this.$refs.testModal.open(record);\n        this.$refs.testModal.title = \"发送测试\";\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["SysMessageTemplateList.vue"], "names": [], "mappings": ";AA2HA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "SysMessageTemplateList.vue", "sourceRoot": "src/views/modules/message", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n        <a-row :gutter=\"24\">\n\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"模板CODE\">\n              <a-input placeholder=\"请输入模板CODE\" v-model=\"queryParam.templateCode\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"模板内容\">\n              <a-input placeholder=\"请输入模板内容\" v-model=\"queryParam.templateContent\"></a-input>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"toggleSearchStatus\">\n            <a-col :md=\"6\" :sm=\"8\">\n              <a-form-item label=\"模板标题\">\n                <a-input placeholder=\"请输入模板标题\" v-model=\"queryParam.templateName\"></a-input>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"6\" :sm=\"8\">\n              <a-form-item label=\"模板类型\">\n                <a-input placeholder=\"请输入模板类型\" v-model=\"queryParam.templateType\"></a-input>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :md=\"6\" :sm=\"8\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n                {{ toggleSearchStatus ? '收起' : '展开' }}\n                <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n      <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('消息模板')\">导出</a-button>\n      <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\"\n                @change=\"handleImportExcel\">\n        <a-button type=\"primary\" icon=\"import\">导入</a-button>\n      </a-upload>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{\n        selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <!-- 字符串超长截取省略号显示-->\n        <span slot=\"templateContent\" slot-scope=\"text\">\n          <j-ellipsis :value=\"text\" :length=\"25\" />\n        </span>\n\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n\n          <a-divider type=\"vertical\"/>\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\"/></a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n              <a-menu-item>\n                  <a @click=\"handleTest(record)\">发送测试</a>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <!-- 表单区域 -->\n    <sysMessageTemplate-modal ref=\"modalForm\" @ok=\"modalFormOk\"></sysMessageTemplate-modal>\n\n    <sysMessageTest-modal ref=\"testModal\"></sysMessageTest-modal>\n  </a-card>\n</template>\n\n<script>\n  import SysMessageTemplateModal from './modules/SysMessageTemplateModal'\n  import SysMessageTestModal from './modules/SysMessageTestModal'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n  import JEllipsis from \"@/components/jeecg/JEllipsis\";\n\n  export default {\n    name: \"SysMessageTemplateList\",\n    mixins: [JeecgListMixin],\n    components: {\n      JEllipsis,\n      SysMessageTemplateModal,\n      SysMessageTestModal\n    },\n    data() {\n      return {\n        description: '消息模板管理页面',\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key: 'rowIndex',\n            width: 60,\n            align: \"center\",\n            customRender: function (t, r, index) {\n              return parseInt(index) + 1;\n            }\n          },\n          {\n            title: '模板CODE',\n            align: \"center\",\n            dataIndex: 'templateCode'\n          },\n          {\n            title: '模板标题',\n            align: \"center\",\n            dataIndex: 'templateName'\n          },\n          {\n            title: '模板内容',\n            align: \"center\",\n            dataIndex: 'templateContent',\n            scopedSlots: {customRender: 'templateContent'},\n          },\n          {\n            title: '模板类型',\n            align: \"center\",\n            dataIndex: 'templateType',\n            customRender: function (text) {\n              if(text=='1') {\n                return \"短信\";\n              }\n              if(text=='2') {\n                return \"邮件\";\n              }\n              if(text=='3') {\n                return \"微信\";\n              }\n              if(text=='4') {\n                return \"系统\";\n              }\n            }\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: \"center\",\n            scopedSlots: {customRender: 'action'},\n          }\n        ],\n        url: {\n          list: \"/message/sysMessageTemplate/list\",\n          delete: \"/message/sysMessageTemplate/delete\",\n          deleteBatch: \"/message/sysMessageTemplate/deleteBatch\",\n          exportXlsUrl: \"message/sysMessageTemplate/exportXls\",\n          importExcelUrl: \"message/sysMessageTemplate/importExcel\",\n        },\n      }\n    },\n    computed: {\n      importExcelUrl: function () {\n        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n      }\n    },\n    methods: {\n      handleTest(record){\n        this.$refs.testModal.open(record);\n        this.$refs.testModal.title = \"发送测试\";\n      }\n\n    }\n  }\n</script>\n<style lang=\"less\" scoped>\n  /** Button按钮间距 */\n  .ant-btn {\n    margin-left: 3px\n  }\n\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n</style>"]}]}