{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue?vue&type=template&id=05334a82&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"page-header-index-wide\">\n  <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n    <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n      <div class=\"account-settings-info-left\">\n        <a-menu\n          :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n          :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n          :defaultSelectedKeys=\"defaultSelectedKeys\"\n          type=\"inner\"\n          @openChange=\"onOpenChange\"\n        >\n          <a-menu-item key=\"/account/settings/base\">\n            <router-link :to=\"{ name: 'account-settings-base' }\">\n              个人设置\n            </router-link>\n          </a-menu-item>\n          <a-menu-item key=\"/account/settings/password\">\n            <router-link :to=\"{ name: 'account-settings-password' }\">\n              修改密码\n            </router-link>\n          </a-menu-item>\n          <a-menu-item v-if=\"false\" key=\"/account/settings/security\">\n            <router-link :to=\"{ name: 'account-settings-security' }\">\n              安全设置\n            </router-link>\n          </a-menu-item>\n          <a-menu-item v-if=\"false\" key=\"/account/settings/custom\">\n            <router-link :to=\"{ name: 'account-settings-custom' }\">\n              个性化\n            </router-link>\n          </a-menu-item>\n          <a-menu-item v-if=\"false\" key=\"/account/settings/binding\">\n            <router-link :to=\"{ name: 'account-settings-binding' }\">\n              账户绑定\n            </router-link>\n          </a-menu-item>\n          <a-menu-item v-if=\"false\" key=\"/account/settings/notification\">\n            <router-link :to=\"{ name: 'account-settings-notification' }\">\n              新消息通知\n            </router-link>\n          </a-menu-item>\n        </a-menu>\n      </div>\n      <div class=\"account-settings-info-right\">\n        <div class=\"account-settings-info-title\">\n          <span>{{ $route.meta.title }}</span>\n        </div>\n        <route-view></route-view>\n      </div>\n    </div>\n  </a-card>\n</div>\n", null]}