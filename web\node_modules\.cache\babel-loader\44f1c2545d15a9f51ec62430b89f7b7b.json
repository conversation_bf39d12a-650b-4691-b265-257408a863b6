{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelB.vue?vue&type=template&id=19b28c85&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelB.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-card\", {\n    staticStyle: {\n      \"min-width\": \"500px\",\n      \"overflow-x\": \"auto\"\n    }\n  }, [_c(\"p\", [_vm._v(\"我是右侧页面\")]), _c(\"img-turn-page\")], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "_v", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/jeecg/modules/SplitPanelB.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-card\",\n    { staticStyle: { \"min-width\": \"500px\", \"overflow-x\": \"auto\" } },\n    [_c(\"p\", [_vm._v(\"我是右侧页面\")]), _c(\"img-turn-page\")],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR;IAAEE,WAAW,EAAE;MAAE,WAAW,EAAE,OAAO;MAAE,YAAY,EAAE;IAAO;EAAE,CAAC,EAC/D,CAACF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEH,EAAE,CAAC,eAAe,CAAC,CAAC,EAClD,CACF,CAAC;AACH,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxBN,MAAM,CAACO,aAAa,GAAG,IAAI;AAE3B,SAASP,MAAM,EAAEM,eAAe", "ignoreList": []}]}