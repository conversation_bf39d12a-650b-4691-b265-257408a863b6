{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlineExam.vue?vue&type=template&id=0987cf69&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlineExam.vue", "mtime": 1753194259986}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n  <!-- 考试界面 -->\n  <exam-taking-page\n    v-if=\"showExamPage\"\n    :paper-id=\"currentPaper.id\"\n    @back-to-list=\"backToExamList\"\n    @exam-completed=\"handleExamCompleted\"\n  />\n\n  <!-- 试卷列表界面 -->\n  <a-card :bordered=\"false\" v-else>\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"24\">\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"科目\">\n            <a-select placeholder=\"请选择科目\" v-model=\"queryParam.subject\">\n              <a-select-option value=\"\">全部</a-select-option>\n              <a-select-option value=\"Scratch\">Scratch</a-select-option>\n              <a-select-option value=\"Python\">Python</a-select-option>\n              <a-select-option value=\"C++\">C++</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"级别\">\n            <a-select placeholder=\"请选择级别\" v-model=\"queryParam.level\" :disabled=\"!queryParam.subject\">\n              <a-select-option value=\"\">全部</a-select-option>\n              <template v-if=\"queryParam.subject === 'Scratch'\">\n                <a-select-option value=\"一级\">一级</a-select-option>\n                <a-select-option value=\"二级\">二级</a-select-option>\n                <a-select-option value=\"三级\">三级</a-select-option>\n                <a-select-option value=\"四级\">四级</a-select-option>\n              </template>\n              <template v-else>\n                <a-select-option value=\"一级\">一级</a-select-option>\n                <a-select-option value=\"二级\">二级</a-select-option>\n                <a-select-option value=\"三级\">三级</a-select-option>\n                <a-select-option value=\"四级\">四级</a-select-option>\n                <a-select-option value=\"五级\">五级</a-select-option>\n                <a-select-option value=\"六级\">六级</a-select-option>\n                <a-select-option value=\"七级\">七级</a-select-option>\n                <a-select-option value=\"八级\">八级</a-select-option>\n              </template>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"类型\">\n            <a-select placeholder=\"请选择类型\" v-model=\"queryParam.type\">\n              <a-select-option value=\"\">全部</a-select-option>\n              <a-select-option value=\"真题\">真题</a-select-option>\n              <a-select-option value=\"模拟\">模拟</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"年份\">\n            <a-select placeholder=\"请选择年份\" v-model=\"queryParam.year\">\n              <a-select-option value=\"\">全部</a-select-option>\n              <a-select-option :value=\"year\" v-for=\"year in yearOptions\" :key=\"year\">{{ year }}年</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <span class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n\n  <!-- 卡片列表 -->\n  <div class=\"card-list\">\n    <a-spin :spinning=\"loading\">\n      <a-row :gutter=\"[10, 10]\">\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"paper in dataSource\" :key=\"paper.id\">\n          <a-card hoverable class=\"exam-card\">\n            <!-- 卡片头部彩带 -->\n            <div class=\"card-header\" :class=\"getSubjectClass(paper.subject)\">\n              <div class=\"subject-icon\">\n                <a-icon :type=\"getSubjectIcon(paper.subject)\" />\n              </div>\n              <div class=\"subject-info\">\n                <span class=\"subject-name\">{{ paper.subject }}</span>\n                <span class=\"level-badge\">{{ paper.level }}</span>\n              </div>\n              <div class=\"year-badge\">{{ paper.year }}年</div>\n            </div>\n\n            <!-- 卡片内容 -->\n            <div class=\"card-content\">\n              <h3 class=\"card-title\">{{ paper.title }}</h3>\n\n              <div class=\"card-info\">\n                <div class=\"info-item\">\n                  <span class=\"info-label\">考试时长：</span>\n                  <span>{{ paper.examDuration }}分钟</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"info-label\">试卷难度：</span>\n                  <a-rate :value=\"paper.difficulty\" :count=\"3\" disabled size=\"small\" />\n                </div>\n              </div>\n\n              <div class=\"card-actions\">\n                <a-button type=\"primary\" size=\"large\" @click.stop=\"handleStartExam(paper)\" class=\"start-btn\">\n                  <a-icon type=\"play-circle\" />\n                  开始考试\n                </a-button>\n                <a-button @click.stop=\"handleViewDetail(paper)\" class=\"detail-btn\">\n                  <a-icon type=\"eye\" />\n                  查看详情\n                </a-button>\n              </div>\n            </div>\n          </a-card>\n        </a-col>\n        <a-col :span=\"24\" v-if=\"dataSource.length === 0\">\n          <a-empty description=\"暂无可用试卷\" />\n        </a-col>\n      </a-row>\n    </a-spin>\n    \n    <!-- 分页 -->\n    <a-pagination\n      v-if=\"dataSource.length > 0\"\n      class=\"pagination\"\n      :current=\"ipagination.current\"\n      :pageSize=\"ipagination.pageSize\"\n      :total=\"ipagination.total\"\n      :pageSizeOptions=\"ipagination.pageSizeOptions\"\n      :showTotal=\"(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n      :showQuickJumper=\"true\"\n      :showSizeChanger=\"true\"\n      @change=\"handlePageChange\"\n      @showSizeChange=\"handleSizeChange\"\n    />\n  </div>\n\n  <!-- 考试说明模态框 -->\n  <a-modal\n    title=\"考试说明\"\n    :visible=\"examInfoVisible\"\n    :footer=\"null\"\n    :width=\"600\"\n    @cancel=\"examInfoVisible = false\"\n  >\n    <div class=\"exam-info\">\n      <h3>{{ currentPaper.title }}</h3>\n      <div class=\"exam-info-content\">\n        <div class=\"basic-info\">\n          <div class=\"info-item\">\n            <span class=\"label\">科目：</span>\n            <span class=\"value\">{{ currentPaper.subject }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"label\">级别：</span>\n            <span class=\"value\">{{ getLevelText(currentPaper.level) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"label\">考试时长：</span>\n            <span class=\"value\">{{ currentPaper.examDuration }}分钟</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"label\">通过标准：</span>\n            <span class=\"value\">试卷满分100分，得分<span class=\"score-highlight\">60分（含）</span>以上通过</span>\n          </div>\n        </div>\n        <div class=\"exam-rules\">\n          <h4>考试须知</h4>\n          <div class=\"rules-list\">\n            <div class=\"rule-item\">\n              <i class=\"rule-icon\">⚠️</i>\n              <span>考试过程中请勿刷新或关闭页面，否则可能导致答案丢失</span>\n            </div>\n            <div class=\"rule-item\">\n              <i class=\"rule-icon\">⏰</i>\n              <span>请在规定时间内完成考试，超时系统将自动提交</span>\n            </div>\n            <div class=\"rule-item\">\n              <i class=\"rule-icon\">📝</i>\n              <span>开始考试后，计时将立即开始，请合理安排答题时间</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"exam-actions\">\n        <a-button type=\"primary\" size=\"large\" @click=\"confirmStartExam\">开始考试</a-button>\n        <a-button size=\"large\" @click=\"examInfoVisible = false\">取消</a-button>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 试卷详情模态框 -->\n  <a-modal\n    title=\"试卷详情\"\n    :visible=\"detailModalVisible\"\n    :footer=\"null\"\n    :width=\"800\"\n    @cancel=\"detailModalVisible = false\"\n  >\n    <div v-if=\"currentPaperDetail\" class=\"paper-detail\">\n      <!-- 试卷基本信息 -->\n      <div class=\"detail-header\" :class=\"getSubjectClass(currentPaperDetail.subject)\">\n        <div class=\"header-background\"></div>\n        <div class=\"header-content\">\n          <div class=\"subject-icon-large\">\n            <a-icon :type=\"getSubjectIcon(currentPaperDetail.subject)\" />\n          </div>\n          <div class=\"header-info\">\n            <h2>{{ currentPaperDetail.title }}</h2>\n            <div class=\"detail-badges\">\n              <div class=\"badge-item subject-badge\">\n                <a-icon type=\"book\" />\n                <span>{{ currentPaperDetail.subject }}</span>\n              </div>\n              <div class=\"badge-item level-badge\">\n                <a-icon type=\"trophy\" />\n                <span>{{ currentPaperDetail.level }}</span>\n              </div>\n              <div class=\"badge-item year-badge\">\n                <a-icon type=\"calendar\" />\n                <span>{{ currentPaperDetail.year }}年</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 试卷详细信息 -->\n      <div class=\"detail-content\">\n        <a-row :gutter=\"24\">\n          <a-col :span=\"12\">\n            <div class=\"info-card\">\n              <h4><a-icon type=\"clock-circle\" /> 考试信息</h4>\n              <p><strong>考试时长：</strong>{{ currentPaperDetail.examDuration }}分钟</p>\n              <p><strong>难度等级：</strong>\n                <a-rate :value=\"currentPaperDetail.difficulty\" :count=\"3\" disabled />\n                {{ getDifficultyText(currentPaperDetail.difficulty) }}\n              </p>\n              <p><strong>试卷类型：</strong>{{ currentPaperDetail.type }}</p>\n            </div>\n          </a-col>\n          <a-col :span=\"12\">\n            <div class=\"info-card\">\n              <h4><a-icon type=\"file-text\" /> 题目构成</h4>\n              <p><strong>单选题：</strong>15题，每题2分，共30分</p>\n              <p><strong>判断题：</strong>10题，每题2分，共20分</p>\n              <p><strong>编程题：</strong>2题，每题25分，共50分</p>\n              <p><strong>总分：</strong>100分</p>\n            </div>\n          </a-col>\n        </a-row>\n      </div>\n    </div>\n  </a-modal>\n  </a-card>\n</div>\n", null]}