{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue?vue&type=template&id=5e7337c8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue", "mtime": 1746672706055}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<!-- <div id=\"IndexPage\" :style=\"{ background: sysConfig.homeBgColor }\"> -->\n  <!-- <div :style=\"{ 'background-image': 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')', 'background-repeat': sysConfig.homeBgRepeat }\"> -->\n    <div class=\"boxBackground\">\n      <div class=\"boxContent\" v-html=\"sysConfig._homeHtml\"></div>\n    </div>\n  <!-- </div> -->\n<!-- </div> -->\n", null]}