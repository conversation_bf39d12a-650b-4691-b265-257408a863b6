{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\search\\SearchLayout.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\search\\SearchLayout.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"SearchLayout\",\n  data: function data() {\n    var _this = this;\n    return {\n      tabs: {\n        items: [{\n          key: '1',\n          title: '文章'\n        }, {\n          key: '2',\n          title: '项目'\n        }, {\n          key: '3',\n          title: '应用'\n        }],\n        active: function active() {\n          switch (_this.$route.path) {\n            case '/list/search/article':\n              return '1';\n            case '/list/search/project':\n              return '2';\n            case '/list/search/application':\n              return '3';\n            default:\n              return '1';\n          }\n        },\n        callback: function callback(key) {\n          switch (key) {\n            case '1':\n              _this.$router.push('/list/search/article');\n              break;\n            case '2':\n              _this.$router.push('/list/search/project');\n              break;\n            case '3':\n              _this.$router.push('/list/search/application');\n              break;\n            default:\n              _this.$router.push('/workplace');\n          }\n        }\n      },\n      search: true\n    };\n  },\n  computed: {},\n  methods: {}\n};", {"version": 3, "names": ["name", "data", "_this", "tabs", "items", "key", "title", "active", "$route", "path", "callback", "$router", "push", "search", "computed", "methods"], "sources": ["src/views/list/search/SearchLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"search-content\">\n    <router-view />\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"SearchLayout\",\n    data () {\n      return {\n        tabs: {\n          items: [\n            {\n              key: '1',\n              title: '文章'\n            },\n            {\n              key: '2',\n              title: '项目'\n            },\n            {\n              key: '3',\n              title: '应用'\n            },\n          ],\n          active: () => {\n            switch (this.$route.path) {\n              case '/list/search/article':\n                return '1'\n              case '/list/search/project':\n                return '2'\n              case '/list/search/application':\n                return '3'\n              default:\n                return '1'\n            }\n          },\n          callback: (key) => {\n            switch (key) {\n              case '1':\n                this.$router.push('/list/search/article')\n                break\n              case '2':\n                this.$router.push('/list/search/project')\n                break\n              case '3':\n                this.$router.push('/list/search/application')\n                break\n              default:\n                this.$router.push('/workplace')\n            }\n          }\n        },\n        search: true\n      }\n    },\n    computed: {\n\n    },\n    methods: {\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .search-head{\n    background-color: #fff;\n    margin: -25px -24px -24px;\n    .search-input{\n      text-align: center;\n      margin-bottom: 16px;\n    }\n  }\n  .search-content{\n    margin-top: 48px;\n  }\n</style>"], "mappings": "AAOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,IAAA;QACAC,KAAA,GACA;UACAC,GAAA;UACAC,KAAA;QACA,GACA;UACAD,GAAA;UACAC,KAAA;QACA,GACA;UACAD,GAAA;UACAC,KAAA;QACA,EACA;QACAC,MAAA,WAAAA,OAAA;UACA,QAAAL,KAAA,CAAAM,MAAA,CAAAC,IAAA;YACA;cACA;YACA;cACA;YACA;cACA;YACA;cACA;UACA;QACA;QACAC,QAAA,WAAAA,SAAAL,GAAA;UACA,QAAAA,GAAA;YACA;cACAH,KAAA,CAAAS,OAAA,CAAAC,IAAA;cACA;YACA;cACAV,KAAA,CAAAS,OAAA,CAAAC,IAAA;cACA;YACA;cACAV,KAAA,CAAAS,OAAA,CAAAC,IAAA;cACA;YACA;cACAV,KAAA,CAAAS,OAAA,CAAAC,IAAA;UACA;QACA;MACA;MACAC,MAAA;IACA;EACA;EACAC,QAAA,GAEA;EACAC,OAAA,GACA;AACA", "ignoreList": []}]}