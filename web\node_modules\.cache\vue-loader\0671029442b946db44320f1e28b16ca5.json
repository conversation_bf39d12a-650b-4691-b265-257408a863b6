{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { getAction } from '@/api/manage'\n  export default {\n    props: ['sex','name'],\n    data () {\n      return {\n        formLayout: 'horizontal',\n        form: this.$form.createForm(this),\n        areaOptions:[]\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            console.log('Received values of form: ', values)\n          }\n        })\n      },\n      handleSelectChange (value) {\n        console.log(value)\n        this.form.setFieldsValue({\n          note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,\n        })\n      },\n      onChange(value, selectedOptions) {\n        console.log(value, selectedOptions);\n      },\n      filter(inputValue, path) {\n        return (path.some(option => (option.label).toLowerCase().indexOf(inputValue.toLowerCase()) > -1));\n      },\n    },\n    created (){\n      console.log('============= online href common props ============= ');\n      console.log('props sex: ',this.sex);\n      console.log('props name: ',this.name);\n\n      getAction('/api/area').then((res) => {\n          console.log(\"------------\")\n          console.log(res)\n          this.areaOptions = res;\n      })\n    },\n    watch: {\n      $route: {\n        immediate: true,\n        handler() {\n          console.log('============= online href  $route props ============= ');\n          let sex = this.$route.query.sex\n          console.log('$route sex: ', sex);\n        }\n      }\n    },\n  }\n", {"version": 3, "sources": ["helloworld.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "helloworld.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-form @submit=\"handleSubmit\" :form=\"form\">\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Note\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-input v-decorator=\"['note',{rules: [{ required: true, message: 'Please input your note!' }]}]\"/>\n      </a-form-item>\n      </a-col>\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Gender\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-select v-decorator=\"['gender',{rules: [{ required: true, message: 'Please select your gender!' }]}]\" placeholder=\"Select a option and change input text above\" @change=\"this.handleSelectChange\">\n          <a-select-option value=\"male\">male</a-select-option>\n          <a-select-option value=\"female\">female</a-select-option>\n        </a-select>\n      </a-form-item>\n      </a-col>\n      <a-col :md=\"24\" :sm=\"24\">\n      <a-form-item label=\"Gender\" :labelCol=\"{ span: 7 }\" :wrapperCol=\"{ span: 15 }\">\n        <a-cascader :options=\"areaOptions\" @change=\"onChange\" :showSearch=\"{filter}\" placeholder=\"Please select\" />\n      </a-form-item>\n      </a-col>\n      <a-form-item :wrapperCol=\"{ span: 12, offset: 5 }\">\n        <a-col :md=\"24\" :sm=\"24\">\n          <a-form-item :wrapperCol=\"{ span: 12, offset: 5 }\">\n            <a-button type=\"primary\" htmlType=\"submit\">Submit</a-button>\n          </a-form-item>\n        </a-col>\n      </a-form-item>\n    </a-form>\n  </a-card>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n  export default {\n    props: ['sex','name'],\n    data () {\n      return {\n        formLayout: 'horizontal',\n        form: this.$form.createForm(this),\n        areaOptions:[]\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            console.log('Received values of form: ', values)\n          }\n        })\n      },\n      handleSelectChange (value) {\n        console.log(value)\n        this.form.setFieldsValue({\n          note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,\n        })\n      },\n      onChange(value, selectedOptions) {\n        console.log(value, selectedOptions);\n      },\n      filter(inputValue, path) {\n        return (path.some(option => (option.label).toLowerCase().indexOf(inputValue.toLowerCase()) > -1));\n      },\n    },\n    created (){\n      console.log('============= online href common props ============= ');\n      console.log('props sex: ',this.sex);\n      console.log('props name: ',this.name);\n\n      getAction('/api/area').then((res) => {\n          console.log(\"------------\")\n          console.log(res)\n          this.areaOptions = res;\n      })\n    },\n    watch: {\n      $route: {\n        immediate: true,\n        handler() {\n          console.log('============= online href  $route props ============= ');\n          let sex = this.$route.query.sex\n          console.log('$route sex: ', sex);\n        }\n      }\n    },\n  }\n</script>"]}]}