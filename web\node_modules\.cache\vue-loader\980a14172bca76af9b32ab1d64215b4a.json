{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue?vue&type=template&id=d2d75d0c&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"page-header-index-wide\">\n  <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n    <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n      <div class=\"account-settings-info-left\">\n        <a-menu\n          :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n          :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n          :defaultSelectedKeys=\"defaultSelectedKeys\"\n          type=\"inner\"\n          @openChange=\"onOpenChange\"\n        >\n          <a-menu-item v-for=\"(course,index) in courseList\" :key=\"'/teaching/mineCourse/course?id='+course.id\">\n            <router-link :to=\"{ name: 'teaching-mineCourse-course',  query: {id:course.id}}\" :meta=\"{keepAlive:false}\">\n              {{course.courseName}}\n            </router-link>\n          </a-menu-item>\n        </a-menu>\n      </div>\n      <div class=\"account-settings-info-right\">\n        <route-view></route-view>\n      </div>\n    </div>\n  </a-card>\n</div>\n", null]}