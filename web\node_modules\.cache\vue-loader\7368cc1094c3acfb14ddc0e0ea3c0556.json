{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderModalForJEditableTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderModalForJEditableTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n  import { FormTypes, VALIDATE_NO_PASSED, getRefPromise, validateFormAndTables } from '@/utils/JEditableTableUtil'\n  import { httpAction, getAction } from '@/api/manage'\n  import JDate from '@/components/jeecg/JDate'\n  import pick from 'lodash.pick'\n  import moment from 'moment'\n\n  export default {\n    name: 'JeecgOrderModalForJEditableTable',\n    components: {\n      JDate, JEditableTable\n    },\n    data() {\n      return {\n        title: '操作',\n        visible: false,\n        form: this.$form.createForm(this),\n        confirmLoading: false,\n        model: {},\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 6 }\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 24 - 6 }\n        },\n        activeKey: '1',\n        // 客户信息\n        table1: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '客户名',\n              key: 'name',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{ required: true, message: '${title}不能为空' }]\n            },\n            {\n              title: '性别',\n              key: 'sex',\n              width: '18%',\n              type: FormTypes.select,\n              options: [ // 下拉选项\n                { title: '男', value: '1' },\n                { title: '女', value: '2' }\n              ],\n              defaultValue: '',\n              placeholder: '请选择${title}'\n            },\n            {\n              title: '身份证号',\n              key: 'idcard',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{\n                pattern: '^\\\\d{6}(18|19|20)?\\\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\\\d|3[01])\\\\d{3}(\\\\d|[xX])$',\n                message: '${title}格式不正确'\n              }]\n            },\n            {\n              title: '手机号',\n              key: 'telphone',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{\n                pattern: '^1(3|4|5|7|8)\\\\d{9}$',\n                message: '${title}格式不正确'\n              }]\n            }\n          ]\n        },\n        // 机票信息\n        table2: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '航班号',\n              key: 'ticketCode',\n              width: '40%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{ required: true, message: '${title}不能为空' }]\n            },\n            {\n              title: '航班时间',\n              key: 'tickectDate',\n              width: '30%',\n              type: FormTypes.date,\n              placeholder: '请选择${title}',\n              defaultValue: ''\n            }\n          ]\n        },\n        url: {\n          add: '/test/jeecgOrderMain/add',\n          edit: '/test/jeecgOrderMain/edit',\n          orderCustomerList: '/test/jeecgOrderMain/queryOrderCustomerListByMainId',\n          orderTicketList: '/test/jeecgOrderMain/queryOrderTicketListByMainId'\n        }\n      }\n    },\n    created() {\n    },\n    methods: {\n\n      // 获取所有的editableTable实例\n      getAllTable() {\n        return Promise.all([\n          getRefPromise(this, 'editableTable1'),\n          getRefPromise(this, 'editableTable2')\n        ])\n      },\n\n      add() {\n        // 默认新增一条数据\n        this.getAllTable().then(editableTables => {\n          editableTables[0].add()\n          editableTables[1].add()\n        })\n\n        this.edit({})\n      },\n      edit(record) {\n        this.visible = true\n        this.activeKey = '1'\n        this.form.resetFields()\n        this.model = Object.assign({}, record)\n\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'orderCode', 'ctype', 'orderMoney', 'content'))\n          //时间格式化\n          this.form.setFieldsValue({ orderDate: this.model.orderDate ? moment(this.model.orderDate) : null })\n        })\n\n        // 加载子表数据\n        if (this.model.id) {\n          let params = { id: this.model.id }\n          this.requestTableData(this.url.orderCustomerList, params, this.table1)\n          this.requestTableData(this.url.orderTicketList, params, this.table2)\n        }\n\n      },\n      close() {\n        this.visible = false\n        this.getAllTable().then(editableTables => {\n          editableTables[0].initialize()\n          editableTables[1].initialize()\n        })\n        this.$emit('close')\n      },\n      /** 查询某个tab的数据 */\n      requestTableData(url, params, tab) {\n        tab.loading = true\n        getAction(url, params).then(res => {\n          tab.dataSource = res.result || []\n        }).finally(() => {\n          tab.loading = false\n        })\n      },\n      handleOk() {\n        this.validateFields()\n      },\n      handleCancel() {\n        this.close()\n      },\n      /** ATab 选项卡切换事件 */\n      handleChangeTabs(key) {\n        getRefPromise(this, `editableTable${key}`).then(editableTable => {\n          editableTable.resetScrollTop()\n        })\n      },\n\n      /** 触发表单验证 */\n      validateFields() {\n        this.getAllTable().then(tables => {\n          /** 一次性验证主表和所有的次表 */\n          return validateFormAndTables(this.form, tables)\n        }).then(allValues => {\n          let formData = this.classifyIntoFormData(allValues)\n          // 发起请求\n          return this.requestAddOrEdit(formData)\n        }).catch(e => {\n          if (e.error === VALIDATE_NO_PASSED) {\n            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n            this.activeKey = e.index == null ? this.activeKey : (e.index + 1).toString()\n          } else {\n            console.error(e)\n          }\n        })\n      },\n      /** 整理成formData */\n      classifyIntoFormData(allValues) {\n        let orderMain = Object.assign(this.model, allValues.formValue)\n        //时间格式化\n        orderMain.orderDate = orderMain.orderDate ? orderMain.orderDate.format('YYYY-MM-DD HH:mm:ss') : null\n        return {\n          ...orderMain, // 展开\n          jeecgOrderCustomerList: allValues.tablesValue[0].values,\n          jeecgOrderTicketList: allValues.tablesValue[1].values\n        }\n      },\n      /** 发起新增或修改的请求 */\n      requestAddOrEdit(formData) {\n        let url = this.url.add, method = 'post'\n        if (this.model.id) {\n          url = this.url.edit\n          method = 'put'\n        }\n        this.confirmLoading = true\n        httpAction(url, formData, method).then((res) => {\n          if (res.success) {\n            this.$message.success(res.message)\n            this.$emit('ok')\n            this.close()\n          } else {\n            this.$message.warning(res.message)\n          }\n        }).finally(() => {\n          this.confirmLoading = false\n        })\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["JeecgOrderModalForJEditableTable.vue"], "names": [], "mappings": ";;AAqGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "JeecgOrderModalForJEditableTable.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"1200\"\n    :visible=\"visible\"\n    :maskClosable=\"false\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 主表单区域 -->\n        <a-row class=\"form-row\" :gutter=\"0\">\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单号\">\n              <a-input\n                placeholder=\"请输入订单号\"\n                v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单类型\">\n              <a-select placeholder=\"请选择订单类型\" v-decorator=\"['ctype',{}]\">\n                <a-select-option value=\"1\">国内订单</a-select-option>\n                <a-select-option value=\"2\">国际订单</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单日期\">\n              <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\" v-decorator=\"[ 'orderDate',{}]\"/>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"0\">\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单金额\">\n              <a-input-number placeholder=\"请输入订单金额\" style=\"width: 100%\" v-decorator=\"[ 'orderMoney', {}]\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"订单备注\">\n              <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\"/>\n            </a-form-item>\n          </a-col>\n        </a-row>\n      </a-form>\n\n      <!-- 子表单区域 -->\n      <a-tabs v-model=\"activeKey\" @change=\"handleChangeTabs\">\n        <a-tab-pane tab=\"客户信息\" key=\"1\" :forceRender=\"true\">\n\n          <j-editable-table\n            ref=\"editableTable1\"\n            :loading=\"table1.loading\"\n            :columns=\"table1.columns\"\n            :dataSource=\"table1.dataSource\"\n            :maxHeight=\"300\"\n            :rowNumber=\"true\"\n            :rowSelection=\"true\"\n            :actionButton=\"true\"/>\n\n        </a-tab-pane>\n\n        <a-tab-pane tab=\"机票信息\" key=\"2\" :forceRender=\"true\">\n\n          <j-editable-table\n            ref=\"editableTable2\"\n            :loading=\"table2.loading\"\n            :columns=\"table2.columns\"\n            :dataSource=\"table2.dataSource\"\n            :maxHeight=\"300\"\n            :rowNumber=\"true\"\n            :rowSelection=\"true\"\n            :actionButton=\"true\"/>\n\n        </a-tab-pane>\n      </a-tabs>\n\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n  import { FormTypes, VALIDATE_NO_PASSED, getRefPromise, validateFormAndTables } from '@/utils/JEditableTableUtil'\n  import { httpAction, getAction } from '@/api/manage'\n  import JDate from '@/components/jeecg/JDate'\n  import pick from 'lodash.pick'\n  import moment from 'moment'\n\n  export default {\n    name: 'JeecgOrderModalForJEditableTable',\n    components: {\n      JDate, JEditableTable\n    },\n    data() {\n      return {\n        title: '操作',\n        visible: false,\n        form: this.$form.createForm(this),\n        confirmLoading: false,\n        model: {},\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 6 }\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 24 - 6 }\n        },\n        activeKey: '1',\n        // 客户信息\n        table1: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '客户名',\n              key: 'name',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{ required: true, message: '${title}不能为空' }]\n            },\n            {\n              title: '性别',\n              key: 'sex',\n              width: '18%',\n              type: FormTypes.select,\n              options: [ // 下拉选项\n                { title: '男', value: '1' },\n                { title: '女', value: '2' }\n              ],\n              defaultValue: '',\n              placeholder: '请选择${title}'\n            },\n            {\n              title: '身份证号',\n              key: 'idcard',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{\n                pattern: '^\\\\d{6}(18|19|20)?\\\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\\\d|3[01])\\\\d{3}(\\\\d|[xX])$',\n                message: '${title}格式不正确'\n              }]\n            },\n            {\n              title: '手机号',\n              key: 'telphone',\n              width: '24%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{\n                pattern: '^1(3|4|5|7|8)\\\\d{9}$',\n                message: '${title}格式不正确'\n              }]\n            }\n          ]\n        },\n        // 机票信息\n        table2: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '航班号',\n              key: 'ticketCode',\n              width: '40%',\n              type: FormTypes.input,\n              defaultValue: '',\n              placeholder: '请输入${title}',\n              validateRules: [{ required: true, message: '${title}不能为空' }]\n            },\n            {\n              title: '航班时间',\n              key: 'tickectDate',\n              width: '30%',\n              type: FormTypes.date,\n              placeholder: '请选择${title}',\n              defaultValue: ''\n            }\n          ]\n        },\n        url: {\n          add: '/test/jeecgOrderMain/add',\n          edit: '/test/jeecgOrderMain/edit',\n          orderCustomerList: '/test/jeecgOrderMain/queryOrderCustomerListByMainId',\n          orderTicketList: '/test/jeecgOrderMain/queryOrderTicketListByMainId'\n        }\n      }\n    },\n    created() {\n    },\n    methods: {\n\n      // 获取所有的editableTable实例\n      getAllTable() {\n        return Promise.all([\n          getRefPromise(this, 'editableTable1'),\n          getRefPromise(this, 'editableTable2')\n        ])\n      },\n\n      add() {\n        // 默认新增一条数据\n        this.getAllTable().then(editableTables => {\n          editableTables[0].add()\n          editableTables[1].add()\n        })\n\n        this.edit({})\n      },\n      edit(record) {\n        this.visible = true\n        this.activeKey = '1'\n        this.form.resetFields()\n        this.model = Object.assign({}, record)\n\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'orderCode', 'ctype', 'orderMoney', 'content'))\n          //时间格式化\n          this.form.setFieldsValue({ orderDate: this.model.orderDate ? moment(this.model.orderDate) : null })\n        })\n\n        // 加载子表数据\n        if (this.model.id) {\n          let params = { id: this.model.id }\n          this.requestTableData(this.url.orderCustomerList, params, this.table1)\n          this.requestTableData(this.url.orderTicketList, params, this.table2)\n        }\n\n      },\n      close() {\n        this.visible = false\n        this.getAllTable().then(editableTables => {\n          editableTables[0].initialize()\n          editableTables[1].initialize()\n        })\n        this.$emit('close')\n      },\n      /** 查询某个tab的数据 */\n      requestTableData(url, params, tab) {\n        tab.loading = true\n        getAction(url, params).then(res => {\n          tab.dataSource = res.result || []\n        }).finally(() => {\n          tab.loading = false\n        })\n      },\n      handleOk() {\n        this.validateFields()\n      },\n      handleCancel() {\n        this.close()\n      },\n      /** ATab 选项卡切换事件 */\n      handleChangeTabs(key) {\n        getRefPromise(this, `editableTable${key}`).then(editableTable => {\n          editableTable.resetScrollTop()\n        })\n      },\n\n      /** 触发表单验证 */\n      validateFields() {\n        this.getAllTable().then(tables => {\n          /** 一次性验证主表和所有的次表 */\n          return validateFormAndTables(this.form, tables)\n        }).then(allValues => {\n          let formData = this.classifyIntoFormData(allValues)\n          // 发起请求\n          return this.requestAddOrEdit(formData)\n        }).catch(e => {\n          if (e.error === VALIDATE_NO_PASSED) {\n            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n            this.activeKey = e.index == null ? this.activeKey : (e.index + 1).toString()\n          } else {\n            console.error(e)\n          }\n        })\n      },\n      /** 整理成formData */\n      classifyIntoFormData(allValues) {\n        let orderMain = Object.assign(this.model, allValues.formValue)\n        //时间格式化\n        orderMain.orderDate = orderMain.orderDate ? orderMain.orderDate.format('YYYY-MM-DD HH:mm:ss') : null\n        return {\n          ...orderMain, // 展开\n          jeecgOrderCustomerList: allValues.tablesValue[0].values,\n          jeecgOrderTicketList: allValues.tablesValue[1].values\n        }\n      },\n      /** 发起新增或修改的请求 */\n      requestAddOrEdit(formData) {\n        let url = this.url.add, method = 'post'\n        if (this.model.id) {\n          url = this.url.edit\n          method = 'put'\n        }\n        this.confirmLoading = true\n        httpAction(url, formData, method).then((res) => {\n          if (res.success) {\n            this.$message.success(res.message)\n            this.$emit('ok')\n            this.close()\n          } else {\n            this.$message.warning(res.message)\n          }\n        }).finally(() => {\n          this.confirmLoading = false\n        })\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n</style>"]}]}