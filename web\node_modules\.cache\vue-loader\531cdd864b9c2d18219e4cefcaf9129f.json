{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\Home.vue?vue&type=style&index=0&id=3dd2e005&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\Home.vue", "mtime": 1751206454251}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.editor-card{\n  width: 100%;\n  height: 200px;\n  margin: 30px auto;\n  padding: 20px;\n  background: linear-gradient(-30deg,#4fb5ff,#60bcff);\n  border-radius: 20px;\n  .ant-row-flex{\n    height: 100%;\n  }\n  img{\n    width: auto;\n    height: 100px;\n  }\n  h2{\n    color: white;\n    text-align: center;\n  }\n  .ant-btn{\n    border-radius: 50px;\n    display: block;\n    margin: 10px auto;\n  }\n}\n.panel-works {\n  margin-bottom: 60px;\n  padding: 20px;\n  background-color: #f9f9f9;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n}\n.panel-title {\n  font-size: 28px;\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.title-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 30px 0 15px;\n}\n\n.title-text {\n  display: inline-block;\n  padding: 10px 30px;\n  background: linear-gradient(90deg, #FF9900, #FFCC00);\n  color: white;\n  border-radius: 30px;\n  font-weight: bold;\n  box-shadow: 0 4px 10px rgba(255, 153, 0, 0.4);\n  position: relative;\n  z-index: 2;\n  font-size: 22px;\n}\n\n.title-decoration {\n  display: flex;\n  align-items: center;\n  margin: 0 15px;\n  position: relative;\n  \n  &.left {\n    flex-direction: row;\n  }\n  \n  &.right {\n    flex-direction: row-reverse;\n  }\n  \n  .decoration-circle {\n    width: 10px;\n    height: 10px;\n    border-radius: 50%;\n    background: #FFD700;\n    margin: 0 5px;\n    animation: pulse 2s infinite;\n  }\n  \n  .decoration-line {\n    height: 2px;\n    width: 30px;\n    background: linear-gradient(90deg, transparent, #FFD700, transparent);\n  }\n  \n  .decoration-star {\n    width: 15px;\n    height: 15px;\n    background: #FFCC00;\n    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n    margin: 0 5px;\n    animation: spin 5s linear infinite;\n  }\n}\n\n.title-separator {\n  height: 2px;\n  width: 100%;\n  background: linear-gradient(90deg, transparent, #FFD700, transparent);\n  margin-bottom: 20px;\n  border-radius: 3px;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.2); }\n  100% { transform: scale(1); }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.work-card {\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n  min-height: 300px;\n  height: 100%;\n  min-width: 200px;\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n    border: 1px solid rgba(79, 181, 255, 0.3);\n  }\n  \n  /deep/.ant-card-body {\n    padding: 0px;\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n  }\n  \n  a {\n    display: block;\n    height: 200px;\n    overflow: hidden;\n    position: relative;\n    text-align: center;\n  }\n  \n  .work-cover {\n    width: 100%;\n    height: 200px;\n    object-fit: cover;\n    transition: all 0.3s ease;\n  }\n  \n  img[src*=\"code.png\"] {\n    object-fit: contain;\n    padding: 20px;\n    max-width: 100%;\n    max-height: 100%;\n    width: auto;\n    height: auto;\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n  \n  &:hover .work-cover {\n    transform: scale(1.05);\n  }\n  \n  .work-stats-row {\n    position: absolute;\n    top: -15px;\n    left: 0;\n    right: 0;\n    background: rgba(255, 255, 255, 0.9);\n    padding: 8px 10px;\n    border-radius: 0 0 10px 10px;\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n    z-index: 2;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .stats-left {\n    display: flex;\n    align-items: center;\n  }\n  \n  .stats-right {\n    display: flex;\n    align-items: center;\n    justify-content: flex-end;\n  }\n  \n  .work-info{\n    padding: 15px;\n    padding-top: 35px;\n    transition: all 0.3s ease;\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n  }\n  \n  .work-info p {\n    margin-top: 5px;\n    margin-bottom: 10px;\n    word-break: break-all;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n  }\n  \n  &:hover .work-info {\n    background-color: #f0f8ff;\n  }\n  \n  .work-author {\n    margin-top: auto;\n    span {\n      line-height: 40px;\n      display: block;\n    }\n  }\n  \n  /* 添加提交时间样式 */\n  .work-submit-time {\n    font-size: 12px;\n    color: #999;\n    margin-top: -5px;\n  }\n  .ant-tag {\n    float: right;\n  }\n  > div {\n    padding: 10px;\n    margin: 10px;\n  }\n}\n.load-more {\n  display: block;\n  margin: 20px auto;\n  text-align: left;\n  color: #1890ff;\n  font-weight: bold;\n  \n  &:hover {\n    text-decoration: underline;\n  }\n}\n\n.language-tag {\n  font-size: 16px;\n  font-weight: bold;\n  padding: 5px 12px;\n  border-radius: 6px;\n  background: linear-gradient(45deg, #4fb5ff, #6bc3ff);\n  color: white;\n  border: none;\n  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);\n  display: inline-block;\n  margin: 0;\n}\n\n/* 添加日期时间小组件样式 */\n.datetime-widget {\n  position: fixed;\n  top: 20px;\n  /* right: 180px; 移除固定位置，由JS动态控制 */\n  z-index: 1000;\n  background: transparent; /* 改为透明背景 */\n  padding: 5px 12px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-size: 16px; /* 增大字号 */\n  color: white; /* 改为白色文字，与通知按钮一致 */\n  box-shadow: none; /* 移除阴影 */\n  transition: right 0.3s ease; /* 添加过渡效果使位置变化更平滑 */\n}\n", {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAwxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div>\n    <!-- 添加日期时间小组件 -->\n    <div class=\"datetime-widget\" ref=\"datetimeWidget\">\n      <a-icon type=\"calendar\" />\n      <span>{{ currentDateTime }}</span>\n    </div>\n    \n    <a-row :gutter=\"[24,24]\" class=\"editor-nav\">\n      <a-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n        <div class=\"editor-card editor-sjr\" style=\"background: linear-gradient(-30deg,#4fb5ff,#60bcff);\">\n          <a-row type=\"flex\" justify=\"space-around\" align=\"middle\">\n            <a-col :span=\"10\">\n              <img src=\"@assets/sjr.png\" alt=\"\">\n            </a-col>\n            <a-col :span=\"14\">\n              <h2>ScratchJr编辑器</h2>\n              <a-button size=\"large\" @click=\"toEditor(2)\">开始创作</a-button>\n            </a-col>\n          </a-row>\n        </div>\n      </a-col>\n      <a-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n        <div class=\"editor-card editor-sc\" style=\"background: linear-gradient(-60deg,#ffaa30,#ffbf35);\">\n          <a-row type=\"flex\" justify=\"space-around\" align=\"middle\">\n            <a-col :span=\"10\">\n              <img src=\"@assets/scratch.png\" alt=\"\">\n            </a-col>\n            <a-col :span=\"14\">\n              <h2>Scratch编辑器</h2>\n              <a-button size=\"large\" @click=\"toEditor(1)\">开始创作</a-button>\n            </a-col>\n          </a-row>\n        </div>\n      </a-col>\n      <a-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n        <div class=\"editor-card editor-py\" style=\"background: linear-gradient(-30deg,#f35981,#fb7397);\">\n          <a-row type=\"flex\" justify=\"space-around\" align=\"middle\">\n            <a-col :span=\"10\">\n              <img src=\"@assets/python.png\" alt=\"\">\n            </a-col>\n            <a-col :span=\"14\">\n              <h2>Python编辑器</h2>\n              <a-button size=\"large\" @click=\"toEditor(3)\">开始创作</a-button>\n            </a-col>\n          </a-row>\n        </div>\n      </a-col>\n      <a-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\n        <div class=\"editor-card editor-cpp\" style=\"background: linear-gradient(-45deg,#42b883,#35495e);\">\n          <a-row type=\"flex\" justify=\"space-around\" align=\"middle\">\n            <a-col :span=\"10\">\n              <img src=\"@assets/cpp.png\" alt=\"\">\n            </a-col>\n            <a-col :span=\"14\">\n              <h2>C++编辑器</h2>\n              <a-button size=\"large\" @click=\"toEditor(4)\">开始创作</a-button>\n            </a-col>\n          </a-row>\n        </div>\n      </a-col>\n    </a-row>\n    <div class=\"panel-works\" v-if=\"greatLeaderboard.length>0\">\n      <h1 class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"star\" theme=\"twoTone\" two-tone-color=\"#ffd81b\" />\n          精选作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div class=\"title-separator\"></div>\n      <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n        <a-col v-for=\"(item, index) in greatLeaderboard\" :key=\"index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n          <a-card class=\"work-card\">\n            <a @click=\"toDetail(item.id)\" target=\"_blank\">\n              <img class=\"work-cover\" v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n              <img v-if=\"item.workType == 4 || item.workType == 5 || item.workType == 10\" src=\"@/assets/code.png\" alt=\"\" />\n            </a>\n            <div class=\"work-info\">\n              <div class=\"work-stats-row\">\n                <div class=\"stats-left\">\n                  <a-icon type=\"eye\" /> {{ item.viewNum }} \n                  <a-divider type=\"vertical\"></a-divider>\n                  <a-icon type=\"like\" /> {{ item.starNum }}\n                </div>\n                <div class=\"stats-right\">\n                  <a-tag class=\"language-tag\">{{ item.workType_dictText }}</a-tag>\n                </div>\n              </div>\n              <p>{{ item.workName }}</p>\n              <a-row class=\"work-author\">\n                <a-col :span=\"6\">\n                  <a-avatar shape=\"square\" class=\"avatar\" :size=\"40\" :src=\"item.avatar_url\" />\n                </a-col>\n                <a-col :span=\"18\">\n                  <span>{{ item.realname || item.username }}</span>\n                  <div class=\"work-submit-time\">{{ formatDate(item.createTime) }}</div>\n                </a-col>\n              </a-row>\n            </div>\n          </a-card>\n        </a-col>\n      </a-row>\n      <router-link v-if=\"page.greatLeaderboard>-1\"  class=\"load-more\" :to=\"{path:'/workList?type=3'}\" >查看更多...</router-link>\n    </div>\n\n    <div class=\"panel-works\" v-if=\"courseLeaderboard.length>0\">\n      <h1 class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"calculator\" theme=\"twoTone\" two-tone-color=\"#eb2f96\" />\n          推荐课程\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div class=\"title-separator\"></div>\n      <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n        <a-col v-for=\"(item, index) in courseLeaderboard\" :key=\"index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n          <a-card class=\"work-card\">\n              <a @click=\"toCourseDetail(item.id)\" target=\"_blank\">\n                <img class=\"work-cover\" :src=\"item.courseCover_url\" />\n              </a>\n              <div class=\"work-info\">\n                <p>{{ item.courseName }}</p>\n              </div>\n            </a-card>\n        </a-col>\n      </a-row>\n      <router-link v-if=\"page.courseLeaderboard>-1\" class=\"load-more\" :to=\"{path:'/courseList'}\" >查看更多...</router-link>\n    </div>\n\n    <div class=\"panel-works\">\n      <h1 class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"like\" theme=\"twoTone\" two-tone-color=\"#52c41a\" />\n          最赞作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div class=\"title-separator\"></div>\n      <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n        <a-col v-for=\"(item, index) in starLeaderboard\" :key=\"index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n          <a-card class=\"work-card\">\n            <a @click=\"toDetail(item.id)\" target=\"_blank\">\n              <img class=\"work-cover\" v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n              <img v-if=\"item.workType == 4 || item.workType == 5 || item.workType == 10\" src=\"@/assets/code.png\" alt=\"\" />\n            </a>\n            <div class=\"work-info\">\n              <div class=\"work-stats-row\">\n                <div class=\"stats-left\">\n                  <a-icon type=\"eye\" /> {{ item.viewNum }} \n                  <a-divider type=\"vertical\"></a-divider>\n                  <a-icon type=\"like\" /> {{ item.starNum }}\n                </div>\n                <div class=\"stats-right\">\n                  <a-tag class=\"language-tag\">{{ item.workType_dictText }}</a-tag>\n                </div>\n              </div>\n              <p>{{ item.workName }}</p>\n              <a-row class=\"work-author\">\n                <a-col :span=\"6\">\n                  <a-avatar shape=\"square\" class=\"avatar\" :size=\"40\" :src=\"item.avatar_url\" />\n                </a-col>\n                <a-col :span=\"18\">\n                  <span>{{ item.realname || item.username }}</span>\n                  <div class=\"work-submit-time\">{{ formatDate(item.createTime) }}</div>\n                </a-col>\n              </a-row>\n            </div>\n          </a-card>\n        </a-col>\n      </a-row>\n      <router-link v-if=\"page.starLeaderboard>-1\" class=\"load-more\" :to=\"{path:'/workList?type=2'}\" >查看更多...</router-link>\n    </div>\n\n    <!-- 添加全局消息通知监听器，不显示但处理WebSocket消息 -->\n    <global-notification-listener ref=\"notificationListener\" />\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue'\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { mapActions, mapGetters } from 'vuex'\nimport Header from './modules/Header'\nimport Banner from './modules/Banner'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\nimport QrCode from '@/components/tools/QrCode'\nimport moment from 'moment'\n// 导入全局消息通知监听器组件\nimport GlobalNotificationListener from './modules/GlobalNotificationListener'\n\nexport default {\n  name: 'PublicWorkList',\n  components: {\n    qrcode: QrCode,\n    Header,\n    Footer,\n    UserEnter,\n    Banner,\n    GlobalNotificationListener, // 注册全局消息通知监听器组件\n  },\n  data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      logo: '/logo.png',\n      avatarUrl: '/logo.png',\n      token: '',\n      greatLeaderboard: [],\n      starLeaderboard: [],\n      courseLeaderboard: [],\n      page: {\n        starLeaderboard: 0,\n        courseLeaderboard: 0,\n        greatLeaderboard: 0,\n      },\n      // 添加日期时间相关数据\n      currentDateTime: '',\n      datetimeTimer: null,\n      // 添加窗口调整监听器\n      resizeHandler: null,\n      // 添加DOM变化观察器\n      observer: null\n    }\n  },\n  created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo\n    }\n    if (this.getFileAccessHttpUrl(this.avatar())) {\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    }\n    this.getGreatLeaderboard()\n    this.getStarLeaderboard()\n    // this.getCourseLeaderboard()\n    \n    // 初始化日期时间并设置定时更新\n    this.updateDateTime()\n    this.datetimeTimer = setInterval(this.updateDateTime, 1000) // 每秒更新一次\n  },\n  mounted() {\n    // 添加初始化日期时间组件位置的逻辑\n    this.$nextTick(() => {\n      this.updateDatetimePosition()\n      \n      // 添加窗口大小变化监听器\n      this.resizeHandler = () => this.updateDatetimePosition()\n      window.addEventListener('resize', this.resizeHandler)\n      \n      // 添加定时器，确保在页面元素加载完成后再次调整位置\n      setTimeout(() => {\n        this.updateDatetimePosition()\n      }, 500)\n      \n      // 添加DOM变化观察器，监听可能的按钮动态加载\n      this.observer = new MutationObserver(() => {\n        this.updateDatetimePosition()\n      })\n      \n      // 监听整个文档的变化，特别关注子节点的添加和移除\n      this.observer.observe(document.body, {\n        childList: true,\n        subtree: true\n      })\n      \n      // 初始位置可能需要多次尝试调整，因为其他组件可能延迟加载\n      const positionInterval = setInterval(() => {\n        this.updateDatetimePosition()\n      }, 1000)\n      \n      // 5秒后清除这个检查间隔，假设到那时所有元素都已加载完成\n      setTimeout(() => {\n        clearInterval(positionInterval)\n      }, 5000)\n    })\n  },\n  beforeDestroy() {\n    // 组件销毁前清除定时器，防止内存泄漏\n    if (this.datetimeTimer) {\n      clearInterval(this.datetimeTimer)\n      this.datetimeTimer = null\n    }\n    \n    // 移除窗口大小变化监听器\n    if (this.resizeHandler) {\n      window.removeEventListener('resize', this.resizeHandler)\n      this.resizeHandler = null\n    }\n    \n    // 断开DOM变化观察器\n    if (this.observer) {\n      this.observer.disconnect()\n      this.observer = null\n    }\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    ...mapActions(['Logout']),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    // 添加更新日期时间的方法\n    updateDateTime() {\n      this.currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss')\n    },\n    // 添加更新日期时间组件位置的方法\n    updateDatetimePosition() {\n      // 尝试获取通知按钮元素（使用多种选择器提高成功率）\n      let notificationBtn = document.querySelector('.header-notice')\n      \n      // 如果没找到，尝试其他可能的选择器\n      if (!notificationBtn) {\n        notificationBtn = document.querySelector('.ant-badge-count')\n      }\n      if (!notificationBtn) {\n        notificationBtn = document.querySelector('.ant-badge')\n      }\n      if (!notificationBtn) {\n        // 查找右上角所有的图标按钮\n        const allIcons = document.querySelectorAll('.right-content .action-item')\n        // 取最右边的图标之前的那个（通常是通知按钮）\n        if (allIcons && allIcons.length > 1) {\n          notificationBtn = allIcons[allIcons.length - 2]\n        }\n      }\n      \n      const datetimeWidget = this.$refs.datetimeWidget\n      \n      if (notificationBtn && datetimeWidget) {\n        // 获取通知按钮的位置信息\n        const btnRect = notificationBtn.getBoundingClientRect()\n        \n        // 计算日期时间组件的位置（在通知按钮左侧10px）\n        const rightPosition = window.innerWidth - btnRect.left + 10\n        \n        // 应用新的位置，同时确保不会超出屏幕\n        datetimeWidget.style.right = `${Math.max(10, rightPosition)}px`\n      } else {\n        // 如果找不到通知按钮，则根据屏幕宽度设置一个合适的位置\n        if (datetimeWidget) {\n          // 根据屏幕宽度动态调整位置\n          const screenWidth = window.innerWidth\n          if (screenWidth > 1200) {\n            datetimeWidget.style.right = '180px'\n          } else if (screenWidth > 768) {\n            datetimeWidget.style.right = '120px'\n          } else {\n            datetimeWidget.style.right = '80px'\n          }\n        }\n      }\n    },\n    formatDate(dateString) {\n      if (!dateString) return ''\n      return moment(dateString).format('YYYY-MM-DD HH:mm')\n    },\n    enter(type) {\n      switch(type){\n        case 0:this.$router.push('/user/login');break;\n        case 1:this.$router.push('/account/center');break;\n        case 2:this.$router.push('/teaching/mineCourse/cardList');break;\n        default:this.$router.push('/account/center');break;\n      }\n    },\n    changeAccount(){\n      const that = this\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk() {\n          return that.Logout({}).then(() => {\n            window.location.href=\"/user/login\";\n          }).catch(err => {\n            that.$message.error({\n              title: '错误',\n              description: err.message\n            })\n          })\n        },\n        onCancel() {\n        },\n      });\n    },\n    //获取精选作品\n    getGreatLeaderboard() {\n      this.page.greatLeaderboard += 1\n      getAction('/teaching/teachingWork/leaderboard', {\n        orderBy: 'create_time',\n        workStatus: 4,\n        pageSize: 4,\n        pageNo: this.page.greatLeaderboard,\n      }).then((res) => {\n        if (res.success) {\n          this.greatLeaderboard = this.greatLeaderboard.concat(res.result.records)\n          if(this.greatLeaderboard.length >= res.result.total){\n            this.page.greatLeaderboard = -1\n          }\n        }\n      })\n    },\n    //获取点赞排行\n    getStarLeaderboard() {\n      this.page.starLeaderboard += 1\n      getAction('/teaching/teachingWork/leaderboard', {\n        orderBy: 'star',\n        pageSize: 8,\n        pageNo: this.page.starLeaderboard,\n      }).then((res) => {\n        if (res.success) {\n          this.starLeaderboard = this.starLeaderboard.concat(res.result.records)\n          if(this.starLeaderboard.length >= res.result.total){\n            this.page.starLeaderboard = -1\n          }\n        }\n      })\n    },\n    //获取推荐课程\n    getCourseLeaderboard() {\n      this.page.courseLeaderboard += 1\n      getAction('/teaching/teachingCourse/getHomeCourse', {\n        pageSize: 4,\n        pageNo: this.page.courseLeaderboard,\n      }).then((res) => {\n        if (res.success) {\n          this.courseLeaderboard = this.courseLeaderboard.concat(res.result.records)\n          if(this.courseLeaderboard.length >= res.result.total){\n            this.page.courseLeaderboard = -1\n          }\n        }\n      })\n    },\n    toDetail(id) {\n      let route = this.$router.resolve({\n        path: \"/work-detail\",\n        query: {\n          id: id,\n        },\n      });\n      window.open(route.href, '_blank');\n    },\n    toCourseDetail(id){\n      this.$router.push('/teaching/mineCourse/courseUnitCard?id=' + id)\n    },\n    toEditor(type){\n      switch(type){\n        case 1:\n          window.open(\"/scratch3/index.html?scene=create\")\n          break;\n        case 2:\n          window.open(\"/scratchjr/home.html\")\n          break;\n        case 3:\n          window.open(\"/python/index.html\")\n          break;\n        case 4:\n          window.open(\"/cpp/index.html\")\n          break;\n      }\n    },\n    _isMobile() {\n      return (\n        navigator.userAgent.match(\n          /(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n        ) != null\n      )\n    },\n    // 更新依赖金币的组件\n    updateUserComponents() {\n      // 通知父组件HomeLayout更新金币数量\n      if (this.$parent && typeof this.$parent.updateUserCoin === 'function') {\n        this.$parent.updateUserCoin()\n      }\n    },\n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n  .editor-card{\n    width: 100%;\n    height: 200px;\n    margin: 30px auto;\n    padding: 20px;\n    background: linear-gradient(-30deg,#4fb5ff,#60bcff);\n    border-radius: 20px;\n    .ant-row-flex{\n      height: 100%;\n    }\n    img{\n      width: auto;\n      height: 100px;\n    }\n    h2{\n      color: white;\n      text-align: center;\n    }\n    .ant-btn{\n      border-radius: 50px;\n      display: block;\n      margin: 10px auto;\n    }\n  }\n  .panel-works {\n    margin-bottom: 60px;\n    padding: 20px;\n    background-color: #f9f9f9;\n    border-radius: 15px;\n    box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n  }\n  .panel-title {\n    font-size: 28px;\n    color: #333;\n    margin-bottom: 20px;\n  }\n  \n  .title-container {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 30px 0 15px;\n  }\n  \n  .title-text {\n    display: inline-block;\n    padding: 10px 30px;\n    background: linear-gradient(90deg, #FF9900, #FFCC00);\n    color: white;\n    border-radius: 30px;\n    font-weight: bold;\n    box-shadow: 0 4px 10px rgba(255, 153, 0, 0.4);\n    position: relative;\n    z-index: 2;\n    font-size: 22px;\n  }\n  \n  .title-decoration {\n    display: flex;\n    align-items: center;\n    margin: 0 15px;\n    position: relative;\n    \n    &.left {\n      flex-direction: row;\n    }\n    \n    &.right {\n      flex-direction: row-reverse;\n    }\n    \n    .decoration-circle {\n      width: 10px;\n      height: 10px;\n      border-radius: 50%;\n      background: #FFD700;\n      margin: 0 5px;\n      animation: pulse 2s infinite;\n    }\n    \n    .decoration-line {\n      height: 2px;\n      width: 30px;\n      background: linear-gradient(90deg, transparent, #FFD700, transparent);\n    }\n    \n    .decoration-star {\n      width: 15px;\n      height: 15px;\n      background: #FFCC00;\n      clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n      margin: 0 5px;\n      animation: spin 5s linear infinite;\n    }\n  }\n  \n  .title-separator {\n    height: 2px;\n    width: 100%;\n    background: linear-gradient(90deg, transparent, #FFD700, transparent);\n    margin-bottom: 20px;\n    border-radius: 3px;\n  }\n  \n  @keyframes pulse {\n    0% { transform: scale(1); }\n    50% { transform: scale(1.2); }\n    100% { transform: scale(1); }\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n\n  .work-card {\n    border-radius: 10px;\n    overflow: hidden;\n    box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n    min-height: 300px;\n    height: 100%;\n    min-width: 200px;\n    display: flex;\n    flex-direction: column;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n      border: 1px solid rgba(79, 181, 255, 0.3);\n    }\n    \n    /deep/.ant-card-body {\n      padding: 0px;\n      display: flex;\n      flex-direction: column;\n      flex: 1;\n    }\n    \n    a {\n      display: block;\n      height: 200px;\n      overflow: hidden;\n      position: relative;\n      text-align: center;\n    }\n    \n    .work-cover {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      transition: all 0.3s ease;\n    }\n    \n    img[src*=\"code.png\"] {\n      object-fit: contain;\n      padding: 20px;\n      max-width: 100%;\n      max-height: 100%;\n      width: auto;\n      height: auto;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n    }\n    \n    &:hover .work-cover {\n      transform: scale(1.05);\n    }\n    \n    .work-stats-row {\n      position: absolute;\n      top: -15px;\n      left: 0;\n      right: 0;\n      background: rgba(255, 255, 255, 0.9);\n      padding: 8px 10px;\n      border-radius: 0 0 10px 10px;\n      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n      z-index: 2;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    }\n    \n    .stats-left {\n      display: flex;\n      align-items: center;\n    }\n    \n    .stats-right {\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n    }\n    \n    .work-info{\n      padding: 15px;\n      padding-top: 35px;\n      transition: all 0.3s ease;\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      position: relative;\n    }\n    \n    .work-info p {\n      margin-top: 5px;\n      margin-bottom: 10px;\n      word-break: break-all;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n    \n    &:hover .work-info {\n      background-color: #f0f8ff;\n    }\n    \n    .work-author {\n      margin-top: auto;\n      span {\n        line-height: 40px;\n        display: block;\n      }\n    }\n    \n    /* 添加提交时间样式 */\n    .work-submit-time {\n      font-size: 12px;\n      color: #999;\n      margin-top: -5px;\n    }\n    .ant-tag {\n      float: right;\n    }\n    > div {\n      padding: 10px;\n      margin: 10px;\n    }\n  }\n  .load-more {\n    display: block;\n    margin: 20px auto;\n    text-align: left;\n    color: #1890ff;\n    font-weight: bold;\n    \n    &:hover {\n      text-decoration: underline;\n    }\n  }\n\n  .language-tag {\n    font-size: 16px;\n    font-weight: bold;\n    padding: 5px 12px;\n    border-radius: 6px;\n    background: linear-gradient(45deg, #4fb5ff, #6bc3ff);\n    color: white;\n    border: none;\n    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);\n    display: inline-block;\n    margin: 0;\n  }\n\n  /* 添加日期时间小组件样式 */\n  .datetime-widget {\n    position: fixed;\n    top: 20px;\n    /* right: 180px; 移除固定位置，由JS动态控制 */\n    z-index: 1000;\n    background: transparent; /* 改为透明背景 */\n    padding: 5px 12px;\n    border-radius: 20px;\n    display: flex;\n    align-items: center;\n    gap: 5px;\n    font-size: 16px; /* 增大字号 */\n    color: white; /* 改为白色文字，与通知按钮一致 */\n    box-shadow: none; /* 移除阴影 */\n    transition: right 0.3s ease; /* 添加过渡效果使位置变化更平滑 */\n  }\n</style>"]}]}