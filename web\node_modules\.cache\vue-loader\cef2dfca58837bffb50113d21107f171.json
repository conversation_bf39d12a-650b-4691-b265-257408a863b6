{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\CourseList.vue?vue&type=style&index=0&id=1a024a02&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\CourseList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n.panel-works {\n  margin: 30px 0;\n}\n.panel-title {\n  margin-top: 24px;\n  font-size: 26px;\n  color: #333;\n}\n.work-card {\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n  max-height: 300px;\n  min-width: 200px;\n  /deep/.ant-card-body {\n    padding: 0px;\n  }\n  .work-cover {\n    width: 100%;\n    max-height: 150px;\n  }\n  .work-info{\n    padding: 10px;\n  }\n  .work-author {\n    span {\n      line-height: 40px;\n    }\n  }\n  .ant-tag {\n    float: right;\n  }\n  > div {\n    padding: 10px;\n    margin: 10px;\n  }\n}\n.load-more {\n  display: block;\n  margin: 10px auto;\n  text-align: center;\n}\n", {"version": 3, "sources": ["CourseList.vue"], "names": [], "mappings": ";;AAsMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CourseList.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div>\n      <div class=\"panel-works\">\n        <a-card class=\"search-card\" :bordered=\"false\">\n          <j-dict-select-tag\n            style=\"width: 200px;\"\n            :defaultShowAll=\"true\"\n            @change=\"handleChangeCategory\"\n            v-model=\"courseCategory\"\n            :trigger-change=\"true\"\n            dictCode=\"course_category\"\n            plcaeholder=\"请选择课程分类\"\n          />\n          <a-divider type=\"vertical\"></a-divider>\n          <j-dict-select-tag\n            style=\"width: 200px;\"\n            :defaultShowAll=\"true\"\n            @change=\"handleChangeType\"\n            v-model=\"courseType\"\n            :trigger-change=\"true\"\n            dictCode=\"course_type\"\n             plcaeholder=\"请选择课程性质\"\n          />\n          <a-divider type=\"vertical\"></a-divider>\n          <a-input-search @search=\"onSearch\" style=\"width: 200px;\" placeholder=\"请输入课程名称\"></a-input-search>\n        </a-card>\n        <h1 class=\"panel-title\">\n          <a-icon type=\"calculator\" theme=\"twoTone\" />\n          推荐课程\n        </h1>\n        <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n          <a-col v-for=\"(item, index) in datasource\" :key=\"index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n            <a-card class=\"work-card\">\n              <a @click=\"toDetail(item)\" target=\"_blank\">\n                <img class=\"work-cover\" :src=\"item.courseCover_url\" />\n              </a>\n              <div class=\"work-info\">\n                <p>{{ item.courseName }}</p>\n              </div>\n            </a-card>\n          </a-col>\n        </a-row>\n        <a-spin style=\"margin:50px auto;\" v-if=\"loading\"/>\n        <a-empty v-if=\"!loading && datasource.length==0\"/>\n        <a-button v-if=\"!loading && datasource.length>0 && page>-1\" class=\"load-more\" type=\"dash\" @click=\"getData\">加载更多……</a-button>\n      </div>\n\n      <j-modal \n      :visible=\"showCourseDetail\" \n      :title=\"currentCourse.courseName\"\n      :width=\"500\"\n      @cancel=\"showCourseDetail=false\"\n      @ok=\"toCourse\"\n      okText=\"去上课\"\n      cancelText=\"关闭\"\n      >\n        <div v-html=\"currentCourse.courseDesc\"></div>\n      </j-modal>\n  </div>\n</template>\n\n<script>\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\nimport Header from './modules/Header'\nimport Banner from './modules/Banner'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\nimport QrCode from '@/components/tools/QrCode'\nexport default {\n  name: 'CoursekList',\n    components: {\n    qrcode: QrCode,\n    Header,\n    Footer,\n    UserEnter,\n    Banner\n  },\n  data() {\n    return {\n      loading: false,\n      datasource: [],\n      page: 0,\n      courseType: '',\n      courseCategory: '',\n      courseName: '',\n      showCourseDetail: false,\n      currentCourse: {}\n    }\n  },\n  created() {\n    this.getData()\n  },\n   methods: {\n    getFileAccessHttpUrl,\n    onSearch(v){\n      this.courseName = v\n      this.page = 0\n      this.datasource = []\n      this.getData()\n    },\n    handleChangeCategory(v){\n      this.courseCategory = v\n      this.page = 0\n      this.datasource = []\n      this.getData()\n    },\n    handleChangeType(v){\n      this.courseType = v\n      this.page = 0\n      this.datasource = []\n      this.getData()\n    },\n    getData() {\n      this.loading = true\n      this.page += 1\n      getAction('/teaching/teachingCourse/getHomeCourse', {\n        courseType:this.courseType,\n        courseCategory:this.courseCategory,\n        courseName:this.courseName,\n        orderBy: 'time',\n        pageSize: this._isMobile() ? 12 : 24,\n        pageNo: this.page,\n      }).then((res) => {\n        this.loading = false\n        if (res.success) {\n          this.datasource = this.datasource.concat(res.result.records)\n           if(this.datasource.length >= res.result.total){\n            this.page = -1\n          }\n        }\n      })\n    },\n    toDetail(item) {\n      console.log(item);\n      this.showCourseDetail = true\n      this.currentCourse = item\n      \n    },\n    toCourse(){\n      this.$router.push('/teaching/mineCourse/courseUnitCard?id=' + this.currentCourse.id)\n    },\n    _isMobile() {\n      return (\n        navigator.userAgent.match(\n          /(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n        ) != null\n      )\n    },\n   }\n}\n</script>\n\n<style lang=\"less\" scoped>\n\n  .panel-works {\n    margin: 30px 0;\n  }\n  .panel-title {\n    margin-top: 24px;\n    font-size: 26px;\n    color: #333;\n  }\n  .work-card {\n    border-radius: 10px;\n    overflow: hidden;\n    box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n    max-height: 300px;\n    min-width: 200px;\n    /deep/.ant-card-body {\n      padding: 0px;\n    }\n    .work-cover {\n      width: 100%;\n      max-height: 150px;\n    }\n    .work-info{\n      padding: 10px;\n    }\n    .work-author {\n      span {\n        line-height: 40px;\n      }\n    }\n    .ant-tag {\n      float: right;\n    }\n    > div {\n      padding: 10px;\n      margin: 10px;\n    }\n  }\n  .load-more {\n    display: block;\n    margin: 10px auto;\n    text-align: center;\n  }\n</style>"]}]}