{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\_util\\StringUtil.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\_util\\StringUtil.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["/**\n * 获取字符串的长度ascii长度为1 中文长度为2\n * @param str\n * @returns {number}\n */\nexport var getStrFullLength = function getStrFullLength() {\n  var str = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return str.split('').reduce(function (pre, cur) {\n    var charCode = cur.charCodeAt(0);\n    if (charCode >= 0 && charCode <= 128) {\n      return pre + 1;\n    }\n    return pre + 2;\n  }, 0);\n};\n\n/**\n * 给定一个字符串和一个长度,将此字符串按指定长度截取\n * @param str\n * @param maxLength\n * @returns {string}\n */\nexport var cutStrByFullLength = function cutStrByFullLength() {\n  var str = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var maxLength = arguments.length > 1 ? arguments[1] : undefined;\n  var showLength = 0;\n  return str.split('').reduce(function (pre, cur) {\n    var charCode = cur.charCodeAt(0);\n    if (charCode >= 0 && charCode <= 128) {\n      showLength += 1;\n    } else {\n      showLength += 2;\n    }\n    if (showLength <= maxLength) {\n      return pre + cur;\n    }\n    return pre;\n  }, '');\n};", {"version": 3, "names": ["getStr<PERSON>ull<PERSON><PERSON>th", "str", "arguments", "length", "undefined", "split", "reduce", "pre", "cur", "charCode", "charCodeAt", "cut<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "showLength"], "sources": ["E:/teachingproject/teaching/web/src/components/_util/StringUtil.js"], "sourcesContent": ["/**\n * 获取字符串的长度ascii长度为1 中文长度为2\n * @param str\n * @returns {number}\n */\nexport const getStrFullLength = (str = '') =>\n  str.split('').reduce((pre, cur) => {\n    const charCode = cur.charCodeAt(0)\n    if (charCode >= 0 && charCode <= 128) {\n      return pre + 1\n    }\n    return pre + 2\n  }, 0)\n\n/**\n * 给定一个字符串和一个长度,将此字符串按指定长度截取\n * @param str\n * @param maxLength\n * @returns {string}\n */\nexport const cutStrByFullLength = (str = '', maxLength) => {\n  let showLength = 0\n  return str.split('').reduce((pre, cur) => {\n    const charCode = cur.charCodeAt(0)\n    if (charCode >= 0 && charCode <= 128) {\n      showLength += 1\n    } else {\n      showLength += 2\n    }\n    if (showLength <= maxLength) {\n      return pre + cur\n    }\n    return pre\n  }, '')\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMA,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,IAAIC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,OACvCD,GAAG,CAACI,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;IACjC,IAAMC,QAAQ,GAAGD,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IAClC,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,GAAG,EAAE;MACpC,OAAOF,GAAG,GAAG,CAAC;IAChB;IACA,OAAOA,GAAG,GAAG,CAAC;EAChB,CAAC,EAAE,CAAC,CAAC;AAAA;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAA4B;EAAA,IAAxBV,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEU,SAAS,GAAAV,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACpD,IAAIS,UAAU,GAAG,CAAC;EAClB,OAAOZ,GAAG,CAACI,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;IACxC,IAAMC,QAAQ,GAAGD,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IAClC,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,GAAG,EAAE;MACpCI,UAAU,IAAI,CAAC;IACjB,CAAC,MAAM;MACLA,UAAU,IAAI,CAAC;IACjB;IACA,IAAIA,UAAU,IAAID,SAAS,EAAE;MAC3B,OAAOL,GAAG,GAAGC,GAAG;IAClB;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC", "ignoreList": []}]}