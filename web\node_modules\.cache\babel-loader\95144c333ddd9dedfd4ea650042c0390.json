{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue?vue&type=template&id=24a56e6e&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue", "mtime": 1749545283056}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-container\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-content\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"footer-right\"\n  }, [_c(\"div\", {\n    staticClass: \"contact-info\"\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"phone\"\n    }\n  }), _vm._v(\" 联系电话: 19866725905\\n        \"), _c(\"a-icon\", {\n    staticStyle: {\n      \"margin-left\": \"15px\"\n    },\n    attrs: {\n      type: \"mail\"\n    }\n  }), _vm._v(\" 邮箱: <EMAIL>\\n      \")], 1), _c(\"div\", {\n    staticClass: \"social-media\"\n  }, [_c(\"a-popover\", {\n    attrs: {\n      placement: \"top\",\n      trigger: \"hover\"\n    }\n  }, [_c(\"template\", {\n    slot: \"content\"\n  }, [_c(\"div\", {\n    staticClass: \"qrcode-container\"\n  }, [_c(\"img\", {\n    staticClass: \"qrcode-img\",\n    attrs: {\n      src: \"/img/wechat-qrcode.png\",\n      alt: \"微信二维码\"\n    }\n  }), _c(\"p\", [_vm._v(\"微信\")])])]), _c(\"a-icon\", {\n    staticClass: \"social-icon\",\n    attrs: {\n      type: \"wechat\"\n    }\n  })], 2), _c(\"a-popover\", {\n    attrs: {\n      placement: \"top\",\n      trigger: \"hover\"\n    }\n  }, [_c(\"template\", {\n    slot: \"content\"\n  }, [_c(\"div\", {\n    staticClass: \"qrcode-container\"\n  }, [_c(\"img\", {\n    staticClass: \"qrcode-img\",\n    attrs: {\n      src: \"/img/qq-qrcode.png\",\n      alt: \"QQ二维码\"\n    }\n  }), _c(\"p\", [_vm._v(\"QQ\")])])]), _c(\"a-icon\", {\n    staticClass: \"social-icon\",\n    attrs: {\n      type: \"qq\"\n    }\n  })], 2)], 1)])]), _c(\"div\", {\n    staticClass: \"footer-bottom\"\n  }, [_c(\"p\", [_vm._v(\"© \" + _vm._s(new Date().getFullYear()) + \" CFish科技工作室 - 版权所有\")])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-left\"\n  }, [_c(\"div\", {\n    staticClass: \"brand\"\n  }, [_c(\"h3\", [_vm._v(\"CFish科技工作室\")]), _c(\"p\", [_vm._v(\"科技少儿编程教学平台\")])]), _c(\"div\", {\n    staticClass: \"slogan\"\n  }, [_vm._v(\"赋能未来 · 智创童年\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "attrs", "type", "_v", "staticStyle", "placement", "trigger", "slot", "src", "alt", "_s", "Date", "getFullYear", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/Footer.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"footer-container\" }, [\n    _c(\"div\", { staticClass: \"footer-content\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"footer-right\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"contact-info\" },\n          [\n            _c(\"a-icon\", { attrs: { type: \"phone\" } }),\n            _vm._v(\" 联系电话: 19866725905\\n        \"),\n            _c(\"a-icon\", {\n              staticStyle: { \"margin-left\": \"15px\" },\n              attrs: { type: \"mail\" },\n            }),\n            _vm._v(\" 邮箱: <EMAIL>\\n      \"),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"social-media\" },\n          [\n            _c(\n              \"a-popover\",\n              { attrs: { placement: \"top\", trigger: \"hover\" } },\n              [\n                _c(\"template\", { slot: \"content\" }, [\n                  _c(\"div\", { staticClass: \"qrcode-container\" }, [\n                    _c(\"img\", {\n                      staticClass: \"qrcode-img\",\n                      attrs: {\n                        src: \"/img/wechat-qrcode.png\",\n                        alt: \"微信二维码\",\n                      },\n                    }),\n                    _c(\"p\", [_vm._v(\"微信\")]),\n                  ]),\n                ]),\n                _c(\"a-icon\", {\n                  staticClass: \"social-icon\",\n                  attrs: { type: \"wechat\" },\n                }),\n              ],\n              2\n            ),\n            _c(\n              \"a-popover\",\n              { attrs: { placement: \"top\", trigger: \"hover\" } },\n              [\n                _c(\"template\", { slot: \"content\" }, [\n                  _c(\"div\", { staticClass: \"qrcode-container\" }, [\n                    _c(\"img\", {\n                      staticClass: \"qrcode-img\",\n                      attrs: { src: \"/img/qq-qrcode.png\", alt: \"QQ二维码\" },\n                    }),\n                    _c(\"p\", [_vm._v(\"QQ\")]),\n                  ]),\n                ]),\n                _c(\"a-icon\", {\n                  staticClass: \"social-icon\",\n                  attrs: { type: \"qq\" },\n                }),\n              ],\n              2\n            ),\n          ],\n          1\n        ),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"footer-bottom\" }, [\n      _c(\"p\", [\n        _vm._v(\n          \"© \" +\n            _vm._s(new Date().getFullYear()) +\n            \" CFish科技工作室 - 版权所有\"\n        ),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-left\" }, [\n      _c(\"div\", { staticClass: \"brand\" }, [\n        _c(\"h3\", [_vm._v(\"CFish科技工作室\")]),\n        _c(\"p\", [_vm._v(\"科技少儿编程教学平台\")]),\n      ]),\n      _c(\"div\", { staticClass: \"slogan\" }, [_vm._v(\"赋能未来 · 智创童年\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CACpDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,QAAQ,EAAE;IAAEI,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAQ;EAAE,CAAC,CAAC,EAC1CN,GAAG,CAACO,EAAE,CAAC,8BAA8B,CAAC,EACtCN,EAAE,CAAC,QAAQ,EAAE;IACXO,WAAW,EAAE;MAAE,aAAa,EAAE;IAAO,CAAC;IACtCH,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO;EACxB,CAAC,CAAC,EACFN,GAAG,CAACO,EAAE,CAAC,+BAA+B,CAAC,CACxC,EACD,CACF,CAAC,EACDN,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IAAEI,KAAK,EAAE;MAAEI,SAAS,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ;EAAE,CAAC,EACjD,CACET,EAAE,CAAC,UAAU,EAAE;IAAEU,IAAI,EAAE;EAAU,CAAC,EAAE,CAClCV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,YAAY;IACzBE,KAAK,EAAE;MACLO,GAAG,EAAE,wBAAwB;MAC7BC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFZ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,EACFN,EAAE,CAAC,QAAQ,EAAE;IACXE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAS;EAC1B,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDL,EAAE,CACA,WAAW,EACX;IAAEI,KAAK,EAAE;MAAEI,SAAS,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ;EAAE,CAAC,EACjD,CACET,EAAE,CAAC,UAAU,EAAE;IAAEU,IAAI,EAAE;EAAU,CAAC,EAAE,CAClCV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,YAAY;IACzBE,KAAK,EAAE;MAAEO,GAAG,EAAE,oBAAoB;MAAEC,GAAG,EAAE;IAAQ;EACnD,CAAC,CAAC,EACFZ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,EACFN,EAAE,CAAC,QAAQ,EAAE;IACXE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAK;EACtB,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACO,EAAE,CACJ,IAAI,GACFP,GAAG,CAACc,EAAE,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAChC,oBACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIjB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EAChCN,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAChC,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CAACH,GAAG,CAACO,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAC9D,CAAC;AACJ,CAAC,CACF;AACDR,MAAM,CAACmB,aAAa,GAAG,IAAI;AAE3B,SAASnB,MAAM,EAAEkB,eAAe", "ignoreList": []}]}