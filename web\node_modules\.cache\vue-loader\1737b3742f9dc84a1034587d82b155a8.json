{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { postAction } from '@/api/manage'\n  export default {\n    name: 'JImportModal',\n    props:{\n      url:{\n        type: String,\n        default: '',\n        required: false\n      },\n      biz:{\n        type: String,\n        default: '',\n        required: false\n      }\n    },\n    data(){\n      return {\n        visible:false,\n        uploading:false,\n        fileList:[],\n        uploadAction:'',\n        foreignKeys:''\n      }\n    },\n    watch: {\n      url (val) {\n        if(val){\n         this.uploadAction = window._CONFIG['domianURL']+val\n        }\n      }\n    },\n    created () {\n      this.uploadAction = window._CONFIG['domianURL']+this.url\n    },\n\n    methods:{\n      handleClose(){\n        this.visible=false\n      },\n      show(arg){\n        this.fileList = []\n        this.uploading = false\n        this.visible = true\n        this.foreignKeys = arg;\n      },\n      handleRemove(file) {\n        const index = this.fileList.indexOf(file);\n        const newFileList = this.fileList.slice();\n        newFileList.splice(index, 1);\n        this.fileList = newFileList\n      },\n      beforeUpload(file) {\n        this.fileList = [...this.fileList, file]\n        return false;\n      },\n      handleImport() {\n        const { fileList } = this;\n        const formData = new FormData();\n        if(this.biz){\n          formData.append('isSingleTableImport',this.biz);\n        }\n        if(this.foreignKeys && this.foreignKeys.length>0){\n          formData.append('foreignKeys',this.foreignKeys);\n        }\n        fileList.forEach((file) => {\n          formData.append('files[]', file);\n        });\n        this.uploading = true\n        postAction(this.uploadAction, formData).then((res) => {\n          this.uploading = false\n          if(res.success){\n            this.$message.success(res.message)\n            this.visible=false\n            this.$emit('ok')\n          }else{\n            this.$message.warning(res.message)\n          }\n        })\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["JImportModal.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "JImportModal.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-modal\n    title=\"导入EXCEL\"\n    :width=\"600\"\n    :visible=\"visible\"\n    :confirmLoading=\"uploading\"\n    @cancel=\"handleClose\">\n\n    <a-upload\n      name=\"file\"\n      :multiple=\"true\"\n      accept=\".xls,.xlsx\"\n      :fileList=\"fileList\"\n      :remove=\"handleRemove\"\n      :beforeUpload=\"beforeUpload\">\n      <a-button>\n        <a-icon type=\"upload\" />\n        选择导入文件\n      </a-button>\n    </a-upload>\n\n    <template slot=\"footer\">\n      <a-button @click=\"handleClose\">关闭</a-button>\n      <a-button\n        type=\"primary\"\n        @click=\"handleImport\"\n        :disabled=\"fileList.length === 0\"\n        :loading=\"uploading\">\n        {{ uploading ? '上传中...' : '开始上传' }}\n      </a-button>\n    </template>\n\n  </a-modal>\n</template>\n\n<script>\n  import { postAction } from '@/api/manage'\n  export default {\n    name: 'JImportModal',\n    props:{\n      url:{\n        type: String,\n        default: '',\n        required: false\n      },\n      biz:{\n        type: String,\n        default: '',\n        required: false\n      }\n    },\n    data(){\n      return {\n        visible:false,\n        uploading:false,\n        fileList:[],\n        uploadAction:'',\n        foreignKeys:''\n      }\n    },\n    watch: {\n      url (val) {\n        if(val){\n         this.uploadAction = window._CONFIG['domianURL']+val\n        }\n      }\n    },\n    created () {\n      this.uploadAction = window._CONFIG['domianURL']+this.url\n    },\n\n    methods:{\n      handleClose(){\n        this.visible=false\n      },\n      show(arg){\n        this.fileList = []\n        this.uploading = false\n        this.visible = true\n        this.foreignKeys = arg;\n      },\n      handleRemove(file) {\n        const index = this.fileList.indexOf(file);\n        const newFileList = this.fileList.slice();\n        newFileList.splice(index, 1);\n        this.fileList = newFileList\n      },\n      beforeUpload(file) {\n        this.fileList = [...this.fileList, file]\n        return false;\n      },\n      handleImport() {\n        const { fileList } = this;\n        const formData = new FormData();\n        if(this.biz){\n          formData.append('isSingleTableImport',this.biz);\n        }\n        if(this.foreignKeys && this.foreignKeys.length>0){\n          formData.append('foreignKeys',this.foreignKeys);\n        }\n        fileList.forEach((file) => {\n          formData.append('files[]', file);\n        });\n        this.uploading = true\n        postAction(this.uploadAction, formData).then((res) => {\n          this.uploading = false\n          if(res.success){\n            this.$message.success(res.message)\n            this.visible=false\n            this.$emit('ok')\n          }else{\n            this.$message.warning(res.message)\n          }\n        })\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}