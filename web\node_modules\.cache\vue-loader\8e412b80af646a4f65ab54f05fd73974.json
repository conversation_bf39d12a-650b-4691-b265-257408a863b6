{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\MediaWall.vue?vue&type=template&id=3e581990&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\MediaWall.vue", "mtime": 1749711343444}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"media-wall\">\n  <div class=\"media-wall-header\">\n    <h2>小小创客照片墙</h2>\n  </div>\n\n  <div class=\"media-wall-content\">\n    <div v-if=\"effectiveMediaItems.length > 0\" class=\"polaroid-grid\">\n      <div \n        v-for=\"(item, index) in effectiveMediaItems\" \n        :key=\"index\" \n        class=\"media-item polaroid\"\n        :style=\"{ transform: `rotate(${getRandomRotation(index)}deg)` }\"\n        @click=\"openMediaViewer(index)\"\n      >\n        <div class=\"polaroid-wrapper\">\n          <img \n            v-if=\"item.type === 'image'\" \n            :src=\"getFileUrl(item.thumbnail || item.url)\" \n            alt=\"缩略图\" \n            class=\"media-thumbnail\"\n          />\n          <div v-if=\"item.type === 'video'\" class=\"video-thumbnail-container\">\n            <img :src=\"getFileUrl(item.thumbnail)\" alt=\"视频封面\" class=\"media-thumbnail\" />\n            <div class=\"video-play-icon\">\n              <a-icon type=\"play-circle\" />\n            </div>\n          </div>\n          <div class=\"media-caption\">\n            <span>{{ item.title }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div v-else class=\"empty-media-container\">\n      <a-empty description=\"暂无照片\" />\n    </div>\n  </div>\n\n  <!-- 媒体查看器弹窗 -->\n  <a-modal\n    v-model=\"mediaViewerVisible\"\n    :footer=\"null\"\n    :width=\"900\"\n    :closable=\"false\"\n    :maskClosable=\"true\"\n    @cancel=\"closeMediaViewer\"\n    centered\n    destroyOnClose\n    class=\"media-viewer-modal\"\n  >\n    <div class=\"media-viewer\">\n      <div class=\"media-viewer-content\">\n        <img \n          v-if=\"currentMedia && currentMedia.type === 'image'\" \n          :src=\"getFileUrl(currentMedia.url)\" \n          class=\"viewer-image\"\n          alt=\"查看大图\"\n        />\n        <video \n          v-if=\"currentMedia && currentMedia.type === 'video'\"\n          :src=\"getFileUrl(currentMedia.url)\"\n          class=\"viewer-video\"\n          controls\n          autoplay\n        ></video>\n      </div>\n\n      <div class=\"media-viewer-footer\">\n        <div class=\"media-info\">\n          <h3>{{ currentMedia ? currentMedia.title : '' }}</h3>\n        </div>\n        <div class=\"media-navigation\">\n          <a-button \n            shape=\"circle\" \n            icon=\"left\"\n            @click=\"navigateMedia(-1)\"\n            :disabled=\"currentMediaIndex === 0\"\n          ></a-button>\n          <span class=\"media-count\">{{ currentMediaIndex + 1 }} / {{ effectiveMediaItems.length }}</span>\n          <a-button \n            shape=\"circle\" \n            icon=\"right\"\n            @click=\"navigateMedia(1)\"\n            :disabled=\"currentMediaIndex === effectiveMediaItems.length - 1\"\n          ></a-button>\n        </div>\n        <div class=\"media-actions\">\n          <a-button type=\"primary\" @click=\"closeMediaViewer\">关闭</a-button>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</div>\n", null]}