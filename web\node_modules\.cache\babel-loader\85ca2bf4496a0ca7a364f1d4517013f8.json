{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectRole.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import JSelectBizComponent from './JSelectBizComponent';\nexport default {\n  name: 'JSelectMultiUser',\n  components: {\n    JSelectBizComponent: JSelectBizComponent\n  },\n  props: ['value'],\n  data: function data() {\n    return {\n      returnKeys: ['id', 'roleCode'],\n      url: {\n        list: '/sys/role/list'\n      },\n      columns: [{\n        title: '角色名称',\n        dataIndex: 'roleName',\n        align: 'center',\n        width: 120\n      }, {\n        title: '角色编码',\n        dataIndex: 'roleCode',\n        align: 'center',\n        width: 120\n      }]\n    };\n  }\n};", {"version": 3, "names": ["JSelectBizComponent", "name", "components", "props", "data", "returnKeys", "url", "list", "columns", "title", "dataIndex", "align", "width"], "sources": ["src/components/jeecgbiz/JSelectRole.vue"], "sourcesContent": ["<template>\n  <j-select-biz-component\n    :value=\"value\"\n\n    name=\"角色\"\n    displayKey=\"roleName\"\n\n    :returnKeys=\"returnKeys\"\n    :listUrl=\"url.list\"\n    :columns=\"columns\"\n    queryParamText=\"角色编码\"\n\n    v-on=\"$listeners\"\n    v-bind=\"$attrs\"\n  />\n</template>\n\n<script>\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        returnKeys: ['id', 'roleCode'],\n        url: { list: '/sys/role/list' },\n        columns: [\n          { title: '角色名称', dataIndex: 'roleName', align: 'center', width: 120 },\n          { title: '角色编码', dataIndex: 'roleCode', align: 'center', width: 120 }\n        ]\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped></style>"], "mappings": "AAkBA,OAAAA,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,mBAAA,EAAAA;EAAA;EACAG,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,GAAA;QAAAC,IAAA;MAAA;MACAC,OAAA,GACA;QAAAC,KAAA;QAAAC,SAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,SAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}