{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport AreaChartTy from '@/components/chart/AreaChartTy';\nimport Bar from '@/components/chart/Bar';\nimport BarMultid from '@/components/chart/BarMultid';\nimport DashChartDemo from '@/components/chart/DashChartDemo';\nimport LineChartMultid from '@/components/chart/LineChartMultid';\nimport Liquid from '@/components/chart/Liquid';\nimport MiniBar from '@/components/chart/MiniBar';\nimport MiniArea from '@/components/chart/MiniArea';\nimport MiniProgress from '@/components/chart/MiniProgress';\nimport Pie from '@/components/chart/Pie';\nimport Radar from '@/components/chart/Radar';\nimport RankList from '@/components/chart/RankList';\nimport TransferBar from '@/components/chart/TransferBar';\nimport Trend from '@/components/chart/Trend';\nimport BarAndLine from '@/components/chart/BarAndLine';\nexport default {\n  name: 'ViserChartDemo',\n  components: {\n    Bar: Bar,\n    MiniBar: MiniBar,\n    BarMultid: BarMultid,\n    AreaChartTy: AreaChartTy,\n    LineChartMultid: LineChartMultid,\n    Pie: Pie,\n    Radar: Radar,\n    DashChartDemo: DashChartDemo,\n    MiniProgress: MiniProgress,\n    RankList: RankList,\n    TransferBar: TransferBar,\n    Trend: Trend,\n    Liquid: Liquid,\n    MiniArea: MiniArea,\n    BarAndLine: BarAndLine\n  },\n  data: function data() {\n    return {\n      height: 420,\n      rankList: [],\n      barData: [],\n      areaData: []\n    };\n  },\n  created: function created() {\n    var _this = this;\n    setTimeout(function () {\n      _this.loadBarData();\n      _this.loadAreaData();\n      _this.loadRankListData();\n    }, 100);\n  },\n  methods: {\n    loadData: function loadData(x, y, max, min) {\n      var before = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : '';\n      var after = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : '月';\n      var data = [];\n      for (var i = 0; i < 12; i += 1) {\n        data.push(_defineProperty(_defineProperty({}, x, \"\".concat(before).concat(i + 1).concat(after)), y, Math.floor(Math.random() * max) + min));\n      }\n      return data;\n    },\n    // 加载柱状图数据\n    loadBarData: function loadBarData() {\n      this.barData = this.loadData('x', 'y', 1000, 200);\n    },\n    // 加载AreaChartTy的数据\n    loadAreaData: function loadAreaData() {\n      this.areaData = this.loadData('x', 'y', 500, 100);\n    },\n    loadRankListData: function loadRankListData() {\n      this.rankList = this.loadData('name', 'total', 2000, 100, '北京朝阳 ', ' 号店');\n    }\n  }\n};", {"version": 3, "names": ["AreaChartTy", "Bar", "BarMultid", "DashChartDemo", "LineChartMultid", "Liquid", "MiniBar", "MiniArea", "MiniProgress", "Pie", "Radar", "RankList", "TransferBar", "Trend", "BarAndLine", "name", "components", "data", "height", "rankList", "barData", "areaData", "created", "_this", "setTimeout", "loadBarData", "loadAreaData", "loadRankListData", "methods", "loadData", "x", "y", "max", "min", "before", "arguments", "length", "undefined", "after", "i", "push", "_defineProperty", "concat", "Math", "floor", "random"], "sources": ["src/views/jeecg/report/ViserChartDemo.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-tabs defaultActiveKey=\"1\">\n      <!-- 柱状图 -->\n      <a-tab-pane tab=\"柱状图\" key=\"1\">\n        <bar title=\"销售额排行\" :dataSource=\"barData\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 多列柱状图 -->\n      <a-tab-pane tab=\"多列柱状图\" key=\"2\">\n        <bar-multid title=\"多列柱状图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 迷你柱状图 -->\n      <a-tab-pane tab=\"迷你柱状图\" key=\"3\">\n        <mini-bar :dataSource=\"barData\" :width=\"400\" :height=\"200\"/>\n      </a-tab-pane>\n      <!-- 面积图 -->\n      <a-tab-pane tab=\"面积图\" key=\"4\">\n        <area-chart-ty title=\"销售额排行\" :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 迷你面积图 -->\n      <a-tab-pane tab=\"迷你面积图\" key=\"5\">\n        <div style=\"padding-top: 100px;width:600px;height:200px\">\n          <mini-area :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n        </div>\n      </a-tab-pane>\n      <!-- 多行折线图 -->\n      <a-tab-pane tab=\"多行折线图\" key=\"6\">\n        <line-chart-multid title=\"多行折线图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 饼图 -->\n      <a-tab-pane tab=\"饼图\" key=\"7\">\n        <pie title=\"饼图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 雷达图 -->\n      <a-tab-pane tab=\"雷达图\" key=\"8\">\n        <radar title=\"雷达图\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 仪表盘 -->\n      <a-tab-pane tab=\"仪表盘\" key=\"9\">\n        <dash-chart-demo title=\"仪表盘\" :value=\"9\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- 进度条 -->\n      <a-tab-pane tab=\"进度条\" key=\"10\">\n        <mini-progress :percentage=\"30\" :target=\"40\" :height=\"30\"/>\n        <mini-progress :percentage=\"51\" :target=\"60\" :height=\"30\" color=\"#FFA500\"/>\n        <mini-progress :percentage=\"66\" :target=\"80\" :height=\"30\" color=\"#1E90FF\"/>\n        <mini-progress :percentage=\"74\" :target=\"70\" :height=\"30\" color=\"#FF4500\"/>\n        <mini-progress :percentage=\"92\" :target=\"100\" :height=\"30\" color=\"#49CC49\"/>\n      </a-tab-pane>\n      <!-- 排名列表 -->\n      <a-tab-pane tab=\"排名列表\" key=\"11\">\n        <rank-list title=\"门店销售排行榜\" :list=\"rankList\" style=\"width: 600px;margin: 0 auto;\"/>\n      </a-tab-pane>\n      <!-- TransferBar -->\n      <a-tab-pane tab=\"TransferBar\" key=\"12\">\n        <transfer-bar title=\"年度消耗流量一览表\" :data=\"barData\" x=\"月份\" y=\"流量(Mb)\" :height=\"height\"/>\n      </a-tab-pane>\n      <!-- Trend -->\n      <a-tab-pane tab=\"Trend\" key=\"13\">\n        <trend title=\"Trend\" term=\"Trend：\" :percentage=\"30\"/>\n      </a-tab-pane>\n      <!-- Liquid -->\n      <a-tab-pane tab=\"Liquid\" key=\"14\">\n        <liquid :height=\"height\"/>\n      </a-tab-pane>\n      <!-- BarAndLine -->\n      <a-tab-pane tab=\"BarAndLine\" key=\"15\">\n        <bar-and-line :height=\"height\"/>\n      </a-tab-pane>\n    </a-tabs>\n  </a-card>\n</template>\n\n<script>\n  import AreaChartTy from '@/components/chart/AreaChartTy'\n  import Bar from '@/components/chart/Bar'\n  import BarMultid from '@/components/chart/BarMultid'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import Liquid from '@/components/chart/Liquid'\n  import MiniBar from '@/components/chart/MiniBar'\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import Pie from '@/components/chart/Pie'\n  import Radar from '@/components/chart/Radar'\n  import RankList from '@/components/chart/RankList'\n  import TransferBar from '@/components/chart/TransferBar'\n  import Trend from '@/components/chart/Trend'\n  import BarAndLine from '@/components/chart/BarAndLine'\n\n  export default {\n    name: 'ViserChartDemo',\n    components: {\n      Bar, MiniBar, BarMultid, AreaChartTy, LineChartMultid,\n      Pie, Radar, DashChartDemo, MiniProgress, RankList,\n      TransferBar, Trend, Liquid, MiniArea, BarAndLine\n    },\n    data() {\n      return {\n        height: 420,\n        rankList: [],\n        barData: [],\n        areaData: []\n      }\n    },\n    created() {\n      setTimeout(() => {\n        this.loadBarData()\n        this.loadAreaData()\n        this.loadRankListData()\n      }, 100)\n    },\n    methods: {\n      loadData(x, y, max, min, before = '', after = '月') {\n        let data = []\n        for (let i = 0; i < 12; i += 1) {\n          data.push({\n            [x]: `${before}${i + 1}${after}`,\n            [y]: Math.floor(Math.random() * max) + min\n          })\n        }\n        return data\n      },\n      // 加载柱状图数据\n      loadBarData() {\n        this.barData = this.loadData('x', 'y', 1000, 200)\n      },\n      // 加载AreaChartTy的数据\n      loadAreaData() {\n        this.areaData = this.loadData('x', 'y', 500, 100)\n      },\n      loadRankListData() {\n        this.rankList = this.loadData('name', 'total', 2000, 100, '北京朝阳 ', ' 号店')\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n"], "mappings": ";;;;AA0EA,OAAAA,WAAA;AACA,OAAAC,GAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,eAAA;AACA,OAAAC,MAAA;AACA,OAAAC,OAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,GAAA;AACA,OAAAC,KAAA;AACA,OAAAC,QAAA;AACA,OAAAC,WAAA;AACA,OAAAC,KAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAf,GAAA,EAAAA,GAAA;IAAAK,OAAA,EAAAA,OAAA;IAAAJ,SAAA,EAAAA,SAAA;IAAAF,WAAA,EAAAA,WAAA;IAAAI,eAAA,EAAAA,eAAA;IACAK,GAAA,EAAAA,GAAA;IAAAC,KAAA,EAAAA,KAAA;IAAAP,aAAA,EAAAA,aAAA;IAAAK,YAAA,EAAAA,YAAA;IAAAG,QAAA,EAAAA,QAAA;IACAC,WAAA,EAAAA,WAAA;IAAAC,KAAA,EAAAA,KAAA;IAAAR,MAAA,EAAAA,MAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAO,UAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,UAAA;MACAD,KAAA,CAAAE,WAAA;MACAF,KAAA,CAAAG,YAAA;MACAH,KAAA,CAAAI,gBAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,CAAA,EAAAC,CAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,KAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAlB,IAAA;MACA,SAAAsB,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACAtB,IAAA,CAAAuB,IAAA,CAAAC,eAAA,CAAAA,eAAA,KACAX,CAAA,KAAAY,MAAA,CAAAR,MAAA,EAAAQ,MAAA,CAAAH,CAAA,MAAAG,MAAA,CAAAJ,KAAA,IACAP,CAAA,EAAAY,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAb,GAAA,IAAAC,GAAA,CACA;MACA;MACA,OAAAhB,IAAA;IACA;IACA;IACAQ,WAAA,WAAAA,YAAA;MACA,KAAAL,OAAA,QAAAS,QAAA;IACA;IACA;IACAH,YAAA,WAAAA,aAAA;MACA,KAAAL,QAAA,QAAAQ,QAAA;IACA;IACAF,gBAAA,WAAAA,iBAAA;MACA,KAAAR,QAAA,QAAAU,QAAA;IACA;EACA;AACA", "ignoreList": []}]}