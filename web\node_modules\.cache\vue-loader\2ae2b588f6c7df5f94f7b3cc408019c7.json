{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        url: { list: '/sys/user/list' },\n        columns: [\n          { title: '姓名', align: 'center', width: '25%', widthRight: '70%', dataIndex: 'realname' },\n          { title: '账号', align: 'center', width: '25%', dataIndex: 'username' },\n          { title: '电话', align: 'center', width: '20%', dataIndex: 'phone' },\n          { title: '出生日期', align: 'center', width: '20%', dataIndex: 'birthday' }\n        ],\n        // 定义在这里的参数都是可以在外部传递覆盖的，可以更灵活的定制化使用的组件\n        default: {\n          name: '用户',\n          width: 1200,\n          displayKey: 'realname',\n          returnKeys: ['id', 'username'],\n          queryParamText: '账号',\n        }\n      }\n    },\n    computed: {\n      attrs() {\n        return Object.assign(this.default, this.$attrs)\n      }\n    }\n  }\n", {"version": 3, "sources": ["JSelectMultiUser.vue"], "names": [], "mappings": ";AAaA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JSelectMultiUser.vue", "sourceRoot": "src/components/jeecgbiz", "sourcesContent": ["<template>\n  <!-- 定义在这里的参数都是不可在外部覆盖的，防止出现问题 -->\n  <j-select-biz-component\n    :value=\"value\"\n    :ellipsisLength=\"25\"\n    :listUrl=\"url.list\"\n    :columns=\"columns\"\n    v-on=\"$listeners\"\n    v-bind=\"attrs\"\n  />\n</template>\n\n<script>\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        url: { list: '/sys/user/list' },\n        columns: [\n          { title: '姓名', align: 'center', width: '25%', widthRight: '70%', dataIndex: 'realname' },\n          { title: '账号', align: 'center', width: '25%', dataIndex: 'username' },\n          { title: '电话', align: 'center', width: '20%', dataIndex: 'phone' },\n          { title: '出生日期', align: 'center', width: '20%', dataIndex: 'birthday' }\n        ],\n        // 定义在这里的参数都是可以在外部传递覆盖的，可以更灵活的定制化使用的组件\n        default: {\n          name: '用户',\n          width: 1200,\n          displayKey: 'realname',\n          returnKeys: ['id', 'username'],\n          queryParamText: '账号',\n        }\n      }\n    },\n    computed: {\n      attrs() {\n        return Object.assign(this.default, this.$attrs)\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped></style>"]}]}