{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue?vue&type=template&id=5b82e1da&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue", "mtime": 1751031689617}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"banner\"\n  }, [_c(\"notice-marquee\", {\n    ref: \"noticeMarquee\",\n    on: {\n      showNotice: _vm.showNoticeDetail\n    }\n  }), _vm.banner.length > 0 ? _c(\"div\", [_c(\"swiper\", {\n    ref: \"mySwiper\",\n    staticClass: \"swiper-wrappe carousel\",\n    attrs: {\n      options: _vm.swiperOptions\n    }\n  }, [_vm._l(_vm.banner, function (b, i) {\n    return _c(\"swiper-slide\", {\n      key: i,\n      staticClass: \"swiper-slide\"\n    }, [b.href ? _c(\"a\", {\n      attrs: {\n        href: b.href,\n        target: \"_blank\"\n      }\n    }, [_c(\"img\", {\n      attrs: {\n        src: b.img,\n        alt: \"\"\n      }\n    })]) : _c(\"img\", {\n      attrs: {\n        src: b.img,\n        alt: \"\"\n      }\n    })]);\n  }), _c(\"div\", {\n    staticClass: \"swiper-pagination swiper-pagination-white\",\n    attrs: {\n      slot: \"pagination\"\n    },\n    slot: \"pagination\"\n  }), _c(\"div\", {\n    staticClass: \"swiper-button-prev swiper-button-white\",\n    attrs: {\n      slot: \"button-prev\"\n    },\n    slot: \"button-prev\"\n  }), _c(\"div\", {\n    staticClass: \"swiper-button-next swiper-button-white\",\n    attrs: {\n      slot: \"button-next\"\n    },\n    slot: \"button-next\"\n  })], 2)], 1) : _vm._e(), _c(\"show-announcement\", {\n    ref: \"showAnnouncement\"\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "ref", "on", "showNotice", "showNoticeDetail", "banner", "length", "attrs", "options", "swiperOptions", "_l", "b", "i", "key", "href", "target", "src", "img", "alt", "slot", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/Banner.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"banner\" },\n    [\n      _c(\"notice-marquee\", {\n        ref: \"noticeMarquee\",\n        on: { showNotice: _vm.showNoticeDetail },\n      }),\n      _vm.banner.length > 0\n        ? _c(\n            \"div\",\n            [\n              _c(\n                \"swiper\",\n                {\n                  ref: \"mySwiper\",\n                  staticClass: \"swiper-wrappe carousel\",\n                  attrs: { options: _vm.swiperOptions },\n                },\n                [\n                  _vm._l(_vm.banner, function (b, i) {\n                    return _c(\n                      \"swiper-slide\",\n                      { key: i, staticClass: \"swiper-slide\" },\n                      [\n                        b.href\n                          ? _c(\n                              \"a\",\n                              { attrs: { href: b.href, target: \"_blank\" } },\n                              [_c(\"img\", { attrs: { src: b.img, alt: \"\" } })]\n                            )\n                          : _c(\"img\", { attrs: { src: b.img, alt: \"\" } }),\n                      ]\n                    )\n                  }),\n                  _c(\"div\", {\n                    staticClass: \"swiper-pagination swiper-pagination-white\",\n                    attrs: { slot: \"pagination\" },\n                    slot: \"pagination\",\n                  }),\n                  _c(\"div\", {\n                    staticClass: \"swiper-button-prev swiper-button-white\",\n                    attrs: { slot: \"button-prev\" },\n                    slot: \"button-prev\",\n                  }),\n                  _c(\"div\", {\n                    staticClass: \"swiper-button-next swiper-button-white\",\n                    attrs: { slot: \"button-next\" },\n                    slot: \"button-next\",\n                  }),\n                ],\n                2\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\"show-announcement\", { ref: \"showAnnouncement\" }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAS,CAAC,EACzB,CACEF,EAAE,CAAC,gBAAgB,EAAE;IACnBG,GAAG,EAAE,eAAe;IACpBC,EAAE,EAAE;MAAEC,UAAU,EAAEN,GAAG,CAACO;IAAiB;EACzC,CAAC,CAAC,EACFP,GAAG,CAACQ,MAAM,CAACC,MAAM,GAAG,CAAC,GACjBR,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CACA,QAAQ,EACR;IACEG,GAAG,EAAE,UAAU;IACfD,WAAW,EAAE,wBAAwB;IACrCO,KAAK,EAAE;MAAEC,OAAO,EAAEX,GAAG,CAACY;IAAc;EACtC,CAAC,EACD,CACEZ,GAAG,CAACa,EAAE,CAACb,GAAG,CAACQ,MAAM,EAAE,UAAUM,CAAC,EAAEC,CAAC,EAAE;IACjC,OAAOd,EAAE,CACP,cAAc,EACd;MAAEe,GAAG,EAAED,CAAC;MAAEZ,WAAW,EAAE;IAAe,CAAC,EACvC,CACEW,CAAC,CAACG,IAAI,GACFhB,EAAE,CACA,GAAG,EACH;MAAES,KAAK,EAAE;QAAEO,IAAI,EAAEH,CAAC,CAACG,IAAI;QAAEC,MAAM,EAAE;MAAS;IAAE,CAAC,EAC7C,CAACjB,EAAE,CAAC,KAAK,EAAE;MAAES,KAAK,EAAE;QAAES,GAAG,EAAEL,CAAC,CAACM,GAAG;QAAEC,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,CAChD,CAAC,GACDpB,EAAE,CAAC,KAAK,EAAE;MAAES,KAAK,EAAE;QAAES,GAAG,EAAEL,CAAC,CAACM,GAAG;QAAEC,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,CAErD,CAAC;EACH,CAAC,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,2CAA2C;IACxDO,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAa,CAAC;IAC7BA,IAAI,EAAE;EACR,CAAC,CAAC,EACFrB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,wCAAwC;IACrDO,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAc,CAAC;IAC9BA,IAAI,EAAE;EACR,CAAC,CAAC,EACFrB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,wCAAwC;IACrDO,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAc,CAAC;IAC9BA,IAAI,EAAE;EACR,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDtB,GAAG,CAACuB,EAAE,CAAC,CAAC,EACZtB,EAAE,CAAC,mBAAmB,EAAE;IAAEG,GAAG,EAAE;EAAmB,CAAC,CAAC,CACrD,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIoB,eAAe,GAAG,EAAE;AACxBzB,MAAM,CAAC0B,aAAa,GAAG,IAAI;AAE3B,SAAS1B,MAAM,EAAEyB,eAAe", "ignoreList": []}]}