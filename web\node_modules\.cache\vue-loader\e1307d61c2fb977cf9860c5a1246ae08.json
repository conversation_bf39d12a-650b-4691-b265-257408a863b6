{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderDMainList.vue?vue&type=template&id=e04bf6d8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderDMainList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"24\">\n\n        <a-col :md=\"6\" :sm=\"24\">\n          <a-form-item label=\"订单号\">\n            <a-input placeholder=\"请输入订单号\" v-model=\"queryParam.orderCode\"></a-input>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"24\">\n          <a-form-item label=\"订单类型\">\n            <a-select placeholder=\"请输入订单类型\" v-model=\"queryParam.ctype\">\n              <a-select-option value=\"1\">国内订单</a-select-option>\n              <a-select-option value=\"2\">国际订单</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n\n        <a-col :md=\"6\" :sm=\"24\">\n          <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n            <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n          </span>\n        </a-col>\n\n      </a-row>\n    </a-form>\n  </div>\n\n\n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\">\n    <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n\n    <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n      <a-menu slot=\"overlay\">\n        <a-menu-item key=\"1\" @click=\"batchDel\">\n          <a-icon type=\"delete\"/>\n          删除\n        </a-menu-item>\n      </a-menu>\n      <a-button style=\"margin-left: 8px\"> 批量操作\n        <a-icon type=\"down\"/>\n      </a-button>\n    </a-dropdown>\n  </div>\n\n  <!-- table区域-begin -->\n  <div>\n    <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n      <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n      <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n    </div>\n\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      filterMultiple=\"filterMultiple\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type:type}\"\n      @change=\"handleTableChange\"\n      :customRow=\"clickThenCheck\"\n    >\n\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\"/>\n        <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n          <a>删除</a>\n        </a-popconfirm>\n      </span>\n\n    </a-table>\n  </div>\n  <!-- table区域-end -->\n\n  <a-tabs defaultActiveKey=\"1\">\n    <a-tab-pane tab=\"客户信息\" key=\"1\">\n      <Jeecg-Order-Customer-List ref=\"JeecgOrderCustomerList\"></Jeecg-Order-Customer-List>\n    </a-tab-pane>\n    <a-tab-pane tab=\"机票信息\" key=\"2\" forceRender>\n      <Jeecg-Order-Ticket-List ref=\"JeecgOrderTicketList\"></Jeecg-Order-Ticket-List>\n    </a-tab-pane>\n  </a-tabs>\n\n  <!-- 表单区域 -->\n  <jeecgOrderDMain-modal ref=\"modalForm\" @ok=\"modalFormOk\"></jeecgOrderDMain-modal>\n\n</a-card>\n", null]}