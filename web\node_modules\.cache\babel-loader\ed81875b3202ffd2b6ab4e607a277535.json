{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JInput.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JInput.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var JINPUT_QUERY_LIKE = 'like';\nvar JINPUT_QUERY_NE = 'ne';\nvar JINPUT_QUERY_GE = 'ge'; //大于等于\nvar JINPUT_QUERY_LE = 'le'; //小于等于\n\nexport default {\n  name: 'JInput',\n  props: {\n    value: {\n      type: String,\n      required: false\n    },\n    type: {\n      type: String,\n      required: false,\n      default: JINPUT_QUERY_LIKE\n    },\n    placeholder: {\n      type: String,\n      required: false,\n      default: ''\n    }\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler: function handler() {\n        this.initVal();\n      }\n    },\n    // update-begin author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n    type: function type() {\n      this.backValue({\n        target: {\n          value: this.inputVal\n        }\n      });\n    } // update-end author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n  },\n  model: {\n    prop: 'value',\n    event: 'change'\n  },\n  data: function data() {\n    return {\n      inputVal: ''\n    };\n  },\n  methods: {\n    initVal: function initVal() {\n      if (!this.value) {\n        this.inputVal = '';\n      } else {\n        var text = this.value;\n        switch (this.type) {\n          case JINPUT_QUERY_LIKE:\n            text = text.substring(1, text.length - 1);\n            break;\n          case JINPUT_QUERY_NE:\n            text = text.substring(1);\n            break;\n          case JINPUT_QUERY_GE:\n            text = text.substring(2);\n            break;\n          case JINPUT_QUERY_LE:\n            text = text.substring(2);\n            break;\n          default:\n        }\n        this.inputVal = text;\n      }\n    },\n    backValue: function backValue(e) {\n      var text = e.target.value;\n      switch (this.type) {\n        case JINPUT_QUERY_LIKE:\n          text = \"*\" + text + \"*\";\n          break;\n        case JINPUT_QUERY_NE:\n          text = \"!\" + text;\n          break;\n        case JINPUT_QUERY_GE:\n          text = \">=\" + text;\n          break;\n        case JINPUT_QUERY_LE:\n          text = \"<=\" + text;\n          break;\n        default:\n      }\n      this.$emit(\"change\", text);\n    }\n  }\n};", {"version": 3, "names": ["JINPUT_QUERY_LIKE", "JINPUT_QUERY_NE", "JINPUT_QUERY_GE", "JINPUT_QUERY_LE", "name", "props", "value", "type", "String", "required", "default", "placeholder", "watch", "immediate", "handler", "initVal", "backValue", "target", "inputVal", "model", "prop", "event", "data", "methods", "text", "substring", "length", "e", "$emit"], "sources": ["src/components/jeecg/JInput.vue"], "sourcesContent": ["<template>\n  <a-input :placeholder=\"placeholder\" :value=\"inputVal\" @input=\"backValue\"></a-input>\n</template>\n\n<script>\n  const JINPUT_QUERY_LIKE = 'like';\n  const JINPUT_QUERY_NE = 'ne';\n  const JINPUT_QUERY_GE = 'ge'; //大于等于\n  const JINPUT_QUERY_LE = 'le'; //小于等于\n  \n  export default {\n    name: 'JInput',\n    props:{\n      value:{\n        type:String,\n        required:false\n      },\n      type:{\n        type:String,\n        required:false,\n        default:JINPUT_QUERY_LIKE\n      },\n      placeholder:{\n        type:String,\n        required:false,\n        default:''\n      }\n    },\n    watch:{\n      value:{\n        immediate:true,\n        handler:function(){\n          this.initVal();\n        }\n      },\n      // update-begin author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n      type() {\n        this.backValue({ target: { value: this.inputVal } })\n      },\n      // update-end author:sunjianlei date:20200225 for:当 type 变化的时候重新计算值 ------\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    data(){\n      return {\n        inputVal:''\n      }\n    },\n    methods:{\n      initVal(){\n        if(!this.value){\n          this.inputVal = ''\n        }else{\n          let text = this.value\n          switch (this.type) {\n            case JINPUT_QUERY_LIKE:\n              text = text.substring(1,text.length-1);\n              break;\n            case JINPUT_QUERY_NE:\n              text = text.substring(1);\n              break;\n            case JINPUT_QUERY_GE:\n              text = text.substring(2);\n              break;\n            case JINPUT_QUERY_LE:\n              text = text.substring(2);\n              break;\n            default:\n          }\n          this.inputVal = text\n        }\n      },\n      backValue(e){\n        let text = e.target.value\n        switch (this.type) {\n          case JINPUT_QUERY_LIKE:\n            text = \"*\"+text+\"*\";\n            break;\n          case JINPUT_QUERY_NE:\n            text = \"!\"+text;\n            break;\n          case JINPUT_QUERY_GE:\n            text = \">=\"+text;\n            break;\n          case JINPUT_QUERY_LE:\n            text = \"<=\"+text;\n            break;\n          default:\n        }\n        this.$emit(\"change\",text)\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAKA,IAAAA,iBAAA;AACA,IAAAC,eAAA;AACA,IAAAC,eAAA;AACA,IAAAC,eAAA;;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAV;IACA;IACAW,WAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAE,KAAA;IACAN,KAAA;MACAO,SAAA;MACAC,OAAA,WAAAA,QAAA;QACA,KAAAC,OAAA;MACA;IACA;IACA;IACAR,IAAA,WAAAA,KAAA;MACA,KAAAS,SAAA;QAAAC,MAAA;UAAAX,KAAA,OAAAY;QAAA;MAAA;IACA,EACA;EACA;EACAC,KAAA;IACAC,IAAA;IACAC,KAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAJ,QAAA;IACA;EACA;EACAK,OAAA;IACAR,OAAA,WAAAA,QAAA;MACA,UAAAT,KAAA;QACA,KAAAY,QAAA;MACA;QACA,IAAAM,IAAA,QAAAlB,KAAA;QACA,aAAAC,IAAA;UACA,KAAAP,iBAAA;YACAwB,IAAA,GAAAA,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAAE,MAAA;YACA;UACA,KAAAzB,eAAA;YACAuB,IAAA,GAAAA,IAAA,CAAAC,SAAA;YACA;UACA,KAAAvB,eAAA;YACAsB,IAAA,GAAAA,IAAA,CAAAC,SAAA;YACA;UACA,KAAAtB,eAAA;YACAqB,IAAA,GAAAA,IAAA,CAAAC,SAAA;YACA;UACA;QACA;QACA,KAAAP,QAAA,GAAAM,IAAA;MACA;IACA;IACAR,SAAA,WAAAA,UAAAW,CAAA;MACA,IAAAH,IAAA,GAAAG,CAAA,CAAAV,MAAA,CAAAX,KAAA;MACA,aAAAC,IAAA;QACA,KAAAP,iBAAA;UACAwB,IAAA,SAAAA,IAAA;UACA;QACA,KAAAvB,eAAA;UACAuB,IAAA,SAAAA,IAAA;UACA;QACA,KAAAtB,eAAA;UACAsB,IAAA,UAAAA,IAAA;UACA;QACA,KAAArB,eAAA;UACAqB,IAAA,UAAAA,IAAA;UACA;QACA;MACA;MACA,KAAAI,KAAA,WAAAJ,IAAA;IACA;EACA;AACA", "ignoreList": []}]}