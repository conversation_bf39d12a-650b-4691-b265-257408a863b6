{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { putAction } from '@/api/manage';\nimport { mapGetters } from 'vuex';\nexport default {\n  name: \"PasswordSetting\",\n  data: function data() {\n    return {\n      confirmLoading: false,\n      validatorRules: {\n        oldpassword: {\n          rules: [{\n            required: true,\n            message: '请输入旧密码!'\n          }]\n        },\n        password: {\n          rules: [{\n            required: true,\n            message: '请输入新密码!'\n          }, {\n            validator: this.validateToNextPassword\n          }]\n        },\n        confirmpassword: {\n          rules: [{\n            required: true,\n            message: '请确认新密码!'\n          }, {\n            validator: this.compareToFirstPassword\n          }]\n        }\n      },\n      confirmDirty: false,\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 12\n        }\n      },\n      form: this.$form.createForm(this),\n      url: \"sys/user/updatePassword\",\n      username: \"\"\n    };\n  },\n  created: function created() {\n    // 获取当前用户名\n    var userInfo = this.userInfo();\n    if (userInfo && userInfo.username) {\n      this.username = userInfo.username;\n    }\n  },\n  methods: _objectSpread(_objectSpread({}, mapGetters(['userInfo'])), {}, {\n    handleSubmit: function handleSubmit() {\n      var _this = this;\n      var that = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n          var params = Object.assign({\n            username: _this.username\n          }, values);\n          console.log(\"修改密码提交数据\", params);\n          putAction(_this.url, params).then(function (res) {\n            if (res.success) {\n              console.log(res);\n              that.$message.success(res.message);\n              // 清空表单\n              that.form.resetFields();\n            } else {\n              that.$message.warning(res.message);\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n          });\n        }\n      });\n    },\n    handleReset: function handleReset() {\n      // 重置表单\n      this.form.resetFields();\n      this.$message.info('表单已重置');\n    },\n    validateToNextPassword: function validateToNextPassword(rule, value, callback) {\n      var form = this.form;\n      if (value && this.confirmDirty) {\n        form.validateFields(['confirm'], {\n          force: true\n        });\n      }\n      callback();\n    },\n    compareToFirstPassword: function compareToFirstPassword(rule, value, callback) {\n      var form = this.form;\n      if (value && value !== form.getFieldValue('password')) {\n        callback('两次输入的密码不一样！');\n      } else {\n        callback();\n      }\n    },\n    handleConfirmBlur: function handleConfirmBlur(e) {\n      var value = e.target.value;\n      this.confirmDirty = this.confirmDirty || !!value;\n    }\n  })\n};", {"version": 3, "names": ["putAction", "mapGetters", "name", "data", "confirmLoading", "validatorRules", "oldpassword", "rules", "required", "message", "password", "validator", "validateToNextPassword", "confirmpassword", "compareToFirstPassword", "confirmDirty", "labelCol", "xs", "span", "sm", "wrapperCol", "form", "$form", "createForm", "url", "username", "created", "userInfo", "methods", "_objectSpread", "handleSubmit", "_this", "that", "validateFields", "err", "values", "params", "Object", "assign", "console", "log", "then", "res", "success", "$message", "resetFields", "warning", "finally", "handleReset", "info", "rule", "value", "callback", "force", "getFieldValue", "handleConfirmBlur", "e", "target"], "sources": ["src/views/account/settings/PasswordSetting.vue"], "sourcesContent": ["<template>\n  <div class=\"password-setting\">\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\" class=\"password-form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"旧密码\">\n          <a-input\n            type=\"password\"\n            placeholder=\"请输入旧密码\"\n            v-decorator=\"[ 'oldpassword', validatorRules.oldpassword]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"新密码\">\n          <a-input\n            type=\"password\"\n            placeholder=\"请输入新密码\"\n            v-decorator=\"[ 'password', validatorRules.password]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"确认新密码\">\n          <a-input\n            type=\"password\"\n            @blur=\"handleConfirmBlur\"\n            placeholder=\"请确认新密码\"\n            v-decorator=\"[ 'confirmpassword', validatorRules.confirmpassword]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item :wrapperCol=\"{ offset: 5, span: 12 }\">\n          <div class=\"button-group\">\n            <a-button type=\"primary\" @click=\"handleSubmit\" :loading=\"confirmLoading\">\n              提交\n            </a-button>\n            <a-button @click=\"handleReset\" style=\"margin-left: 8px;\">\n              重置\n            </a-button>\n          </div>\n        </a-form-item>\n\n      </a-form>\n    </a-spin>\n  </div>\n</template>\n\n<script>\nimport { putAction } from '@/api/manage'\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: \"PasswordSetting\",\n  data () {\n    return {\n      confirmLoading: false,\n      validatorRules:{\n        oldpassword:{\n          rules: [{\n            required: true, message: '请输入旧密码!',\n          }],\n        },\n        password:{\n          rules: [{\n            required: true, message: '请输入新密码!',\n          }, {\n            validator: this.validateToNextPassword,\n          }],\n        },\n        confirmpassword:{\n          rules: [{\n            required: true, message: '请确认新密码!',\n          }, {\n            validator: this.compareToFirstPassword,\n          }],\n        }\n      },\n      confirmDirty:false,\n      labelCol: {\n        xs: { span: 24 },\n        sm: { span: 5 },\n      },\n      wrapperCol: {\n        xs: { span: 24 },\n        sm: { span: 12 },\n      },\n\n      form:this.$form.createForm(this),\n      url: \"sys/user/updatePassword\",\n      username:\"\",\n    }\n  },\n  created() {\n    // 获取当前用户名\n    let userInfo = this.userInfo()\n    if (userInfo && userInfo.username) {\n      this.username = userInfo.username\n    }\n  },\n  methods: {\n    ...mapGetters(['userInfo']),\n    handleSubmit () {\n      const that = this;\n      // 触发表单验证\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          that.confirmLoading = true;\n          let params = Object.assign({username:this.username},values)\n          console.log(\"修改密码提交数据\",params)\n          putAction(this.url,params).then((res)=>{\n            if(res.success){\n              console.log(res)\n              that.$message.success(res.message);\n              // 清空表单\n              that.form.resetFields();\n            }else{\n              that.$message.warning(res.message);\n            }\n          }).finally(() => {\n            that.confirmLoading = false;\n          })\n        }\n      })\n    },\n    handleReset() {\n      // 重置表单\n      this.form.resetFields();\n      this.$message.info('表单已重置');\n    },\n    validateToNextPassword  (rule, value, callback) {\n      const form = this.form;\n      if (value && this.confirmDirty) {\n        form.validateFields(['confirm'], { force: true })\n      }\n      callback();\n    },\n    compareToFirstPassword  (rule, value, callback) {\n      const form = this.form;\n      if (value && value !== form.getFieldValue('password')) {\n        callback('两次输入的密码不一样！');\n      } else {\n        callback()\n      }\n    },\n    handleConfirmBlur  (e) {\n      const value = e.target.value\n      this.confirmDirty = this.confirmDirty || !!value\n    }\n  }\n}\n</script>\n\n<style scoped>\n.password-setting {\n  max-width: 600px;\n}\n\n.password-form {\n  padding: 20px 0;\n}\n\n.password-input {\n  max-width: 300px;\n}\n\n.button-group {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  max-width: 300px;\n}\n\n.button-group .ant-btn {\n  min-width: 80px;\n}\n</style>\n"], "mappings": ";;;;;;AAwDA,SAAAA,SAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MACAC,cAAA;QACAC,WAAA;UACAC,KAAA;YACAC,QAAA;YAAAC,OAAA;UACA;QACA;QACAC,QAAA;UACAH,KAAA;YACAC,QAAA;YAAAC,OAAA;UACA;YACAE,SAAA,OAAAC;UACA;QACA;QACAC,eAAA;UACAN,KAAA;YACAC,QAAA;YAAAC,OAAA;UACA;YACAE,SAAA,OAAAG;UACA;QACA;MACA;MACAC,YAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MAEAG,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,GAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,QAAA,QAAAA,QAAA;IACA,IAAAA,QAAA,IAAAA,QAAA,CAAAF,QAAA;MACA,KAAAA,QAAA,GAAAE,QAAA,CAAAF,QAAA;IACA;EACA;EACAG,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACA5B,UAAA;IACA6B,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA;MACA;MACA,KAAAX,IAAA,CAAAY,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAF,IAAA,CAAA5B,cAAA;UACA,IAAAgC,MAAA,GAAAC,MAAA,CAAAC,MAAA;YAAAb,QAAA,EAAAM,KAAA,CAAAN;UAAA,GAAAU,MAAA;UACAI,OAAA,CAAAC,GAAA,aAAAJ,MAAA;UACApC,SAAA,CAAA+B,KAAA,CAAAP,GAAA,EAAAY,MAAA,EAAAK,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAJ,OAAA,CAAAC,GAAA,CAAAE,GAAA;cACAV,IAAA,CAAAY,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAjC,OAAA;cACA;cACAuB,IAAA,CAAAX,IAAA,CAAAwB,WAAA;YACA;cACAb,IAAA,CAAAY,QAAA,CAAAE,OAAA,CAAAJ,GAAA,CAAAjC,OAAA;YACA;UACA,GAAAsC,OAAA;YACAf,IAAA,CAAA5B,cAAA;UACA;QACA;MACA;IACA;IACA4C,WAAA,WAAAA,YAAA;MACA;MACA,KAAA3B,IAAA,CAAAwB,WAAA;MACA,KAAAD,QAAA,CAAAK,IAAA;IACA;IACArC,sBAAA,WAAAA,uBAAAsC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAA/B,IAAA,QAAAA,IAAA;MACA,IAAA8B,KAAA,SAAApC,YAAA;QACAM,IAAA,CAAAY,cAAA;UAAAoB,KAAA;QAAA;MACA;MACAD,QAAA;IACA;IACAtC,sBAAA,WAAAA,uBAAAoC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAA/B,IAAA,QAAAA,IAAA;MACA,IAAA8B,KAAA,IAAAA,KAAA,KAAA9B,IAAA,CAAAiC,aAAA;QACAF,QAAA;MACA;QACAA,QAAA;MACA;IACA;IACAG,iBAAA,WAAAA,kBAAAC,CAAA;MACA,IAAAL,KAAA,GAAAK,CAAA,CAAAC,MAAA,CAAAN,KAAA;MACA,KAAApC,YAAA,QAAAA,YAAA,MAAAoC,KAAA;IACA;EAAA;AAEA", "ignoreList": []}]}