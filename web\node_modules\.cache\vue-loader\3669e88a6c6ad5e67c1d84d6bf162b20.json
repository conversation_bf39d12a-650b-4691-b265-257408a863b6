{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue?vue&type=template&id=d2093876&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"account-settings-info-view\">\n    <div :style=\"{background: 'url(' + getFileAccessHttpUrl(courseInfo.courseMap) + ') no-repeat', backgroundSize:'auto', height: '1000px'}\">\n      <div v-for=\"unit in unitList\" :key=\"unit.id\" class=\"unit\" \n        :style=\"{left: unit.mapX-25+'px', top: unit.mapY-25+'px'}\"\n        @click=\"viewUnit(unit)\">\n          <i class=\"flag\"></i>\n          <div class=\"unit-title\">{{unit.unitName}}</div>\n      </div>\n      <unitView-modal ref=\"unitViewModal\"/>\n    </div>\n</div>\n", null]}