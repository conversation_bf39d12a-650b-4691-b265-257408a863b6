{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue?vue&type=template&id=4083ec1a&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue", "mtime": 1750046294782}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"game-center\"\n  }, [_c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"header\", [_c(\"div\", {\n    staticClass: \"header-content\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"img\", {\n    staticClass: \"user-avatar\",\n    attrs: {\n      src: _vm.userAvatar,\n      alt: \"用户头像\"\n    }\n  }), _c(\"span\", {\n    staticClass: \"user-name\"\n  }, [_vm._v(_vm._s(_vm.userName))]), _c(\"div\", {\n    staticClass: \"coin-display\"\n  }, [_c(\"span\", {\n    staticClass: \"coin-icon\"\n  }), _c(\"span\", {\n    staticClass: \"coin-amount\"\n  }, [_vm._v(_vm._s(_vm.userCoins))])])])])]), _c(\"div\", {\n    staticClass: \"game-grid\"\n  }, _vm._l(_vm.games, function (game) {\n    return _c(\"game-card\", {\n      key: game.id,\n      attrs: {\n        game: game\n      },\n      on: {\n        \"play-game\": _vm.openGameModal\n      }\n    });\n  }), 1), _vm._m(1)]), _vm.showModal ? _c(\"game-modal\", {\n    attrs: {\n      game: _vm.currentGame,\n      \"user-coins\": _vm.userCoins,\n      \"user-name\": _vm.userName,\n      \"user-avatar\": _vm.userAvatar\n    },\n    on: {\n      close: _vm.closeGameModal,\n      \"consume-coins\": _vm.consumeCoins\n    }\n  }) : _vm._e(), _vm.showConfirmDialog ? _c(\"confirm-dialog\", {\n    attrs: {\n      message: _vm.confirmMessage,\n      \"game-cost\": _vm.currentGame ? _vm.currentGame.cost : 0\n    },\n    on: {\n      confirm: _vm.handleConfirm,\n      cancel: function cancel($event) {\n        _vm.showConfirmDialog = false;\n      }\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"title-area\"\n  }, [_c(\"h1\", [_vm._v(\"休闲游戏中心\")]), _c(\"p\", {\n    staticClass: \"subtitle\"\n  }, [_vm._v(\"放松心情，享受游戏乐趣\")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"footer\", [_c(\"p\", [_vm._v(\"© 2025 休闲游戏中心 | CFish科技少儿编程\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "attrs", "src", "userAvatar", "alt", "_v", "_s", "userName", "userCoins", "_l", "games", "game", "key", "id", "on", "openGameModal", "showModal", "currentGame", "close", "closeGameModal", "consumeCoins", "_e", "showConfirmDialog", "message", "confirmMessage", "cost", "confirm", "handleConfirm", "cancel", "$event", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/game/GameCenter.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"game-center\" },\n    [\n      _c(\"div\", { staticClass: \"container\" }, [\n        _c(\"header\", [\n          _c(\"div\", { staticClass: \"header-content\" }, [\n            _vm._m(0),\n            _c(\"div\", { staticClass: \"user-info\" }, [\n              _c(\"img\", {\n                staticClass: \"user-avatar\",\n                attrs: { src: _vm.userAvatar, alt: \"用户头像\" },\n              }),\n              _c(\"span\", { staticClass: \"user-name\" }, [\n                _vm._v(_vm._s(_vm.userName)),\n              ]),\n              _c(\"div\", { staticClass: \"coin-display\" }, [\n                _c(\"span\", { staticClass: \"coin-icon\" }),\n                _c(\"span\", { staticClass: \"coin-amount\" }, [\n                  _vm._v(_vm._s(_vm.userCoins)),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"game-grid\" },\n          _vm._l(_vm.games, function (game) {\n            return _c(\"game-card\", {\n              key: game.id,\n              attrs: { game: game },\n              on: { \"play-game\": _vm.openGameModal },\n            })\n          }),\n          1\n        ),\n        _vm._m(1),\n      ]),\n      _vm.showModal\n        ? _c(\"game-modal\", {\n            attrs: {\n              game: _vm.currentGame,\n              \"user-coins\": _vm.userCoins,\n              \"user-name\": _vm.userName,\n              \"user-avatar\": _vm.userAvatar,\n            },\n            on: {\n              close: _vm.closeGameModal,\n              \"consume-coins\": _vm.consumeCoins,\n            },\n          })\n        : _vm._e(),\n      _vm.showConfirmDialog\n        ? _c(\"confirm-dialog\", {\n            attrs: {\n              message: _vm.confirmMessage,\n              \"game-cost\": _vm.currentGame ? _vm.currentGame.cost : 0,\n            },\n            on: {\n              confirm: _vm.handleConfirm,\n              cancel: function ($event) {\n                _vm.showConfirmDialog = false\n              },\n            },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"title-area\" }, [\n      _c(\"h1\", [_vm._v(\"休闲游戏中心\")]),\n      _c(\"p\", { staticClass: \"subtitle\" }, [_vm._v(\"放松心情，享受游戏乐趣\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"footer\", [\n      _c(\"p\", [_vm._v(\"© 2025 休闲游戏中心 | CFish科技少儿编程\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,QAAQ,EAAE,CACXA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEC,GAAG,EAAEN,GAAG,CAACO,UAAU;MAAEC,GAAG,EAAE;IAAO;EAC5C,CAAC,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACvCH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,QAAQ,CAAC,CAAC,CAC7B,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,EAAE,CAACV,GAAG,CAACY,SAAS,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5BH,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,KAAK,EAAE,UAAUC,IAAI,EAAE;IAChC,OAAOd,EAAE,CAAC,WAAW,EAAE;MACrBe,GAAG,EAAED,IAAI,CAACE,EAAE;MACZZ,KAAK,EAAE;QAAEU,IAAI,EAAEA;MAAK,CAAC;MACrBG,EAAE,EAAE;QAAE,WAAW,EAAElB,GAAG,CAACmB;MAAc;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACDnB,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CACV,CAAC,EACFJ,GAAG,CAACoB,SAAS,GACTnB,EAAE,CAAC,YAAY,EAAE;IACfI,KAAK,EAAE;MACLU,IAAI,EAAEf,GAAG,CAACqB,WAAW;MACrB,YAAY,EAAErB,GAAG,CAACY,SAAS;MAC3B,WAAW,EAAEZ,GAAG,CAACW,QAAQ;MACzB,aAAa,EAAEX,GAAG,CAACO;IACrB,CAAC;IACDW,EAAE,EAAE;MACFI,KAAK,EAAEtB,GAAG,CAACuB,cAAc;MACzB,eAAe,EAAEvB,GAAG,CAACwB;IACvB;EACF,CAAC,CAAC,GACFxB,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZzB,GAAG,CAAC0B,iBAAiB,GACjBzB,EAAE,CAAC,gBAAgB,EAAE;IACnBI,KAAK,EAAE;MACLsB,OAAO,EAAE3B,GAAG,CAAC4B,cAAc;MAC3B,WAAW,EAAE5B,GAAG,CAACqB,WAAW,GAAGrB,GAAG,CAACqB,WAAW,CAACQ,IAAI,GAAG;IACxD,CAAC;IACDX,EAAE,EAAE;MACFY,OAAO,EAAE9B,GAAG,CAAC+B,aAAa;MAC1BC,MAAM,EAAE,SAAAA,OAAUC,MAAM,EAAE;QACxBjC,GAAG,CAAC0B,iBAAiB,GAAG,KAAK;MAC/B;IACF;EACF,CAAC,CAAC,GACF1B,GAAG,CAACyB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIS,eAAe,GAAG,CACpB,YAAY;EACV,IAAIlC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BR,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CAACH,GAAG,CAACS,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAC9D,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIT,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE,CAClBA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,CACjD,CAAC;AACJ,CAAC,CACF;AACDV,MAAM,CAACoC,aAAa,GAAG,IAAI;AAE3B,SAASpC,MAAM,EAAEmC,eAAe", "ignoreList": []}]}