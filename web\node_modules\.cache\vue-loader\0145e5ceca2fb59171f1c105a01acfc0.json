{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Binding.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Binding.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n  export default {\n    data () {\n      return {\n        data: []\n      }\n    },\n    methods: {\n\n    }\n  }\n", {"version": 3, "sources": ["Binding.vue"], "names": [], "mappings": ";AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "Binding.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <a-list\n    itemLayout=\"horizontal\"\n    :dataSource=\"data\"\n  >\n\n  </a-list>\n</template>\n\n<script>\n  export default {\n    data () {\n      return {\n        data: []\n      }\n    },\n    methods: {\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}