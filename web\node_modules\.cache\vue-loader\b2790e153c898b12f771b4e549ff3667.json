{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\RoleList.vue?vue&type=template&id=7ce28e7b", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\RoleList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"48\">\n        <a-col :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"角色ID\">\n            <a-input placeholder=\"请输入\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"状态\">\n            <a-select placeholder=\"请选择\" default-value=\"0\">\n              <a-select-option value=\"0\">全部</a-select-option>\n              <a-select-option value=\"1\">正常</a-select-option>\n              <a-select-option value=\"2\">禁用</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"8\" :sm=\"24\">\n          <span class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\">查询</a-button>\n            <a-button style=\"margin-left: 8px\">重置</a-button>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n\n  <s-table\n    ref=\"table\"\n    size=\"default\"\n    :columns=\"columns\"\n    :data=\"loadData\"\n  >\n    <div\n      slot=\"expandedRowRender\"\n      slot-scope=\"record\"\n      style=\"margin: 0\">\n      <a-row\n        :gutter=\"24\"\n        :style=\"{ marginBottom: '12px' }\">\n        <a-col :span=\"12\" v-for=\"(role, index) in record.permissions\" :key=\"index\" :style=\"{ marginBottom: '12px' }\">\n          <a-col :span=\"4\">\n            <span>{{ role.permissionName }}：</span>\n          </a-col>\n          <a-col :span=\"20\" v-if=\"role.actionEntitySet.length > 0\">\n            <a-tag color=\"cyan\" v-for=\"(action, k) in role.actionEntitySet\" :key=\"k\">{{ action.describe }}</a-tag>\n          </a-col>\n          <a-col :span=\"20\" v-else>-</a-col>\n        </a-col>\n      </a-row>\n    </div>\n    <span slot=\"action\" slot-scope=\"text, record\">\n      <a @click=\"$refs.modal.edit(record)\">编辑</a>\n      <a-divider type=\"vertical\" />\n      <a-dropdown>\n        <a class=\"ant-dropdown-link\">\n          更多 <a-icon type=\"down\" />\n        </a>\n        <a-menu slot=\"overlay\">\n          <a-menu-item>\n            <a href=\"javascript:;\">详情</a>\n          </a-menu-item>\n          <a-menu-item>\n            <a href=\"javascript:;\">禁用</a>\n          </a-menu-item>\n          <a-menu-item>\n            <a href=\"javascript:;\">删除</a>\n          </a-menu-item>\n        </a-menu>\n      </a-dropdown>\n    </span>\n  </s-table>\n\n  <role-modal ref=\"modal\" @ok=\"handleOk\"></role-modal>\n\n</a-card>\n", null]}