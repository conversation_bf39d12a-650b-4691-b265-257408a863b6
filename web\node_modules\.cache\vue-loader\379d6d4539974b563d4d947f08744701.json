{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\testManage.vue?vue&type=template&id=6cd6c724&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\testManage.vue", "mtime": 1753195481732}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"24\">\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"试卷标题\">\n            <a-input placeholder=\"请输入试卷标题\" v-model=\"queryParam.title\"></a-input>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"所属科目\">\n            <a-select placeholder=\"请选择所属科目\" v-model=\"queryParam.subject\" allowClear>\n              <a-select-option value=\"Scratch\">Scratch</a-select-option>\n              <a-select-option value=\"Python\">Python</a-select-option>\n              <a-select-option value=\"C++\">C++</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"所属级别\">\n            <a-select placeholder=\"请选择所属级别\" v-model=\"queryParam.level\" allowClear>\n              <a-select-option v-for=\"(level, index) in getLevelOptions()\" :key=\"index\" :value=\"level\">\n                {{ level }}\n              </a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"难度\">\n            <a-select placeholder=\"请选择难度\" v-model=\"queryParam.difficulty\" allowClear>\n              <a-select-option :value=\"1\">简单</a-select-option>\n              <a-select-option :value=\"2\">中等</a-select-option>\n              <a-select-option :value=\"3\">困难</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"类型\">\n            <a-select placeholder=\"请选择类型\" v-model=\"queryParam.type\" allowClear>\n              <a-select-option value=\"真题\">真题</a-select-option>\n              <a-select-option value=\"模拟\">模拟</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"年份\">\n            <a-date-picker\n              placeholder=\"请选择年份\"\n              v-model=\"queryParam.year\"\n              :format=\"yearFormat\"\n              :mode=\"yearMode\"\n              @panelChange=\"handleYearPanelChange\"\n              @change=\"handleYearChange\"\n            />\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <span class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n\n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\">\n    <a-button type=\"primary\" icon=\"plus\" @click=\"handleAdd\">新增试卷</a-button>\n    <a-button type=\"primary\" icon=\"cloud-upload\" @click=\"handleImport\">导入试卷</a-button>\n    <a-button type=\"primary\" icon=\"cloud-download\" @click=\"handleExport\">批量导出</a-button>\n    <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n      <a-menu slot=\"overlay\">\n        <a-menu-item key=\"1\" @click=\"batchDel\">\n          <a-icon type=\"delete\" />删除\n        </a-menu-item>\n      </a-menu>\n      <a-button style=\"margin-left: 8px\">\n        批量操作 <a-icon type=\"down\" />\n      </a-button>\n    </a-dropdown>\n  </div>\n\n  <!-- 表格区域 -->\n  <a-table\n    ref=\"table\"\n    size=\"middle\"\n    bordered\n    rowKey=\"id\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :pagination=\"ipagination\"\n    :loading=\"loading\"\n    :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n    @change=\"handleTableChange\">\n    \n    <!-- 自定义难度展示 -->\n    <template slot=\"difficultySlot\" slot-scope=\"text\">\n      <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n      <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n      <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n      <a-tag v-else>未知</a-tag>\n    </template>\n    \n    <!-- 自定义考试时长展示 -->\n    <template slot=\"durationSlot\" slot-scope=\"text\">\n      {{ text }} 分钟\n    </template>\n    \n    <!-- 操作列 -->\n    <span slot=\"action\" slot-scope=\"text, record\">\n      <a @click=\"handleEdit(record)\">编辑</a>\n      <a-divider type=\"vertical\" />\n      <a @click=\"handlePreview(record)\">预览</a>\n      <a-divider type=\"vertical\" />\n      <a-dropdown>\n        <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n        <a-menu slot=\"overlay\">\n          <a-menu-item>\n            <a @click=\"handleCopy(record)\">复制</a>\n          </a-menu-item>\n          <a-menu-item>\n            <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n              <a>删除</a>\n            </a-popconfirm>\n          </a-menu-item>\n        </a-menu>\n      </a-dropdown>\n    </span>\n  </a-table>\n\n  <!-- 表单模态框 -->\n  <paper-modal ref=\"modalForm\" @ok=\"modalFormOk\"></paper-modal>\n  \n  <!-- 导入模态框 -->\n  <a-modal\n    title=\"导入试卷\"\n    :width=\"800\"\n    :visible=\"importModalVisible\"\n    :maskClosable=\"false\"\n    :confirmLoading=\"importConfirmLoading\"\n    @ok=\"handleImportOk\"\n    @cancel=\"handleImportCancel\">\n    \n    <a-alert\n      type=\"info\"\n      show-icon\n      style=\"margin-bottom: 16px\"\n    >\n      <div slot=\"message\">\n        <span>导入试卷说明</span>\n        <a-tooltip placement=\"right\" overlayClassName=\"import-help-tooltip\">\n          <template slot=\"title\">\n            <div style=\"max-width: 400px;\">\n              <div><strong>标准导入流程详解：</strong></div>\n              <div style=\"margin-top: 8px;\">\n                <div><strong>步骤1：获取纯文本模板</strong></div>\n                <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                  点击\"下载模板\"获取【纯文本模板】文件\n                </div>\n              </div>\n              <div style=\"margin-top: 8px;\">\n                <div><strong>步骤2：填写试卷数据</strong></div>\n                <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                  在【纯文本模板】中按格式填写您的试卷内容\n                </div>\n              </div>\n              <div style=\"margin-top: 8px;\">\n                <div><strong>步骤3：自动格式化</strong></div>\n                <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                  点击\"自动格式化\"上传填好的【纯文本模板】<br/>\n                  填写试卷标题、科目、级别、难度等元数据<br/>\n                  下载生成的【格式化试卷文件】\n                </div>\n              </div>\n              <div style=\"margin-top: 8px;\">\n                <div><strong>步骤4：导入</strong></div>\n                <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                  使用【格式化试卷文件】进行导入\n                </div>\n              </div>\n            </div>\n          </template>\n          <a-icon\n            type=\"question-circle\"\n            style=\"margin-left: 8px; color: #1890ff; cursor: help;\"\n          />\n        </a-tooltip>\n      </div>\n      <div slot=\"description\">\n        <div style=\"padding: 8px;\">\n          <strong>💡 完整流程</strong>：【纯文本模板】→ 填写数据 → 【自动格式化】→ 【格式化试卷文件】→ 导入\n        </div>\n      </div>\n    </a-alert>\n    \n    <!-- 减少分隔线上下间距 -->\n    <a-divider style=\"margin: 4px 0\" />\n    \n    <!-- 文件上传区域，与problemManage.vue保持一致 -->\n    <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n      <input\n        ref=\"fileInput\"\n        type=\"file\"\n        accept=\".txt\"\n        @change=\"onFileSelected\"\n        style=\"display: none\"\n      />\n      <div \n        class=\"upload-drop-area\" \n        @click=\"triggerFileInput\"\n        @dragover.prevent\n        @dragenter.prevent=\"isDragover = true\"\n        @dragleave.prevent=\"isDragover = false\"\n        @drop.prevent=\"onFileDrop\"\n        :class=\"{'is-dragover': isDragover}\"\n        style=\"padding: 16px 24px;\"\n      >\n        <a-icon type=\"cloud-upload\" class=\"upload-icon\" />\n        <div class=\"upload-text\" style=\"margin: 8px 0;\">\n          <span v-if=\"!selectedFile\">点击或拖拽文件到此区域上传</span>\n          <span v-else class=\"selected-file\">\n            <a-icon type=\"file-text\" /> {{ selectedFile.name }}\n            <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeSelectedFile\" />\n          </span>\n        </div>\n        <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerFileInput\" style=\"margin-top: 8px;\">\n          选择文件\n        </a-button>\n      </div>\n      <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n        <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，遵循标准格式化试卷文件格式\n        <a class=\"template-link\" @click=\"downloadTemplate\">下载模板</a>\n        <a-divider type=\"vertical\" />\n        <a class=\"template-link\" @click=\"showAutoTemplateModal\">自动格式化</a>\n      </div>\n    </div>\n  </a-modal>\n  \n  <!-- 预览模态框 -->\n  <a-modal\n    title=\"试卷预览\"\n    :visible=\"previewModalVisible\"\n    :width=\"800\"\n    :footer=\"null\"\n    @cancel=\"handlePreviewCancel\"\n  >\n    <a-spin :spinning=\"previewLoading\">\n      <div v-if=\"previewData\">\n        <h2>{{ previewData.title }}</h2>\n        <div class=\"paper-info\">\n          <p><strong>科目：</strong>{{ previewData.subject }}</p>\n          <p><strong>级别：</strong>{{ previewData.level }}</p>\n          <p><strong>难度：</strong>\n            <a-tag color=\"green\" v-if=\"previewData.difficulty === 1\">简单</a-tag>\n            <a-tag color=\"orange\" v-else-if=\"previewData.difficulty === 2\">中等</a-tag>\n            <a-tag color=\"red\" v-else-if=\"previewData.difficulty === 3\">困难</a-tag>\n          </p>\n          <p><strong>类型：</strong>{{ previewData.type }}</p>\n          <p><strong>年份：</strong>{{ previewData.year }}</p>\n          <p><strong>考试时长：</strong>{{ previewData.examDuration }} 分钟</p>\n          <p><strong>作者：</strong>{{ previewData.author }}</p>\n        </div>\n        \n        <!-- 试卷内容预览 -->\n        <div class=\"paper-content\" v-if=\"previewData.content\">\n          <!-- 单选题部分 -->\n          <div v-if=\"previewContent.singleChoiceQuestions && previewContent.singleChoiceQuestions.length > 0\">\n            <h3>一、单选题（每题 {{ previewContent.singleChoiceScore }} 分，共 {{ previewContent.singleChoiceQuestions.length * previewContent.singleChoiceScore }} 分）</h3>\n            <div v-for=\"(question, index) in previewContent.singleChoiceQuestions\" :key=\"'sc-'+question.id\" class=\"question-item\">\n              <p>{{ index + 1 }}. {{ question.title }}</p>\n              <p v-if=\"question.options && question.options.length >= 4\">\n                <div>A. {{ question.options[0] }}</div>\n                <div>B. {{ question.options[1] }}</div>\n                <div>C. {{ question.options[2] }}</div>\n                <div>D. {{ question.options[3] }}</div>\n              </p>\n            </div>\n          </div>\n          \n          <!-- 判断题部分 -->\n          <div v-if=\"previewContent.judgmentQuestions && previewContent.judgmentQuestions.length > 0\">\n            <h3>二、判断题（每题 {{ previewContent.judgmentScore }} 分，共 {{ previewContent.judgmentQuestions.length * previewContent.judgmentScore }} 分）</h3>\n            <div v-for=\"(question, index) in previewContent.judgmentQuestions\" :key=\"'jd-'+question.id\" class=\"question-item\">\n              <p>{{ index + 1 }}. {{ question.title }}</p>\n            </div>\n          </div>\n          \n          <!-- 编程题部分 -->\n          <div v-if=\"previewContent.programmingQuestions && previewContent.programmingQuestions.length > 0\">\n            <h3>三、编程题（每题 {{ previewContent.programmingScore }} 分，共 {{ previewContent.programmingQuestions.length * previewContent.programmingScore }} 分）</h3>\n            <div v-for=\"(question, index) in previewContent.programmingQuestions\" :key=\"'pg-'+question.id\" class=\"question-item\">\n              <p>{{ index + 1 }}. {{ question.title }}</p>\n              <div class=\"question-limits\">\n                <span>时间限制：{{ question.timeLimit }} ms</span>\n                <span>内存限制：{{ question.memoryLimit }} MB</span>\n              </div>\n              <div v-if=\"question.description\" class=\"markdown-body\">\n                <h4>题目描述</h4>\n                <div>{{ question.description }}</div>\n              </div>\n              <div v-if=\"question.inputFormat\" class=\"markdown-body\">\n                <h4>输入格式</h4>\n                <div>{{ question.inputFormat }}</div>\n              </div>\n              <div v-if=\"question.outputFormat\" class=\"markdown-body\">\n                <h4>输出格式</h4>\n                <div>{{ question.outputFormat }}</div>\n              </div>\n              <!-- 样例展示 -->\n              <div v-if=\"question.samples && question.samples.length > 0\">\n                <div v-for=\"(sample, sIndex) in question.samples\" :key=\"'sample-'+sIndex\">\n                  <h4>样例 {{ sIndex + 1 }}</h4>\n                  <div class=\"sample-container\">\n                    <div class=\"sample-input\">\n                      <div class=\"sample-header\">输入</div>\n                      <pre>{{ sample.input }}</pre>\n                    </div>\n                    <div class=\"sample-output\">\n                      <div class=\"sample-header\">输出</div>\n                      <pre>{{ sample.output }}</pre>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <!-- 提示信息 -->\n              <div v-if=\"question.hint\" class=\"markdown-body\">\n                <h4>提示</h4>\n                <div>{{ question.hint }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </a-spin>\n  </a-modal>\n\n  <!-- 自动格式化模态框 -->\n  <a-modal\n    title=\"自动格式化\"\n    :width=\"600\"\n    :visible=\"autoTemplateModalVisible\"\n    :maskClosable=\"false\"\n    :confirmLoading=\"autoTemplateConfirmLoading\"\n    @ok=\"handleAutoTemplateOk\"\n    @cancel=\"handleAutoTemplateCancel\"\n  >\n    <a-alert\n      type=\"info\"\n      show-icon\n      style=\"margin-bottom: 16px\"\n      message=\"自动格式化说明\"\n    >\n      <div slot=\"description\">\n        <div><strong>功能说明</strong>：上传填写好试卷数据的【纯文本模板】文件，系统将自动格式化为标准导入格式。</div>\n        <div>支持自动识别试卷元数据，并添加相应的格式标记。</div>\n        <div>格式化后生成【格式化试卷文件】，可直接用于导入。</div>\n      </div>\n    </a-alert>\n\n    <!-- 元数据输入区域 -->\n    <a-form :label-col=\"{ span: 6 }\" :wrapper-col=\"{ span: 18 }\" style=\"margin-bottom: 16px;\">\n      <!-- 试卷标题单独一行 -->\n      <a-form-item label=\"试卷标题\">\n        <a-input v-model=\"autoTemplateParam.title\" placeholder=\"请输入试卷标题，例如：C++一级2025年3月真题\" />\n      </a-form-item>\n\n      <!-- 所属级别单独一行 -->\n      <a-form-item label=\"所属级别\">\n        <a-input v-model=\"autoTemplateParam.level\" placeholder=\"请输入级别，例如：1、2、3\" />\n      </a-form-item>\n\n      <!-- 年份单独一行 -->\n      <a-form-item label=\"年份\">\n        <a-input v-model=\"autoTemplateParam.year\" placeholder=\"请输入年份，例如：2025\" />\n      </a-form-item>\n\n      <!-- 所属科目单独一行 -->\n      <a-form-item label=\"所属科目\">\n        <a-select\n          v-model=\"autoTemplateParam.subject\"\n          placeholder=\"请选择科目\"\n          allowClear\n        >\n          <a-select-option value=\"Scratch\">Scratch</a-select-option>\n          <a-select-option value=\"Python\">Python</a-select-option>\n          <a-select-option value=\"C++\">C++</a-select-option>\n        </a-select>\n      </a-form-item>\n\n      <!-- 难度单独一行 -->\n      <a-form-item label=\"难度\">\n        <a-select\n          v-model=\"autoTemplateParam.difficulty\"\n          placeholder=\"请选择难度\"\n          allowClear\n        >\n          <a-select-option :value=\"1\">简单</a-select-option>\n          <a-select-option :value=\"2\">中等</a-select-option>\n          <a-select-option :value=\"3\">困难</a-select-option>\n        </a-select>\n      </a-form-item>\n\n      <!-- 类型单独一行（因为只有两个选项，给它更多空间） -->\n      <a-form-item label=\"类型\">\n        <a-select\n          v-model=\"autoTemplateParam.type\"\n          placeholder=\"请选择类型\"\n          allowClear\n        >\n          <a-select-option value=\"真题\">真题</a-select-option>\n          <a-select-option value=\"模拟\">模拟</a-select-option>\n        </a-select>\n      </a-form-item>\n    </a-form>\n\n    <a-divider style=\"margin: 16px 0\" />\n\n    <!-- 文件上传区域 -->\n    <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n      <input\n        ref=\"autoTemplateFileInput\"\n        type=\"file\"\n        accept=\".txt\"\n        @change=\"onAutoTemplateFileChange\"\n        style=\"display: none\"\n      />\n      <div\n        class=\"upload-drop-area\"\n        @click=\"triggerAutoTemplateFileInput\"\n        @dragover.prevent\n        @dragenter.prevent=\"handleAutoTemplateDragEnter\"\n        @dragleave.prevent=\"handleAutoTemplateDragLeave\"\n        @drop.prevent=\"handleAutoTemplateDrop\"\n        :class=\"{'is-dragover': isAutoTemplateDragover}\"\n        style=\"padding: 16px 24px;\"\n      >\n        <a-icon type=\"file-text\" class=\"upload-icon\" />\n        <div class=\"upload-text\" style=\"margin: 8px 0;\">\n          <span v-if=\"!autoTemplateSelectedFile\">点击或拖拽纯文本文件到此区域上传</span>\n          <span v-else class=\"selected-file\">\n            <a-icon type=\"file-text\" /> {{ autoTemplateSelectedFile.name }}\n            <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeAutoTemplateSelectedFile\" />\n          </span>\n        </div>\n        <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerAutoTemplateFileInput\" style=\"margin-top: 8px;\">\n          选择文件\n        </a-button>\n      </div>\n      <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n        <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，系统将自动格式化为导入模板格式\n      </div>\n    </div>\n  </a-modal>\n</a-card>\n", null]}