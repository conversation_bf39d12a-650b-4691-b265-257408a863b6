{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\PermissionList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\PermissionList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import STable from '@/components/table/';\nexport default {\n  name: \"TableList\",\n  components: {\n    STable: STable\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      description: '列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。',\n      visible: false,\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      form: null,\n      mdl: {},\n      // 高级搜索 展开/关闭\n      advanced: false,\n      // 查询参数\n      queryParam: {},\n      // 表头\n      columns: [{\n        title: '唯一识别码',\n        dataIndex: 'id'\n      }, {\n        title: '权限名称',\n        dataIndex: 'name'\n      }, {\n        title: '可操作权限',\n        dataIndex: 'actions',\n        scopedSlots: {\n          customRender: 'actions'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '操作',\n        width: '150px',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 向后端拉取可以用的操作列表\n      permissionList: null,\n      // 加载数据方法 必须为 Promise 对象\n      loadData: function loadData(parameter) {\n        return _this.$http.get('/api/permission', {\n          params: Object.assign(parameter, _this.queryParam)\n        }).then(function (res) {\n          var result = res.result;\n          result.data.map(function (permission) {\n            permission.actionList = JSON.parse(permission.actionData);\n            return permission;\n          });\n          return result;\n        });\n      },\n      selectedRowKeys: [],\n      selectedRows: []\n    };\n  },\n  filters: {\n    statusFilter: function statusFilter(status) {\n      var statusMap = {\n        1: '正常',\n        2: '禁用'\n      };\n      return statusMap[status];\n    }\n  },\n  created: function created() {\n    this.loadPermissionList();\n  },\n  methods: {\n    loadPermissionList: function loadPermissionList() {\n      var _this2 = this;\n      // permissionList\n      new Promise(function (resolve) {\n        var data = [{\n          label: '新增',\n          value: 'add',\n          defaultChecked: false\n        }, {\n          label: '查询',\n          value: 'get',\n          defaultChecked: false\n        }, {\n          label: '修改',\n          value: 'update',\n          defaultChecked: false\n        }, {\n          label: '列表',\n          value: 'query',\n          defaultChecked: false\n        }, {\n          label: '删除',\n          value: 'delete',\n          defaultChecked: false\n        }, {\n          label: '导入',\n          value: 'import',\n          defaultChecked: false\n        }, {\n          label: '导出',\n          value: 'export',\n          defaultChecked: false\n        }];\n        setTimeout(resolve(data), 1500);\n      }).then(function (res) {\n        _this2.permissionList = res;\n      });\n    },\n    handleEdit: function handleEdit(record) {\n      this.mdl = Object.assign({}, record);\n      console.log(this.mdl);\n      this.visible = true;\n    },\n    handleOk: function handleOk() {},\n    onChange: function onChange(selectedRowKeys, selectedRows) {\n      this.selectedRowKeys = selectedRowKeys;\n      this.selectedRows = selectedRows;\n    },\n    toggleAdvanced: function toggleAdvanced() {\n      this.advanced = !this.advanced;\n    }\n  },\n  watch: {\n    /*\n    'selectedRows': function (selectedRows) {\n      this.needTotalList = this.needTotalList.map(item => {\n        return {\n          ...item,\n          total: selectedRows.reduce( (sum, val) => {\n            return sum + val[item.dataIndex]\n          }, 0)\n        }\n      })\n    }\n    */\n  }\n};", {"version": 3, "names": ["STable", "name", "components", "data", "_this", "description", "visible", "labelCol", "xs", "span", "sm", "wrapperCol", "form", "mdl", "advanced", "queryParam", "columns", "title", "dataIndex", "scopedSlots", "customRender", "width", "permissionList", "loadData", "parameter", "$http", "get", "params", "Object", "assign", "then", "res", "result", "map", "permission", "actionList", "JSON", "parse", "actionData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "filters", "statusFilter", "status", "statusMap", "created", "loadPermissionList", "methods", "_this2", "Promise", "resolve", "label", "value", "defaultChecked", "setTimeout", "handleEdit", "record", "console", "log", "handleOk", "onChange", "toggleAdvanced", "watch"], "sources": ["src/views/list/PermissionList.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"48\">\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"角色ID\">\n              <a-input placeholder=\"请输入\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"状态\">\n              <a-select placeholder=\"请选择\" default-value=\"0\">\n                <a-select-option value=\"0\">全部</a-select-option>\n                <a-select-option value=\"1\">关闭</a-select-option>\n                <a-select-option value=\"2\">运行中</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <span class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\">查询</a-button>\n              <a-button style=\"margin-left: 8px\">重置</a-button>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <s-table :columns=\"columns\" :data=\"loadData\">\n\n      <span slot=\"actions\" slot-scope=\"text, record\">\n        <a-tag v-for=\"(action, index) in record.actionList\" :key=\"index\">{{ action.describe }}</a-tag>\n      </span>\n\n      <span slot=\"status\" slot-scope=\"text\">\n        {{ text | statusFilter }}\n      </span>\n\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">\n            更多 <a-icon type=\"down\" />\n          </a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a href=\"javascript:;\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">禁用</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">删除</a>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </s-table>\n\n    <a-modal\n      title=\"操作\"\n      :width=\"800\"\n      v-model=\"visible\"\n      @ok=\"handleOk\"\n    >\n      <a-form :autoFormCreate=\"(form)=>{this.form = form}\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"唯一识别码\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input placeholder=\"唯一识别码\" v-model=\"mdl.id\" id=\"no\" disabled=\"disabled\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"权限名称\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input placeholder=\"起一个名字\" v-model=\"mdl.name\" id=\"permission_name\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"状态\"\n          hasFeedback\n          validateStatus=\"warning\"\n        >\n          <a-select v-model=\"mdl.status\">\n            <a-select-option value=\"1\">正常</a-select-option>\n            <a-select-option value=\"2\">禁用</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"描述\"\n          hasFeedback\n        >\n          <a-textarea :rows=\"5\" v-model=\"mdl.describe\" placeholder=\"...\" id=\"describe\"/>\n        </a-form-item>\n\n        <a-divider />\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"赋予权限\"\n          hasFeedback\n        >\n          <a-select\n            style=\"width: 100%\"\n            mode=\"multiple\"\n            v-model=\"mdl.actions\"\n            :allowClear=\"true\"\n          >\n            <a-select-option v-for=\"(action, index) in permissionList\" :key=\"index\" :value=\"action.value\">{{ action.label }}</a-select-option>\n          </a-select>\n        </a-form-item>\n\n      </a-form>\n    </a-modal>\n\n  </a-card>\n</template>\n\n<script>\n  import STable from '@/components/table/'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      STable\n    },\n    data () {\n      return {\n        description: '列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。',\n\n        visible: false,\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: false,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '唯一识别码',\n            dataIndex: 'id'\n          },\n          {\n            title: '权限名称',\n            dataIndex: 'name',\n          },\n          {\n            title: '可操作权限',\n            dataIndex: 'actions',\n            scopedSlots: { customRender: 'actions' },\n          },\n          {\n            title: '状态',\n            dataIndex: 'status',\n            scopedSlots: { customRender: 'status' },\n          },\n          {\n            title: '操作',\n            width: '150px',\n            dataIndex: 'action',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 向后端拉取可以用的操作列表\n        permissionList: null,\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return this.$http.get('/api/permission', {\n            params: Object.assign(parameter, this.queryParam)\n          }).then(res => {\n            let result = res.result\n            result.data.map(permission => {\n                permission.actionList = JSON.parse(permission.actionData)\n                return permission\n              })\n            return result\n          })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    filters: {\n      statusFilter(status) {\n        const statusMap = {\n          1: '正常',\n          2: '禁用'\n        }\n        return statusMap[status]\n      }\n    },\n    created () {\n      this.loadPermissionList()\n    },\n    methods: {\n      loadPermissionList () {\n        // permissionList\n        new Promise((resolve => {\n          const data = [\n            { label: '新增', value: 'add', defaultChecked: false },\n            { label: '查询', value: 'get', defaultChecked: false },\n            { label: '修改', value: 'update', defaultChecked: false },\n            { label: '列表', value: 'query', defaultChecked: false },\n            { label: '删除', value: 'delete', defaultChecked: false },\n            { label: '导入', value: 'import', defaultChecked: false },\n            { label: '导出', value: 'export', defaultChecked: false }\n          ]\n          setTimeout(resolve(data), 1500)\n        })).then(res => {\n          this.permissionList = res\n        })\n      },\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n\n      },\n      onChange (selectedRowKeys, selectedRows) {\n        this.selectedRowKeys = selectedRowKeys\n        this.selectedRows = selectedRows\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n</script>"], "mappings": "AAwIA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,MAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,WAAA;MAEAC,OAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAG,IAAA;MACAC,GAAA;MAEA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAH,KAAA;QACAI,KAAA;QACAH,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MACA;MACAE,cAAA;MACA;MACAC,QAAA,WAAAA,SAAAC,SAAA;QACA,OAAApB,KAAA,CAAAqB,KAAA,CAAAC,GAAA;UACAC,MAAA,EAAAC,MAAA,CAAAC,MAAA,CAAAL,SAAA,EAAApB,KAAA,CAAAW,UAAA;QACA,GAAAe,IAAA,WAAAC,GAAA;UACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;UACAA,MAAA,CAAA7B,IAAA,CAAA8B,GAAA,WAAAC,UAAA;YACAA,UAAA,CAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,UAAA,CAAAI,UAAA;YACA,OAAAJ,UAAA;UACA;UACA,OAAAF,MAAA;QACA;MACA;MAEAO,eAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,MAAA;MACA;MACA,IAAAC,OAAA,WAAAC,OAAA;QACA,IAAA/C,IAAA,IACA;UAAAgD,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;UAAAC,cAAA;QAAA,EACA;QACAC,UAAA,CAAAJ,OAAA,CAAA/C,IAAA;MACA,GAAA2B,IAAA,WAAAC,GAAA;QACAiB,MAAA,CAAA1B,cAAA,GAAAS,GAAA;MACA;IACA;IACAwB,UAAA,WAAAA,WAAAC,MAAA;MACA,KAAA3C,GAAA,GAAAe,MAAA,CAAAC,MAAA,KAAA2B,MAAA;MACAC,OAAA,CAAAC,GAAA,MAAA7C,GAAA;MACA,KAAAP,OAAA;IACA;IACAqD,QAAA,WAAAA,SAAA,GAEA;IACAC,QAAA,WAAAA,SAAArB,eAAA,EAAAC,YAAA;MACA,KAAAD,eAAA,GAAAA,eAAA;MACA,KAAAC,YAAA,GAAAA,YAAA;IACA;IACAqB,cAAA,WAAAA,eAAA;MACA,KAAA/C,QAAA,SAAAA,QAAA;IACA;EACA;EACAgD,KAAA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXA;AAaA", "ignoreList": []}]}