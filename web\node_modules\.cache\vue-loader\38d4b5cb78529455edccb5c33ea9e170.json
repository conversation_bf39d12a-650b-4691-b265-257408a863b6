{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\MineWorks.vue?vue&type=template&id=9973fa86&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\MineWorks.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"app-list\">\n  <!-- 添加筛选条件区域 -->\n  <div class=\"filter-container\">\n    <div class=\"filter-item\">\n      <span class=\"filter-label\">作品类型：</span>\n      <a-select\n        v-model=\"filterType\"\n        style=\"width: 120px\"\n        placeholder=\"选择作品类型\"\n        @change=\"handleFilterChange\"\n      >\n        <a-select-option value=\"\">全部</a-select-option>\n        <a-select-option value=\"2\">Scratch</a-select-option>\n        <a-select-option value=\"4\">Python</a-select-option>\n        <a-select-option value=\"5\">C++</a-select-option>\n      </a-select>\n    </div>\n    <div class=\"filter-item\">\n      <span class=\"filter-label\">作品状态：</span>\n      <a-select\n        v-model=\"filterStatus\"\n        style=\"width: 120px\"\n        placeholder=\"选择作品状态\"\n        @change=\"handleFilterChange\"\n      >\n        <a-select-option value=\"\">全部</a-select-option>\n        <a-select-option value=\"4\">精选作品</a-select-option>\n        <a-select-option value=\"3\">首页展示</a-select-option>\n        <a-select-option value=\"2\">已批改</a-select-option>\n        <a-select-option value=\"other\">其他</a-select-option>\n      </a-select>\n    </div>\n    <div class=\"filter-item\">\n      <a-button type=\"primary\" icon=\"reload\" @click=\"refreshWorks\">刷新</a-button>\n    </div>\n  </div>\n\n  <a-card hoverable v-for=\"item in filteredDataSource\" :key=\"item.id\">\n    <div slot=\"cover\" class=\"meta-cardInfo\">\n      <a-tag color=\"blue\">{{item.workType_dictText}}</a-tag>\n      <a-tag v-if=\"item.workStatus == 2\" class=\"status-tag\" color=\"green\">已批改</a-tag>\n      <a-tag v-if=\"item.workStatus == 3\" class=\"status-tag\" color=\"orange\">首页展示</a-tag>\n      <a-tag v-if=\"item.workStatus == 4\" class=\"status-tag\" color=\"purple\">精选作品</a-tag>\n      <a :href=\"getEditorHref(item)\" target=\"_blank\">\n        <img v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n        <img v-else src=\"@/assets/code.png\" alt=\"\">\n      </a>\n      </div>\n    <a-card-meta>\n      <a slot=\"description\" :href=\"getEditorHref(item)\" target=\"_blank\">\n        <h3><j-ellipsis :value=\"item.workName\" :length=\"35\" /></h3>\n      </a>\n    </a-card-meta>\n    <template class=\"ant-card-actions\" slot=\"actions\">\n      <a-popconfirm \n        :title=\"getDeleteConfirmTitle(item)\" \n        @confirm=\"() => handleDelete(item.id)\"\n      >\n        <span class=\"delete-icon-wrapper\" :class=\"{'important-delete': isImportantWork(item)}\">\n          <a-icon \n            type=\"delete\" \n            :style=\"isImportantWork(item) ? { color: '#ff4d4f !important', fontSize: '16px' } : {}\"\n            :class=\"{'delete-icon-red': isImportantWork(item)}\"\n          />\n        </span>\n      </a-popconfirm>\n      <a :href=\"getEditorHref(item)\" target=\"_blank\">\n        <a-icon type=\"edit\"/>\n      </a>\n      <a-popover trigger=\"click\" v-if=\"item.workType==1||item.workType==2\">\n        <template slot=\"content\">\n          <qrcode :value=\"url.shareUrl + item.id\" :size=\"250\"></qrcode>\n        </template>\n        <a><a-icon type=\"share-alt\"/></a>\n      </a-popover>\n    </template>\n  </a-card>\n  \n  <div v-if=\"filteredDataSource.length === 0\" style=\"text-align: center; margin-top: 20px;\">\n    暂无作品\n  </div>\n</div>\n", null]}