{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue", "mtime": 1753249833907}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport { \n  addPaper, \n  editPaper, \n  queryPaperById,\n  getProblemList,\n  getPaperQuestions\n} from '@/api/examSystem'\nimport moment from 'moment'\n\nexport default {\n  name: 'PaperModal',\n  data() {\n    return {\n      // 表单参数\n      title: \"新增试卷\",\n      visible: false,\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      model: {},\n      labelCol: { span: 4 },\n      wrapperCol: { span: 18 },\n      smallLabelCol: { span: 12 },\n      smallWrapperCol: { span: 12 },\n      modalWidth: 900,\n      \n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      \n      // 科目级别选项\n      subjectSelected: '',\n      levelOptions: [],\n      \n      // 选择的题目\n      selectedQuestions: {\n        singleChoiceQuestions: [], // 单选题\n        judgmentQuestions: [], // 判断题\n        programmingQuestions: [] // 编程题\n      },\n      \n      // 记录原始表单值和选中题目，用于检测表单是否被修改\n      originalFormValues: null,\n      originalSelectedQuestions: null,\n      formChanged: false,\n      \n      // 题目表格列定义\n      singleChoiceColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      judgmentColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      programmingColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      \n      // 题目选择器相关\n      questionSelectorVisible: false,\n      questionSelectorTitle: '选择题目',\n      questionType: 0, // 当前选择的题目类型 1:单选题 2:判断题 3:编程题\n      questionQueryParam: {\n        title: '',\n        subject: '',\n        level: '',\n        difficulty: undefined,\n        questionType: undefined\n      },\n      questionList: [],\n      questionLoading: false,\n      questionPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      tempSelectedQuestionKeys: [], // 临时选中的题目ID\n      tempSelectedQuestions: [], // 临时选中的题目对象\n      questionSelectorColumns: [\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', dataIndex: 'difficulty', width: '80px', scopedSlots: { customRender: 'difficultySlot' } },\n        { title: '作者', dataIndex: 'author', width: '120px' }\n      ],\n      \n      // URL\n      url: {\n        add: \"/teaching/examSystem/testManage/add\",\n        edit: \"/teaching/examSystem/testManage/edit\",\n        queryById: \"/teaching/examSystem/testManage/queryById\"\n      },\n    }\n  },\n  methods: {\n    // 保存原始表单值和选中题目\n    saveOriginalFormValues() {\n      // 获取当前表单的所有值\n      this.originalFormValues = this.form.getFieldsValue();\n      \n      // 深拷贝当前选中题目\n      this.originalSelectedQuestions = {\n        singleChoiceQuestions: [...this.selectedQuestions.singleChoiceQuestions],\n        judgmentQuestions: [...this.selectedQuestions.judgmentQuestions],\n        programmingQuestions: [...this.selectedQuestions.programmingQuestions]\n      };\n      \n      // 重置表单变更标志\n      this.formChanged = false;\n    },\n    \n    // 添加表单变更监听\n    addFormChangeListener() {\n      // 先移除之前可能存在的事件监听器\n      this.removeFormChangeListener();\n      \n      // 监听表单字段变化 - 使用更可靠的方式\n      const formValueChangeHandler = () => {\n        // 标记表单已变更\n        this.formChanged = true;\n      };\n      \n      // 保存处理函数引用，以便后续可以移除\n      this.formValueChangeHandler = formValueChangeHandler;\n      \n      // 为所有表单控件添加change事件监听\n      this.$nextTick(() => {\n        // 获取表单所有input, select, textarea元素\n        const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n        \n        // 为每个元素添加change和input事件监听\n        formInputs.forEach(element => {\n          element.addEventListener('change', this.formValueChangeHandler);\n          element.addEventListener('input', this.formValueChangeHandler);\n        });\n        \n        // 特别处理富文本编辑器和其他特殊组件的变化\n        const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n        specialElements.forEach(element => {\n          element.addEventListener('click', this.formValueChangeHandler);\n        });\n      });\n    },\n    \n    // 移除表单变更监听器，避免内存泄露\n    removeFormChangeListener() {\n      if (this.formValueChangeHandler) {\n        this.$nextTick(() => {\n          // 获取表单所有input, select, textarea元素\n          const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n          \n          // 移除事件监听\n          formInputs.forEach(element => {\n            element.removeEventListener('change', this.formValueChangeHandler);\n            element.removeEventListener('input', this.formValueChangeHandler);\n          });\n          \n          // 移除特殊元素的监听\n          const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n          specialElements.forEach(element => {\n            element.removeEventListener('click', this.formValueChangeHandler);\n          });\n        });\n      }\n    },\n    \n    // 检测表单是否发生变化\n    hasFormChanged() {\n      if (this.formChanged) return true;\n      \n      // 检查题目选择是否变化\n      if (this.originalSelectedQuestions) {\n        // 检查各类题目数量是否变化\n        if (\n          this.selectedQuestions.singleChoiceQuestions.length !== this.originalSelectedQuestions.singleChoiceQuestions.length ||\n          this.selectedQuestions.judgmentQuestions.length !== this.originalSelectedQuestions.judgmentQuestions.length ||\n          this.selectedQuestions.programmingQuestions.length !== this.originalSelectedQuestions.programmingQuestions.length\n        ) {\n          return true;\n        }\n        \n        // 检查具体题目是否变化（通过ID比较）\n        const currentSingleIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        const originalSingleIds = this.originalSelectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        \n        const currentJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        const originalJudgmentIds = this.originalSelectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        \n        const currentProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        const originalProgrammingIds = this.originalSelectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        \n        if (\n          currentSingleIds !== originalSingleIds ||\n          currentJudgmentIds !== originalJudgmentIds ||\n          currentProgrammingIds !== originalProgrammingIds\n        ) {\n          return true;\n        }\n      }\n      \n      return false;\n    },\n    \n    // 打开添加模态框\n    add() {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"新增试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.$nextTick(() => {\n        // 默认分数\n        this.form.setFieldsValue({\n          singleChoiceScore: 2,\n          judgmentScore: 2,\n          programmingScore: 25,\n          examDuration: 120 // 默认120分钟\n        })\n        \n        // 保存原始表单值和选中题目，用于检测变更\n        this.saveOriginalFormValues()\n        \n        // 添加表单变更监听\n        this.addFormChangeListener()\n      })\n    },\n    \n    // 打开编辑模态框\n    edit(record) {\n      this.form.resetFields()\n      this.model = Object.assign({}, record)\n      this.visible = true\n      this.title = \"编辑试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      // 先获取试卷基本信息\n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            id: paper.id,\n            title: paper.title,\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 获取试卷题目详情\n              this.loadPaperQuestions(paper.id, content)\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n              this.confirmLoading = false\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n            this.confirmLoading = false\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        } else {\n          this.$message.warning(res.message || '获取试卷详情失败')\n          this.confirmLoading = false\n        }\n      }).catch(() => {\n        this.$message.warning('获取试卷详情失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 加载试卷题目详情\n    loadPaperQuestions(paperId, content) {\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then((res) => {\n        if (res.success) {\n          // 将题目按类型分组\n          const questionData = res.result || []\n          \n          // 创建题目类型到分数的映射\n          const scoreMap = {}\n          if (content && content.questions) {\n            content.questions.forEach(item => {\n              scoreMap[item.questionId] = item.score\n            })\n          }\n          \n          // 按题目类型分组\n          questionData.forEach(item => {\n            const question = item.question\n            const score = item.score\n            \n            // 处理题目数据\n            if (question) {\n              // 根据题型分组\n              switch (question.questionType) {\n                case 1: // 单选题\n                  this.selectedQuestions.singleChoiceQuestions.push(question)\n                  break\n                case 2: // 判断题\n                  this.selectedQuestions.judgmentQuestions.push(question)\n                  break\n                case 3: // 编程题\n                  this.selectedQuestions.programmingQuestions.push(question)\n                  break\n              }\n            }\n          })\n          \n          // 保存原始表单值和选中题目，用于检测变更\n          this.saveOriginalFormValues()\n        } else {\n          this.$message.warning('获取试卷题目失败')\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.$message.warning('获取试卷题目失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 复制试卷\n    copy(record) {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"复制试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            title: paper.title + ' (复制)',\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 设置题目列表\n              if (content.singleChoiceQuestions) {\n                this.selectedQuestions.singleChoiceQuestions = content.singleChoiceQuestions\n              }\n              if (content.judgmentQuestions) {\n                this.selectedQuestions.judgmentQuestions = content.judgmentQuestions\n              }\n              if (content.programmingQuestions) {\n                this.selectedQuestions.programmingQuestions = content.programmingQuestions\n              }\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.confirmLoading = false\n      })\n    },\n    \n    // 科目变更\n    onSubjectChange(value) {\n      this.subjectSelected = value\n      this.updateLevelOptions()\n\n      // 清空级别选择\n      this.$nextTick(() => {\n        this.form.setFieldsValue({\n          level: undefined\n        })\n      })\n    },\n\n    // 级别变更\n    onLevelChange(value) {\n      // 根据级别自动设置考试时长\n      if (value) {\n        const examDuration = this.getExamDurationByLevel(value)\n        this.$nextTick(() => {\n          this.form.setFieldsValue({\n            examDuration: examDuration\n          })\n        })\n      }\n    },\n    \n    // 更新级别选项\n    updateLevelOptions() {\n      if (this.subjectSelected === 'Scratch') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级']\n      } else if (this.subjectSelected === 'Python' || this.subjectSelected === 'C++') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      } else {\n        this.levelOptions = []\n      }\n    },\n    \n    // 根据级别计算考试时长\n    getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n      \n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.startsWith('五') || level.startsWith('六') || level.startsWith('七') || level.startsWith('八')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    \n    // 年份选择面板变化\n    handleYearPanelChange(value, mode) {\n      this.form.setFieldsValue({ year: value })\n      this.yearMode = 'year'\n    },\n    \n    // 年份选择变化\n    handleYearChange(date, dateString) {\n      this.form.setFieldsValue({ year: date })\n    },\n    \n    // 渲染难度标签\n    renderDifficulty(difficulty) {\n      let color = 'default'\n      let text = '未知'\n      \n      if (difficulty === 1) {\n        color = 'green'\n        text = '简单'\n      } else if (difficulty === 2) {\n        color = 'orange'\n        text = '中等'\n      } else if (difficulty === 3) {\n        color = 'red'\n        text = '困难'\n      }\n      \n      return <a-tag color={color}>{text}</a-tag>\n    },\n    \n    // 打开题目选择器\n    showQuestionSelector(type) {\n      this.questionType = type\n      \n      // 根据题目类型设置标题\n      switch (type) {\n        case 1:\n          this.questionSelectorTitle = '选择单选题'\n          break\n        case 2:\n          this.questionSelectorTitle = '选择判断题'\n          break\n        case 3:\n          this.questionSelectorTitle = '选择编程题'\n          break\n      }\n      \n      // 获取当前表单的科目和级别\n      const values = this.form.getFieldsValue(['subject', 'level'])\n      \n      if (!values.subject || !values.level) {\n        this.$message.warning('请先选择科目和级别')\n        return\n      }\n      \n      // 设置查询参数\n      this.questionQueryParam = {\n        title: '',\n        subject: values.subject,\n        level: values.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.questionPagination.current = 1\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n      \n      // 显示模态框\n      this.questionSelectorVisible = true\n      \n      // 加载题目列表\n      this.loadQuestions(1)\n    },\n    \n    // 加载题目列表\n    loadQuestions(page) {\n      if (page) {\n        this.questionPagination.current = page\n      }\n      \n      this.questionLoading = true\n      \n      const params = {\n        ...this.questionQueryParam,\n        pageNo: this.questionPagination.current,\n        pageSize: this.questionPagination.pageSize\n      }\n      \n      // 调用接口查询题目列表\n      getProblemList(params).then((res) => {\n        if (res.success) {\n          this.questionList = res.result.records || []\n          this.questionPagination.total = res.result.total || 0\n          \n          // 更新已选题目的选中状态\n          this.updateSelectedQuestionKeys()\n        } else {\n          this.$message.warning(res.message || '获取题目列表失败')\n        }\n        this.questionLoading = false\n      }).catch(() => {\n        this.questionLoading = false\n      })\n    },\n    \n    // 重置题目查询条件\n    resetQuestionQuery() {\n      // 保留科目和级别、题型\n      this.questionQueryParam = {\n        title: '',\n        subject: this.questionQueryParam.subject,\n        level: this.questionQueryParam.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.loadQuestions(1)\n    },\n    \n    // 题目表格变化\n    handleQuestionTableChange(pagination) {\n      this.questionPagination.current = pagination.current\n      this.loadQuestions()\n    },\n    \n    // 题目选择变化\n    onQuestionSelectionChange(selectedRowKeys, selectedRows) {\n      this.tempSelectedQuestionKeys = selectedRowKeys\n      this.tempSelectedQuestions = selectedRows\n    },\n    \n    // 更新已选题目的选中状态\n    updateSelectedQuestionKeys() {\n      // 获取当前类型的已选题目ID列表\n      let selectedIds = []\n      switch (this.questionType) {\n        case 1:\n          selectedIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          break\n        case 2:\n          selectedIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          break\n        case 3:\n          selectedIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          break\n      }\n      \n      // 查找当前页面中已选择的题目\n      const currentPageSelectedIds = this.questionList\n        .filter(q => selectedIds.includes(q.id))\n        .map(q => q.id)\n      \n      this.tempSelectedQuestionKeys = currentPageSelectedIds\n    },\n    \n    // 确认题目选择\n    confirmQuestionSelection() {\n      // 根据题目类型，将选择的题目添加到对应的列表中\n      switch (this.questionType) {\n        case 1:\n          // 过滤掉重复的题目\n          const existingSingleChoiceIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          const newSingleChoice = this.tempSelectedQuestions.filter(q => !existingSingleChoiceIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.singleChoiceQuestions = [\n            ...this.selectedQuestions.singleChoiceQuestions,\n            ...newSingleChoice\n          ]\n          break\n        case 2:\n          // 过滤掉重复的题目\n          const existingJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          const newJudgment = this.tempSelectedQuestions.filter(q => !existingJudgmentIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.judgmentQuestions = [\n            ...this.selectedQuestions.judgmentQuestions,\n            ...newJudgment\n          ]\n          break\n        case 3:\n          // 过滤掉重复的题目\n          const existingProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          const newProgramming = this.tempSelectedQuestions.filter(q => !existingProgrammingIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.programmingQuestions = [\n            ...this.selectedQuestions.programmingQuestions,\n            ...newProgramming\n          ]\n          break\n      }\n      \n      // 标记表单已变更\n      this.formChanged = true;\n      \n      // 关闭模态框\n      this.handleQuestionSelectorCancel()\n    },\n    \n    // 关闭题目选择器\n    handleQuestionSelectorCancel() {\n      this.questionSelectorVisible = false\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n    },\n    \n    // 移除已选题目\n    removeQuestion(questionType, index) {\n      this.selectedQuestions[questionType].splice(index, 1)\n      \n      // 标记表单已变更\n      this.formChanged = true;\n    },\n    \n    // 提交表单\n    handleOk() {\n      const that = this\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          that.confirmLoading = true\n          \n          // 检查是否有题目\n          const hasQuestions = this.selectedQuestions.singleChoiceQuestions.length > 0 ||\n                              this.selectedQuestions.judgmentQuestions.length > 0 ||\n                              this.selectedQuestions.programmingQuestions.length > 0\n          \n          if (!hasQuestions) {\n            this.$message.warning('请至少添加一道题目')\n            that.confirmLoading = false\n            return\n          }\n          \n          // 处理年份，转为字符串\n          const year = values.year ? moment(values.year).format('YYYY') : moment().format('YYYY')\n          \n          // 构建content字段\n          // 收集所有题目ID和分数\n          const questions = []\n          \n          // 处理单选题\n          this.selectedQuestions.singleChoiceQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.singleChoiceScore || 2\n            })\n          })\n          \n          // 处理判断题\n          this.selectedQuestions.judgmentQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.judgmentScore || 2\n            })\n          })\n          \n          // 处理编程题\n          this.selectedQuestions.programmingQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.programmingScore || 25\n            })\n          })\n          \n          const content = {\n            singleChoiceScore: values.singleChoiceScore,\n            judgmentScore: values.judgmentScore,\n            programmingScore: values.programmingScore,\n            questions: questions\n          }\n          \n          // 构建提交数据\n          const params = {\n            ...this.model,\n            title: values.title,\n            subject: values.subject,\n            level: values.level,\n            difficulty: values.difficulty,\n            type: values.type,\n            year: year,\n            author: values.author,\n            examDuration: values.examDuration,\n            content: JSON.stringify(content)\n          }\n          \n          // 发送请求\n          let apiMethod = params.id ? editPaper : addPaper\n          apiMethod(params).then((res) => {\n            if (res.success) {\n              that.$message.success(res.message || (params.id ? '编辑成功' : '添加成功'))\n              that.$emit('ok')\n              that.close()\n            } else {\n              that.$message.warning(res.message || (params.id ? '编辑失败' : '添加失败'))\n            }\n          }).finally(() => {\n            that.confirmLoading = false\n          })\n        }\n      })\n    },\n    \n    // 处理取消操作\n    handleCancel() {\n      // 检查表单是否有变更\n      if (this.hasFormChanged()) {\n        // 有变更，显示确认对话框\n      this.$confirm({\n        title: '提示',\n        content: '还未提交，是否退出？',\n        okText: '是',\n        cancelText: '否',\n        onOk: () => {\n          this.close();\n        }\n      });\n      } else {\n        // 无变更，直接关闭\n        this.close();\n      }\n    },\n    \n    close() {\n      this.visible = false\n      this.questionSelectorVisible = false\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      // 清理事件监听器，避免内存泄漏\n      this.removeFormChangeListener();\n    }\n  }\n}\n", {"version": 3, "sources": ["PaperModal.vue"], "names": [], "mappings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file": "PaperModal.vue", "sourceRoot": "src/views/examSystem/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    :maskClosable=\"false\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n    \n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 基本信息 -->\n        <a-form-item label=\"试卷标题\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input\n            placeholder=\"请输入试卷标题\"\n            v-decorator=\"['title', {rules: [{required: true, message: '请输入试卷标题!'}]}]\" />\n        </a-form-item>\n        \n        <a-form-item label=\"所属科目\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择所属科目\"\n            v-decorator=\"['subject', {rules: [{required: true, message: '请选择所属科目!'}]}]\"\n            @change=\"onSubjectChange\">\n            <a-select-option value=\"Scratch\">Scratch</a-select-option>\n            <a-select-option value=\"Python\">Python</a-select-option>\n            <a-select-option value=\"C++\">C++</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"所属级别\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择所属级别\"\n            v-decorator=\"['level', {rules: [{required: true, message: '请选择所属级别!'}]}]\"\n            @change=\"onLevelChange\">\n            <a-select-option v-for=\"(level, index) in levelOptions\" :key=\"index\" :value=\"level\">\n              {{ level }}\n            </a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"难度\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择难度\"\n            v-decorator=\"['difficulty', {rules: [{required: true, message: '请选择难度!'}]}]\">\n            <a-select-option :value=\"1\">简单</a-select-option>\n            <a-select-option :value=\"2\">中等</a-select-option>\n            <a-select-option :value=\"3\">困难</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择类型\"\n            v-decorator=\"['type', {rules: [{required: true, message: '请选择类型!'}]}]\">\n            <a-select-option value=\"真题\">真题</a-select-option>\n            <a-select-option value=\"模拟\">模拟</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"年份\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-date-picker\n            placeholder=\"请选择年份\"\n            v-decorator=\"['year', {rules: [{required: true, message: '请选择年份!'}]}]\"\n            :format=\"yearFormat\"\n            :mode=\"yearMode\"\n            @panelChange=\"handleYearPanelChange\"\n            @change=\"handleYearChange\" />\n        </a-form-item>\n        \n        <a-form-item label=\"作者\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input\n            placeholder=\"请输入作者\"\n            v-decorator=\"['author', {rules: [{required: true, message: '请输入作者!'}]}]\" />\n        </a-form-item>\n        \n        <a-form-item label=\"考试时长(分钟)\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input-number\n            placeholder=\"请输入考试时长\"\n            v-decorator=\"['examDuration', {rules: [{required: true, message: '请输入考试时长!'}]}]\"\n            :min=\"1\"\n            :max=\"1440\"\n            :step=\"10\" />\n        </a-form-item>\n        \n        <!-- 题目分数设置 -->\n        <a-divider>题目分数设置</a-divider>\n        <a-row :gutter=\"24\">\n          <a-col :span=\"8\">\n            <a-form-item label=\"单选题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"单选题每题分数\"\n                v-decorator=\"['singleChoiceScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"8\">\n            <a-form-item label=\"判断题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"判断题每题分数\"\n                v-decorator=\"['judgmentScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"8\">\n            <a-form-item label=\"编程题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"编程题每题分数\"\n                v-decorator=\"['programmingScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n        </a-row>\n        \n        <!-- 题目选择区域 -->\n        <a-divider>题目选择</a-divider>\n        \n        <!-- 单选题选择 -->\n        <a-collapse defaultActiveKey=\"1\">\n          <a-collapse-panel key=\"1\" header=\"单选题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(1)\">\n              添加单选题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"singleChoiceColumns\"\n              :dataSource=\"selectedQuestions.singleChoiceQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('singleChoiceQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n          \n          <!-- 判断题选择 -->\n          <a-collapse-panel key=\"2\" header=\"判断题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(2)\">\n              添加判断题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"judgmentColumns\"\n              :dataSource=\"selectedQuestions.judgmentQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('judgmentQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n          \n          <!-- 编程题选择 -->\n          <a-collapse-panel key=\"3\" header=\"编程题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(3)\">\n              添加编程题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"programmingColumns\"\n              :dataSource=\"selectedQuestions.programmingQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('programmingQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n        </a-collapse>\n      </a-form>\n    </a-spin>\n    \n    <!-- 题目选择器模态框 -->\n    <a-modal\n      :title=\"questionSelectorTitle\"\n      :width=\"800\"\n      :visible=\"questionSelectorVisible\"\n      :footer=\"null\"\n      @cancel=\"handleQuestionSelectorCancel\"\n    >\n      <!-- 查询条件 -->\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"12\">\n          <a-col :md=\"12\">\n            <a-form-item label=\"题目标题\">\n              <a-input v-model=\"questionQueryParam.title\" placeholder=\"请输入题目标题\" />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-form-item label=\"科目/级别\">\n              <a-input v-model=\"questionQueryParam.subject\" disabled />\n              <a-input v-model=\"questionQueryParam.level\" disabled style=\"width: 80px; margin-left: 8px;\" />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-form-item label=\"难度\">\n              <a-select v-model=\"questionQueryParam.difficulty\" placeholder=\"请选择难度\" allowClear style=\"width: 160px;\">\n                <a-select-option :value=\"1\">简单</a-select-option>\n                <a-select-option :value=\"2\">中等</a-select-option>\n                <a-select-option :value=\"3\">困难</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-button type=\"primary\" @click=\"loadQuestions(1)\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetQuestionQuery\">重置</a-button>\n          </a-col>\n        </a-row>\n      </a-form>\n      \n      <!-- 题目列表 -->\n      <a-table\n        size=\"middle\"\n        rowKey=\"id\"\n        :columns=\"questionSelectorColumns\"\n        :dataSource=\"questionList\"\n        :pagination=\"questionPagination\"\n        :loading=\"questionLoading\"\n        :rowSelection=\"{\n          selectedRowKeys: tempSelectedQuestionKeys,\n          onChange: onQuestionSelectionChange\n        }\"\n        @change=\"handleQuestionTableChange\"\n        style=\"margin-top: 16px;\"\n      >\n        <!-- 自定义难度展示 -->\n        <template slot=\"difficultySlot\" slot-scope=\"text\">\n          <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n          <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n          <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n          <a-tag v-else>未知</a-tag>\n        </template>\n      </a-table>\n      \n      <!-- 操作按钮 -->\n      <div style=\"text-align: right; margin-top: 16px;\">\n        <a-button @click=\"handleQuestionSelectorCancel\">取消</a-button>\n        <a-button type=\"primary\" style=\"margin-left: 8px;\" @click=\"confirmQuestionSelection\">\n          确认选择\n        </a-button>\n      </div>\n    </a-modal>\n  </a-modal>\n</template>\n\n<script>\nimport { \n  addPaper, \n  editPaper, \n  queryPaperById,\n  getProblemList,\n  getPaperQuestions\n} from '@/api/examSystem'\nimport moment from 'moment'\n\nexport default {\n  name: 'PaperModal',\n  data() {\n    return {\n      // 表单参数\n      title: \"新增试卷\",\n      visible: false,\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      model: {},\n      labelCol: { span: 4 },\n      wrapperCol: { span: 18 },\n      smallLabelCol: { span: 12 },\n      smallWrapperCol: { span: 12 },\n      modalWidth: 900,\n      \n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      \n      // 科目级别选项\n      subjectSelected: '',\n      levelOptions: [],\n      \n      // 选择的题目\n      selectedQuestions: {\n        singleChoiceQuestions: [], // 单选题\n        judgmentQuestions: [], // 判断题\n        programmingQuestions: [] // 编程题\n      },\n      \n      // 记录原始表单值和选中题目，用于检测表单是否被修改\n      originalFormValues: null,\n      originalSelectedQuestions: null,\n      formChanged: false,\n      \n      // 题目表格列定义\n      singleChoiceColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      judgmentColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      programmingColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      \n      // 题目选择器相关\n      questionSelectorVisible: false,\n      questionSelectorTitle: '选择题目',\n      questionType: 0, // 当前选择的题目类型 1:单选题 2:判断题 3:编程题\n      questionQueryParam: {\n        title: '',\n        subject: '',\n        level: '',\n        difficulty: undefined,\n        questionType: undefined\n      },\n      questionList: [],\n      questionLoading: false,\n      questionPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      tempSelectedQuestionKeys: [], // 临时选中的题目ID\n      tempSelectedQuestions: [], // 临时选中的题目对象\n      questionSelectorColumns: [\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', dataIndex: 'difficulty', width: '80px', scopedSlots: { customRender: 'difficultySlot' } },\n        { title: '作者', dataIndex: 'author', width: '120px' }\n      ],\n      \n      // URL\n      url: {\n        add: \"/teaching/examSystem/testManage/add\",\n        edit: \"/teaching/examSystem/testManage/edit\",\n        queryById: \"/teaching/examSystem/testManage/queryById\"\n      },\n    }\n  },\n  methods: {\n    // 保存原始表单值和选中题目\n    saveOriginalFormValues() {\n      // 获取当前表单的所有值\n      this.originalFormValues = this.form.getFieldsValue();\n      \n      // 深拷贝当前选中题目\n      this.originalSelectedQuestions = {\n        singleChoiceQuestions: [...this.selectedQuestions.singleChoiceQuestions],\n        judgmentQuestions: [...this.selectedQuestions.judgmentQuestions],\n        programmingQuestions: [...this.selectedQuestions.programmingQuestions]\n      };\n      \n      // 重置表单变更标志\n      this.formChanged = false;\n    },\n    \n    // 添加表单变更监听\n    addFormChangeListener() {\n      // 先移除之前可能存在的事件监听器\n      this.removeFormChangeListener();\n      \n      // 监听表单字段变化 - 使用更可靠的方式\n      const formValueChangeHandler = () => {\n        // 标记表单已变更\n        this.formChanged = true;\n      };\n      \n      // 保存处理函数引用，以便后续可以移除\n      this.formValueChangeHandler = formValueChangeHandler;\n      \n      // 为所有表单控件添加change事件监听\n      this.$nextTick(() => {\n        // 获取表单所有input, select, textarea元素\n        const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n        \n        // 为每个元素添加change和input事件监听\n        formInputs.forEach(element => {\n          element.addEventListener('change', this.formValueChangeHandler);\n          element.addEventListener('input', this.formValueChangeHandler);\n        });\n        \n        // 特别处理富文本编辑器和其他特殊组件的变化\n        const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n        specialElements.forEach(element => {\n          element.addEventListener('click', this.formValueChangeHandler);\n        });\n      });\n    },\n    \n    // 移除表单变更监听器，避免内存泄露\n    removeFormChangeListener() {\n      if (this.formValueChangeHandler) {\n        this.$nextTick(() => {\n          // 获取表单所有input, select, textarea元素\n          const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n          \n          // 移除事件监听\n          formInputs.forEach(element => {\n            element.removeEventListener('change', this.formValueChangeHandler);\n            element.removeEventListener('input', this.formValueChangeHandler);\n          });\n          \n          // 移除特殊元素的监听\n          const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n          specialElements.forEach(element => {\n            element.removeEventListener('click', this.formValueChangeHandler);\n          });\n        });\n      }\n    },\n    \n    // 检测表单是否发生变化\n    hasFormChanged() {\n      if (this.formChanged) return true;\n      \n      // 检查题目选择是否变化\n      if (this.originalSelectedQuestions) {\n        // 检查各类题目数量是否变化\n        if (\n          this.selectedQuestions.singleChoiceQuestions.length !== this.originalSelectedQuestions.singleChoiceQuestions.length ||\n          this.selectedQuestions.judgmentQuestions.length !== this.originalSelectedQuestions.judgmentQuestions.length ||\n          this.selectedQuestions.programmingQuestions.length !== this.originalSelectedQuestions.programmingQuestions.length\n        ) {\n          return true;\n        }\n        \n        // 检查具体题目是否变化（通过ID比较）\n        const currentSingleIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        const originalSingleIds = this.originalSelectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        \n        const currentJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        const originalJudgmentIds = this.originalSelectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        \n        const currentProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        const originalProgrammingIds = this.originalSelectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        \n        if (\n          currentSingleIds !== originalSingleIds ||\n          currentJudgmentIds !== originalJudgmentIds ||\n          currentProgrammingIds !== originalProgrammingIds\n        ) {\n          return true;\n        }\n      }\n      \n      return false;\n    },\n    \n    // 打开添加模态框\n    add() {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"新增试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.$nextTick(() => {\n        // 默认分数\n        this.form.setFieldsValue({\n          singleChoiceScore: 2,\n          judgmentScore: 2,\n          programmingScore: 25,\n          examDuration: 120 // 默认120分钟\n        })\n        \n        // 保存原始表单值和选中题目，用于检测变更\n        this.saveOriginalFormValues()\n        \n        // 添加表单变更监听\n        this.addFormChangeListener()\n      })\n    },\n    \n    // 打开编辑模态框\n    edit(record) {\n      this.form.resetFields()\n      this.model = Object.assign({}, record)\n      this.visible = true\n      this.title = \"编辑试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      // 先获取试卷基本信息\n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            id: paper.id,\n            title: paper.title,\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 获取试卷题目详情\n              this.loadPaperQuestions(paper.id, content)\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n              this.confirmLoading = false\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n            this.confirmLoading = false\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        } else {\n          this.$message.warning(res.message || '获取试卷详情失败')\n          this.confirmLoading = false\n        }\n      }).catch(() => {\n        this.$message.warning('获取试卷详情失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 加载试卷题目详情\n    loadPaperQuestions(paperId, content) {\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then((res) => {\n        if (res.success) {\n          // 将题目按类型分组\n          const questionData = res.result || []\n          \n          // 创建题目类型到分数的映射\n          const scoreMap = {}\n          if (content && content.questions) {\n            content.questions.forEach(item => {\n              scoreMap[item.questionId] = item.score\n            })\n          }\n          \n          // 按题目类型分组\n          questionData.forEach(item => {\n            const question = item.question\n            const score = item.score\n            \n            // 处理题目数据\n            if (question) {\n              // 根据题型分组\n              switch (question.questionType) {\n                case 1: // 单选题\n                  this.selectedQuestions.singleChoiceQuestions.push(question)\n                  break\n                case 2: // 判断题\n                  this.selectedQuestions.judgmentQuestions.push(question)\n                  break\n                case 3: // 编程题\n                  this.selectedQuestions.programmingQuestions.push(question)\n                  break\n              }\n            }\n          })\n          \n          // 保存原始表单值和选中题目，用于检测变更\n          this.saveOriginalFormValues()\n        } else {\n          this.$message.warning('获取试卷题目失败')\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.$message.warning('获取试卷题目失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 复制试卷\n    copy(record) {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"复制试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            title: paper.title + ' (复制)',\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 设置题目列表\n              if (content.singleChoiceQuestions) {\n                this.selectedQuestions.singleChoiceQuestions = content.singleChoiceQuestions\n              }\n              if (content.judgmentQuestions) {\n                this.selectedQuestions.judgmentQuestions = content.judgmentQuestions\n              }\n              if (content.programmingQuestions) {\n                this.selectedQuestions.programmingQuestions = content.programmingQuestions\n              }\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.confirmLoading = false\n      })\n    },\n    \n    // 科目变更\n    onSubjectChange(value) {\n      this.subjectSelected = value\n      this.updateLevelOptions()\n\n      // 清空级别选择\n      this.$nextTick(() => {\n        this.form.setFieldsValue({\n          level: undefined\n        })\n      })\n    },\n\n    // 级别变更\n    onLevelChange(value) {\n      // 根据级别自动设置考试时长\n      if (value) {\n        const examDuration = this.getExamDurationByLevel(value)\n        this.$nextTick(() => {\n          this.form.setFieldsValue({\n            examDuration: examDuration\n          })\n        })\n      }\n    },\n    \n    // 更新级别选项\n    updateLevelOptions() {\n      if (this.subjectSelected === 'Scratch') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级']\n      } else if (this.subjectSelected === 'Python' || this.subjectSelected === 'C++') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      } else {\n        this.levelOptions = []\n      }\n    },\n    \n    // 根据级别计算考试时长\n    getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n      \n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.startsWith('五') || level.startsWith('六') || level.startsWith('七') || level.startsWith('八')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    \n    // 年份选择面板变化\n    handleYearPanelChange(value, mode) {\n      this.form.setFieldsValue({ year: value })\n      this.yearMode = 'year'\n    },\n    \n    // 年份选择变化\n    handleYearChange(date, dateString) {\n      this.form.setFieldsValue({ year: date })\n    },\n    \n    // 渲染难度标签\n    renderDifficulty(difficulty) {\n      let color = 'default'\n      let text = '未知'\n      \n      if (difficulty === 1) {\n        color = 'green'\n        text = '简单'\n      } else if (difficulty === 2) {\n        color = 'orange'\n        text = '中等'\n      } else if (difficulty === 3) {\n        color = 'red'\n        text = '困难'\n      }\n      \n      return <a-tag color={color}>{text}</a-tag>\n    },\n    \n    // 打开题目选择器\n    showQuestionSelector(type) {\n      this.questionType = type\n      \n      // 根据题目类型设置标题\n      switch (type) {\n        case 1:\n          this.questionSelectorTitle = '选择单选题'\n          break\n        case 2:\n          this.questionSelectorTitle = '选择判断题'\n          break\n        case 3:\n          this.questionSelectorTitle = '选择编程题'\n          break\n      }\n      \n      // 获取当前表单的科目和级别\n      const values = this.form.getFieldsValue(['subject', 'level'])\n      \n      if (!values.subject || !values.level) {\n        this.$message.warning('请先选择科目和级别')\n        return\n      }\n      \n      // 设置查询参数\n      this.questionQueryParam = {\n        title: '',\n        subject: values.subject,\n        level: values.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.questionPagination.current = 1\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n      \n      // 显示模态框\n      this.questionSelectorVisible = true\n      \n      // 加载题目列表\n      this.loadQuestions(1)\n    },\n    \n    // 加载题目列表\n    loadQuestions(page) {\n      if (page) {\n        this.questionPagination.current = page\n      }\n      \n      this.questionLoading = true\n      \n      const params = {\n        ...this.questionQueryParam,\n        pageNo: this.questionPagination.current,\n        pageSize: this.questionPagination.pageSize\n      }\n      \n      // 调用接口查询题目列表\n      getProblemList(params).then((res) => {\n        if (res.success) {\n          this.questionList = res.result.records || []\n          this.questionPagination.total = res.result.total || 0\n          \n          // 更新已选题目的选中状态\n          this.updateSelectedQuestionKeys()\n        } else {\n          this.$message.warning(res.message || '获取题目列表失败')\n        }\n        this.questionLoading = false\n      }).catch(() => {\n        this.questionLoading = false\n      })\n    },\n    \n    // 重置题目查询条件\n    resetQuestionQuery() {\n      // 保留科目和级别、题型\n      this.questionQueryParam = {\n        title: '',\n        subject: this.questionQueryParam.subject,\n        level: this.questionQueryParam.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.loadQuestions(1)\n    },\n    \n    // 题目表格变化\n    handleQuestionTableChange(pagination) {\n      this.questionPagination.current = pagination.current\n      this.loadQuestions()\n    },\n    \n    // 题目选择变化\n    onQuestionSelectionChange(selectedRowKeys, selectedRows) {\n      this.tempSelectedQuestionKeys = selectedRowKeys\n      this.tempSelectedQuestions = selectedRows\n    },\n    \n    // 更新已选题目的选中状态\n    updateSelectedQuestionKeys() {\n      // 获取当前类型的已选题目ID列表\n      let selectedIds = []\n      switch (this.questionType) {\n        case 1:\n          selectedIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          break\n        case 2:\n          selectedIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          break\n        case 3:\n          selectedIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          break\n      }\n      \n      // 查找当前页面中已选择的题目\n      const currentPageSelectedIds = this.questionList\n        .filter(q => selectedIds.includes(q.id))\n        .map(q => q.id)\n      \n      this.tempSelectedQuestionKeys = currentPageSelectedIds\n    },\n    \n    // 确认题目选择\n    confirmQuestionSelection() {\n      // 根据题目类型，将选择的题目添加到对应的列表中\n      switch (this.questionType) {\n        case 1:\n          // 过滤掉重复的题目\n          const existingSingleChoiceIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          const newSingleChoice = this.tempSelectedQuestions.filter(q => !existingSingleChoiceIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.singleChoiceQuestions = [\n            ...this.selectedQuestions.singleChoiceQuestions,\n            ...newSingleChoice\n          ]\n          break\n        case 2:\n          // 过滤掉重复的题目\n          const existingJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          const newJudgment = this.tempSelectedQuestions.filter(q => !existingJudgmentIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.judgmentQuestions = [\n            ...this.selectedQuestions.judgmentQuestions,\n            ...newJudgment\n          ]\n          break\n        case 3:\n          // 过滤掉重复的题目\n          const existingProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          const newProgramming = this.tempSelectedQuestions.filter(q => !existingProgrammingIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.programmingQuestions = [\n            ...this.selectedQuestions.programmingQuestions,\n            ...newProgramming\n          ]\n          break\n      }\n      \n      // 标记表单已变更\n      this.formChanged = true;\n      \n      // 关闭模态框\n      this.handleQuestionSelectorCancel()\n    },\n    \n    // 关闭题目选择器\n    handleQuestionSelectorCancel() {\n      this.questionSelectorVisible = false\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n    },\n    \n    // 移除已选题目\n    removeQuestion(questionType, index) {\n      this.selectedQuestions[questionType].splice(index, 1)\n      \n      // 标记表单已变更\n      this.formChanged = true;\n    },\n    \n    // 提交表单\n    handleOk() {\n      const that = this\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          that.confirmLoading = true\n          \n          // 检查是否有题目\n          const hasQuestions = this.selectedQuestions.singleChoiceQuestions.length > 0 ||\n                              this.selectedQuestions.judgmentQuestions.length > 0 ||\n                              this.selectedQuestions.programmingQuestions.length > 0\n          \n          if (!hasQuestions) {\n            this.$message.warning('请至少添加一道题目')\n            that.confirmLoading = false\n            return\n          }\n          \n          // 处理年份，转为字符串\n          const year = values.year ? moment(values.year).format('YYYY') : moment().format('YYYY')\n          \n          // 构建content字段\n          // 收集所有题目ID和分数\n          const questions = []\n          \n          // 处理单选题\n          this.selectedQuestions.singleChoiceQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.singleChoiceScore || 2\n            })\n          })\n          \n          // 处理判断题\n          this.selectedQuestions.judgmentQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.judgmentScore || 2\n            })\n          })\n          \n          // 处理编程题\n          this.selectedQuestions.programmingQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.programmingScore || 25\n            })\n          })\n          \n          const content = {\n            singleChoiceScore: values.singleChoiceScore,\n            judgmentScore: values.judgmentScore,\n            programmingScore: values.programmingScore,\n            questions: questions\n          }\n          \n          // 构建提交数据\n          const params = {\n            ...this.model,\n            title: values.title,\n            subject: values.subject,\n            level: values.level,\n            difficulty: values.difficulty,\n            type: values.type,\n            year: year,\n            author: values.author,\n            examDuration: values.examDuration,\n            content: JSON.stringify(content)\n          }\n          \n          // 发送请求\n          let apiMethod = params.id ? editPaper : addPaper\n          apiMethod(params).then((res) => {\n            if (res.success) {\n              that.$message.success(res.message || (params.id ? '编辑成功' : '添加成功'))\n              that.$emit('ok')\n              that.close()\n            } else {\n              that.$message.warning(res.message || (params.id ? '编辑失败' : '添加失败'))\n            }\n          }).finally(() => {\n            that.confirmLoading = false\n          })\n        }\n      })\n    },\n    \n    // 处理取消操作\n    handleCancel() {\n      // 检查表单是否有变更\n      if (this.hasFormChanged()) {\n        // 有变更，显示确认对话框\n      this.$confirm({\n        title: '提示',\n        content: '还未提交，是否退出？',\n        okText: '是',\n        cancelText: '否',\n        onOk: () => {\n          this.close();\n        }\n      });\n      } else {\n        // 无变更，直接关闭\n        this.close();\n      }\n    },\n    \n    close() {\n      this.visible = false\n      this.questionSelectorVisible = false\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      // 清理事件监听器，避免内存泄漏\n      this.removeFormChangeListener();\n    }\n  }\n}\n</script>\n\n<style scoped>\n</style> "]}]}