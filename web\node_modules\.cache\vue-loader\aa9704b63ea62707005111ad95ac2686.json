{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlineExam.vue?vue&type=style&index=0&id=0987cf69&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\onlineExam.vue", "mtime": 1753194259986}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\r\n.card-list {\r\n  margin-top: 12px;\r\n}\r\n\r\n.exam-card {\r\n  min-height: 270px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .ant-card-body {\r\n    padding: 0 !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  // 覆盖 Ant Design 卡片的默认内边距\r\n  /deep/ .ant-card-body {\r\n    padding: 0 !important;\r\n  }\r\n\r\n  // 卡片头部彩带\r\n  .card-header {\r\n    height: 80px;\r\n    padding: 16px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    color: white;\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);\r\n    }\r\n\r\n    .subject-icon {\r\n      font-size: 24px;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    .subject-info {\r\n      flex: 1;\r\n\r\n      .subject-name {\r\n        display: block;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .level-badge {\r\n        display: inline-block;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        padding: 2px 8px;\r\n        border-radius: 12px;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n\r\n    .year-badge {\r\n      background: rgba(255, 255, 255, 0.2);\r\n      padding: 4px 12px;\r\n      border-radius: 16px;\r\n      font-size: 14px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  // 科目主题色\r\n  .scratch-theme {\r\n    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\r\n  }\r\n\r\n  .python-theme {\r\n    background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);\r\n  }\r\n\r\n  .cpp-theme {\r\n    background: linear-gradient(135deg, #2196f3 0%, #03a9f4 100%);\r\n  }\r\n\r\n  .default-theme {\r\n    background: linear-gradient(135deg, #9c27b0 0%, #e91e63 100%);\r\n  }\r\n\r\n  // 卡片内容\r\n  .card-content {\r\n    flex: 1;\r\n    padding: 12px 16px 0px 12px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 3px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n    min-height: 44px;\r\n  }\r\n\r\n  .card-info {\r\n    margin-bottom: 10px;\r\n\r\n    .info-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 4px;\r\n      color: #666;\r\n      font-size: 14px;\r\n\r\n      .info-label {\r\n        color: #1890ff;\r\n        font-weight: 500;\r\n        margin-right: 4px;\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 8px;\r\n        color: #1890ff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 12px;\r\n\r\n    .start-btn {\r\n      width: 110px;\r\n      height: 36px;\r\n      border-radius: 18px;\r\n      font-weight: bold;\r\n      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\r\n      border: none;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      line-height: 1;\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\r\n        transform: translateY(-1px);\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 4px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n\r\n      span {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n    }\r\n\r\n    .detail-btn {\r\n      width: 110px;\r\n      height: 36px;\r\n      border-radius: 18px;\r\n      border: 1px solid #d9d9d9;\r\n      color: #666;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      line-height: 1;\r\n\r\n      &:hover {\r\n        border-color: #1890ff;\r\n        color: #1890ff;\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 4px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n\r\n      span {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.label {\r\n  font-weight: bold;\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n\r\n\r\n// 试卷详情模态框样式\r\n.paper-detail {\r\n  .detail-header {\r\n    position: relative;\r\n    border-radius: 16px;\r\n    margin-bottom: 24px;\r\n    overflow: hidden;\r\n    min-height: 120px;\r\n\r\n    .header-background {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n      z-index: 1;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: linear-gradient(45deg, rgba(255,255,255,0.08) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.08) 75%);\r\n        background-size: 30px 30px;\r\n        animation: move 25s linear infinite;\r\n      }\r\n    }\r\n\r\n    .header-content {\r\n      position: relative;\r\n      z-index: 2;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 24px;\r\n      color: white;\r\n\r\n      .subject-icon-large {\r\n        width: 80px;\r\n        height: 80px;\r\n        background: linear-gradient(135deg, #5dade2 0%, #3498db 100%);\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 32px;\r\n        margin-right: 20px;\r\n        color: #fff;\r\n        box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);\r\n        border: 3px solid rgba(255, 255, 255, 0.9);\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: -50%;\r\n          left: -50%;\r\n          width: 200%;\r\n          height: 200%;\r\n          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);\r\n          animation: shine 4s ease-in-out infinite;\r\n        }\r\n      }\r\n\r\n      .header-info {\r\n        flex: 1;\r\n\r\n        h2 {\r\n          margin: 0 0 16px 0;\r\n          font-size: 24px;\r\n          font-weight: bold;\r\n          color: #fff;\r\n          text-shadow: 0 2px 8px rgba(74, 144, 226, 0.5);\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .detail-badges {\r\n          display: flex;\r\n          gap: 12px;\r\n          flex-wrap: wrap;\r\n\r\n          .badge-item {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 8px 16px;\r\n            border-radius: 20px;\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            background: rgba(255, 255, 255, 0.25);\r\n            color: white;\r\n            backdrop-filter: blur(10px);\r\n            border: 1px solid rgba(255, 255, 255, 0.3);\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.35);\r\n              transform: translateY(-2px);\r\n            }\r\n\r\n            .anticon {\r\n              margin-right: 6px;\r\n              font-size: 14px;\r\n            }\r\n\r\n            span {\r\n              text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .detail-content {\r\n    .info-card {\r\n      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);\r\n      border: 1px solid #e6f2ff;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      margin-bottom: 20px;\r\n      box-shadow: 0 4px 16px rgba(74, 144, 226, 0.08);\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        height: 3px;\r\n        background: linear-gradient(90deg, #4a90e2 0%, #357abd 100%);\r\n        border-radius: 12px 12px 0 0;\r\n      }\r\n\r\n      h4 {\r\n        color: #4a90e2;\r\n        margin-bottom: 20px;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .anticon {\r\n          margin-right: 10px;\r\n          font-size: 20px;\r\n          color: #4a90e2;\r\n        }\r\n      }\r\n\r\n      p {\r\n        margin-bottom: 12px;\r\n        color: #555;\r\n        font-size: 15px;\r\n        line-height: 1.6;\r\n\r\n        strong {\r\n          color: #2c3e50;\r\n          margin-right: 8px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        .ant-rate {\r\n          margin-right: 8px;\r\n          font-size: 16px;\r\n\r\n          .ant-rate-star {\r\n            color: #f39c12;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.label {\r\n  font-weight: bold;\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.exam-info {\r\n  h3 {\r\n    font-size: 20px;\r\n    text-align: center;\r\n    margin-bottom: 24px;\r\n    color: #4a90e2;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .exam-info-content {\r\n    margin-bottom: 32px;\r\n\r\n    .basic-info {\r\n      background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);\r\n      border: 1px solid #d6e9f7;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      margin-bottom: 24px;\r\n\r\n      .info-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .label {\r\n          font-weight: 600;\r\n          color: #4a90e2;\r\n          min-width: 80px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        .value {\r\n          color: #2c3e50;\r\n          flex: 1;\r\n\r\n          .score-highlight {\r\n            color: #ff4d4f;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .exam-rules {\r\n      h4 {\r\n        color: #4a90e2;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        margin-bottom: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        &::before {\r\n          content: '';\r\n          width: 4px;\r\n          height: 16px;\r\n          background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n          border-radius: 2px;\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .rules-list {\r\n        .rule-item {\r\n          display: flex;\r\n          align-items: flex-start;\r\n          padding: 6px 16px;\r\n          background: #fff;\r\n          transition: all 0.3s ease;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .rule-icon {\r\n            font-size: 16px;\r\n            margin-right: 12px;\r\n            margin-top: 2px;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          span {\r\n            color: #555;\r\n            line-height: 1.5;\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .exam-actions {\r\n    text-align: center;\r\n    padding-top: 16px;\r\n\r\n    button {\r\n      margin: 0 8px;\r\n      height: 40px;\r\n      padding: 0 24px;\r\n      border-radius: 6px;\r\n      font-weight: 500;\r\n\r\n      &.ant-btn-primary {\r\n        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n        border: none;\r\n        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);\r\n\r\n        &:hover {\r\n          background: linear-gradient(135deg, #357abd 0%, #2c5aa0 100%);\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);\r\n        }\r\n      }\r\n\r\n      &:not(.ant-btn-primary) {\r\n        color: #666;\r\n        border-color: #d9d9d9;\r\n\r\n        &:hover {\r\n          color: #4a90e2;\r\n          border-color: #4a90e2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes move {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 40px 40px;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n  50% {\r\n    transform: translateX(100%) translateY(100%) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n}\r\n\r\n@keyframes rainbow {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes move {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 60px 60px;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n  50% {\r\n    transform: translateX(100%) translateY(100%) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n}\r\n", {"version": 3, "sources": ["onlineExam.vue"], "names": [], "mappings": ";AAqe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file": "onlineExam.vue", "sourceRoot": "src/views/examSystem", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 考试界面 -->\r\n    <exam-taking-page\r\n      v-if=\"showExamPage\"\r\n      :paper-id=\"currentPaper.id\"\r\n      @back-to-list=\"backToExamList\"\r\n      @exam-completed=\"handleExamCompleted\"\r\n    />\r\n\r\n    <!-- 试卷列表界面 -->\r\n    <a-card :bordered=\"false\" v-else>\r\n    <!-- 查询区域 -->\r\n    <div class=\"table-page-search-wrapper\">\r\n      <a-form layout=\"inline\">\r\n        <a-row :gutter=\"24\">\r\n          <a-col :md=\"6\" :sm=\"8\">\r\n            <a-form-item label=\"科目\">\r\n              <a-select placeholder=\"请选择科目\" v-model=\"queryParam.subject\">\r\n                <a-select-option value=\"\">全部</a-select-option>\r\n                <a-select-option value=\"Scratch\">Scratch</a-select-option>\r\n                <a-select-option value=\"Python\">Python</a-select-option>\r\n                <a-select-option value=\"C++\">C++</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"8\">\r\n            <a-form-item label=\"级别\">\r\n              <a-select placeholder=\"请选择级别\" v-model=\"queryParam.level\" :disabled=\"!queryParam.subject\">\r\n                <a-select-option value=\"\">全部</a-select-option>\r\n                <template v-if=\"queryParam.subject === 'Scratch'\">\r\n                  <a-select-option value=\"一级\">一级</a-select-option>\r\n                  <a-select-option value=\"二级\">二级</a-select-option>\r\n                  <a-select-option value=\"三级\">三级</a-select-option>\r\n                  <a-select-option value=\"四级\">四级</a-select-option>\r\n                </template>\r\n                <template v-else>\r\n                  <a-select-option value=\"一级\">一级</a-select-option>\r\n                  <a-select-option value=\"二级\">二级</a-select-option>\r\n                  <a-select-option value=\"三级\">三级</a-select-option>\r\n                  <a-select-option value=\"四级\">四级</a-select-option>\r\n                  <a-select-option value=\"五级\">五级</a-select-option>\r\n                  <a-select-option value=\"六级\">六级</a-select-option>\r\n                  <a-select-option value=\"七级\">七级</a-select-option>\r\n                  <a-select-option value=\"八级\">八级</a-select-option>\r\n                </template>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"8\">\r\n            <a-form-item label=\"类型\">\r\n              <a-select placeholder=\"请选择类型\" v-model=\"queryParam.type\">\r\n                <a-select-option value=\"\">全部</a-select-option>\r\n                <a-select-option value=\"真题\">真题</a-select-option>\r\n                <a-select-option value=\"模拟\">模拟</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"8\">\r\n            <a-form-item label=\"年份\">\r\n              <a-select placeholder=\"请选择年份\" v-model=\"queryParam.year\">\r\n                <a-select-option value=\"\">全部</a-select-option>\r\n                <a-select-option :value=\"year\" v-for=\"year in yearOptions\" :key=\"year\">{{ year }}年</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"8\">\r\n            <span class=\"table-page-search-submitButtons\">\r\n              <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\r\n              <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\r\n            </span>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n    </div>\r\n\r\n    <!-- 卡片列表 -->\r\n    <div class=\"card-list\">\r\n      <a-spin :spinning=\"loading\">\r\n        <a-row :gutter=\"[10, 10]\">\r\n          <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"paper in dataSource\" :key=\"paper.id\">\r\n            <a-card hoverable class=\"exam-card\">\r\n              <!-- 卡片头部彩带 -->\r\n              <div class=\"card-header\" :class=\"getSubjectClass(paper.subject)\">\r\n                <div class=\"subject-icon\">\r\n                  <a-icon :type=\"getSubjectIcon(paper.subject)\" />\r\n                </div>\r\n                <div class=\"subject-info\">\r\n                  <span class=\"subject-name\">{{ paper.subject }}</span>\r\n                  <span class=\"level-badge\">{{ paper.level }}</span>\r\n                </div>\r\n                <div class=\"year-badge\">{{ paper.year }}年</div>\r\n              </div>\r\n\r\n              <!-- 卡片内容 -->\r\n              <div class=\"card-content\">\r\n                <h3 class=\"card-title\">{{ paper.title }}</h3>\r\n\r\n                <div class=\"card-info\">\r\n                  <div class=\"info-item\">\r\n                    <span class=\"info-label\">考试时长：</span>\r\n                    <span>{{ paper.examDuration }}分钟</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <span class=\"info-label\">试卷难度：</span>\r\n                    <a-rate :value=\"paper.difficulty\" :count=\"3\" disabled size=\"small\" />\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <a-button type=\"primary\" size=\"large\" @click.stop=\"handleStartExam(paper)\" class=\"start-btn\">\r\n                    <a-icon type=\"play-circle\" />\r\n                    开始考试\r\n                  </a-button>\r\n                  <a-button @click.stop=\"handleViewDetail(paper)\" class=\"detail-btn\">\r\n                    <a-icon type=\"eye\" />\r\n                    查看详情\r\n                  </a-button>\r\n                </div>\r\n              </div>\r\n            </a-card>\r\n          </a-col>\r\n          <a-col :span=\"24\" v-if=\"dataSource.length === 0\">\r\n            <a-empty description=\"暂无可用试卷\" />\r\n          </a-col>\r\n        </a-row>\r\n      </a-spin>\r\n      \r\n      <!-- 分页 -->\r\n      <a-pagination\r\n        v-if=\"dataSource.length > 0\"\r\n        class=\"pagination\"\r\n        :current=\"ipagination.current\"\r\n        :pageSize=\"ipagination.pageSize\"\r\n        :total=\"ipagination.total\"\r\n        :pageSizeOptions=\"ipagination.pageSizeOptions\"\r\n        :showTotal=\"(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\r\n        :showQuickJumper=\"true\"\r\n        :showSizeChanger=\"true\"\r\n        @change=\"handlePageChange\"\r\n        @showSizeChange=\"handleSizeChange\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 考试说明模态框 -->\r\n    <a-modal\r\n      title=\"考试说明\"\r\n      :visible=\"examInfoVisible\"\r\n      :footer=\"null\"\r\n      :width=\"600\"\r\n      @cancel=\"examInfoVisible = false\"\r\n    >\r\n      <div class=\"exam-info\">\r\n        <h3>{{ currentPaper.title }}</h3>\r\n        <div class=\"exam-info-content\">\r\n          <div class=\"basic-info\">\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">科目：</span>\r\n              <span class=\"value\">{{ currentPaper.subject }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">级别：</span>\r\n              <span class=\"value\">{{ getLevelText(currentPaper.level) }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">考试时长：</span>\r\n              <span class=\"value\">{{ currentPaper.examDuration }}分钟</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"label\">通过标准：</span>\r\n              <span class=\"value\">试卷满分100分，得分<span class=\"score-highlight\">60分（含）</span>以上通过</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"exam-rules\">\r\n            <h4>考试须知</h4>\r\n            <div class=\"rules-list\">\r\n              <div class=\"rule-item\">\r\n                <i class=\"rule-icon\">⚠️</i>\r\n                <span>考试过程中请勿刷新或关闭页面，否则可能导致答案丢失</span>\r\n              </div>\r\n              <div class=\"rule-item\">\r\n                <i class=\"rule-icon\">⏰</i>\r\n                <span>请在规定时间内完成考试，超时系统将自动提交</span>\r\n              </div>\r\n              <div class=\"rule-item\">\r\n                <i class=\"rule-icon\">📝</i>\r\n                <span>开始考试后，计时将立即开始，请合理安排答题时间</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"exam-actions\">\r\n          <a-button type=\"primary\" size=\"large\" @click=\"confirmStartExam\">开始考试</a-button>\r\n          <a-button size=\"large\" @click=\"examInfoVisible = false\">取消</a-button>\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n\r\n    <!-- 试卷详情模态框 -->\r\n    <a-modal\r\n      title=\"试卷详情\"\r\n      :visible=\"detailModalVisible\"\r\n      :footer=\"null\"\r\n      :width=\"800\"\r\n      @cancel=\"detailModalVisible = false\"\r\n    >\r\n      <div v-if=\"currentPaperDetail\" class=\"paper-detail\">\r\n        <!-- 试卷基本信息 -->\r\n        <div class=\"detail-header\" :class=\"getSubjectClass(currentPaperDetail.subject)\">\r\n          <div class=\"header-background\"></div>\r\n          <div class=\"header-content\">\r\n            <div class=\"subject-icon-large\">\r\n              <a-icon :type=\"getSubjectIcon(currentPaperDetail.subject)\" />\r\n            </div>\r\n            <div class=\"header-info\">\r\n              <h2>{{ currentPaperDetail.title }}</h2>\r\n              <div class=\"detail-badges\">\r\n                <div class=\"badge-item subject-badge\">\r\n                  <a-icon type=\"book\" />\r\n                  <span>{{ currentPaperDetail.subject }}</span>\r\n                </div>\r\n                <div class=\"badge-item level-badge\">\r\n                  <a-icon type=\"trophy\" />\r\n                  <span>{{ currentPaperDetail.level }}</span>\r\n                </div>\r\n                <div class=\"badge-item year-badge\">\r\n                  <a-icon type=\"calendar\" />\r\n                  <span>{{ currentPaperDetail.year }}年</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 试卷详细信息 -->\r\n        <div class=\"detail-content\">\r\n          <a-row :gutter=\"24\">\r\n            <a-col :span=\"12\">\r\n              <div class=\"info-card\">\r\n                <h4><a-icon type=\"clock-circle\" /> 考试信息</h4>\r\n                <p><strong>考试时长：</strong>{{ currentPaperDetail.examDuration }}分钟</p>\r\n                <p><strong>难度等级：</strong>\r\n                  <a-rate :value=\"currentPaperDetail.difficulty\" :count=\"3\" disabled />\r\n                  {{ getDifficultyText(currentPaperDetail.difficulty) }}\r\n                </p>\r\n                <p><strong>试卷类型：</strong>{{ currentPaperDetail.type }}</p>\r\n              </div>\r\n            </a-col>\r\n            <a-col :span=\"12\">\r\n              <div class=\"info-card\">\r\n                <h4><a-icon type=\"file-text\" /> 题目构成</h4>\r\n                <p><strong>单选题：</strong>15题，每题2分，共30分</p>\r\n                <p><strong>判断题：</strong>10题，每题2分，共20分</p>\r\n                <p><strong>编程题：</strong>2题，每题25分，共50分</p>\r\n                <p><strong>总分：</strong>100分</p>\r\n              </div>\r\n            </a-col>\r\n          </a-row>\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExamList } from '@/api/examSystem'\r\nimport moment from 'moment'\r\nimport ExamTakingPage from './pages/ExamTakingPage.vue'\r\n\r\nexport default {\r\n  name: 'OnlineExam',\r\n  components: {\r\n    ExamTakingPage\r\n  },\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      queryParam: {\r\n        subject: '',\r\n        level: '',\r\n        type: '',\r\n        year: ''\r\n      },\r\n      // 数据源\r\n      dataSource: [],\r\n      // 分页参数\r\n      ipagination: {\r\n        current: 1,\r\n        pageSize: 12,\r\n        pageSizeOptions: ['12', '24', '36'],\r\n        showTotal: (total, range) => {\r\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\r\n        },\r\n        showQuickJumper: true,\r\n        showSizeChanger: true,\r\n        total: 0\r\n      },\r\n      // 加载状态\r\n      loading: false,\r\n      // 年份选项\r\n      yearOptions: [],\r\n      // 考试说明模态框显示状态\r\n      examInfoVisible: false,\r\n      // 当前选择的试卷\r\n      currentPaper: {},\r\n      // 试卷详情模态框显示状态\r\n      detailModalVisible: false,\r\n      // 当前试卷详情\r\n      currentPaperDetail: null,\r\n      // 考试界面显示状态\r\n      showExamPage: false\r\n    }\r\n  },\r\n  created() {\r\n    // 生成年份选项（当前年份往前推5年）\r\n    const currentYear = moment().year()\r\n    for (let i = 0; i < 5; i++) {\r\n      this.yearOptions.push(currentYear - i)\r\n    }\r\n    this.loadData()\r\n  },\r\n\r\n  // ✅ 组件激活时（从keep-alive缓存中恢复）\r\n  activated() {\r\n    console.log('在线考试组件被激活，当前状态：', {\r\n      showExamPage: this.showExamPage,\r\n      currentPaper: (this.currentPaper && this.currentPaper.title) || '无',\r\n      dataSourceLength: this.dataSource.length\r\n    })\r\n\r\n    // 🔧 修复：移除自动刷新逻辑，保持用户的筛选状态\r\n    // 注释掉自动刷新，让用户手动刷新或通过查询按钮刷新\r\n    // if (!this.showExamPage) {\r\n    //   this.loadData()\r\n    // }\r\n\r\n    // 如果需要刷新数据，用户可以：\r\n    // 1. 点击\"查询\"按钮手动刷新\r\n    // 2. 修改筛选条件触发自动查询\r\n    // 这样可以保持用户设置的筛选条件状态\r\n  },\r\n\r\n  // ✅ 组件失活时（被keep-alive缓存）\r\n  deactivated() {\r\n    console.log('在线考试组件被缓存，保存状态')\r\n\r\n    // 在线考试组件主要保持以下状态：\r\n    // 1. 筛选条件 (queryParam)\r\n    // 2. 分页状态 (ipagination)\r\n    // 3. 考试界面状态 (showExamPage, currentPaper)\r\n    // 这些状态会被keep-alive自动保持，无需额外处理\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1\r\n      }\r\n      this.loading = true\r\n      const params = Object.assign({}, this.queryParam)\r\n      params.pageNo = this.ipagination.current\r\n      params.pageSize = this.ipagination.pageSize\r\n      \r\n      getExamList(params).then(res => {\r\n        if (res.success) {\r\n          console.log('获取试卷列表成功:', res.result)\r\n          this.dataSource = res.result.records || []\r\n          this.ipagination.total = res.result.total || 0\r\n          console.log('试卷数据:', this.dataSource)\r\n          // 检查每个试卷的ID\r\n          this.dataSource.forEach((paper, index) => {\r\n            console.log(`试卷${index}:`, paper)\r\n            console.log(`试卷${index} ID:`, paper.id)\r\n          })\r\n        } else {\r\n          this.$message.error(res.message)\r\n        }\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 重置搜索\r\n    searchReset() {\r\n      this.queryParam = {\r\n        subject: '',\r\n        level: '',\r\n        type: '',\r\n        year: ''\r\n      }\r\n      this.loadData(1)\r\n    },\r\n    // 页码变化\r\n    handlePageChange(page) {\r\n      this.ipagination.current = page\r\n      this.loadData()\r\n    },\r\n    // 每页条数变化\r\n    handleSizeChange(current, size) {\r\n      this.ipagination.current = current\r\n      this.ipagination.pageSize = size\r\n      this.loadData()\r\n    },\r\n    // 获取级别文本（现在数据库直接存储中文，无需转换）\r\n    getLevelText(level) {\r\n      return level || '未知级别'\r\n    },\r\n\r\n    // 获取科目图标\r\n    getSubjectIcon(subject) {\r\n      const iconMap = {\r\n        'Scratch': 'build',\r\n        'Python': 'code',\r\n        'C++': 'laptop'\r\n      }\r\n      return iconMap[subject] || 'file'\r\n    },\r\n\r\n    // 获取科目样式类\r\n    getSubjectClass(subject) {\r\n      const classMap = {\r\n        'Scratch': 'scratch-theme',\r\n        'Python': 'python-theme',\r\n        'C++': 'cpp-theme'\r\n      }\r\n      return classMap[subject] || 'default-theme'\r\n    },\r\n\r\n    // 获取科目颜色\r\n    getSubjectColor(subject) {\r\n      const colorMap = {\r\n        'Scratch': 'orange',\r\n        'Python': 'green',\r\n        'C++': 'blue'\r\n      }\r\n      return colorMap[subject] || 'default'\r\n    },\r\n\r\n    // 获取难度文本\r\n    getDifficultyText(difficulty) {\r\n      const difficultyMap = {\r\n        1: '简单',\r\n        2: '中等',\r\n        3: '困难'\r\n      }\r\n      return difficultyMap[difficulty] || '未知'\r\n    },\r\n    // 处理开始考试\r\n    handleStartExam(paper) {\r\n      console.log('开始考试，试卷数据:', paper)\r\n      console.log('试卷ID:', paper.id)\r\n      this.currentPaper = paper\r\n      this.examInfoVisible = true\r\n    },\r\n    // 确认开始考试\r\n    confirmStartExam() {\r\n      console.log('确认开始考试，当前试卷:', this.currentPaper)\r\n      console.log('传递给ExamTakingPage的paperId:', this.currentPaper.id)\r\n      this.examInfoVisible = false\r\n      // 显示考试界面\r\n      this.showExamPage = true\r\n    },\r\n    // 返回试卷列表\r\n    backToExamList() {\r\n      this.showExamPage = false\r\n      this.currentPaper = {}\r\n    },\r\n    // 处理考试完成\r\n    handleExamCompleted(examData) {\r\n      console.log('考试完成：', examData)\r\n      this.$message.success('考试已完成！')\r\n      // 返回列表页面\r\n      this.backToExamList()\r\n    },\r\n    // 查看详情\r\n    handleViewDetail(paper) {\r\n      this.currentPaperDetail = paper\r\n      this.detailModalVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.card-list {\r\n  margin-top: 12px;\r\n}\r\n\r\n.exam-card {\r\n  min-height: 270px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .ant-card-body {\r\n    padding: 0 !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  // 覆盖 Ant Design 卡片的默认内边距\r\n  /deep/ .ant-card-body {\r\n    padding: 0 !important;\r\n  }\r\n\r\n  // 卡片头部彩带\r\n  .card-header {\r\n    height: 80px;\r\n    padding: 16px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    color: white;\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);\r\n    }\r\n\r\n    .subject-icon {\r\n      font-size: 24px;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    .subject-info {\r\n      flex: 1;\r\n\r\n      .subject-name {\r\n        display: block;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .level-badge {\r\n        display: inline-block;\r\n        background: rgba(255, 255, 255, 0.2);\r\n        padding: 2px 8px;\r\n        border-radius: 12px;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n\r\n    .year-badge {\r\n      background: rgba(255, 255, 255, 0.2);\r\n      padding: 4px 12px;\r\n      border-radius: 16px;\r\n      font-size: 14px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  // 科目主题色\r\n  .scratch-theme {\r\n    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\r\n  }\r\n\r\n  .python-theme {\r\n    background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);\r\n  }\r\n\r\n  .cpp-theme {\r\n    background: linear-gradient(135deg, #2196f3 0%, #03a9f4 100%);\r\n  }\r\n\r\n  .default-theme {\r\n    background: linear-gradient(135deg, #9c27b0 0%, #e91e63 100%);\r\n  }\r\n\r\n  // 卡片内容\r\n  .card-content {\r\n    flex: 1;\r\n    padding: 12px 16px 0px 12px;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 3px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n    min-height: 44px;\r\n  }\r\n\r\n  .card-info {\r\n    margin-bottom: 10px;\r\n\r\n    .info-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 4px;\r\n      color: #666;\r\n      font-size: 14px;\r\n\r\n      .info-label {\r\n        color: #1890ff;\r\n        font-weight: 500;\r\n        margin-right: 4px;\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 8px;\r\n        color: #1890ff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 12px;\r\n\r\n    .start-btn {\r\n      width: 110px;\r\n      height: 36px;\r\n      border-radius: 18px;\r\n      font-weight: bold;\r\n      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\r\n      border: none;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      line-height: 1;\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\r\n        transform: translateY(-1px);\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 4px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n\r\n      span {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n    }\r\n\r\n    .detail-btn {\r\n      width: 110px;\r\n      height: 36px;\r\n      border-radius: 18px;\r\n      border: 1px solid #d9d9d9;\r\n      color: #666;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      line-height: 1;\r\n\r\n      &:hover {\r\n        border-color: #1890ff;\r\n        color: #1890ff;\r\n      }\r\n\r\n      .anticon {\r\n        margin-right: 4px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n\r\n      span {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        vertical-align: middle;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.label {\r\n  font-weight: bold;\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n\r\n\r\n// 试卷详情模态框样式\r\n.paper-detail {\r\n  .detail-header {\r\n    position: relative;\r\n    border-radius: 16px;\r\n    margin-bottom: 24px;\r\n    overflow: hidden;\r\n    min-height: 120px;\r\n\r\n    .header-background {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n      z-index: 1;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: linear-gradient(45deg, rgba(255,255,255,0.08) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.08) 75%);\r\n        background-size: 30px 30px;\r\n        animation: move 25s linear infinite;\r\n      }\r\n    }\r\n\r\n    .header-content {\r\n      position: relative;\r\n      z-index: 2;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 24px;\r\n      color: white;\r\n\r\n      .subject-icon-large {\r\n        width: 80px;\r\n        height: 80px;\r\n        background: linear-gradient(135deg, #5dade2 0%, #3498db 100%);\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 32px;\r\n        margin-right: 20px;\r\n        color: #fff;\r\n        box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);\r\n        border: 3px solid rgba(255, 255, 255, 0.9);\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: -50%;\r\n          left: -50%;\r\n          width: 200%;\r\n          height: 200%;\r\n          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);\r\n          animation: shine 4s ease-in-out infinite;\r\n        }\r\n      }\r\n\r\n      .header-info {\r\n        flex: 1;\r\n\r\n        h2 {\r\n          margin: 0 0 16px 0;\r\n          font-size: 24px;\r\n          font-weight: bold;\r\n          color: #fff;\r\n          text-shadow: 0 2px 8px rgba(74, 144, 226, 0.5);\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .detail-badges {\r\n          display: flex;\r\n          gap: 12px;\r\n          flex-wrap: wrap;\r\n\r\n          .badge-item {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 8px 16px;\r\n            border-radius: 20px;\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            background: rgba(255, 255, 255, 0.25);\r\n            color: white;\r\n            backdrop-filter: blur(10px);\r\n            border: 1px solid rgba(255, 255, 255, 0.3);\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.35);\r\n              transform: translateY(-2px);\r\n            }\r\n\r\n            .anticon {\r\n              margin-right: 6px;\r\n              font-size: 14px;\r\n            }\r\n\r\n            span {\r\n              text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .detail-content {\r\n    .info-card {\r\n      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);\r\n      border: 1px solid #e6f2ff;\r\n      border-radius: 12px;\r\n      padding: 24px;\r\n      margin-bottom: 20px;\r\n      box-shadow: 0 4px 16px rgba(74, 144, 226, 0.08);\r\n      transition: all 0.3s ease;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        height: 3px;\r\n        background: linear-gradient(90deg, #4a90e2 0%, #357abd 100%);\r\n        border-radius: 12px 12px 0 0;\r\n      }\r\n\r\n      h4 {\r\n        color: #4a90e2;\r\n        margin-bottom: 20px;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .anticon {\r\n          margin-right: 10px;\r\n          font-size: 20px;\r\n          color: #4a90e2;\r\n        }\r\n      }\r\n\r\n      p {\r\n        margin-bottom: 12px;\r\n        color: #555;\r\n        font-size: 15px;\r\n        line-height: 1.6;\r\n\r\n        strong {\r\n          color: #2c3e50;\r\n          margin-right: 8px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        .ant-rate {\r\n          margin-right: 8px;\r\n          font-size: 16px;\r\n\r\n          .ant-rate-star {\r\n            color: #f39c12;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.label {\r\n  font-weight: bold;\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.exam-info {\r\n  h3 {\r\n    font-size: 20px;\r\n    text-align: center;\r\n    margin-bottom: 24px;\r\n    color: #4a90e2;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .exam-info-content {\r\n    margin-bottom: 32px;\r\n\r\n    .basic-info {\r\n      background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);\r\n      border: 1px solid #d6e9f7;\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      margin-bottom: 24px;\r\n\r\n      .info-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .label {\r\n          font-weight: 600;\r\n          color: #4a90e2;\r\n          min-width: 80px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        .value {\r\n          color: #2c3e50;\r\n          flex: 1;\r\n\r\n          .score-highlight {\r\n            color: #ff4d4f;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .exam-rules {\r\n      h4 {\r\n        color: #4a90e2;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        margin-bottom: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        &::before {\r\n          content: '';\r\n          width: 4px;\r\n          height: 16px;\r\n          background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n          border-radius: 2px;\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .rules-list {\r\n        .rule-item {\r\n          display: flex;\r\n          align-items: flex-start;\r\n          padding: 6px 16px;\r\n          background: #fff;\r\n          transition: all 0.3s ease;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .rule-icon {\r\n            font-size: 16px;\r\n            margin-right: 12px;\r\n            margin-top: 2px;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          span {\r\n            color: #555;\r\n            line-height: 1.5;\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .exam-actions {\r\n    text-align: center;\r\n    padding-top: 16px;\r\n\r\n    button {\r\n      margin: 0 8px;\r\n      height: 40px;\r\n      padding: 0 24px;\r\n      border-radius: 6px;\r\n      font-weight: 500;\r\n\r\n      &.ant-btn-primary {\r\n        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\r\n        border: none;\r\n        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);\r\n\r\n        &:hover {\r\n          background: linear-gradient(135deg, #357abd 0%, #2c5aa0 100%);\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);\r\n        }\r\n      }\r\n\r\n      &:not(.ant-btn-primary) {\r\n        color: #666;\r\n        border-color: #d9d9d9;\r\n\r\n        &:hover {\r\n          color: #4a90e2;\r\n          border-color: #4a90e2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes move {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 40px 40px;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n  50% {\r\n    transform: translateX(100%) translateY(100%) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n}\r\n\r\n@keyframes rainbow {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes move {\r\n  0% {\r\n    background-position: 0 0;\r\n  }\r\n  100% {\r\n    background-position: 60px 60px;\r\n  }\r\n}\r\n\r\n@keyframes shine {\r\n  0% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n  50% {\r\n    transform: translateX(100%) translateY(100%) rotate(45deg);\r\n  }\r\n  100% {\r\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\r\n  }\r\n}\r\n</style>"]}]}