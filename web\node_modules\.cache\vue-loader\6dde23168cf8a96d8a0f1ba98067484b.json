{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\DefaultTable.vue?vue&type=template&id=2fa9f269&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\DefaultTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n<div>\n  <a-button @click=\"handleTableCheck\" type=\"primary\">表单验证</a-button>\n  <span style=\"padding-left:8px;\"></span>\n  <a-tooltip placement=\"top\" title=\"获取值，忽略表单验证\" :autoAdjustOverflow=\"true\">\n    <a-button @click=\"handleTableGet\" type=\"primary\">获取值</a-button>\n  </a-tooltip>\n  <span style=\"padding-left:8px;\"></span>\n  <a-tooltip placement=\"top\" title=\"模拟加载1000条数据\" :autoAdjustOverflow=\"true\">\n    <a-button @click=\"handleTableSet\" type=\"primary\">设置值</a-button>\n  </a-tooltip>\n\n\n  <j-editable-table\n    ref=\"editableTable\"\n    :loading=\"loading\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :rowNumber=\"true\"\n    :rowSelection=\"true\"\n    :actionButton=\"true\"\n    :dragSort=\"true\"\n    style=\"margin-top: 8px;\"\n    @selectRowChange=\"handleSelectRowChange\">\n\n    <template v-slot:action=\"props\">\n      <a @click=\"handleDelete(props)\">删除</a>\n    </template>\n\n  </j-editable-table>\n</div>\n\n", null]}