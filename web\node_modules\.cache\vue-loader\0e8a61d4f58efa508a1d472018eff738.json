{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ArchivesStatisticst.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ArchivesStatisticst.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import Bar from '@/components/chart/Bar'\n  import Pie from '@/components/chart/Pie'\n  import ACol from 'ant-design-vue/es/grid/Col'\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'ArchivesStatisticst',\n    components: {\n      ACol,\n      Bar,\n      Pie\n    },\n    data() {\n      return {\n        description: '档案统计页面',\n        // 查询条件\n        queryParam: {},\n        // 数据集\n        countSource: [],\n        // 柱状图\n        barType: 'year',\n        barDate: ['month', 'month'],\n        barValue: [],\n        // 饼状图\n        pieType: 'year',\n        pieDate: ['month', 'month'],\n        pieValue: [],\n        // 统计图类型\n        tabStatus:\"bar\",\n        url: {\n          getYearCountInfo: \"/api/report/getYearCountInfo\",\n          getMonthCountInfo:\"/api/report/getMonthCountInfo\",\n          getCntrNoCountInfo:\"/api/report/getCntrNoCountInfo\",\n          getCabinetCountInfo:\"/api/report/getCabinetCountInfo\",\n        },\n      }\n    },\n    created() {\n      let url = this.url.getYearCountInfo;\n      this.loadDate(url,'year',{});\n    },\n    methods: {\n      loadDate(url,type,param) {\n        getAction(url,param,'get').then((res) => {\n          if (res.success) {\n            this.countSource = [];\n            if(type === 'year'){\n              this.getYearCountSource(res.result);\n            }\n            if(type === 'month'){\n              this.getMonthCountSource(res.result);\n            }\n            if(type === 'category'){\n              this.getCategoryCountSource(res.result);\n            }\n            if(type === 'cabinet'){\n              this.getCabinetCountSource(res.result);\n            }\n          }else{\n            var that=this;\n            that.$message.warning(res.message);\n          }\n        })\n      },\n      getYearCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: `${data[i].year}年`,\n              y: data[i].yearcount\n            })\n          }else{\n            this.countSource.push({\n              item: `${data[i].year}年`,\n              count:data[i].yearcount\n            })\n          }\n        }\n      },\n      getMonthCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: data[i].month,\n              y: data[i].monthcount\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].month,\n              count:data[i].monthcount\n            })\n          }\n        }\n      },\n      getCategoryCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus ===\"bar\"){\n            this.countSource.push({\n              x: data[i].classifyname,\n              y: data[i].cntrnocount\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].classifyname,\n              count:data[i].cntrnocount\n            })\n          }\n        }\n      },\n      getCabinetCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: data[i].cabinetname,\n              y: data[i].cabinetcocunt\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].cabinetname,\n              count:data[i].cabinetcocunt\n            })\n          }\n        }\n      },\n      // 选择统计图类别\n      callback(key) {\n        if(key === \"1\"){\n          this.tabStatus = \"bar\";\n          this.queryDatebar();\n        }else{\n          this.tabStatus = \"pie\";\n          this.queryDatepie();\n        }\n      },\n      // 选择统计类别\n      statisticst(e) {\n        if(this.tabStatus === \"pie\"){\n          this.pieType = e.target.value;\n          this.queryDatepie();\n        }else{\n          this.barType = e.target.value;\n          this.queryDatebar();\n        }\n      },\n      // 按月份查询\n      queryDatebar(){\n        if(this.barValue.length>0){\n          this.getUrl(this.barType,{startTime:this.barValue[0]._d,endTime:this.barValue[1]._d});\n        }else{\n          this.getUrl(this.barType,{});\n        }\n      },\n      queryDatepie(){\n        if(this.pieValue.length>0){\n          this.getUrl(this.pieType,{startTime:this.pieValue[0]._d,endTime:this.pieValue[1]._d});\n        }else{\n          this.getUrl(this.pieType,{});\n        }\n      },\n      searchReset(){\n        console.log(this.tabStatus);\n        if(this.tabStatus === \"pie\"){\n          this.pieValue = [];\n        }else{\n          this.barValue = [];\n        }\n        this.getUrl(this.barType,{});\n      },\n      // 选择请求url\n      getUrl(type,param){\n        let url = \"\";\n        if(type === 'year'){\n          url = this.url.getYearCountInfo;\n        }\n        if(type === 'month'){\n          url = this.url.getMonthCountInfo;\n        }\n        if(type === 'category'){\n          url = this.url.getCntrNoCountInfo;\n        }\n        if(type === 'cabinet'){\n          url = this.url.getCabinetCountInfo;\n        }\n        this.loadDate(url,type,param);\n      },\n      // 选择月份日期\n      handleBarDate(value, mode) {\n        this.barValue = value\n        this.barDate = [\n          mode[0] === 'date' ? 'month' : mode[0],\n          mode[1] === 'date' ? 'month' : mode[1]\n        ]\n      },\n      handlePieDate(value, mode) {\n        this.pieValue = value\n        this.pieDate = [\n          mode[0] === 'date' ? 'month' : mode[0],\n          mode[1] === 'date' ? 'month' : mode[1]\n        ]\n      },\n    }\n  }\n", {"version": 3, "sources": ["ArchivesStatisticst.vue"], "names": [], "mappings": ";AAiEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ArchivesStatisticst.vue", "sourceRoot": "src/views/jeecg/report", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-tabs defaultActiveKey=\"1\" @change=\"callback\">\n      <a-tab-pane tab=\"柱状图\" key=\"1\">\n        <a-row>\n          <a-col :span=\"10\">\n            <a-radio-group :value=\"barType\" @change=\"statisticst\">\n              <a-radio-button value=\"year\">按年统计</a-radio-button>\n              <a-radio-button value=\"month\">按月统计</a-radio-button>\n              <a-radio-button value=\"category\">按类别统计</a-radio-button>\n              <a-radio-button value=\"cabinet\">按柜号统计</a-radio-button>\n            </a-radio-group>\n          </a-col>\n          <a-col :span=\"14\">\n            <a-form v-if=\"barType === 'month' && false\" layout=\"inline\" style=\"margin-top: -4px\">\n              <a-form-item label=\"月份区间\">\n                <a-range-picker\n                  :placeholder=\"['开始月份', '结束月份']\"\n                  format=\"YYYY-MM\"\n                  :value=\"barValue\"\n                  :mode=\"barDate\"\n                  @panelChange=\"handleBarDate\"/>\n              </a-form-item>\n              <a-button style=\"margin-top: 2px\" type=\"primary\" icon=\"search\" @click=\"queryDatebar\">查询</a-button>\n              <a-button style=\"margin-top: 2px;margin-left: 8px\" type=\"primary\" icon=\"reload\" @click=\"searchReset\">重置</a-button>\n            </a-form>\n          </a-col>\n          <bar class=\"statistic\" title=\"档案统计\" :dataSource=\"countSource\" :height=\"400\"/>\n        </a-row>\n      </a-tab-pane>\n\n      <a-tab-pane tab=\"饼状图\" key=\"2\">\n        <a-row :gutter=\"24\">\n          <a-col :span=\"10\">\n            <a-radio-group :value=\"pieType\" @change=\"statisticst\">\n              <a-radio-button value=\"year\">按年统计</a-radio-button>\n              <a-radio-button value=\"month\">按月统计</a-radio-button>\n              <a-radio-button value=\"category\">按类别统计</a-radio-button>\n              <a-radio-button value=\"cabinet\">按柜号统计</a-radio-button>\n            </a-radio-group>\n          </a-col>\n          <a-col :span=\"14\">\n            <a-form v-if=\"pieType === 'month' && false\" layout=\"inline\" style=\"margin-top: -4px\">\n              <a-row :gutter=\"24\">\n                <a-form-item label=\"月份区间\">\n                  <a-range-picker\n                    :placeholder=\"['开始月份', '结束月份']\"\n                    format=\"YYYY-MM\"\n                    :value=\"pieValue\"\n                    :mode=\"pieDate\"\n                    @panelChange=\"handlePieDate\"/>\n                </a-form-item>\n                <a-button style=\"margin-top: 2px\" type=\"primary\" icon=\"search\" @click=\"queryDatepie\">查询</a-button>\n                <a-button style=\"margin-top: 2px;margin-left: 8px\" type=\"primary\" icon=\"reload\" @click=\"searchReset\">重置</a-button>\n              </a-row>\n            </a-form>\n          </a-col>\n          <pie class=\"statistic\" title=\"档案统计\" :dataSource=\"countSource\" :height=\"450\"/>\n        </a-row>\n      </a-tab-pane>\n    </a-tabs>\n  </a-card>\n</template>\n\n<script>\n  import Bar from '@/components/chart/Bar'\n  import Pie from '@/components/chart/Pie'\n  import ACol from 'ant-design-vue/es/grid/Col'\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'ArchivesStatisticst',\n    components: {\n      ACol,\n      Bar,\n      Pie\n    },\n    data() {\n      return {\n        description: '档案统计页面',\n        // 查询条件\n        queryParam: {},\n        // 数据集\n        countSource: [],\n        // 柱状图\n        barType: 'year',\n        barDate: ['month', 'month'],\n        barValue: [],\n        // 饼状图\n        pieType: 'year',\n        pieDate: ['month', 'month'],\n        pieValue: [],\n        // 统计图类型\n        tabStatus:\"bar\",\n        url: {\n          getYearCountInfo: \"/api/report/getYearCountInfo\",\n          getMonthCountInfo:\"/api/report/getMonthCountInfo\",\n          getCntrNoCountInfo:\"/api/report/getCntrNoCountInfo\",\n          getCabinetCountInfo:\"/api/report/getCabinetCountInfo\",\n        },\n      }\n    },\n    created() {\n      let url = this.url.getYearCountInfo;\n      this.loadDate(url,'year',{});\n    },\n    methods: {\n      loadDate(url,type,param) {\n        getAction(url,param,'get').then((res) => {\n          if (res.success) {\n            this.countSource = [];\n            if(type === 'year'){\n              this.getYearCountSource(res.result);\n            }\n            if(type === 'month'){\n              this.getMonthCountSource(res.result);\n            }\n            if(type === 'category'){\n              this.getCategoryCountSource(res.result);\n            }\n            if(type === 'cabinet'){\n              this.getCabinetCountSource(res.result);\n            }\n          }else{\n            var that=this;\n            that.$message.warning(res.message);\n          }\n        })\n      },\n      getYearCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: `${data[i].year}年`,\n              y: data[i].yearcount\n            })\n          }else{\n            this.countSource.push({\n              item: `${data[i].year}年`,\n              count:data[i].yearcount\n            })\n          }\n        }\n      },\n      getMonthCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: data[i].month,\n              y: data[i].monthcount\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].month,\n              count:data[i].monthcount\n            })\n          }\n        }\n      },\n      getCategoryCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus ===\"bar\"){\n            this.countSource.push({\n              x: data[i].classifyname,\n              y: data[i].cntrnocount\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].classifyname,\n              count:data[i].cntrnocount\n            })\n          }\n        }\n      },\n      getCabinetCountSource(data){\n        for (let i = 0; i < data.length; i++) {\n          if(this.tabStatus === \"bar\"){\n            this.countSource.push({\n              x: data[i].cabinetname,\n              y: data[i].cabinetcocunt\n            })\n          }else{\n            this.countSource.push({\n              item: data[i].cabinetname,\n              count:data[i].cabinetcocunt\n            })\n          }\n        }\n      },\n      // 选择统计图类别\n      callback(key) {\n        if(key === \"1\"){\n          this.tabStatus = \"bar\";\n          this.queryDatebar();\n        }else{\n          this.tabStatus = \"pie\";\n          this.queryDatepie();\n        }\n      },\n      // 选择统计类别\n      statisticst(e) {\n        if(this.tabStatus === \"pie\"){\n          this.pieType = e.target.value;\n          this.queryDatepie();\n        }else{\n          this.barType = e.target.value;\n          this.queryDatebar();\n        }\n      },\n      // 按月份查询\n      queryDatebar(){\n        if(this.barValue.length>0){\n          this.getUrl(this.barType,{startTime:this.barValue[0]._d,endTime:this.barValue[1]._d});\n        }else{\n          this.getUrl(this.barType,{});\n        }\n      },\n      queryDatepie(){\n        if(this.pieValue.length>0){\n          this.getUrl(this.pieType,{startTime:this.pieValue[0]._d,endTime:this.pieValue[1]._d});\n        }else{\n          this.getUrl(this.pieType,{});\n        }\n      },\n      searchReset(){\n        console.log(this.tabStatus);\n        if(this.tabStatus === \"pie\"){\n          this.pieValue = [];\n        }else{\n          this.barValue = [];\n        }\n        this.getUrl(this.barType,{});\n      },\n      // 选择请求url\n      getUrl(type,param){\n        let url = \"\";\n        if(type === 'year'){\n          url = this.url.getYearCountInfo;\n        }\n        if(type === 'month'){\n          url = this.url.getMonthCountInfo;\n        }\n        if(type === 'category'){\n          url = this.url.getCntrNoCountInfo;\n        }\n        if(type === 'cabinet'){\n          url = this.url.getCabinetCountInfo;\n        }\n        this.loadDate(url,type,param);\n      },\n      // 选择月份日期\n      handleBarDate(value, mode) {\n        this.barValue = value\n        this.barDate = [\n          mode[0] === 'date' ? 'month' : mode[0],\n          mode[1] === 'date' ? 'month' : mode[1]\n        ]\n      },\n      handlePieDate(value, mode) {\n        this.pieValue = value\n        this.pieDate = [\n          mode[0] === 'date' ? 'month' : mode[0],\n          mode[1] === 'date' ? 'month' : mode[1]\n        ]\n      },\n    }\n  }\n</script>\n<style scoped>\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n\n  .statistic {\n    padding: 0px !important;\n    margin-top: 50px;\n  }\n\n  .statistic h4 {\n    margin-bottom: 20px;\n    text-align: center !important;\n    font-size: 24px !important;;\n  }\n\n  .statistic #canvas_1 {\n    width: 100% !important;\n  }\n</style>"]}]}