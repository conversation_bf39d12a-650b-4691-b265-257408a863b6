{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue?vue&type=template&id=1773ddaa", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm.tips !== \"\" ? _c(\"tooltip\", [_c(\"template\", {\n    slot: \"title\"\n  }, [_vm._v(_vm._s(_vm.tips))]), _c(\"avatar\", {\n    attrs: {\n      size: _vm.avatarSize,\n      src: _vm.src\n    }\n  })], 2) : _c(\"avatar\", {\n    attrs: {\n      size: _vm.avatarSize,\n      src: _vm.src\n    }\n  });\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "tips", "slot", "_v", "_s", "attrs", "size", "avatarSize", "src", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/AvatarList/Item.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.tips !== \"\"\n    ? _c(\n        \"tooltip\",\n        [\n          _c(\"template\", { slot: \"title\" }, [_vm._v(_vm._s(_vm.tips))]),\n          _c(\"avatar\", { attrs: { size: _vm.avatarSize, src: _vm.src } }),\n        ],\n        2\n      )\n    : _c(\"avatar\", { attrs: { size: _vm.avatarSize, src: _vm.src } })\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,IAAI,KAAK,EAAE,GAClBF,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CAAC,UAAU,EAAE;IAAEG,IAAI,EAAE;EAAQ,CAAC,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,EAC7DF,EAAE,CAAC,QAAQ,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAER,GAAG,CAACS,UAAU;MAAEC,GAAG,EAAEV,GAAG,CAACU;IAAI;EAAE,CAAC,CAAC,CAChE,EACD,CACF,CAAC,GACDT,EAAE,CAAC,QAAQ,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAER,GAAG,CAACS,UAAU;MAAEC,GAAG,EAAEV,GAAG,CAACU;IAAI;EAAE,CAAC,CAAC;AACrE,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}]}