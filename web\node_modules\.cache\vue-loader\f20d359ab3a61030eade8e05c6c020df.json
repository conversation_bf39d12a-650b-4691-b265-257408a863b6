{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue?vue&type=style&index=0&id=2de1d165&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.password-setting {\n  max-width: 600px;\n}\n\n.password-form {\n  padding: 20px 0;\n}\n\n.password-input {\n  max-width: 300px;\n}\n\n.button-group {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  max-width: 300px;\n}\n\n.button-group .ant-btn {\n  min-width: 80px;\n}\n", {"version": 3, "sources": ["PasswordSetting.vue"], "names": [], "mappings": ";AAiKA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PasswordSetting.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <div class=\"password-setting\">\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\" class=\"password-form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"旧密码\">\n          <a-input\n            type=\"password\"\n            placeholder=\"请输入旧密码\"\n            v-decorator=\"[ 'oldpassword', validatorRules.oldpassword]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"新密码\">\n          <a-input\n            type=\"password\"\n            placeholder=\"请输入新密码\"\n            v-decorator=\"[ 'password', validatorRules.password]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"确认新密码\">\n          <a-input\n            type=\"password\"\n            @blur=\"handleConfirmBlur\"\n            placeholder=\"请确认新密码\"\n            v-decorator=\"[ 'confirmpassword', validatorRules.confirmpassword]\"\n            class=\"password-input\" />\n        </a-form-item>\n\n        <a-form-item :wrapperCol=\"{ offset: 5, span: 12 }\">\n          <div class=\"button-group\">\n            <a-button type=\"primary\" @click=\"handleSubmit\" :loading=\"confirmLoading\">\n              提交\n            </a-button>\n            <a-button @click=\"handleReset\" style=\"margin-left: 8px;\">\n              重置\n            </a-button>\n          </div>\n        </a-form-item>\n\n      </a-form>\n    </a-spin>\n  </div>\n</template>\n\n<script>\nimport { putAction } from '@/api/manage'\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: \"PasswordSetting\",\n  data () {\n    return {\n      confirmLoading: false,\n      validatorRules:{\n        oldpassword:{\n          rules: [{\n            required: true, message: '请输入旧密码!',\n          }],\n        },\n        password:{\n          rules: [{\n            required: true, message: '请输入新密码!',\n          }, {\n            validator: this.validateToNextPassword,\n          }],\n        },\n        confirmpassword:{\n          rules: [{\n            required: true, message: '请确认新密码!',\n          }, {\n            validator: this.compareToFirstPassword,\n          }],\n        }\n      },\n      confirmDirty:false,\n      labelCol: {\n        xs: { span: 24 },\n        sm: { span: 5 },\n      },\n      wrapperCol: {\n        xs: { span: 24 },\n        sm: { span: 12 },\n      },\n\n      form:this.$form.createForm(this),\n      url: \"sys/user/updatePassword\",\n      username:\"\",\n    }\n  },\n  created() {\n    // 获取当前用户名\n    let userInfo = this.userInfo()\n    if (userInfo && userInfo.username) {\n      this.username = userInfo.username\n    }\n  },\n  methods: {\n    ...mapGetters(['userInfo']),\n    handleSubmit () {\n      const that = this;\n      // 触发表单验证\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          that.confirmLoading = true;\n          let params = Object.assign({username:this.username},values)\n          console.log(\"修改密码提交数据\",params)\n          putAction(this.url,params).then((res)=>{\n            if(res.success){\n              console.log(res)\n              that.$message.success(res.message);\n              // 清空表单\n              that.form.resetFields();\n            }else{\n              that.$message.warning(res.message);\n            }\n          }).finally(() => {\n            that.confirmLoading = false;\n          })\n        }\n      })\n    },\n    handleReset() {\n      // 重置表单\n      this.form.resetFields();\n      this.$message.info('表单已重置');\n    },\n    validateToNextPassword  (rule, value, callback) {\n      const form = this.form;\n      if (value && this.confirmDirty) {\n        form.validateFields(['confirm'], { force: true })\n      }\n      callback();\n    },\n    compareToFirstPassword  (rule, value, callback) {\n      const form = this.form;\n      if (value && value !== form.getFieldValue('password')) {\n        callback('两次输入的密码不一样！');\n      } else {\n        callback()\n      }\n    },\n    handleConfirmBlur  (e) {\n      const value = e.target.value\n      this.confirmDirty = this.confirmDirty || !!value\n    }\n  }\n}\n</script>\n\n<style scoped>\n.password-setting {\n  max-width: 600px;\n}\n\n.password-form {\n  padding: 20px 0;\n}\n\n.password-input {\n  max-width: 300px;\n}\n\n.button-group {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  max-width: 300px;\n}\n\n.button-group .ant-btn {\n  min-width: 80px;\n}\n</style>\n"]}]}