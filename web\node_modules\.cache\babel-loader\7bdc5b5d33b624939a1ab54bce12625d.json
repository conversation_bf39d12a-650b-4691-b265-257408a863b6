{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import { VueCropper } from 'vue-cropper';\nexport default {\n  components: {\n    VueCropper: VueCropper\n  },\n  data: function data() {\n    return {\n      visible: false,\n      id: null,\n      confirmLoading: false,\n      options: {\n        img: '/avatar2.jpg',\n        autoCrop: true,\n        autoCropWidth: 200,\n        autoCropHeight: 200,\n        fixedBox: true\n      },\n      previews: {}\n    };\n  },\n  methods: {\n    edit: function edit(id) {\n      this.visible = true;\n      this.id = id;\n      /* 获取原始头像 */\n    },\n    close: function close() {\n      this.id = null;\n      this.visible = false;\n    },\n    cancelHandel: function cancelHandel() {\n      this.close();\n    },\n    okHandel: function okHandel() {\n      var vm = this;\n      vm.confirmLoading = true;\n      setTimeout(function () {\n        vm.confirmLoading = false;\n        vm.close();\n        vm.$message.success('上传头像成功');\n      }, 2000);\n    },\n    realTime: function realTime(data) {\n      this.previews = data;\n    }\n  }\n};", {"version": 3, "names": ["VueCropper", "components", "data", "visible", "id", "confirmLoading", "options", "img", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "previews", "methods", "edit", "close", "cancelHandel", "okHandel", "vm", "setTimeout", "$message", "success", "realTime"], "sources": ["src/views/account/settings/AvatarModal.vue"], "sourcesContent": ["<template>\n  <a-modal :visible=\"visible\" title=\"修改头像\" :maskClosable=\"false\" :confirmLoading=\"confirmLoading\" :width=\"800\">\n    <a-row>\n      <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n        <vue-cropper\n          ref=\"cropper\"\n          :img=\"options.img\"\n          :info=\"true\"\n          :autoCrop=\"options.autoCrop\"\n          :autoCropWidth=\"options.autoCropWidth\"\n          :autoCropHeight=\"options.autoCropHeight\"\n          :fixedBox=\"options.fixedBox\"\n          @realTime=\"realTime\"\n        >\n        </vue-cropper>\n      </a-col>\n      <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n        <div class=\"avatar-upload-preview\">\n          <img :src=\"previews.url\" :style=\"previews.img\"/>\n        </div>\n      </a-col>\n    </a-row>\n\n    <template slot=\"footer\">\n      <a-button key=\"back\" @click=\"cancelHandel\">取消</a-button>\n      <a-button key=\"submit\" type=\"primary\" :loading=\"confirmLoading\" @click=\"okHandel\">保存</a-button>\n    </template>\n  </a-modal>\n</template>\n<script>\n  import { VueCropper } from 'vue-cropper'\n\n  export default {\n    components: {\n      VueCropper\n    },\n    data() {\n      return {\n        visible: false,\n        id: null,\n        confirmLoading: false,\n\n        options: {\n          img: '/avatar2.jpg',\n          autoCrop: true,\n          autoCropWidth: 200,\n          autoCropHeight: 200,\n          fixedBox: true\n        },\n        previews: {},\n      };\n    },\n    methods: {\n      edit(id) {\n        this.visible = true;\n        this.id = id;\n        /* 获取原始头像 */\n\n      },\n      close() {\n        this.id = null;\n        this.visible = false;\n      },\n      cancelHandel() {\n        this.close();\n      },\n      okHandel() {\n        const vm = this\n\n        vm.confirmLoading = true\n        setTimeout(() => {\n          vm.confirmLoading = false\n          vm.close()\n          vm.$message.success('上传头像成功');\n        }, 2000)\n\n      },\n\n      realTime(data) {\n        this.previews = data\n      }\n    }\n  };\n</script>\n\n<style lang=\"less\" scoped>\n\n  .avatar-upload-preview {\n    position: absolute;\n    top: 50%;\n    transform: translate(50%, -50%);\n    width: 180px;\n    height: 180px;\n    border-radius: 50%;\n    box-shadow: 0 0 4px #ccc;\n    overflow: hidden;\n\n    img {\n      width: 100%;\n      height: 100%;\n    }\n  }\n</style>"], "mappings": "AA8BA,SAAAA,UAAA;AAEA;EACAC,UAAA;IACAD,UAAA,EAAAA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,EAAA;MACAC,cAAA;MAEAC,OAAA;QACAC,GAAA;QACAC,QAAA;QACAC,aAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAV,EAAA;MACA,KAAAD,OAAA;MACA,KAAAC,EAAA,GAAAA,EAAA;MACA;IAEA;IACAW,KAAA,WAAAA,MAAA;MACA,KAAAX,EAAA;MACA,KAAAD,OAAA;IACA;IACAa,YAAA,WAAAA,aAAA;MACA,KAAAD,KAAA;IACA;IACAE,QAAA,WAAAA,SAAA;MACA,IAAAC,EAAA;MAEAA,EAAA,CAAAb,cAAA;MACAc,UAAA;QACAD,EAAA,CAAAb,cAAA;QACAa,EAAA,CAAAH,KAAA;QACAG,EAAA,CAAAE,QAAA,CAAAC,OAAA;MACA;IAEA;IAEAC,QAAA,WAAAA,SAAApB,IAAA;MACA,KAAAU,QAAA,GAAAV,IAAA;IACA;EACA;AACA", "ignoreList": []}]}