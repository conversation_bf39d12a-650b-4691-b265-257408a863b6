{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\index.vue?vue&type=template&id=7cddf3ce&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-row class=\"j-select-biz-component-box\" type=\"flex\" :gutter=\"8\">\n  <a-col class=\"left\" :class=\"{'full': !buttons}\">\n    <slot name=\"left\">\n      <a-select\n        mode=\"multiple\"\n        :placeholder=\"placeholder\"\n        v-model=\"selectValue\"\n        :options=\"selectOptions\"\n        allowClear\n        :disabled=\"disabled\"\n        :open=\"selectOpen\"\n        style=\"width: 100%;\"\n        @dropdownVisibleChange=\"handleDropdownVisibleChange\"\n        @click.native=\"visible=(buttons?visible:true)\"\n      />\n    </slot>\n  </a-col>\n\n  <a-col v-if=\"buttons\" class=\"right\">\n    <a-button type=\"primary\" icon=\"search\" :disabled=\"disabled\" @click=\"visible=true\">{{selectButtonText}}</a-button>\n  </a-col>\n\n  <j-select-biz-component-modal\n    v-model=\"selectValue\"\n    :visible.sync=\"visible\"\n    v-bind=\"modalProps\"\n    @options=\"handleOptions\"\n  />\n</a-row>\n", null]}