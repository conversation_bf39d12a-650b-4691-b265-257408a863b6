{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\JvmInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\JvmInfo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import moment from 'moment';\nimport { getAction } from '@/api/manage';\nmoment.locale('zh-cn');\nexport default {\n  data: function data() {\n    return {\n      time: '',\n      loading: true,\n      tableLoading: true,\n      columns: [{\n        title: '参数',\n        width: '30%',\n        dataIndex: 'param',\n        scopedSlots: {\n          customRender: 'param'\n        }\n      }, {\n        title: '描述',\n        width: '40%',\n        dataIndex: 'text',\n        scopedSlots: {\n          customRender: 'text'\n        }\n      }, {\n        title: '当前值',\n        width: '30%',\n        dataIndex: 'value',\n        scopedSlots: {\n          customRender: 'value'\n        }\n      }],\n      dataSource: [],\n      // 列表通过 textInfo 渲染出颜色、描述和单位\n      textInfo: {\n        'jvm.memory.max': {\n          color: 'purple',\n          text: 'JVM 最大内存',\n          unit: 'MB'\n        },\n        'jvm.memory.committed': {\n          color: 'purple',\n          text: 'JVM 可用内存',\n          unit: 'MB'\n        },\n        'jvm.memory.used': {\n          color: 'purple',\n          text: 'JVM 已用内存',\n          unit: 'MB'\n        },\n        'jvm.buffer.memory.used': {\n          color: 'cyan',\n          text: 'JVM 缓冲区已用内存',\n          unit: 'MB'\n        },\n        'jvm.buffer.count': {\n          color: 'cyan',\n          text: '当前缓冲区数量',\n          unit: '个'\n        },\n        'jvm.threads.daemon': {\n          color: 'green',\n          text: 'JVM 守护线程数量',\n          unit: '个'\n        },\n        'jvm.threads.live': {\n          color: 'green',\n          text: 'JVM 当前活跃线程数量',\n          unit: '个'\n        },\n        'jvm.threads.peak': {\n          color: 'green',\n          text: 'JVM 峰值线程数量',\n          unit: '个'\n        },\n        'jvm.classes.loaded': {\n          color: 'orange',\n          text: 'JVM 已加载 Class 数量',\n          unit: '个'\n        },\n        'jvm.classes.unloaded': {\n          color: 'orange',\n          text: 'JVM 未加载 Class 数量',\n          unit: '个'\n        },\n        'jvm.gc.memory.allocated': {\n          color: 'pink',\n          text: 'GC 时, 年轻代分配的内存空间',\n          unit: 'MB'\n        },\n        'jvm.gc.memory.promoted': {\n          color: 'pink',\n          text: 'GC 时, 老年代分配的内存空间',\n          unit: 'MB'\n        },\n        'jvm.gc.max.data.size': {\n          color: 'pink',\n          text: 'GC 时, 老年代的最大内存空间',\n          unit: 'MB'\n        },\n        'jvm.gc.live.data.size': {\n          color: 'pink',\n          text: 'FullGC 时, 老年代的内存空间',\n          unit: 'MB'\n        },\n        'jvm.gc.pause.count': {\n          color: 'blue',\n          text: '系统启动以来GC 次数',\n          unit: '次'\n        },\n        'jvm.gc.pause.totalTime': {\n          color: 'blue',\n          text: '系统启动以来GC 总耗时',\n          unit: '秒'\n        }\n      },\n      // 当一条记录中需要取出多条数据的时候需要配置该字段\n      moreInfo: {\n        'jvm.gc.pause': ['.count', '.totalTime']\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.loadTomcatInfo();\n  },\n  methods: {\n    handleClickUpdate: function handleClickUpdate() {\n      this.loadTomcatInfo();\n    },\n    loadTomcatInfo: function loadTomcatInfo() {\n      var _this = this;\n      this.tableLoading = true;\n      this.time = moment().format('YYYY年MM月DD日 HH时mm分ss秒');\n      Promise.all([getAction('actuator/metrics/jvm.memory.max'), getAction('actuator/metrics/jvm.memory.committed'), getAction('actuator/metrics/jvm.memory.used'), getAction('actuator/metrics/jvm.buffer.memory.used'), getAction('actuator/metrics/jvm.buffer.count'), getAction('actuator/metrics/jvm.threads.daemon'), getAction('actuator/metrics/jvm.threads.live'), getAction('actuator/metrics/jvm.threads.peak'), getAction('actuator/metrics/jvm.classes.loaded'), getAction('actuator/metrics/jvm.classes.unloaded'), getAction('actuator/metrics/jvm.gc.memory.allocated'), getAction('actuator/metrics/jvm.gc.memory.promoted'), getAction('actuator/metrics/jvm.gc.max.data.size'), getAction('actuator/metrics/jvm.gc.live.data.size'), getAction('actuator/metrics/jvm.gc.pause')]).then(function (res) {\n        var info = [];\n        res.forEach(function (value, id) {\n          var more = _this.moreInfo[value.name];\n          if (!(more instanceof Array)) {\n            more = [''];\n          }\n          more.forEach(function (item, idx) {\n            var param = value.name + item;\n            var val = value.measurements[idx].value;\n            if (param === 'jvm.memory.max' || param === 'jvm.memory.committed' || param === 'jvm.memory.used' || param === 'jvm.buffer.memory.used' || param === 'jvm.gc.memory.allocated' || param === 'jvm.gc.memory.promoted' || param === 'jvm.gc.max.data.size' || param === 'jvm.gc.live.data.size') {\n              val = _this.convert(val, Number);\n            }\n            info.push({\n              id: param + id,\n              param: param,\n              text: 'false value',\n              value: val\n            });\n          });\n        });\n        _this.dataSource = info;\n      }).catch(function (e) {\n        console.error(e);\n        _this.$message.error('获取JVM信息失败');\n      }).finally(function () {\n        _this.loading = false;\n        _this.tableLoading = false;\n      });\n    },\n    convert: function convert(value, type) {\n      if (type === Number) {\n        return Number(value / 1048576).toFixed(3);\n      } else if (type === Date) {\n        return moment(value * 1000).format('YYYY-MM-DD HH:mm:ss');\n      }\n      return value;\n    }\n  }\n};", {"version": 3, "names": ["moment", "getAction", "locale", "data", "time", "loading", "tableLoading", "columns", "title", "width", "dataIndex", "scopedSlots", "customRender", "dataSource", "textInfo", "color", "text", "unit", "moreInfo", "mounted", "loadTomcatInfo", "methods", "handleClickUpdate", "_this", "format", "Promise", "all", "then", "res", "info", "for<PERSON>ach", "value", "id", "more", "name", "Array", "item", "idx", "param", "val", "measurements", "convert", "Number", "push", "catch", "e", "console", "error", "$message", "finally", "type", "toFixed", "Date"], "sources": ["src/views/modules/monitor/JvmInfo.vue"], "sourcesContent": ["<template>\n  <a-skeleton active :loading=\"loading\" :paragraph=\"{rows: 17}\">\n    <a-card :bordered=\"false\">\n\n      <a-alert type=\"info\" :showIcon=\"true\">\n        <div slot=\"message\">\n          上次更新时间：{{ this.time }}\n          <a-divider type=\"vertical\"/>\n          <a @click=\"handleClickUpdate\">立即更新</a>\n        </div>\n      </a-alert>\n\n      <a-table\n        rowKey=\"id\"\n        size=\"middle\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"false\"\n        :loading=\"tableLoading\"\n        style=\"margin-top: 20px;\">\n\n        <template slot=\"param\" slot-scope=\"text, record\">\n          <a-tag :color=\"textInfo[record.param].color\">{{ text }}</a-tag>\n        </template>\n\n        <template slot=\"text\" slot-scope=\"text, record\">\n          {{ textInfo[record.param].text }}\n        </template>\n\n        <template slot=\"value\" slot-scope=\"text, record\">\n          {{ text }} {{ textInfo[record.param].unit }}\n        </template>\n\n      </a-table>\n    </a-card>\n  </a-skeleton>\n</template>\n<script>\n  import moment from 'moment'\n  import { getAction } from '@/api/manage'\n\n  moment.locale('zh-cn')\n\n  export default {\n    data() {\n      return {\n        time: '',\n        loading: true,\n        tableLoading: true,\n        columns: [{\n          title: '参数',\n          width: '30%',\n          dataIndex: 'param',\n          scopedSlots: { customRender: 'param' }\n        }, {\n          title: '描述',\n          width: '40%',\n          dataIndex: 'text',\n          scopedSlots: { customRender: 'text' }\n        }, {\n          title: '当前值',\n          width: '30%',\n          dataIndex: 'value',\n          scopedSlots: { customRender: 'value' }\n        }],\n        dataSource: [],\n        // 列表通过 textInfo 渲染出颜色、描述和单位\n        textInfo: {\n          'jvm.memory.max': { color: 'purple', text: 'JVM 最大内存', unit: 'MB' },\n          'jvm.memory.committed': { color: 'purple', text: 'JVM 可用内存', unit: 'MB' },\n          'jvm.memory.used': { color: 'purple', text: 'JVM 已用内存', unit: 'MB' },\n          'jvm.buffer.memory.used': { color: 'cyan', text: 'JVM 缓冲区已用内存', unit: 'MB' },\n          'jvm.buffer.count': { color: 'cyan', text: '当前缓冲区数量', unit: '个' },\n          'jvm.threads.daemon': { color: 'green', text: 'JVM 守护线程数量', unit: '个' },\n          'jvm.threads.live': { color: 'green', text: 'JVM 当前活跃线程数量', unit: '个' },\n          'jvm.threads.peak': { color: 'green', text: 'JVM 峰值线程数量', unit: '个' },\n          'jvm.classes.loaded': { color: 'orange', text: 'JVM 已加载 Class 数量', unit: '个' },\n          'jvm.classes.unloaded': { color: 'orange', text: 'JVM 未加载 Class 数量', unit: '个' },\n          'jvm.gc.memory.allocated': { color: 'pink', text: 'GC 时, 年轻代分配的内存空间', unit: 'MB' },\n          'jvm.gc.memory.promoted': { color: 'pink', text: 'GC 时, 老年代分配的内存空间', unit: 'MB' },\n          'jvm.gc.max.data.size': { color: 'pink', text: 'GC 时, 老年代的最大内存空间', unit: 'MB' },\n          'jvm.gc.live.data.size': { color: 'pink', text: 'FullGC 时, 老年代的内存空间', unit: 'MB' },\n          'jvm.gc.pause.count': { color: 'blue', text: '系统启动以来GC 次数', unit: '次' },\n          'jvm.gc.pause.totalTime': { color: 'blue', text: '系统启动以来GC 总耗时', unit: '秒' }\n        },\n        // 当一条记录中需要取出多条数据的时候需要配置该字段\n        moreInfo: {\n          'jvm.gc.pause': ['.count', '.totalTime']\n        }\n      }\n    },\n    mounted() {\n      this.loadTomcatInfo()\n    },\n    methods: {\n\n      handleClickUpdate() {\n        this.loadTomcatInfo()\n      },\n\n      loadTomcatInfo() {\n        this.tableLoading = true\n        this.time = moment().format('YYYY年MM月DD日 HH时mm分ss秒')\n        Promise.all([\n          getAction('actuator/metrics/jvm.memory.max'),\n          getAction('actuator/metrics/jvm.memory.committed'),\n          getAction('actuator/metrics/jvm.memory.used'),\n          getAction('actuator/metrics/jvm.buffer.memory.used'),\n          getAction('actuator/metrics/jvm.buffer.count'),\n          getAction('actuator/metrics/jvm.threads.daemon'),\n          getAction('actuator/metrics/jvm.threads.live'),\n          getAction('actuator/metrics/jvm.threads.peak'),\n          getAction('actuator/metrics/jvm.classes.loaded'),\n          getAction('actuator/metrics/jvm.classes.unloaded'),\n          getAction('actuator/metrics/jvm.gc.memory.allocated'),\n          getAction('actuator/metrics/jvm.gc.memory.promoted'),\n          getAction('actuator/metrics/jvm.gc.max.data.size'),\n          getAction('actuator/metrics/jvm.gc.live.data.size'),\n          getAction('actuator/metrics/jvm.gc.pause')\n        ]).then((res) => {\n\n          let info = []\n          res.forEach((value, id) => {\n            let more = this.moreInfo[value.name]\n            if (!(more instanceof Array)) {\n              more = ['']\n            }\n            more.forEach((item, idx) => {\n              let param = value.name + item\n              let val = value.measurements[idx].value\n\n              if (param === 'jvm.memory.max'\n                || param === 'jvm.memory.committed'\n                || param === 'jvm.memory.used'\n                || param === 'jvm.buffer.memory.used'\n                || param === 'jvm.gc.memory.allocated'\n                || param === 'jvm.gc.memory.promoted'\n                || param === 'jvm.gc.max.data.size'\n                || param === 'jvm.gc.live.data.size'\n              ) {\n                val = this.convert(val, Number)\n              }\n              info.push({ id: param + id, param, text: 'false value', value: val })\n            })\n          })\n          this.dataSource = info\n\n\n        }).catch((e) => {\n          console.error(e)\n          this.$message.error('获取JVM信息失败')\n        }).finally(() => {\n          this.loading = false\n          this.tableLoading = false\n        })\n      },\n\n      convert(value, type) {\n        if (type === Number) {\n          return Number(value / 1048576).toFixed(3)\n        } else if (type === Date) {\n          return moment(value * 1000).format('YYYY-MM-DD HH:mm:ss')\n        }\n        return value\n      }\n    }\n  }\n</script>\n<style></style>\n"], "mappings": "AAsCA,OAAAA,MAAA;AACA,SAAAC,SAAA;AAEAD,MAAA,CAAAE,MAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,YAAA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA;QACAJ,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA;QACAJ,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;QACA;UAAAC,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;QACA;UAAAF,KAAA;UAAAC,IAAA;UAAAC,IAAA;QAAA;MACA;MACA;MACAC,QAAA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IAEAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,cAAA;IACA;IAEAA,cAAA,WAAAA,eAAA;MAAA,IAAAG,KAAA;MACA,KAAAjB,YAAA;MACA,KAAAF,IAAA,GAAAJ,MAAA,GAAAwB,MAAA;MACAC,OAAA,CAAAC,GAAA,EACAzB,SAAA,qCACAA,SAAA,2CACAA,SAAA,sCACAA,SAAA,6CACAA,SAAA,uCACAA,SAAA,yCACAA,SAAA,uCACAA,SAAA,uCACAA,SAAA,yCACAA,SAAA,2CACAA,SAAA,8CACAA,SAAA,6CACAA,SAAA,2CACAA,SAAA,4CACAA,SAAA,kCACA,EAAA0B,IAAA,WAAAC,GAAA;QAEA,IAAAC,IAAA;QACAD,GAAA,CAAAE,OAAA,WAAAC,KAAA,EAAAC,EAAA;UACA,IAAAC,IAAA,GAAAV,KAAA,CAAAL,QAAA,CAAAa,KAAA,CAAAG,IAAA;UACA,MAAAD,IAAA,YAAAE,KAAA;YACAF,IAAA;UACA;UACAA,IAAA,CAAAH,OAAA,WAAAM,IAAA,EAAAC,GAAA;YACA,IAAAC,KAAA,GAAAP,KAAA,CAAAG,IAAA,GAAAE,IAAA;YACA,IAAAG,GAAA,GAAAR,KAAA,CAAAS,YAAA,CAAAH,GAAA,EAAAN,KAAA;YAEA,IAAAO,KAAA,yBACAA,KAAA,+BACAA,KAAA,0BACAA,KAAA,iCACAA,KAAA,kCACAA,KAAA,iCACAA,KAAA,+BACAA,KAAA,8BACA;cACAC,GAAA,GAAAhB,KAAA,CAAAkB,OAAA,CAAAF,GAAA,EAAAG,MAAA;YACA;YACAb,IAAA,CAAAc,IAAA;cAAAX,EAAA,EAAAM,KAAA,GAAAN,EAAA;cAAAM,KAAA,EAAAA,KAAA;cAAAtB,IAAA;cAAAe,KAAA,EAAAQ;YAAA;UACA;QACA;QACAhB,KAAA,CAAAV,UAAA,GAAAgB,IAAA;MAGA,GAAAe,KAAA,WAAAC,CAAA;QACAC,OAAA,CAAAC,KAAA,CAAAF,CAAA;QACAtB,KAAA,CAAAyB,QAAA,CAAAD,KAAA;MACA,GAAAE,OAAA;QACA1B,KAAA,CAAAlB,OAAA;QACAkB,KAAA,CAAAjB,YAAA;MACA;IACA;IAEAmC,OAAA,WAAAA,QAAAV,KAAA,EAAAmB,IAAA;MACA,IAAAA,IAAA,KAAAR,MAAA;QACA,OAAAA,MAAA,CAAAX,KAAA,YAAAoB,OAAA;MACA,WAAAD,IAAA,KAAAE,IAAA;QACA,OAAApD,MAAA,CAAA+B,KAAA,SAAAP,MAAA;MACA;MACA,OAAAO,KAAA;IACA;EACA;AACA", "ignoreList": []}]}