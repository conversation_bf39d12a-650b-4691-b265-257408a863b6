{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\LineChartMultid.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\LineChartMultid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'LineChartMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jan', jeecg: 7.0, jeebt: 3.9 },\n          { type: 'Feb', jeecg: 6.9, jeebt: 4.2 },\n          { type: 'Mar', jeecg: 9.5, jeebt: 5.7 },\n          { type: 'Apr', jeecg: 14.5, jeebt: 8.5 },\n          { type: 'May', jeecg: 18.4, jeebt: 11.9 },\n          { type: 'Jun', jeecg: 21.5, jeebt: 15.2 },\n          { type: 'Jul', jeecg: 25.2, jeebt: 17.0 },\n          { type: 'Aug', jeecg: 26.5, jeebt: 16.6 },\n          { type: 'Sep', jeecg: 23.3, jeebt: 14.2 },\n          { type: 'Oct', jeecg: 18.3, jeebt: 10.3 },\n          { type: 'Nov', jeecg: 13.9, jeebt: 6.6 },\n          { type: 'Dec', jeecg: 9.6, jeebt: 4.8 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['jeecg', 'jeebt']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases:{\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        scale: [{\n          dataKey: 'x',\n          min: 0,\n          max: 1\n        }],\n        style: { stroke: '#fff', lineWidth: 1 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n        let rows =  dv.rows\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.x) {\n              row.x = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n", {"version": 3, "sources": ["LineChartMultid.vue"], "names": [], "mappings": ";AAcA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "LineChartMultid.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :onClick=\"handleClick\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-legend/>\n      <v-line position=\"type*y\" color=\"x\"/>\n      <v-point position=\"type*y\" color=\"x\" :size=\"4\" :v-style=\"style\" :shape=\"'circle'\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'LineChartMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jan', jeecg: 7.0, jeebt: 3.9 },\n          { type: 'Feb', jeecg: 6.9, jeebt: 4.2 },\n          { type: 'Mar', jeecg: 9.5, jeebt: 5.7 },\n          { type: 'Apr', jeecg: 14.5, jeebt: 8.5 },\n          { type: 'May', jeecg: 18.4, jeebt: 11.9 },\n          { type: 'Jun', jeecg: 21.5, jeebt: 15.2 },\n          { type: 'Jul', jeecg: 25.2, jeebt: 17.0 },\n          { type: 'Aug', jeecg: 26.5, jeebt: 16.6 },\n          { type: 'Sep', jeecg: 23.3, jeebt: 14.2 },\n          { type: 'Oct', jeecg: 18.3, jeebt: 10.3 },\n          { type: 'Nov', jeecg: 13.9, jeebt: 6.6 },\n          { type: 'Dec', jeecg: 9.6, jeebt: 4.8 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['jeecg', 'jeebt']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases:{\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        scale: [{\n          dataKey: 'x',\n          min: 0,\n          max: 1\n        }],\n        style: { stroke: '#fff', lineWidth: 1 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n        let rows =  dv.rows\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.x) {\n              row.x = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}