{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue?vue&type=template&id=10e519b4&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"1000\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n      <!-- 主表单区域 -->\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单号\"\n        hasFeedback>\n        <a-input\n          placeholder=\"请输入订单号\"\n          v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\"\n        />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单类型\">\n        <a-select placeholder=\"请输入订单类型\" v-decorator=\"['ctype',{}]\">\n          <a-select-option value=\"1\">国内订单</a-select-option>\n          <a-select-option value=\"2\">国际订单</a-select-option>\n        </a-select>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单日期\">\n        <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator=\"[ 'orderDate',{}]\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单金额\">\n        <a-input-number style=\"width: 200px\" v-decorator=\"[ 'orderMoney', {}]\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"订单备注\">\n        <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\"/>\n      </a-form-item>\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}