{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue?vue&type=style&index=0&id=5e7337c8&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue", "mtime": 1746672706055}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.slick-arrow {\n  border-radius: 50%;\n  text-align: center;\n  height: 40px;\n  line-height: 40px;\n  width: 40px;\n  background: #364e799a;\n  overflow: hidden;\n  color: #fff;\n  opacity: 0.5;\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n.slick-arrow:hover {\n  background: #364e79;\n  color: #fff;\n  opacity: 1;\n}\n#IndexPage {\n  width: 100%;\n  height: auto;\n  margin-top: 28px;\n  .banner {\n    width: 100%;\n    height: 100%;\n    background: #ccc;\n  }\n  .boxBackground {\n    width: 100%;\n    // background: #030149;\n    .boxContent {\n      width: 1200px;\n      margin: 0 auto;\n      height: 100%;\n      overflow: hidden;\n      // max-height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n\n      /deep/img {\n        max-width: 100%;\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Index.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <!-- <div id=\"IndexPage\" :style=\"{ background: sysConfig.homeBgColor }\"> -->\n    <!-- <div :style=\"{ 'background-image': 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')', 'background-repeat': sysConfig.homeBgRepeat }\"> -->\n      <div class=\"boxBackground\">\n        <div class=\"boxContent\" v-html=\"sysConfig._homeHtml\"></div>\n      </div>\n    <!-- </div> -->\n  <!-- </div> -->\n</template>\n<script>\nimport { getFileAccessHttpUrl } from '@/api/manage'\nexport default {\n  components: {},\n  data() {\n    return {\n      sysConfig: {},\n    }\n  },\n  watch: {},\n  methods: {},\n  created() {\n    this.sysConfig = this.$store.getters.sysConfig\n  },\n  mounted() {},\n  methods:{\n    getFileAccessHttpUrl\n  }\n}\n</script>\n<style lang='less' scoped>\n.slick-arrow {\n  border-radius: 50%;\n  text-align: center;\n  height: 40px;\n  line-height: 40px;\n  width: 40px;\n  background: #364e799a;\n  overflow: hidden;\n  color: #fff;\n  opacity: 0.5;\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n.slick-arrow:hover {\n  background: #364e79;\n  color: #fff;\n  opacity: 1;\n}\n#IndexPage {\n  width: 100%;\n  height: auto;\n  margin-top: 28px;\n  .banner {\n    width: 100%;\n    height: 100%;\n    background: #ccc;\n  }\n  .boxBackground {\n    width: 100%;\n    // background: #030149;\n    .boxContent {\n      width: 1200px;\n      margin: 0 auto;\n      height: 100%;\n      overflow: hidden;\n      // max-height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n\n      /deep/img {\n        max-width: 100%;\n      }\n    }\n  }\n}\n</style>"]}]}