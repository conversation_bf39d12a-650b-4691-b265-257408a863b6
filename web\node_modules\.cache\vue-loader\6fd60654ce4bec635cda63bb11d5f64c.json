{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue?vue&type=template&id=********&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<a-modal :visible=\"visible\" title=\"修改头像\" :maskClosable=\"false\" :confirmLoading=\"confirmLoading\" :width=\"800\">\n  <a-row>\n    <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n      <vue-cropper\n        ref=\"cropper\"\n        :img=\"options.img\"\n        :info=\"true\"\n        :autoCrop=\"options.autoCrop\"\n        :autoCropWidth=\"options.autoCropWidth\"\n        :autoCropHeight=\"options.autoCropHeight\"\n        :fixedBox=\"options.fixedBox\"\n        @realTime=\"realTime\"\n      >\n      </vue-cropper>\n    </a-col>\n    <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n      <div class=\"avatar-upload-preview\">\n        <img :src=\"previews.url\" :style=\"previews.img\"/>\n      </div>\n    </a-col>\n  </a-row>\n\n  <template slot=\"footer\">\n    <a-button key=\"back\" @click=\"cancelHandel\">取消</a-button>\n    <a-button key=\"submit\" type=\"primary\" :loading=\"confirmLoading\" @click=\"okHandel\">保存</a-button>\n  </template>\n</a-modal>\n", null]}