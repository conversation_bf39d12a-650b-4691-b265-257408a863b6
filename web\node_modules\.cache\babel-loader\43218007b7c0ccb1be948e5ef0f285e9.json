{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport PageLayout from '@/components/page/PageLayout';\nimport RouteView from \"@/components/layouts/RouteView\";\nimport { MineWorksPage } from './page';\nimport { mapGetters } from 'vuex';\nimport { getFileAccessHttpUrl } from '@/api/manage';\nexport default {\n  components: {\n    RouteView: RouteView,\n    PageLayout: PageLayout,\n    MineWorksPage: MineWorksPage\n  },\n  data: function data() {\n    return {\n      tabListNoTitle: [{\n        key: 'mineWorks',\n        tab: '我的作品'\n      }\n      // , {\n      //   key: 'greatWorks',\n      //   tab: '精选作品',\n      // }\n      ],\n      noTitleKey: 'mineWorks'\n    };\n  },\n  mounted: function mounted() {\n    this.getWorks();\n  },\n  methods: _objectSpread(_objectSpread({}, mapGetters([\"nickname\", \"avatar\"])), {}, {\n    getAvatar: function getAvatar() {\n      return getFileAccessHttpUrl(this.avatar());\n    },\n    getTeams: function getTeams() {\n      var _this = this;\n      this.$http.get('/api/workplace/teams').then(function (res) {\n        _this.teams = res.result;\n        _this.teamSpinning = false;\n      });\n    },\n    getWorks: function getWorks() {},\n    handleTabChange: function handleTabChange(key, type) {\n      this[type] = key;\n    },\n    showTagInput: function showTagInput() {\n      var _this2 = this;\n      this.tagInputVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.tagInput.focus();\n      });\n    },\n    handleInputChange: function handleInputChange(e) {\n      this.tagInputValue = e.target.value;\n    }\n  })\n};", {"version": 3, "names": ["PageLayout", "RouteView", "MineWorksPage", "mapGetters", "getFileAccessHttpUrl", "components", "data", "tabListNoTitle", "key", "tab", "noTitleKey", "mounted", "getWorks", "methods", "_objectSpread", "get<PERSON><PERSON><PERSON>", "avatar", "getTeams", "_this", "$http", "get", "then", "res", "teams", "result", "teamSpinning", "handleTabChange", "type", "showTagInput", "_this2", "tagInputVisible", "$nextTick", "$refs", "tagInput", "focus", "handleInputChange", "e", "tagInputValue", "target", "value"], "sources": ["src/views/account/center/Index.vue"], "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide page-header-wrapper-grid-content-main\">\n    <a-row :gutter=\"24\">\n      <a-col :md=\"24\" :lg=\"24\">\n        <a-card\n          style=\"width:100%\"\n          :bordered=\"false\"\n          :tabList=\"tabListNoTitle\"\n          :activeTabKey=\"noTitleKey\"\n          @tabChange=\"key => handleTabChange(key, 'noTitleKey')\"\n        >\n          <mineWorks-page v-if=\"noTitleKey === 'mineWorks'\"></mineWorks-page>\n        </a-card>\n      </a-col>\n    </a-row>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { MineWorksPage } from './page'\n  import { mapGetters } from 'vuex'\n  import { getFileAccessHttpUrl } from '@/api/manage';\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout,\n      MineWorksPage\n    },\n    data() {\n      return {\n        tabListNoTitle: [{\n            key: 'mineWorks',\n            tab: '我的作品',\n          }\n          // , {\n          //   key: 'greatWorks',\n          //   tab: '精选作品',\n          // }\n        ],\n        noTitleKey: 'mineWorks',\n      }\n    },\n    mounted () {\n      this.getWorks()\n    },\n    methods: {\n      ...mapGetters([\"nickname\", \"avatar\"]),\n      getAvatar(){\n          return getFileAccessHttpUrl(this.avatar());\n      },\n      getTeams() {\n        this.$http.get('/api/workplace/teams')\n          .then(res => {\n            this.teams = res.result\n            this.teamSpinning = false\n          })\n      },\n\n      getWorks(){\n\n      },\n\n      handleTabChange (key, type) {\n        this[type] = key\n      },\n\n\n      showTagInput () {\n        this.tagInputVisible = true\n        this.$nextTick(() => {\n          this.$refs.tagInput.focus()\n        })\n      },\n\n      handleInputChange (e) {\n        this.tagInputValue = e.target.value\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .page-header-wrapper-grid-content-main {\n    width: 100%;\n    height: 100%;\n    min-height: 100%;\n    transition: .3s;\n\n    .account-center-avatarHolder {\n      text-align: center;\n      margin-bottom: 24px;\n\n      & > .avatar {\n        margin: 0 auto;\n        width: 104px;\n        height: 104px;\n        margin-bottom: 20px;\n        border-radius: 50%;\n        overflow: hidden;\n        img {\n          height: 100%;\n          width: 100%;\n        }\n      }\n\n      .username {\n        color: rgba(0, 0, 0, 0.85);\n        font-size: 20px;\n        line-height: 28px;\n        font-weight: 500;\n        margin-bottom: 4px;\n      }\n    }\n\n    .account-center-detail {\n\n      p {\n        margin-bottom: 8px;\n        padding-left: 26px;\n        position: relative;\n      }\n\n      i {\n        position: absolute;\n        height: 14px;\n        width: 14px;\n        left: 0;\n        top: 4px;\n        background: url(https://gw.alipayobjects.com/zos/rmsportal/pBjWzVAHnOOtAUvZmZfy.svg)\n      }\n\n      .title {\n        background-position: 0 0;\n      }\n      .group {\n        background-position: 0 -22px;\n      }\n      .address {\n        background-position: 0 -44px;\n      }\n    }\n\n    .account-center-tags {\n      .ant-tag {\n        margin-bottom: 8px;\n      }\n    }\n\n    .account-center-team {\n\n      .members {\n        a {\n          display: block;\n          margin: 12px 0;\n          line-height: 24px;\n          height: 24px;\n          .member {\n            font-size: 14px;\n            color: rgba(0, 0, 0, .65);\n            line-height: 24px;\n            max-width: 100px;\n            vertical-align: top;\n            margin-left: 12px;\n            transition: all 0.3s;\n            display: inline-block;\n          }\n          &:hover {\n            span {\n              color: #1890ff;\n            }\n          }\n        }\n      }\n    }\n\n    .tagsTitle, .teamTitle {\n      font-weight: 500;\n      color: rgba(0,0,0,.85);\n      margin-bottom: 12px;\n    }\n\n  }\n\n</style>"], "mappings": ";;;;;;AAmBA,OAAAA,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,UAAA;IACAJ,SAAA,EAAAA,SAAA;IACAD,UAAA,EAAAA,UAAA;IACAE,aAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;QACAC,GAAA;QACAC,GAAA;MACA;MACA;MACA;MACA;MACA;MAAA,CACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACAX,UAAA;IACAY,SAAA,WAAAA,UAAA;MACA,OAAAX,oBAAA,MAAAY,MAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,GAAA,yBACAC,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAAK,KAAA,GAAAD,GAAA,CAAAE,MAAA;QACAN,KAAA,CAAAO,YAAA;MACA;IACA;IAEAb,QAAA,WAAAA,SAAA,GAEA;IAEAc,eAAA,WAAAA,gBAAAlB,GAAA,EAAAmB,IAAA;MACA,KAAAA,IAAA,IAAAnB,GAAA;IACA;IAGAoB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,eAAA;MACA,KAAAC,SAAA;QACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IAEAC,iBAAA,WAAAA,kBAAAC,CAAA;MACA,KAAAC,aAAA,GAAAD,CAAA,CAAAE,MAAA,CAAAC,KAAA;IACA;EAAA;AAEA", "ignoreList": []}]}