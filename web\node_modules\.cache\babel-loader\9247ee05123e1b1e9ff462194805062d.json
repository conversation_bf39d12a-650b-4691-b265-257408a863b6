{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue?vue&type=template&id=1de75ee0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"standard-table\"\n  }, [_c(\"div\", {\n    staticClass: \"alert\"\n  }, [_c(\"a-alert\", {\n    attrs: {\n      type: \"info\",\n      \"show-icon\": true\n    }\n  }, [_c(\"div\", {\n    attrs: {\n      slot: \"message\"\n    },\n    slot: \"message\"\n  }, [_vm._v(\"\\n        已选择 \"), _c(\"a\", {\n    staticStyle: {\n      \"font-weight\": \"600\"\n    }\n  }, [_vm._v(_vm._s(_vm.selectedRows.length))]), _vm._v(\"  \\n        \"), _vm._l(_vm.needTotalList, function (item, index) {\n    return item.needTotal ? [_vm._v(\"\\n          \" + _vm._s(item.title) + \" 总计 \\n          \"), _c(\"a\", {\n      key: index,\n      staticStyle: {\n        \"font-weight\": \"600\"\n      }\n    }, [_vm._v(\"\\n            \" + _vm._s(item.customRender ? item.customRender(item.total) : item.total) + \"\\n          \")]), _vm._v(\"  \\n        \")] : _vm._e();\n  }), _c(\"a\", {\n    staticStyle: {\n      \"margin-left\": \"24px\"\n    },\n    on: {\n      click: _vm.onClearSelected\n    }\n  }, [_vm._v(\"清空\")])], 2)])], 1), _c(\"a-table\", {\n    attrs: {\n      size: _vm.size,\n      bordered: _vm.bordered,\n      loading: _vm.loading,\n      columns: _vm.columns,\n      dataSource: _vm.current,\n      rowKey: _vm.rowKey,\n      pagination: _vm.pagination,\n      rowSelection: {\n        selectedRowKeys: _vm.selectedRowKeys,\n        onChange: _vm.updateSelect\n      }\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "type", "slot", "_v", "staticStyle", "_s", "selectedRows", "length", "_l", "needTotalList", "item", "index", "needTotal", "title", "key", "customRender", "total", "_e", "on", "click", "onClearSelected", "size", "bordered", "loading", "columns", "dataSource", "current", "<PERSON><PERSON><PERSON>", "pagination", "rowSelection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "updateSelect", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/table/StandardTable.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"standard-table\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"alert\" },\n        [\n          _c(\"a-alert\", { attrs: { type: \"info\", \"show-icon\": true } }, [\n            _c(\n              \"div\",\n              { attrs: { slot: \"message\" }, slot: \"message\" },\n              [\n                _vm._v(\"\\n        已选择 \"),\n                _c(\"a\", { staticStyle: { \"font-weight\": \"600\" } }, [\n                  _vm._v(_vm._s(_vm.selectedRows.length)),\n                ]),\n                _vm._v(\"  \\n        \"),\n                _vm._l(_vm.needTotalList, function (item, index) {\n                  return item.needTotal\n                    ? [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(item.title) +\n                            \" 总计 \\n          \"\n                        ),\n                        _c(\n                          \"a\",\n                          { key: index, staticStyle: { \"font-weight\": \"600\" } },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  item.customRender\n                                    ? item.customRender(item.total)\n                                    : item.total\n                                ) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                        _vm._v(\"  \\n        \"),\n                      ]\n                    : _vm._e()\n                }),\n                _c(\n                  \"a\",\n                  {\n                    staticStyle: { \"margin-left\": \"24px\" },\n                    on: { click: _vm.onClearSelected },\n                  },\n                  [_vm._v(\"清空\")]\n                ),\n              ],\n              2\n            ),\n          ]),\n        ],\n        1\n      ),\n      _c(\"a-table\", {\n        attrs: {\n          size: _vm.size,\n          bordered: _vm.bordered,\n          loading: _vm.loading,\n          columns: _vm.columns,\n          dataSource: _vm.current,\n          rowKey: _vm.rowKey,\n          pagination: _vm.pagination,\n          rowSelection: {\n            selectedRowKeys: _vm.selectedRowKeys,\n            onChange: _vm.updateSelect,\n          },\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CAAC,SAAS,EAAE;IAAEG,KAAK,EAAE;MAAEC,IAAI,EAAE,MAAM;MAAE,WAAW,EAAE;IAAK;EAAE,CAAC,EAAE,CAC5DJ,EAAE,CACA,KAAK,EACL;IAAEG,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAU,CAAC;IAAEA,IAAI,EAAE;EAAU,CAAC,EAC/C,CACEN,GAAG,CAACO,EAAE,CAAC,gBAAgB,CAAC,EACxBN,EAAE,CAAC,GAAG,EAAE;IAAEO,WAAW,EAAE;MAAE,aAAa,EAAE;IAAM;EAAE,CAAC,EAAE,CACjDR,GAAG,CAACO,EAAE,CAACP,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,YAAY,CAACC,MAAM,CAAC,CAAC,CACxC,CAAC,EACFX,GAAG,CAACO,EAAE,CAAC,cAAc,CAAC,EACtBP,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,aAAa,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC/C,OAAOD,IAAI,CAACE,SAAS,GACjB,CACEhB,GAAG,CAACO,EAAE,CACJ,cAAc,GACZP,GAAG,CAACS,EAAE,CAACK,IAAI,CAACG,KAAK,CAAC,GAClB,kBACJ,CAAC,EACDhB,EAAE,CACA,GAAG,EACH;MAAEiB,GAAG,EAAEH,KAAK;MAAEP,WAAW,EAAE;QAAE,aAAa,EAAE;MAAM;IAAE,CAAC,EACrD,CACER,GAAG,CAACO,EAAE,CACJ,gBAAgB,GACdP,GAAG,CAACS,EAAE,CACJK,IAAI,CAACK,YAAY,GACbL,IAAI,CAACK,YAAY,CAACL,IAAI,CAACM,KAAK,CAAC,GAC7BN,IAAI,CAACM,KACX,CAAC,GACD,cACJ,CAAC,CAEL,CAAC,EACDpB,GAAG,CAACO,EAAE,CAAC,cAAc,CAAC,CACvB,GACDP,GAAG,CAACqB,EAAE,CAAC,CAAC;EACd,CAAC,CAAC,EACFpB,EAAE,CACA,GAAG,EACH;IACEO,WAAW,EAAE;MAAE,aAAa,EAAE;IAAO,CAAC;IACtCc,EAAE,EAAE;MAAEC,KAAK,EAAEvB,GAAG,CAACwB;IAAgB;EACnC,CAAC,EACD,CAACxB,GAAG,CAACO,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,EACDN,EAAE,CAAC,SAAS,EAAE;IACZG,KAAK,EAAE;MACLqB,IAAI,EAAEzB,GAAG,CAACyB,IAAI;MACdC,QAAQ,EAAE1B,GAAG,CAAC0B,QAAQ;MACtBC,OAAO,EAAE3B,GAAG,CAAC2B,OAAO;MACpBC,OAAO,EAAE5B,GAAG,CAAC4B,OAAO;MACpBC,UAAU,EAAE7B,GAAG,CAAC8B,OAAO;MACvBC,MAAM,EAAE/B,GAAG,CAAC+B,MAAM;MAClBC,UAAU,EAAEhC,GAAG,CAACgC,UAAU;MAC1BC,YAAY,EAAE;QACZC,eAAe,EAAElC,GAAG,CAACkC,eAAe;QACpCC,QAAQ,EAAEnC,GAAG,CAACoC;MAChB;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBtC,MAAM,CAACuC,aAAa,GAAG,IAAI;AAE3B,SAASvC,MAAM,EAAEsC,eAAe", "ignoreList": []}]}