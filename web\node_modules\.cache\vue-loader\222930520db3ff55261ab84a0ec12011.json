{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarAndLine.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarAndLine.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'BarAndLine',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: '10:10', bar: 200, line: 1000 },\n          { type: '10:15', bar: 600, line: 1000},\n          { type: '10:20', bar: 200, line: 1000},\n          { type: '10:25', bar: 900, line: 1000},\n          { type: '10:30', bar: 200, line: 1000},\n          { type: '10:35', bar: 200, line: 1000},\n          { type: '10:40', bar: 100, line: 1000}\n        ]\n      },\n      height: {\n        type: Number,\n        default: 400\n      }\n    },\n    data() {\n      return {\n        padding: { top:50, right:50, bottom:100, left:50 },\n        scale: [{\n          dataKey: 'bar',\n          min: 0\n        }, {\n          dataKey: 'line',\n          min: 0\n        }]\n      }\n    },\n    computed: {\n      data() {\n        return this.dataSource\n      }\n    }\n  }\n", {"version": 3, "sources": ["BarAndLine.vue"], "names": [], "mappings": ";AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarAndLine.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 50px 32px 0' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :padding=\" padding\" :onClick=\"handleClick\">\n      <v-tooltip/>\n      <v-legend/>\n      <v-axis/>\n      <v-bar position=\"type*bar\"/>\n      <v-line position=\"type*line\" color=\"#2fc25b\" :size=\"3\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'BarAndLine',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: '10:10', bar: 200, line: 1000 },\n          { type: '10:15', bar: 600, line: 1000},\n          { type: '10:20', bar: 200, line: 1000},\n          { type: '10:25', bar: 900, line: 1000},\n          { type: '10:30', bar: 200, line: 1000},\n          { type: '10:35', bar: 200, line: 1000},\n          { type: '10:40', bar: 100, line: 1000}\n        ]\n      },\n      height: {\n        type: Number,\n        default: 400\n      }\n    },\n    data() {\n      return {\n        padding: { top:50, right:50, bottom:100, left:50 },\n        scale: [{\n          dataKey: 'bar',\n          min: 0\n        }, {\n          dataKey: 'line',\n          min: 0\n        }]\n      }\n    },\n    computed: {\n      data() {\n        return this.dataSource\n      }\n    }\n  }\n</script>"]}]}