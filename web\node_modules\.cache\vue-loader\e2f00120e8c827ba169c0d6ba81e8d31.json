{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue?vue&type=template&id=3e5ff95c&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue", "mtime": 1753197976376}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"24\">\n        <a-col :md=\"6\" :sm=\"24\">\n          <a-form-item label=\"科目\">\n            <a-select v-model=\"queryParam.subject\" placeholder=\"请选择科目\" allowClear>\n              <a-select-option value=\"Scratch\">Scratch</a-select-option>\n              <a-select-option value=\"Python\">Python</a-select-option>\n              <a-select-option value=\"C++\">C++</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"24\">\n          <a-form-item label=\"级别\">\n            <a-select v-model=\"queryParam.level\" placeholder=\"请选择级别\" allowClear>\n              <a-select-option v-for=\"option in getLevelOptions()\" :key=\"option.value\" :value=\"option.value\">{{ option.label }}</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"24\">\n          <a-form-item label=\"考试状态\">\n            <a-select v-model=\"queryParam.status\" placeholder=\"请选择状态\" allowClear>\n              <a-select-option :value=\"1\">已提交</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"24\">\n          <span class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetQuery\">重置</a-button>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n\n  <!-- 考试统计信息 -->\n  <a-card title=\"考试统计\" style=\"margin: 16px 0;\">\n    <a-row :gutter=\"16\">\n      <a-col :span=\"8\">\n        <a-statistic title=\"已完成考试\" :value=\"completedExamCount\" />\n      </a-col>\n      <a-col :span=\"8\">\n        <a-statistic title=\"通过考试\" :value=\"passedExamCount\" />\n      </a-col>\n      <a-col :span=\"8\">\n        <a-statistic\n          title=\"通过率\"\n          :value=\"passRate\"\n          :precision=\"2\"\n          suffix=\"%\"\n          :valueStyle=\"{ color: passRate >= 60 ? '#3f8600' : '#cf1322' }\"\n        />\n      </a-col>\n    </a-row>\n  </a-card>\n\n  <a-table\n    ref=\"table\"\n    size=\"middle\"\n    bordered\n    rowKey=\"id\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :pagination=\"ipagination\"\n    :loading=\"loading\"\n    @change=\"handleTableChange\">\n\n    <template slot=\"statusSlot\" slot-scope=\"text\">\n      <a-badge :status=\"getStatusType(text)\" :text=\"getStatusText(text)\" />\n    </template>\n\n    <template slot=\"scoreSlot\" slot-scope=\"text, record\">\n      <span v-if=\"record.status === 1\">\n        <span :style=\"{ color: text >= 60 ? '#52c41a' : '#f5222d' }\">{{ text }}</span>\n        <span style=\"margin-left: 8px;\">{{ text >= 60 ? '(通过)' : '(未通过)' }}</span>\n      </span>\n      <span v-else>-</span>\n    </template>\n\n    <template slot=\"paperTitleSlot\" slot-scope=\"text, record\">\n      <span>{{ text }}</span>\n      <!-- 暂时移除paperType标签，因为后端VO中没有这个字段 -->\n    </template>\n\n    <template slot=\"actionSlot\" slot-scope=\"text, record\">\n      <div v-if=\"record.status === 1\">\n        <a @click=\"viewResult(record)\">查看结果</a>\n        <a-divider type=\"vertical\" />\n        <a @click=\"viewPaper(record)\">查看试卷</a>\n      </div>\n      <span v-else>-</span>\n    </template>\n  </a-table>\n\n  <!-- 考试结果模态框 -->\n  <exam-result-modal\n    v-model=\"resultModalVisible\"\n    :examResult=\"currentExamResult\"\n    :paperInfo=\"currentPaperInfo\"\n    :examQuestions=\"currentExamQuestions\"\n    :examDuration=\"currentExamDuration\"\n    :fromRecords=\"true\"\n  />\n\n  <!-- 试卷预览组件 -->\n  <exam-paper-preview\n    v-model=\"paperPreviewVisible\"\n    :paperData=\"paperPreviewData\"\n  />\n</a-card>\n", null]}