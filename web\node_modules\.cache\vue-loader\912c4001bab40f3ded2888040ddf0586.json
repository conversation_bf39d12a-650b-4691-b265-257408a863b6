{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  const dataSource = []\n  dataSource.push(null)\n  for (let i = 0; i < 11; i++) {\n    dataSource.push({\n      title: 'Alipay',\n      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n      content: '在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。'\n    })\n  }\n\n\n  export default {\n    name: \"CardList\",\n    data () {\n      return {\n        description: '段落示意：蚂蚁金服务设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态， 提供跨越设计与开发的体验解决方案。',\n        linkList: [\n          { icon: 'rocket', href: '#', title: '快速开始' },\n          { icon: 'info-circle-o', href: '#', title: '产品简介' },\n          { icon: 'file-text', href: '#', title: '产品文档' }\n        ],\n        extraImage: 'https://gw.alipayobjects.com/zos/rmsportal/RzwpdLnhmvDJToTdfDPe.png',\n        dataSource\n      }\n    }\n  }\n", {"version": 3, "sources": ["CardList.vue"], "names": [], "mappings": ";;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CardList.vue", "sourceRoot": "src/views/list", "sourcesContent": ["<template>\n  <div class=\"card-list\" ref=\"content\">\n    <a-list\n      :grid=\"{gutter: 24, lg: 3, md: 2, sm: 1, xs: 1}\"\n      :dataSource=\"dataSource\"\n    >\n      <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\n        <template v-if=\"item === null\">\n          <a-button class=\"new-btn\" type=\"dashed\">\n            <a-icon type=\"plus\"/>\n            新增产品\n          </a-button>\n        </template>\n        <template v-else>\n          <a-card :hoverable=\"true\">\n            <a-card-meta>\n              <div style=\"margin-bottom: 3px\" slot=\"title\">{{ item.title }}</div>\n              <a-avatar class=\"card-avatar\" slot=\"avatar\" :src=\"item.avatar\" size=\"large\"/>\n              <div class=\"meta-content\" slot=\"description\">{{ item.content }}</div>\n            </a-card-meta>\n            <template class=\"ant-card-actions\" slot=\"actions\">\n              <a>操作一</a>\n              <a>操作二</a>\n            </template>\n          </a-card>\n        </template>\n      </a-list-item>\n    </a-list>\n  </div>\n</template>\n\n<script>\n\n  const dataSource = []\n  dataSource.push(null)\n  for (let i = 0; i < 11; i++) {\n    dataSource.push({\n      title: 'Alipay',\n      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n      content: '在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。'\n    })\n  }\n\n\n  export default {\n    name: \"CardList\",\n    data () {\n      return {\n        description: '段落示意：蚂蚁金服务设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态， 提供跨越设计与开发的体验解决方案。',\n        linkList: [\n          { icon: 'rocket', href: '#', title: '快速开始' },\n          { icon: 'info-circle-o', href: '#', title: '产品简介' },\n          { icon: 'file-text', href: '#', title: '产品文档' }\n        ],\n        extraImage: 'https://gw.alipayobjects.com/zos/rmsportal/RzwpdLnhmvDJToTdfDPe.png',\n        dataSource\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .card-avatar {\n    width: 48px;\n    height: 48px;\n    border-radius: 48px;\n  }\n\n  .ant-card-actions {\n    background: #f7f9fa;\n    li {\n      float: left;\n      text-align: center;\n      margin: 12px 0;\n      color: rgba(0, 0, 0, 0.45);\n      width: 50%;\n\n      &:not(:last-child) {\n        border-right: 1px solid #e8e8e8;\n      }\n\n      a {\n        color: rgba(0, 0, 0, .45);\n        line-height: 22px;\n        display: inline-block;\n        width: 100%;\n        &:hover {\n          color: #1890ff;\n        }\n      }\n    }\n  }\n\n  .new-btn {\n    background-color: #fff;\n    border-radius: 2px;\n    width: 100%;\n    height: 188px;\n  }\n\n  .meta-content {\n    position: relative;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    height: 64px;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n  }\n</style>"]}]}