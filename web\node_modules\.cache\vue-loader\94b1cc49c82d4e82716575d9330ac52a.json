{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexChart.vue?vue&type=style&index=0&id=69ba1f7e&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexChart.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.circle-cust{\n  position: relative;\n  top: 28px;\n  left: -100%;\n}\n.extra-wrapper {\n  line-height: 55px;\n  padding-right: 24px;\n\n  .extra-item {\n    display: inline-block;\n    margin-right: 24px;\n\n    a {\n      margin-left: 24px;\n    }\n  }\n}\n\n/* 首页访问量统计 */\n.head-info {\n  position: relative;\n  text-align: left;\n  padding: 0 32px 0 0;\n  min-width: 125px;\n\n  &.center {\n    text-align: center;\n    padding: 0 32px;\n  }\n\n  span {\n    color: rgba(0, 0, 0, .45);\n    display: inline-block;\n    font-size: .95rem;\n    line-height: 42px;\n    margin-bottom: 4px;\n  }\n  p {\n    line-height: 42px;\n    margin: 0;\n    a {\n      font-weight: 600;\n      font-size: 1rem;\n    }\n  }\n}\n", {"version": 3, "sources": ["IndexChart.vue"], "names": [], "mappings": ";AA6QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-row :gutter=\"24\">\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"总销售额\" total=\"￥126,560\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <trend flag=\"up\" style=\"margin-right: 16px;\">\n              <span slot=\"term\">周同比</span>\n              12%\n            </trend>\n            <trend flag=\"down\">\n              <span slot=\"term\">日同比</span>\n              11%\n            </trend>\n          </div>\n          <template slot=\"footer\">日均销售额<span>￥ 234.56</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"订单量\" :total=\"8846 | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-area />\n          </div>\n          <template slot=\"footer\">日订单量<span> {{ '1234' | NumberFormat }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"支付笔数\" :total=\"6560 | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-bar :height=\"40\" />\n          </div>\n          <template slot=\"footer\">转化率 <span>60%</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"运营活动效果\" total=\"78%\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-progress color=\"rgb(19, 194, 194)\" :target=\"80\" :percentage=\"78\" :height=\"8\" />\n          </div>\n          <template slot=\"footer\">\n            <trend flag=\"down\" style=\"margin-right: 16px;\">\n              <span slot=\"term\">同周比</span>\n              12%\n            </trend>\n            <trend flag=\"up\">\n              <span slot=\"term\">日环比</span>\n              80%\n            </trend>\n          </template>\n        </chart-card>\n      </a-col>\n    </a-row>\n\n    <a-card :loading=\"loading\" :bordered=\"false\" :body-style=\"{padding: '0'}\">\n      <div class=\"salesCard\">\n        <a-tabs default-active-key=\"1\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n          <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n            <div class=\"extra-item\">\n              <a>今日</a>\n              <a>本周</a>\n              <a>本月</a>\n              <a>本年</a>\n            </div>\n            <a-range-picker :style=\"{width: '256px'}\" />\n          </div>\n          <a-tab-pane loading=\"true\" tab=\"销售额\" key=\"1\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <bar title=\"销售额排行\" :dataSource=\"barData\"/>\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\"/>\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n          <a-tab-pane tab=\"销售趋势\" key=\"2\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <bar title=\"销售额趋势\" :dataSource=\"barData\"/>\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\"/>\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n        </a-tabs>\n      </div>\n    </a-card>\n\n    <a-row>\n      <a-col :span=\"24\">\n        <a-card :loading=\"loading\" :bordered=\"false\" title=\"最近一周访问量统计\" :style=\"{ marginTop: '24px' }\">\n          <a-row>\n            <a-col :span=\"6\">\n              <head-info title=\"今日IP\" :content=\"loginfo.todayIp\"></head-info>\n            </a-col>\n            <a-col :span=\"2\">\n              <a-spin class='circle-cust'>\n                <a-icon slot=\"indicator\" type=\"environment\" style=\"font-size: 24px\"  />\n              </a-spin>\n            </a-col>\n            <a-col :span=\"6\">\n              <head-info title=\"今日访问\" :content=\"loginfo.todayVisitCount\"></head-info>\n            </a-col>\n            <a-col :span=\"2\">\n              <a-spin class='circle-cust'>\n                <a-icon slot=\"indicator\" type=\"team\" style=\"font-size: 24px\"  />\n              </a-spin>\n            </a-col>\n            <a-col :span=\"6\">\n              <head-info title=\"总访问量\" :content=\"loginfo.totalVisitCount\"></head-info>\n            </a-col>\n            <a-col :span=\"2\">\n              <a-spin class='circle-cust'>\n                <a-icon slot=\"indicator\" type=\"rise\" style=\"font-size: 24px\"  />\n              </a-spin>\n            </a-col>\n          </a-row>\n          <line-chart-multid :fields=\"visitFields\" :dataSource=\"visitInfo\"></line-chart-multid>\n        </a-card>\n      </a-col>\n    </a-row>\n  </div>\n</template>\n\n<script>\n  import ChartCard from '@/components/ChartCard'\n  import ACol from \"ant-design-vue/es/grid/Col\"\n  import ATooltip from \"ant-design-vue/es/tooltip/Tooltip\"\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniBar from '@/components/chart/MiniBar'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import RankList from '@/components/chart/RankList'\n  import Bar from '@/components/chart/Bar'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import HeadInfo from '@/components/tools/HeadInfo.vue'\n\n  import Trend from '@/components/Trend'\n  import { getLoginfo,getVisitInfo } from '@/api/api'\n\n  const rankList = []\n  for (let i = 0; i < 7; i++) {\n    rankList.push({\n      name: '白鹭岛 ' + (i+1) + ' 号店',\n      total: 1234.56 - i * 100\n    })\n  }\n  const barData = []\n  for (let i = 0; i < 12; i += 1) {\n    barData.push({\n      x: `${i + 1}月`,\n      y: Math.floor(Math.random() * 1000) + 200\n    })\n  }\n  export default {\n    name: \"IndexChart\",\n    components: {\n      ATooltip,\n      ACol,\n      ChartCard,\n      MiniArea,\n      MiniBar,\n      MiniProgress,\n      RankList,\n      Bar,\n      Trend,\n      LineChartMultid,\n      HeadInfo\n    },\n    data() {\n      return {\n        loading: true,\n        center: null,\n        rankList,\n        barData,\n        loginfo:{},\n        visitFields:['ip','visit'],\n        visitInfo:[],\n        indicator: <a-icon type=\"loading\" style=\"font-size: 24px\" spin />\n      }\n    },\n    created() {\n      setTimeout(() => {\n        this.loading = !this.loading\n      }, 1000)\n      this.initLogInfo();\n    },\n    methods: {\n      initLogInfo () {\n        getLoginfo(null).then((res)=>{\n          if(res.success){\n            Object.keys(res.result).forEach(key=>{\n              res.result[key] =res.result[key]+\"\"\n            })\n            this.loginfo = res.result;\n          }\n        })\n        getVisitInfo().then(res=>{\n          if(res.success){\n             console.log(\"aaaaaa\",res.result)\n             this.visitInfo = res.result;\n           }\n         })\n      },\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .circle-cust{\n    position: relative;\n    top: 28px;\n    left: -100%;\n  }\n  .extra-wrapper {\n    line-height: 55px;\n    padding-right: 24px;\n\n    .extra-item {\n      display: inline-block;\n      margin-right: 24px;\n\n      a {\n        margin-left: 24px;\n      }\n    }\n  }\n\n  /* 首页访问量统计 */\n  .head-info {\n    position: relative;\n    text-align: left;\n    padding: 0 32px 0 0;\n    min-width: 125px;\n\n    &.center {\n      text-align: center;\n      padding: 0 32px;\n    }\n\n    span {\n      color: rgba(0, 0, 0, .45);\n      display: inline-block;\n      font-size: .95rem;\n      line-height: 42px;\n      margin-bottom: 4px;\n    }\n    p {\n      line-height: 42px;\n      margin: 0;\n      a {\n        font-weight: 600;\n        font-size: 1rem;\n      }\n    }\n  }\n</style>"]}]}