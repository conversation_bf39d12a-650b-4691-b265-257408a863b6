{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import STable from '@/components/table/';\nimport ATextarea from \"ant-design-vue/es/input/TextArea\";\nimport AInput from \"ant-design-vue/es/input/Input\";\nimport moment from \"moment\";\nimport axios from 'axios';\nimport { getRoleList, getServiceList } from '@/api/manage';\nexport default {\n  name: \"TableList\",\n  components: {\n    AInput: AInput,\n    ATextarea: ATextarea,\n    STable: STable\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      visibleCreateModal: false,\n      visible: false,\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 12\n        }\n      },\n      form: null,\n      mdl: {},\n      // 高级搜索 展开/关闭\n      advanced: true,\n      // 查询参数\n      queryParam: {},\n      // 表头\n      columns: [{\n        title: '规则编号',\n        dataIndex: 'no'\n      }, {\n        title: '描述',\n        dataIndex: 'description'\n      }, {\n        title: '服务调用次数',\n        dataIndex: 'callNo',\n        sorter: true,\n        needTotal: true,\n        customRender: function customRender(text) {\n          return text + ' 次';\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        needTotal: true\n      }, {\n        title: '更新时间',\n        dataIndex: 'updatedAt',\n        sorter: true\n      }, {\n        table: '操作',\n        dataIndex: 'action',\n        width: '150px',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 加载数据方法 必须为 Promise 对象\n      loadData: function loadData(parameter) {\n        return getServiceList(Object.assign(parameter, _this.queryParam)).then(function (res) {\n          return res.result;\n        });\n      },\n      selectedRowKeys: [],\n      selectedRows: []\n    };\n  },\n  created: function created() {\n    getRoleList({\n      t: new Date()\n    });\n  },\n  methods: {\n    handleEdit: function handleEdit(record) {\n      this.mdl = Object.assign({}, record);\n      console.log(this.mdl);\n      this.visible = true;\n    },\n    handleOk: function handleOk() {},\n    //添加逻辑\n    handleModalVisible: function handleModalVisible(isVisible) {\n      this.visibleCreateModal = isVisible;\n    },\n    handleCreateModalOk: function handleCreateModalOk() {\n      var _this2 = this;\n      this.createForm.validateFields(function (err, fieldsValue) {\n        if (err) {\n          return;\n        }\n        var description = _this2.createForm.getFieldValue('description');\n        axios.post('/saveRule', {\n          desc: description\n        }).then(function (res) {\n          _this2.createForm.resetFields();\n          _this2.visibleCreateModal = false;\n          _this2.loadRuleData();\n        });\n      });\n    },\n    handleCreateModalCancel: function handleCreateModalCancel() {\n      this.visibleCreateModal = false;\n    },\n    onChange: function onChange(row) {\n      this.selectedRowKeys = row.selectedRowKeys;\n      this.selectedRows = row.selectedRows;\n      console.log(this.$refs.table);\n    },\n    toggleAdvanced: function toggleAdvanced() {\n      this.advanced = !this.advanced;\n    },\n    resetSearchForm: function resetSearchForm() {\n      this.queryParam = {\n        date: moment(new Date())\n      };\n    }\n  },\n  watch: {\n    /*\n    'selectedRows': function (selectedRows) {\n      this.needTotalList = this.needTotalList.map(item => {\n        return {\n          ...item,\n          total: selectedRows.reduce( (sum, val) => {\n            return sum + val[item.dataIndex]\n          }, 0)\n        }\n      })\n    }\n    */\n  }\n};", {"version": 3, "names": ["STable", "ATextarea", "AInput", "moment", "axios", "getRoleList", "getServiceList", "name", "components", "data", "_this", "visibleCreateModal", "visible", "labelCol", "xs", "span", "sm", "wrapperCol", "form", "mdl", "advanced", "queryParam", "columns", "title", "dataIndex", "sorter", "needTotal", "customRender", "text", "table", "width", "scopedSlots", "loadData", "parameter", "Object", "assign", "then", "res", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "created", "t", "Date", "methods", "handleEdit", "record", "console", "log", "handleOk", "handleModalVisible", "isVisible", "handleCreateModalOk", "_this2", "createForm", "validateFields", "err", "fieldsValue", "description", "getFieldValue", "post", "desc", "resetFields", "loadRuleData", "handleCreateModalCancel", "onChange", "row", "$refs", "toggleAdvanced", "resetSearchForm", "date", "watch"], "sources": ["src/views/list/TableList.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"48\">\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"规则编号\">\n              <a-input v-model=\"queryParam.id\" placeholder=\"\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"使用状态\">\n              <a-select v-model=\"queryParam.status\" placeholder=\"请选择\" default-value=\"0\">\n                <a-select-option value=\"0\">全部</a-select-option>\n                <a-select-option value=\"1\">关闭</a-select-option>\n                <a-select-option value=\"2\">运行中</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"advanced\">\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"调用次数\">\n                <a-input-number v-model=\"queryParam.callNo\" style=\"width: 100%\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"更新日期\">\n                <a-date-picker v-model=\"queryParam.date\" style=\"width: 100%\" placeholder=\"请输入更新日期\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select v-model=\"queryParam.useStatus\" placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :md=\"!advanced && 8 || 24\" :sm=\"24\">\n            <span class=\"table-page-search-submitButtons\" :style=\"advanced && { float: 'right', overflow: 'hidden' } || {} \">\n              <a-button type=\"primary\">查询</a-button>\n              <a-button style=\"margin-left: 8px\" @click=\"resetSearchForm\">重置</a-button>\n              <a @click=\"toggleAdvanced\" style=\"margin-left: 8px\">\n                {{ advanced ? '收起' : '展开' }}\n                <a-icon :type=\"advanced ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <div class=\"table-operator\">\n      <a-button type=\"primary\" icon=\"plus\" @click=\"() => this.handleModalVisible(true)\">新建</a-button>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\"><a-icon type=\"delete\" />删除</a-menu-item>\n          <!-- lock | unlock -->\n          <a-menu-item key=\"2\"><a-icon type=\"lock\" />锁定</a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作 <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <s-table\n      ref=\"table\"\n      size=\"default\"\n      :columns=\"columns\"\n      :data=\"loadData\"\n      :showAlertInfo=\"true\"\n      @onSelect=\"onChange\"\n    >\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">\n            更多 <a-icon type=\"down\" />\n          </a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a href=\"javascript:;\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">禁用</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">删除</a>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </s-table>\n\n    <a-modal\n      title=\"操作\"\n      :width=\"800\"\n      v-model=\"visible\"\n      @ok=\"handleOk\"\n    >\n      <a-form :autoFormCreate=\"(form)=>{this.form = form}\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"规则编号\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input placeholder=\"规则编号\" v-model=\"mdl.no\" id=\"no\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"服务调用次数\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input-number :min=\"1\" id=\"callNo\" v-model=\"mdl.callNo\" style=\"width: 100%\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"状态\"\n          hasFeedback\n          validateStatus=\"warning\"\n        >\n          <a-select defaultValue=\"1\" v-model=\"mdl.status\">\n            <a-select-option value=\"1\">Option 1</a-select-option>\n            <a-select-option value=\"2\">Option 2</a-select-option>\n            <a-select-option value=\"3\">Option 3</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"描述\"\n          hasFeedback\n          help=\"请填写一段描述\"\n        >\n          <a-textarea :rows=\"5\" v-model=\"mdl.description\" placeholder=\"...\" id=\"description\"/>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"更新时间\"\n          hasFeedback\n          validateStatus=\"error\"\n        >\n          <a-date-picker\n            style=\"width: 100%\"\n            showTime\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            placeholder=\"Select Time\"\n          />\n        </a-form-item>\n\n      </a-form>\n    </a-modal>\n\n    <a-modal title=\"新建规则\" destroyOnClose :visible=\"visibleCreateModal\" @ok=\"handleCreateModalOk\" @cancel=\"handleCreateModalCancel\">\n      <!---->\n      <a-form style=\"margin-top: 8px\" :autoFormCreate=\"(form)=>{this.createForm = form}\">\n        <a-form-item :labelCol=\"{ span: 5 }\" :wrapperCol=\"{ span: 15 }\" label=\"描述\" fieldDecoratorId=\"description\" :fieldDecoratorOptions=\"{rules: [{ required: true, message: '请输入至少五个字符的规则描述！', min: 5 }]}\">\n          <a-input placeholder=\"请输入\" />\n        </a-form-item>\n      </a-form>\n    </a-modal>\n\n  </a-card>\n</template>\n\n<script>\n  import STable from '@/components/table/'\n  import ATextarea from \"ant-design-vue/es/input/TextArea\"\n  import AInput from \"ant-design-vue/es/input/Input\"\n  import moment from \"moment\"\n  import axios from 'axios';\n  import { getRoleList, getServiceList } from '@/api/manage'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      AInput,\n      ATextarea,\n      STable\n    },\n    data () {\n      return {\n        visibleCreateModal:false,\n        visible: false,\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 12 },\n        },\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: true,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '规则编号',\n            dataIndex: 'no'\n          },\n          {\n            title: '描述',\n            dataIndex: 'description'\n          },\n          {\n            title: '服务调用次数',\n            dataIndex: 'callNo',\n            sorter: true,\n            needTotal: true,\n            customRender: (text) => text + ' 次'\n          },\n          {\n            title: '状态',\n            dataIndex: 'status',\n            needTotal: true\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updatedAt',\n            sorter: true\n          },\n          {\n            table: '操作',\n            dataIndex: 'action',\n            width: '150px',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return getServiceList(Object.assign(parameter, this.queryParam))\n            .then(res => {\n              return res.result\n            })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    created () {\n      getRoleList({ t: new Date()})\n    },\n    methods: {\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n\n      },\n\n      //添加逻辑\n      handleModalVisible(isVisible) {\n        this.visibleCreateModal = isVisible;\n      },\n      handleCreateModalOk() {\n        this.createForm.validateFields((err, fieldsValue) => {\n          if (err) {\n            return;\n          }\n          const description = this.createForm.getFieldValue('description');\n          axios.post('/saveRule', {\n            desc: description,\n          }).then((res) => {\n            this.createForm.resetFields();\n            this.visibleCreateModal = false;\n            this.loadRuleData();\n          });\n        });\n      },\n      handleCreateModalCancel() {\n        this.visibleCreateModal = false;\n      },\n\n      onChange (row) {\n        this.selectedRowKeys = row.selectedRowKeys\n        this.selectedRows = row.selectedRows\n\n        console.log(this.$refs.table)\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n\n      resetSearchForm () {\n        this.queryParam = {\n          date: moment(new Date())\n        }\n      }\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n</script>"], "mappings": "AA8LA,OAAAA,MAAA;AACA,OAAAC,SAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,KAAA;AACA,SAAAC,WAAA,EAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,MAAA,EAAAA,MAAA;IACAD,SAAA,EAAAA,SAAA;IACAD,MAAA,EAAAA;EACA;EACAS,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,kBAAA;MACAC,OAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAG,IAAA;MACAC,GAAA;MAEA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UAAA,OAAAA,IAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAE,SAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,MAAA;MACA,GACA;QACAI,KAAA;QACAL,SAAA;QACAM,KAAA;QACAC,WAAA;UAAAJ,YAAA;QAAA;MACA,EACA;MACA;MACAK,QAAA,WAAAA,SAAAC,SAAA;QACA,OAAA3B,cAAA,CAAA4B,MAAA,CAAAC,MAAA,CAAAF,SAAA,EAAAvB,KAAA,CAAAW,UAAA,GACAe,IAAA,WAAAC,GAAA;UACA,OAAAA,GAAA,CAAAC,MAAA;QACA;MACA;MAEAC,eAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACApC,WAAA;MAAAqC,CAAA,MAAAC,IAAA;IAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,MAAA;MACA,KAAA3B,GAAA,GAAAe,MAAA,CAAAC,MAAA,KAAAW,MAAA;MACAC,OAAA,CAAAC,GAAA,MAAA7B,GAAA;MACA,KAAAP,OAAA;IACA;IACAqC,QAAA,WAAAA,SAAA,GAEA;IAEA;IACAC,kBAAA,WAAAA,mBAAAC,SAAA;MACA,KAAAxC,kBAAA,GAAAwC,SAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,UAAA,CAAAC,cAAA,WAAAC,GAAA,EAAAC,WAAA;QACA,IAAAD,GAAA;UACA;QACA;QACA,IAAAE,WAAA,GAAAL,MAAA,CAAAC,UAAA,CAAAK,aAAA;QACAvD,KAAA,CAAAwD,IAAA;UACAC,IAAA,EAAAH;QACA,GAAAtB,IAAA,WAAAC,GAAA;UACAgB,MAAA,CAAAC,UAAA,CAAAQ,WAAA;UACAT,MAAA,CAAA1C,kBAAA;UACA0C,MAAA,CAAAU,YAAA;QACA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAA;MACA,KAAArD,kBAAA;IACA;IAEAsD,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAA3B,eAAA,GAAA2B,GAAA,CAAA3B,eAAA;MACA,KAAAC,YAAA,GAAA0B,GAAA,CAAA1B,YAAA;MAEAO,OAAA,CAAAC,GAAA,MAAAmB,KAAA,CAAAtC,KAAA;IACA;IACAuC,cAAA,WAAAA,eAAA;MACA,KAAAhD,QAAA,SAAAA,QAAA;IACA;IAEAiD,eAAA,WAAAA,gBAAA;MACA,KAAAhD,UAAA;QACAiD,IAAA,EAAAnE,MAAA,KAAAwC,IAAA;MACA;IACA;EACA;EACA4B,KAAA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXA;AAaA", "ignoreList": []}]}