{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue?vue&type=template&id=0c70ee28&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue", "mtime": 1753243085891}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"notice-marquee-wrapper\" v-if=\"showMarquee\">\n  <div class=\"notice-icon\">\n    <a-icon type=\"notification\" theme=\"filled\" />\n  </div>\n  <div class=\"notice-content\">\n    <a-carousel\n      :autoplay=\"true\"\n      vertical\n      :dots=\"false\"\n      :autoplaySpeed=\"rotateSpeed\"\n    >\n      <div v-for=\"(notice, index) in validNotices\" :key=\"index\" class=\"notice-item\">\n        <div class=\"notice-main-content\">\n          <div class=\"notice-title-line\">\n            <a @click=\"showNoticeDetail(notice)\" :title=\"notice.title\">\n              {{ notice.title }}\n            </a>\n            <span v-if=\"notice.priority === 'L'\" class=\"notice-tag normal\">一般</span>\n            <span v-if=\"notice.priority === 'M'\" class=\"notice-tag important\">重要</span>\n            <span v-if=\"notice.priority === 'H'\" class=\"notice-tag urgent\">紧急</span>\n          </div>\n          \n          <!-- 添加公告摘要显示 -->\n          <div v-if=\"notice.msgAbstract\" class=\"notice-abstract\">{{ notice.msgAbstract }}</div>\n        </div>\n      </div>\n    </a-carousel>\n  </div>\n  <div class=\"more-link\">\n    <a @click=\"showMoreNotices\">更多</a>\n  </div>\n</div>\n", null]}