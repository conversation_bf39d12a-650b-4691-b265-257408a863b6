{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue?vue&type=template&id=7035909e&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n  <a-card class=\"card\" title=\"仓库管理\" :bordered=\"false\">\n    <repository-form ref=\"repository\" :showSubmit=\"false\" />\n  </a-card>\n  <a-card class=\"card\" title=\"任务管理\" :bordered=\"false\">\n    <task-form ref=\"task\" :showSubmit=\"false\" />\n  </a-card>\n\n  <!-- table -->\n  <a-card>\n    <form :autoFormCreate=\"(form) => this.form = form\">\n      <a-table\n        :columns=\"columns\"\n        :dataSource=\"data\"\n        :pagination=\"false\"\n      >\n        <template v-for=\"(col, i) in ['name', 'workId', 'department']\" :slot=\"col\" slot-scope=\"text, record, index\">\n          <a-input\n            :key=\"col\"\n            v-if=\"record.editable\"\n            style=\"margin: -5px 0\"\n            :value=\"text\"\n            :placeholder=\"columns[i].title\"\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\n          />\n          <template v-else>{{ text }}</template>\n        </template>\n        <template slot=\"operation\" slot-scope=\"text, record, index\">\n          <template v-if=\"record.editable\">\n            <span v-if=\"record.isNew\">\n              <a @click=\"saveRow(record.key)\">添加</a>\n              <a-divider type=\"vertical\" />\n              <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </span>\n            <span v-else>\n              <a @click=\"saveRow(record.key)\">保存</a>\n              <a-divider type=\"vertical\" />\n              <a @click=\"cancel(record.key)\">取消</a>\n            </span>\n          </template>\n          <span v-else>\n            <a @click=\"toggle(record.key)\">编辑</a>\n            <a-divider type=\"vertical\" />\n            <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n              <a>删除</a>\n            </a-popconfirm>\n          </span>\n        </template>\n      </a-table>\n      <a-button style=\"width: 100%; margin-top: 16px; margin-bottom: 8px\" type=\"dashed\" icon=\"plus\" @click=\"newMember\">新增成员</a-button>\n    </form>\n  </a-card>\n\n  <!-- fixed footer toolbar -->\n  <footer-tool-bar>\n    <a-button type=\"primary\" @click=\"validate\" :loading=\"loading\">提交</a-button>\n  </footer-tool-bar>\n</div>\n", null]}