{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { triggerWindowResizeEvent } from '@/utils/util';\nexport default {\n  name: 'Bar',\n  props: {\n    dataSource: {\n      type: Array,\n      required: true\n    },\n    yaxisText: {\n      type: String,\n      default: 'y'\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    height: {\n      type: Number,\n      default: 254\n    }\n  },\n  data: function data() {\n    return {\n      padding: ['auto', 'auto', '40', '50']\n    };\n  },\n  computed: {\n    scale: function scale() {\n      return [{\n        dataKey: 'y',\n        alias: this.yaxisText\n      }];\n    }\n  },\n  mounted: function mounted() {\n    triggerWindowResizeEvent();\n  }\n};", {"version": 3, "names": ["triggerWindowResizeEvent", "name", "props", "dataSource", "type", "Array", "required", "yaxisText", "String", "default", "title", "height", "Number", "data", "padding", "computed", "scale", "dataKey", "alias", "mounted"], "sources": ["src/components/chart/Bar.vue"], "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :forceFit=\"true\" :height=\"height\" :data=\"dataSource\" :scale=\"scale\" :padding=\"padding\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { triggerWindowResizeEvent } from '@/utils/util'\n\n  export default {\n    name: 'Bar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true\n      },\n      yaxisText: {\n        type: String,\n        default: 'y'\n      },\n      title: {\n        type: String,\n        default: ''\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return { padding: ['auto', 'auto', '40', '50'] }\n    },\n    computed: {\n      scale() {\n        return [{\n          dataKey: 'y',\n          alias: this.yaxisText\n        }]\n      }\n    },\n    mounted() {\n      triggerWindowResizeEvent()\n    }\n  }\n</script>"], "mappings": "AAYA,SAAAA,wBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAN,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAP,IAAA,EAAAQ,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MAAAC,OAAA;IAAA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA;QACAC,OAAA;QACAC,KAAA,OAAAX;MACA;IACA;EACA;EACAY,OAAA,WAAAA,QAAA;IACAnB,wBAAA;EACA;AACA", "ignoreList": []}]}