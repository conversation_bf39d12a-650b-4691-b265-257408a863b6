{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue?vue&type=style&index=0&id=d2093876&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n\n.avatar-upload-wrapper {\n  height: 200px;\n  width: 100%;\n}\n\n.unit{\n  width: 50px;\n  height: 0;\n  position: relative;\n  display: block;\n  .flag{\n      display: block;\n      width: 64px;\n      height: 64px;\n      background: url(\"/img/position.png\") no-repeat;\n      background-size: 64px 64px;\n      // margin: 10px 0;\n      // border-radius: 0px 18px 31px 18px;\n      // transform: rotate(225deg);\n      // background: radial-gradient(#aedbe6, #57b0f3d4, #128fec);;\n      // -webkit-box-shadow:rgba(66,140,240,0.5) 0px 10px 16px;\n  }\n  .unit-title{\n      background-color: #52c41ab3;\n      display: block; \n      width: fit-content;\n      padding: .4em .6em .3em;\n      font-size: 75%;\n      font-weight: 700;\n      line-height: 1;\n      color: #fff;\n      text-align: center;\n      white-space: nowrap;\n      vertical-align: baseline;\n      border-radius: .25em;\n  }\n}\n\n.ant-upload-preview {\n  position: relative;\n  margin: 0 auto;\n  width: 100%;\n  max-width: 180px;\n  border-radius: 50%;\n  box-shadow: 0 0 4px #ccc;\n\n  .upload-icon {\n    position: absolute;\n    top: 0;\n    right: 10px;\n    font-size: 1.4rem;\n    padding: 0.5rem;\n    background: rgba(222, 221, 221, 0.7);\n    border-radius: 50%;\n    border: 1px solid rgba(0, 0, 0, 0.2);\n  }\n  .mask {\n    opacity: 0;\n    position: absolute;\n    background: rgba(0,0,0,0.4);\n    cursor: pointer;\n    transition: opacity 0.4s;\n\n    &:hover {\n      opacity: 1;\n    }\n\n    i {\n      font-size: 2rem;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      margin-left: -1rem;\n      margin-top: -1rem;\n      color: #d6d6d6;\n    }\n  }\n\n  img, .mask {\n    width: 100%;\n    max-width: 180px;\n    height: 100%;\n    border-radius: 50%;\n    overflow: hidden;\n  }\n}\n", {"version": 3, "sources": ["CourseUnitMap.vue"], "names": [], "mappings": ";;AAqLA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CourseUnitMap.vue", "sourceRoot": "src/views/account/course", "sourcesContent": ["<template>\n  <div class=\"account-settings-info-view\">\n      <div :style=\"{background: 'url(' + getFileAccessHttpUrl(courseInfo.courseMap) + ') no-repeat', backgroundSize:'auto', height: '1000px'}\">\n        <div v-for=\"unit in unitList\" :key=\"unit.id\" class=\"unit\" \n          :style=\"{left: unit.mapX-25+'px', top: unit.mapY-25+'px'}\"\n          @click=\"viewUnit(unit)\">\n            <i class=\"flag\"></i>\n            <div class=\"unit-title\">{{unit.unitName}}</div>\n        </div>\n        <unitView-modal ref=\"unitViewModal\"/>\n      </div>\n  </div>\n</template>\n\n<script>\n\n import { getAction,getFileAccessHttpUrl } from '@/api/manage'\n import UnitViewModal from './modules/UnitViewModal'\n  export default {\n    components: {\n        UnitViewModal\n    },\n    data () {\n      return {\n        // cropper\n        preview: {},\n        option: {\n          img: '/avatar2.jpg',\n          info: true,\n          size: 1,\n          outputType: 'jpeg',\n          canScale: false,\n          autoCrop: true,\n          // 只有自动截图开启 宽度高度才生效\n          autoCropWidth: 180,\n          autoCropHeight: 180,\n          fixedBox: true,\n          // 开启宽度和高度比例\n          fixed: true,\n          fixedNumber: [1, 1]\n        },\n        courseInfo: {},\n        unitList:[],\n        url:{\n            courseInfo: \"/teaching/teachingCourse/queryById\",\n            unitList: \"/teaching/teachingCourseUnit/mineUnit\"\n        },\n        visible : false,\n        unit: {}\n      }\n    },\n    created(){\n        let courseId = this.$route.query.id\n        console.log(\"courseId\"+courseId)\n        if(courseId){\n            this.getCourseInfo(courseId)\n            this.getUnitList(courseId)\n        }\n    },\n    methods: {\n        getFileAccessHttpUrl,\n        getCourseInfo(courseId){\n            getAction(this.url.courseInfo, {id: courseId}).then(res=>{\n                console.log(res)\n                if(res.success){\n                  this.courseInfo = res.result\n                  this.$route.meta.title = \"我的课程-\" + this.courseInfo.courseName\n                  this.courseInfo.map = this.getFileAccessHttpUrl(this.courseInfo.map)\n                }else{\n                  this.$message.error(res.message)\n                }\n            })\n        },\n        getUnitList(courseId){\n            getAction(this.url.unitList, {courseId: courseId, pageNo: 1, pageSize:99}).then(res=>{\n                console.log(res)\n                if(res.success){\n                  this.unitList = res.result.records\n                }else{\n                  this.$message.error(res.message)\n                }\n            })\n        },\n        viewUnit(unit){\n          this.$refs.unitViewModal.visible = true;\n          this.$refs.unitViewModal.unit = unit\n        },\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n\n  .avatar-upload-wrapper {\n    height: 200px;\n    width: 100%;\n  }\n\n  .unit{\n    width: 50px;\n    height: 0;\n    position: relative;\n    display: block;\n    .flag{\n        display: block;\n        width: 64px;\n        height: 64px;\n        background: url(\"/img/position.png\") no-repeat;\n        background-size: 64px 64px;\n        // margin: 10px 0;\n        // border-radius: 0px 18px 31px 18px;\n        // transform: rotate(225deg);\n        // background: radial-gradient(#aedbe6, #57b0f3d4, #128fec);;\n        // -webkit-box-shadow:rgba(66,140,240,0.5) 0px 10px 16px;\n    }\n    .unit-title{\n        background-color: #52c41ab3;\n        display: block; \n        width: fit-content;\n        padding: .4em .6em .3em;\n        font-size: 75%;\n        font-weight: 700;\n        line-height: 1;\n        color: #fff;\n        text-align: center;\n        white-space: nowrap;\n        vertical-align: baseline;\n        border-radius: .25em;\n    }\n  }\n\n  .ant-upload-preview {\n    position: relative;\n    margin: 0 auto;\n    width: 100%;\n    max-width: 180px;\n    border-radius: 50%;\n    box-shadow: 0 0 4px #ccc;\n\n    .upload-icon {\n      position: absolute;\n      top: 0;\n      right: 10px;\n      font-size: 1.4rem;\n      padding: 0.5rem;\n      background: rgba(222, 221, 221, 0.7);\n      border-radius: 50%;\n      border: 1px solid rgba(0, 0, 0, 0.2);\n    }\n    .mask {\n      opacity: 0;\n      position: absolute;\n      background: rgba(0,0,0,0.4);\n      cursor: pointer;\n      transition: opacity 0.4s;\n\n      &:hover {\n        opacity: 1;\n      }\n\n      i {\n        font-size: 2rem;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        margin-left: -1rem;\n        margin-top: -1rem;\n        color: #d6d6d6;\n      }\n    }\n\n    img, .mask {\n      width: 100%;\n      max-width: 180px;\n      height: 100%;\n      border-radius: 50%;\n      overflow: hidden;\n    }\n  }\n</style>"]}]}