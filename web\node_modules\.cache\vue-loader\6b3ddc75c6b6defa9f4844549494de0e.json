{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue?vue&type=template&id=f3631116&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue", "mtime": 1750830741902}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n    <div class=\"panel-works\">\n      <h1 v-if=\"type==0\" class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"fire\" theme=\"twoTone\" two-tone-color=\"#eb2f96\" />\n          最火作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div v-if=\"type==0\" class=\"title-separator\"></div>\n      \n      <h1 v-if=\"type==1\" class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"clock-circle\" theme=\"twoTone\" />\n          最新作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div v-if=\"type==1\" class=\"title-separator\"></div>\n      \n      <!-- 筛选下拉框，移到标题和作品之间 -->\n      <div v-if=\"type==1\" class=\"filter-wrapper\">\n        <a-select \n          :value=\"sortOption\" \n          @change=\"handleSortChange\" \n          size=\"default\"\n          class=\"filter-select\"\n        >\n          <a-select-option value=\"time\">按最新时间</a-select-option>\n          <a-select-option value=\"view\">按观看数</a-select-option>\n          <a-select-option value=\"star\">按点赞数</a-select-option>\n        </a-select>\n      </div>\n      \n      <h1 v-if=\"type==2\" class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"like\" theme=\"twoTone\" two-tone-color=\"#52c41a\" />\n          最赞作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div v-if=\"type==2\" class=\"title-separator\"></div>\n      \n      <h1 v-if=\"type==3\" class=\"panel-title title-container\">\n        <div class=\"title-decoration left\">\n          <div class=\"decoration-circle\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-star\"></div>\n        </div>\n        <span class=\"title-text\">\n          <a-icon type=\"star\" theme=\"twoTone\" two-tone-color=\"#ffd81b\" />\n          精选作品\n        </span>\n        <div class=\"title-decoration right\">\n          <div class=\"decoration-star\"></div>\n          <div class=\"decoration-line\"></div>\n          <div class=\"decoration-circle\"></div>\n        </div>\n      </h1>\n      <div v-if=\"type==3\" class=\"title-separator\"></div>\n      \n      <div class=\"works-container\" :style=\"{ minHeight: containerMinHeight + 'px' }\">\n        <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n          <a-col v-for=\"(item, index) in datasource\" :key=\"item.id || index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n            <transition name=\"fade\" appear>\n              <a-card class=\"work-card\" :style=\"{ animationDelay: index * 50 + 'ms' }\">\n                <a @click=\"toDetail(item.id)\" target=\"_blank\">\n                  <img class=\"work-cover\" v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n                  <img v-if=\"item.workType == 4 || item.workType == 5 || item.workType == 10\" src=\"@/assets/code.png\" alt=\"\" />\n                </a>\n                <div class=\"work-info\">\n                  <div class=\"work-stats-row\">\n                    <div class=\"stats-left\">\n                      <a-icon type=\"eye\" /> {{ item.viewNum }} \n                      <a-divider type=\"vertical\"></a-divider>\n                      <a-icon type=\"like\" /> {{ item.starNum }}\n                    </div>\n                    <div class=\"stats-right\">\n                      <a-tag class=\"language-tag\">{{ item.workType_dictText }}</a-tag>\n                    </div>\n                  </div>\n                  <p>{{ item.workName }}</p>\n                  <a-row class=\"work-author\">\n                    <a-col :span=\"6\">\n                      <a-avatar shape=\"square\" class=\"avatar\" :size=\"40\" :src=\"item.avatar_url\" />\n                    </a-col>\n                    <a-col :span=\"18\">\n                      <span>{{ item.realname || item.username }}</span>\n                      <div class=\"work-submit-time\">{{ formatDate(item.createTime) }}</div>\n                    </a-col>\n                  </a-row>\n                </div>\n              </a-card>\n            </transition>\n          </a-col>\n        </a-row>\n        \n        <!-- 骨架屏加载状态 -->\n        <div v-if=\"silentLoading && datasource.length === 0\" class=\"skeleton-container\">\n          <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n            <a-col v-for=\"i in 8\" :key=\"i\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n              <div class=\"skeleton-card\">\n                <div class=\"skeleton-image\"></div>\n                <div class=\"skeleton-content\">\n                  <div class=\"skeleton-stats\"></div>\n                  <div class=\"skeleton-title\"></div>\n                  <div class=\"skeleton-author\"></div>\n                </div>\n              </div>\n            </a-col>\n          </a-row>\n        </div>\n        \n        <a-spin style=\"margin:50px auto;\" v-if=\"loading && !silentLoading\"/>\n        <a-empty v-if=\"!loading && !silentLoading && datasource.length==0\"/>\n        <a-button v-if=\"!loading && datasource.length>0\" class=\"load-more\" type=\"dash\" @click=\"getData\">加载更多……</a-button>\n      </div>\n    </div>\n</div>\n", null]}