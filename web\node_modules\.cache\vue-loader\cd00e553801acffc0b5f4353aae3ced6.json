{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue?vue&type=template&id=2d23c767&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue", "mtime": 1751099917648}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"shopping-modal\">\n  <a-modal\n    :visible=\"visible\"\n    :footer=\"null\"\n    @cancel=\"handleCancel\"\n    width=\"1200px\"\n    :maskClosable=\"true\"\n    :bodyStyle=\"{ padding: '0' }\"\n    class=\"kids-modal\"\n  >\n    <!-- 自定义标题 -->\n    <template #title>\n      <div class=\"modal-title\">\n        <img :src=\"require('@/assets/shopping_img/gift-icon.png')\" alt=\"礼物图标\" class=\"title-small-icon\" />\n        <span>奖品兑换中心</span>\n      </div>\n    </template>\n\n    <div class=\"shopping-container\">\n      <!-- 左侧礼物架区域 -->\n      <div class=\"gift-shelf\">\n        <div class=\"shelf-header\">\n          <!-- <div class=\"shelf-title\">\n            <img :src=\"require('@/assets/shopping_img/gift-icon.png')\" alt=\"礼物图标\" class=\"title-icon\" />\n            <span>奇妙礼物坊</span>\n          </div> -->\n          <div class=\"shelf-subtitle\">用你的金币兑换喜欢的礼物吧！</div>\n        </div>\n        \n        <div class=\"shelf-content\">\n          <!-- 第1行礼物 -->\n          <div class=\"gift-row\" v-if=\"giftRow1 && giftRow1.length > 0\">\n            <div class=\"gift-item\" v-for=\"item in giftRow1\" :key=\"item.id\">\n              <div class=\"gift-box\">\n                <div class=\"gift-ribbon\">\n                  <span>热门</span>\n                </div>\n                <div class=\"gift-image-container\">\n                  <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                  <div class=\"gift-shine\"></div>\n                </div>\n                <div class=\"gift-info\">\n                  <div class=\"gift-name\">{{ item.name }}</div>\n                  <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                  <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <!-- 第1行和第2行之间的分割线 -->\n          <div class=\"divider-line\" v-if=\"giftRow1 && giftRow1.length > 0 && giftRow2 && giftRow2.length > 0\">\n            <div class=\"star-divider\">★</div>\n          </div>\n          \n          <!-- 第2行礼物 -->\n          <div class=\"gift-row\" v-if=\"giftRow2 && giftRow2.length > 0\">\n            <div class=\"gift-item\" v-for=\"item in giftRow2\" :key=\"item.id\">\n              <div class=\"gift-box\">\n                <div class=\"gift-image-container\">\n                  <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                  <div class=\"gift-shine\"></div>\n                </div>\n                <div class=\"gift-info\">\n                  <div class=\"gift-name\">{{ item.name }}</div>\n                  <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                  <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <!-- 第2行和第3行之间的分割线 -->\n          <div class=\"divider-line\" v-if=\"giftRow2 && giftRow2.length > 0 && giftRow3 && giftRow3.length > 0\">\n            <div class=\"star-divider\">★</div>\n          </div>\n          \n          <!-- 第3行礼物 -->\n          <div class=\"gift-row\" v-if=\"giftRow3 && giftRow3.length > 0\">\n            <div class=\"gift-item\" v-for=\"item in giftRow3\" :key=\"item.id\">\n              <div class=\"gift-box\">\n                <div class=\"gift-image-container\">\n                  <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                  <div class=\"gift-shine\"></div>\n                </div>\n                <div class=\"gift-info\">\n                  <div class=\"gift-name\">{{ item.name }}</div>\n                  <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                  <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <!-- 第3行和第4行之间的分割线 -->\n          <div class=\"divider-line\" v-if=\"giftRow3 && giftRow3.length > 0 && giftRow4 && giftRow4.length > 0\">\n            <div class=\"star-divider\">★</div>\n          </div>\n          \n          <!-- 第4行礼物 -->\n          <div class=\"gift-row\" v-if=\"giftRow4 && giftRow4.length > 0\">\n            <div class=\"gift-item\" v-for=\"item in giftRow4\" :key=\"item.id\">\n              <div class=\"gift-box\">\n                <div class=\"gift-image-container\">\n                  <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                  <div class=\"gift-shine\"></div>\n                </div>\n                <div class=\"gift-info\">\n                  <div class=\"gift-name\">{{ item.name }}</div>\n                  <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                  <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <!-- 第4行和第5行之间的分割线 -->\n          <div class=\"divider-line\" v-if=\"giftRow4 && giftRow4.length > 0 && giftRow5 && giftRow5.length > 0\">\n            <div class=\"star-divider\">★</div>\n          </div>\n          \n          <!-- 第5行礼物 -->\n          <div class=\"gift-row\" v-if=\"giftRow5 && giftRow5.length > 0\">\n            <div class=\"gift-item\" v-for=\"item in giftRow5\" :key=\"item.id\">\n              <div class=\"gift-box premium\">\n                <div class=\"gift-ribbon\">\n                  <span>珍稀</span>\n                </div>\n                <div class=\"gift-image-container\">\n                  <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                  <div class=\"gift-shine\"></div>\n                </div>\n                <div class=\"gift-info\">\n                  <div class=\"gift-name\">{{ item.name }}</div>\n                  <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                  <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 右侧用户信息区域 -->\n      <div class=\"user-info\">\n        <div class=\"user-card\">\n          <div class=\"card-decoration top-left\"></div>\n          <div class=\"card-decoration top-right\"></div>\n          <div class=\"card-decoration bottom-left\"></div>\n          <div class=\"card-decoration bottom-right\"></div>\n          \n          <div class=\"user-avatar-box\">\n            <div class=\"avatar-frame\">\n              <img :src=\"userAvatar\" alt=\"账号头像\" class=\"user-avatar\">\n            </div>\n          </div>\n          <div class=\"user-name\">{{ userName }}</div>\n          <div class=\"user-score\">\n            <div class=\"coin-container\">\n              <div class=\"coin-stack\"></div>\n              <span>剩余金币: {{ userScore }}</span>\n            </div>\n          </div>\n          \n          <div class=\"exchange-history\">\n            <div class=\"history-title\">\n              <i class=\"history-icon\"></i>兑换记录\n              <a-button type=\"link\" class=\"more-btn\" @click=\"showAllRecords\">更多</a-button>\n            </div>\n            <div class=\"history-list\">\n              <a-spin :spinning=\"loadingRecords\">\n                <div v-if=\"exchangeRecords && exchangeRecords.length > 0\">\n                  <div v-for=\"(record, index) in exchangeRecords\" :key=\"index\" class=\"history-item\">\n                    <span class=\"gift-name\">{{ record.giftName }}</span>\n                    <span class=\"gift-time\">{{ formatDate(record.exchangeTime) }}</span>\n                    <span class=\"gift-status\" :class=\"record.status === 1 ? 'received' : 'waiting'\">\n                      {{ record.status === 1 ? '已领取' : '待领取' }}\n                    </span>\n                  </div>\n                </div>\n                <div v-else class=\"history-empty\">暂无兑换记录</div>\n              </a-spin>\n            </div>\n          </div>\n          \n          <div class=\"coin-rules\">\n            <a-button type=\"link\" @click=\"showRules\">如何获得更多金币?</a-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n  \n  <!-- 添加兑换记录详情模态框 -->\n  <a-modal\n    :title=\"'礼物兑换记录'\"\n    :visible=\"recordsModalVisible\"\n    @cancel=\"closeRecordsModal\"\n    :footer=\"null\"\n    width=\"800px\"\n    :bodyStyle=\"{ maxHeight: '600px', overflow: 'auto', padding: '24px' }\"\n    class=\"records-modal\"\n  >\n    <a-spin :spinning=\"loadingAllRecords\">\n      <a-table\n        :columns=\"recordColumns\"\n        :dataSource=\"allExchangeRecords\"\n        :pagination=\"pagination\"\n        @change=\"handleTableChange\"\n        rowKey=\"id\"\n        size=\"middle\"\n        :scroll=\"{ y: 450 }\"\n      >\n        <template slot=\"giftName\" slot-scope=\"text\">\n          <span>{{ text }}</span>\n        </template>\n        <template slot=\"exchangeTime\" slot-scope=\"text\">\n          <span>{{ formatDate(text) }}</span>\n        </template>\n        <template slot=\"status\" slot-scope=\"text\">\n          <a-tag :color=\"text === 1 ? 'green' : 'orange'\">\n            {{ text === 1 ? '已领取' : '待领取' }}\n          </a-tag>\n        </template>\n        <template slot=\"receiveTime\" slot-scope=\"text\">\n          <span>{{ text ? formatDate(text) : '-' }}</span>\n        </template>\n      </a-table>\n    </a-spin>\n  </a-modal>\n</div>\n", null]}