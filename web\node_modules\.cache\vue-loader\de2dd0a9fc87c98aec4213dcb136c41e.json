{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue?vue&type=template&id=24a56e6e&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue", "mtime": 1749545283056}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"footer-container\">\n  <div class=\"footer-content\">\n    <div class=\"footer-left\">\n      <div class=\"brand\">\n        <h3>CFish科技工作室</h3>\n        <p>科技少儿编程教学平台</p>\n      </div>\n      <div class=\"slogan\">赋能未来 · 智创童年</div>\n    </div>\n    \n    <div class=\"footer-right\">\n      <div class=\"contact-info\">\n        <a-icon type=\"phone\" /> 联系电话: 19866725905\n        <a-icon type=\"mail\" style=\"margin-left: 15px\" /> 邮箱: <EMAIL>\n      </div>\n      <div class=\"social-media\">\n        <a-popover placement=\"top\" trigger=\"hover\">\n          <template slot=\"content\">\n            <div class=\"qrcode-container\">\n              <img src=\"/img/wechat-qrcode.png\" alt=\"微信二维码\" class=\"qrcode-img\" />\n              <p>微信</p>\n            </div>\n          </template>\n          <a-icon type=\"wechat\" class=\"social-icon\" />\n        </a-popover>\n        \n        <a-popover placement=\"top\" trigger=\"hover\">\n          <template slot=\"content\">\n            <div class=\"qrcode-container\">\n              <img src=\"/img/qq-qrcode.png\" alt=\"QQ二维码\" class=\"qrcode-img\" />\n              <p>QQ</p>\n            </div>\n          </template>\n          <a-icon type=\"qq\" class=\"social-icon\" />\n        </a-popover>\n      </div>\n    </div>\n  </div>\n  <div class=\"footer-bottom\">\n    <p>© {{ new Date().getFullYear() }} CFish科技工作室 - 版权所有</p>\n  </div>\n</div>\n", null]}