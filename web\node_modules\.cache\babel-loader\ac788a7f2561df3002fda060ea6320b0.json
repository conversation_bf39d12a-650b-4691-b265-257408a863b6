{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\SelectUserListModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\SelectUserListModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getUserList } from '@/api/api';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nexport default {\n  name: \"SelectUserListModal\",\n  mixins: [JeecgListMixin],\n  data: function data() {\n    return {\n      title: \"操作\",\n      visible: false,\n      model: {},\n      confirmLoading: false,\n      url: {\n        add: \"/act/model/create\",\n        list: \"/sys/user/list\"\n      },\n      columns: [{\n        title: '用户账号',\n        align: \"center\",\n        dataIndex: 'username',\n        fixed: 'left',\n        width: 200\n      }, {\n        title: '用户姓名',\n        align: \"center\",\n        dataIndex: 'realname'\n      }, {\n        title: '性别',\n        align: \"center\",\n        dataIndex: 'sex_dictText'\n      }, {\n        title: '手机号码',\n        align: \"center\",\n        dataIndex: 'phone'\n      }, {\n        title: '邮箱',\n        align: \"center\",\n        dataIndex: 'email'\n      }, {\n        title: '状态',\n        align: \"center\",\n        dataIndex: 'status_dictText'\n      }]\n    };\n  },\n  created: function created() {\n    var _this = this;\n    //Step.2 加载用户数据\n    getUserList().then(function (res) {\n      if (res.success) {\n        _this.dataSource = res.result.records;\n        _this.ipagination.total = res.result.total;\n      }\n    });\n  },\n  methods: {\n    open: function open() {\n      this.visible = true;\n\n      //Step.1 清空选中用户\n      this.selectedRowKeys = [];\n      this.selectedRows = [];\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleChange: function handleChange(info) {\n      var file = info.file;\n      if (file.response.success) {\n        this.$message.success(file.response.message);\n        this.$emit('ok');\n        this.close();\n      } else {\n        this.$message.warn(file.response.message);\n        this.close();\n      }\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    handleSubmit: function handleSubmit() {\n      this.$emit('ok', this.selectionRows);\n      this.close();\n    }\n  }\n};", {"version": 3, "names": ["getUserList", "JeecgListMixin", "name", "mixins", "data", "title", "visible", "model", "confirmLoading", "url", "add", "list", "columns", "align", "dataIndex", "fixed", "width", "created", "_this", "then", "res", "success", "dataSource", "result", "records", "ipagination", "total", "methods", "open", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "close", "$emit", "handleChange", "info", "file", "response", "$message", "message", "warn", "handleCancel", "handleSubmit", "selectionRows"], "sources": ["src/components/jeecgbiz/modal/SelectUserListModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"用户列表\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleSubmit\"\n    @cancel=\"handleCancel\">\n\n    <a-table\n      ref=\"table\"\n      bordered\n      size=\"middle\"\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"></a-table>\n  </a-modal>\n</template>\n\n<script>\n  import {getUserList} from '@/api/api'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n\n  export default {\n    name: \"SelectUserListModal\",\n    mixins: [JeecgListMixin],\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        confirmLoading: false,\n        url: {\n          add: \"/act/model/create\",\n          list: \"/sys/user/list\"\n        },\n        columns: [\n          {\n            title: '用户账号',\n            align: \"center\",\n            dataIndex: 'username',\n            fixed: 'left',\n            width: 200\n          },\n          {\n            title: '用户姓名',\n            align: \"center\",\n            dataIndex: 'realname',\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex_dictText'\n          },\n          {\n            title: '手机号码',\n            align: \"center\",\n            dataIndex: 'phone'\n          },\n          {\n            title: '邮箱',\n            align: \"center\",\n            dataIndex: 'email'\n          },\n          {\n            title: '状态',\n            align: \"center\",\n            dataIndex: 'status_dictText'\n          }\n        ]\n      }\n    },\n    created() {\n      //Step.2 加载用户数据\n      getUserList().then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records;\n          this.ipagination.total = res.result.total;\n        }\n      })\n    },\n    methods: {\n      open() {\n        this.visible = true;\n\n        //Step.1 清空选中用户\n        this.selectedRowKeys = []\n        this.selectedRows = []\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleChange(info) {\n        let file = info.file;\n        if (file.response.success) {\n          this.$message.success(file.response.message);\n          this.$emit('ok');\n          this.close()\n        } else {\n          this.$message.warn(file.response.message);\n          this.close()\n        }\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleSubmit() {\n        this.$emit('ok', this.selectionRows);\n        this.close()\n      },\n    }\n  }\n</script>\n\n<style>\n\n</style>\n"], "mappings": "AAuBA,SAAAA,WAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAF,cAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,cAAA;MACAC,GAAA;QACAC,GAAA;QACAC,IAAA;MACA;MACAC,OAAA,GACA;QACAP,KAAA;QACAQ,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAX,KAAA;QACAQ,KAAA;QACAC,SAAA;MACA,GACA;QACAT,KAAA;QACAQ,KAAA;QACAC,SAAA;MACA,GACA;QACAT,KAAA;QACAQ,KAAA;QACAC,SAAA;MACA,GACA;QACAT,KAAA;QACAQ,KAAA;QACAC,SAAA;MACA,GACA;QACAT,KAAA;QACAQ,KAAA;QACAC,SAAA;MACA;IAEA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACAlB,WAAA,GAAAmB,IAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACAH,KAAA,CAAAI,UAAA,GAAAF,GAAA,CAAAG,MAAA,CAAAC,OAAA;QACAN,KAAA,CAAAO,WAAA,CAAAC,KAAA,GAAAN,GAAA,CAAAG,MAAA,CAAAG,KAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAtB,OAAA;;MAEA;MACA,KAAAuB,eAAA;MACA,KAAAC,YAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAA1B,OAAA;IACA;IACA2B,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,CAAAf,OAAA;QACA,KAAAgB,QAAA,CAAAhB,OAAA,CAAAc,IAAA,CAAAC,QAAA,CAAAE,OAAA;QACA,KAAAN,KAAA;QACA,KAAAD,KAAA;MACA;QACA,KAAAM,QAAA,CAAAE,IAAA,CAAAJ,IAAA,CAAAC,QAAA,CAAAE,OAAA;QACA,KAAAP,KAAA;MACA;IAEA;IACAS,YAAA,WAAAA,aAAA;MACA,KAAAT,KAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MACA,KAAAT,KAAA,YAAAU,aAAA;MACA,KAAAX,KAAA;IACA;EACA;AACA", "ignoreList": []}]}