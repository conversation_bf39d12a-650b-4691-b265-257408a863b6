{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue?vue&type=template&id=0edfacbb&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue", "mtime": 1753199398217}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div :class=\"['header', menuFixed?'menu-fixed':'']\">\n  <router-link :to=\"{ path: '/home' }\">\n    <img class=\"logo\" :src=\"logo\" alt=\"\" />\n  </router-link>\n  <t-menu class=\"menu\" mode=\"horizontal\" :menu=\"menus\"></t-menu>\n  <div class=\"header-avatar\">\n    <header-notice class=\"action\"/>\n    <img class=\"avatar\" :src=\"avatarUrl\" @click=\"enter\" alt=\"\" />\n    <span v-if=\"$store.state.user.info\">\n      <span @click=\"enter\">{{ $store.state.user.info.realname }}</span>\n      <a-divider type=\"vertical\" />\n      <span @click=\"handleLogout\">退出</span>\n    </span>\n    <span v-else>\n      <span @click=\"enter\">登录</span>\n      <a-divider type=\"vertical\" />\n      <span @click=\"enter\">注册</span>\n    </span>\n  </div>\n  \n  <software-download ref=\"softwareDownload\" />\n  <shopping-modal ref=\"shoppingModal\" />\n</div>\n", null]}