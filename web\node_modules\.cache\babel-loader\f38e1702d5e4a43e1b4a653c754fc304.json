{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue?vue&type=template&id=17d86c04&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeStatusBar.vue", "mtime": 1753194711604}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-card\", {\n    staticClass: \"practice-status\",\n    class: {\n      \"status-card-fullscreen\": _vm.isFullScreen\n    },\n    attrs: {\n      bordered: false\n    }\n  }, [_c(\"a-row\", {\n    attrs: {\n      type: \"flex\",\n      align: \"middle\"\n    }\n  }, [_c(\"a-col\", {\n    attrs: {\n      span: _vm.isFullScreen ? 24 : 16\n    }\n  }, [_vm.isFullScreen && _vm.practiseMode === \"count\" ? _c(\"div\", {\n    staticClass: \"fullscreen-mode-tag\"\n  }, [_c(\"a-tag\", {\n    attrs: {\n      color: \"green\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"ordered-list\"\n    }\n  }), _vm._v(\" 题目模式: \" + _vm._s(_vm.practiseCount) + \"题\\n        \")], 1)], 1) : _vm._e(), _vm.isFullScreen && _vm.isWrongRecordsMode ? _c(\"div\", {\n    staticClass: \"fullscreen-mode-tag\"\n  }, [_c(\"a-tag\", {\n    attrs: {\n      color: \"red\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"book\"\n    }\n  }), _vm.wrongRecordsPracticeMode === \"single\" ? _c(\"span\", [_vm._v(\"单题错题练习\")]) : _vm.wrongRecordsPracticeMode === \"selected\" ? _c(\"span\", [_vm._v(\"选中错题练习\")]) : _vm.wrongRecordsPracticeMode === \"wrongRecords\" ? _c(\"span\", [_vm._v(\"全部错题练习\")]) : _vm._e()], 1)], 1) : _vm._e(), _c(\"div\", {\n    staticClass: \"practice-progress\",\n    class: {\n      \"full-width-progress\": _vm.isFullScreen\n    }\n  }, [_c(\"div\", {\n    staticClass: \"progress-header\"\n  }, [_c(\"span\", {\n    staticClass: \"progress-text\"\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"dashboard\"\n    }\n  }), _vm._v(\" 进度: \" + _vm._s(_vm.answeredQuestions.length) + \"/\" + _vm._s(_vm.questionList.length) + \"\\n          \")], 1), _c(\"span\", {\n    staticClass: \"progress-percent\"\n  }, [_vm._v(\"\\n            \" + _vm._s(Math.round(_vm.answeredQuestions.length / _vm.questionList.length * 100)) + \"%\\n          \")])]), _c(\"a-progress\", {\n    attrs: {\n      percent: _vm.answeredQuestions.length / _vm.questionList.length * 100,\n      showInfo: false,\n      status: \"active\",\n      strokeColor: \"linear-gradient(to right, #108ee9, #2db7f5)\"\n    }\n  })], 1)]), !_vm.isFullScreen ? _c(\"a-col\", {\n    staticStyle: {\n      \"text-align\": \"right\"\n    },\n    attrs: {\n      span: 8\n    }\n  }, [_vm.practiseMode === \"time\" ? _c(\"div\", {\n    staticClass: \"practice-timer\"\n  }, [_c(\"a-badge\", {\n    attrs: {\n      status: \"processing\"\n    }\n  }), _c(\"a-statistic\", {\n    attrs: {\n      value: _vm.remainingTimeText,\n      valueStyle: {\n        fontSize: \"24px\",\n        color: \"#ff4d4f\",\n        background: \"#fff2f0\",\n        padding: \"4px 12px\",\n        borderRadius: \"8px\",\n        fontWeight: \"bold\"\n      },\n      suffix: \"剩余\"\n    }\n  })], 1) : _vm._e(), _vm.practiseMode === \"free\" ? _c(\"a-tag\", {\n    attrs: {\n      color: \"blue\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"unlock\"\n    }\n  }), _vm._v(\" 自由模式\\n      \")], 1) : _vm._e(), _vm.practiseMode === \"count\" ? _c(\"a-tag\", {\n    attrs: {\n      color: \"green\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"ordered-list\"\n    }\n  }), _vm._v(\" 题目模式: \" + _vm._s(_vm.practiseCount) + \"题\\n      \")], 1) : _vm._e(), _vm.practiseMode === \"wrong\" ? _c(\"div\", {\n    staticClass: \"wrong-practice-info\"\n  }, [_c(\"div\", {\n    staticClass: \"practice-title-tag\"\n  }, [_c(\"a-tag\", {\n    attrs: {\n      color: \"red\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"book\"\n    }\n  }), _vm._v(\"\\n            \" + _vm._s(_vm.practiceTitle || \"错题练习\") + \"\\n          \")], 1)], 1)]) : _vm._e(), _vm.isWrongRecordsMode ? _c(\"a-tag\", {\n    attrs: {\n      color: \"red\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"book\"\n    }\n  }), _vm.wrongRecordsPracticeMode === \"single\" ? _c(\"span\", [_vm._v(\"单题错题练习\")]) : _vm.wrongRecordsPracticeMode === \"selected\" ? _c(\"span\", [_vm._v(\"选中错题练习\")]) : _vm.wrongRecordsPracticeMode === \"wrongRecords\" ? _c(\"span\", [_vm._v(\"全部错题练习\")]) : _vm._e()], 1) : _vm._e()], 1) : _vm._e()], 1), _vm.isFullScreen && _vm.practiseMode === \"time\" ? _c(\"div\", {\n    staticClass: \"fullscreen-timer\"\n  }, [_c(\"a-badge\", {\n    attrs: {\n      status: \"processing\"\n    }\n  }), _c(\"a-statistic\", {\n    attrs: {\n      value: _vm.remainingTimeText,\n      valueStyle: {\n        fontSize: \"24px\",\n        color: \"#ff4d4f\",\n        background: \"#fff2f0\",\n        padding: \"4px 12px\",\n        borderRadius: \"8px\",\n        fontWeight: \"bold\"\n      },\n      suffix: \"剩余\"\n    }\n  })], 1) : _vm._e(), _vm.isFullScreen && _vm.practiseMode === \"free\" ? _c(\"div\", {\n    staticClass: \"fullscreen-mode-tag\"\n  }, [_c(\"a-tag\", {\n    attrs: {\n      color: \"blue\"\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"unlock\"\n    }\n  }), _vm._v(\" 自由模式\\n    \")], 1)], 1) : _vm._e(), _c(\"div\", {\n    staticClass: \"navigator-container\"\n  }, [_c(\"div\", {\n    staticClass: \"navigator-header\"\n  }, [_c(\"span\", [_c(\"a-icon\", {\n    attrs: {\n      type: \"bars\"\n    }\n  }), _vm._v(\" 题目导航\")], 1)]), _c(\"div\", {\n    staticClass: \"navigator-buttons\"\n  }, _vm._l(_vm.questionList, function (q, index) {\n    return _c(\"a-tooltip\", {\n      key: index,\n      attrs: {\n        title: _vm.getQuestionTypeText(q.questionType)\n      }\n    }, [_c(\"a-button\", {\n      class: _vm.getButtonClass(q, index),\n      attrs: {\n        type: _vm.getButtonType(q, index),\n        shape: \"circle\",\n        size: \"small\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.jumpToQuestion(index + 1);\n        }\n      }\n    }, [_vm._v(\"\\n          \" + _vm._s(index + 1) + \"\\n        \")])], 1);\n  }), 1)]), _c(\"div\", {\n    staticClass: \"practice-actions\"\n  }, [_vm.isReviewMode ? [_c(\"a-button\", {\n    attrs: {\n      type: \"danger\"\n    },\n    on: {\n      click: _vm.exitReviewMode\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"rollback\"\n    }\n  }), _vm._v(\" 退出查阅\\n      \")], 1), _c(\"a-button\", {\n    attrs: {\n      type: _vm.showAnswer ? \"dashed\" : \"default\"\n    },\n    on: {\n      click: _vm.toggleShowAnswer\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: _vm.showAnswer ? \"eye-invisible\" : \"eye\"\n    }\n  }), _vm._v(\"\\n        \" + _vm._s(_vm.showAnswer ? \"隐藏答案\" : \"显示答案\") + \"\\n      \")], 1), _c(\"a-button\", {\n    staticClass: \"collect-button\",\n    attrs: {\n      type: _vm.isCollected ? \"primary\" : \"default\",\n      loading: _vm.collectLoading\n    },\n    on: {\n      click: _vm.collectQuestion\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: _vm.isCollected ? \"star\" : \"star-o\",\n      theme: _vm.isCollected ? \"filled\" : \"outlined\"\n    }\n  }), _vm._v(\"\\n        \" + _vm._s(_vm.isCollected ? \"已收藏\" : \"收藏题目\") + \"\\n      \")], 1)] : [_c(\"a-button\", {\n    attrs: {\n      type: \"danger\"\n    },\n    on: {\n      click: _vm.exitPractise\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"rollback\"\n    }\n  }), _vm._v(\" \" + _vm._s(_vm.practiseMode === \"wrong\" ? \"退出错题练习\" : \"退出刷题\") + \"\\n      \")], 1), _c(\"a-button\", {\n    staticClass: \"collect-button\",\n    attrs: {\n      type: _vm.isCollected ? \"primary\" : \"default\",\n      loading: _vm.collectLoading\n    },\n    on: {\n      click: _vm.collectQuestion\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: _vm.isCollected ? \"star\" : \"star-o\",\n      theme: _vm.isCollected ? \"filled\" : \"outlined\"\n    }\n  }), _vm._v(\"\\n        \" + _vm._s(_vm.isCollected ? \"已收藏\" : \"收藏题目\") + \"\\n      \")], 1)]], 2)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "isFullScreen", "attrs", "bordered", "type", "align", "span", "practiseMode", "color", "_v", "_s", "practiseCount", "_e", "isWrongRecordsMode", "wrongRecordsPracticeMode", "answeredQuestions", "length", "questionList", "Math", "round", "percent", "showInfo", "status", "strokeColor", "staticStyle", "value", "remainingTimeText", "valueStyle", "fontSize", "background", "padding", "borderRadius", "fontWeight", "suffix", "practiceTitle", "_l", "q", "index", "key", "title", "getQuestionTypeText", "questionType", "getButtonClass", "getButtonType", "shape", "size", "on", "click", "$event", "jumpToQuestion", "isReviewMode", "exitReviewMode", "showAnswer", "toggleShowAnswer", "isCollected", "loading", "collectLoading", "collectQuestion", "theme", "exitPractise", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/examSystem/components/PracticeStatusBar.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-card\",\n    {\n      staticClass: \"practice-status\",\n      class: { \"status-card-fullscreen\": _vm.isFullScreen },\n      attrs: { bordered: false },\n    },\n    [\n      _c(\n        \"a-row\",\n        { attrs: { type: \"flex\", align: \"middle\" } },\n        [\n          _c(\"a-col\", { attrs: { span: _vm.isFullScreen ? 24 : 16 } }, [\n            _vm.isFullScreen && _vm.practiseMode === \"count\"\n              ? _c(\n                  \"div\",\n                  { staticClass: \"fullscreen-mode-tag\" },\n                  [\n                    _c(\n                      \"a-tag\",\n                      { attrs: { color: \"green\" } },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"ordered-list\" } }),\n                        _vm._v(\n                          \" 题目模式: \" +\n                            _vm._s(_vm.practiseCount) +\n                            \"题\\n        \"\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            _vm.isFullScreen && _vm.isWrongRecordsMode\n              ? _c(\n                  \"div\",\n                  { staticClass: \"fullscreen-mode-tag\" },\n                  [\n                    _c(\n                      \"a-tag\",\n                      { attrs: { color: \"red\" } },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"book\" } }),\n                        _vm.wrongRecordsPracticeMode === \"single\"\n                          ? _c(\"span\", [_vm._v(\"单题错题练习\")])\n                          : _vm.wrongRecordsPracticeMode === \"selected\"\n                          ? _c(\"span\", [_vm._v(\"选中错题练习\")])\n                          : _vm.wrongRecordsPracticeMode === \"wrongRecords\"\n                          ? _c(\"span\", [_vm._v(\"全部错题练习\")])\n                          : _vm._e(),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            _c(\n              \"div\",\n              {\n                staticClass: \"practice-progress\",\n                class: { \"full-width-progress\": _vm.isFullScreen },\n              },\n              [\n                _c(\"div\", { staticClass: \"progress-header\" }, [\n                  _c(\n                    \"span\",\n                    { staticClass: \"progress-text\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"dashboard\" } }),\n                      _vm._v(\n                        \" 进度: \" +\n                          _vm._s(_vm.answeredQuestions.length) +\n                          \"/\" +\n                          _vm._s(_vm.questionList.length) +\n                          \"\\n          \"\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"span\", { staticClass: \"progress-percent\" }, [\n                    _vm._v(\n                      \"\\n            \" +\n                        _vm._s(\n                          Math.round(\n                            (_vm.answeredQuestions.length /\n                              _vm.questionList.length) *\n                              100\n                          )\n                        ) +\n                        \"%\\n          \"\n                    ),\n                  ]),\n                ]),\n                _c(\"a-progress\", {\n                  attrs: {\n                    percent:\n                      (_vm.answeredQuestions.length / _vm.questionList.length) *\n                      100,\n                    showInfo: false,\n                    status: \"active\",\n                    strokeColor: \"linear-gradient(to right, #108ee9, #2db7f5)\",\n                  },\n                }),\n              ],\n              1\n            ),\n          ]),\n          !_vm.isFullScreen\n            ? _c(\n                \"a-col\",\n                { staticStyle: { \"text-align\": \"right\" }, attrs: { span: 8 } },\n                [\n                  _vm.practiseMode === \"time\"\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"practice-timer\" },\n                        [\n                          _c(\"a-badge\", { attrs: { status: \"processing\" } }),\n                          _c(\"a-statistic\", {\n                            attrs: {\n                              value: _vm.remainingTimeText,\n                              valueStyle: {\n                                fontSize: \"24px\",\n                                color: \"#ff4d4f\",\n                                background: \"#fff2f0\",\n                                padding: \"4px 12px\",\n                                borderRadius: \"8px\",\n                                fontWeight: \"bold\",\n                              },\n                              suffix: \"剩余\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm.practiseMode === \"free\"\n                    ? _c(\n                        \"a-tag\",\n                        { attrs: { color: \"blue\" } },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"unlock\" } }),\n                          _vm._v(\" 自由模式\\n      \"),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm.practiseMode === \"count\"\n                    ? _c(\n                        \"a-tag\",\n                        { attrs: { color: \"green\" } },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"ordered-list\" } }),\n                          _vm._v(\n                            \" 题目模式: \" +\n                              _vm._s(_vm.practiseCount) +\n                              \"题\\n      \"\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm.practiseMode === \"wrong\"\n                    ? _c(\"div\", { staticClass: \"wrong-practice-info\" }, [\n                        _c(\n                          \"div\",\n                          { staticClass: \"practice-title-tag\" },\n                          [\n                            _c(\n                              \"a-tag\",\n                              { attrs: { color: \"red\" } },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"book\" } }),\n                                _vm._v(\n                                  \"\\n            \" +\n                                    _vm._s(_vm.practiceTitle || \"错题练习\") +\n                                    \"\\n          \"\n                                ),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ])\n                    : _vm._e(),\n                  _vm.isWrongRecordsMode\n                    ? _c(\n                        \"a-tag\",\n                        { attrs: { color: \"red\" } },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"book\" } }),\n                          _vm.wrongRecordsPracticeMode === \"single\"\n                            ? _c(\"span\", [_vm._v(\"单题错题练习\")])\n                            : _vm.wrongRecordsPracticeMode === \"selected\"\n                            ? _c(\"span\", [_vm._v(\"选中错题练习\")])\n                            : _vm.wrongRecordsPracticeMode === \"wrongRecords\"\n                            ? _c(\"span\", [_vm._v(\"全部错题练习\")])\n                            : _vm._e(),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm.isFullScreen && _vm.practiseMode === \"time\"\n        ? _c(\n            \"div\",\n            { staticClass: \"fullscreen-timer\" },\n            [\n              _c(\"a-badge\", { attrs: { status: \"processing\" } }),\n              _c(\"a-statistic\", {\n                attrs: {\n                  value: _vm.remainingTimeText,\n                  valueStyle: {\n                    fontSize: \"24px\",\n                    color: \"#ff4d4f\",\n                    background: \"#fff2f0\",\n                    padding: \"4px 12px\",\n                    borderRadius: \"8px\",\n                    fontWeight: \"bold\",\n                  },\n                  suffix: \"剩余\",\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm.isFullScreen && _vm.practiseMode === \"free\"\n        ? _c(\n            \"div\",\n            { staticClass: \"fullscreen-mode-tag\" },\n            [\n              _c(\n                \"a-tag\",\n                { attrs: { color: \"blue\" } },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"unlock\" } }),\n                  _vm._v(\" 自由模式\\n    \"),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\"div\", { staticClass: \"navigator-container\" }, [\n        _c(\"div\", { staticClass: \"navigator-header\" }, [\n          _c(\n            \"span\",\n            [_c(\"a-icon\", { attrs: { type: \"bars\" } }), _vm._v(\" 题目导航\")],\n            1\n          ),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"navigator-buttons\" },\n          _vm._l(_vm.questionList, function (q, index) {\n            return _c(\n              \"a-tooltip\",\n              {\n                key: index,\n                attrs: { title: _vm.getQuestionTypeText(q.questionType) },\n              },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    class: _vm.getButtonClass(q, index),\n                    attrs: {\n                      type: _vm.getButtonType(q, index),\n                      shape: \"circle\",\n                      size: \"small\",\n                    },\n                    on: {\n                      click: function ($event) {\n                        return _vm.jumpToQuestion(index + 1)\n                      },\n                    },\n                  },\n                  [_vm._v(\"\\n          \" + _vm._s(index + 1) + \"\\n        \")]\n                ),\n              ],\n              1\n            )\n          }),\n          1\n        ),\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"practice-actions\" },\n        [\n          _vm.isReviewMode\n            ? [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { type: \"danger\" },\n                    on: { click: _vm.exitReviewMode },\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"rollback\" } }),\n                    _vm._v(\" 退出查阅\\n      \"),\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { type: _vm.showAnswer ? \"dashed\" : \"default\" },\n                    on: { click: _vm.toggleShowAnswer },\n                  },\n                  [\n                    _c(\"a-icon\", {\n                      attrs: { type: _vm.showAnswer ? \"eye-invisible\" : \"eye\" },\n                    }),\n                    _vm._v(\n                      \"\\n        \" +\n                        _vm._s(_vm.showAnswer ? \"隐藏答案\" : \"显示答案\") +\n                        \"\\n      \"\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"collect-button\",\n                    attrs: {\n                      type: _vm.isCollected ? \"primary\" : \"default\",\n                      loading: _vm.collectLoading,\n                    },\n                    on: { click: _vm.collectQuestion },\n                  },\n                  [\n                    _c(\"a-icon\", {\n                      attrs: {\n                        type: _vm.isCollected ? \"star\" : \"star-o\",\n                        theme: _vm.isCollected ? \"filled\" : \"outlined\",\n                      },\n                    }),\n                    _vm._v(\n                      \"\\n        \" +\n                        _vm._s(_vm.isCollected ? \"已收藏\" : \"收藏题目\") +\n                        \"\\n      \"\n                    ),\n                  ],\n                  1\n                ),\n              ]\n            : [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { type: \"danger\" },\n                    on: { click: _vm.exitPractise },\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"rollback\" } }),\n                    _vm._v(\n                      \" \" +\n                        _vm._s(\n                          _vm.practiseMode === \"wrong\"\n                            ? \"退出错题练习\"\n                            : \"退出刷题\"\n                        ) +\n                        \"\\n      \"\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"collect-button\",\n                    attrs: {\n                      type: _vm.isCollected ? \"primary\" : \"default\",\n                      loading: _vm.collectLoading,\n                    },\n                    on: { click: _vm.collectQuestion },\n                  },\n                  [\n                    _c(\"a-icon\", {\n                      attrs: {\n                        type: _vm.isCollected ? \"star\" : \"star-o\",\n                        theme: _vm.isCollected ? \"filled\" : \"outlined\",\n                      },\n                    }),\n                    _vm._v(\n                      \"\\n        \" +\n                        _vm._s(_vm.isCollected ? \"已收藏\" : \"收藏题目\") +\n                        \"\\n      \"\n                    ),\n                  ],\n                  1\n                ),\n              ],\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR;IACEE,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;MAAE,wBAAwB,EAAEJ,GAAG,CAACK;IAAa,CAAC;IACrDC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAM;EAC3B,CAAC,EACD,CACEN,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,EAC5C,CACER,EAAE,CAAC,OAAO,EAAE;IAAEK,KAAK,EAAE;MAAEI,IAAI,EAAEV,GAAG,CAACK,YAAY,GAAG,EAAE,GAAG;IAAG;EAAE,CAAC,EAAE,CAC3DL,GAAG,CAACK,YAAY,IAAIL,GAAG,CAACW,YAAY,KAAK,OAAO,GAC5CV,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAe;EAAE,CAAC,CAAC,EACjDR,GAAG,CAACa,EAAE,CACJ,SAAS,GACPb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,aAAa,CAAC,GACzB,aACJ,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDf,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACK,YAAY,IAAIL,GAAG,CAACiB,kBAAkB,GACtChB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAO;EAAE,CAAC,CAAC,EACzCR,GAAG,CAACkB,wBAAwB,KAAK,QAAQ,GACrCjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACkB,wBAAwB,KAAK,UAAU,GAC3CjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACkB,wBAAwB,KAAK,cAAc,GAC/CjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDhB,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZf,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,mBAAmB;IAChCC,KAAK,EAAE;MAAE,qBAAqB,EAAEJ,GAAG,CAACK;IAAa;EACnD,CAAC,EACD,CACEJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,MAAM,EACN;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAY;EAAE,CAAC,CAAC,EAC9CR,GAAG,CAACa,EAAE,CACJ,OAAO,GACLb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACmB,iBAAiB,CAACC,MAAM,CAAC,GACpC,GAAG,GACHpB,GAAG,CAACc,EAAE,CAACd,GAAG,CAACqB,YAAY,CAACD,MAAM,CAAC,GAC/B,cACJ,CAAC,CACF,EACD,CACF,CAAC,EACDnB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC9CH,GAAG,CAACa,EAAE,CACJ,gBAAgB,GACdb,GAAG,CAACc,EAAE,CACJQ,IAAI,CAACC,KAAK,CACPvB,GAAG,CAACmB,iBAAiB,CAACC,MAAM,GAC3BpB,GAAG,CAACqB,YAAY,CAACD,MAAM,GACvB,GACJ,CACF,CAAC,GACD,eACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFnB,EAAE,CAAC,YAAY,EAAE;IACfK,KAAK,EAAE;MACLkB,OAAO,EACJxB,GAAG,CAACmB,iBAAiB,CAACC,MAAM,GAAGpB,GAAG,CAACqB,YAAY,CAACD,MAAM,GACvD,GAAG;MACLK,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACF,CAAC3B,GAAG,CAACK,YAAY,GACbJ,EAAE,CACA,OAAO,EACP;IAAE2B,WAAW,EAAE;MAAE,YAAY,EAAE;IAAQ,CAAC;IAAEtB,KAAK,EAAE;MAAEI,IAAI,EAAE;IAAE;EAAE,CAAC,EAC9D,CACEV,GAAG,CAACW,YAAY,KAAK,MAAM,GACvBV,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CAAC,SAAS,EAAE;IAAEK,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAa;EAAE,CAAC,CAAC,EAClDzB,EAAE,CAAC,aAAa,EAAE;IAChBK,KAAK,EAAE;MACLuB,KAAK,EAAE7B,GAAG,CAAC8B,iBAAiB;MAC5BC,UAAU,EAAE;QACVC,QAAQ,EAAE,MAAM;QAChBpB,KAAK,EAAE,SAAS;QAChBqB,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;MACd,CAAC;MACDC,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDrC,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACW,YAAY,KAAK,MAAM,GACvBV,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAS;EAAE,CAAC,CAAC,EAC3CR,GAAG,CAACa,EAAE,CAAC,eAAe,CAAC,CACxB,EACD,CACF,CAAC,GACDb,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACW,YAAY,KAAK,OAAO,GACxBV,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAe;EAAE,CAAC,CAAC,EACjDR,GAAG,CAACa,EAAE,CACJ,SAAS,GACPb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,aAAa,CAAC,GACzB,WACJ,CAAC,CACF,EACD,CACF,CAAC,GACDf,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACW,YAAY,KAAK,OAAO,GACxBV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAO;EAAE,CAAC,CAAC,EACzCR,GAAG,CAACa,EAAE,CACJ,gBAAgB,GACdb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACsC,aAAa,IAAI,MAAM,CAAC,GACnC,cACJ,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,GACFtC,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACiB,kBAAkB,GAClBhB,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAM;EAAE,CAAC,EAC3B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAO;EAAE,CAAC,CAAC,EACzCR,GAAG,CAACkB,wBAAwB,KAAK,QAAQ,GACrCjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACkB,wBAAwB,KAAK,UAAU,GAC3CjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACkB,wBAAwB,KAAK,cAAc,GAC/CjB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAC9Bb,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,GACDhB,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,GACDhB,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDhB,GAAG,CAACK,YAAY,IAAIL,GAAG,CAACW,YAAY,KAAK,MAAM,GAC3CV,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CAAC,SAAS,EAAE;IAAEK,KAAK,EAAE;MAAEoB,MAAM,EAAE;IAAa;EAAE,CAAC,CAAC,EAClDzB,EAAE,CAAC,aAAa,EAAE;IAChBK,KAAK,EAAE;MACLuB,KAAK,EAAE7B,GAAG,CAAC8B,iBAAiB;MAC5BC,UAAU,EAAE;QACVC,QAAQ,EAAE,MAAM;QAChBpB,KAAK,EAAE,SAAS;QAChBqB,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;MACd,CAAC;MACDC,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDrC,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACK,YAAY,IAAIL,GAAG,CAACW,YAAY,KAAK,MAAM,GAC3CV,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,OAAO,EACP;IAAEK,KAAK,EAAE;MAAEM,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEX,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAS;EAAE,CAAC,CAAC,EAC3CR,GAAG,CAACa,EAAE,CAAC,aAAa,CAAC,CACtB,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDb,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CACA,MAAM,EACN,CAACA,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAO;EAAE,CAAC,CAAC,EAAER,GAAG,CAACa,EAAE,CAAC,OAAO,CAAC,CAAC,EAC5D,CACF,CAAC,CACF,CAAC,EACFZ,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpCH,GAAG,CAACuC,EAAE,CAACvC,GAAG,CAACqB,YAAY,EAAE,UAAUmB,CAAC,EAAEC,KAAK,EAAE;IAC3C,OAAOxC,EAAE,CACP,WAAW,EACX;MACEyC,GAAG,EAAED,KAAK;MACVnC,KAAK,EAAE;QAAEqC,KAAK,EAAE3C,GAAG,CAAC4C,mBAAmB,CAACJ,CAAC,CAACK,YAAY;MAAE;IAC1D,CAAC,EACD,CACE5C,EAAE,CACA,UAAU,EACV;MACEG,KAAK,EAAEJ,GAAG,CAAC8C,cAAc,CAACN,CAAC,EAAEC,KAAK,CAAC;MACnCnC,KAAK,EAAE;QACLE,IAAI,EAAER,GAAG,CAAC+C,aAAa,CAACP,CAAC,EAAEC,KAAK,CAAC;QACjCO,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOpD,GAAG,CAACqD,cAAc,CAACZ,KAAK,GAAG,CAAC,CAAC;QACtC;MACF;IACF,CAAC,EACD,CAACzC,GAAG,CAACa,EAAE,CAAC,cAAc,GAAGb,GAAG,CAACc,EAAE,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAC5D,CAAC,CACF,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACFxC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEH,GAAG,CAACsD,YAAY,GACZ,CACErD,EAAE,CACA,UAAU,EACV;IACEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAS,CAAC;IACzB0C,EAAE,EAAE;MAAEC,KAAK,EAAEnD,GAAG,CAACuD;IAAe;EAClC,CAAC,EACD,CACEtD,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,EAC7CR,GAAG,CAACa,EAAE,CAAC,eAAe,CAAC,CACxB,EACD,CACF,CAAC,EACDZ,EAAE,CACA,UAAU,EACV;IACEK,KAAK,EAAE;MAAEE,IAAI,EAAER,GAAG,CAACwD,UAAU,GAAG,QAAQ,GAAG;IAAU,CAAC;IACtDN,EAAE,EAAE;MAAEC,KAAK,EAAEnD,GAAG,CAACyD;IAAiB;EACpC,CAAC,EACD,CACExD,EAAE,CAAC,QAAQ,EAAE;IACXK,KAAK,EAAE;MAAEE,IAAI,EAAER,GAAG,CAACwD,UAAU,GAAG,eAAe,GAAG;IAAM;EAC1D,CAAC,CAAC,EACFxD,GAAG,CAACa,EAAE,CACJ,YAAY,GACVb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACwD,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC,GACxC,UACJ,CAAC,CACF,EACD,CACF,CAAC,EACDvD,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,gBAAgB;IAC7BG,KAAK,EAAE;MACLE,IAAI,EAAER,GAAG,CAAC0D,WAAW,GAAG,SAAS,GAAG,SAAS;MAC7CC,OAAO,EAAE3D,GAAG,CAAC4D;IACf,CAAC;IACDV,EAAE,EAAE;MAAEC,KAAK,EAAEnD,GAAG,CAAC6D;IAAgB;EACnC,CAAC,EACD,CACE5D,EAAE,CAAC,QAAQ,EAAE;IACXK,KAAK,EAAE;MACLE,IAAI,EAAER,GAAG,CAAC0D,WAAW,GAAG,MAAM,GAAG,QAAQ;MACzCI,KAAK,EAAE9D,GAAG,CAAC0D,WAAW,GAAG,QAAQ,GAAG;IACtC;EACF,CAAC,CAAC,EACF1D,GAAG,CAACa,EAAE,CACJ,YAAY,GACVb,GAAG,CAACc,EAAE,CAACd,GAAG,CAAC0D,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC,GACxC,UACJ,CAAC,CACF,EACD,CACF,CAAC,CACF,GACD,CACEzD,EAAE,CACA,UAAU,EACV;IACEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAS,CAAC;IACzB0C,EAAE,EAAE;MAAEC,KAAK,EAAEnD,GAAG,CAAC+D;IAAa;EAChC,CAAC,EACD,CACE9D,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,EAC7CR,GAAG,CAACa,EAAE,CACJ,GAAG,GACDb,GAAG,CAACc,EAAE,CACJd,GAAG,CAACW,YAAY,KAAK,OAAO,GACxB,QAAQ,GACR,MACN,CAAC,GACD,UACJ,CAAC,CACF,EACD,CACF,CAAC,EACDV,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,gBAAgB;IAC7BG,KAAK,EAAE;MACLE,IAAI,EAAER,GAAG,CAAC0D,WAAW,GAAG,SAAS,GAAG,SAAS;MAC7CC,OAAO,EAAE3D,GAAG,CAAC4D;IACf,CAAC;IACDV,EAAE,EAAE;MAAEC,KAAK,EAAEnD,GAAG,CAAC6D;IAAgB;EACnC,CAAC,EACD,CACE5D,EAAE,CAAC,QAAQ,EAAE;IACXK,KAAK,EAAE;MACLE,IAAI,EAAER,GAAG,CAAC0D,WAAW,GAAG,MAAM,GAAG,QAAQ;MACzCI,KAAK,EAAE9D,GAAG,CAAC0D,WAAW,GAAG,QAAQ,GAAG;IACtC;EACF,CAAC,CAAC,EACF1D,GAAG,CAACa,EAAE,CACJ,YAAY,GACVb,GAAG,CAACc,EAAE,CAACd,GAAG,CAAC0D,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC,GACxC,UACJ,CAAC,CACF,EACD,CACF,CAAC,CACF,CACN,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIM,eAAe,GAAG,EAAE;AACxBjE,MAAM,CAACkE,aAAa,GAAG,IAAI;AAE3B,SAASlE,MAAM,EAAEiE,eAAe", "ignoreList": []}]}