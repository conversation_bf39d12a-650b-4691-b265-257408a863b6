{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\props-util.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\props-util.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n/**\n * 该文件截取自 \"ant-design-vue/es/_util/props-util.js\" 文件，并对其做出特殊修改\n */\nfunction classNames() {\n  var classes = [];\n  for (var i = 0; i < arguments.length; i++) {\n    var arg = arguments[i];\n    if (!arg) continue;\n    var argType = _typeof(arg);\n    if (argType === 'string' || argType === 'number') {\n      classes.push(arg);\n    } else if (Array.isArray(arg) && arg.length) {\n      var inner = classNames.apply(null, arg);\n      if (inner) {\n        classes.push(inner);\n      }\n    } else if (argType === 'object') {\n      for (var key in arg) {\n        if (arg.hasOwnProperty(key) && arg[key]) {\n          classes.push(key);\n        }\n      }\n    }\n  }\n  return classes.join(' ');\n}\nvar camelizeRE = /-(\\w)/g;\nfunction camelize(str) {\n  return str.replace(camelizeRE, function (_, c) {\n    return c ? c.toUpperCase() : '';\n  });\n}\nfunction objectCamelize(obj) {\n  var res = {};\n  Object.keys(obj).forEach(function (k) {\n    return res[camelize(k)] = obj[k];\n  });\n  return res;\n}\nfunction parseStyleText() {\n  var cssText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var camel = arguments.length > 1 ? arguments[1] : undefined;\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      if (tmp.length > 1) {\n        var k = camel ? camelize(tmp[0].trim()) : tmp[0].trim();\n        res[k] = tmp[1].trim();\n      }\n    }\n  });\n  return res;\n}\nexport function getClass(ele) {\n  var data = {};\n  if (ele.data) {\n    data = ele.data;\n  } else if (ele.$vnode && ele.$vnode.data) {\n    data = ele.$vnode.data;\n  }\n  var tempCls = data.class || {};\n  var staticClass = data.staticClass;\n  var cls = {};\n  staticClass && staticClass.split(' ').forEach(function (c) {\n    cls[c.trim()] = true;\n  });\n  if (typeof tempCls === 'string') {\n    tempCls.split(' ').forEach(function (c) {\n      cls[c.trim()] = true;\n    });\n  } else if (Array.isArray(tempCls)) {\n    classNames(tempCls).split(' ').forEach(function (c) {\n      cls[c.trim()] = true;\n    });\n  } else {\n    cls = _objectSpread(_objectSpread({}, cls), tempCls);\n  }\n  return cls;\n}\nexport function getStyle(ele, camel) {\n  getClass(ele);\n  var data = {};\n  if (ele.data) {\n    data = ele.data;\n  } else if (ele.$vnode && ele.$vnode.data) {\n    data = ele.$vnode.data;\n  }\n\n  // update-begin-author:sunjianlei date:20200303 for: style 和 staticStyle 可以共存\n  var style = data.style || {};\n  var staticStyle = data.staticStyle;\n  staticStyle = staticStyle ? objectCamelize(data.staticStyle) : {};\n  // update-end-author:sunjianlei date:20200303 for: style 和 staticStyle 可以共存\n\n  if (typeof style === 'string') {\n    style = parseStyleText(style, camel);\n  } else if (camel && style) {\n    // 驼峰化\n    style = objectCamelize(style);\n  }\n  return _objectSpread(_objectSpread({}, staticStyle), style);\n}", {"version": 3, "names": ["classNames", "classes", "i", "arguments", "length", "arg", "argType", "_typeof", "push", "Array", "isArray", "inner", "apply", "key", "hasOwnProperty", "join", "camelizeRE", "camelize", "str", "replace", "_", "c", "toUpperCase", "objectCamelize", "obj", "res", "Object", "keys", "for<PERSON>ach", "k", "parseStyleText", "cssText", "undefined", "camel", "listDelimiter", "propertyDelimiter", "split", "item", "tmp", "trim", "getClass", "ele", "data", "$vnode", "tempCls", "class", "staticClass", "cls", "_objectSpread", "getStyle", "style", "staticStyle"], "sources": ["E:/teachingproject/teaching/web/src/utils/props-util.js"], "sourcesContent": ["/**\n * 该文件截取自 \"ant-design-vue/es/_util/props-util.js\" 文件，并对其做出特殊修改\n */\nfunction classNames() {\n  let classes = []\n\n  for (let i = 0; i < arguments.length; i++) {\n    let arg = arguments[i]\n    if (!arg) continue\n\n    let argType = typeof arg\n\n    if (argType === 'string' || argType === 'number') {\n      classes.push(arg)\n    } else if (Array.isArray(arg) && arg.length) {\n      let inner = classNames.apply(null, arg)\n      if (inner) {\n        classes.push(inner)\n      }\n    } else if (argType === 'object') {\n      for (let key in arg) {\n        if (arg.hasOwnProperty(key) && arg[key]) {\n          classes.push(key)\n        }\n      }\n    }\n  }\n  return classes.join(' ')\n}\n\nconst camelizeRE = /-(\\w)/g\n\nfunction camelize(str) {\n  return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''))\n}\n\n\nfunction objectCamelize(obj) {\n  let res = {}\n  Object.keys(obj).forEach(k => (res[camelize(k)] = obj[k]))\n  return res\n}\n\nfunction parseStyleText(cssText = '', camel) {\n  const res = {}\n  const listDelimiter = /;(?![^(]*\\))/g\n  const propertyDelimiter = /:(.+)/\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      const tmp = item.split(propertyDelimiter)\n      if (tmp.length > 1) {\n        const k = camel ? camelize(tmp[0].trim()) : tmp[0].trim()\n        res[k] = tmp[1].trim()\n      }\n    }\n  })\n  return res\n}\n\nexport function getClass(ele) {\n  let data = {}\n  if (ele.data) {\n    data = ele.data\n  } else if (ele.$vnode && ele.$vnode.data) {\n    data = ele.$vnode.data\n  }\n  const tempCls = data.class || {}\n  const staticClass = data.staticClass\n  let cls = {}\n  staticClass &&\n  staticClass.split(' ').forEach(c => {\n    cls[c.trim()] = true\n  })\n  if (typeof tempCls === 'string') {\n    tempCls.split(' ').forEach(c => {\n      cls[c.trim()] = true\n    })\n  } else if (Array.isArray(tempCls)) {\n    classNames(tempCls)\n      .split(' ')\n      .forEach(c => {\n        cls[c.trim()] = true\n      })\n  } else {\n    cls = { ...cls, ...tempCls }\n  }\n  return cls\n}\n\nexport function getStyle(ele, camel) {\n\n  getClass(ele)\n\n  let data = {}\n  if (ele.data) {\n    data = ele.data\n  } else if (ele.$vnode && ele.$vnode.data) {\n    data = ele.$vnode.data\n  }\n\n  // update-begin-author:sunjianlei date:20200303 for: style 和 staticStyle 可以共存\n  let style = data.style || {}\n  let staticStyle = data.staticStyle\n  staticStyle = staticStyle ? objectCamelize(data.staticStyle) : {}\n  // update-end-author:sunjianlei date:20200303 for: style 和 staticStyle 可以共存\n\n  if (typeof style === 'string') {\n    style = parseStyleText(style, camel)\n  } else if (camel && style) {\n    // 驼峰化\n    style = objectCamelize(style)\n  }\n  return { ...staticStyle, ...style }\n}\n\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA,SAASA,UAAUA,CAAA,EAAG;EACpB,IAAIC,OAAO,GAAG,EAAE;EAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,GAAG,GAAGF,SAAS,CAACD,CAAC,CAAC;IACtB,IAAI,CAACG,GAAG,EAAE;IAEV,IAAIC,OAAO,GAAAC,OAAA,CAAUF,GAAG;IAExB,IAAIC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;MAChDL,OAAO,CAACO,IAAI,CAACH,GAAG,CAAC;IACnB,CAAC,MAAM,IAAII,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,IAAIA,GAAG,CAACD,MAAM,EAAE;MAC3C,IAAIO,KAAK,GAAGX,UAAU,CAACY,KAAK,CAAC,IAAI,EAAEP,GAAG,CAAC;MACvC,IAAIM,KAAK,EAAE;QACTV,OAAO,CAACO,IAAI,CAACG,KAAK,CAAC;MACrB;IACF,CAAC,MAAM,IAAIL,OAAO,KAAK,QAAQ,EAAE;MAC/B,KAAK,IAAIO,GAAG,IAAIR,GAAG,EAAE;QACnB,IAAIA,GAAG,CAACS,cAAc,CAACD,GAAG,CAAC,IAAIR,GAAG,CAACQ,GAAG,CAAC,EAAE;UACvCZ,OAAO,CAACO,IAAI,CAACK,GAAG,CAAC;QACnB;MACF;IACF;EACF;EACA,OAAOZ,OAAO,CAACc,IAAI,CAAC,GAAG,CAAC;AAC1B;AAEA,IAAMC,UAAU,GAAG,QAAQ;AAE3B,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,CAACC,OAAO,CAACH,UAAU,EAAE,UAACI,CAAC,EAAEC,CAAC;IAAA,OAAMA,CAAC,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;EAAA,CAAC,CAAC;AACtE;AAGA,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,UAAAC,CAAC;IAAA,OAAKJ,GAAG,CAACR,QAAQ,CAACY,CAAC,CAAC,CAAC,GAAGL,GAAG,CAACK,CAAC,CAAC;EAAA,CAAC,CAAC;EAC1D,OAAOJ,GAAG;AACZ;AAEA,SAASK,cAAcA,CAAA,EAAsB;EAAA,IAArBC,OAAO,GAAA5B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA6B,SAAA,GAAA7B,SAAA,MAAG,EAAE;EAAA,IAAE8B,KAAK,GAAA9B,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAA6B,SAAA;EACzC,IAAMP,GAAG,GAAG,CAAC,CAAC;EACd,IAAMS,aAAa,GAAG,eAAe;EACrC,IAAMC,iBAAiB,GAAG,OAAO;EACjCJ,OAAO,CAACK,KAAK,CAACF,aAAa,CAAC,CAACN,OAAO,CAAC,UAAUS,IAAI,EAAE;IACnD,IAAIA,IAAI,EAAE;MACR,IAAMC,GAAG,GAAGD,IAAI,CAACD,KAAK,CAACD,iBAAiB,CAAC;MACzC,IAAIG,GAAG,CAAClC,MAAM,GAAG,CAAC,EAAE;QAClB,IAAMyB,CAAC,GAAGI,KAAK,GAAGhB,QAAQ,CAACqB,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QACzDd,GAAG,CAACI,CAAC,CAAC,GAAGS,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MACxB;IACF;EACF,CAAC,CAAC;EACF,OAAOd,GAAG;AACZ;AAEA,OAAO,SAASe,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,IAAID,GAAG,CAACC,IAAI,EAAE;IACZA,IAAI,GAAGD,GAAG,CAACC,IAAI;EACjB,CAAC,MAAM,IAAID,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACE,MAAM,CAACD,IAAI,EAAE;IACxCA,IAAI,GAAGD,GAAG,CAACE,MAAM,CAACD,IAAI;EACxB;EACA,IAAME,OAAO,GAAGF,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;EAChC,IAAMC,WAAW,GAAGJ,IAAI,CAACI,WAAW;EACpC,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZD,WAAW,IACXA,WAAW,CAACV,KAAK,CAAC,GAAG,CAAC,CAACR,OAAO,CAAC,UAAAP,CAAC,EAAI;IAClC0B,GAAG,CAAC1B,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;EACtB,CAAC,CAAC;EACF,IAAI,OAAOK,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,CAACR,OAAO,CAAC,UAAAP,CAAC,EAAI;MAC9B0B,GAAG,CAAC1B,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IACtB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI9B,KAAK,CAACC,OAAO,CAACkC,OAAO,CAAC,EAAE;IACjC5C,UAAU,CAAC4C,OAAO,CAAC,CAChBR,KAAK,CAAC,GAAG,CAAC,CACVR,OAAO,CAAC,UAAAP,CAAC,EAAI;MACZ0B,GAAG,CAAC1B,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IACtB,CAAC,CAAC;EACN,CAAC,MAAM;IACLQ,GAAG,GAAAC,aAAA,CAAAA,aAAA,KAAQD,GAAG,GAAKH,OAAO,CAAE;EAC9B;EACA,OAAOG,GAAG;AACZ;AAEA,OAAO,SAASE,QAAQA,CAACR,GAAG,EAAER,KAAK,EAAE;EAEnCO,QAAQ,CAACC,GAAG,CAAC;EAEb,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,IAAID,GAAG,CAACC,IAAI,EAAE;IACZA,IAAI,GAAGD,GAAG,CAACC,IAAI;EACjB,CAAC,MAAM,IAAID,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACE,MAAM,CAACD,IAAI,EAAE;IACxCA,IAAI,GAAGD,GAAG,CAACE,MAAM,CAACD,IAAI;EACxB;;EAEA;EACA,IAAIQ,KAAK,GAAGR,IAAI,CAACQ,KAAK,IAAI,CAAC,CAAC;EAC5B,IAAIC,WAAW,GAAGT,IAAI,CAACS,WAAW;EAClCA,WAAW,GAAGA,WAAW,GAAG5B,cAAc,CAACmB,IAAI,CAACS,WAAW,CAAC,GAAG,CAAC,CAAC;EACjE;;EAEA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGpB,cAAc,CAACoB,KAAK,EAAEjB,KAAK,CAAC;EACtC,CAAC,MAAM,IAAIA,KAAK,IAAIiB,KAAK,EAAE;IACzB;IACAA,KAAK,GAAG3B,cAAc,CAAC2B,KAAK,CAAC;EAC/B;EACA,OAAAF,aAAA,CAAAA,aAAA,KAAYG,WAAW,GAAKD,KAAK;AACnC", "ignoreList": []}]}