{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\List.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\List.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import Avatar from 'ant-design-vue/es/avatar'\n  import AvatarItem from './Item'\n  import { filterEmpty } from '@/components/_util/util'\n\n  export default {\n    AvatarItem,\n    name: \"AvatarList\",\n    components: {\n      Avatar,\n      AvatarItem\n    },\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-avatar-list'\n      },\n      /**\n       * 头像大小 类型: large、small 、mini, default\n       * 默认值: default\n       */\n      size: {\n        type: [String, Number],\n        default: 'default'\n      },\n      /**\n       * 要显示的最大项目\n       */\n      maxLength: {\n        type: Number,\n        default: 0\n      },\n      /**\n       * 多余的项目风格\n       */\n      excessItemsStyle: {\n        type: Object,\n        default: () => {\n          return {\n            color: '#f56a00',\n            backgroundColor: '#fde3cf'\n          }\n        }\n      }\n    },\n    data () {\n      return {}\n    },\n    methods: {\n      getItems(items) {\n        const classString = {\n          [`${this.prefixCls}-item`]: true,\n          [`${this.size}`]: true\n        }\n\n        if (this.maxLength > 0) {\n          items = items.slice(0, this.maxLength)\n          items.push((<Avatar size={ this.size } style={ this.excessItemsStyle }>{`+${this.maxLength}`}</Avatar>))\n        }\n        const itemList = items.map((item) => (\n          <li class={ classString }>{ item }</li>\n        ))\n        return itemList\n      }\n    },\n    render () {\n      const { prefixCls, size } = this.$props\n      const classString = {\n        [`${prefixCls}`]: true,\n        [`${size}`]: true,\n      }\n      const items = filterEmpty(this.$slots.default)\n      const itemsDom = items && items.length ? <ul class={`${prefixCls}-items`}>{ this.getItems(items) }</ul> : null\n\n      return (\n        <div class={ classString }>\n          { itemsDom }\n        </div>\n      )\n    }\n  }\n", {"version": 3, "sources": ["List.vue"], "names": [], "mappings": ";AAmBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "List.vue", "sourceRoot": "src/components/AvatarList", "sourcesContent": ["<!--\n<template>\n  <div :class=\"[prefixCls]\">\n    <ul>\n      <slot></slot>\n      <template v-for=\"item in filterEmpty($slots.default).slice(0, 3)\"></template>\n\n\n      <template v-if=\"maxLength > 0 && filterEmpty($slots.default).length > maxLength\">\n        <avatar-item :size=\"size\">\n          <avatar :size=\"size !== 'mini' && size || 20\" :style=\"excessItemsStyle\">{{ `+${maxLength}` }}</avatar>\n        </avatar-item>\n      </template>\n    </ul>\n  </div>\n</template>\n-->\n\n<script>\n  import Avatar from 'ant-design-vue/es/avatar'\n  import AvatarItem from './Item'\n  import { filterEmpty } from '@/components/_util/util'\n\n  export default {\n    AvatarItem,\n    name: \"AvatarList\",\n    components: {\n      Avatar,\n      AvatarItem\n    },\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-avatar-list'\n      },\n      /**\n       * 头像大小 类型: large、small 、mini, default\n       * 默认值: default\n       */\n      size: {\n        type: [String, Number],\n        default: 'default'\n      },\n      /**\n       * 要显示的最大项目\n       */\n      maxLength: {\n        type: Number,\n        default: 0\n      },\n      /**\n       * 多余的项目风格\n       */\n      excessItemsStyle: {\n        type: Object,\n        default: () => {\n          return {\n            color: '#f56a00',\n            backgroundColor: '#fde3cf'\n          }\n        }\n      }\n    },\n    data () {\n      return {}\n    },\n    methods: {\n      getItems(items) {\n        const classString = {\n          [`${this.prefixCls}-item`]: true,\n          [`${this.size}`]: true\n        }\n\n        if (this.maxLength > 0) {\n          items = items.slice(0, this.maxLength)\n          items.push((<Avatar size={ this.size } style={ this.excessItemsStyle }>{`+${this.maxLength}`}</Avatar>))\n        }\n        const itemList = items.map((item) => (\n          <li class={ classString }>{ item }</li>\n        ))\n        return itemList\n      }\n    },\n    render () {\n      const { prefixCls, size } = this.$props\n      const classString = {\n        [`${prefixCls}`]: true,\n        [`${size}`]: true,\n      }\n      const items = filterEmpty(this.$slots.default)\n      const itemsDom = items && items.length ? <ul class={`${prefixCls}-items`}>{ this.getItems(items) }</ul> : null\n\n      return (\n        <div class={ classString }>\n          { itemsDom }\n        </div>\n      )\n    }\n  }\n</script>"]}]}