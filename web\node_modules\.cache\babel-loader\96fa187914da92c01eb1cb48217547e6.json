{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\exception\\type.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\exception\\type.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["var types = {\n  403: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/wZcnGqRDyhPOEYFcZDnb.svg',\n    title: '403',\n    desc: '抱歉，你无权访问该页面'\n  },\n  404: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/KpnpchXsobRgLElEozzI.svg',\n    title: '404',\n    desc: '抱歉，你访问的页面不存在或仍在开发中'\n  },\n  500: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/RVRUAYdCGeYNBWoKiIwB.svg',\n    title: '500',\n    desc: '抱歉，服务器出错了'\n  }\n};\nexport default types;", {"version": 3, "names": ["types", "img", "title", "desc"], "sources": ["E:/teachingproject/teaching/web/src/views/exception/type.js"], "sourcesContent": ["const types = {\n  403: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/wZcnGqRDyhPOEYFcZDnb.svg',\n    title: '403',\n    desc: '抱歉，你无权访问该页面'\n  },\n  404: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/KpnpchXsobRgLElEozzI.svg',\n    title: '404',\n    desc: '抱歉，你访问的页面不存在或仍在开发中'\n  },\n  500: {\n    img: 'https://gw.alipayobjects.com/zos/rmsportal/RVRUAYdCGeYNBWoKiIwB.svg',\n    title: '500',\n    desc: '抱歉，服务器出错了'\n  }\n}\n\nexport default types"], "mappings": "AAAA,IAAMA,KAAK,GAAG;EACZ,GAAG,EAAE;IACHC,GAAG,EAAE,qEAAqE;IAC1EC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR,CAAC;EACD,GAAG,EAAE;IACHF,GAAG,EAAE,qEAAqE;IAC1EC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR,CAAC;EACD,GAAG,EAAE;IACHF,GAAG,EAAE,qEAAqE;IAC1EC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR;AACF,CAAC;AAED,eAAeH,KAAK", "ignoreList": []}]}