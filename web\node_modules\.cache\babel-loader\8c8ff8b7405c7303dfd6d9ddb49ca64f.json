{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\menu\\tmenu.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\menu\\tmenu.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import _mergeJSXProps, { default as _mergeJSXProps2 } from \"@vue/babel-helper-vue-jsx-merge-props\";\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport Menu from 'ant-design-vue/es/menu';\nimport Icon from 'ant-design-vue/es/icon';\nvar Item = Menu.Item,\n  SubMenu = Menu.SubMenu;\nexport default {\n  name: 'TMenu',\n  props: {\n    menu: {\n      type: Array,\n      required: true\n    },\n    theme: {\n      type: String,\n      required: false,\n      default: 'light'\n    },\n    mode: {\n      type: String,\n      required: false,\n      default: 'inline'\n    },\n    collapsed: {\n      type: Boolean,\n      required: false,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      openKeys: [],\n      selectedKeys: [],\n      cachedOpenKeys: [],\n      isLogin: false\n    };\n  },\n  computed: {\n    rootSubmenuKeys: function rootSubmenuKeys(vm) {\n      var keys = [];\n      vm.menu.forEach(function (item) {\n        return keys.push(item.path);\n      });\n      return keys;\n    }\n  },\n  created: function created() {\n    this.isLogin = this.$store.getters.userInfo != null;\n  },\n  mounted: function mounted() {\n    // this.updateMenu()\n  },\n  watch: {\n    collapsed: function collapsed(val) {\n      if (val) {\n        this.cachedOpenKeys = this.openKeys.concat();\n        this.openKeys = [];\n      } else {\n        this.openKeys = this.cachedOpenKeys;\n      }\n    },\n    $route: function $route() {\n      // this.updateMenu()\n    }\n  },\n  methods: {\n    // select menu item\n    onOpenChange: function onOpenChange(openKeys) {\n      var _this = this;\n      // 在水平模式下时执行，并且不再执行后续\n      if (this.mode === 'horizontal') {\n        this.openKeys = openKeys;\n        return;\n      }\n      // 非水平模式时\n      var latestOpenKey = openKeys.find(function (key) {\n        return !_this.openKeys.includes(key);\n      });\n      if (!this.rootSubmenuKeys.includes(latestOpenKey)) {\n        this.openKeys = openKeys;\n      } else {\n        this.openKeys = latestOpenKey ? [latestOpenKey] : [];\n      }\n    },\n    // updateMenu() {\n    //   const routes = this.$route.matched.concat()\n    //   const { hidden } = this.$route.meta\n    //   if (routes.length >= 3 && hidden) {\n    //     routes.pop()\n    //     this.selectedKeys = [routes[routes.length - 1].path]\n    //   } else {\n    //     this.selectedKeys = [routes.pop().path]\n    //   }\n    //   const openKeys = []\n    //   if (this.mode === 'inline') {\n    //     routes.forEach(item => {\n    //       openKeys.push(item.path)\n    //     })\n    //   }\n    //   //update-begin-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对\n    //   if (!this.selectedKeys || this.selectedKeys[0].indexOf(\":\") < 0) {\n    //     this.collapsed ? (this.cachedOpenKeys = openKeys) : (this.openKeys = openKeys)\n    //   }\n    //   //update-end-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对\n    // },\n    // render\n    renderItem: function renderItem(menu) {\n      if (!menu.hidden) {\n        return menu.children ? this.renderSubMenu(menu) : this.renderMenuItem(menu);\n      }\n      return null;\n    },\n    renderMenuItem: function renderMenuItem(menu) {\n      var h = this.$createElement;\n      var tag = menu.route && !menu.internalOrExternal ? 'router-link' : 'a';\n      var target = menu.internalOrExternal ? \"_blank\" : \"_self\";\n      var props = {\n        to: {\n          path: menu.url\n        }\n      };\n      var attrs = {\n        href: menu.url,\n        target: target\n      };\n      if (menu.needLogin && !this.isLogin) {\n        return;\n      }\n      return h(Item, _mergeJSXProps([{}, {\n        key: menu.id\n      }]), [h(tag, {\n        \"props\": _objectSpread({}, props),\n        \"attrs\": _objectSpread({}, attrs)\n      }, [this.renderIcon(menu.icon), h(\"span\", [menu.title])])]);\n    },\n    renderSubMenu: function renderSubMenu(menu) {\n      var _this2 = this;\n      var h = this.$createElement;\n      var tag = menu.route && !menu.internalOrExternal ? 'router-link' : 'a';\n      var props = {\n        to: {\n          path: menu.url\n        }\n      };\n      var target = menu.internalOrExternal ? \"_blank\" : \"_self\";\n      var attrs = {\n        href: menu.url,\n        target: target\n      };\n      var itemArr = [];\n      menu.children.forEach(function (item) {\n        return itemArr.push(_this2.renderItem(item));\n      });\n      return h(SubMenu, _mergeJSXProps2([{}, {\n        key: menu.id\n      }]), [h(tag, {\n        \"slot\": \"title\",\n        \"props\": _objectSpread({}, props),\n        \"attrs\": _objectSpread({}, attrs)\n      }, [this.renderIcon(menu.icon), h(\"span\", [menu.title])]), itemArr]);\n    },\n    renderIcon: function renderIcon(icon) {\n      var h = this.$createElement;\n      if (icon === 'none' || icon === undefined) {\n        return null;\n      }\n      var props = {};\n      _typeof(icon) === 'object' ? props.component = icon : props.type = icon;\n      return h(Icon, {\n        \"props\": _objectSpread({}, props)\n      });\n    }\n  },\n  render: function render() {\n    var _this3 = this;\n    var h = arguments[0];\n    var mode = this.mode,\n      theme = this.theme,\n      menu = this.menu;\n    var props = {\n      mode: mode,\n      theme: theme,\n      openKeys: this.openKeys,\n      overflowedIndicator: h(\"a-icon\", {\n        \"style\": \"color:white\",\n        \"attrs\": {\n          \"type\": \"menu\"\n        }\n      })\n    };\n    var on = {\n      select: function select(obj) {\n        _this3.selectedKeys = obj.selectedKeys;\n        _this3.$emit('select', obj);\n      },\n      openChange: this.onOpenChange\n    };\n    var menuTree = menu.map(function (item) {\n      if (item.hidden) {\n        return null;\n      }\n      return _this3.renderItem(item);\n    });\n    // {...{ props, on: on }}\n    return h(Menu, {\n      \"props\": _objectSpread({}, props),\n      \"on\": _objectSpread({}, on),\n      \"model\": {\n        value: _this3.selectedKeys,\n        callback: function callback($$v) {\n          _this3.selectedKeys = $$v;\n        }\n      }\n    }, [menuTree]);\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Icon", "<PERSON><PERSON>", "SubMenu", "name", "props", "menu", "type", "Array", "required", "theme", "String", "default", "mode", "collapsed", "Boolean", "data", "openKeys", "<PERSON><PERSON><PERSON><PERSON>", "cachedOpenKeys", "is<PERSON>ogin", "computed", "rootSubmenuKeys", "vm", "keys", "for<PERSON>ach", "item", "push", "path", "created", "$store", "getters", "userInfo", "mounted", "watch", "val", "concat", "$route", "methods", "onOpenChange", "_this", "latestOpenKey", "find", "key", "includes", "renderItem", "hidden", "children", "renderSubMenu", "renderMenuItem", "h", "$createElement", "tag", "route", "internalOrExternal", "target", "to", "url", "attrs", "href", "needLogin", "_mergeJSXProps", "id", "_objectSpread", "renderIcon", "icon", "title", "_this2", "itemArr", "_mergeJSXProps2", "undefined", "_typeof", "component", "render", "_this3", "arguments", "overflowedIndicator", "on", "select", "obj", "$emit", "openChange", "menuTree", "map", "value", "callback", "$$v"], "sources": ["E:/teachingproject/teaching/web/src/components/menu/tmenu.js"], "sourcesContent": ["import Menu from 'ant-design-vue/es/menu'\nimport Icon from 'ant-design-vue/es/icon'\n\nconst { Item, SubMenu } = Menu\n\nexport default {\n  name: 'TMenu',\n  props: {\n    menu: {\n      type: Array,\n      required: true\n    },\n    theme: {\n      type: String,\n      required: false,\n      default: 'light'\n    },\n    mode: {\n      type: String,\n      required: false,\n      default: 'inline'\n    },\n    collapsed: {\n      type: Boolean,\n      required: false,\n      default: false\n    }\n  },\n  data() {\n    return {\n      openKeys: [],\n      selectedKeys: [],\n      cachedOpenKeys: [],\n      isLogin: false\n    }\n  },\n  computed: {\n    rootSubmenuKeys: vm => {\n      const keys = []\n      vm.menu.forEach(item => keys.push(item.path))\n      return keys\n    }\n  },\n  created() {\n    this.isLogin = this.$store.getters.userInfo != null;\n  },\n  mounted() {\n    // this.updateMenu()\n  },\n  watch: {\n    collapsed(val) {\n      if (val) {\n        this.cachedOpenKeys = this.openKeys.concat()\n        this.openKeys = []\n      } else {\n        this.openKeys = this.cachedOpenKeys\n      }\n    },\n    $route: function () {\n      // this.updateMenu()\n    }\n  },\n  methods: {\n    // select menu item\n    onOpenChange(openKeys) {\n\n      // 在水平模式下时执行，并且不再执行后续\n      if (this.mode === 'horizontal') {\n        this.openKeys = openKeys\n        return\n      }\n      // 非水平模式时\n      const latestOpenKey = openKeys.find(key => !this.openKeys.includes(key))\n      if (!this.rootSubmenuKeys.includes(latestOpenKey)) {\n        this.openKeys = openKeys\n      } else {\n        this.openKeys = latestOpenKey ? [latestOpenKey] : []\n      }\n    },\n    // updateMenu() {\n    //   const routes = this.$route.matched.concat()\n    //   const { hidden } = this.$route.meta\n    //   if (routes.length >= 3 && hidden) {\n    //     routes.pop()\n    //     this.selectedKeys = [routes[routes.length - 1].path]\n    //   } else {\n    //     this.selectedKeys = [routes.pop().path]\n    //   }\n    //   const openKeys = []\n    //   if (this.mode === 'inline') {\n    //     routes.forEach(item => {\n    //       openKeys.push(item.path)\n    //     })\n    //   }\n    //   //update-begin-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对\n    //   if (!this.selectedKeys || this.selectedKeys[0].indexOf(\":\") < 0) {\n    //     this.collapsed ? (this.cachedOpenKeys = openKeys) : (this.openKeys = openKeys)\n    //   }\n    //   //update-end-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对\n    // },\n\n    // render\n    renderItem(menu) {\n      if (!menu.hidden) {\n        return menu.children ? this.renderSubMenu(menu) : this.renderMenuItem(menu)\n      }\n      return null\n    },\n    renderMenuItem(menu) {\n      const tag = (menu.route && !menu.internalOrExternal) ? 'router-link' : 'a'\n      const target = menu.internalOrExternal ? \"_blank\" : \"_self\"\n      const props = { to: { path: menu.url } }\n      const attrs = { href: menu.url, target: target }\n\n      if(menu.needLogin && !this.isLogin){\n        return\n      }\n      return (\n        <Item {...{ key: menu.id }}>\n          <tag {...{ props, attrs }}>\n            {this.renderIcon(menu.icon)}\n            <span>{menu.title}</span>\n          </tag>\n        </Item>\n      )\n    },\n    renderSubMenu(menu) {\n      const tag = (menu.route && !menu.internalOrExternal) ? 'router-link' : 'a'\n      const props = { to: { path: menu.url } }\n      const target = menu.internalOrExternal ? \"_blank\" : \"_self\"\n      const attrs = { href: menu.url, target: target}\n      const itemArr = []\n      menu.children.forEach(item => itemArr.push(this.renderItem(item)))\n      return (\n        <SubMenu {...{ key: menu.id }}>\n          <tag slot=\"title\" {...{props, attrs}}>\n            {this.renderIcon(menu.icon)}\n            <span>{menu.title}</span>\n          </tag>\n          {itemArr}\n        </SubMenu>\n      )\n    },\n    renderIcon(icon) {\n      if (icon === 'none' || icon === undefined) {\n        return null\n      }\n      const props = {}\n      typeof (icon) === 'object' ? props.component = icon : props.type = icon\n      return (\n        <Icon {... { props }} />\n      )\n    }\n  },\n\n  render() {\n    const { mode, theme, menu } = this\n    const props = {\n      mode: mode,\n      theme: theme,\n      openKeys: this.openKeys,\n      overflowedIndicator: <a-icon style=\"color:white\" type=\"menu\"/>\n    }\n    const on = {\n      select: obj => {\n        this.selectedKeys = obj.selectedKeys\n        this.$emit('select', obj)\n      },\n      openChange: this.onOpenChange\n    }\n\n    const menuTree = menu.map(item => {\n      if (item.hidden) {\n        return null\n      }\n      return this.renderItem(item)\n    })\n    // {...{ props, on: on }}\n    return (\n      <Menu vModel={this.selectedKeys} {...{ props, on: on }}>\n        {menuTree}\n      </Menu>\n    )\n  }\n}\n"], "mappings": ";;;;;;;AAAA,OAAOA,IAAI,MAAM,wBAAwB;AACzC,OAAOC,IAAI,MAAM,wBAAwB;AAEzC,IAAQC,IAAI,GAAcF,IAAI,CAAtBE,IAAI;EAAEC,OAAO,GAAKH,IAAI,CAAhBG,OAAO;AAErB,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,KAAK;MACXC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEI,MAAM;MACZF,QAAQ,EAAE,KAAK;MACfG,OAAO,EAAE;IACX,CAAC;IACDC,IAAI,EAAE;MACJN,IAAI,EAAEI,MAAM;MACZF,QAAQ,EAAE,KAAK;MACfG,OAAO,EAAE;IACX,CAAC;IACDE,SAAS,EAAE;MACTP,IAAI,EAAEQ,OAAO;MACbN,QAAQ,EAAE,KAAK;MACfG,OAAO,EAAE;IACX;EACF,CAAC;EACDI,IAAI,WAAAA,KAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,eAAe,EAAE,SAAAA,gBAAAC,EAAE,EAAI;MACrB,IAAMC,IAAI,GAAG,EAAE;MACfD,EAAE,CAACjB,IAAI,CAACmB,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAIF,IAAI,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;MAAA,EAAC;MAC7C,OAAOJ,IAAI;IACb;EACF,CAAC;EACDK,OAAO,WAAAA,QAAA,EAAG;IACR,IAAI,CAACT,OAAO,GAAG,IAAI,CAACU,MAAM,CAACC,OAAO,CAACC,QAAQ,IAAI,IAAI;EACrD,CAAC;EACDC,OAAO,WAAAA,QAAA,EAAG;IACR;EAAA,CACD;EACDC,KAAK,EAAE;IACLpB,SAAS,WAAAA,UAACqB,GAAG,EAAE;MACb,IAAIA,GAAG,EAAE;QACP,IAAI,CAAChB,cAAc,GAAG,IAAI,CAACF,QAAQ,CAACmB,MAAM,CAAC,CAAC;QAC5C,IAAI,CAACnB,QAAQ,GAAG,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACE,cAAc;MACrC;IACF,CAAC;IACDkB,MAAM,EAAE,SAAAA,OAAA,EAAY;MAClB;IAAA;EAEJ,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,YAAY,WAAAA,aAACtB,QAAQ,EAAE;MAAA,IAAAuB,KAAA;MAErB;MACA,IAAI,IAAI,CAAC3B,IAAI,KAAK,YAAY,EAAE;QAC9B,IAAI,CAACI,QAAQ,GAAGA,QAAQ;QACxB;MACF;MACA;MACA,IAAMwB,aAAa,GAAGxB,QAAQ,CAACyB,IAAI,CAAC,UAAAC,GAAG;QAAA,OAAI,CAACH,KAAI,CAACvB,QAAQ,CAAC2B,QAAQ,CAACD,GAAG,CAAC;MAAA,EAAC;MACxE,IAAI,CAAC,IAAI,CAACrB,eAAe,CAACsB,QAAQ,CAACH,aAAa,CAAC,EAAE;QACjD,IAAI,CAACxB,QAAQ,GAAGA,QAAQ;MAC1B,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,GAAGwB,aAAa,GAAG,CAACA,aAAa,CAAC,GAAG,EAAE;MACtD;IACF,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAI,UAAU,WAAAA,WAACvC,IAAI,EAAE;MACf,IAAI,CAACA,IAAI,CAACwC,MAAM,EAAE;QAChB,OAAOxC,IAAI,CAACyC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC1C,IAAI,CAAC,GAAG,IAAI,CAAC2C,cAAc,CAAC3C,IAAI,CAAC;MAC7E;MACA,OAAO,IAAI;IACb,CAAC;IACD2C,cAAc,WAAAA,eAAC3C,IAAI,EAAE;MAAA,IAAA4C,CAAA,QAAAC,cAAA;MACnB,IAAMC,GAAG,GAAI9C,IAAI,CAAC+C,KAAK,IAAI,CAAC/C,IAAI,CAACgD,kBAAkB,GAAI,aAAa,GAAG,GAAG;MAC1E,IAAMC,MAAM,GAAGjD,IAAI,CAACgD,kBAAkB,GAAG,QAAQ,GAAG,OAAO;MAC3D,IAAMjD,KAAK,GAAG;QAAEmD,EAAE,EAAE;UAAE5B,IAAI,EAAEtB,IAAI,CAACmD;QAAI;MAAE,CAAC;MACxC,IAAMC,KAAK,GAAG;QAAEC,IAAI,EAAErD,IAAI,CAACmD,GAAG;QAAEF,MAAM,EAAEA;MAAO,CAAC;MAEhD,IAAGjD,IAAI,CAACsD,SAAS,IAAI,CAAC,IAAI,CAACxC,OAAO,EAAC;QACjC;MACF;MACA,OAAA8B,CAAA,CAAAhD,IAAA,EAAA2D,cAAA,MACY;QAAElB,GAAG,EAAErC,IAAI,CAACwD;MAAG,CAAC,KAAAZ,CAAA,CAAAE,GAAA;QAAA,SAAAW,aAAA,KACb1D,KAAK;QAAA,SAAA0D,aAAA,KAAEL,KAAK;MAAA,IACpB,IAAI,CAACM,UAAU,CAAC1D,IAAI,CAAC2D,IAAI,CAAC,EAAAf,CAAA,UACpB5C,IAAI,CAAC4D,KAAK;IAIzB,CAAC;IACDlB,aAAa,WAAAA,cAAC1C,IAAI,EAAE;MAAA,IAAA6D,MAAA;MAAA,IAAAjB,CAAA,QAAAC,cAAA;MAClB,IAAMC,GAAG,GAAI9C,IAAI,CAAC+C,KAAK,IAAI,CAAC/C,IAAI,CAACgD,kBAAkB,GAAI,aAAa,GAAG,GAAG;MAC1E,IAAMjD,KAAK,GAAG;QAAEmD,EAAE,EAAE;UAAE5B,IAAI,EAAEtB,IAAI,CAACmD;QAAI;MAAE,CAAC;MACxC,IAAMF,MAAM,GAAGjD,IAAI,CAACgD,kBAAkB,GAAG,QAAQ,GAAG,OAAO;MAC3D,IAAMI,KAAK,GAAG;QAAEC,IAAI,EAAErD,IAAI,CAACmD,GAAG;QAAEF,MAAM,EAAEA;MAAM,CAAC;MAC/C,IAAMa,OAAO,GAAG,EAAE;MAClB9D,IAAI,CAACyC,QAAQ,CAACtB,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAI0C,OAAO,CAACzC,IAAI,CAACwC,MAAI,CAACtB,UAAU,CAACnB,IAAI,CAAC,CAAC;MAAA,EAAC;MAClE,OAAAwB,CAAA,CAAA/C,OAAA,EAAAkE,eAAA,MACe;QAAE1B,GAAG,EAAErC,IAAI,CAACwD;MAAG,CAAC,KAAAZ,CAAA,CAAAE,GAAA;QAAA,QACjB,OAAO;QAAA,SAAAW,aAAA,KAAM1D,KAAK;QAAA,SAAA0D,aAAA,KAAEL,KAAK;MAAA,IAChC,IAAI,CAACM,UAAU,CAAC1D,IAAI,CAAC2D,IAAI,CAAC,EAAAf,CAAA,UACpB5C,IAAI,CAAC4D,KAAK,MAElBE,OAAO;IAGd,CAAC;IACDJ,UAAU,WAAAA,WAACC,IAAI,EAAE;MAAA,IAAAf,CAAA,QAAAC,cAAA;MACf,IAAIc,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAKK,SAAS,EAAE;QACzC,OAAO,IAAI;MACb;MACA,IAAMjE,KAAK,GAAG,CAAC,CAAC;MAChBkE,OAAA,CAAQN,IAAI,MAAM,QAAQ,GAAG5D,KAAK,CAACmE,SAAS,GAAGP,IAAI,GAAG5D,KAAK,CAACE,IAAI,GAAG0D,IAAI;MACvE,OAAAf,CAAA,CAAAjD,IAAA;QAAA,SAAA8D,aAAA,KACe1D,KAAK;MAAA;IAEtB;EACF,CAAC;EAEDoE,MAAM,WAAAA,OAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,IAAAxB,CAAA,GAAAyB,SAAA;IACP,IAAQ9D,IAAI,GAAkB,IAAI,CAA1BA,IAAI;MAAEH,KAAK,GAAW,IAAI,CAApBA,KAAK;MAAEJ,IAAI,GAAK,IAAI,CAAbA,IAAI;IACzB,IAAMD,KAAK,GAAG;MACZQ,IAAI,EAAEA,IAAI;MACVH,KAAK,EAAEA,KAAK;MACZO,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB2D,mBAAmB,EAAA1B,CAAA;QAAA,SAAgB,aAAa;QAAA;UAAA,QAAM;QAAM;MAAA;IAC9D,CAAC;IACD,IAAM2B,EAAE,GAAG;MACTC,MAAM,EAAE,SAAAA,OAAAC,GAAG,EAAI;QACbL,MAAI,CAACxD,YAAY,GAAG6D,GAAG,CAAC7D,YAAY;QACpCwD,MAAI,CAACM,KAAK,CAAC,QAAQ,EAAED,GAAG,CAAC;MAC3B,CAAC;MACDE,UAAU,EAAE,IAAI,CAAC1C;IACnB,CAAC;IAED,IAAM2C,QAAQ,GAAG5E,IAAI,CAAC6E,GAAG,CAAC,UAAAzD,IAAI,EAAI;MAChC,IAAIA,IAAI,CAACoB,MAAM,EAAE;QACf,OAAO,IAAI;MACb;MACA,OAAO4B,MAAI,CAAC7B,UAAU,CAACnB,IAAI,CAAC;IAC9B,CAAC,CAAC;IACF;IACA,OAAAwB,CAAA,CAAAlD,IAAA;MAAA,SAAA+D,aAAA,KACyC1D,KAAK;MAAA,MAAA0D,aAAA,KAAMc,EAAE;MAAA;QAAAO,KAAA,EAAtCV,MAAI,CAACxD,YAAY;QAAAmE,QAAA,WAAAA,SAAAC,GAAA;UAAjBZ,MAAI,CAACxD,YAAY,GAAAoE,GAAA;QAAA;MAAA;IAAA,IAC5BJ,QAAQ;EAGf;AACF,CAAC", "ignoreList": []}]}