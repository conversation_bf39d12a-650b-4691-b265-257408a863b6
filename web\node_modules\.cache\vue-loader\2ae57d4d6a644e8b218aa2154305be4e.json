{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import STable from '@/components/table/'\n  import ATextarea from \"ant-design-vue/es/input/TextArea\"\n  import AInput from \"ant-design-vue/es/input/Input\"\n  import moment from \"moment\"\n  import axios from 'axios';\n  import { getRoleList, getServiceList } from '@/api/manage'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      AInput,\n      ATextarea,\n      STable\n    },\n    data () {\n      return {\n        visibleCreateModal:false,\n        visible: false,\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 12 },\n        },\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: true,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '规则编号',\n            dataIndex: 'no'\n          },\n          {\n            title: '描述',\n            dataIndex: 'description'\n          },\n          {\n            title: '服务调用次数',\n            dataIndex: 'callNo',\n            sorter: true,\n            needTotal: true,\n            customRender: (text) => text + ' 次'\n          },\n          {\n            title: '状态',\n            dataIndex: 'status',\n            needTotal: true\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updatedAt',\n            sorter: true\n          },\n          {\n            table: '操作',\n            dataIndex: 'action',\n            width: '150px',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return getServiceList(Object.assign(parameter, this.queryParam))\n            .then(res => {\n              return res.result\n            })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    created () {\n      getRoleList({ t: new Date()})\n    },\n    methods: {\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n\n      },\n\n      //添加逻辑\n      handleModalVisible(isVisible) {\n        this.visibleCreateModal = isVisible;\n      },\n      handleCreateModalOk() {\n        this.createForm.validateFields((err, fieldsValue) => {\n          if (err) {\n            return;\n          }\n          const description = this.createForm.getFieldValue('description');\n          axios.post('/saveRule', {\n            desc: description,\n          }).then((res) => {\n            this.createForm.resetFields();\n            this.visibleCreateModal = false;\n            this.loadRuleData();\n          });\n        });\n      },\n      handleCreateModalCancel() {\n        this.visibleCreateModal = false;\n      },\n\n      onChange (row) {\n        this.selectedRowKeys = row.selectedRowKeys\n        this.selectedRows = row.selectedRows\n\n        console.log(this.$refs.table)\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n\n      resetSearchForm () {\n        this.queryParam = {\n          date: moment(new Date())\n        }\n      }\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n", {"version": 3, "sources": ["TableList.vue"], "names": [], "mappings": ";AA8LA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TableList.vue", "sourceRoot": "src/views/list", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"48\">\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"规则编号\">\n              <a-input v-model=\"queryParam.id\" placeholder=\"\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"使用状态\">\n              <a-select v-model=\"queryParam.status\" placeholder=\"请选择\" default-value=\"0\">\n                <a-select-option value=\"0\">全部</a-select-option>\n                <a-select-option value=\"1\">关闭</a-select-option>\n                <a-select-option value=\"2\">运行中</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"advanced\">\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"调用次数\">\n                <a-input-number v-model=\"queryParam.callNo\" style=\"width: 100%\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"更新日期\">\n                <a-date-picker v-model=\"queryParam.date\" style=\"width: 100%\" placeholder=\"请输入更新日期\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select v-model=\"queryParam.useStatus\" placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :md=\"!advanced && 8 || 24\" :sm=\"24\">\n            <span class=\"table-page-search-submitButtons\" :style=\"advanced && { float: 'right', overflow: 'hidden' } || {} \">\n              <a-button type=\"primary\">查询</a-button>\n              <a-button style=\"margin-left: 8px\" @click=\"resetSearchForm\">重置</a-button>\n              <a @click=\"toggleAdvanced\" style=\"margin-left: 8px\">\n                {{ advanced ? '收起' : '展开' }}\n                <a-icon :type=\"advanced ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <div class=\"table-operator\">\n      <a-button type=\"primary\" icon=\"plus\" @click=\"() => this.handleModalVisible(true)\">新建</a-button>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\"><a-icon type=\"delete\" />删除</a-menu-item>\n          <!-- lock | unlock -->\n          <a-menu-item key=\"2\"><a-icon type=\"lock\" />锁定</a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作 <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <s-table\n      ref=\"table\"\n      size=\"default\"\n      :columns=\"columns\"\n      :data=\"loadData\"\n      :showAlertInfo=\"true\"\n      @onSelect=\"onChange\"\n    >\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">\n            更多 <a-icon type=\"down\" />\n          </a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a href=\"javascript:;\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">禁用</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">删除</a>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </s-table>\n\n    <a-modal\n      title=\"操作\"\n      :width=\"800\"\n      v-model=\"visible\"\n      @ok=\"handleOk\"\n    >\n      <a-form :autoFormCreate=\"(form)=>{this.form = form}\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"规则编号\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input placeholder=\"规则编号\" v-model=\"mdl.no\" id=\"no\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"服务调用次数\"\n          hasFeedback\n          validateStatus=\"success\"\n        >\n          <a-input-number :min=\"1\" id=\"callNo\" v-model=\"mdl.callNo\" style=\"width: 100%\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"状态\"\n          hasFeedback\n          validateStatus=\"warning\"\n        >\n          <a-select defaultValue=\"1\" v-model=\"mdl.status\">\n            <a-select-option value=\"1\">Option 1</a-select-option>\n            <a-select-option value=\"2\">Option 2</a-select-option>\n            <a-select-option value=\"3\">Option 3</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"描述\"\n          hasFeedback\n          help=\"请填写一段描述\"\n        >\n          <a-textarea :rows=\"5\" v-model=\"mdl.description\" placeholder=\"...\" id=\"description\"/>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"更新时间\"\n          hasFeedback\n          validateStatus=\"error\"\n        >\n          <a-date-picker\n            style=\"width: 100%\"\n            showTime\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            placeholder=\"Select Time\"\n          />\n        </a-form-item>\n\n      </a-form>\n    </a-modal>\n\n    <a-modal title=\"新建规则\" destroyOnClose :visible=\"visibleCreateModal\" @ok=\"handleCreateModalOk\" @cancel=\"handleCreateModalCancel\">\n      <!---->\n      <a-form style=\"margin-top: 8px\" :autoFormCreate=\"(form)=>{this.createForm = form}\">\n        <a-form-item :labelCol=\"{ span: 5 }\" :wrapperCol=\"{ span: 15 }\" label=\"描述\" fieldDecoratorId=\"description\" :fieldDecoratorOptions=\"{rules: [{ required: true, message: '请输入至少五个字符的规则描述！', min: 5 }]}\">\n          <a-input placeholder=\"请输入\" />\n        </a-form-item>\n      </a-form>\n    </a-modal>\n\n  </a-card>\n</template>\n\n<script>\n  import STable from '@/components/table/'\n  import ATextarea from \"ant-design-vue/es/input/TextArea\"\n  import AInput from \"ant-design-vue/es/input/Input\"\n  import moment from \"moment\"\n  import axios from 'axios';\n  import { getRoleList, getServiceList } from '@/api/manage'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      AInput,\n      ATextarea,\n      STable\n    },\n    data () {\n      return {\n        visibleCreateModal:false,\n        visible: false,\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 12 },\n        },\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: true,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '规则编号',\n            dataIndex: 'no'\n          },\n          {\n            title: '描述',\n            dataIndex: 'description'\n          },\n          {\n            title: '服务调用次数',\n            dataIndex: 'callNo',\n            sorter: true,\n            needTotal: true,\n            customRender: (text) => text + ' 次'\n          },\n          {\n            title: '状态',\n            dataIndex: 'status',\n            needTotal: true\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updatedAt',\n            sorter: true\n          },\n          {\n            table: '操作',\n            dataIndex: 'action',\n            width: '150px',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return getServiceList(Object.assign(parameter, this.queryParam))\n            .then(res => {\n              return res.result\n            })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    created () {\n      getRoleList({ t: new Date()})\n    },\n    methods: {\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n\n      },\n\n      //添加逻辑\n      handleModalVisible(isVisible) {\n        this.visibleCreateModal = isVisible;\n      },\n      handleCreateModalOk() {\n        this.createForm.validateFields((err, fieldsValue) => {\n          if (err) {\n            return;\n          }\n          const description = this.createForm.getFieldValue('description');\n          axios.post('/saveRule', {\n            desc: description,\n          }).then((res) => {\n            this.createForm.resetFields();\n            this.visibleCreateModal = false;\n            this.loadRuleData();\n          });\n        });\n      },\n      handleCreateModalCancel() {\n        this.visibleCreateModal = false;\n      },\n\n      onChange (row) {\n        this.selectedRowKeys = row.selectedRowKeys\n        this.selectedRows = row.selectedRows\n\n        console.log(this.$refs.table)\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n\n      resetSearchForm () {\n        this.queryParam = {\n          date: moment(new Date())\n        }\n      }\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n</script>"]}]}