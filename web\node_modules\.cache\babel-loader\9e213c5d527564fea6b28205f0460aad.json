{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import PageLayout from '@/components/page/PageLayout';\nimport RouteView from \"@/components/layouts/RouteView\";\nimport { getAction } from '@/api/manage';\nimport { mixinDevice } from '@/utils/mixin.js';\nexport default {\n  components: {\n    RouteView: RouteView,\n    PageLayout: PageLayout\n  },\n  mixins: [mixinDevice],\n  data: function data() {\n    return {\n      // horizontal  inline\n      mode: 'inline',\n      mainInfoHeight: \"100%\",\n      openKeys: [],\n      defaultSelectedKeys: [],\n      // cropper\n      preview: {},\n      option: {\n        img: '/avatar2.jpg',\n        info: true,\n        size: 1,\n        outputType: 'jpeg',\n        canScale: false,\n        autoCrop: true,\n        // 只有自动截图开启 宽度高度才生效\n        autoCropWidth: 180,\n        autoCropHeight: 180,\n        fixedBox: true,\n        // 开启宽度和高度比例\n        fixed: true,\n        fixedNumber: [1, 1]\n      },\n      courseList: [],\n      url: {\n        mineCourseList: '/teaching/teachingCourse/mineCourse'\n      },\n      pageTitle: ''\n    };\n  },\n  created: function created() {\n    this.initMineCourse();\n    this.updateMenu();\n  },\n  mounted: function mounted() {\n    this.mainInfoHeight = window.innerHeight - 285 + \"px\";\n  },\n  methods: {\n    onOpenChange: function onOpenChange(openKeys) {\n      this.openKeys = openKeys;\n    },\n    updateMenu: function updateMenu() {\n      var routes = this.$route.matched.concat();\n      this.defaultSelectedKeys = [routes.pop().path];\n    },\n    initMineCourse: function initMineCourse() {\n      var _this = this;\n      getAction(this.url.mineCourseList, {}).then(function (res) {\n        if (res.success) {\n          _this.courseList = res.result;\n        } else {\n          console.log(res);\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["PageLayout", "RouteView", "getAction", "mixinDevice", "components", "mixins", "data", "mode", "mainInfoHeight", "openKeys", "defaultSelectedKeys", "preview", "option", "img", "info", "size", "outputType", "canScale", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "fixed", "fixedNumber", "courseList", "url", "mineCourseList", "pageTitle", "created", "initMineCourse", "updateMenu", "mounted", "window", "innerHeight", "methods", "onOpenChange", "routes", "$route", "matched", "concat", "pop", "path", "_this", "then", "res", "success", "result", "console", "log"], "sources": ["src/views/account/course/Index.vue"], "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n      <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n        <div class=\"account-settings-info-left\">\n          <a-menu\n            :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n            :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n            :defaultSelectedKeys=\"defaultSelectedKeys\"\n            type=\"inner\"\n            @openChange=\"onOpenChange\"\n          >\n            <a-menu-item v-for=\"(course,index) in courseList\" :key=\"'/teaching/mineCourse/course?id='+course.id\">\n              <router-link :to=\"{ name: 'teaching-mineCourse-course',  query: {id:course.id}}\" :meta=\"{keepAlive:false}\">\n                {{course.courseName}}\n              </router-link>\n            </a-menu-item>\n          </a-menu>\n        </div>\n        <div class=\"account-settings-info-right\">\n          <route-view></route-view>\n        </div>\n      </div>\n    </a-card>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { getAction } from '@/api/manage'\n  import { mixinDevice } from '@/utils/mixin.js'\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout\n    },\n    mixins: [mixinDevice],\n    data () {\n      return {\n        // horizontal  inline\n        mode: 'inline',\n        mainInfoHeight:\"100%\",\n        openKeys: [],\n        defaultSelectedKeys: [],\n\n        // cropper\n        preview: {},\n        option: {\n          img: '/avatar2.jpg',\n          info: true,\n          size: 1,\n          outputType: 'jpeg',\n          canScale: false,\n          autoCrop: true,\n          // 只有自动截图开启 宽度高度才生效\n          autoCropWidth: 180,\n          autoCropHeight: 180,\n          fixedBox: true,\n          // 开启宽度和高度比例\n          fixed: true,\n          fixedNumber: [1, 1]\n        },\n        courseList: [],\n        url: {\n          mineCourseList: '/teaching/teachingCourse/mineCourse'\n        },\n        pageTitle: ''\n      }\n    },\n    created () {\n      this.initMineCourse()\n      this.updateMenu()\n    },\n    mounted(){\n      this.mainInfoHeight = (window.innerHeight-285)+\"px\";\n    },\n    methods: {\n      onOpenChange (openKeys) {\n        this.openKeys = openKeys\n      },\n      updateMenu () {\n        let routes = this.$route.matched.concat()\n        this.defaultSelectedKeys = [ routes.pop().path ]\n      },\n      initMineCourse(){\n        getAction(this.url.mineCourseList, {}).then(res => {\n          if(res.success){\n            this.courseList = res.result\n          }else{\n            console.log(res)\n          }\n        })\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .account-settings-info-main {\n    width: 100%;\n    display: flex;\n    height: 100%;\n    overflow: auto;\n\n    &.mobile {\n      display: block;\n\n      .account-settings-info-left {\n        border-right: unset;\n        border-bottom: 1px solid #e8e8e8;\n        width: 100%;\n        height: 50px;\n        overflow-x: auto;\n        overflow-y: scroll;\n      }\n      .account-settings-info-right {\n        padding: 20px 40px;\n      }\n    }\n\n    .account-settings-info-left {\n      border-right: 1px solid #e8e8e8;\n      width: 224px;\n    }\n\n    .account-settings-info-right {\n      flex: 1 1;\n      padding: 8px 40px;\n      .account-settings-info-view {\n        padding-top: 12px;\n      }\n    }\n  }\n\n</style>"], "mappings": "AA4BA,OAAAA,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,SAAA;AACA,SAAAC,WAAA;AAEA;EACAC,UAAA;IACAH,SAAA,EAAAA,SAAA;IACAD,UAAA,EAAAA;EACA;EACAK,MAAA,GAAAF,WAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACAC,cAAA;MACAC,QAAA;MACAC,mBAAA;MAEA;MACAC,OAAA;MACAC,MAAA;QACAC,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACA;QACAC,aAAA;QACAC,cAAA;QACAC,QAAA;QACA;QACAC,KAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,GAAA;QACAC,cAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAvB,cAAA,GAAAwB,MAAA,CAAAC,WAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA1B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IACAqB,UAAA,WAAAA,WAAA;MACA,IAAAM,MAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA;MACA,KAAA7B,mBAAA,IAAA0B,MAAA,CAAAI,GAAA,GAAAC,IAAA;IACA;IACAZ,cAAA,WAAAA,eAAA;MAAA,IAAAa,KAAA;MACAxC,SAAA,MAAAuB,GAAA,CAAAC,cAAA,MAAAiB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAH,KAAA,CAAAlB,UAAA,GAAAoB,GAAA,CAAAE,MAAA;QACA;UACAC,OAAA,CAAAC,GAAA,CAAAJ,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}