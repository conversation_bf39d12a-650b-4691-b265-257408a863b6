{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Liquid.vue?vue&type=template&id=0aaf4b86&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Liquid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"v-chart\", {\n    attrs: {\n      forceFit: true,\n      height: _vm.height,\n      width: _vm.width,\n      data: _vm.data,\n      scale: _vm.scale,\n      padding: 0\n    }\n  }, [_c(\"v-tooltip\"), _c(\"v-interval\", {\n    attrs: {\n      shape: [\"liquid-fill-gauge\"],\n      position: \"transfer*value\",\n      color: \"\",\n      \"v-style\": {\n        lineWidth: 8,\n        opacity: 0.75\n      },\n      tooltip: [\"transfer*value\", function (transfer, value) {\n        return {\n          name: transfer,\n          value: value\n        };\n      }]\n    }\n  }), _vm._l(_vm.data, function (row, index) {\n    return _c(\"v-guide\", {\n      key: index,\n      attrs: {\n        type: \"text\",\n        top: true,\n        position: {\n          gender: row.transfer,\n          value: 45\n        },\n        content: row.value + \"%\",\n        \"v-style\": {\n          fontSize: 100,\n          textAlign: \"center\",\n          opacity: 0.75\n        }\n      }\n    });\n  })], 2)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "forceFit", "height", "width", "data", "scale", "padding", "shape", "position", "color", "lineWidth", "opacity", "tooltip", "transfer", "value", "name", "_l", "row", "index", "key", "type", "top", "gender", "content", "fontSize", "textAlign", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/chart/Liquid.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"v-chart\",\n        {\n          attrs: {\n            forceFit: true,\n            height: _vm.height,\n            width: _vm.width,\n            data: _vm.data,\n            scale: _vm.scale,\n            padding: 0,\n          },\n        },\n        [\n          _c(\"v-tooltip\"),\n          _c(\"v-interval\", {\n            attrs: {\n              shape: [\"liquid-fill-gauge\"],\n              position: \"transfer*value\",\n              color: \"\",\n              \"v-style\": {\n                lineWidth: 8,\n                opacity: 0.75,\n              },\n              tooltip: [\n                \"transfer*value\",\n                (transfer, value) => {\n                  return {\n                    name: transfer,\n                    value,\n                  }\n                },\n              ],\n            },\n          }),\n          _vm._l(_vm.data, function (row, index) {\n            return _c(\"v-guide\", {\n              key: index,\n              attrs: {\n                type: \"text\",\n                top: true,\n                position: {\n                  gender: row.transfer,\n                  value: 45,\n                },\n                content: row.value + \"%\",\n                \"v-style\": {\n                  fontSize: 100,\n                  textAlign: \"center\",\n                  opacity: 0.75,\n                },\n              },\n            })\n          }),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,SAAS,EACT;IACEE,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAEL,GAAG,CAACK,MAAM;MAClBC,KAAK,EAAEN,GAAG,CAACM,KAAK;MAChBC,IAAI,EAAEP,GAAG,CAACO,IAAI;MACdC,KAAK,EAAER,GAAG,CAACQ,KAAK;MAChBC,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACER,EAAE,CAAC,WAAW,CAAC,EACfA,EAAE,CAAC,YAAY,EAAE;IACfE,KAAK,EAAE;MACLO,KAAK,EAAE,CAAC,mBAAmB,CAAC;MAC5BC,QAAQ,EAAE,gBAAgB;MAC1BC,KAAK,EAAE,EAAE;MACT,SAAS,EAAE;QACTC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE,CACP,gBAAgB,EAChB,UAACC,QAAQ,EAAEC,KAAK,EAAK;QACnB,OAAO;UACLC,IAAI,EAAEF,QAAQ;UACdC,KAAK,EAALA;QACF,CAAC;MACH,CAAC;IAEL;EACF,CAAC,CAAC,EACFjB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACO,IAAI,EAAE,UAAUa,GAAG,EAAEC,KAAK,EAAE;IACrC,OAAOpB,EAAE,CAAC,SAAS,EAAE;MACnBqB,GAAG,EAAED,KAAK;MACVlB,KAAK,EAAE;QACLoB,IAAI,EAAE,MAAM;QACZC,GAAG,EAAE,IAAI;QACTb,QAAQ,EAAE;UACRc,MAAM,EAAEL,GAAG,CAACJ,QAAQ;UACpBC,KAAK,EAAE;QACT,CAAC;QACDS,OAAO,EAAEN,GAAG,CAACH,KAAK,GAAG,GAAG;QACxB,SAAS,EAAE;UACTU,QAAQ,EAAE,GAAG;UACbC,SAAS,EAAE,QAAQ;UACnBd,OAAO,EAAE;QACX;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIe,eAAe,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI;AAE3B,SAAS/B,MAAM,EAAE8B,eAAe", "ignoreList": []}]}