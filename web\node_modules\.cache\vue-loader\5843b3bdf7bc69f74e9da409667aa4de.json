{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue?vue&type=template&id=48d8f00f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue", "mtime": 1753199441584}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"user-enter\">\n  <div v-if=\"token\">\n    <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"avatarUrl\" />\n    <h3>欢迎您，{{ nickname() }}</h3>\n    <a-button type=\"primary\" @click=\"enter\">进入系统</a-button>\n    <a-button type=\"dashed\" @click=\"changeAccount\">切换账号</a-button>\n  </div>\n  <div v-else>\n    <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"logo2\" />\n    <h3 class=\"welcome\">欢迎来到{{ brandName }}</h3>\n    <a-button type=\"dashed\" @click=\"login\">登录/注册</a-button>\n  </div>\n</div>\n", null]}