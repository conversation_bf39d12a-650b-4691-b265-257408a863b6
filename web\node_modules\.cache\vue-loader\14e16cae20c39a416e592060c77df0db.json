{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\App.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n  const dataSource = []\n  for (let i = 0; i < 11; i++) {\n    dataSource.push({\n      title: '<PERSON>pay',\n      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n      activeUser: 17,\n      newUser: 1700\n    })\n  }\n\n  export default {\n    name: \"Article\",\n    components: {},\n    data() {\n      return {\n        dataSource,\n      }\n    }\n  }\n", {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AA4DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src/views/account/center/page", "sourcesContent": ["<template>\n  <div class=\"app-list\">\n    <a-list\n      :grid=\"{ gutter: 24, lg: 3, md: 2, sm: 1, xs: 1 }\"\n      :dataSource=\"dataSource\">\n      <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\n        <a-card :hoverable=\"true\">\n          <a-card-meta>\n            <div style=\"margin-bottom: 3px\" slot=\"title\">{{ item.title }}</div>\n            <a-avatar class=\"card-avatar\" slot=\"avatar\" :src=\"item.avatar\" size=\"small\" icon=\"user\"/>\n            <div class=\"meta-cardInfo\" slot=\"description\">\n              <div>\n                <p>活跃用户</p>\n                <p>\n                  <span>{{ item.activeUser }}<span>万</span></span>\n                </p>\n              </div>\n              <div>\n                <p>新增用户</p>\n                <p>{{ item.newUser | NumberFormat }}</p>\n              </div>\n            </div>\n          </a-card-meta>\n          <template class=\"ant-card-actions\" slot=\"actions\">\n            <a>\n              <a-icon type=\"download\"/>\n            </a>\n            <a>\n              <a-icon type=\"edit\"/>\n            </a>\n            <a>\n              <a-icon type=\"share-alt\"/>\n            </a>\n            <a>\n              <a-dropdown>\n                <a class=\"ant-dropdown-link\" href=\"javascript:;\">\n                  <a-icon type=\"ellipsis\"/>\n                </a>\n                <a-menu slot=\"overlay\">\n                  <a-menu-item>\n                    <a href=\"javascript:;\">1st menu item</a>\n                  </a-menu-item>\n                  <a-menu-item>\n                    <a href=\"javascript:;\">2nd menu item</a>\n                  </a-menu-item>\n                  <a-menu-item>\n                    <a href=\"javascript:;\">3rd menu item</a>\n                  </a-menu-item>\n                </a-menu>\n              </a-dropdown>\n            </a>\n          </template>\n        </a-card>\n      </a-list-item>\n    </a-list>\n\n  </div>\n</template>\n\n<script>\n  const dataSource = []\n  for (let i = 0; i < 11; i++) {\n    dataSource.push({\n      title: 'Alipay',\n      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n      activeUser: 17,\n      newUser: 1700\n    })\n  }\n\n  export default {\n    name: \"Article\",\n    components: {},\n    data() {\n      return {\n        dataSource,\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n\n  .app-list {\n\n    .meta-cardInfo {\n      zoom: 1;\n      margin-top: 16px;\n\n      > div {\n        position: relative;\n        text-align: left;\n        float: left;\n        width: 50%;\n\n        p {\n          line-height: 32px;\n          font-size: 24px;\n          margin: 0;\n\n          &:first-child {\n            color: rgba(0, 0, 0, .45);\n            font-size: 12px;\n            line-height: 20px;\n            margin-bottom: 4px;\n          }\n        }\n\n      }\n    }\n  }\n\n</style>\n"]}]}