{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue?vue&type=template&id=6d5cf9b0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n  <a-form style=\"max-width: 500px; margin: 40px auto 0;\">\n    <a-alert\n      :closable=\"true\"\n      message=\"确认转账后，资金将直接打入对方账户，无法退回。\"\n      style=\"margin-bottom: 24px;\"\n    />\n    <a-form-item\n      label=\"付款账户\"\n      :labelCol=\"{span: 5}\"\n      :wrapperCol=\"{span: 19}\"\n      class=\"stepFormText\"\n    >\n      <EMAIL>\n    </a-form-item>\n    <a-form-item\n      label=\"收款账户\"\n      :labelCol=\"{span: 5}\"\n      :wrapperCol=\"{span: 19}\"\n      class=\"stepFormText\"\n    >\n      <EMAIL>\n    </a-form-item>\n    <a-form-item\n      label=\"收款人姓名\"\n      :labelCol=\"{span: 5}\"\n      :wrapperCol=\"{span: 19}\"\n      class=\"stepFormText\"\n    >\n      Alex\n    </a-form-item>\n    <a-form-item\n      label=\"转账金额\"\n      :labelCol=\"{span: 5}\"\n      :wrapperCol=\"{span: 19}\"\n      class=\"stepFormText\"\n    >\n      ￥ 5,000.00\n    </a-form-item>\n    <a-form-item :wrapperCol=\"{span: 19, offset: 5}\">\n      <a-button :loading=\"loading\" type=\"primary\" @click=\"nextStep\">提交</a-button>\n      <a-button style=\"margin-left: 8px\" @click=\"prevStep\">上一步</a-button>\n    </a-form-item>\n  </a-form>\n</div>\n", null]}