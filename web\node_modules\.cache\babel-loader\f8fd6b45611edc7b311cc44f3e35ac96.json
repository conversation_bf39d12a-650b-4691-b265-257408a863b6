{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import Avatar from 'ant-design-vue/es/avatar';\nimport Tooltip from 'ant-design-vue/es/tooltip';\nexport default {\n  name: \"AvatarItem\",\n  components: {\n    Avatar: Avatar,\n    Tooltip: Tooltip\n  },\n  props: {\n    tips: {\n      type: String,\n      default: '',\n      required: false\n    },\n    src: {\n      type: String,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      size: this.$parent.size\n    };\n  },\n  computed: {\n    avatarSize: function avatarSize() {\n      return this.size !== 'mini' && this.size || 20;\n    }\n  },\n  watch: {\n    '$parent.size': function $parentSize(val) {\n      this.size = val;\n    }\n  }\n};", {"version": 3, "names": ["Avatar", "<PERSON><PERSON><PERSON>", "name", "components", "props", "tips", "type", "String", "default", "required", "src", "data", "size", "$parent", "computed", "avatarSize", "watch", "$parentSize", "val"], "sources": ["src/components/AvatarList/Item.vue"], "sourcesContent": ["<template>\n  <tooltip v-if=\"tips !== ''\">\n    <template slot=\"title\">{{ tips }}</template>\n    <avatar :size=\"avatarSize\" :src=\"src\" />\n  </tooltip>\n  <avatar v-else :size=\"avatarSize\" :src=\"src\" />\n</template>\n\n<script>\n  import Avatar from 'ant-design-vue/es/avatar'\n  import Tooltip from 'ant-design-vue/es/tooltip'\n\n  export default {\n    name: \"AvatarItem\",\n    components: {\n      Avatar,\n      Tooltip\n    },\n    props: {\n      tips: {\n        type: String,\n        default: '',\n        required: false\n      },\n      src: {\n        type: String,\n        default: ''\n      }\n    },\n    data () {\n      return {\n        size: this.$parent.size\n      }\n    },\n    computed: {\n      avatarSize () {\n        return this.size !== 'mini' && this.size || 20\n      }\n    },\n    watch: {\n      '$parent.size' (val) {\n        this.size = val\n      }\n    }\n  }\n</script>"], "mappings": "AASA,OAAAA,MAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,MAAA,EAAAA,MAAA;IACAC,OAAA,EAAAA;EACA;EACAG,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACAC,GAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,OAAA,CAAAD;IACA;EACA;EACAE,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAH,IAAA,oBAAAA,IAAA;IACA;EACA;EACAI,KAAA;IACA,yBAAAC,YAAAC,GAAA;MACA,KAAAN,IAAA,GAAAM,GAAA;IACA;EACA;AACA", "ignoreList": []}]}