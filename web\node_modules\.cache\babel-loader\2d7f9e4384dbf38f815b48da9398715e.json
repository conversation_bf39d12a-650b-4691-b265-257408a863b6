{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\wrongRecords.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\wrongRecords.vue", "mtime": 1753586761732}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getAction, deleteAction } from '@/api/manage';\nimport * as markedLib from 'marked';\nimport { getWrongRecordList, deleteWrongRecord, deleteBatchWrongRecord, queryProblemById, exportWrongRecords } from '@/api/examSystem';\nimport moment from 'moment';\nimport WrongQuestionPractice from './components/WrongQuestionPractice.vue';\n\n// 确保正确获取marked函数\nvar marked = markedLib.marked || markedLib.default || markedLib;\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nexport default {\n  name: 'WrongRecords',\n  mixins: [JeecgListMixin],\n  // 添加标准化列表状态管理\n  components: {\n    WrongQuestionPractice: WrongQuestionPractice\n  },\n  data: function data() {\n    return _defineProperty(_defineProperty({\n      description: '错题记录页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/examMistake/list',\n        delete: '/teaching/examSystem/examMistake/delete',\n        deleteBatch: '/teaching/examSystem/examMistake/deleteBatch'\n      },\n      // 错题练习模式控制\n      showPracticeMode: false,\n      practiceConfig: {},\n      // 查询参数\n      queryParam: {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined\n      },\n      // 表格列定义\n      columns: [{\n        title: '题目标题',\n        dataIndex: 'questionTitle',\n        width: '30%',\n        ellipsis: true\n      }, {\n        title: '科目/级别',\n        dataIndex: 'subject',\n        scopedSlots: {\n          customRender: 'subjectSlot'\n        }\n      }, {\n        title: '题型',\n        dataIndex: 'questionType',\n        scopedSlots: {\n          customRender: 'questionTypeSlot'\n        }\n      }, {\n        title: '错误次数',\n        dataIndex: 'mistakeCount',\n        sorter: true,\n        scopedSlots: {\n          customRender: 'mistakeCountSlot'\n        }\n      }, {\n        title: '最近错误时间',\n        dataIndex: 'lastMistakeTime',\n        sorter: true\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'actionSlot'\n        }\n      }],\n      // 注意：dataSource, ipagination, selectedRowKeys, loading 等状态\n      // 已由JeecgListMixin提供，无需重复定义\n      // 详情模态框\n      detailModalVisible: false,\n      currentQuestion: {},\n      currentRecord: {}\n    }, \"url\", {\n      list: '/teaching/examSystem/examMistake/list',\n      delete: '/teaching/examSystem/examMistake/delete',\n      deleteBatch: '/teaching/examSystem/examMistake/deleteBatch',\n      getQuestionDetail: '/teaching/examSystem/problemManage/queryById'\n    }), \"exportLoading\", false);\n  },\n  mounted: function mounted() {\n    this.loadData();\n  },\n  methods: {\n    // 获取级别选项\n    getLevelOptions: function getLevelOptions() {\n      var subject = this.queryParam.subject;\n      if (!subject) {\n        // 如果没有选择科目，返回所有级别\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }, {\n          value: '五级',\n          label: '五级'\n        }, {\n          value: '六级',\n          label: '六级'\n        }, {\n          value: '七级',\n          label: '七级'\n        }, {\n          value: '八级',\n          label: '八级'\n        }];\n      }\n      if (subject === 'Scratch') {\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }];\n      } else if (subject === 'Python' || subject === 'C++') {\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }, {\n          value: '五级',\n          label: '五级'\n        }, {\n          value: '六级',\n          label: '六级'\n        }, {\n          value: '七级',\n          label: '七级'\n        }, {\n          value: '八级',\n          label: '八级'\n        }];\n      }\n      return [];\n    },\n    // 加载数据\n    loadData: function loadData(arg) {\n      var _this = this;\n      if (arg === 1) {\n        this.ipagination.current = 1;\n      }\n      this.loading = true;\n      var params = _objectSpread({}, this.queryParam);\n      params.pageNo = this.ipagination.current;\n      params.pageSize = this.ipagination.pageSize;\n      getWrongRecordList(params).then(function (res) {\n        if (res.success) {\n          _this.dataSource = res.result.records || res.result;\n          _this.ipagination.total = res.result.total || 0;\n        } else {\n          _this.$message.error(res.message || '获取错题记录失败');\n        }\n        _this.loading = false;\n      }).catch(function (err) {\n        _this.loading = false;\n        _this.$message.error('获取错题记录失败：' + err.message);\n      });\n    },\n    // 表格变化处理\n    handleTableChange: function handleTableChange(pagination, filters, sorter) {\n      this.ipagination.current = pagination.current;\n\n      // 添加排序参数\n      if (sorter && sorter.field) {\n        this.queryParam.sortField = sorter.field;\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';\n      } else {\n        delete this.queryParam.sortField;\n        delete this.queryParam.sortOrder;\n      }\n      this.loadData();\n    },\n    // 选择行变化\n    onSelectChange: function onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys;\n    },\n    // 重置查询条件\n    resetQuery: function resetQuery() {\n      this.queryParam = {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined\n      };\n      this.loadData(1);\n    },\n    // 获取题型名称\n    getQuestionTypeName: function getQuestionTypeName(type) {\n      switch (type) {\n        case 1:\n          return '单选题';\n        case 2:\n          return '判断题';\n        case 3:\n          return '编程题';\n        default:\n          return '未知';\n      }\n    },\n    // 获取题型颜色\n    getQuestionTypeColor: function getQuestionTypeColor(type) {\n      switch (type) {\n        case 1:\n          return 'blue';\n        case 2:\n          return 'green';\n        case 3:\n          return 'orange';\n        default:\n          return 'default';\n      }\n    },\n    // 查看题目详情\n    viewQuestion: function viewQuestion(record) {\n      var _this2 = this;\n      this.currentRecord = record;\n\n      // 获取题目详情\n      queryProblemById(record.questionId).then(function (res) {\n        if (res.success) {\n          _this2.currentQuestion = res.result;\n\n          // 确保content字段是一个对象（如果是字符串，则解析）\n          if (typeof _this2.currentQuestion.content === 'string') {\n            try {\n              _this2.currentQuestion.content = JSON.parse(_this2.currentQuestion.content);\n            } catch (e) {\n              console.error('解析题目内容失败', e);\n              // 如果解析失败，提供一个空对象\n              _this2.currentQuestion.content = {};\n            }\n          } else if (!_this2.currentQuestion.content) {\n            // 如果content为null或undefined，提供一个空对象\n            _this2.currentQuestion.content = {};\n          }\n          _this2.detailModalVisible = true;\n        } else {\n          _this2.$message.error(res.message || '获取题目详情失败');\n        }\n      }).catch(function (err) {\n        console.error('获取题目详情异常', err);\n        _this2.$message.error('获取题目详情失败');\n      });\n    },\n    // 练习题目\n    practiceQuestion: function practiceQuestion(record) {\n      // 关闭模态框（如果打开）\n      this.detailModalVisible = false;\n\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'single',\n        questionId: record.questionId,\n        title: '单题练习'\n      };\n      this.showPracticeMode = true;\n    },\n    // 练习选中题目\n    practiceSelected: function practiceSelected() {\n      var _this3 = this;\n      if (!this.selectedRowKeys.length) {\n        this.$message.warning('请先选择需要练习的题目');\n        return;\n      }\n\n      // 获取所有选中行的题目ID\n      var selectedRecords = this.dataSource.filter(function (item) {\n        return _this3.selectedRowKeys.includes(item.id);\n      });\n      var questionIds = selectedRecords.map(function (record) {\n        return record.questionId;\n      });\n\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'selected',\n        questionIds: questionIds,\n        title: \"\\u7EC3\\u4E60\\u9009\\u4E2D\\u9898\\u76EE (\".concat(questionIds.length, \"\\u9898)\")\n      };\n      this.showPracticeMode = true;\n    },\n    // 练习所有错题\n    practiceAll: function practiceAll() {\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'wrongRecords',\n        queryParam: _objectSpread({}, this.queryParam),\n        title: '全部错题练习'\n      };\n      this.showPracticeMode = true;\n    },\n    // 返回错题记录列表\n    backToList: function backToList() {\n      this.showPracticeMode = false;\n      this.practiceConfig = {};\n      // 重新加载数据以更新错题状态\n      this.loadData();\n    },\n    // 删除单个记录\n    deleteRecord: function deleteRecord(id) {\n      var _this4 = this;\n      deleteAction(this.url.delete, {\n        id: id\n      }).then(function (res) {\n        if (res.success) {\n          _this4.$message.success('删除成功');\n          _this4.loadData();\n        } else {\n          _this4.$message.error(res.message || '删除失败');\n        }\n      });\n    },\n    // 批量删除\n    batchDelete: function batchDelete() {\n      var _this5 = this;\n      if (!this.selectedRowKeys.length) {\n        this.$message.warning('请选择需要删除的记录');\n        return;\n      }\n      this.$confirm({\n        title: '确认删除',\n        content: \"\\u786E\\u5B9A\\u5220\\u9664\\u9009\\u4E2D\\u7684 \".concat(this.selectedRowKeys.length, \" \\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\"),\n        onOk: function onOk() {\n          deleteAction(_this5.url.deleteBatch, {\n            ids: _this5.selectedRowKeys.join(',')\n          }).then(function (res) {\n            if (res.success) {\n              _this5.$message.success('批量删除成功');\n              _this5.selectedRowKeys = [];\n              _this5.loadData();\n            } else {\n              _this5.$message.error(res.message || '批量删除失败');\n            }\n          });\n        }\n      });\n    },\n    // Markdown转HTML\n    markdownToHtml: function markdownToHtml(text) {\n      if (!text) return '';\n      try {\n        return marked(text);\n      } catch (e) {\n        console.error('Markdown解析错误:', e);\n        return text; // 解析失败时返回原始文本\n      }\n    },\n    // 格式化编程题代码\n    formatProgrammingCode: function formatProgrammingCode(code) {\n      if (!code) return '';\n      try {\n        // 尝试解析JSON格式的代码\n        var codeObj = JSON.parse(code);\n        // 如果是带有code字段的对象，则返回code字段的值\n        if (codeObj && _typeof(codeObj) === 'object' && codeObj.code) {\n          return codeObj.code;\n        }\n        // 如果是其他格式的JSON，则返回其字符串形式\n        return JSON.stringify(codeObj, null, 2);\n      } catch (e) {\n        // 如果不是有效的JSON，则直接返回原始代码\n        return code;\n      }\n    },\n    // 获取题型对应的图标\n    getQuestionTypeIcon: function getQuestionTypeIcon(type) {\n      switch (type) {\n        case 1:\n          return 'check-circle';\n        case 2:\n          return 'question-circle';\n        case 3:\n          return 'code';\n        default:\n          return 'file-unknown';\n      }\n    },\n    // 获取题型对应的样式类名\n    getQuestionTypeClass: function getQuestionTypeClass(type) {\n      switch (type) {\n        case 1:\n          return 'single-choice';\n        case 2:\n          return 'true-false';\n        case 3:\n          return 'programming';\n        default:\n          return 'default';\n      }\n    },\n    // 添加导出错题方法\n    handleExport: function handleExport() {\n      var _this6 = this;\n      // 添加提示，告知用户导出内容\n      this.$confirm({\n        title: '导出错题',\n        content: '将导出符合当前查询条件的错题（仅包含题目内容，不含解析）。如需导出全部错题，请先清空查询条件。',\n        onOk: function onOk() {\n          // 构建导出参数\n          var params = _objectSpread({}, _this6.queryParam);\n\n          // 如果有选中行，则只导出选中的错题\n          if (_this6.selectedRowKeys && _this6.selectedRowKeys.length > 0) {\n            params.ids = _this6.selectedRowKeys.join(',');\n          }\n          _this6.exportLoading = true;\n          exportWrongRecords(params).then(function (res) {\n            if (res.type === 'application/json') {\n              // 处理错误响应\n              var reader = new FileReader();\n              reader.onload = function (e) {\n                try {\n                  var result = JSON.parse(e.target.result);\n                  _this6.$message.error(result.message || '导出失败');\n                } catch (e) {\n                  _this6.$message.error('导出失败：服务器返回格式错误');\n                }\n                _this6.exportLoading = false;\n              };\n              reader.readAsText(res);\n            } else {\n              // 处理成功响应\n              // 创建Blob对象\n              var blob = new Blob([res], {\n                type: 'text/plain;charset=utf-8'\n              });\n\n              // 创建文件名\n              var fileName = \"\\u9519\\u9898\\u5BFC\\u51FA_\".concat(new Date().getTime(), \".txt\");\n\n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName);\n              } else {\n                // 现代浏览器\n                var link = document.createElement('a');\n                link.href = URL.createObjectURL(blob);\n                link.download = fileName;\n                link.style.display = 'none';\n                document.body.appendChild(link);\n                link.click();\n\n                // 清理创建的对象URL\n                setTimeout(function () {\n                  URL.revokeObjectURL(link.href);\n                  document.body.removeChild(link);\n                }, 100);\n              }\n              _this6.$message.success('导出成功');\n            }\n            _this6.exportLoading = false;\n          }).catch(function (error) {\n            console.error('导出失败:', error);\n            _this6.$message.error('导出过程发生错误');\n            _this6.exportLoading = false;\n          });\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["getAction", "deleteAction", "markedLib", "getWrongRecordList", "deleteWrongRecord", "deleteBatchWrongRecord", "queryProblemById", "exportWrongRecords", "moment", "WrongQuestionPractice", "marked", "default", "JeecgListMixin", "name", "mixins", "components", "data", "_defineProperty", "description", "url", "list", "delete", "deleteBatch", "showPracticeMode", "practiceConfig", "queryParam", "subject", "undefined", "level", "questionType", "columns", "title", "dataIndex", "width", "ellipsis", "scopedSlots", "customRender", "sorter", "detailModalVisible", "currentQuestion", "currentRecord", "getQuestionDetail", "mounted", "loadData", "methods", "getLevelOptions", "value", "label", "arg", "_this", "ipagination", "current", "loading", "params", "_objectSpread", "pageNo", "pageSize", "then", "res", "success", "dataSource", "result", "records", "total", "$message", "error", "message", "catch", "err", "handleTableChange", "pagination", "filters", "field", "sortField", "sortOrder", "order", "onSelectChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset<PERSON><PERSON>y", "getQuestionTypeName", "type", "getQuestionTypeColor", "viewQuestion", "record", "_this2", "questionId", "content", "JSON", "parse", "e", "console", "practiceQuestion", "mode", "practiceSelected", "_this3", "length", "warning", "selected<PERSON><PERSON><PERSON><PERSON>", "filter", "item", "includes", "id", "questionIds", "map", "concat", "practiceAll", "backToList", "deleteRecord", "_this4", "batchDelete", "_this5", "$confirm", "onOk", "ids", "join", "markdownToHtml", "text", "formatProgrammingCode", "code", "codeObj", "_typeof", "stringify", "getQuestionTypeIcon", "getQuestionTypeClass", "handleExport", "_this6", "exportLoading", "reader", "FileReader", "onload", "target", "readAsText", "blob", "Blob", "fileName", "Date", "getTime", "window", "navigator", "msSaveOrOpenBlob", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "setTimeout", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/examSystem/wrongRecords.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 错题记录列表页面 -->\n    <a-card v-if=\"!showPracticeMode\" :bordered=\"false\">\n      <div class=\"table-page-search-wrapper\">\n        <a-form layout=\"inline\">\n          <a-row :gutter=\"24\">\n            <a-col :md=\"6\" :sm=\"24\">\n              <a-form-item label=\"科目\">\n                <a-select v-model=\"queryParam.subject\" placeholder=\"请选择科目\" allowClear>\n                  <a-select-option value=\"Scratch\">Scratch</a-select-option>\n                  <a-select-option value=\"Python\">Python</a-select-option>\n                  <a-select-option value=\"C++\">C++</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"6\" :sm=\"24\">\n              <a-form-item label=\"级别\">\n                <a-select v-model=\"queryParam.level\" placeholder=\"请选择级别\" allowClear>\n                  <a-select-option v-for=\"option in getLevelOptions()\" :key=\"option.value\" :value=\"option.value\">{{ option.label }}</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"6\" :sm=\"24\">\n              <a-form-item label=\"题型\">\n                <a-select v-model=\"queryParam.questionType\" placeholder=\"请选择题型\" allowClear>\n                  <a-select-option :value=\"1\">单选题</a-select-option>\n                  <a-select-option :value=\"2\">判断题</a-select-option>\n                  <a-select-option :value=\"3\">编程题</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"6\" :sm=\"24\">\n              <span class=\"table-page-search-submitButtons\">\n                <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\n                <a-button style=\"margin-left: 8px\" @click=\"resetQuery\">重置</a-button>\n              </span>\n            </a-col>\n          </a-row>\n        </a-form>\n      </div>\n\n      <div class=\"table-operator\">\n        <a-button type=\"primary\" @click=\"practiceSelected\" :disabled=\"!selectedRowKeys.length\">练习选中题目</a-button>\n        <a-button style=\"margin-left: 8px\" @click=\"practiceAll\">练习所有错题</a-button>\n        <!-- 添加导出错题按钮 -->\n        <a-button type=\"primary\" icon=\"cloud-download\" @click=\"handleExport\">导出错题</a-button>\n        <!-- 修改批量删除按钮，参考collection.vue的样式 -->\n        <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n          <a-menu slot=\"overlay\">\n            <a-menu-item key=\"1\" @click=\"batchDelete\">\n              <a-icon type=\"delete\" />批量删除\n            </a-menu-item>\n          </a-menu>\n          <a-button class=\"batch-operation-btn\">\n            批量操作 <a-icon type=\"down\" />\n          </a-button>\n        </a-dropdown>\n      </div>\n\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      @change=\"handleTableChange\">\n\n      <template slot=\"questionTypeSlot\" slot-scope=\"text\">\n        <a-tag :color=\"getQuestionTypeColor(text)\">{{ getQuestionTypeName(text) }}</a-tag>\n      </template>\n      \n      <template slot=\"subjectSlot\" slot-scope=\"text, record\">\n        <span>{{ text }} {{ record.level }}</span>\n      </template>\n\n      <template slot=\"mistakeCountSlot\" slot-scope=\"text\">\n        <a-badge :count=\"text\" :numberStyle=\"{backgroundColor: text > 5 ? '#f5222d' : '#52c41a'}\" />\n      </template>\n\n      <template slot=\"actionSlot\" slot-scope=\"text, record\">\n        <a @click=\"viewQuestion(record)\">查看</a>\n        <a-divider type=\"vertical\" />\n        <a @click=\"practiceQuestion(record)\">练习</a>\n        <a-divider type=\"vertical\" />\n        <a-popconfirm title=\"确定删除此错题记录吗?\" @confirm=\"() => deleteRecord(record.id)\">\n          <a>删除</a>\n        </a-popconfirm>\n      </template>\n    </a-table>\n\n    <!-- 查看错题详情的模态框 -->\n    <a-modal\n      :width=\"800\"\n      :visible=\"detailModalVisible\"\n      :footer=\"null\"\n      :bodyStyle=\"{ padding: '0' }\"\n      :maskStyle=\"{ background: 'rgba(0,0,0,0.65)' }\"\n      @cancel=\"detailModalVisible = false\">\n      \n      <!-- 模态框头部彩带 -->\n      <div :class=\"['modal-header-ribbon', getQuestionTypeClass(currentQuestion.questionType)]\">\n        <div class=\"question-type-icon\">\n          <a-icon :type=\"getQuestionTypeIcon(currentQuestion.questionType)\" />\n        </div>\n        <div class=\"question-type-text\">{{ getQuestionTypeName(currentQuestion.questionType) }}</div>\n      </div>\n      \n      <!-- 客观题 -->\n      <div class=\"modal-content-wrapper\">\n        <template v-if=\"currentQuestion.questionType !== 3\">\n          <div class=\"question-title\">\n            <h3>{{ currentQuestion.title }}</h3>\n          </div>\n          <div class=\"question-content\">\n            <!-- 单选题选项 -->\n            <div v-if=\"currentQuestion.questionType === 1\" class=\"option-list\">\n              <div v-for=\"(option, index) in currentQuestion.content.options\" \n                  :key=\"index\" \n                  :class=\"['option-item', { \n                    'correct-option': currentQuestion.content.answer === String.fromCharCode(65 + index),\n                    'wrong-option': currentRecord.lastAnswer === String.fromCharCode(65 + index) && currentRecord.lastAnswer !== currentQuestion.content.answer\n                  }]\">\n                <span class=\"option-letter\">{{ String.fromCharCode(65 + index) }}</span>\n                <span class=\"option-text\">{{ option }}</span>\n                <a-icon v-if=\"currentQuestion.content.answer === String.fromCharCode(65 + index)\" \n                       type=\"check-circle\" theme=\"filled\" class=\"correct-icon\" />\n                <a-icon v-if=\"currentRecord.lastAnswer === String.fromCharCode(65 + index) && currentRecord.lastAnswer !== currentQuestion.content.answer\" \n                       type=\"close-circle\" theme=\"filled\" class=\"wrong-icon\" />\n              </div>\n            </div>\n            \n            <!-- 判断题选项 -->\n            <div v-else-if=\"currentQuestion.questionType === 2\" class=\"option-list\">\n              <div :class=\"['option-item', { \n                  'correct-option': currentQuestion.content.answer === 'T',\n                  'wrong-option': (currentRecord.lastAnswer === 'true' || currentRecord.lastAnswer === 'T') && currentQuestion.content.answer !== 'T'\n                }]\">\n                <span class=\"option-text\">正确</span>\n                <a-icon v-if=\"currentQuestion.content.answer === 'T'\" \n                       type=\"check-circle\" theme=\"filled\" class=\"correct-icon\" />\n                <a-icon v-if=\"(currentRecord.lastAnswer === 'true' || currentRecord.lastAnswer === 'T') && currentQuestion.content.answer !== 'T'\" \n                       type=\"close-circle\" theme=\"filled\" class=\"wrong-icon\" />\n              </div>\n              <div :class=\"['option-item', { \n                  'correct-option': currentQuestion.content.answer === 'F',\n                  'wrong-option': (currentRecord.lastAnswer === 'false' || currentRecord.lastAnswer === 'F') && currentQuestion.content.answer !== 'F'\n                }]\">\n                <span class=\"option-text\">错误</span>\n                <a-icon v-if=\"currentQuestion.content.answer === 'F'\" \n                       type=\"check-circle\" theme=\"filled\" class=\"correct-icon\" />\n                <a-icon v-if=\"(currentRecord.lastAnswer === 'false' || currentRecord.lastAnswer === 'F') && currentQuestion.content.answer !== 'F'\" \n                       type=\"close-circle\" theme=\"filled\" class=\"wrong-icon\" />\n              </div>\n            </div>\n          </div>\n          \n          <a-divider class=\"styled-divider\" />\n          \n          <div class=\"wrong-answer-section-simple\">\n            <div class=\"section-title-simple\">\n              <a-icon type=\"warning\" theme=\"filled\" style=\"color: #ff4d4f\" />\n              <span>您的错误答案</span>\n            </div>\n            <div class=\"wrong-answer-content-simple\">{{ currentRecord.lastAnswer }}</div>\n          </div>\n          \n          <div class=\"analysis-section-simple\" v-if=\"currentQuestion.content && currentQuestion.content.analysis\">\n            <div class=\"section-title-simple\">\n              <a-icon type=\"bulb\" theme=\"filled\" style=\"color: #52c41a\" />\n              <span>解析</span>\n            </div>\n            <div class=\"analysis-content-simple\">{{ currentQuestion.content.analysis }}</div>\n          </div>\n          \n          <div class=\"analysis-section-simple\" v-else>\n            <div class=\"section-title-simple\">\n              <a-icon type=\"bulb\" theme=\"filled\" style=\"color: #52c41a\" />\n              <span>解析</span>\n            </div>\n            <div class=\"analysis-content-simple no-analysis\">暂无解析</div>\n          </div>\n        </template>\n        \n        <!-- 编程题 -->\n        <template v-else>\n          <div class=\"question-title\">\n            <h3>{{ currentQuestion.title }}</h3>\n          </div>\n          \n          <a-tabs default-active-key=\"1\" class=\"styled-tabs\">\n            <a-tab-pane key=\"1\" tab=\"题目描述\">\n              <div class=\"problem-description\" v-if=\"currentQuestion.content && currentQuestion.content.description\" v-html=\"markdownToHtml(currentQuestion.content.description)\"></div>\n              <div class=\"problem-description\" v-else>暂无题目描述</div>\n              \n              <a-divider class=\"styled-divider\" />\n              \n              <div class=\"format-section\">\n                <div class=\"section-title\">\n                  <a-icon type=\"arrow-down\" />\n                  <span>输入格式</span>\n                </div>\n                <div v-if=\"currentQuestion.content && currentQuestion.content.input_format\" v-html=\"markdownToHtml(currentQuestion.content.input_format)\"></div>\n                <div v-else>暂无输入格式</div>\n              </div>\n              \n              <a-divider class=\"styled-divider\" />\n              \n              <div class=\"format-section\">\n                <div class=\"section-title\">\n                  <a-icon type=\"arrow-up\" />\n                  <span>输出格式</span>\n                </div>\n                <div v-if=\"currentQuestion.content && currentQuestion.content.output_format\" v-html=\"markdownToHtml(currentQuestion.content.output_format)\"></div>\n                <div v-else>暂无输出格式</div>\n              </div>\n              \n              <a-divider class=\"styled-divider\" />\n              \n              <div class=\"samples-section\" v-if=\"currentQuestion.content && currentQuestion.content.sample_cases && currentQuestion.content.sample_cases.length > 0\">\n                <div v-for=\"(sample, index) in currentQuestion.content.sample_cases\" :key=\"index\" class=\"sample-case\">\n                  <div class=\"section-title\">\n                    <a-icon type=\"experiment\" />\n                    <span>样例 {{ index + 1 }}</span>\n                  </div>\n                  <a-row :gutter=\"16\">\n                    <a-col :span=\"12\">\n                      <div class=\"sample-label\">输入：</div>\n                      <pre class=\"sample-content\">{{ sample.input }}</pre>\n                    </a-col>\n                    <a-col :span=\"12\">\n                      <div class=\"sample-label\">输出：</div>\n                      <pre class=\"sample-content\">{{ sample.output }}</pre>\n                    </a-col>\n                  </a-row>\n                </div>\n              </div>\n              <div class=\"samples-section\" v-else>\n                <div class=\"section-title\">\n                  <a-icon type=\"experiment\" />\n                  <span>样例</span>\n                </div>\n                <div>暂无样例</div>\n              </div>\n              \n              <a-divider v-if=\"currentQuestion.content && currentQuestion.content.hint\" class=\"styled-divider\" />\n              \n              <div class=\"hint-section\" v-if=\"currentQuestion.content && currentQuestion.content.hint\">\n                <div class=\"section-title\">\n                  <a-icon type=\"notification\" />\n                  <span>提示</span>\n                </div>\n                <div v-html=\"markdownToHtml(currentQuestion.content.hint)\"></div>\n              </div>\n\n              <a-divider v-if=\"currentQuestion.content && currentQuestion.content.analysis\" class=\"styled-divider\" />\n\n              <div class=\"analysis-section\" v-if=\"currentQuestion.content && currentQuestion.content.analysis\">\n                <div class=\"section-title\">\n                  <a-icon type=\"bulb\" theme=\"filled\" style=\"color: #52c41a\" />\n                  <span>解析</span>\n                </div>\n                <div v-html=\"markdownToHtml(currentQuestion.content.analysis)\" class=\"analysis-content\"></div>\n              </div>\n            </a-tab-pane>\n            <a-tab-pane key=\"2\" tab=\"您的代码\">\n              <a-alert \n                message=\"这是您最近一次错误的代码，可能包含错误或者未通过的测试用例\" \n                type=\"warning\" \n                class=\"code-alert\"\n              />\n              <pre class=\"code-display\">{{ formatProgrammingCode(currentRecord.lastAnswer) }}</pre>\n            </a-tab-pane>\n          </a-tabs>\n        </template>\n      </div>\n      \n      <div class=\"modal-footer\">\n        <a-button type=\"primary\" @click=\"practiceQuestion(currentRecord)\" class=\"practice-button\">\n          <a-icon type=\"code\" />练习此题\n        </a-button>\n        <a-button @click=\"detailModalVisible = false\" class=\"close-button\">关闭</a-button>\n      </div>\n    </a-modal>\n    </a-card>\n\n    <!-- 错题练习组件 -->\n    <wrong-question-practice\n      v-if=\"showPracticeMode\"\n      :practiceConfig=\"practiceConfig\"\n      @back-to-list=\"backToList\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getAction, deleteAction } from '@/api/manage'\nimport * as markedLib from 'marked'\nimport { getWrongRecordList, deleteWrongRecord, deleteBatchWrongRecord, queryProblemById, exportWrongRecords } from '@/api/examSystem'\nimport moment from 'moment'\nimport WrongQuestionPractice from './components/WrongQuestionPractice.vue'\n\n// 确保正确获取marked函数\nconst marked = markedLib.marked || markedLib.default || markedLib\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\n\nexport default {\n  name: 'WrongRecords',\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\n  components: {\n    WrongQuestionPractice\n  },\n  data() {\n    return {\n      description: '错题记录页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/examMistake/list',\n        delete: '/teaching/examSystem/examMistake/delete',\n        deleteBatch: '/teaching/examSystem/examMistake/deleteBatch'\n      },\n      // 错题练习模式控制\n      showPracticeMode: false,\n      practiceConfig: {},\n\n      // 查询参数\n      queryParam: {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined\n      },\n      // 表格列定义\n      columns: [\n        {\n          title: '题目标题',\n          dataIndex: 'questionTitle',\n          width: '30%',\n          ellipsis: true\n        },\n        {\n          title: '科目/级别',\n          dataIndex: 'subject',\n          scopedSlots: { customRender: 'subjectSlot' }\n        },\n        {\n          title: '题型',\n          dataIndex: 'questionType',\n          scopedSlots: { customRender: 'questionTypeSlot' }\n        },\n        {\n          title: '错误次数',\n          dataIndex: 'mistakeCount',\n          sorter: true,\n          scopedSlots: { customRender: 'mistakeCountSlot' }\n        },\n        {\n          title: '最近错误时间',\n          dataIndex: 'lastMistakeTime',\n          sorter: true\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          scopedSlots: { customRender: 'actionSlot' }\n        }\n      ],\n      // 注意：dataSource, ipagination, selectedRowKeys, loading 等状态\n      // 已由JeecgListMixin提供，无需重复定义\n      // 详情模态框\n      detailModalVisible: false,\n      currentQuestion: {},\n      currentRecord: {},\n      // API URL - 更新为新的路径\n      url: {\n        list: '/teaching/examSystem/examMistake/list',\n        delete: '/teaching/examSystem/examMistake/delete',\n        deleteBatch: '/teaching/examSystem/examMistake/deleteBatch',\n        getQuestionDetail: '/teaching/examSystem/problemManage/queryById'\n      },\n      // 添加导出加载状态\n      exportLoading: false,\n    }\n  },\n  mounted() {\n    this.loadData()\n  },\n  methods: {\n    // 获取级别选项\n    getLevelOptions() {\n      const subject = this.queryParam.subject\n      if (!subject) {\n        // 如果没有选择科目，返回所有级别\n        return [\n          { value: '一级', label: '一级' },\n          { value: '二级', label: '二级' },\n          { value: '三级', label: '三级' },\n          { value: '四级', label: '四级' },\n          { value: '五级', label: '五级' },\n          { value: '六级', label: '六级' },\n          { value: '七级', label: '七级' },\n          { value: '八级', label: '八级' }\n        ]\n      }\n\n      if (subject === 'Scratch') {\n        return [\n          { value: '一级', label: '一级' },\n          { value: '二级', label: '二级' },\n          { value: '三级', label: '三级' },\n          { value: '四级', label: '四级' }\n        ]\n      } else if (subject === 'Python' || subject === 'C++') {\n        return [\n          { value: '一级', label: '一级' },\n          { value: '二级', label: '二级' },\n          { value: '三级', label: '三级' },\n          { value: '四级', label: '四级' },\n          { value: '五级', label: '五级' },\n          { value: '六级', label: '六级' },\n          { value: '七级', label: '七级' },\n          { value: '八级', label: '八级' }\n        ]\n      }\n\n      return []\n    },\n\n    // 加载数据\n    loadData(arg) {\n      if (arg === 1) {\n        this.ipagination.current = 1\n      }\n      this.loading = true\n      const params = {...this.queryParam}\n      params.pageNo = this.ipagination.current\n      params.pageSize = this.ipagination.pageSize\n      \n      getWrongRecordList(params).then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records || res.result\n          this.ipagination.total = res.result.total || 0\n        } else {\n          this.$message.error(res.message || '获取错题记录失败')\n        }\n        this.loading = false\n      }).catch((err) => {\n        this.loading = false\n        this.$message.error('获取错题记录失败：' + err.message)\n      })\n    },\n    \n    // 表格变化处理\n    handleTableChange(pagination, filters, sorter) {\n      this.ipagination.current = pagination.current\n      \n      // 添加排序参数\n      if (sorter && sorter.field) {\n        this.queryParam.sortField = sorter.field\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\n      } else {\n        delete this.queryParam.sortField\n        delete this.queryParam.sortOrder\n      }\n      \n      this.loadData()\n    },\n    \n    // 选择行变化\n    onSelectChange(selectedRowKeys) {\n      this.selectedRowKeys = selectedRowKeys\n    },\n    \n    // 重置查询条件\n    resetQuery() {\n      this.queryParam = {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined\n      }\n      this.loadData(1)\n    },\n    \n    // 获取题型名称\n    getQuestionTypeName(type) {\n      switch (type) {\n        case 1: return '单选题'\n        case 2: return '判断题'\n        case 3: return '编程题'\n        default: return '未知'\n      }\n    },\n    \n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      switch (type) {\n        case 1: return 'blue'\n        case 2: return 'green'\n        case 3: return 'orange'\n        default: return 'default'\n      }\n    },\n    \n    // 查看题目详情\n    viewQuestion(record) {\n      this.currentRecord = record\n      \n      // 获取题目详情\n      queryProblemById(record.questionId).then((res) => {\n        if (res.success) {\n          this.currentQuestion = res.result\n          \n          // 确保content字段是一个对象（如果是字符串，则解析）\n          if (typeof this.currentQuestion.content === 'string') {\n            try {\n              this.currentQuestion.content = JSON.parse(this.currentQuestion.content)\n            } catch (e) {\n              console.error('解析题目内容失败', e)\n              // 如果解析失败，提供一个空对象\n              this.currentQuestion.content = {}\n            }\n          } else if (!this.currentQuestion.content) {\n            // 如果content为null或undefined，提供一个空对象\n            this.currentQuestion.content = {}\n          }\n          \n          this.detailModalVisible = true\n        } else {\n          this.$message.error(res.message || '获取题目详情失败')\n        }\n      }).catch(err => {\n        console.error('获取题目详情异常', err)\n        this.$message.error('获取题目详情失败')\n      })\n    },\n    \n    // 练习题目\n    practiceQuestion(record) {\n      // 关闭模态框（如果打开）\n      this.detailModalVisible = false\n\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'single',\n        questionId: record.questionId,\n        title: '单题练习'\n      }\n      this.showPracticeMode = true\n    },\n\n    // 练习选中题目\n    practiceSelected() {\n      if (!this.selectedRowKeys.length) {\n        this.$message.warning('请先选择需要练习的题目')\n        return\n      }\n\n      // 获取所有选中行的题目ID\n      const selectedRecords = this.dataSource.filter(item => this.selectedRowKeys.includes(item.id))\n      const questionIds = selectedRecords.map(record => record.questionId)\n\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'selected',\n        questionIds: questionIds,\n        title: `练习选中题目 (${questionIds.length}题)`\n      }\n      this.showPracticeMode = true\n    },\n\n    // 练习所有错题\n    practiceAll() {\n      // 设置练习配置并切换到练习模式\n      this.practiceConfig = {\n        mode: 'wrongRecords',\n        queryParam: { ...this.queryParam },\n        title: '全部错题练习'\n      }\n      this.showPracticeMode = true\n    },\n\n    // 返回错题记录列表\n    backToList() {\n      this.showPracticeMode = false\n      this.practiceConfig = {}\n      // 重新加载数据以更新错题状态\n      this.loadData()\n    },\n    \n    // 删除单个记录\n    deleteRecord(id) {\n      deleteAction(this.url.delete, { id: id }).then((res) => {\n        if (res.success) {\n          this.$message.success('删除成功')\n          this.loadData()\n        } else {\n          this.$message.error(res.message || '删除失败')\n        }\n      })\n    },\n    \n    // 批量删除\n    batchDelete() {\n      if (!this.selectedRowKeys.length) {\n        this.$message.warning('请选择需要删除的记录')\n        return\n      }\n      \n      this.$confirm({\n        title: '确认删除',\n        content: `确定删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,\n        onOk: () => {\n          deleteAction(this.url.deleteBatch, { ids: this.selectedRowKeys.join(',') }).then((res) => {\n            if (res.success) {\n              this.$message.success('批量删除成功')\n              this.selectedRowKeys = []\n              this.loadData()\n            } else {\n              this.$message.error(res.message || '批量删除失败')\n            }\n          })\n        }\n      })\n    },\n    \n    // Markdown转HTML\n    markdownToHtml(text) {\n      if (!text) return ''\n      try {\n        return marked(text)\n      } catch (e) {\n        console.error('Markdown解析错误:', e)\n        return text // 解析失败时返回原始文本\n      }\n    },\n    \n    // 格式化编程题代码\n    formatProgrammingCode(code) {\n      if (!code) return ''\n      try {\n        // 尝试解析JSON格式的代码\n        const codeObj = JSON.parse(code)\n        // 如果是带有code字段的对象，则返回code字段的值\n        if (codeObj && typeof codeObj === 'object' && codeObj.code) {\n          return codeObj.code\n        }\n        // 如果是其他格式的JSON，则返回其字符串形式\n        return JSON.stringify(codeObj, null, 2)\n      } catch (e) {\n        // 如果不是有效的JSON，则直接返回原始代码\n        return code\n      }\n    },\n\n    // 获取题型对应的图标\n    getQuestionTypeIcon(type) {\n      switch (type) {\n        case 1: return 'check-circle'\n        case 2: return 'question-circle'\n        case 3: return 'code'\n        default: return 'file-unknown'\n      }\n    },\n    \n    // 获取题型对应的样式类名\n    getQuestionTypeClass(type) {\n      switch (type) {\n        case 1: return 'single-choice'\n        case 2: return 'true-false'\n        case 3: return 'programming'\n        default: return 'default'\n      }\n    },\n\n    // 添加导出错题方法\n    handleExport() {\n      // 添加提示，告知用户导出内容\n      this.$confirm({\n        title: '导出错题',\n        content: '将导出符合当前查询条件的错题（仅包含题目内容，不含解析）。如需导出全部错题，请先清空查询条件。',\n        onOk: () => {\n          // 构建导出参数\n          const params = {...this.queryParam}\n          \n          // 如果有选中行，则只导出选中的错题\n          if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {\n            params.ids = this.selectedRowKeys.join(',')\n          }\n          \n          this.exportLoading = true\n          \n          exportWrongRecords(params).then((res) => {\n            if (res.type === 'application/json') {\n              // 处理错误响应\n              const reader = new FileReader()\n              reader.onload = (e) => {\n                try {\n                  const result = JSON.parse(e.target.result)\n                  this.$message.error(result.message || '导出失败')\n                } catch (e) {\n                  this.$message.error('导出失败：服务器返回格式错误')\n                }\n                this.exportLoading = false\n              }\n              reader.readAsText(res)\n            } else {\n              // 处理成功响应\n              // 创建Blob对象\n              const blob = new Blob([res], { type: 'text/plain;charset=utf-8' })\n              \n              // 创建文件名\n              const fileName = `错题导出_${new Date().getTime()}.txt`\n              \n              // 创建下载链接\n              if (window.navigator.msSaveOrOpenBlob) {\n                // IE11和Edge的兼容处理\n                window.navigator.msSaveOrOpenBlob(blob, fileName)\n              } else {\n                // 现代浏览器\n                const link = document.createElement('a')\n                link.href = URL.createObjectURL(blob)\n                link.download = fileName\n                link.style.display = 'none'\n                document.body.appendChild(link)\n                link.click()\n                \n                // 清理创建的对象URL\n                setTimeout(() => {\n                  URL.revokeObjectURL(link.href)\n                  document.body.removeChild(link)\n                }, 100)\n              }\n              \n              this.$message.success('导出成功')\n            }\n            \n            this.exportLoading = false\n          }).catch((error) => {\n            console.error('导出失败:', error)\n            this.$message.error('导出过程发生错误')\n            this.exportLoading = false\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.question-content {\n  margin: 16px 0;\n}\n\n.correct-option {\n  font-weight: bold;\n  color: #52c41a;\n}\n\npre {\n  background-color: #f5f5f5;\n  padding: 10px;\n  border-radius: 5px;\n  overflow-x: auto;\n}\n\n.wrong-answer {\n  margin-bottom: 10px;\n  color: #f5222d;\n}\n\n.no-analysis {\n  color: #999;\n  font-style: italic;\n}\n\n/* 新增样式 */\n.modal-header-ribbon {\n  padding: 12px 24px;\n  display: flex;\n  align-items: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.single-choice {\n  background: linear-gradient(135deg, #1890ff, #096dd9);\n}\n\n.true-false {\n  background: linear-gradient(135deg, #52c41a, #389e0d);\n}\n\n.programming {\n  background: linear-gradient(135deg, #fa8c16, #d46b08);\n}\n\n.default {\n  background: linear-gradient(135deg, #722ed1, #531dab);\n}\n\n.question-type-icon {\n  margin-right: 10px;\n  font-size: 24px;\n}\n\n.modal-content-wrapper {\n  padding: 20px 24px;\n}\n\n.question-title {\n  margin-bottom: 20px;\n  border-left: 4px solid #1890ff;\n  padding-left: 12px;\n}\n\n.question-title h3 {\n  margin: 0;\n  font-size: 18px;\n  color: #333;\n}\n\n.option-list {\n  margin-bottom: 20px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  margin-bottom: 10px;\n  border-radius: 6px;\n  background-color: #f5f5f5;\n  transition: all 0.3s;\n}\n\n.option-item:hover {\n  background-color: #e6f7ff;\n}\n\n.option-letter {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  background-color: #1890ff;\n  color: white;\n  font-weight: bold;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.option-symbol {\n  font-weight: bold;\n  margin-right: 12px;\n  font-size: 16px;\n}\n\n.correct-option .option-symbol {\n  color: #52c41a;\n}\n\n.wrong-option .option-symbol {\n  color: #ff4d4f;\n}\n\n.option-text {\n  flex: 1;\n}\n\n.correct-icon {\n  color: #52c41a;\n  font-size: 18px;\n  margin-left: 10px;\n}\n\n.wrong-icon {\n  color: #f5222d;\n  font-size: 18px;\n  margin-left: 10px;\n}\n\n.correct-option {\n  background-color: #f6ffed;\n  border: 1px solid #b7eb8f;\n}\n\n.correct-option .option-label {\n  background-color: #52c41a;\n}\n\n.wrong-option {\n  background-color: #fff2f0;\n  border: 1px solid #ffccc7;\n}\n\n.wrong-option .option-label {\n  background-color: #ff4d4f;\n}\n\n.styled-divider {\n  margin: 16px 0;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n  font-weight: bold;\n  color: #333;\n}\n\n.section-title .anticon {\n  margin-right: 8px;\n  color: #1890ff;\n}\n\n.wrong-answer-section {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #fff2f0;\n  border-radius: 6px;\n  border-left: 4px solid #ff4d4f;\n}\n\n.wrong-answer-section .section-title .anticon {\n  color: #ff4d4f;\n}\n\n.wrong-answer-content {\n  color: #ff4d4f;\n  font-weight: bold;\n}\n\n.analysis-section {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #f6ffed;\n  border-radius: 6px;\n  border-left: 4px solid #52c41a;\n}\n\n.analysis-section .section-title .anticon {\n  color: #52c41a;\n}\n\n.analysis-content {\n  line-height: 1.6;\n}\n\n.no-analysis {\n  color: #999;\n  font-style: italic;\n}\n\n.styled-tabs {\n  margin-top: 16px;\n}\n\n.format-section {\n  margin-bottom: 20px;\n}\n\n.samples-section {\n  margin-bottom: 20px;\n}\n\n.sample-case {\n  margin-bottom: 16px;\n  padding: 16px;\n  background-color: #f9f9f9;\n  border-radius: 6px;\n}\n\n.sample-label {\n  font-weight: bold;\n  margin-bottom: 8px;\n  color: #1890ff;\n}\n\n.sample-content {\n  background-color: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n}\n\n.hint-section {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #e6f7ff;\n  border-radius: 6px;\n  border-left: 4px solid #1890ff;\n}\n\n.code-alert {\n  margin-bottom: 16px;\n}\n\n.code-display {\n  background-color: #282c34;\n  color: #abb2bf;\n  padding: 16px;\n  border-radius: 6px;\n  max-height: 400px;\n  overflow-y: auto;\n  font-family: \"Fira Code\", \"Consolas\", monospace;\n}\n\n.modal-footer {\n  padding: 12px 24px;\n  text-align: right;\n  background-color: #f5f5f5;\n  border-top: 1px solid #e8e8e8;\n}\n\n.practice-button {\n  background: linear-gradient(135deg, #1890ff, #096dd9);\n  border: none;\n  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);\n  transition: all 0.3s;\n}\n\n.practice-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);\n}\n\n.close-button {\n  margin-left: 8px;\n}\n\n.wrong-answer-section-simple {\n  margin-bottom: 16px;\n  border-bottom: 1px dashed #f0f0f0;\n  padding-bottom: 16px;\n}\n\n.analysis-section-simple {\n  margin-bottom: 16px;\n}\n\n.section-title-simple {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #333;\n}\n\n.section-title-simple .anticon {\n  margin-right: 8px;\n}\n\n.wrong-answer-content-simple {\n  color: #ff4d4f;\n  padding-left: 24px;\n  font-weight: 500;\n}\n\n.analysis-content-simple {\n  line-height: 1.6;\n  padding-left: 24px;\n  color: #555;\n}\n\n.no-analysis {\n  color: #999;\n  font-style: italic;\n}\n\n/* 批量操作按钮样式 */\n.batch-operation-btn {\n  margin-left: 8px;\n  background-color: #ff4d4f;\n  border-color: #ff4d4f;\n  color: #fff;\n  font-weight: 500;\n}\n.batch-operation-btn:hover, \n.batch-operation-btn:focus {\n  background-color: #ff7875;\n  border-color: #ff7875;\n  color: #fff;\n}\n\n/* 操作按钮区域样式 */\n.table-operator {\n  margin-bottom: 18px;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.table-operator button {\n  margin-right: 12px;\n  margin-bottom: 8px;\n}\n</style> "], "mappings": ";;;;;;AA4SA,SAAAA,SAAA,EAAAC,YAAA;AACA,YAAAC,SAAA;AACA,SAAAC,kBAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,kBAAA;AACA,OAAAC,MAAA;AACA,OAAAC,qBAAA;;AAEA;AACA,IAAAC,MAAA,GAAAR,SAAA,CAAAQ,MAAA,IAAAR,SAAA,CAAAS,OAAA,IAAAT,SAAA;AACA,SAAAU,cAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAF,cAAA;EAAA;EACAG,UAAA;IACAN,qBAAA,EAAAA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA,OAAAC,eAAA,CAAAA,eAAA;MACAC,WAAA;MACA;MACAC,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,WAAA;MACA;MACA;MACAC,gBAAA;MACAC,cAAA;MAEA;MACAC,UAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,YAAA,EAAAF;MACA;MACA;MACAG,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAG,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAG,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAK,MAAA;QACAF,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAK,MAAA;MACA,GACA;QACAN,KAAA;QACAC,SAAA;QACAG,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MACA;MACA;MACA;MACAE,kBAAA;MACAC,eAAA;MACAC,aAAA;IAAA,UAEA;MACApB,IAAA;MACAC,MAAA;MACAC,WAAA;MACAmB,iBAAA;IACA,qBAEA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,IAAAnB,OAAA,QAAAD,UAAA,CAAAC,OAAA;MACA,KAAAA,OAAA;QACA;QACA,QACA;UAAAoB,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;MACA;MAEA,IAAArB,OAAA;QACA,QACA;UAAAoB,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;MACA,WAAArB,OAAA,iBAAAA,OAAA;QACA,QACA;UAAAoB,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;MACA;MAEA;IACA;IAEA;IACAJ,QAAA,WAAAA,SAAAK,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,GAAA;QACA,KAAAE,WAAA,CAAAC,OAAA;MACA;MACA,KAAAC,OAAA;MACA,IAAAC,MAAA,GAAAC,aAAA,UAAA7B,UAAA;MACA4B,MAAA,CAAAE,MAAA,QAAAL,WAAA,CAAAC,OAAA;MACAE,MAAA,CAAAG,QAAA,QAAAN,WAAA,CAAAM,QAAA;MAEArD,kBAAA,CAAAkD,MAAA,EAAAI,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAV,KAAA,CAAAW,UAAA,GAAAF,GAAA,CAAAG,MAAA,CAAAC,OAAA,IAAAJ,GAAA,CAAAG,MAAA;UACAZ,KAAA,CAAAC,WAAA,CAAAa,KAAA,GAAAL,GAAA,CAAAG,MAAA,CAAAE,KAAA;QACA;UACAd,KAAA,CAAAe,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,OAAA;QACA;QACAjB,KAAA,CAAAG,OAAA;MACA,GAAAe,KAAA,WAAAC,GAAA;QACAnB,KAAA,CAAAG,OAAA;QACAH,KAAA,CAAAe,QAAA,CAAAC,KAAA,eAAAG,GAAA,CAAAF,OAAA;MACA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAAC,UAAA,EAAAC,OAAA,EAAAlC,MAAA;MACA,KAAAa,WAAA,CAAAC,OAAA,GAAAmB,UAAA,CAAAnB,OAAA;;MAEA;MACA,IAAAd,MAAA,IAAAA,MAAA,CAAAmC,KAAA;QACA,KAAA/C,UAAA,CAAAgD,SAAA,GAAApC,MAAA,CAAAmC,KAAA;QACA,KAAA/C,UAAA,CAAAiD,SAAA,GAAArC,MAAA,CAAAsC,KAAA;MACA;QACA,YAAAlD,UAAA,CAAAgD,SAAA;QACA,YAAAhD,UAAA,CAAAiD,SAAA;MACA;MAEA,KAAA/B,QAAA;IACA;IAEA;IACAiC,cAAA,WAAAA,eAAAC,eAAA;MACA,KAAAA,eAAA,GAAAA,eAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAArD,UAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,YAAA,EAAAF;MACA;MACA,KAAAgB,QAAA;IACA;IAEA;IACAoC,mBAAA,WAAAA,oBAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAAD,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,KAAA5C,aAAA,GAAA2C,MAAA;;MAEA;MACA7E,gBAAA,CAAA6E,MAAA,CAAAE,UAAA,EAAA5B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAyB,MAAA,CAAA7C,eAAA,GAAAmB,GAAA,CAAAG,MAAA;;UAEA;UACA,WAAAuB,MAAA,CAAA7C,eAAA,CAAA+C,OAAA;YACA;cACAF,MAAA,CAAA7C,eAAA,CAAA+C,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAJ,MAAA,CAAA7C,eAAA,CAAA+C,OAAA;YACA,SAAAG,CAAA;cACAC,OAAA,CAAAzB,KAAA,aAAAwB,CAAA;cACA;cACAL,MAAA,CAAA7C,eAAA,CAAA+C,OAAA;YACA;UACA,YAAAF,MAAA,CAAA7C,eAAA,CAAA+C,OAAA;YACA;YACAF,MAAA,CAAA7C,eAAA,CAAA+C,OAAA;UACA;UAEAF,MAAA,CAAA9C,kBAAA;QACA;UACA8C,MAAA,CAAApB,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAsB,OAAA,CAAAzB,KAAA,aAAAG,GAAA;QACAgB,MAAA,CAAApB,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA0B,gBAAA,WAAAA,iBAAAR,MAAA;MACA;MACA,KAAA7C,kBAAA;;MAEA;MACA,KAAAd,cAAA;QACAoE,IAAA;QACAP,UAAA,EAAAF,MAAA,CAAAE,UAAA;QACAtD,KAAA;MACA;MACA,KAAAR,gBAAA;IACA;IAEA;IACAsE,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,UAAAjB,eAAA,CAAAkB,MAAA;QACA,KAAA/B,QAAA,CAAAgC,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,eAAA,QAAArC,UAAA,CAAAsC,MAAA,WAAAC,IAAA;QAAA,OAAAL,MAAA,CAAAjB,eAAA,CAAAuB,QAAA,CAAAD,IAAA,CAAAE,EAAA;MAAA;MACA,IAAAC,WAAA,GAAAL,eAAA,CAAAM,GAAA,WAAApB,MAAA;QAAA,OAAAA,MAAA,CAAAE,UAAA;MAAA;;MAEA;MACA,KAAA7D,cAAA;QACAoE,IAAA;QACAU,WAAA,EAAAA,WAAA;QACAvE,KAAA,2CAAAyE,MAAA,CAAAF,WAAA,CAAAP,MAAA;MACA;MACA,KAAAxE,gBAAA;IACA;IAEA;IACAkF,WAAA,WAAAA,YAAA;MACA;MACA,KAAAjF,cAAA;QACAoE,IAAA;QACAnE,UAAA,EAAA6B,aAAA,UAAA7B,UAAA;QACAM,KAAA;MACA;MACA,KAAAR,gBAAA;IACA;IAEA;IACAmF,UAAA,WAAAA,WAAA;MACA,KAAAnF,gBAAA;MACA,KAAAC,cAAA;MACA;MACA,KAAAmB,QAAA;IACA;IAEA;IACAgE,YAAA,WAAAA,aAAAN,EAAA;MAAA,IAAAO,MAAA;MACA3G,YAAA,MAAAkB,GAAA,CAAAE,MAAA;QAAAgF,EAAA,EAAAA;MAAA,GAAA5C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAiD,MAAA,CAAA5C,QAAA,CAAAL,OAAA;UACAiD,MAAA,CAAAjE,QAAA;QACA;UACAiE,MAAA,CAAA5C,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,OAAA;QACA;MACA;IACA;IAEA;IACA2C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAAjC,eAAA,CAAAkB,MAAA;QACA,KAAA/B,QAAA,CAAAgC,OAAA;QACA;MACA;MAEA,KAAAe,QAAA;QACAhF,KAAA;QACAuD,OAAA,gDAAAkB,MAAA,MAAA3B,eAAA,CAAAkB,MAAA;QACAiB,IAAA,WAAAA,KAAA;UACA/G,YAAA,CAAA6G,MAAA,CAAA3F,GAAA,CAAAG,WAAA;YAAA2F,GAAA,EAAAH,MAAA,CAAAjC,eAAA,CAAAqC,IAAA;UAAA,GAAAzD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAmD,MAAA,CAAA9C,QAAA,CAAAL,OAAA;cACAmD,MAAA,CAAAjC,eAAA;cACAiC,MAAA,CAAAnE,QAAA;YACA;cACAmE,MAAA,CAAA9C,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,OAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiD,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA;QACA,OAAA1G,MAAA,CAAA0G,IAAA;MACA,SAAA3B,CAAA;QACAC,OAAA,CAAAzB,KAAA,kBAAAwB,CAAA;QACA,OAAA2B,IAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,IAAA;MACA,KAAAA,IAAA;MACA;QACA;QACA,IAAAC,OAAA,GAAAhC,IAAA,CAAAC,KAAA,CAAA8B,IAAA;QACA;QACA,IAAAC,OAAA,IAAAC,OAAA,CAAAD,OAAA,kBAAAA,OAAA,CAAAD,IAAA;UACA,OAAAC,OAAA,CAAAD,IAAA;QACA;QACA;QACA,OAAA/B,IAAA,CAAAkC,SAAA,CAAAF,OAAA;MACA,SAAA9B,CAAA;QACA;QACA,OAAA6B,IAAA;MACA;IACA;IAEA;IACAI,mBAAA,WAAAA,oBAAA1C,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACA2C,oBAAA,WAAAA,qBAAA3C,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACA4C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAd,QAAA;QACAhF,KAAA;QACAuD,OAAA;QACA0B,IAAA,WAAAA,KAAA;UACA;UACA,IAAA3D,MAAA,GAAAC,aAAA,KAAAuE,MAAA,CAAApG,UAAA;;UAEA;UACA,IAAAoG,MAAA,CAAAhD,eAAA,IAAAgD,MAAA,CAAAhD,eAAA,CAAAkB,MAAA;YACA1C,MAAA,CAAA4D,GAAA,GAAAY,MAAA,CAAAhD,eAAA,CAAAqC,IAAA;UACA;UAEAW,MAAA,CAAAC,aAAA;UAEAvH,kBAAA,CAAA8C,MAAA,EAAAI,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAsB,IAAA;cACA;cACA,IAAA+C,MAAA,OAAAC,UAAA;cACAD,MAAA,CAAAE,MAAA,aAAAxC,CAAA;gBACA;kBACA,IAAA5B,MAAA,GAAA0B,IAAA,CAAAC,KAAA,CAAAC,CAAA,CAAAyC,MAAA,CAAArE,MAAA;kBACAgE,MAAA,CAAA7D,QAAA,CAAAC,KAAA,CAAAJ,MAAA,CAAAK,OAAA;gBACA,SAAAuB,CAAA;kBACAoC,MAAA,CAAA7D,QAAA,CAAAC,KAAA;gBACA;gBACA4D,MAAA,CAAAC,aAAA;cACA;cACAC,MAAA,CAAAI,UAAA,CAAAzE,GAAA;YACA;cACA;cACA;cACA,IAAA0E,IAAA,OAAAC,IAAA,EAAA3E,GAAA;gBAAAsB,IAAA;cAAA;;cAEA;cACA,IAAAsD,QAAA,+BAAA9B,MAAA,KAAA+B,IAAA,GAAAC,OAAA;;cAEA;cACA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,gBAAA;gBACA;gBACAF,MAAA,CAAAC,SAAA,CAAAC,gBAAA,CAAAP,IAAA,EAAAE,QAAA;cACA;gBACA;gBACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,IAAA,CAAAG,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAb,IAAA;gBACAQ,IAAA,CAAAM,QAAA,GAAAZ,QAAA;gBACAM,IAAA,CAAAO,KAAA,CAAAC,OAAA;gBACAP,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,IAAA;gBACAA,IAAA,CAAAW,KAAA;;gBAEA;gBACAC,UAAA;kBACAR,GAAA,CAAAS,eAAA,CAAAb,IAAA,CAAAG,IAAA;kBACAF,QAAA,CAAAQ,IAAA,CAAAK,WAAA,CAAAd,IAAA;gBACA;cACA;cAEAf,MAAA,CAAA7D,QAAA,CAAAL,OAAA;YACA;YAEAkE,MAAA,CAAAC,aAAA;UACA,GAAA3D,KAAA,WAAAF,KAAA;YACAyB,OAAA,CAAAzB,KAAA,UAAAA,KAAA;YACA4D,MAAA,CAAA7D,QAAA,CAAAC,KAAA;YACA4D,MAAA,CAAAC,aAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}