{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: 'JCheckbox',\n  props: {\n    value: {\n      type: String,\n      required: false\n    },\n    /*label value*/\n    options: {\n      type: Array,\n      required: true\n    }\n  },\n  data: function data() {\n    return {\n      checkboxArray: !this.value ? [] : this.value.split(\",\")\n    };\n  },\n  watch: {\n    value: function value(val) {\n      if (!val) {\n        this.checkboxArray = [];\n      } else {\n        this.checkboxArray = this.value.split(\",\");\n      }\n    }\n  },\n  methods: {\n    onChange: function onChange(checkedValues) {\n      this.$emit('change', checkedValues.join(\",\"));\n    }\n  },\n  model: {\n    prop: 'value',\n    event: 'change'\n  }\n};", {"version": 3, "names": ["name", "props", "value", "type", "String", "required", "options", "Array", "data", "checkboxArray", "split", "watch", "val", "methods", "onChange", "checkedValues", "$emit", "join", "model", "prop", "event"], "sources": ["src/components/jeecg/JCheckbox.vue"], "sourcesContent": ["<template>\n  <a-checkbox-group :options=\"options\" :value=\"checkboxArray\" v-bind=\"$attrs\" @change=\"onChange\" />\n</template>\n\n<script>\n  export default {\n    name: 'JCheckbox',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      /*label value*/\n      options:{\n        type: Array,\n        required: true\n      }\n    },\n    data(){\n      return {\n        checkboxArray:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.checkboxArray = []\n        }else{\n          this.checkboxArray = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (checkedValues) {\n        this.$emit('change', checkedValues.join(\",\"));\n      },\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n</script>\n"], "mappings": "AAKA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA,QAAAP,KAAA,aAAAA,KAAA,CAAAQ,KAAA;IACA;EACA;EACAC,KAAA;IACAT,KAAA,WAAAA,MAAAU,GAAA;MACA,KAAAA,GAAA;QACA,KAAAH,aAAA;MACA;QACA,KAAAA,aAAA,QAAAP,KAAA,CAAAQ,KAAA;MACA;IACA;EACA;EACAG,OAAA;IACAC,QAAA,WAAAA,SAAAC,aAAA;MACA,KAAAC,KAAA,WAAAD,aAAA,CAAAE,IAAA;IACA;EACA;EACAC,KAAA;IACAC,IAAA;IACAC,KAAA;EACA;AACA", "ignoreList": []}]}