{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue?vue&type=template&id=2d23c767&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue", "mtime": 1751099917648}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"shopping-modal\"\n  }, [_c(\"a-modal\", {\n    staticClass: \"kids-modal\",\n    attrs: {\n      visible: _vm.visible,\n      footer: null,\n      width: \"1200px\",\n      maskClosable: true,\n      bodyStyle: {\n        padding: \"0\"\n      }\n    },\n    on: {\n      cancel: _vm.handleCancel\n    },\n    scopedSlots: _vm._u([{\n      key: \"title\",\n      fn: function fn() {\n        return [_c(\"div\", {\n          staticClass: \"modal-title\"\n        }, [_c(\"img\", {\n          staticClass: \"title-small-icon\",\n          attrs: {\n            src: require(\"@/assets/shopping_img/gift-icon.png\"),\n            alt: \"礼物图标\"\n          }\n        }), _c(\"span\", [_vm._v(\"奖品兑换中心\")])])];\n      },\n      proxy: true\n    }])\n  }, [_c(\"div\", {\n    staticClass: \"shopping-container\"\n  }, [_c(\"div\", {\n    staticClass: \"gift-shelf\"\n  }, [_c(\"div\", {\n    staticClass: \"shelf-header\"\n  }, [_c(\"div\", {\n    staticClass: \"shelf-subtitle\"\n  }, [_vm._v(\"用你的金币兑换喜欢的礼物吧！\")])]), _c(\"div\", {\n    staticClass: \"shelf-content\"\n  }, [_vm.giftRow1 && _vm.giftRow1.length > 0 ? _c(\"div\", {\n    staticClass: \"gift-row\"\n  }, _vm._l(_vm.giftRow1, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"gift-item\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-box\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-ribbon\"\n    }, [_c(\"span\", [_vm._v(\"热门\")])]), _c(\"div\", {\n      staticClass: \"gift-image-container\"\n    }, [_c(\"img\", {\n      staticClass: \"gift-image\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"礼物图片\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"gift-shine\"\n    })]), _c(\"div\", {\n      staticClass: \"gift-info\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"gift-score\"\n    }, [_c(\"i\", {\n      staticClass: \"coin-icon\"\n    }), _vm._v(\"所需金币: \" + _vm._s(item.score))]), _c(\"a-button\", {\n      staticClass: \"exchange-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.exchangeGift(item);\n        }\n      }\n    }, [_vm._v(\"立即兑换\")])], 1)])]);\n  }), 0) : _vm._e(), _vm.giftRow1 && _vm.giftRow1.length > 0 && _vm.giftRow2 && _vm.giftRow2.length > 0 ? _c(\"div\", {\n    staticClass: \"divider-line\"\n  }, [_c(\"div\", {\n    staticClass: \"star-divider\"\n  }, [_vm._v(\"★\")])]) : _vm._e(), _vm.giftRow2 && _vm.giftRow2.length > 0 ? _c(\"div\", {\n    staticClass: \"gift-row\"\n  }, _vm._l(_vm.giftRow2, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"gift-item\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-box\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-image-container\"\n    }, [_c(\"img\", {\n      staticClass: \"gift-image\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"礼物图片\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"gift-shine\"\n    })]), _c(\"div\", {\n      staticClass: \"gift-info\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"gift-score\"\n    }, [_c(\"i\", {\n      staticClass: \"coin-icon\"\n    }), _vm._v(\"所需金币: \" + _vm._s(item.score))]), _c(\"a-button\", {\n      staticClass: \"exchange-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.exchangeGift(item);\n        }\n      }\n    }, [_vm._v(\"立即兑换\")])], 1)])]);\n  }), 0) : _vm._e(), _vm.giftRow2 && _vm.giftRow2.length > 0 && _vm.giftRow3 && _vm.giftRow3.length > 0 ? _c(\"div\", {\n    staticClass: \"divider-line\"\n  }, [_c(\"div\", {\n    staticClass: \"star-divider\"\n  }, [_vm._v(\"★\")])]) : _vm._e(), _vm.giftRow3 && _vm.giftRow3.length > 0 ? _c(\"div\", {\n    staticClass: \"gift-row\"\n  }, _vm._l(_vm.giftRow3, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"gift-item\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-box\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-image-container\"\n    }, [_c(\"img\", {\n      staticClass: \"gift-image\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"礼物图片\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"gift-shine\"\n    })]), _c(\"div\", {\n      staticClass: \"gift-info\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"gift-score\"\n    }, [_c(\"i\", {\n      staticClass: \"coin-icon\"\n    }), _vm._v(\"所需金币: \" + _vm._s(item.score))]), _c(\"a-button\", {\n      staticClass: \"exchange-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.exchangeGift(item);\n        }\n      }\n    }, [_vm._v(\"立即兑换\")])], 1)])]);\n  }), 0) : _vm._e(), _vm.giftRow3 && _vm.giftRow3.length > 0 && _vm.giftRow4 && _vm.giftRow4.length > 0 ? _c(\"div\", {\n    staticClass: \"divider-line\"\n  }, [_c(\"div\", {\n    staticClass: \"star-divider\"\n  }, [_vm._v(\"★\")])]) : _vm._e(), _vm.giftRow4 && _vm.giftRow4.length > 0 ? _c(\"div\", {\n    staticClass: \"gift-row\"\n  }, _vm._l(_vm.giftRow4, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"gift-item\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-box\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-image-container\"\n    }, [_c(\"img\", {\n      staticClass: \"gift-image\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"礼物图片\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"gift-shine\"\n    })]), _c(\"div\", {\n      staticClass: \"gift-info\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"gift-score\"\n    }, [_c(\"i\", {\n      staticClass: \"coin-icon\"\n    }), _vm._v(\"所需金币: \" + _vm._s(item.score))]), _c(\"a-button\", {\n      staticClass: \"exchange-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.exchangeGift(item);\n        }\n      }\n    }, [_vm._v(\"立即兑换\")])], 1)])]);\n  }), 0) : _vm._e(), _vm.giftRow4 && _vm.giftRow4.length > 0 && _vm.giftRow5 && _vm.giftRow5.length > 0 ? _c(\"div\", {\n    staticClass: \"divider-line\"\n  }, [_c(\"div\", {\n    staticClass: \"star-divider\"\n  }, [_vm._v(\"★\")])]) : _vm._e(), _vm.giftRow5 && _vm.giftRow5.length > 0 ? _c(\"div\", {\n    staticClass: \"gift-row\"\n  }, _vm._l(_vm.giftRow5, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"gift-item\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-box premium\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-ribbon\"\n    }, [_c(\"span\", [_vm._v(\"珍稀\")])]), _c(\"div\", {\n      staticClass: \"gift-image-container\"\n    }, [_c(\"img\", {\n      staticClass: \"gift-image\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"礼物图片\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"gift-shine\"\n    })]), _c(\"div\", {\n      staticClass: \"gift-info\"\n    }, [_c(\"div\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"gift-score\"\n    }, [_c(\"i\", {\n      staticClass: \"coin-icon\"\n    }), _vm._v(\"所需金币: \" + _vm._s(item.score))]), _c(\"a-button\", {\n      staticClass: \"exchange-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.exchangeGift(item);\n        }\n      }\n    }, [_vm._v(\"立即兑换\")])], 1)])]);\n  }), 0) : _vm._e()])]), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"div\", {\n    staticClass: \"user-card\"\n  }, [_c(\"div\", {\n    staticClass: \"card-decoration top-left\"\n  }), _c(\"div\", {\n    staticClass: \"card-decoration top-right\"\n  }), _c(\"div\", {\n    staticClass: \"card-decoration bottom-left\"\n  }), _c(\"div\", {\n    staticClass: \"card-decoration bottom-right\"\n  }), _c(\"div\", {\n    staticClass: \"user-avatar-box\"\n  }, [_c(\"div\", {\n    staticClass: \"avatar-frame\"\n  }, [_c(\"img\", {\n    staticClass: \"user-avatar\",\n    attrs: {\n      src: _vm.userAvatar,\n      alt: \"账号头像\"\n    }\n  })])]), _c(\"div\", {\n    staticClass: \"user-name\"\n  }, [_vm._v(_vm._s(_vm.userName))]), _c(\"div\", {\n    staticClass: \"user-score\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-container\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-stack\"\n  }), _c(\"span\", [_vm._v(\"剩余金币: \" + _vm._s(_vm.userScore))])])]), _c(\"div\", {\n    staticClass: \"exchange-history\"\n  }, [_c(\"div\", {\n    staticClass: \"history-title\"\n  }, [_c(\"i\", {\n    staticClass: \"history-icon\"\n  }), _vm._v(\"兑换记录\\n              \"), _c(\"a-button\", {\n    staticClass: \"more-btn\",\n    attrs: {\n      type: \"link\"\n    },\n    on: {\n      click: _vm.showAllRecords\n    }\n  }, [_vm._v(\"更多\")])], 1), _c(\"div\", {\n    staticClass: \"history-list\"\n  }, [_c(\"a-spin\", {\n    attrs: {\n      spinning: _vm.loadingRecords\n    }\n  }, [_vm.exchangeRecords && _vm.exchangeRecords.length > 0 ? _c(\"div\", _vm._l(_vm.exchangeRecords, function (record, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"history-item\"\n    }, [_c(\"span\", {\n      staticClass: \"gift-name\"\n    }, [_vm._v(_vm._s(record.giftName))]), _c(\"span\", {\n      staticClass: \"gift-time\"\n    }, [_vm._v(_vm._s(_vm.formatDate(record.exchangeTime)))]), _c(\"span\", {\n      staticClass: \"gift-status\",\n      class: record.status === 1 ? \"received\" : \"waiting\"\n    }, [_vm._v(\"\\n                      \" + _vm._s(record.status === 1 ? \"已领取\" : \"待领取\") + \"\\n                    \")])]);\n  }), 0) : _c(\"div\", {\n    staticClass: \"history-empty\"\n  }, [_vm._v(\"暂无兑换记录\")])])], 1)]), _c(\"div\", {\n    staticClass: \"coin-rules\"\n  }, [_c(\"a-button\", {\n    attrs: {\n      type: \"link\"\n    },\n    on: {\n      click: _vm.showRules\n    }\n  }, [_vm._v(\"如何获得更多金币?\")])], 1)])])])]), _c(\"a-modal\", {\n    staticClass: \"records-modal\",\n    attrs: {\n      title: \"礼物兑换记录\",\n      visible: _vm.recordsModalVisible,\n      footer: null,\n      width: \"800px\",\n      bodyStyle: {\n        maxHeight: \"600px\",\n        overflow: \"auto\",\n        padding: \"24px\"\n      }\n    },\n    on: {\n      cancel: _vm.closeRecordsModal\n    }\n  }, [_c(\"a-spin\", {\n    attrs: {\n      spinning: _vm.loadingAllRecords\n    }\n  }, [_c(\"a-table\", {\n    attrs: {\n      columns: _vm.recordColumns,\n      dataSource: _vm.allExchangeRecords,\n      pagination: _vm.pagination,\n      rowKey: \"id\",\n      size: \"middle\",\n      scroll: {\n        y: 450\n      }\n    },\n    on: {\n      change: _vm.handleTableChange\n    },\n    scopedSlots: _vm._u([{\n      key: \"giftName\",\n      fn: function fn(text) {\n        return [_c(\"span\", [_vm._v(_vm._s(text))])];\n      }\n    }, {\n      key: \"exchangeTime\",\n      fn: function fn(text) {\n        return [_c(\"span\", [_vm._v(_vm._s(_vm.formatDate(text)))])];\n      }\n    }, {\n      key: \"status\",\n      fn: function fn(text) {\n        return [_c(\"a-tag\", {\n          attrs: {\n            color: text === 1 ? \"green\" : \"orange\"\n          }\n        }, [_vm._v(\"\\n            \" + _vm._s(text === 1 ? \"已领取\" : \"待领取\") + \"\\n          \")])];\n      }\n    }, {\n      key: \"receiveTime\",\n      fn: function fn(text) {\n        return [_c(\"span\", [_vm._v(_vm._s(text ? _vm.formatDate(text) : \"-\"))])];\n      }\n    }])\n  })], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "visible", "footer", "width", "maskClosable", "bodyStyle", "padding", "on", "cancel", "handleCancel", "scopedSlots", "_u", "key", "fn", "src", "require", "alt", "_v", "proxy", "giftRow1", "length", "_l", "item", "id", "imgUrl", "_s", "name", "score", "type", "click", "$event", "exchangeGift", "_e", "giftRow2", "giftRow3", "giftRow4", "giftRow5", "userAvatar", "userName", "userScore", "showAllRecords", "spinning", "loadingRecords", "exchangeRecords", "record", "index", "giftName", "formatDate", "exchangeTime", "class", "status", "showRules", "title", "recordsModalVisible", "maxHeight", "overflow", "closeRecordsModal", "loadingAllRecords", "columns", "recordColumns", "dataSource", "allExchangeRecords", "pagination", "<PERSON><PERSON><PERSON>", "size", "scroll", "y", "change", "handleTableChange", "text", "color", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/ShoppingModal.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"shopping-modal\" },\n    [\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"kids-modal\",\n          attrs: {\n            visible: _vm.visible,\n            footer: null,\n            width: \"1200px\",\n            maskClosable: true,\n            bodyStyle: { padding: \"0\" },\n          },\n          on: { cancel: _vm.handleCancel },\n          scopedSlots: _vm._u([\n            {\n              key: \"title\",\n              fn: function () {\n                return [\n                  _c(\"div\", { staticClass: \"modal-title\" }, [\n                    _c(\"img\", {\n                      staticClass: \"title-small-icon\",\n                      attrs: {\n                        src: require(\"@/assets/shopping_img/gift-icon.png\"),\n                        alt: \"礼物图标\",\n                      },\n                    }),\n                    _c(\"span\", [_vm._v(\"奖品兑换中心\")]),\n                  ]),\n                ]\n              },\n              proxy: true,\n            },\n          ]),\n        },\n        [\n          _c(\"div\", { staticClass: \"shopping-container\" }, [\n            _c(\"div\", { staticClass: \"gift-shelf\" }, [\n              _c(\"div\", { staticClass: \"shelf-header\" }, [\n                _c(\"div\", { staticClass: \"shelf-subtitle\" }, [\n                  _vm._v(\"用你的金币兑换喜欢的礼物吧！\"),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"shelf-content\" }, [\n                _vm.giftRow1 && _vm.giftRow1.length > 0\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"gift-row\" },\n                      _vm._l(_vm.giftRow1, function (item) {\n                        return _c(\n                          \"div\",\n                          { key: item.id, staticClass: \"gift-item\" },\n                          [\n                            _c(\"div\", { staticClass: \"gift-box\" }, [\n                              _c(\"div\", { staticClass: \"gift-ribbon\" }, [\n                                _c(\"span\", [_vm._v(\"热门\")]),\n                              ]),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-image-container\" },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"gift-image\",\n                                    attrs: {\n                                      src: item.imgUrl,\n                                      alt: \"礼物图片\",\n                                    },\n                                  }),\n                                  _c(\"div\", { staticClass: \"gift-shine\" }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-info\" },\n                                [\n                                  _c(\"div\", { staticClass: \"gift-name\" }, [\n                                    _vm._v(_vm._s(item.name)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"gift-score\" }, [\n                                    _c(\"i\", { staticClass: \"coin-icon\" }),\n                                    _vm._v(\"所需金币: \" + _vm._s(item.score)),\n                                  ]),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"exchange-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.exchangeGift(item)\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"立即兑换\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : _vm._e(),\n                _vm.giftRow1 &&\n                _vm.giftRow1.length > 0 &&\n                _vm.giftRow2 &&\n                _vm.giftRow2.length > 0\n                  ? _c(\"div\", { staticClass: \"divider-line\" }, [\n                      _c(\"div\", { staticClass: \"star-divider\" }, [_vm._v(\"★\")]),\n                    ])\n                  : _vm._e(),\n                _vm.giftRow2 && _vm.giftRow2.length > 0\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"gift-row\" },\n                      _vm._l(_vm.giftRow2, function (item) {\n                        return _c(\n                          \"div\",\n                          { key: item.id, staticClass: \"gift-item\" },\n                          [\n                            _c(\"div\", { staticClass: \"gift-box\" }, [\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-image-container\" },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"gift-image\",\n                                    attrs: {\n                                      src: item.imgUrl,\n                                      alt: \"礼物图片\",\n                                    },\n                                  }),\n                                  _c(\"div\", { staticClass: \"gift-shine\" }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-info\" },\n                                [\n                                  _c(\"div\", { staticClass: \"gift-name\" }, [\n                                    _vm._v(_vm._s(item.name)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"gift-score\" }, [\n                                    _c(\"i\", { staticClass: \"coin-icon\" }),\n                                    _vm._v(\"所需金币: \" + _vm._s(item.score)),\n                                  ]),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"exchange-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.exchangeGift(item)\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"立即兑换\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : _vm._e(),\n                _vm.giftRow2 &&\n                _vm.giftRow2.length > 0 &&\n                _vm.giftRow3 &&\n                _vm.giftRow3.length > 0\n                  ? _c(\"div\", { staticClass: \"divider-line\" }, [\n                      _c(\"div\", { staticClass: \"star-divider\" }, [_vm._v(\"★\")]),\n                    ])\n                  : _vm._e(),\n                _vm.giftRow3 && _vm.giftRow3.length > 0\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"gift-row\" },\n                      _vm._l(_vm.giftRow3, function (item) {\n                        return _c(\n                          \"div\",\n                          { key: item.id, staticClass: \"gift-item\" },\n                          [\n                            _c(\"div\", { staticClass: \"gift-box\" }, [\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-image-container\" },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"gift-image\",\n                                    attrs: {\n                                      src: item.imgUrl,\n                                      alt: \"礼物图片\",\n                                    },\n                                  }),\n                                  _c(\"div\", { staticClass: \"gift-shine\" }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-info\" },\n                                [\n                                  _c(\"div\", { staticClass: \"gift-name\" }, [\n                                    _vm._v(_vm._s(item.name)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"gift-score\" }, [\n                                    _c(\"i\", { staticClass: \"coin-icon\" }),\n                                    _vm._v(\"所需金币: \" + _vm._s(item.score)),\n                                  ]),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"exchange-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.exchangeGift(item)\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"立即兑换\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : _vm._e(),\n                _vm.giftRow3 &&\n                _vm.giftRow3.length > 0 &&\n                _vm.giftRow4 &&\n                _vm.giftRow4.length > 0\n                  ? _c(\"div\", { staticClass: \"divider-line\" }, [\n                      _c(\"div\", { staticClass: \"star-divider\" }, [_vm._v(\"★\")]),\n                    ])\n                  : _vm._e(),\n                _vm.giftRow4 && _vm.giftRow4.length > 0\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"gift-row\" },\n                      _vm._l(_vm.giftRow4, function (item) {\n                        return _c(\n                          \"div\",\n                          { key: item.id, staticClass: \"gift-item\" },\n                          [\n                            _c(\"div\", { staticClass: \"gift-box\" }, [\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-image-container\" },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"gift-image\",\n                                    attrs: {\n                                      src: item.imgUrl,\n                                      alt: \"礼物图片\",\n                                    },\n                                  }),\n                                  _c(\"div\", { staticClass: \"gift-shine\" }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-info\" },\n                                [\n                                  _c(\"div\", { staticClass: \"gift-name\" }, [\n                                    _vm._v(_vm._s(item.name)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"gift-score\" }, [\n                                    _c(\"i\", { staticClass: \"coin-icon\" }),\n                                    _vm._v(\"所需金币: \" + _vm._s(item.score)),\n                                  ]),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"exchange-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.exchangeGift(item)\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"立即兑换\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : _vm._e(),\n                _vm.giftRow4 &&\n                _vm.giftRow4.length > 0 &&\n                _vm.giftRow5 &&\n                _vm.giftRow5.length > 0\n                  ? _c(\"div\", { staticClass: \"divider-line\" }, [\n                      _c(\"div\", { staticClass: \"star-divider\" }, [_vm._v(\"★\")]),\n                    ])\n                  : _vm._e(),\n                _vm.giftRow5 && _vm.giftRow5.length > 0\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"gift-row\" },\n                      _vm._l(_vm.giftRow5, function (item) {\n                        return _c(\n                          \"div\",\n                          { key: item.id, staticClass: \"gift-item\" },\n                          [\n                            _c(\"div\", { staticClass: \"gift-box premium\" }, [\n                              _c(\"div\", { staticClass: \"gift-ribbon\" }, [\n                                _c(\"span\", [_vm._v(\"珍稀\")]),\n                              ]),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-image-container\" },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"gift-image\",\n                                    attrs: {\n                                      src: item.imgUrl,\n                                      alt: \"礼物图片\",\n                                    },\n                                  }),\n                                  _c(\"div\", { staticClass: \"gift-shine\" }),\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                { staticClass: \"gift-info\" },\n                                [\n                                  _c(\"div\", { staticClass: \"gift-name\" }, [\n                                    _vm._v(_vm._s(item.name)),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"gift-score\" }, [\n                                    _c(\"i\", { staticClass: \"coin-icon\" }),\n                                    _vm._v(\"所需金币: \" + _vm._s(item.score)),\n                                  ]),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"exchange-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.exchangeGift(item)\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"立即兑换\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : _vm._e(),\n              ]),\n            ]),\n            _c(\"div\", { staticClass: \"user-info\" }, [\n              _c(\"div\", { staticClass: \"user-card\" }, [\n                _c(\"div\", { staticClass: \"card-decoration top-left\" }),\n                _c(\"div\", { staticClass: \"card-decoration top-right\" }),\n                _c(\"div\", { staticClass: \"card-decoration bottom-left\" }),\n                _c(\"div\", { staticClass: \"card-decoration bottom-right\" }),\n                _c(\"div\", { staticClass: \"user-avatar-box\" }, [\n                  _c(\"div\", { staticClass: \"avatar-frame\" }, [\n                    _c(\"img\", {\n                      staticClass: \"user-avatar\",\n                      attrs: { src: _vm.userAvatar, alt: \"账号头像\" },\n                    }),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"user-name\" }, [\n                  _vm._v(_vm._s(_vm.userName)),\n                ]),\n                _c(\"div\", { staticClass: \"user-score\" }, [\n                  _c(\"div\", { staticClass: \"coin-container\" }, [\n                    _c(\"div\", { staticClass: \"coin-stack\" }),\n                    _c(\"span\", [_vm._v(\"剩余金币: \" + _vm._s(_vm.userScore))]),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"exchange-history\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"history-title\" },\n                    [\n                      _c(\"i\", { staticClass: \"history-icon\" }),\n                      _vm._v(\"兑换记录\\n              \"),\n                      _c(\n                        \"a-button\",\n                        {\n                          staticClass: \"more-btn\",\n                          attrs: { type: \"link\" },\n                          on: { click: _vm.showAllRecords },\n                        },\n                        [_vm._v(\"更多\")]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"history-list\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loadingRecords } },\n                        [\n                          _vm.exchangeRecords && _vm.exchangeRecords.length > 0\n                            ? _c(\n                                \"div\",\n                                _vm._l(\n                                  _vm.exchangeRecords,\n                                  function (record, index) {\n                                    return _c(\n                                      \"div\",\n                                      {\n                                        key: index,\n                                        staticClass: \"history-item\",\n                                      },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"gift-name\" },\n                                          [_vm._v(_vm._s(record.giftName))]\n                                        ),\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"gift-time\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(\n                                                _vm.formatDate(\n                                                  record.exchangeTime\n                                                )\n                                              )\n                                            ),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"span\",\n                                          {\n                                            staticClass: \"gift-status\",\n                                            class:\n                                              record.status === 1\n                                                ? \"received\"\n                                                : \"waiting\",\n                                          },\n                                          [\n                                            _vm._v(\n                                              \"\\n                      \" +\n                                                _vm._s(\n                                                  record.status === 1\n                                                    ? \"已领取\"\n                                                    : \"待领取\"\n                                                ) +\n                                                \"\\n                    \"\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    )\n                                  }\n                                ),\n                                0\n                              )\n                            : _c(\"div\", { staticClass: \"history-empty\" }, [\n                                _vm._v(\"暂无兑换记录\"),\n                              ]),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"coin-rules\" },\n                  [\n                    _c(\n                      \"a-button\",\n                      { attrs: { type: \"link\" }, on: { click: _vm.showRules } },\n                      [_vm._v(\"如何获得更多金币?\")]\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n            ]),\n          ]),\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"records-modal\",\n          attrs: {\n            title: \"礼物兑换记录\",\n            visible: _vm.recordsModalVisible,\n            footer: null,\n            width: \"800px\",\n            bodyStyle: {\n              maxHeight: \"600px\",\n              overflow: \"auto\",\n              padding: \"24px\",\n            },\n          },\n          on: { cancel: _vm.closeRecordsModal },\n        },\n        [\n          _c(\n            \"a-spin\",\n            { attrs: { spinning: _vm.loadingAllRecords } },\n            [\n              _c(\"a-table\", {\n                attrs: {\n                  columns: _vm.recordColumns,\n                  dataSource: _vm.allExchangeRecords,\n                  pagination: _vm.pagination,\n                  rowKey: \"id\",\n                  size: \"middle\",\n                  scroll: { y: 450 },\n                },\n                on: { change: _vm.handleTableChange },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"giftName\",\n                    fn: function (text) {\n                      return [_c(\"span\", [_vm._v(_vm._s(text))])]\n                    },\n                  },\n                  {\n                    key: \"exchangeTime\",\n                    fn: function (text) {\n                      return [\n                        _c(\"span\", [_vm._v(_vm._s(_vm.formatDate(text)))]),\n                      ]\n                    },\n                  },\n                  {\n                    key: \"status\",\n                    fn: function (text) {\n                      return [\n                        _c(\n                          \"a-tag\",\n                          { attrs: { color: text === 1 ? \"green\" : \"orange\" } },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(text === 1 ? \"已领取\" : \"待领取\") +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                  {\n                    key: \"receiveTime\",\n                    fn: function (text) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(text ? _vm.formatDate(text) : \"-\")),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,YAAY;IACzBC,KAAK,EAAE;MACLC,OAAO,EAAEL,GAAG,CAACK,OAAO;MACpBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAI;IAC5B,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEZ,GAAG,CAACa;IAAa,CAAC;IAChCC,WAAW,EAAEd,GAAG,CAACe,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,OAAO;MACZC,EAAE,EAAE,SAAAA,GAAA,EAAY;QACd,OAAO,CACLhB,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;UACRE,WAAW,EAAE,kBAAkB;UAC/BC,KAAK,EAAE;YACLc,GAAG,EAAEC,OAAO,CAAC,qCAAqC,CAAC;YACnDC,GAAG,EAAE;UACP;QACF,CAAC,CAAC,EACFnB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC/B,CAAC,CACH;MACH,CAAC;MACDC,KAAK,EAAE;IACT,CAAC,CACF;EACH,CAAC,EACD,CACErB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACqB,EAAE,CAAC,gBAAgB,CAAC,CACzB,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAACuB,QAAQ,IAAIvB,GAAG,CAACuB,QAAQ,CAACC,MAAM,GAAG,CAAC,GACnCvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACuB,QAAQ,EAAE,UAAUG,IAAI,EAAE;IACnC,OAAOzB,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAEU,IAAI,CAACC,EAAE;MAAExB,WAAW,EAAE;IAAY,CAAC,EAC1C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFpB,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QACLc,GAAG,EAAEQ,IAAI,CAACE,MAAM;QAChBR,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAE5C,CAAC,EACDF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1B,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,CAAC,EACrCH,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACK,KAAK,CAAC,CAAC,CACtC,CAAC,EACF9B,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QAAE4B,IAAI,EAAE;MAAU,CAAC;MAC1BrB,EAAE,EAAE;QACFsB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,YAAY,CAACT,IAAI,CAAC;QAC/B;MACF;IACF,CAAC,EACD,CAAC1B,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACuB,QAAQ,IACZvB,GAAG,CAACuB,QAAQ,CAACC,MAAM,GAAG,CAAC,IACvBxB,GAAG,CAACqC,QAAQ,IACZrC,GAAG,CAACqC,QAAQ,CAACb,MAAM,GAAG,CAAC,GACnBvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACqB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAC1D,CAAC,GACFrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACqC,QAAQ,IAAIrC,GAAG,CAACqC,QAAQ,CAACb,MAAM,GAAG,CAAC,GACnCvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACqC,QAAQ,EAAE,UAAUX,IAAI,EAAE;IACnC,OAAOzB,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAEU,IAAI,CAACC,EAAE;MAAExB,WAAW,EAAE;IAAY,CAAC,EAC1C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QACLc,GAAG,EAAEQ,IAAI,CAACE,MAAM;QAChBR,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAE5C,CAAC,EACDF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1B,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,CAAC,EACrCH,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACK,KAAK,CAAC,CAAC,CACtC,CAAC,EACF9B,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QAAE4B,IAAI,EAAE;MAAU,CAAC;MAC1BrB,EAAE,EAAE;QACFsB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,YAAY,CAACT,IAAI,CAAC;QAC/B;MACF;IACF,CAAC,EACD,CAAC1B,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACqC,QAAQ,IACZrC,GAAG,CAACqC,QAAQ,CAACb,MAAM,GAAG,CAAC,IACvBxB,GAAG,CAACsC,QAAQ,IACZtC,GAAG,CAACsC,QAAQ,CAACd,MAAM,GAAG,CAAC,GACnBvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACqB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAC1D,CAAC,GACFrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACsC,QAAQ,IAAItC,GAAG,CAACsC,QAAQ,CAACd,MAAM,GAAG,CAAC,GACnCvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACsC,QAAQ,EAAE,UAAUZ,IAAI,EAAE;IACnC,OAAOzB,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAEU,IAAI,CAACC,EAAE;MAAExB,WAAW,EAAE;IAAY,CAAC,EAC1C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QACLc,GAAG,EAAEQ,IAAI,CAACE,MAAM;QAChBR,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAE5C,CAAC,EACDF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1B,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,CAAC,EACrCH,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACK,KAAK,CAAC,CAAC,CACtC,CAAC,EACF9B,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QAAE4B,IAAI,EAAE;MAAU,CAAC;MAC1BrB,EAAE,EAAE;QACFsB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,YAAY,CAACT,IAAI,CAAC;QAC/B;MACF;IACF,CAAC,EACD,CAAC1B,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACsC,QAAQ,IACZtC,GAAG,CAACsC,QAAQ,CAACd,MAAM,GAAG,CAAC,IACvBxB,GAAG,CAACuC,QAAQ,IACZvC,GAAG,CAACuC,QAAQ,CAACf,MAAM,GAAG,CAAC,GACnBvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACqB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAC1D,CAAC,GACFrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACuC,QAAQ,IAAIvC,GAAG,CAACuC,QAAQ,CAACf,MAAM,GAAG,CAAC,GACnCvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACuC,QAAQ,EAAE,UAAUb,IAAI,EAAE;IACnC,OAAOzB,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAEU,IAAI,CAACC,EAAE;MAAExB,WAAW,EAAE;IAAY,CAAC,EAC1C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QACLc,GAAG,EAAEQ,IAAI,CAACE,MAAM;QAChBR,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAE5C,CAAC,EACDF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1B,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,CAAC,EACrCH,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACK,KAAK,CAAC,CAAC,CACtC,CAAC,EACF9B,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QAAE4B,IAAI,EAAE;MAAU,CAAC;MAC1BrB,EAAE,EAAE;QACFsB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,YAAY,CAACT,IAAI,CAAC;QAC/B;MACF;IACF,CAAC,EACD,CAAC1B,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACuC,QAAQ,IACZvC,GAAG,CAACuC,QAAQ,CAACf,MAAM,GAAG,CAAC,IACvBxB,GAAG,CAACwC,QAAQ,IACZxC,GAAG,CAACwC,QAAQ,CAAChB,MAAM,GAAG,CAAC,GACnBvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACqB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAC1D,CAAC,GACFrB,GAAG,CAACoC,EAAE,CAAC,CAAC,EACZpC,GAAG,CAACwC,QAAQ,IAAIxC,GAAG,CAACwC,QAAQ,CAAChB,MAAM,GAAG,CAAC,GACnCvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACwC,QAAQ,EAAE,UAAUd,IAAI,EAAE;IACnC,OAAOzB,EAAE,CACP,KAAK,EACL;MAAEe,GAAG,EAAEU,IAAI,CAACC,EAAE;MAAExB,WAAW,EAAE;IAAY,CAAC,EAC1C,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFpB,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QACLc,GAAG,EAAEQ,IAAI,CAACE,MAAM;QAChBR,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,CAAC,CAE5C,CAAC,EACDF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1B,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,CAAC,EACrCH,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAACH,IAAI,CAACK,KAAK,CAAC,CAAC,CACtC,CAAC,EACF9B,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE;QAAE4B,IAAI,EAAE;MAAU,CAAC;MAC1BrB,EAAE,EAAE;QACFsB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,YAAY,CAACT,IAAI,CAAC;QAC/B;MACF;IACF,CAAC,EACD,CAAC1B,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDrB,GAAG,CAACoC,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC,EACFnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA2B,CAAC,CAAC,EACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,CAAC,EACvDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA8B,CAAC,CAAC,EACzDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA+B,CAAC,CAAC,EAC1DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEc,GAAG,EAAElB,GAAG,CAACyC,UAAU;MAAErB,GAAG,EAAE;IAAO;EAC5C,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAAC7B,GAAG,CAAC0C,QAAQ,CAAC,CAAC,CAC7B,CAAC,EACFzC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAAC,QAAQ,GAAGrB,GAAG,CAAC6B,EAAE,CAAC7B,GAAG,CAAC2C,SAAS,CAAC,CAAC,CAAC,CAAC,CACvD,CAAC,CACH,CAAC,EACF1C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,GAAG,CAACqB,EAAE,CAAC,sBAAsB,CAAC,EAC9BpB,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE;MAAE4B,IAAI,EAAE;IAAO,CAAC;IACvBrB,EAAE,EAAE;MAAEsB,KAAK,EAAEjC,GAAG,CAAC4C;IAAe;EAClC,CAAC,EACD,CAAC5C,GAAG,CAACqB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,QAAQ,EACR;IAAEG,KAAK,EAAE;MAAEyC,QAAQ,EAAE7C,GAAG,CAAC8C;IAAe;EAAE,CAAC,EAC3C,CACE9C,GAAG,CAAC+C,eAAe,IAAI/C,GAAG,CAAC+C,eAAe,CAACvB,MAAM,GAAG,CAAC,GACjDvB,EAAE,CACA,KAAK,EACLD,GAAG,CAACyB,EAAE,CACJzB,GAAG,CAAC+C,eAAe,EACnB,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACvB,OAAOhD,EAAE,CACP,KAAK,EACL;MACEe,GAAG,EAAEiC,KAAK;MACV9C,WAAW,EAAE;IACf,CAAC,EACD,CACEF,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CAACH,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAACmB,MAAM,CAACE,QAAQ,CAAC,CAAC,CAClC,CAAC,EACDjD,EAAE,CACA,MAAM,EACN;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEH,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAAC6B,EAAE,CACJ7B,GAAG,CAACmD,UAAU,CACZH,MAAM,CAACI,YACT,CACF,CACF,CAAC,CAEL,CAAC,EACDnD,EAAE,CACA,MAAM,EACN;MACEE,WAAW,EAAE,aAAa;MAC1BkD,KAAK,EACHL,MAAM,CAACM,MAAM,KAAK,CAAC,GACf,UAAU,GACV;IACR,CAAC,EACD,CACEtD,GAAG,CAACqB,EAAE,CACJ,0BAA0B,GACxBrB,GAAG,CAAC6B,EAAE,CACJmB,MAAM,CAACM,MAAM,KAAK,CAAC,GACf,KAAK,GACL,KACN,CAAC,GACD,wBACJ,CAAC,CAEL,CAAC,CAEL,CAAC;EACH,CACF,CAAC,EACD,CACF,CAAC,GACDrD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAACqB,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,CAEV,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFpB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CACA,UAAU,EACV;IAAEG,KAAK,EAAE;MAAE4B,IAAI,EAAE;IAAO,CAAC;IAAErB,EAAE,EAAE;MAAEsB,KAAK,EAAEjC,GAAG,CAACuD;IAAU;EAAE,CAAC,EACzD,CAACvD,GAAG,CAACqB,EAAE,CAAC,WAAW,CAAC,CACtB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,EACDpB,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MACLoD,KAAK,EAAE,QAAQ;MACfnD,OAAO,EAAEL,GAAG,CAACyD,mBAAmB;MAChCnD,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,OAAO;MACdE,SAAS,EAAE;QACTiD,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,MAAM;QAChBjD,OAAO,EAAE;MACX;IACF,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEZ,GAAG,CAAC4D;IAAkB;EACtC,CAAC,EACD,CACE3D,EAAE,CACA,QAAQ,EACR;IAAEG,KAAK,EAAE;MAAEyC,QAAQ,EAAE7C,GAAG,CAAC6D;IAAkB;EAAE,CAAC,EAC9C,CACE5D,EAAE,CAAC,SAAS,EAAE;IACZG,KAAK,EAAE;MACL0D,OAAO,EAAE9D,GAAG,CAAC+D,aAAa;MAC1BC,UAAU,EAAEhE,GAAG,CAACiE,kBAAkB;MAClCC,UAAU,EAAElE,GAAG,CAACkE,UAAU;MAC1BC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAI;IACnB,CAAC;IACD3D,EAAE,EAAE;MAAE4D,MAAM,EAAEvE,GAAG,CAACwE;IAAkB,CAAC;IACrC1D,WAAW,EAAEd,GAAG,CAACe,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,UAAU;MACfC,EAAE,EAAE,SAAAA,GAAUwD,IAAI,EAAE;QAClB,OAAO,CAACxE,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAAC4C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;IACF,CAAC,EACD;MACEzD,GAAG,EAAE,cAAc;MACnBC,EAAE,EAAE,SAAAA,GAAUwD,IAAI,EAAE;QAClB,OAAO,CACLxE,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAAC7B,GAAG,CAACmD,UAAU,CAACsB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACnD;MACH;IACF,CAAC,EACD;MACEzD,GAAG,EAAE,QAAQ;MACbC,EAAE,EAAE,SAAAA,GAAUwD,IAAI,EAAE;QAClB,OAAO,CACLxE,EAAE,CACA,OAAO,EACP;UAAEG,KAAK,EAAE;YAAEsE,KAAK,EAAED,IAAI,KAAK,CAAC,GAAG,OAAO,GAAG;UAAS;QAAE,CAAC,EACrD,CACEzE,GAAG,CAACqB,EAAE,CACJ,gBAAgB,GACdrB,GAAG,CAAC6B,EAAE,CAAC4C,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,GAClC,cACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,EACD;MACEzD,GAAG,EAAE,aAAa;MAClBC,EAAE,EAAE,SAAAA,GAAUwD,IAAI,EAAE;QAClB,OAAO,CACLxE,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC6B,EAAE,CAAC4C,IAAI,GAAGzE,GAAG,CAACmD,UAAU,CAACsB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAClD,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIE,eAAe,GAAG,EAAE;AACxB5E,MAAM,CAAC6E,aAAa,GAAG,IAAI;AAE3B,SAAS7E,MAAM,EAAE4E,eAAe", "ignoreList": []}]}