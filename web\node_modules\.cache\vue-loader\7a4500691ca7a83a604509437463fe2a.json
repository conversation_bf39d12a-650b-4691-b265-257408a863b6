{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectRole.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        returnKeys: ['id', 'roleCode'],\n        url: { list: '/sys/role/list' },\n        columns: [\n          { title: '角色名称', dataIndex: 'roleName', align: 'center', width: 120 },\n          { title: '角色编码', dataIndex: 'roleCode', align: 'center', width: 120 }\n        ]\n      }\n    }\n  }\n", {"version": 3, "sources": ["JSelectRole.vue"], "names": [], "mappings": ";AAkBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JSelectRole.vue", "sourceRoot": "src/components/jeecgbiz", "sourcesContent": ["<template>\n  <j-select-biz-component\n    :value=\"value\"\n\n    name=\"角色\"\n    displayKey=\"roleName\"\n\n    :returnKeys=\"returnKeys\"\n    :listUrl=\"url.list\"\n    :columns=\"columns\"\n    queryParamText=\"角色编码\"\n\n    v-on=\"$listeners\"\n    v-bind=\"$attrs\"\n  />\n</template>\n\n<script>\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        returnKeys: ['id', 'roleCode'],\n        url: { list: '/sys/role/list' },\n        columns: [\n          { title: '角色名称', dataIndex: 'roleName', align: 'center', width: 120 },\n          { title: '角色编码', dataIndex: 'roleCode', align: 'center', width: 120 }\n        ]\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped></style>"]}]}