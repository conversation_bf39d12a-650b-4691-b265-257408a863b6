{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue?vue&type=style&index=0&id=48d8f00f&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue", "mtime": 1753199441584}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.user-enter {\n  background: url(/img/login-bg.png) no-repeat;\n  background-size: 100% 100%;\n  border-radius: 10px;\n  width: 250px;\n  min-height: 360px;\n  text-align: center;\n  padding-top: 110px;\n  padding-bottom: 20px;\n  line-height: 50px;\n}\n.ant-btn {\n  width: 80%;\n}\n.welcome{\n  padding: 0 20px;\n  line-height: 30px;\n}\n", {"version": 3, "sources": ["UserEnter.vue"], "names": [], "mappings": ";AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UserEnter.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\n  <div class=\"user-enter\">\n    <div v-if=\"token\">\n      <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"avatarUrl\" />\n      <h3>欢迎您，{{ nickname() }}</h3>\n      <a-button type=\"primary\" @click=\"enter\">进入系统</a-button>\n      <a-button type=\"dashed\" @click=\"changeAccount\">切换账号</a-button>\n    </div>\n    <div v-else>\n      <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"logo2\" />\n      <h3 class=\"welcome\">欢迎来到{{ brandName }}</h3>\n      <a-button type=\"dashed\" @click=\"login\">登录/注册</a-button>\n    </div>\n  </div>\n</template>\n<script>\nimport Vue from 'vue'\nimport { mapActions, mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { getFileAccessHttpUrl } from \"@/api/manage\"\nexport default {\n  data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      token: '',\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n    }\n  },\n  created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    if(this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain){\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + \"/\" + this.$store.getters.sysConfig.logo2\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n       this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if(this.getFileAccessHttpUrl(this.avatar())){\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    }\n  },\n  methods: {\n    ...mapActions([\"Logout\"]),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getFileAccessHttpUrl,\n    login(){\n      this.$router.push('/user/login')\n    },\n    enter() {\n      this.$router.push('/account/settings/base')\n    },\n    changeAccount(){\n      const that = this\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk() {\n          return that.Logout({}).then(() => {\n            window.location.href=\"/user/login\";\n          }).catch(err => {\n            that.$message.error({\n              title: '错误',\n              description: err.message\n            })\n          })\n        },\n        onCancel() {\n        },\n      });\n    }\n  },\n}\n</script>\n<style scoped>\n.user-enter {\n  background: url(/img/login-bg.png) no-repeat;\n  background-size: 100% 100%;\n  border-radius: 10px;\n  width: 250px;\n  min-height: 360px;\n  text-align: center;\n  padding-top: 110px;\n  padding-bottom: 20px;\n  line-height: 50px;\n}\n.ant-btn {\n  width: 80%;\n}\n.welcome{\n  padding: 0 20px;\n  line-height: 30px;\n}\n</style>"]}]}