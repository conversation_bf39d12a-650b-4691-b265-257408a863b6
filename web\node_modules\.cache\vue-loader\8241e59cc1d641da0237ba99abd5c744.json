{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Custom.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Custom.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n  import { colorList } from '@/components/tools/setting'\n  import ASwitch from 'ant-design-vue/es/switch'\n  import AList from \"ant-design-vue/es/list\"\n  import AListItem from \"ant-design-vue/es/list/Item\"\n  import { mixin } from '@/utils/mixin.js'\n\n  const Meta = AListItem.Meta\n\n  export default {\n    components: {\n      AListItem,\n      AList,\n      ASwitch,\n      Meta\n    },\n    mixins: [mixin],\n    data () {\n      return {\n      }\n    },\n    filters: {\n      themeFilter(theme) {\n        const themeMap = {\n          'dark': '暗色',\n          'light': '白色'\n        }\n        return themeMap[theme]\n      },\n    },\n    methods: {\n      colorFilter(color) {\n        const c = colorList.filter(o => o.color === color)[0]\n        return c && c.key\n      },\n\n      onChange (checked) {\n        if (checked) {\n          this.$store.dispatch('ToggleTheme',  'dark')\n        } else {\n          this.$store.dispatch('ToggleTheme',  'light')\n        }\n      }\n    },\n    render () {\n      return (\n        <AList itemLayout=\"horizontal\">\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">风格配色</a>\n              <span slot=\"description\">\n                整体风格配色设置\n              </span>\n            </Meta>\n            <div slot=\"actions\">\n              <ASwitch checkedChildren=\"暗色\" unCheckedChildren=\"白色\" defaultChecked={this.navTheme === 'dark' && true || false} onChange={this.onChange} />\n            </div>\n          </AListItem>\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">主题色</a>\n              <span slot=\"description\">\n                页面风格配色： <a domPropsInnerHTML={ this.colorFilter(this.primaryColor) }/>\n              </span>\n            </Meta>\n          </AListItem>\n        </AList>\n      )\n    }\n  }\n", {"version": 3, "sources": ["Custom.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Custom.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<script>\n  import { colorList } from '@/components/tools/setting'\n  import ASwitch from 'ant-design-vue/es/switch'\n  import AList from \"ant-design-vue/es/list\"\n  import AListItem from \"ant-design-vue/es/list/Item\"\n  import { mixin } from '@/utils/mixin.js'\n\n  const Meta = AListItem.Meta\n\n  export default {\n    components: {\n      AListItem,\n      AList,\n      ASwitch,\n      Meta\n    },\n    mixins: [mixin],\n    data () {\n      return {\n      }\n    },\n    filters: {\n      themeFilter(theme) {\n        const themeMap = {\n          'dark': '暗色',\n          'light': '白色'\n        }\n        return themeMap[theme]\n      },\n    },\n    methods: {\n      colorFilter(color) {\n        const c = colorList.filter(o => o.color === color)[0]\n        return c && c.key\n      },\n\n      onChange (checked) {\n        if (checked) {\n          this.$store.dispatch('ToggleTheme',  'dark')\n        } else {\n          this.$store.dispatch('ToggleTheme',  'light')\n        }\n      }\n    },\n    render () {\n      return (\n        <AList itemLayout=\"horizontal\">\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">风格配色</a>\n              <span slot=\"description\">\n                整体风格配色设置\n              </span>\n            </Meta>\n            <div slot=\"actions\">\n              <ASwitch checkedChildren=\"暗色\" unCheckedChildren=\"白色\" defaultChecked={this.navTheme === 'dark' && true || false} onChange={this.onChange} />\n            </div>\n          </AListItem>\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">主题色</a>\n              <span slot=\"description\">\n                页面风格配色： <a domPropsInnerHTML={ this.colorFilter(this.primaryColor) }/>\n              </span>\n            </Meta>\n          </AListItem>\n        </AList>\n      )\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}