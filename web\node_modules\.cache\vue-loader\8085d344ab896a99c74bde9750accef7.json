{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue?vue&type=template&id=0a6c4f15&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"card-list\" ref=\"content\">\n  <a-list\n    :grid=\"{gutter: 24, lg: 3, md: 2, sm: 1, xs: 1}\"\n    :dataSource=\"dataSource\"\n  >\n    <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\n      <template v-if=\"item === null\">\n        <a-button class=\"new-btn\" type=\"dashed\">\n          <a-icon type=\"plus\"/>\n          新增产品\n        </a-button>\n      </template>\n      <template v-else>\n        <a-card :hoverable=\"true\">\n          <a-card-meta>\n            <div style=\"margin-bottom: 3px\" slot=\"title\">{{ item.title }}</div>\n            <a-avatar class=\"card-avatar\" slot=\"avatar\" :src=\"item.avatar\" size=\"large\"/>\n            <div class=\"meta-content\" slot=\"description\">{{ item.content }}</div>\n          </a-card-meta>\n          <template class=\"ant-card-actions\" slot=\"actions\">\n            <a>操作一</a>\n            <a>操作二</a>\n          </template>\n        </a-card>\n      </template>\n    </a-list-item>\n  </a-list>\n</div>\n", null]}