{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\HomeLayout.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\HomeLayout.vue", "mtime": 1753199484133}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport Vue from 'vue';\nimport { getAction, getFileAccessHttpUrl, postAction } from '@/api/manage';\nimport { ACCESS_TOKEN, USER_ROLE } from '@/store/mutation-types';\nimport { mapActions, mapGetters } from 'vuex';\nimport Header from './modules/Header';\nimport Banner from './modules/Banner';\nimport Footer from './modules/Footer';\nimport UserEnter from './modules/UserEnter';\nimport SlidingPanel from './modules/SlidingPanel';\nexport default {\n  name: 'HomeLayout',\n  components: {\n    Header: Header,\n    Footer: Footer,\n    UserEnter: UserEnter,\n    Banner: Banner,\n    SlidingPanel: SlidingPanel\n  },\n  data: function data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      token: '',\n      sysConfig: {},\n      userCoin: 0,\n      dailyTasksVisible: false,\n      dailyTasks: [],\n      tasksLoading: false,\n      signInLoading: false,\n      coinCheckTimer: null,\n      coinHistoryVisible: false,\n      coinRecords: [],\n      historyLoading: false,\n      allLoaded: false,\n      incomeTotal: 0,\n      expenseTotal: 0\n    };\n  },\n  created: function created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN);\n    this.sysConfig = this.$store.getters.sysConfig;\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo;\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2;\n      this.avatarUrl = this.logo;\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n      this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar;\n    }\n    if (this.getFileAccessHttpUrl(this.avatar())) {\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar());\n    }\n    if (this.token) {\n      this.loadUserCoin();\n\n      // 启动定时检查金币变化\n      this.startCoinCheckTimer();\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 清除定时器\n    this.clearCoinCheckTimer();\n  },\n  methods: _objectSpread(_objectSpread(_objectSpread({\n    getFileAccessHttpUrl: getFileAccessHttpUrl\n  }, mapActions(['Logout'])), mapGetters(['nickname', 'avatar', 'userInfo'])), {}, {\n    getUserRole: function getUserRole() {\n      var userInfo = this.$store.getters.userInfo;\n      var userRoles = Vue.ls.get(USER_ROLE) || [];\n      if (!userRoles || userRoles.length === 0) {\n        if (!userInfo || !userInfo.userIdentity) return 'student';\n        switch (userInfo.userIdentity) {\n          case 1:\n            return 'student';\n          case 2:\n            return 'teacher';\n          case 3:\n            return 'admin';\n          default:\n            return 'student';\n        }\n      }\n      var _iterator = _createForOfIteratorHelper(userRoles),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var role = _step.value;\n          if (role.includes('admin')) return 'admin';\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      var _iterator2 = _createForOfIteratorHelper(userRoles),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var _role = _step2.value;\n          if (_role.includes('teacher')) return 'teacher';\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n      return 'student';\n    },\n    getRoleText: function getRoleText() {\n      var userInfo = this.$store.getters.userInfo;\n      var userRoles = Vue.ls.get(USER_ROLE) || [];\n      if (!userRoles || userRoles.length === 0) {\n        if (!userInfo || !userInfo.userIdentity) return '学生';\n        switch (userInfo.userIdentity) {\n          case 1:\n            return '学生';\n          case 2:\n            return '老师';\n          case 3:\n            return '管理员';\n          default:\n            return '学生';\n        }\n      }\n      var _iterator3 = _createForOfIteratorHelper(userRoles),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var role = _step3.value;\n          if (role.includes('admin')) return '管理员';\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      var _iterator4 = _createForOfIteratorHelper(userRoles),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var _role2 = _step4.value;\n          if (_role2.includes('teacher')) return '老师';\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n      return '学生';\n    },\n    enter: function enter(type) {\n      switch (type) {\n        case 0:\n          this.$router.push('/user/login');\n          break;\n        case 1:\n          this.$router.push('/account/center');\n          break;\n        case 2:\n          this.$router.push('/teaching/mineCourse/cardList');\n          break;\n        default:\n          this.$router.push('/account/settings/base');\n          break;\n      }\n    },\n    changeAccount: function changeAccount() {\n      var that = this;\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk: function onOk() {\n          return that.Logout({}).then(function () {\n            window.location.href = '/user/login';\n          }).catch(function (err) {\n            that.$message.error({\n              title: '错误',\n              description: err.message\n            });\n          });\n        },\n        onCancel: function onCancel() {}\n      });\n    },\n    toEditor: function toEditor(type) {\n      switch (type) {\n        case 1:\n          window.open('/scratch3/index.html?scene=create');\n          break;\n        case 2:\n          window.open('/scratchjr/home.html');\n          break;\n        case 3:\n          window.open('/python/index.html');\n          break;\n        case 4:\n          window.open('/cpp/index.html');\n          break;\n      }\n    },\n    _isMobile: function _isMobile() {\n      return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i) != null;\n    },\n    loadUserCoin: function loadUserCoin() {\n      var _this = this;\n      var h = this.$createElement;\n      getAction('/teaching/coin/getUserCoin').then(function (res) {\n        if (res.success) {\n          var newCoinCount = res.result || 0;\n          var oldCoinCount = _this.userCoin;\n\n          // 如果金币有增加，显示提示\n          if (newCoinCount > oldCoinCount && oldCoinCount !== 0) {\n            var increased = newCoinCount - oldCoinCount;\n            _this.$notification.success({\n              message: '金币增加提醒',\n              description: \"\\u606D\\u559C\\u60A8\\u83B7\\u5F97 \".concat(increased, \" \\u679A\\u91D1\\u5E01\\uFF01\"),\n              duration: 5,\n              placement: 'bottomRight',\n              icon: function icon(h) {\n                return h(\"a-icon\", {\n                  \"attrs\": {\n                    \"type\": \"trophy\"\n                  },\n                  \"style\": \"color: #FFD700\"\n                });\n              }\n            });\n          }\n          _this.userCoin = newCoinCount;\n        }\n      });\n    },\n    showDailyTasks: function showDailyTasks() {\n      this.dailyTasksVisible = true;\n      this.loadDailyTasks();\n    },\n    closeDailyTasks: function closeDailyTasks() {\n      this.dailyTasksVisible = false;\n    },\n    loadDailyTasks: function loadDailyTasks() {\n      var _this2 = this;\n      this.tasksLoading = true;\n      getAction('/teaching/dailyTask/getTasks').then(function (res) {\n        _this2.tasksLoading = false;\n        if (res.success) {\n          _this2.dailyTasks = res.result || [];\n        } else {\n          _this2.$message.error(res.message || '加载每日任务失败');\n        }\n      }).catch(function () {\n        _this2.tasksLoading = false;\n      });\n    },\n    doSignIn: function doSignIn() {\n      var _this3 = this;\n      var h = this.$createElement;\n      this.signInLoading = true;\n      postAction('/teaching/dailyTask/signIn').then(function (res) {\n        _this3.signInLoading = false;\n        if (res.success) {\n          // 添加签到成功动画和音效\n          _this3.showTaskCompletionEffect();\n          _this3.$message.success({\n            content: '签到成功! +' + (res.result || 10) + '金币',\n            icon: function icon(h) {\n              return h(\"a-icon\", {\n                \"attrs\": {\n                  \"type\": \"check-circle\"\n                },\n                \"style\": \"color: #52c41a\"\n              });\n            }\n          });\n\n          // 重新加载任务数据\n          _this3.loadDailyTasks();\n          _this3.loadUserCoin();\n        } else {\n          _this3.$message.error(res.message || '签到失败');\n        }\n      }).catch(function () {\n        _this3.signInLoading = false;\n      });\n    },\n    showTaskCompletionEffect: function showTaskCompletionEffect() {\n      // 创建并播放完成动画\n      try {\n        var audio = new Audio('/sound/success.mp3');\n        audio.volume = 0.5;\n        audio.play().catch(function (e) {\n          return console.log('播放音效失败:', e);\n        });\n      } catch (e) {\n        console.log('音效播放异常:', e);\n      }\n\n      // 创建动画元素\n      var container = document.querySelector('.task-modal');\n      if (!container) return;\n\n      // 创建金币动画\n      var _loop = function _loop() {\n        var coin = document.createElement('div');\n        coin.className = 'flying-coin';\n\n        // 随机位置和延迟\n        var left = Math.random() * 80 + 10; // 10% 到 90%\n        var delay = Math.random() * 0.5; // 0 到 0.5秒\n\n        coin.style.cssText = \"\\n          position: absolute;\\n          left: \".concat(left, \"%;\\n          top: 50%;\\n          width: 30px;\\n          height: 30px;\\n          background-image: url('@/assets/coin.png');\\n          background-size: contain;\\n          z-index: 9999;\\n          animation: fly-coin 1.5s ease-in forwards \").concat(delay, \"s;\\n        \");\n        container.appendChild(coin);\n\n        // 动画结束后移除元素\n        setTimeout(function () {\n          container.removeChild(coin);\n        }, (delay + 1.5) * 1000);\n      };\n      for (var i = 0; i < 10; i++) {\n        _loop();\n      }\n\n      // 创建星星动画\n      var _loop2 = function _loop2() {\n        var star = document.createElement('div');\n        star.className = 'flying-star';\n\n        // 随机位置、大小、颜色和延迟\n        var size = Math.random() * 10 + 5; // 5px 到 15px\n        var left = Math.random() * 80 + 10; // 10% 到 90%\n        var hue = Math.random() * 60 + 30; // 黄色到橙色\n        var delay = Math.random() * 0.5; // 0 到 0.5秒\n\n        star.style.cssText = \"\\n          position: absolute;\\n          left: \".concat(left, \"%;\\n          top: 50%;\\n          width: \").concat(size, \"px;\\n          height: \").concat(size, \"px;\\n          background-color: hsl(\").concat(hue, \", 100%, 50%);\\n          clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\\n          z-index: 9999;\\n          animation: fly-star 1s ease-out forwards \").concat(delay, \"s;\\n        \");\n        container.appendChild(star);\n\n        // 动画结束后移除元素\n        setTimeout(function () {\n          container.removeChild(star);\n        }, (delay + 1) * 1000);\n      };\n      for (var _i = 0; _i < 20; _i++) {\n        _loop2();\n      }\n    },\n    startCoinCheckTimer: function startCoinCheckTimer() {\n      var _this4 = this;\n      // 每60秒检查一次金币变化\n      this.coinCheckTimer = setInterval(function () {\n        _this4.updateUserCoin();\n      }, 60000); // 60秒\n    },\n    clearCoinCheckTimer: function clearCoinCheckTimer() {\n      if (this.coinCheckTimer) {\n        clearInterval(this.coinCheckTimer);\n        this.coinCheckTimer = null;\n      }\n    },\n    updateUserCoin: function updateUserCoin() {\n      this.loadUserCoin();\n    },\n    getCompletedTaskCount: function getCompletedTaskCount() {\n      return this.dailyTasks.filter(function (task) {\n        return task.completed;\n      }).length;\n    },\n    getCompletedPercent: function getCompletedPercent() {\n      return this.getCompletedTaskCount() / this.dailyTasks.length * 100;\n    },\n    getTaskIcon: function getTaskIcon(taskId) {\n      // 根据任务ID返回对应的图标\n      switch (taskId) {\n        case 1:\n          return 'calendar';\n        // 签到\n        case 2:\n          return 'comment';\n        // 评论\n        case 3:\n          return 'like';\n        // 点赞\n        default:\n          return 'star';\n      }\n    },\n    goToDoTask: function goToDoTask(taskId) {\n      // 根据任务ID跳转到对应的完成页面\n      this.closeDailyTasks(); // 先关闭任务弹窗\n\n      switch (taskId) {\n        case 2:\n          // 评论作品\n          this.$router.push('/workList');\n          break;\n        case 3:\n          // 点赞作品\n          this.$router.push('/workList');\n          break;\n        default:\n          this.$message.info('请完成相应任务获取金币奖励');\n      }\n    },\n    showCoinHistory: function showCoinHistory() {\n      this.coinHistoryVisible = true;\n      this.loadCoinHistory();\n    },\n    closeCoinHistory: function closeCoinHistory() {\n      this.coinHistoryVisible = false;\n    },\n    loadCoinHistory: function loadCoinHistory() {\n      var _this5 = this;\n      this.historyLoading = true;\n      getAction('/teaching/coin/getRecentCoinRecords').then(function (res) {\n        _this5.historyLoading = false;\n        if (res.success) {\n          _this5.coinRecords = res.result || [];\n\n          // 计算收入和支出总额\n          _this5.incomeTotal = _this5.coinRecords.filter(function (record) {\n            return record.operationType === 1;\n          }).reduce(function (sum, record) {\n            return sum + record.coinCount;\n          }, 0);\n          _this5.expenseTotal = _this5.coinRecords.filter(function (record) {\n            return record.operationType === 2;\n          }).reduce(function (sum, record) {\n            return sum + record.coinCount;\n          }, 0);\n          _this5.allLoaded = true; // 因为API已经返回所有记录，所以不需要加载更多\n        } else {\n          _this5.$message.error(res.message || '加载金币记录失败');\n        }\n      }).catch(function () {\n        _this5.historyLoading = false;\n      });\n    },\n    loadMoreHistory: function loadMoreHistory() {\n      this.loadCoinHistory();\n    },\n    formatDate: function formatDate(timestamp) {\n      if (!timestamp) return '';\n      var date = new Date(timestamp);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes);\n    }\n  })\n};", {"version": 3, "names": ["<PERSON><PERSON>", "getAction", "getFileAccessHttpUrl", "postAction", "ACCESS_TOKEN", "USER_ROLE", "mapActions", "mapGetters", "Header", "Banner", "Footer", "UserEnter", "SlidingPanel", "name", "components", "data", "brandName", "$store", "getters", "sysConfig", "logo", "logo2", "avatarUrl", "token", "userCoin", "dailyTasksVisible", "dailyTasks", "tasksLoading", "signInLoading", "coinCheckTimer", "coinHistoryVisible", "coinRecords", "historyLoading", "allLoaded", "incomeTotal", "expenseTotal", "created", "ls", "get", "qiniuDomain", "avatar", "loadUserCoin", "startCoinCheckTimer", "<PERSON><PERSON><PERSON><PERSON>", "clearCoinCheckTimer", "methods", "_objectSpread", "getUserRole", "userInfo", "userRoles", "length", "userIdentity", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "role", "value", "includes", "err", "e", "f", "_iterator2", "_step2", "getRoleText", "_iterator3", "_step3", "_iterator4", "_step4", "enter", "type", "$router", "push", "changeAccount", "that", "$confirm", "title", "content", "onOk", "Logout", "then", "window", "location", "href", "catch", "$message", "error", "description", "message", "onCancel", "toEditor", "open", "_isMobile", "navigator", "userAgent", "match", "_this", "h", "$createElement", "res", "success", "newCoinCount", "result", "oldCoin<PERSON>ount", "increased", "$notification", "concat", "duration", "placement", "icon", "showDailyTasks", "loadDailyTasks", "closeDailyTasks", "_this2", "doSignIn", "_this3", "showTaskCompletionEffect", "audio", "Audio", "volume", "play", "console", "log", "container", "document", "querySelector", "_loop", "coin", "createElement", "className", "left", "Math", "random", "delay", "style", "cssText", "append<PERSON><PERSON><PERSON>", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "i", "_loop2", "star", "size", "hue", "_this4", "setInterval", "updateUserCoin", "clearInterval", "getCompletedTaskCount", "filter", "task", "completed", "getCompletedPercent", "getTaskIcon", "taskId", "goToDoTask", "info", "showCoinHistory", "loadCoinHistory", "closeCoinHistory", "_this5", "record", "operationType", "reduce", "sum", "coinCount", "loadMoreHistory", "formatDate", "timestamp", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes"], "sources": ["src/views/home/<USER>"], "sourcesContent": ["<template>\n  <div\n    class=\"container\"\n    :style=\"{\n      backgroundColor: sysConfig.homeBgColor,\n      backgroundImage: sysConfig.file_homeBg ? 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')' : '',\n      backgroundRepeat: sysConfig.homeBgRepeat ? sysConfig.homeBgRepeat : '',\n    }\"\n  >\n    <a-layout>\n      <a-layout-header>\n        <Header />\n      </a-layout-header>\n      <a-layout>\n        <a-layout-content>\n          <a-row type=\"flex\" justify=\"space-between\">\n            <a-col :xs=\"24\" :sm=\"16\" :md=\"16\" :lg=\"16\" :xl=\"18\">\n              <Banner />\n            </a-col>\n            <a-col :xs=\"24\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"6\">\n              <div class=\"user-enter\">\n                <div v-if=\"token\">\n                  <div class=\"tech-card\">\n                    <div class=\"card-header\">\n                      <div class=\"avatar-container\">\n                        <a-avatar class=\"avatar\" :size=\"80\" :src=\"avatarUrl\" />\n                        <div class=\"avatar-glow\"></div>\n                      </div>\n                      <div class=\"user-role\" :class=\"getUserRole()\">\n                        {{ getRoleText() }}\n                      </div>\n                    </div>\n                    <div class=\"user-info\">\n                      <h2 class=\"username\">{{ nickname() }}</h2>\n                      <div class=\"welcome-text\">欢迎回来！</div>\n                      <div class=\"user-coin\">\n                        <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"coin-icon\">\n                        <span>{{ userCoin }} 金币</span>\n                      </div>\n                      <div class=\"daily-tasks\">\n                        <a @click=\"showDailyTasks\" class=\"daily-task-btn\">\n                          <span class=\"daily-task-text\">每日任务</span>\n                          <span class=\"daily-task-star\"></span>\n                        </a>\n                        <a @click=\"showCoinHistory\" class=\"coin-history-btn\">\n                          <span class=\"coin-history-text\">金币记录</span>\n                          <a-icon type=\"history\" />\n                        </a>\n                      </div>\n                    </div>\n                    <div class=\"card-actions\">\n                      <a-button type=\"primary\" class=\"btn-my-work action-btn\" @click=\"enter(1)\">\n                        <a-icon type=\"code\" />我的作品\n                      </a-button>\n                      <a-button type=\"primary\" class=\"btn-my-course action-btn\" @click=\"enter(2)\">\n                        <a-icon type=\"book\" />我的课程\n                      </a-button>\n                    </div>\n                  </div>\n                </div>\n                <div v-else>\n                  <div class=\"tech-card\">\n                    <div class=\"card-header\">\n                      <div class=\"avatar-container\">\n                        <a-avatar shape=\"square\" class=\"avatar\" :size=\"80\" :src=\"logo2\" />\n                        <div class=\"avatar-glow\"></div>\n                      </div>\n                    </div>\n                    <div class=\"user-info\">\n                      <h2 class=\"username welcome\">欢迎来到{{ brandName }}</h2>\n                    </div>\n                    <div class=\"card-actions\">\n                      <a-button type=\"primary\" class=\"action-btn login-btn\" @click=\"enter(0)\">\n                        <a-icon type=\"login\" />登录/注册\n                      </a-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </a-col>\n          </a-row>\n          <router-view />\n        </a-layout-content>\n      </a-layout>\n      <a-layout-footer>\n        <Footer />\n      </a-layout-footer>\n    </a-layout>\n    \n    <SlidingPanel />\n    \n    <a-modal\n      :title=\"null\"\n      :visible=\"dailyTasksVisible\"\n      :footer=\"null\"\n      @cancel=\"closeDailyTasks\"\n      :bodyStyle=\"{ padding: '0' }\"\n      width=\"450px\"\n      :maskStyle=\"{ backdropFilter: 'blur(5px)' }\"\n      :destroyOnClose=\"true\"\n      :closable=\"false\"\n      class=\"task-modal\"\n    >\n      <div class=\"task-modal-content\">\n        <!-- 头部设计 -->\n        <div class=\"task-header\">\n          <div class=\"task-header-bg\"></div>\n          <div class=\"task-title\">\n            <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"task-coin-icon\"/>\n            <span>每日任务</span>\n            <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"task-coin-icon\"/>\n          </div>\n          <div class=\"task-subtitle\">完成任务收集金币，解锁更多惊喜！</div>\n          <div class=\"close-btn\" @click=\"closeDailyTasks\">\n            <a-icon type=\"close-circle\" theme=\"filled\" />\n          </div>\n          <!-- 显示完成进度 -->\n          <div class=\"task-progress\">\n            <div class=\"progress-text\">今日进度：已完成 {{ getCompletedTaskCount() }}/{{ dailyTasks.length }}</div>\n            <a-progress \n              :percent=\"getCompletedPercent()\" \n              :strokeColor=\"{\n                '0%': '#FFB800',\n                '100%': '#FF6161',\n              }\"\n              :showInfo=\"false\"\n              strokeWidth={12}\n            />\n          </div>\n        </div>\n        \n        <a-spin :spinning=\"tasksLoading\">\n          <div class=\"daily-tasks-content\">\n            <div \n              class=\"task-item\" \n              v-for=\"(task, index) in dailyTasks\" \n              :key=\"index\"\n              :class=\"{ 'completed': task.completed }\"\n            >\n              <div class=\"task-icon\">\n                <a-icon :type=\"getTaskIcon(task.taskId)\" theme=\"filled\" />\n              </div>\n              <div class=\"task-info\">\n                <div class=\"task-name\">{{ task.taskName }}</div>\n                <div class=\"task-desc\">{{ task.taskDesc }}</div>\n              </div>\n              <div class=\"task-action\">\n                <div class=\"coin-reward\" :class=\"{ 'completed': task.completed }\">\n                  <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"coin-icon\"/>\n                  <span>{{ task.completed ? '已领取' : '+' + task.coinReward }}</span>\n                </div>\n                <template v-if=\"!task.completed && task.taskId === 1\">\n                  <a-button \n                    type=\"primary\" \n                    class=\"sign-btn\" \n                    @click=\"doSignIn\"\n                    :loading=\"signInLoading\"\n                  >\n                    去签到\n                  </a-button>\n                </template>\n                <template v-else-if=\"!task.completed\">\n                  <a-button \n                    type=\"primary\" \n                    class=\"do-task-btn\" \n                    @click=\"goToDoTask(task.taskId)\"\n                  >\n                    去完成\n                  </a-button>\n                </template>\n                <template v-else>\n                  <div class=\"completed-badge\">\n                    <a-icon type=\"check-circle\" theme=\"filled\" />\n                    <span>已完成</span>\n                  </div>\n                </template>\n              </div>\n            </div>\n          </div>\n        </a-spin>\n        \n        <!-- 底部装饰 -->\n        <div class=\"task-footer\">\n          <div class=\"footer-stars\">\n            <span class=\"star star-1\"></span>\n            <span class=\"star star-2\"></span>\n            <span class=\"star star-3\"></span>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 金币历史弹窗 -->\n    <a-modal\n      :title=\"null\"\n      :visible=\"coinHistoryVisible\"\n      :footer=\"null\"\n      @cancel=\"closeCoinHistory\"\n      :bodyStyle=\"{ padding: '0' }\"\n      width=\"550px\"\n      :maskStyle=\"{ backdropFilter: 'blur(5px)' }\"\n      :destroyOnClose=\"true\"\n      :closable=\"false\"\n      class=\"coin-history-modal\"\n    >\n      <div class=\"coin-history-content\">\n        <!-- 头部设计 -->\n        <div class=\"coin-history-header\">\n          <div class=\"coin-history-header-bg\"></div>\n          <div class=\"coin-history-title\">\n            <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"coin-icon\"/>\n            <span>金币记录</span>\n            <img src=\"@/assets/coin.png\" alt=\"金币\" class=\"coin-icon\"/>\n          </div>\n          <div class=\"coin-history-subtitle\">查看您最近一个月的金币收支记录</div>\n          <div class=\"close-btn\" @click=\"closeCoinHistory\">\n            <a-icon type=\"close-circle\" theme=\"filled\" />\n          </div>\n        </div>\n        \n        <a-spin :spinning=\"historyLoading\">\n          <div class=\"coin-records-content\">\n            <a-empty v-if=\"coinRecords.length === 0\" description=\"暂无金币记录\" />\n            <a-list \n              v-else\n              :dataSource=\"coinRecords\" \n              size=\"small\"\n              class=\"coin-list\"\n            >\n              <a-list-item slot=\"renderItem\" slot-scope=\"item\" class=\"coin-record-item\">\n                <div class=\"record-content\">\n                  <div class=\"record-icon\" :class=\"{'income': item.operationType === 1, 'expense': item.operationType === 2}\">\n                    <a-icon :type=\"item.operationType === 1 ? 'plus-circle' : 'minus-circle'\" theme=\"filled\" />\n                  </div>\n                  <div class=\"record-info\">\n                    <div class=\"record-description\">{{ item.description }}</div>\n                    <div class=\"record-time\">{{ formatDate(item.createTime) }}</div>\n                  </div>\n                  <div class=\"record-amount\" :class=\"{'income': item.operationType === 1, 'expense': item.operationType === 2}\">\n                    {{ item.operationType === 1 ? '+' : '-' }}{{ item.coinCount }}\n                  </div>\n                </div>\n              </a-list-item>\n            </a-list>\n            \n            <!-- 加载更多区域 -->\n            <div class=\"load-more\" v-if=\"coinRecords.length > 0\">\n              <a-button \n                v-if=\"!allLoaded\" \n                type=\"link\" \n                @click=\"loadMoreHistory\" \n                :disabled=\"historyLoading\"\n              >\n                {{ historyLoading ? '加载中...' : '查看更多' }}\n              </a-button>\n              <span v-else class=\"no-more\">没有更多记录了</span>\n            </div>\n          </div>\n        </a-spin>\n        \n        <!-- 底部小计 -->\n        <div class=\"coin-history-footer\">\n          <div class=\"coin-summary\">\n            <div class=\"summary-item income\">\n              <span class=\"label\">本月收入：</span>\n              <span class=\"value\">+{{ incomeTotal }}</span>\n            </div>    \n            <div class=\"summary-item expense\">\n              <span class=\"label\">本月支出：</span>\n              <span class=\"value\">-{{ expenseTotal }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue'\nimport { getAction, getFileAccessHttpUrl, postAction } from '@/api/manage'\nimport { ACCESS_TOKEN, USER_ROLE } from '@/store/mutation-types'\nimport { mapActions, mapGetters } from 'vuex'\nimport Header from './modules/Header'\nimport Banner from './modules/Banner'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\nimport SlidingPanel from './modules/SlidingPanel'\n\nexport default {\n  name: 'HomeLayout',\n  components: {\n    Header,\n    Footer,\n    UserEnter,\n    Banner,\n    SlidingPanel\n  },\n  data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      token: '',\n      sysConfig: {},\n      userCoin: 0,\n      dailyTasksVisible: false,\n      dailyTasks: [],\n      tasksLoading: false,\n      signInLoading: false,\n      coinCheckTimer: null,\n      coinHistoryVisible: false,\n      coinRecords: [],\n      historyLoading: false,\n      allLoaded: false,\n      incomeTotal: 0,\n      expenseTotal: 0\n    }\n  },\n  created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    this.sysConfig = this.$store.getters.sysConfig\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2\n      this.avatarUrl = this.logo\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n      this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if (this.getFileAccessHttpUrl(this.avatar())) {\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    }\n    if (this.token) {\n      this.loadUserCoin()\n      \n      // 启动定时检查金币变化\n      this.startCoinCheckTimer()\n    }\n  },\n  beforeDestroy() {\n    // 清除定时器\n    this.clearCoinCheckTimer()\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    ...mapActions(['Logout']),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getUserRole() {\n      const userInfo = this.$store.getters.userInfo\n      const userRoles = Vue.ls.get(USER_ROLE) || []\n      \n      if (!userRoles || userRoles.length === 0) {\n        if (!userInfo || !userInfo.userIdentity) return 'student'\n        \n        switch(userInfo.userIdentity) {\n          case 1: return 'student'\n          case 2: return 'teacher'\n          case 3: return 'admin'\n          default: return 'student'\n        }\n      }\n      \n      for (let role of userRoles) {\n        if (role.includes('admin')) return 'admin'\n      }\n      \n      for (let role of userRoles) {\n        if (role.includes('teacher')) return 'teacher'\n      }\n      \n      return 'student'\n    },\n    getRoleText() {\n      const userInfo = this.$store.getters.userInfo\n      const userRoles = Vue.ls.get(USER_ROLE) || []\n      \n      if (!userRoles || userRoles.length === 0) {\n        if (!userInfo || !userInfo.userIdentity) return '学生'\n        \n        switch(userInfo.userIdentity) {\n          case 1: return '学生'\n          case 2: return '老师'\n          case 3: return '管理员'\n          default: return '学生'\n        }\n      }\n      \n      for (let role of userRoles) {\n        if (role.includes('admin')) return '管理员'\n      }\n      \n      for (let role of userRoles) {\n        if (role.includes('teacher')) return '老师'\n      }\n      \n      return '学生'\n    },\n    enter(type) {\n      switch (type) {\n        case 0:\n          this.$router.push('/user/login')\n          break\n        case 1:\n          this.$router.push('/account/center')\n          break\n        case 2:\n          this.$router.push('/teaching/mineCourse/cardList')\n          break\n        default:\n          this.$router.push('/account/settings/base')\n          break\n      }\n    },\n    changeAccount() {\n      const that = this\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk() {\n          return that\n            .Logout({})\n            .then(() => {\n              window.location.href = '/user/login'\n            })\n            .catch((err) => {\n              that.$message.error({\n                title: '错误',\n                description: err.message,\n              })\n            })\n        },\n        onCancel() {},\n      })\n    },\n    toEditor(type) {\n      switch (type) {\n        case 1:\n          window.open('/scratch3/index.html?scene=create')\n          break\n        case 2:\n          window.open('/scratchjr/home.html')\n          break\n        case 3:\n          window.open('/python/index.html')\n          break\n        case 4:\n          window.open('/cpp/index.html')\n          break\n      }\n    },\n    _isMobile() {\n      return (\n        navigator.userAgent.match(\n          /(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n        ) != null\n      )\n    },\n    loadUserCoin() {\n      getAction('/teaching/coin/getUserCoin').then(res => {\n        if (res.success) {\n          const newCoinCount = res.result || 0\n          const oldCoinCount = this.userCoin\n          \n          // 如果金币有增加，显示提示\n          if (newCoinCount > oldCoinCount && oldCoinCount !== 0) {\n            const increased = newCoinCount - oldCoinCount\n            this.$notification.success({\n              message: '金币增加提醒',\n              description: `恭喜您获得 ${increased} 枚金币！`,\n              duration: 5,\n              placement: 'bottomRight',\n              icon: h => {\n                return <a-icon type=\"trophy\" style=\"color: #FFD700\" />\n              }\n            })\n          }\n          \n          this.userCoin = newCoinCount\n        }\n      })\n    },\n    showDailyTasks() {\n      this.dailyTasksVisible = true\n      this.loadDailyTasks()\n    },\n    closeDailyTasks() {\n      this.dailyTasksVisible = false\n    },\n    loadDailyTasks() {\n      this.tasksLoading = true\n      getAction('/teaching/dailyTask/getTasks').then(res => {\n        this.tasksLoading = false\n        if (res.success) {\n          this.dailyTasks = res.result || []\n        } else {\n          this.$message.error(res.message || '加载每日任务失败')\n        }\n      }).catch(() => {\n        this.tasksLoading = false\n      })\n    },\n    doSignIn() {\n      this.signInLoading = true\n      postAction('/teaching/dailyTask/signIn').then(res => {\n        this.signInLoading = false\n        if (res.success) {\n          // 添加签到成功动画和音效\n          this.showTaskCompletionEffect()\n          this.$message.success({\n            content: '签到成功! +' + (res.result || 10) + '金币',\n            icon: h => {\n              return <a-icon type=\"check-circle\" style=\"color: #52c41a\" />\n            }\n          })\n          \n          // 重新加载任务数据\n          this.loadDailyTasks()\n          this.loadUserCoin()\n        } else {\n          this.$message.error(res.message || '签到失败')\n        }\n      }).catch(() => {\n        this.signInLoading = false\n      })\n    },\n    showTaskCompletionEffect() {\n      // 创建并播放完成动画\n      try {\n        const audio = new Audio('/sound/success.mp3')\n        audio.volume = 0.5\n        audio.play().catch(e => console.log('播放音效失败:', e))\n      } catch (e) {\n        console.log('音效播放异常:', e)\n      }\n      \n      // 创建动画元素\n      const container = document.querySelector('.task-modal')\n      if (!container) return\n      \n      // 创建金币动画\n      for (let i = 0; i < 10; i++) {\n        const coin = document.createElement('div')\n        coin.className = 'flying-coin'\n        \n        // 随机位置和延迟\n        const left = Math.random() * 80 + 10 // 10% 到 90%\n        const delay = Math.random() * 0.5 // 0 到 0.5秒\n        \n        coin.style.cssText = `\n          position: absolute;\n          left: ${left}%;\n          top: 50%;\n          width: 30px;\n          height: 30px;\n          background-image: url('@/assets/coin.png');\n          background-size: contain;\n          z-index: 9999;\n          animation: fly-coin 1.5s ease-in forwards ${delay}s;\n        `\n        container.appendChild(coin)\n        \n        // 动画结束后移除元素\n        setTimeout(() => {\n          container.removeChild(coin)\n        }, (delay + 1.5) * 1000)\n      }\n      \n      // 创建星星动画\n      for (let i = 0; i < 20; i++) {\n        const star = document.createElement('div')\n        star.className = 'flying-star'\n        \n        // 随机位置、大小、颜色和延迟\n        const size = Math.random() * 10 + 5 // 5px 到 15px\n        const left = Math.random() * 80 + 10 // 10% 到 90%\n        const hue = Math.random() * 60 + 30 // 黄色到橙色\n        const delay = Math.random() * 0.5 // 0 到 0.5秒\n        \n        star.style.cssText = `\n          position: absolute;\n          left: ${left}%;\n          top: 50%;\n          width: ${size}px;\n          height: ${size}px;\n          background-color: hsl(${hue}, 100%, 50%);\n          clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n          z-index: 9999;\n          animation: fly-star 1s ease-out forwards ${delay}s;\n        `\n        container.appendChild(star)\n        \n        // 动画结束后移除元素\n        setTimeout(() => {\n          container.removeChild(star)\n        }, (delay + 1) * 1000)\n      }\n    },\n    startCoinCheckTimer() {\n      // 每60秒检查一次金币变化\n      this.coinCheckTimer = setInterval(() => {\n        this.updateUserCoin()\n      }, 60000) // 60秒\n    },\n    clearCoinCheckTimer() {\n      if (this.coinCheckTimer) {\n        clearInterval(this.coinCheckTimer)\n        this.coinCheckTimer = null\n      }\n    },\n    updateUserCoin() {\n      this.loadUserCoin()\n    },\n    getCompletedTaskCount() {\n      return this.dailyTasks.filter(task => task.completed).length\n    },\n    getCompletedPercent() {\n      return (this.getCompletedTaskCount() / this.dailyTasks.length) * 100\n    },\n    getTaskIcon(taskId) {\n      // 根据任务ID返回对应的图标\n      switch(taskId) {\n        case 1: return 'calendar'; // 签到\n        case 2: return 'comment'; // 评论\n        case 3: return 'like'; // 点赞\n        default: return 'star';\n      }\n    },\n    goToDoTask(taskId) {\n      // 根据任务ID跳转到对应的完成页面\n      this.closeDailyTasks(); // 先关闭任务弹窗\n      \n      switch(taskId) {\n        case 2: // 评论作品\n          this.$router.push('/workList');\n          break;\n        case 3: // 点赞作品\n          this.$router.push('/workList');\n          break;\n        default:\n          this.$message.info('请完成相应任务获取金币奖励');\n      }\n    },\n    showCoinHistory() {\n      this.coinHistoryVisible = true\n      this.loadCoinHistory()\n    },\n    closeCoinHistory() {\n      this.coinHistoryVisible = false\n    },\n    loadCoinHistory() {\n      this.historyLoading = true\n      getAction('/teaching/coin/getRecentCoinRecords').then(res => {\n        this.historyLoading = false\n        if (res.success) {\n          this.coinRecords = res.result || []\n          \n          // 计算收入和支出总额\n          this.incomeTotal = this.coinRecords\n            .filter(record => record.operationType === 1)\n            .reduce((sum, record) => sum + record.coinCount, 0);\n          \n          this.expenseTotal = this.coinRecords\n            .filter(record => record.operationType === 2)\n            .reduce((sum, record) => sum + record.coinCount, 0);\n          \n          this.allLoaded = true; // 因为API已经返回所有记录，所以不需要加载更多\n        } else {\n          this.$message.error(res.message || '加载金币记录失败')\n        }\n      }).catch(() => {\n        this.historyLoading = false\n      })\n    },\n    loadMoreHistory() {\n      this.loadCoinHistory()\n    },\n    formatDate(timestamp) {\n      if (!timestamp) return ''\n      const date = new Date(timestamp)\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}`\n    }\n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n.container {\n  background: url(/img/bg_blue.png) no-repeat;\n  background-color: #f6f6f6;\n  background-size: 100% auto;\n  overflow-x: hidden;\n}\n.ant-layout-header,\n.ant-layout-content,\n.ant-layout-sider,\n.ant-layout-sider-children {\n  background: transparent;\n}\n.ant-layout-footer {\n  background: transparent;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n}\n.ant-layout {\n  background: transparent;\n  min-height: calc(100vh - 200px);\n}\n.ant-layout-header {\n  height: auto;\n  min-height: 50px;\n  width: 100%;\n  margin-bottom: 10px;\n  padding: 0;\n  /deep/.banner {\n    border-radius: 10px;\n    overflow: hidden;\n  }\n}\n\n.ant-layout-has-sider {\n  max-width: 1600px;\n  min-width: 800px;\n  margin: -100px auto 0;\n}\n.ant-layout-sider {\n  z-index: 99;\n}\n.ant-layout-content {\n  padding: 20px 20px 0 20px;\n  max-width: 1300px;\n  width: 100%;\n  margin: 0 auto;\n  .user-enter {\n    background: #fff;\n    border: 1px solid #eee;\n    border-radius: 20px;\n    width: 300px;\n    min-height: 300px;\n    text-align: center;\n    line-height: 1.5;\n    float: right;\n    padding: 15px;\n    margin-top: -40px;\n    position: relative;\n    z-index: 10;\n    \n    .tech-card {\n      position: relative;\n      background: linear-gradient(135deg, #3a5cb6, #5281d4);\n      border-radius: 15px;\n      padding: 20px;\n      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);\n      overflow: hidden;\n      \n      &::before {\n        content: '';\n        position: absolute;\n        top: -50%;\n        left: -50%;\n        width: 200%;\n        height: 200%;\n        background: linear-gradient(\n          to right, \n          rgba(255, 255, 255, 0.2) 0%,\n          rgba(255, 255, 255, 0.1) 100%\n        );\n        transform: rotate(30deg);\n        pointer-events: none;\n  }\n      \n      .card-header {\n        position: relative;\n        display: flex;\n        justify-content: center;\n        margin-bottom: 10px;\n        \n        .avatar-container {\n          position: relative;\n          \n          .avatar {\n            width: 90px;\n            height: 90px;\n            border: 3px solid rgba(255, 255, 255, 0.8);\n            box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);\n          }\n          \n          .avatar-glow {\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            width: 90px;\n            height: 90px;\n            border-radius: 50%;\n            background: radial-gradient(\n              circle,\n              rgba(255, 255, 255, 0.4) 0%,\n              rgba(255, 255, 255, 0) 70%\n            );\n            z-index: -1;\n            animation: pulse 2s infinite;\n          }\n        }\n        \n        .user-role {\n          position: absolute;\n          top: 0;\n          right: 0;\n          background: rgba(255, 255, 255, 0.9);\n          color: #000;\n          padding: 3px 8px;\n          border-radius: 10px;\n          font-size: 12px;\n          font-weight: bold;\n          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n          \n          &.student {\n            background: linear-gradient(45deg, #43a047, #66bb6a);\n            color: white;\n          }\n          \n          &.teacher {\n            background: linear-gradient(45deg, #1565c0, #42a5f5);\n            color: white;\n          }\n          \n          &.admin {\n            background: linear-gradient(45deg, #c62828, #ef5350);\n            color: white;\n          }\n        }\n      }\n      \n      .user-info {\n        padding: 10px 0;\n        \n        .username {\n          font-size: 22px;\n          color: #fff;\n          margin-bottom: 5px;\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n          font-weight: bold;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .welcome-text {\n          color: rgba(255, 255, 255, 0.9);\n          font-size: 15px;\n          margin-bottom: 15px;\n        }\n        \n        .user-coin {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #FFD700;\n          font-weight: 600;\n          font-size: 16px;\n          margin-bottom: 5px;\n          \n          .coin-icon {\n            width: 24px;\n            height: 24px;\n            margin-right: 5px;\n          }\n        }\n        \n        .daily-tasks {\n          margin-top: 8px;\n          text-align: center;\n          display: flex;\n          justify-content: center;\n          gap: 15px;\n        }\n      }\n      \n      .card-actions {\n        display: flex;\n        flex-direction: column;\n        gap: 12px;\n        \n        .action-btn {\n          border-radius: 10px;\n          height: 40px;\n          font-size: 15px;\n          background: rgba(255, 255, 255, 0.25);\n          border: 1px solid rgba(255, 255, 255, 0.4);\n          color: white;\n          transition: all 0.3s ease;\n          \n          &:hover {\n            background: rgba(255, 255, 255, 0.35);\n            transform: translateY(-2px);\n            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);\n          }\n          \n          .anticon {\n            margin-right: 8px;\n          }\n        }\n        \n        .login-btn {\n          background: linear-gradient(45deg, #ff9800, #ff5722);\n          border: none;\n          \n          &:hover {\n            background: linear-gradient(45deg, #ff5722, #ff9800);\n          }\n        }\n      }\n    }\n  }\n}\n\n.daily-tasks-content {\n  max-height: 300px;\n  overflow-y: auto;\n  \n  .task-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 10px 0;\n    border-bottom: 1px solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n  \n  .task-info {\n    .task-name {\n      font-weight: 500;\n      margin-bottom: 4px;\n    }\n    \n    .task-desc {\n      font-size: 12px;\n      color: #999;\n    }\n  }\n  \n  .task-action {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 8px;\n  }\n}\n\n@keyframes pulse {\n  0% {\n    transform: translate(-50%, -50%) scale(0.8);\n    opacity: 0.7;\n  }\n  50% {\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: translate(-50%, -50%) scale(0.8);\n    opacity: 0.7;\n  }\n}\n\n.ant-layout-sider {\n  margin-left: 30px;\n  max-width: 300px !important;\n  width: 300px !important;\n}\n\n.task-modal {\n  /deep/ .ant-modal-content {\n    border-radius: 15px;\n    overflow: hidden;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  }\n  /deep/ .ant-modal-body {\n    padding: 0;\n  }\n\n  .task-modal-content {\n    .task-header {\n      position: relative;\n      padding: 25px 20px 45px;\n      background: linear-gradient(135deg, #42a5f5, #6abaf0);\n      border-top-left-radius: 10px;\n      border-top-right-radius: 10px;\n      overflow: hidden;\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n        opacity: 0.7;\n      }\n      \n      .task-header-bg {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: linear-gradient(\n          to right, \n          rgba(255, 255, 255, 0.2) 0%,\n          rgba(255, 255, 255, 0.1) 100%\n        );\n        transform: rotate(30deg);\n        pointer-events: none;\n      }\n      \n      .task-title {\n        position: relative;\n        z-index: 1;\n        font-size: 24px;\n        font-weight: bold;\n        color: #fff;\n        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        margin-bottom: 10px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        .task-coin-icon {\n          width: 28px;\n          height: 28px;\n          margin: 0 10px;\n          animation: float 2s infinite ease-in-out alternate;\n        }\n      }\n      \n      .task-subtitle {\n        position: relative;\n        z-index: 1;\n        font-size: 15px;\n        color: rgba(255, 255, 255, 0.9);\n        margin-bottom: 20px;\n        text-align: center;\n      }\n      \n      .close-btn {\n        position: absolute;\n        top: 15px;\n        right: 15px;\n        z-index: 1;\n        cursor: pointer;\n        color: #fff;\n        font-size: 20px;\n        transition: all 0.3s;\n        \n        &:hover {\n          transform: scale(1.2);\n        }\n      }\n      \n      .task-progress {\n        position: relative;\n        z-index: 1;\n        padding: 0;\n        margin-top: 15px;\n        \n        .progress-text {\n          font-size: 14px;\n          color: #fff;\n          margin-bottom: 8px;\n          text-align: center;\n        }\n      }\n    }\n    \n    .daily-tasks-content {\n      padding: 20px;\n      background: #fff;\n      max-height: 300px;\n      overflow-y: auto;\n      \n      .task-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 15px;\n        margin-bottom: 12px;\n        border-radius: 12px;\n        background: #f8f9fa;\n        border: 1px solid #eaedf2;\n        transition: all 0.3s;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        &:hover {\n          transform: translateY(-3px);\n          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n        }\n        \n        &.completed {\n          background: #e8f5e9;\n          border-color: #c8e6c9;\n        }\n      }\n      \n      .task-icon {\n        width: 44px;\n        height: 44px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 15px;\n        font-size: 22px;\n        color: white;\n        \n        .anticon {\n          background: linear-gradient(135deg, #66bb6a, #81c784);\n          box-shadow: 0 4px 8px rgba(102, 187, 106, 0.3);\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n        }\n        \n        .anticon-calendar {\n          background: linear-gradient(135deg, #ffb74d, #ffa726);\n          box-shadow: 0 4px 8px rgba(255, 167, 38, 0.3);\n        }\n        \n        .anticon-like {\n          background: linear-gradient(135deg, #f48fb1, #ec407a);\n          box-shadow: 0 4px 8px rgba(236, 64, 122, 0.3);\n        }\n        \n        .anticon-comment {\n          background: linear-gradient(135deg, #4fc3f7, #29b6f6);\n          box-shadow: 0 4px 8px rgba(41, 182, 246, 0.3);\n        }\n      }\n      \n      .task-info {\n        flex: 1;\n        \n        .task-name {\n          font-weight: 600;\n          font-size: 16px;\n          color: #333;\n          margin-bottom: 5px;\n        }\n        \n        .task-desc {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n      \n      .task-action {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        gap: 8px;\n        min-width: 80px;\n      }\n      \n      .coin-reward {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: rgba(255, 215, 0, 0.1);\n        padding: 4px 10px;\n        border-radius: 20px;\n        color: #ff9800;\n        font-weight: 600;\n        font-size: 14px;\n        \n        &.completed {\n          color: #66bb6a;\n          background: rgba(102, 187, 106, 0.1);\n        }\n        \n        .coin-icon {\n          width: 20px;\n          height: 20px;\n          margin-right: 5px;\n          animation: spin 5s linear infinite;\n        }\n      }\n      \n      .sign-btn,\n      .do-task-btn {\n        border-radius: 20px;\n        height: 32px;\n        padding: 0 15px;\n        background: rgba(255, 255, 255, 0.25);\n        border: none;\n        color: white;\n        font-weight: 600;\n        font-size: 13px;\n        transition: all 0.3s ease;\n        \n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);\n        }\n      }\n      \n      .sign-btn {\n        background: linear-gradient(45deg, #ff9800, #ff5722);\n        \n        &:hover {\n          background: linear-gradient(45deg, #ff5722, #ff9800);\n        }\n      }\n      \n      .do-task-btn {\n        background: linear-gradient(45deg, #42a5f5, #64b5f6);\n        \n        &:hover {\n          background: linear-gradient(45deg, #64b5f6, #42a5f5);\n        }\n      }\n      \n      .completed-badge {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #66bb6a;\n        color: white;\n        border-radius: 20px;\n        padding: 3px 10px;\n        font-size: 13px;\n        font-weight: 500;\n        \n        .anticon {\n          margin-right: 5px;\n        }\n      }\n    }\n    \n    .task-footer {\n      position: relative;\n      padding: 15px;\n      background: linear-gradient(135deg, #42a5f5, #6abaf0);\n      overflow: hidden;\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n        opacity: 0.7;\n      }\n      \n      .footer-stars {\n        position: relative;\n        z-index: 1;\n        display: flex;\n        justify-content: space-around;\n        align-items: center;\n        \n        .star {\n          width: 24px;\n          height: 24px;\n          background: rgba(255, 255, 255, 0.9);\n          clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n          \n          &.star-1 {\n            animation: twinkle 2s infinite ease-in-out alternate;\n          }\n          \n          &.star-2 {\n            animation: twinkle 3s infinite ease-in-out alternate;\n          }\n          \n          &.star-3 {\n            animation: twinkle 2.5s infinite ease-in-out alternate;\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes float {\n  0% {\n    transform: translateY(0);\n  }\n  100% {\n    transform: translateY(-5px);\n  }\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes twinkle {\n  0% {\n    opacity: 0.5;\n    transform: scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n}\n\n@keyframes fly-coin {\n  0% {\n    transform: translate(0, 0) rotate(0deg);\n    opacity: 1;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: translate(0, -200px) rotate(360deg);\n    opacity: 0;\n  }\n}\n\n@keyframes fly-star {\n  0% {\n    transform: translate(0, 0) scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: translate(\n      calc(100px - 200px * var(--random-x, 0.5)), \n      calc(-100px * var(--random-y, 0.5))\n    ) scale(0);\n    opacity: 0;\n  }\n}\n\n/* 为每日任务按钮添加样式 */\n.daily-tasks {\n  margin-top: 8px;\n  text-align: center;\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n}\n\n.daily-task-btn, .coin-history-btn {\n  display: inline-block;\n  position: relative;\n  padding: 7px 15px;\n  background: linear-gradient(to right, #FFB800, #FF9500);\n  border-radius: 20px;\n  box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);\n  transition: all 0.3s;\n  overflow: hidden;\n}\n\n.coin-history-btn {\n  background: linear-gradient(to right, #3a8ee6, #41b6ff);\n  box-shadow: 0 4px 8px rgba(65, 182, 255, 0.3);\n}\n\n.daily-task-btn:hover, .coin-history-btn:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 12px rgba(255, 152, 0, 0.4);\n}\n\n.coin-history-btn:hover {\n  box-shadow: 0 6px 12px rgba(65, 182, 255, 0.4);\n}\n\n.daily-task-btn:active, .coin-history-btn:active {\n  transform: translateY(0);\n}\n\n.daily-task-text, .coin-history-text {\n  position: relative;\n  z-index: 2;\n  color: #fff;\n  font-weight: bold;\n  font-size: 15px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n  letter-spacing: 1px;\n  font-family: \"微软雅黑\", \"Arial Rounded MT Bold\", Arial, sans-serif;\n  margin-right: 5px;\n}\n\n.daily-task-btn::before, .coin-history-btn::before {\n  content: \"\";\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);\n  transform: rotate(45deg);\n  animation: shine 3s infinite;\n}\n\n@keyframes shine {\n  0% {\n    transform: scale(0.5) rotate(0deg);\n    opacity: 0.3;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    transform: scale(1) rotate(360deg);\n    opacity: 0.3;\n  }\n}\n\n.daily-task-star {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #fff;\n  border-radius: 50%;\n  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);\n  top: 5px;\n  right: 10px;\n  animation: twinkle 2s infinite;\n}\n\n@keyframes twinkle {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: scale(0.8);\n  }\n}\n\n.coin-history-modal {\n  /deep/ .ant-modal-content {\n    border-radius: 15px;\n    overflow: hidden;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  }\n  /deep/ .ant-modal-body {\n    padding: 0;\n  }\n\n  .coin-history-content {\n    .coin-history-header {\n      position: relative;\n      padding: 25px 20px 45px;\n      background: linear-gradient(135deg, #42a5f5, #6abaf0);\n      border-top-left-radius: 10px;\n      border-top-right-radius: 10px;\n      overflow: hidden;\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n        opacity: 0.7;\n      }\n      \n      .coin-history-header-bg {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: linear-gradient(\n          to right, \n          rgba(255, 255, 255, 0.2) 0%,\n          rgba(255, 255, 255, 0.1) 100%\n        );\n        transform: rotate(30deg);\n        pointer-events: none;\n      }\n      \n      .coin-history-title {\n        position: relative;\n        z-index: 1;\n        font-size: 24px;\n        font-weight: bold;\n        color: #fff;\n        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        margin-bottom: 10px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        .coin-icon {\n          width: 28px;\n          height: 28px;\n          margin: 0 10px;\n          animation: float 2s infinite ease-in-out alternate;\n        }\n      }\n      \n      .coin-history-subtitle {\n        position: relative;\n        z-index: 1;\n        font-size: 15px;\n        color: rgba(255, 255, 255, 0.9);\n        margin-bottom: 20px;\n        text-align: center;\n      }\n      \n      .close-btn {\n        position: absolute;\n        top: 15px;\n        right: 15px;\n        z-index: 1;\n        cursor: pointer;\n        color: #fff;\n        font-size: 20px;\n        transition: all 0.3s;\n        \n        &:hover {\n          transform: scale(1.2);\n        }\n      }\n    }\n    \n    .coin-records-content {\n      padding: 20px;\n      background: #fff;\n      max-height: 300px;\n      overflow-y: auto;\n      \n      .coin-record-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 15px;\n        margin-bottom: 12px;\n        border-radius: 12px;\n        background: #f8f9fa;\n        border: 1px solid #eaedf2;\n        transition: all 0.3s;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        &:hover {\n          transform: translateY(-3px);\n          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n        }\n        \n        .record-content {\n          display: flex;\n          align-items: center;\n          gap: 10px;\n          \n          .record-icon {\n            width: 44px;\n            height: 44px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 22px;\n            color: white;\n            \n            .anticon {\n              background: linear-gradient(135deg, #66bb6a, #81c784);\n              box-shadow: 0 4px 8px rgba(102, 187, 106, 0.3);\n              width: 100%;\n              height: 100%;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              border-radius: 50%;\n            }\n            \n            .anticon-plus-circle {\n              background: linear-gradient(135deg, #4caf50, #45a049);\n              box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);\n            }\n            \n            .anticon-minus-circle {\n              background: linear-gradient(135deg, #f44336, #e53935);\n              box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);\n            }\n          }\n          \n          .record-info {\n            flex: 1;\n            \n            .record-description {\n              font-weight: 600;\n              font-size: 16px;\n              color: #333;\n              margin-bottom: 5px;\n            }\n            \n            .record-time {\n              font-size: 13px;\n              color: #666;\n            }\n          }\n        }\n        \n        .record-amount {\n          font-weight: 600;\n          font-size: 16px;\n          color: #333;\n          \n          &.income {\n            color: #4caf50;\n          }\n          \n          &.expense {\n            color: #f44336;\n          }\n        }\n      }\n    }\n    \n    .coin-history-footer {\n      position: relative;\n      padding: 15px;\n      background: linear-gradient(135deg, #42a5f5, #6abaf0);\n      overflow: hidden;\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n        opacity: 0.7;\n      }\n      \n      .coin-summary {\n        position: relative;\n        z-index: 1;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        \n        .summary-item {\n          .label {\n            font-size: 14px;\n            color: rgba(255, 255, 255, 0.9);\n          }\n          \n          .value {\n            font-size: 16px;\n            font-weight: bold;\n            color: #fff;\n          }\n        }\n      }\n    }\n  }\n}\n</style>"], "mappings": ";;;;;;;;;AAuRA,OAAAA,GAAA;AACA,SAAAC,SAAA,EAAAC,oBAAA,EAAAC,UAAA;AACA,SAAAC,YAAA,EAAAC,SAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,SAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,MAAA,EAAAA,MAAA;IACAE,MAAA,EAAAA,MAAA;IACAC,SAAA,EAAAA,SAAA;IACAF,MAAA,EAAAA,MAAA;IACAG,YAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAH,SAAA;MACAI,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,KAAA;MACAJ,SAAA;MACAK,QAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,cAAA;MACAC,SAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAb,KAAA,GAAAvB,GAAA,CAAAqC,EAAA,CAAAC,GAAA,CAAAlC,YAAA;IACA,KAAAe,SAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,SAAA;IACA,SAAAF,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA;MACA,KAAAnB,IAAA,QAAAH,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA,cAAAtB,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAC,IAAA;IACA;IACA,SAAAH,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAE,KAAA,SAAAJ,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA;MACA,KAAAlB,KAAA,QAAAJ,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA,cAAAtB,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAE,KAAA;MACA,KAAAC,SAAA,QAAAF,IAAA;IACA;IACA,SAAAH,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAqB,MAAA,SAAAvB,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA;MACA,KAAAjB,SAAA,QAAAL,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAoB,WAAA,cAAAtB,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAqB,MAAA;IACA;IACA,SAAAtC,oBAAA,MAAAsC,MAAA;MACA,KAAAlB,SAAA,QAAApB,oBAAA,MAAAsC,MAAA;IACA;IACA,SAAAjB,KAAA;MACA,KAAAkB,YAAA;;MAEA;MACA,KAAAC,mBAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA;IACA5C,oBAAA,EAAAA;EAAA,GACAI,UAAA,eACAC,UAAA;IACAwC,WAAA,WAAAA,YAAA;MACA,IAAAC,QAAA,QAAA/B,MAAA,CAAAC,OAAA,CAAA8B,QAAA;MACA,IAAAC,SAAA,GAAAjD,GAAA,CAAAqC,EAAA,CAAAC,GAAA,CAAAjC,SAAA;MAEA,KAAA4C,SAAA,IAAAA,SAAA,CAAAC,MAAA;QACA,KAAAF,QAAA,KAAAA,QAAA,CAAAG,YAAA;QAEA,QAAAH,QAAA,CAAAG,YAAA;UACA;YAAA;UACA;YAAA;UACA;YAAA;UACA;YAAA;QACA;MACA;MAAA,IAAAC,SAAA,GAAAC,0BAAA,CAEAJ,SAAA;QAAAK,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAD,IAAA,CAAAE,QAAA;QACA;MAAA,SAAAC,GAAA;QAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;MAAA;QAAAT,SAAA,CAAAW,CAAA;MAAA;MAAA,IAAAC,UAAA,GAAAX,0BAAA,CAEAJ,SAAA;QAAAgB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAR,CAAA,IAAAC,IAAA;UAAA,IAAAC,KAAA,GAAAO,MAAA,CAAAN,KAAA;UACA,IAAAD,KAAA,CAAAE,QAAA;QACA;MAAA,SAAAC,GAAA;QAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;MAAA;QAAAG,UAAA,CAAAD,CAAA;MAAA;MAEA;IACA;IACAG,WAAA,WAAAA,YAAA;MACA,IAAAlB,QAAA,QAAA/B,MAAA,CAAAC,OAAA,CAAA8B,QAAA;MACA,IAAAC,SAAA,GAAAjD,GAAA,CAAAqC,EAAA,CAAAC,GAAA,CAAAjC,SAAA;MAEA,KAAA4C,SAAA,IAAAA,SAAA,CAAAC,MAAA;QACA,KAAAF,QAAA,KAAAA,QAAA,CAAAG,YAAA;QAEA,QAAAH,QAAA,CAAAG,YAAA;UACA;YAAA;UACA;YAAA;UACA;YAAA;UACA;YAAA;QACA;MACA;MAAA,IAAAgB,UAAA,GAAAd,0BAAA,CAEAJ,SAAA;QAAAmB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAZ,CAAA,MAAAa,MAAA,GAAAD,UAAA,CAAAX,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAU,MAAA,CAAAT,KAAA;UACA,IAAAD,IAAA,CAAAE,QAAA;QACA;MAAA,SAAAC,GAAA;QAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;MAAA;QAAAM,UAAA,CAAAJ,CAAA;MAAA;MAAA,IAAAM,UAAA,GAAAhB,0BAAA,CAEAJ,SAAA;QAAAqB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAd,CAAA,MAAAe,MAAA,GAAAD,UAAA,CAAAb,CAAA,IAAAC,IAAA;UAAA,IAAAC,MAAA,GAAAY,MAAA,CAAAX,KAAA;UACA,IAAAD,MAAA,CAAAE,QAAA;QACA;MAAA,SAAAC,GAAA;QAAAQ,UAAA,CAAAP,CAAA,CAAAD,GAAA;MAAA;QAAAQ,UAAA,CAAAN,CAAA;MAAA;MAEA;IACA;IACAQ,KAAA,WAAAA,MAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAC,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,IAAA;MACA,KAAAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA,WAAAA,KAAA;UACA,OAAAJ,IAAA,CACAK,MAAA,KACAC,IAAA;YACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;UACA,GACAC,KAAA,WAAAzB,GAAA;YACAe,IAAA,CAAAW,QAAA,CAAAC,KAAA;cACAV,KAAA;cACAW,WAAA,EAAA5B,GAAA,CAAA6B;YACA;UACA;QACA;QACAC,QAAA,WAAAA,SAAA;MACA;IACA;IACAC,QAAA,WAAAA,SAAApB,IAAA;MACA,QAAAA,IAAA;QACA;UACAW,MAAA,CAAAU,IAAA;UACA;QACA;UACAV,MAAA,CAAAU,IAAA;UACA;QACA;UACAV,MAAA,CAAAU,IAAA;UACA;QACA;UACAV,MAAA,CAAAU,IAAA;UACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OACAC,SAAA,CAAAC,SAAA,CAAAC,KAAA,CACA,4IACA;IAEA;IACAxD,YAAA,WAAAA,aAAA;MAAA,IAAAyD,KAAA;MAAA,IAAAC,CAAA,QAAAC,cAAA;MACAnG,SAAA,+BAAAiF,IAAA,WAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA,IAAAC,YAAA,GAAAF,GAAA,CAAAG,MAAA;UACA,IAAAC,YAAA,GAAAP,KAAA,CAAA1E,QAAA;;UAEA;UACA,IAAA+E,YAAA,GAAAE,YAAA,IAAAA,YAAA;YACA,IAAAC,SAAA,GAAAH,YAAA,GAAAE,YAAA;YACAP,KAAA,CAAAS,aAAA,CAAAL,OAAA;cACAZ,OAAA;cACAD,WAAA,oCAAAmB,MAAA,CAAAF,SAAA;cACAG,QAAA;cACAC,SAAA;cACAC,IAAA,WAAAA,KAAAZ,CAAA;gBACA,OAAAA,CAAA;kBAAA;oBAAA;kBAAA;kBAAA;gBAAA;cACA;YACA;UACA;UAEAD,KAAA,CAAA1E,QAAA,GAAA+E,YAAA;QACA;MACA;IACA;IACAS,cAAA,WAAAA,eAAA;MACA,KAAAvF,iBAAA;MACA,KAAAwF,cAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAzF,iBAAA;IACA;IACAwF,cAAA,WAAAA,eAAA;MAAA,IAAAE,MAAA;MACA,KAAAxF,YAAA;MACA1B,SAAA,iCAAAiF,IAAA,WAAAmB,GAAA;QACAc,MAAA,CAAAxF,YAAA;QACA,IAAA0E,GAAA,CAAAC,OAAA;UACAa,MAAA,CAAAzF,UAAA,GAAA2E,GAAA,CAAAG,MAAA;QACA;UACAW,MAAA,CAAA5B,QAAA,CAAAC,KAAA,CAAAa,GAAA,CAAAX,OAAA;QACA;MACA,GAAAJ,KAAA;QACA6B,MAAA,CAAAxF,YAAA;MACA;IACA;IACAyF,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,IAAAlB,CAAA,QAAAC,cAAA;MACA,KAAAxE,aAAA;MACAzB,UAAA,+BAAA+E,IAAA,WAAAmB,GAAA;QACAgB,MAAA,CAAAzF,aAAA;QACA,IAAAyE,GAAA,CAAAC,OAAA;UACA;UACAe,MAAA,CAAAC,wBAAA;UACAD,MAAA,CAAA9B,QAAA,CAAAe,OAAA;YACAvB,OAAA,eAAAsB,GAAA,CAAAG,MAAA;YACAO,IAAA,WAAAA,KAAAZ,CAAA;cACA,OAAAA,CAAA;gBAAA;kBAAA;gBAAA;gBAAA;cAAA;YACA;UACA;;UAEA;UACAkB,MAAA,CAAAJ,cAAA;UACAI,MAAA,CAAA5E,YAAA;QACA;UACA4E,MAAA,CAAA9B,QAAA,CAAAC,KAAA,CAAAa,GAAA,CAAAX,OAAA;QACA;MACA,GAAAJ,KAAA;QACA+B,MAAA,CAAAzF,aAAA;MACA;IACA;IACA0F,wBAAA,WAAAA,yBAAA;MACA;MACA;QACA,IAAAC,KAAA,OAAAC,KAAA;QACAD,KAAA,CAAAE,MAAA;QACAF,KAAA,CAAAG,IAAA,GAAApC,KAAA,WAAAxB,CAAA;UAAA,OAAA6D,OAAA,CAAAC,GAAA,YAAA9D,CAAA;QAAA;MACA,SAAAA,CAAA;QACA6D,OAAA,CAAAC,GAAA,YAAA9D,CAAA;MACA;;MAEA;MACA,IAAA+D,SAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,KAAAF,SAAA;;MAEA;MAAA,IAAAG,KAAA,YAAAA,MAAA,EACA;QACA,IAAAC,IAAA,GAAAH,QAAA,CAAAI,aAAA;QACAD,IAAA,CAAAE,SAAA;;QAEA;QACA,IAAAC,IAAA,GAAAC,IAAA,CAAAC,MAAA;QACA,IAAAC,KAAA,GAAAF,IAAA,CAAAC,MAAA;;QAEAL,IAAA,CAAAO,KAAA,CAAAC,OAAA,uDAAA7B,MAAA,CAEAwB,IAAA,0PAAAxB,MAAA,CAOA2B,KAAA,iBACA;QACAV,SAAA,CAAAa,WAAA,CAAAT,IAAA;;QAEA;QACAU,UAAA;UACAd,SAAA,CAAAe,WAAA,CAAAX,IAAA;QACA,IAAAM,KAAA;MACA;MAzBA,SAAAM,CAAA,MAAAA,CAAA,OAAAA,CAAA;QAAAb,KAAA;MAAA;;MA2BA;MAAA,IAAAc,MAAA,YAAAA,OAAA,EACA;QACA,IAAAC,IAAA,GAAAjB,QAAA,CAAAI,aAAA;QACAa,IAAA,CAAAZ,SAAA;;QAEA;QACA,IAAAa,IAAA,GAAAX,IAAA,CAAAC,MAAA;QACA,IAAAF,IAAA,GAAAC,IAAA,CAAAC,MAAA;QACA,IAAAW,GAAA,GAAAZ,IAAA,CAAAC,MAAA;QACA,IAAAC,KAAA,GAAAF,IAAA,CAAAC,MAAA;;QAEAS,IAAA,CAAAP,KAAA,CAAAC,OAAA,uDAAA7B,MAAA,CAEAwB,IAAA,gDAAAxB,MAAA,CAEAoC,IAAA,6BAAApC,MAAA,CACAoC,IAAA,2CAAApC,MAAA,CACAqC,GAAA,yNAAArC,MAAA,CAGA2B,KAAA,iBACA;QACAV,SAAA,CAAAa,WAAA,CAAAK,IAAA;;QAEA;QACAJ,UAAA;UACAd,SAAA,CAAAe,WAAA,CAAAG,IAAA;QACA,IAAAR,KAAA;MACA;MA3BA,SAAAM,EAAA,MAAAA,EAAA,OAAAA,EAAA;QAAAC,MAAA;MAAA;IA4BA;IACApG,mBAAA,WAAAA,oBAAA;MAAA,IAAAwG,MAAA;MACA;MACA,KAAArH,cAAA,GAAAsH,WAAA;QACAD,MAAA,CAAAE,cAAA;MACA;IACA;IACAxG,mBAAA,WAAAA,oBAAA;MACA,SAAAf,cAAA;QACAwH,aAAA,MAAAxH,cAAA;QACA,KAAAA,cAAA;MACA;IACA;IACAuH,cAAA,WAAAA,eAAA;MACA,KAAA3G,YAAA;IACA;IACA6G,qBAAA,WAAAA,sBAAA;MACA,YAAA5H,UAAA,CAAA6H,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,SAAA;MAAA,GAAAvG,MAAA;IACA;IACAwG,mBAAA,WAAAA,oBAAA;MACA,YAAAJ,qBAAA,UAAA5H,UAAA,CAAAwB,MAAA;IACA;IACAyG,WAAA,WAAAA,YAAAC,MAAA;MACA;MACA,QAAAA,MAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAD,MAAA;MACA;MACA,KAAA1C,eAAA;;MAEA,QAAA0C,MAAA;QACA;UAAA;UACA,KAAAnF,OAAA,CAAAC,IAAA;UACA;QACA;UAAA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAa,QAAA,CAAAuE,IAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAjI,kBAAA;MACA,KAAAkI,eAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAnI,kBAAA;IACA;IACAkI,eAAA,WAAAA,gBAAA;MAAA,IAAAE,MAAA;MACA,KAAAlI,cAAA;MACA/B,SAAA,wCAAAiF,IAAA,WAAAmB,GAAA;QACA6D,MAAA,CAAAlI,cAAA;QACA,IAAAqE,GAAA,CAAAC,OAAA;UACA4D,MAAA,CAAAnI,WAAA,GAAAsE,GAAA,CAAAG,MAAA;;UAEA;UACA0D,MAAA,CAAAhI,WAAA,GAAAgI,MAAA,CAAAnI,WAAA,CACAwH,MAAA,WAAAY,MAAA;YAAA,OAAAA,MAAA,CAAAC,aAAA;UAAA,GACAC,MAAA,WAAAC,GAAA,EAAAH,MAAA;YAAA,OAAAG,GAAA,GAAAH,MAAA,CAAAI,SAAA;UAAA;UAEAL,MAAA,CAAA/H,YAAA,GAAA+H,MAAA,CAAAnI,WAAA,CACAwH,MAAA,WAAAY,MAAA;YAAA,OAAAA,MAAA,CAAAC,aAAA;UAAA,GACAC,MAAA,WAAAC,GAAA,EAAAH,MAAA;YAAA,OAAAG,GAAA,GAAAH,MAAA,CAAAI,SAAA;UAAA;UAEAL,MAAA,CAAAjI,SAAA;QACA;UACAiI,MAAA,CAAA3E,QAAA,CAAAC,KAAA,CAAAa,GAAA,CAAAX,OAAA;QACA;MACA,GAAAJ,KAAA;QACA4E,MAAA,CAAAlI,cAAA;MACA;IACA;IACAwI,eAAA,WAAAA,gBAAA;MACA,KAAAR,eAAA;IACA;IACAS,UAAA,WAAAA,WAAAC,SAAA;MACA,KAAAA,SAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,UAAAtE,MAAA,CAAAiE,IAAA,OAAAjE,MAAA,CAAAmE,KAAA,OAAAnE,MAAA,CAAAuE,GAAA,OAAAvE,MAAA,CAAAyE,KAAA,OAAAzE,MAAA,CAAA2E,OAAA;IACA;EAAA;AAEA", "ignoreList": []}]}