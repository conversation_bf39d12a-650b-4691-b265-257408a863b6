{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue?vue&type=template&id=7fb21df8&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div>\n  <j-modal\n    :title=\"'单元 - ' + unit.unitName\"\n    :visible=\"visible\"\n    :width=\"1000\"\n    :fullscreen=\"false\"\n    switchFullscreen\n    @ok=\"handleCancel\"\n    @cancel=\"handleCancel\"\n  >\n    <div class=\"video-area\">\n      <a-tabs v-if=\"unit.courseVideo || unit.courseCase || unit.mediaContent\">\n        <a-tab-pane key=\"video\" tab=\"视频\" v-if=\"unit.courseVideo\">\n          <video v-if=\"unit.courseVideoSource==1\" :src=\"unit.courseVideo_url\" controls=\"true\" controlsList='nodownload noremote footbar' oncontextmenu=\"return false;\"></video>\n          <video v-if=\"unit.courseVideoSource==2\" :src=\"unit.courseVideo\" controls=\"true\" controlsList='nodownload noremote footbar' oncontextmenu=\"return false;\"></video>\n          <div v-if=\"unit.courseVideoSource==3\" v-html=\"unit.courseVideo\"></div>\n        </a-tab-pane>\n        <!-- 注释掉案例Tab页 \n        <a-tab-pane key=\"scratch\" tab=\"案例\" v-if=\"unit.courseCase\">\n          <iframe id=\"player\" :src=\"previewCourseCase(unit)\" scrolling=\"yes\"></iframe>\n        </a-tab-pane>\n        -->\n        <a-tab-pane key=\"media\" tab=\"课程内容\" v-if=\"unit.mediaContent\">\n          <div v-html=\"unit.mediaContent\"></div>\n        </a-tab-pane>\n      </a-tabs>\n    </div>\n    <a-divider>本节课资料</a-divider>\n    <div>\n      <a-row :gutter=\"24\">\n        <a-col :span=\"16\">\n          <a-card size=\"small\" title=\"课程说明\">\n            <div v-html=\"unit.unitIntro ? unit.unitIntro.replace(/\\n/g, '<br>') : ''\"></div>\n          </a-card>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-collapse defaultActiveKey=\"0\" :bordered=\"false\">\n            <template v-slot:expandIcon=\"props\">\n              <a-icon type=\"caret-right\" :rotate=\"props.isActive ? 90 : 0\" />\n            </template>\n            <a-collapse-panel v-if=\"unit.courseWork_url\" key=\"0\" :header=\"'课后作业'\" :style=\"customStyle\">\n              <div class=\"homework-buttons\">\n                <a-button @click=\"showHomeworkOptions(unit)\" type=\"primary\" icon=\"edit\">去做作业</a-button>\n                <!-- 暂时隐藏下载作业按钮 -->\n                <!-- <a-button v-if=\"unit.courseWork_url\" @click=\"downloadWorkDoc(unit.courseWork_url)\" icon=\"download\">下载作业</a-button> -->\n              </div>\n            </a-collapse-panel>\n            <a-collapse-panel v-if=\"unit.coursePpt_url\" :header=\"'课程讲义'\" :style=\"customStyle\">\n              <div v-for=\"(u,i) in unit.coursePpt_url.split(',')\" :key=\"i\">\n                <!-- <a v-else-if=\"u.endsWith('ppt')||u.endsWith('pptx')\" target=\"_blank\" :href=\"'https://view.officeapps.live.com/op/embed.aspx?src='+u\"\n                  ><a-icon type=\"file-ppt\" /> {{(i+1)}}. 查看PPT </a\n                > -->\n                <a target=\"_blank\" :href=\"u\"\n                  ><a-icon type=\"file-pdf\" /> {{(i+1)}}. 下载讲义</a\n                >\n              </div>\n            </a-collapse-panel>\n            <a-collapse-panel v-if=\"unit.coursePlan_url\" :header=\"'课程代码'\" :style=\"customStyle\">\n              <div v-for=\"(u,i) in unit.coursePlan_url.split(',')\" :key=\"i\">\n               <a v-if=\"u.endsWith('sb3')\" target=\"_blank\" :href=\"'/scratch3/index.html?scene=create&workFile='+u\"\n                  ><a-icon type=\"code\" /> {{(i+1)}}. 查看Scratch代码 </a\n                >\n               <!-- 添加对Python文件的支持 -->\n               <a v-else-if=\"u.endsWith('py')\" target=\"_blank\" :href=\"'/python/index.html?lang=python&url='+u\"\n                  ><a-icon type=\"code\" /> {{(i+1)}}. 查看Python代码 </a\n                >\n               <!-- 添加对C++文件的支持 -->\n               <a v-else-if=\"u.endsWith('cpp')\" target=\"_blank\" :href=\"'/cpp/index.html?url='+u\"\n                  ><a-icon type=\"code\" /> {{(i+1)}}. 查看C++代码 </a\n                >\n              </div>\n            </a-collapse-panel>\n          </a-collapse>\n        </a-col>\n      </a-row>\n    </div>\n  </j-modal>\n\n  <!-- 新增的作业选择弹窗 -->\n  <a-modal\n    title=\"选择作业类型\"\n    :visible=\"homeworkOptionsVisible\"\n    :footer=\"null\"\n    @cancel=\"homeworkOptionsVisible = false\"\n    width=\"400px\"\n    centered\n  >\n    <div class=\"homework-options\">\n      <a-button type=\"primary\" size=\"large\" icon=\"form\" @click=\"handleObjectiveExercise\">客观题</a-button>\n      <a-button type=\"primary\" size=\"large\" icon=\"code\" disabled @click=\"handleProgrammingExercise\">\n        编程题\n        <a-tooltip placement=\"bottom\">\n          <template slot=\"title\">\n            <span>该功能暂未开放</span>\n          </template>\n          <a-icon type=\"info-circle\" style=\"margin-left: 5px;\" />\n        </a-tooltip>\n      </a-button>\n    </div>\n  </a-modal>\n  \n  <!-- 客观题弹窗组件 -->\n  <objective-quiz-modal \n    :visible=\"objectiveQuizVisible\"\n    :unitId=\"currentUnitId\"\n    :courseId=\"currentCourseId\"\n    @close=\"objectiveQuizVisible = false\"\n  />\n  \n  <!-- 客观题答题记录详情弹窗 -->\n  <objective-quiz-record-detail-modal ref=\"recordDetailModal\" />\n</div>\n", null]}