{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld2.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\helloworld2.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  data: function data() {\n    return {\n      description: '表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。',\n      value: 1,\n      // form\n      form: this.$form.createForm(this)\n    };\n  },\n  methods: {\n    // handler\n    handleSubmit: function handleSubmit(e) {\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          // eslint-disable-next-line no-console\n          console.log('Received values of form: ', values);\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["data", "description", "value", "form", "$form", "createForm", "methods", "handleSubmit", "e", "preventDefault", "validateFields", "err", "values", "console", "log"], "sources": ["src/views/jeecg/helloworld2.vue"], "sourcesContent": ["<template>\n  <div>\n    hello world!\n  </div>\n</template>\n<script>\n  export default {\n    data () {\n      return {\n        description: '表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。',\n        value: 1,\n\n        // form\n        form: this.$form.createForm(this),\n\n      }\n    },\n    methods: {\n      // handler\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            // eslint-disable-next-line no-console\n            console.log('Received values of form: ', values)\n          }\n        })\n      }\n    }\n  }\n</script>"], "mappings": "AAMA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,KAAA;MAEA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;IAEA;EACA;EACAC,OAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MACAA,CAAA,CAAAC,cAAA;MACA,KAAAN,IAAA,CAAAO,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACA;UACAE,OAAA,CAAAC,GAAA,8BAAAF,MAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}