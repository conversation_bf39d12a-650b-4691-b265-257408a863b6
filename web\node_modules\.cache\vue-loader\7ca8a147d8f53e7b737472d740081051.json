{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\PrintDemo.vue?vue&type=template&id=334a0705&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\PrintDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-card\", {\n    class: {\n      abcdefg: true\n    },\n    attrs: {\n      bordered: false\n    }\n  }, [_c(\"div\", {\n    staticClass: \"no-print\",\n    staticStyle: {\n      \"text-align\": \"right\"\n    }\n  }, [_c(\"a-button\", {\n    directives: [{\n      name: \"print\",\n      rawName: \"v-print\",\n      value: \"#printContent\",\n      expression: \"'#printContent'\"\n    }],\n    attrs: {\n      ghost: \"\",\n      type: \"primary\"\n    }\n  }, [_vm._v(\"打印\")])], 1), _c(\"section\", {\n    ref: \"print\",\n    staticClass: \"abcdefg\",\n    attrs: {\n      id: \"printContent\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"text-align\": \"center\"\n    }\n  }, [_c(\"p\", {\n    staticStyle: {\n      \"font-size\": \"24px\",\n      \"font-weight\": \"800\"\n    }\n  }, [_vm._v(\"打印测试表单\")])]), _c(\"a-col\", {\n    attrs: {\n      md: 24,\n      sm: 24\n    }\n  }, [_c(\"div\", {\n    staticClass: \"sign\",\n    staticStyle: {\n      \"text-align\": \"left\",\n      height: \"inherit\"\n    }\n  }, [_c(\"a-col\", {\n    attrs: {\n      span: 24\n    }\n  }, [_c(\"span\", [_vm._v(\"\\n          打印人员:\\n        \")]), _c(\"a-input\", {\n    staticStyle: {\n      width: \"30%\"\n    },\n    model: {\n      value: _vm.printer,\n      callback: function callback($$v) {\n        _vm.printer = $$v;\n      },\n      expression: \"printer\"\n    }\n  }), _c(\"span\", {\n    staticStyle: {\n      \"margin-left\": \"12.5%\"\n    }\n  }, [_vm._v(\"打印日期:\")]), _c(\"a-input\", {\n    staticStyle: {\n      width: \"30%\"\n    },\n    model: {\n      value: _vm.printTime,\n      callback: function callback($$v) {\n        _vm.printTime = $$v;\n      },\n      expression: \"printTime\"\n    }\n  })], 1), _c(\"a-col\", {\n    attrs: {\n      span: 24\n    }\n  }), _c(\"a-col\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    },\n    attrs: {\n      span: 24\n    }\n  }, [_c(\"span\", [_vm._v(\"打印内容:\")]), _c(\"a-input\", {\n    staticStyle: {\n      width: \"80%\"\n    },\n    model: {\n      value: _vm.printContent,\n      callback: function callback($$v) {\n        _vm.printContent = $$v;\n      },\n      expression: \"printContent\"\n    }\n  })], 1), _c(\"a-col\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    },\n    attrs: {\n      span: 24\n    }\n  }, [_c(\"span\", [_vm._v(\"打印目的:\")]), _c(\"a-input\", {\n    staticStyle: {\n      width: \"80%\"\n    },\n    model: {\n      value: _vm.printReason,\n      callback: function callback($$v) {\n        _vm.printReason = $$v;\n      },\n      expression: \"printReason\"\n    }\n  })], 1), _c(\"a-col\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    },\n    attrs: {\n      span: 24\n    }\n  }, [_c(\"span\", [_vm._v(\"打印图片:\")]), _c(\"br\"), _c(\"a-upload\", {\n    attrs: {\n      action: \"/jsonplaceholder.typicode.com/posts/\",\n      listType: \"picture-card\",\n      fileList: _vm.fileList\n    },\n    on: {\n      preview: _vm.handlePreview,\n      change: _vm.handleChange\n    }\n  }, [_vm.fileList.length < 3 ? _c(\"div\", [_c(\"a-icon\", {\n    attrs: {\n      type: \"plus\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"ant-upload-text\"\n  }, [_vm._v(\"Upload\")])], 1) : _vm._e()]), _c(\"a-modal\", {\n    attrs: {\n      visible: _vm.previewVisible,\n      footer: null\n    },\n    on: {\n      cancel: _vm.handleCancel\n    }\n  }, [_c(\"img\", {\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      alt: \"example\",\n      src: _vm.previewImage\n    }\n  })])], 1)], 1)])], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "class", "abcdefg", "attrs", "bordered", "staticClass", "staticStyle", "directives", "name", "rawName", "value", "expression", "ghost", "type", "_v", "ref", "id", "md", "sm", "height", "span", "width", "model", "printer", "callback", "$$v", "printTime", "printContent", "printReason", "action", "listType", "fileList", "on", "preview", "handlePreview", "change", "handleChange", "length", "_e", "visible", "previewVisible", "footer", "cancel", "handleCancel", "alt", "src", "previewImage", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/jeecg/PrintDemo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-card\",\n    { class: { abcdefg: true }, attrs: { bordered: false } },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"no-print\", staticStyle: { \"text-align\": \"right\" } },\n        [\n          _c(\n            \"a-button\",\n            {\n              directives: [\n                {\n                  name: \"print\",\n                  rawName: \"v-print\",\n                  value: \"#printContent\",\n                  expression: \"'#printContent'\",\n                },\n              ],\n              attrs: { ghost: \"\", type: \"primary\" },\n            },\n            [_vm._v(\"打印\")]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"section\",\n        { ref: \"print\", staticClass: \"abcdefg\", attrs: { id: \"printContent\" } },\n        [\n          _c(\"div\", { staticStyle: { \"text-align\": \"center\" } }, [\n            _c(\n              \"p\",\n              { staticStyle: { \"font-size\": \"24px\", \"font-weight\": \"800\" } },\n              [_vm._v(\"打印测试表单\")]\n            ),\n          ]),\n          _c(\"a-col\", { attrs: { md: 24, sm: 24 } }, [\n            _c(\n              \"div\",\n              {\n                staticClass: \"sign\",\n                staticStyle: { \"text-align\": \"left\", height: \"inherit\" },\n              },\n              [\n                _c(\n                  \"a-col\",\n                  { attrs: { span: 24 } },\n                  [\n                    _c(\"span\", [_vm._v(\"\\n          打印人员:\\n        \")]),\n                    _c(\"a-input\", {\n                      staticStyle: { width: \"30%\" },\n                      model: {\n                        value: _vm.printer,\n                        callback: function ($$v) {\n                          _vm.printer = $$v\n                        },\n                        expression: \"printer\",\n                      },\n                    }),\n                    _c(\"span\", { staticStyle: { \"margin-left\": \"12.5%\" } }, [\n                      _vm._v(\"打印日期:\"),\n                    ]),\n                    _c(\"a-input\", {\n                      staticStyle: { width: \"30%\" },\n                      model: {\n                        value: _vm.printTime,\n                        callback: function ($$v) {\n                          _vm.printTime = $$v\n                        },\n                        expression: \"printTime\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\"a-col\", { attrs: { span: 24 } }),\n                _c(\n                  \"a-col\",\n                  {\n                    staticStyle: { \"margin-top\": \"20px\" },\n                    attrs: { span: 24 },\n                  },\n                  [\n                    _c(\"span\", [_vm._v(\"打印内容:\")]),\n                    _c(\"a-input\", {\n                      staticStyle: { width: \"80%\" },\n                      model: {\n                        value: _vm.printContent,\n                        callback: function ($$v) {\n                          _vm.printContent = $$v\n                        },\n                        expression: \"printContent\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-col\",\n                  {\n                    staticStyle: { \"margin-top\": \"20px\" },\n                    attrs: { span: 24 },\n                  },\n                  [\n                    _c(\"span\", [_vm._v(\"打印目的:\")]),\n                    _c(\"a-input\", {\n                      staticStyle: { width: \"80%\" },\n                      model: {\n                        value: _vm.printReason,\n                        callback: function ($$v) {\n                          _vm.printReason = $$v\n                        },\n                        expression: \"printReason\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-col\",\n                  {\n                    staticStyle: { \"margin-top\": \"20px\" },\n                    attrs: { span: 24 },\n                  },\n                  [\n                    _c(\"span\", [_vm._v(\"打印图片:\")]),\n                    _c(\"br\"),\n                    _c(\n                      \"a-upload\",\n                      {\n                        attrs: {\n                          action: \"/jsonplaceholder.typicode.com/posts/\",\n                          listType: \"picture-card\",\n                          fileList: _vm.fileList,\n                        },\n                        on: {\n                          preview: _vm.handlePreview,\n                          change: _vm.handleChange,\n                        },\n                      },\n                      [\n                        _vm.fileList.length < 3\n                          ? _c(\n                              \"div\",\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                                _c(\"div\", { staticClass: \"ant-upload-text\" }, [\n                                  _vm._v(\"Upload\"),\n                                ]),\n                              ],\n                              1\n                            )\n                          : _vm._e(),\n                      ]\n                    ),\n                    _c(\n                      \"a-modal\",\n                      {\n                        attrs: { visible: _vm.previewVisible, footer: null },\n                        on: { cancel: _vm.handleCancel },\n                      },\n                      [\n                        _c(\"img\", {\n                          staticStyle: { width: \"100%\" },\n                          attrs: { alt: \"example\", src: _vm.previewImage },\n                        }),\n                      ]\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              1\n            ),\n          ]),\n        ],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR;IAAEE,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC;IAAEC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAM;EAAE,CAAC,EACxD,CACEL,EAAE,CACA,KAAK,EACL;IAAEM,WAAW,EAAE,UAAU;IAAEC,WAAW,EAAE;MAAE,YAAY,EAAE;IAAQ;EAAE,CAAC,EACnE,CACEP,EAAE,CACA,UAAU,EACV;IACEQ,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,OAAO;MACbC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,eAAe;MACtBC,UAAU,EAAE;IACd,CAAC,CACF;IACDR,KAAK,EAAE;MAAES,KAAK,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAU;EACtC,CAAC,EACD,CAACf,GAAG,CAACgB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,EACDf,EAAE,CACA,SAAS,EACT;IAAEgB,GAAG,EAAE,OAAO;IAAEV,WAAW,EAAE,SAAS;IAAEF,KAAK,EAAE;MAAEa,EAAE,EAAE;IAAe;EAAE,CAAC,EACvE,CACEjB,EAAE,CAAC,KAAK,EAAE;IAAEO,WAAW,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EAAE,CACrDP,EAAE,CACA,GAAG,EACH;IAAEO,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAE,aAAa,EAAE;IAAM;EAAE,CAAC,EAC9D,CAACR,GAAG,CAACgB,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,CAAC,EACFf,EAAE,CAAC,OAAO,EAAE;IAAEI,KAAK,EAAE;MAAEc,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG;EAAE,CAAC,EAAE,CACzCnB,EAAE,CACA,KAAK,EACL;IACEM,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;MAAE,YAAY,EAAE,MAAM;MAAEa,MAAM,EAAE;IAAU;EACzD,CAAC,EACD,CACEpB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEiB,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACErB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACgB,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,EACnDf,EAAE,CAAC,SAAS,EAAE;IACZO,WAAW,EAAE;MAAEe,KAAK,EAAE;IAAM,CAAC;IAC7BC,KAAK,EAAE;MACLZ,KAAK,EAAEZ,GAAG,CAACyB,OAAO;MAClBC,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvB3B,GAAG,CAACyB,OAAO,GAAGE,GAAG;MACnB,CAAC;MACDd,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFZ,EAAE,CAAC,MAAM,EAAE;IAAEO,WAAW,EAAE;MAAE,aAAa,EAAE;IAAQ;EAAE,CAAC,EAAE,CACtDR,GAAG,CAACgB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFf,EAAE,CAAC,SAAS,EAAE;IACZO,WAAW,EAAE;MAAEe,KAAK,EAAE;IAAM,CAAC;IAC7BC,KAAK,EAAE;MACLZ,KAAK,EAAEZ,GAAG,CAAC4B,SAAS;MACpBF,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvB3B,GAAG,CAAC4B,SAAS,GAAGD,GAAG;MACrB,CAAC;MACDd,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDZ,EAAE,CAAC,OAAO,EAAE;IAAEI,KAAK,EAAE;MAAEiB,IAAI,EAAE;IAAG;EAAE,CAAC,CAAC,EACpCrB,EAAE,CACA,OAAO,EACP;IACEO,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MAAEiB,IAAI,EAAE;IAAG;EACpB,CAAC,EACD,CACErB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACgB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7Bf,EAAE,CAAC,SAAS,EAAE;IACZO,WAAW,EAAE;MAAEe,KAAK,EAAE;IAAM,CAAC;IAC7BC,KAAK,EAAE;MACLZ,KAAK,EAAEZ,GAAG,CAAC6B,YAAY;MACvBH,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvB3B,GAAG,CAAC6B,YAAY,GAAGF,GAAG;MACxB,CAAC;MACDd,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDZ,EAAE,CACA,OAAO,EACP;IACEO,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MAAEiB,IAAI,EAAE;IAAG;EACpB,CAAC,EACD,CACErB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACgB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7Bf,EAAE,CAAC,SAAS,EAAE;IACZO,WAAW,EAAE;MAAEe,KAAK,EAAE;IAAM,CAAC;IAC7BC,KAAK,EAAE;MACLZ,KAAK,EAAEZ,GAAG,CAAC8B,WAAW;MACtBJ,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvB3B,GAAG,CAAC8B,WAAW,GAAGH,GAAG;MACvB,CAAC;MACDd,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDZ,EAAE,CACA,OAAO,EACP;IACEO,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCH,KAAK,EAAE;MAAEiB,IAAI,EAAE;IAAG;EACpB,CAAC,EACD,CACErB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACgB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7Bf,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MACL0B,MAAM,EAAE,sCAAsC;MAC9CC,QAAQ,EAAE,cAAc;MACxBC,QAAQ,EAAEjC,GAAG,CAACiC;IAChB,CAAC;IACDC,EAAE,EAAE;MACFC,OAAO,EAAEnC,GAAG,CAACoC,aAAa;MAC1BC,MAAM,EAAErC,GAAG,CAACsC;IACd;EACF,CAAC,EACD,CACEtC,GAAG,CAACiC,QAAQ,CAACM,MAAM,GAAG,CAAC,GACnBtC,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CAAC,QAAQ,EAAE;IAAEI,KAAK,EAAE;MAAEU,IAAI,EAAE;IAAO;EAAE,CAAC,CAAC,EACzCd,EAAE,CAAC,KAAK,EAAE;IAAEM,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CP,GAAG,CAACgB,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,CACH,EACD,CACF,CAAC,GACDhB,GAAG,CAACwC,EAAE,CAAC,CAAC,CAEhB,CAAC,EACDvC,EAAE,CACA,SAAS,EACT;IACEI,KAAK,EAAE;MAAEoC,OAAO,EAAEzC,GAAG,CAAC0C,cAAc;MAAEC,MAAM,EAAE;IAAK,CAAC;IACpDT,EAAE,EAAE;MAAEU,MAAM,EAAE5C,GAAG,CAAC6C;IAAa;EACjC,CAAC,EACD,CACE5C,EAAE,CAAC,KAAK,EAAE;IACRO,WAAW,EAAE;MAAEe,KAAK,EAAE;IAAO,CAAC;IAC9BlB,KAAK,EAAE;MAAEyC,GAAG,EAAE,SAAS;MAAEC,GAAG,EAAE/C,GAAG,CAACgD;IAAa;EACjD,CAAC,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBlD,MAAM,CAACmD,aAAa,GAAG,IAAI;AAE3B,SAASnD,MAAM,EAAEkD,eAAe", "ignoreList": []}]}