{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\BasicForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\BasicForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: 'BaseForm',\n  data: function data() {\n    return {\n      description: '表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。',\n      value: 1,\n      // form\n      form: this.$form.createForm(this)\n    };\n  },\n  methods: {\n    // handler\n    handleSubmit: function handleSubmit(e) {\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          // eslint-disable-next-line no-console\n          console.log('Received values of form: ', values);\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["name", "data", "description", "value", "form", "$form", "createForm", "methods", "handleSubmit", "e", "preventDefault", "validateFields", "err", "values", "console", "log"], "sources": ["src/views/form/BasicForm.vue"], "sourcesContent": ["<template>\n  <a-card :body-style=\"{padding: '24px 32px'}\" :bordered=\"false\">\n    <a-form @submit=\"handleSubmit\" :form=\"form\">\n      <a-form-item\n        label=\"标题\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\">\n        <a-input\n          v-decorator=\"[\n            'name',\n            {rules: [{ required: true, message: '请输入标题' }]}\n          ]\"\n          name=\"name\"\n          placeholder=\"给目标起个名字\" />\n      </a-form-item>\n      <a-form-item\n        label=\"起止日期\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\">\n        <a-range-picker\n          name=\"buildTime\"\n          style=\"width: 100%\"\n          v-decorator=\"[\n            'buildTime',\n            {rules: [{ required: true, message: '请选择起止日期' }]}\n          ]\" />\n      </a-form-item>\n      <a-form-item\n        label=\"目标描述\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\">\n        <a-textarea\n          rows=\"4\"\n          placeholder=\"请输入你阶段性工作目标\"\n          v-decorator=\"[\n            'description',\n            {rules: [{ required: true, message: '请输入目标描述' }]}\n          ]\" />\n      </a-form-item>\n      <a-form-item\n        label=\"衡量标准\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\">\n        <a-textarea\n          rows=\"4\"\n          placeholder=\"请输入衡量标准\"\n          v-decorator=\"[\n            'type',\n            {rules: [{ required: true, message: '请输入衡量标准' }]}\n          ]\" />\n      </a-form-item>\n      <a-form-item\n        label=\"客户\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\">\n        <a-input \n          placeholder=\"请描述你服务的客户，内部客户直接 @姓名／工号\"\n          v-decorator=\"[\n            'customer',\n            {rules: [{ required: true, message: '请描述你服务的客户' }]}\n          ]\" />\n      </a-form-item>\n      <a-form-item\n        label=\"邀评人\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\"\n        :required=\"false\"\n      >\n        <a-input placeholder=\"请直接 @姓名／工号，最多可邀请 5 人\" />\n      </a-form-item>\n      <a-form-item\n        label=\"权重\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\"\n        :required=\"false\"\n      >\n        <a-input-number :min=\"0\" :max=\"100\" />\n        <span> %</span>\n      </a-form-item>\n      <a-form-item\n        label=\"目标公开\"\n        :labelCol=\"{lg: {span: 7}, sm: {span: 7}}\"\n        :wrapperCol=\"{lg: {span: 10}, sm: {span: 17} }\"\n        :required=\"false\"\n        help=\"客户、邀评人默认被分享\"\n      >\n        <a-radio-group v-model=\"value\">\n          <a-radio :value=\"1\">公开</a-radio>\n          <a-radio :value=\"2\">部分公开</a-radio>\n          <a-radio :value=\"3\">不公开</a-radio>\n        </a-radio-group>\n        <a-form-item>\n          <a-select mode=\"multiple\" v-if=\"value === 2\">\n            <a-select-option value=\"4\">同事一</a-select-option>\n            <a-select-option value=\"5\">同事二</a-select-option>\n            <a-select-option value=\"6\">同事三</a-select-option>\n          </a-select>\n        </a-form-item>\n      </a-form-item>\n      <a-form-item\n        :wrapperCol=\"{ span: 24 }\"\n        style=\"text-align: center\"\n      >\n        <a-button htmlType=\"submit\" type=\"primary\">提交</a-button>\n        <a-button style=\"margin-left: 8px\">保存</a-button>\n      </a-form-item>\n    </a-form>\n  </a-card>\n</template>\n\n<script>\n  export default {\n    name: 'BaseForm',\n    data () {\n      return {\n        description: '表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。',\n        value: 1,\n\n        // form\n        form: this.$form.createForm(this),\n\n      }\n    },\n    methods: {\n\n      // handler\n      handleSubmit (e) {\n        e.preventDefault()\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            // eslint-disable-next-line no-console\n            console.log('Received values of form: ', values)\n          }\n        })\n      }\n    }\n  }\n</script>"], "mappings": "AA+GA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,KAAA;MAEA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;IAEA;EACA;EACAC,OAAA;IAEA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MACAA,CAAA,CAAAC,cAAA;MACA,KAAAN,IAAA,CAAAO,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACA;UACAE,OAAA,CAAAC,GAAA,8BAAAF,MAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}