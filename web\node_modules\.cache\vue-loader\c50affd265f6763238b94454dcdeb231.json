{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\RoleList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\RoleList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import STable from '@/components/table/'\n  import RoleModal from './modules/RoleModal'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      STable,\n      RoleModal\n    },\n    data () {\n      return {\n        description: '列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。',\n\n        visible: false,\n\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: false,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '唯一识别码',\n            dataIndex: 'id'\n          },\n          {\n            title: '角色名称',\n            dataIndex: 'name',\n          },\n          {\n            title: '状态',\n            dataIndex: 'status'\n          },\n          {\n            title: '创建时间',\n            dataIndex: 'createTime',\n            sorter: true\n          }, {\n            title: '操作',\n            width: '150px',\n            dataIndex: 'action',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return this.$http.get('/api/role', {\n            params: Object.assign(parameter, this.queryParam)\n          }).then(res => {\n            return res.result\n          })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    methods: {\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n\n        this.mdl.permissions.forEach(permission => {\n          permission.actionsOptions = permission.actionEntitySet.map(action => {\n            return { label: action.describe, value: action.action, defaultCheck: action.defaultCheck }\n          })\n        })\n\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n        // 新增/修改 成功时，重载列表\n        this.$refs.table.refresh()\n      },\n      onChange (selectedRowKeys, selectedRows) {\n        this.selectedRowKeys = selectedRowKeys\n        this.selectedRows = selectedRows\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n", {"version": 3, "sources": ["RoleList.vue"], "names": [], "mappings": ";AAiFA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RoleList.vue", "sourceRoot": "src/views/list", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"48\">\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"角色ID\">\n              <a-input placeholder=\"请输入\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"状态\">\n              <a-select placeholder=\"请选择\" default-value=\"0\">\n                <a-select-option value=\"0\">全部</a-select-option>\n                <a-select-option value=\"1\">正常</a-select-option>\n                <a-select-option value=\"2\">禁用</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <span class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\">查询</a-button>\n              <a-button style=\"margin-left: 8px\">重置</a-button>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <s-table\n      ref=\"table\"\n      size=\"default\"\n      :columns=\"columns\"\n      :data=\"loadData\"\n    >\n      <div\n        slot=\"expandedRowRender\"\n        slot-scope=\"record\"\n        style=\"margin: 0\">\n        <a-row\n          :gutter=\"24\"\n          :style=\"{ marginBottom: '12px' }\">\n          <a-col :span=\"12\" v-for=\"(role, index) in record.permissions\" :key=\"index\" :style=\"{ marginBottom: '12px' }\">\n            <a-col :span=\"4\">\n              <span>{{ role.permissionName }}：</span>\n            </a-col>\n            <a-col :span=\"20\" v-if=\"role.actionEntitySet.length > 0\">\n              <a-tag color=\"cyan\" v-for=\"(action, k) in role.actionEntitySet\" :key=\"k\">{{ action.describe }}</a-tag>\n            </a-col>\n            <a-col :span=\"20\" v-else>-</a-col>\n          </a-col>\n        </a-row>\n      </div>\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"$refs.modal.edit(record)\">编辑</a>\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">\n            更多 <a-icon type=\"down\" />\n          </a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a href=\"javascript:;\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">禁用</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a href=\"javascript:;\">删除</a>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </s-table>\n\n    <role-modal ref=\"modal\" @ok=\"handleOk\"></role-modal>\n\n  </a-card>\n</template>\n\n<script>\n  import STable from '@/components/table/'\n  import RoleModal from './modules/RoleModal'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      STable,\n      RoleModal\n    },\n    data () {\n      return {\n        description: '列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。',\n\n        visible: false,\n\n        form: null,\n        mdl: {},\n\n        // 高级搜索 展开/关闭\n        advanced: false,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '唯一识别码',\n            dataIndex: 'id'\n          },\n          {\n            title: '角色名称',\n            dataIndex: 'name',\n          },\n          {\n            title: '状态',\n            dataIndex: 'status'\n          },\n          {\n            title: '创建时间',\n            dataIndex: 'createTime',\n            sorter: true\n          }, {\n            title: '操作',\n            width: '150px',\n            dataIndex: 'action',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return this.$http.get('/api/role', {\n            params: Object.assign(parameter, this.queryParam)\n          }).then(res => {\n            return res.result\n          })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    methods: {\n      handleEdit (record) {\n        this.mdl = Object.assign({}, record)\n\n        this.mdl.permissions.forEach(permission => {\n          permission.actionsOptions = permission.actionEntitySet.map(action => {\n            return { label: action.describe, value: action.action, defaultCheck: action.defaultCheck }\n          })\n        })\n\n        console.log(this.mdl)\n        this.visible = true\n      },\n      handleOk () {\n        // 新增/修改 成功时，重载列表\n        this.$refs.table.refresh()\n      },\n      onChange (selectedRowKeys, selectedRows) {\n        this.selectedRowKeys = selectedRowKeys\n        this.selectedRows = selectedRows\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n</script>"]}]}