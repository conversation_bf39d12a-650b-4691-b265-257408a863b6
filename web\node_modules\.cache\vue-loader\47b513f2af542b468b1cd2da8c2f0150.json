{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableInnerEditList.vue?vue&type=style&index=0&id=960976d2&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\TableInnerEditList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.search {\n  margin-bottom: 54px;\n}\n\n.fold {\n  width: calc(100% - 216px);\n  display: inline-block\n}\n\n.operator {\n  margin-bottom: 18px;\n}\n\n@media screen and (max-width: 900px) {\n  .fold {\n    width: 100%;\n  }\n}\n", {"version": 3, "sources": ["TableInnerEditList.vue"], "names": [], "mappings": ";AA8QA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "TableInnerEditList.vue", "sourceRoot": "src/views/list", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"48\">\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"规则编号\">\n              <a-input placeholder=\"\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"使用状态\">\n              <a-select placeholder=\"请选择\" default-value=\"0\">\n                <a-select-option value=\"0\">全部</a-select-option>\n                <a-select-option value=\"1\">关闭</a-select-option>\n                <a-select-option value=\"2\">运行中</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"advanced\">\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"调用次数\">\n                <a-input-number style=\"width: 100%\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"更新日期\">\n                <a-date-picker style=\"width: 100%\" placeholder=\"请输入更新日期\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"使用状态\">\n                <a-select placeholder=\"请选择\" default-value=\"0\">\n                  <a-select-option value=\"0\">全部</a-select-option>\n                  <a-select-option value=\"1\">关闭</a-select-option>\n                  <a-select-option value=\"2\">运行中</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :md=\"!advanced && 8 || 24\" :sm=\"24\">\n            <span class=\"table-page-search-submitButtons\" :style=\"advanced && { float: 'right', overflow: 'hidden' } || {} \">\n              <a-button type=\"primary\">查询</a-button>\n              <a-button style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"toggleAdvanced\" style=\"margin-left: 8px\">\n                {{ advanced ? '收起' : '展开' }}\n                <a-icon :type=\"advanced ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <div class=\"table-operator\">\n      <a-button type=\"primary\" icon=\"plus\" @click=\"() => $router.push({name: 'anime-add'})\">新建</a-button>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\"><a-icon type=\"delete\" />删除</a-menu-item>\n          <!-- lock | unlock -->\n          <a-menu-item key=\"2\"><a-icon type=\"lock\" />锁定</a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作 <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <s-table\n      ref=\"table\"\n      size=\"default\"\n      :columns=\"columns\"\n      :data=\"loadData\"\n      :showAlertInfo=\"true\"\n      @onSelect=\"onChange\"\n    >\n      <template v-for=\"(col, index) in columns\" v-if=\"col.scopedSlots\" :slot=\"col.dataIndex\" slot-scope=\"text, record, index\">\n        <div :key=\"index\">\n          <a-input\n            v-if=\"record.editable\"\n            style=\"margin: -5px 0\"\n            :value=\"text\"\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\n          />\n          <template v-else>{{ text }}</template>\n        </div>\n      </template>\n      <template slot=\"action\" slot-scope=\"text, record, index\">\n        <div class=\"editable-row-operations\">\n          <span v-if=\"record.editable\">\n            <a @click=\"() => save(record)\">保存</a>\n            <a-divider type=\"vertical\" />\n            <a-popconfirm title=\"真的放弃编辑吗?\" @confirm=\"() => cancel(record)\">\n              <a>取消</a>\n            </a-popconfirm>\n          </span>\n          <span v-else>\n            <a class=\"edit\" @click=\"() => edit(record)\">修改</a>\n            <a-divider type=\"vertical\" />\n            <a class=\"delete\" @click=\"() => del(record)\">删除</a>\n          </span>\n        </div>\n      </template>\n    </s-table>\n\n  </a-card>\n</template>\n\n<script>\n  import STable from '@/components/table/'\n\n  export default {\n    name: \"TableList\",\n    components: {\n      STable\n    },\n    data () {\n      return {\n        // 高级搜索 展开/关闭\n        advanced: false,\n        // 查询参数\n        queryParam: {},\n        // 表头\n        columns: [\n          {\n            title: '规则编号',\n            dataIndex: 'no',\n            width: 90\n          },\n          {\n            title: '描述',\n            dataIndex: 'description',\n            scopedSlots: { customRender: 'description' },\n          },\n          {\n            title: '服务调用次数',\n            dataIndex: 'callNo',\n            width: '150px',\n            sorter: true,\n            needTotal: true,\n            scopedSlots: { customRender: 'callNo' },\n            // customRender: (text) => text + ' 次'\n          },\n          {\n            title: '状态',\n            dataIndex: 'status',\n            width: '100px',\n            needTotal: true,\n            scopedSlots: { customRender: 'status' },\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updatedAt',\n            width: '150px',\n            sorter: true,\n            scopedSlots: { customRender: 'updatedAt' },\n          },\n          {\n            table: '操作',\n            dataIndex: 'action',\n            width: '120px',\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        // 加载数据方法 必须为 Promise 对象\n        loadData: parameter => {\n          return this.$http.get('/api/service', {\n            params: Object.assign(parameter, this.queryParam)\n          }).then(res => {\n            return res.result\n          })\n        },\n\n        selectedRowKeys: [],\n        selectedRows: []\n      }\n    },\n    methods: {\n\n      handleChange (value, key, column) {\n        console.log(value, key, column)\n      },\n      edit (row) {\n        row.editable = true\n        // row = Object.assign({}, row)\n        this.$refs.table.updateEdit()\n      },\n      // eslint-disable-next-line\n      del (row) {\n        this.$confirm({\n          title: '警告',\n          content: '真的要删除吗?',\n          okText: '删除',\n          okType: 'danger',\n          cancelText: '取消',\n          onOk() {\n            console.log('OK');\n            // 在这里调用删除接口\n            return new Promise((resolve, reject) => {\n              setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);\n            }).catch(() => console.log('Oops errors!'));\n          },\n          onCancel() {\n            console.log('Cancel');\n          },\n        });\n      },\n      save (row) {\n        delete row.editable\n        this.$refs.table.updateEdit()\n      },\n      cancel (row) {\n        delete row.editable\n        this.$refs.table.updateEdit()\n      },\n\n      onChange (row) {\n        this.selectedRowKeys = row.selectedRowKeys\n        this.selectedRows = row.selectedRows\n      },\n      toggleAdvanced () {\n        this.advanced = !this.advanced\n      },\n    },\n    watch: {\n      /*\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n      */\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .search {\n    margin-bottom: 54px;\n  }\n\n  .fold {\n    width: calc(100% - 216px);\n    display: inline-block\n  }\n\n  .operator {\n    margin-bottom: 18px;\n  }\n\n  @media screen and (max-width: 900px) {\n    .fold {\n      width: 100%;\n    }\n  }\n</style>"]}]}