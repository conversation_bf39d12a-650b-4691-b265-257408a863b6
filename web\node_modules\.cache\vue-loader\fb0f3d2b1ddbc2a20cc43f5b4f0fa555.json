{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue?vue&type=template&id=75101d3c", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div :style=\"{ padding: '0 0 32px 32px' }\">\n  <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n  <v-chart :forceFit=\"true\" :height=\"height\" :data=\"dataSource\" :scale=\"scale\" :padding=\"padding\">\n    <v-tooltip/>\n    <v-axis/>\n    <v-bar position=\"x*y\"/>\n  </v-chart>\n</div>\n", null]}