{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\HttpTrace.vue?vue&type=template&id=37bfd9d6", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\monitor\\HttpTrace.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-skeleton\", {\n    attrs: {\n      active: \"\",\n      loading: _vm.loading,\n      paragraph: {\n        rows: 17\n      }\n    }\n  }, [_c(\"a-card\", {\n    staticClass: \"card-area\",\n    attrs: {\n      bordered: false\n    }\n  }, [_c(\"a-alert\", {\n    attrs: {\n      type: \"info\",\n      showIcon: true\n    }\n  }, [_c(\"div\", {\n    attrs: {\n      slot: \"message\"\n    },\n    slot: \"message\"\n  }, [_vm._v(\"\\n        共追踪到 \" + _vm._s(_vm.dataSource.length) + \" 条近期HTTP请求记录\\n        \"), _c(\"a-divider\", {\n    attrs: {\n      type: \"vertical\"\n    }\n  }), _c(\"a\", {\n    on: {\n      click: _vm.handleClickUpdate\n    }\n  }, [_vm._v(\"立即刷新\")])], 1)]), _c(\"a-table\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    },\n    attrs: {\n      columns: _vm.columns,\n      dataSource: _vm.dataSource,\n      pagination: _vm.pagination,\n      loading: _vm.tableLoading,\n      scroll: {\n        x: 900\n      }\n    },\n    on: {\n      change: _vm.handleTableChange\n    },\n    scopedSlots: _vm._u([{\n      key: \"timeTaken\",\n      fn: function fn(text) {\n        return [text < 500 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"green\"\n          }\n        }, [_vm._v(_vm._s(text) + \" ms\")]) : text < 1000 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"cyan\"\n          }\n        }, [_vm._v(_vm._s(text) + \" ms\")]) : text < 1500 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"orange\"\n          }\n        }, [_vm._v(_vm._s(text) + \" ms\")]) : _c(\"a-tag\", {\n          attrs: {\n            color: \"red\"\n          }\n        }, [_vm._v(_vm._s(text) + \" ms\")])];\n      }\n    }, {\n      key: \"responseStatus\",\n      fn: function fn(text) {\n        return [text < 200 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"pink\"\n          }\n        }, [_vm._v(_vm._s(text) + \" \")]) : text < 201 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"green\"\n          }\n        }, [_vm._v(_vm._s(text) + \" \")]) : text < 399 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"cyan\"\n          }\n        }, [_vm._v(_vm._s(text) + \" \")]) : text < 403 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"orange\"\n          }\n        }, [_vm._v(_vm._s(text) + \" \")]) : text < 501 ? _c(\"a-tag\", {\n          attrs: {\n            color: \"red\"\n          }\n        }, [_vm._v(_vm._s(text) + \" \")]) : _c(\"span\", [_vm._v(_vm._s(text))])];\n      }\n    }, {\n      key: \"requestMethod\",\n      fn: function fn(text) {\n        return [text === \"GET\" ? _c(\"a-tag\", {\n          attrs: {\n            color: \"#87d068\"\n          }\n        }, [_vm._v(_vm._s(text))]) : text === \"POST\" ? _c(\"a-tag\", {\n          attrs: {\n            color: \"#2db7f5\"\n          }\n        }, [_vm._v(_vm._s(text))]) : text === \"PUT\" ? _c(\"a-tag\", {\n          attrs: {\n            color: \"#ffba5a\"\n          }\n        }, [_vm._v(_vm._s(text))]) : text === \"DELETE\" ? _c(\"a-tag\", {\n          attrs: {\n            color: \"#f50\"\n          }\n        }, [_vm._v(_vm._s(text))]) : _c(\"span\", [_vm._v(_vm._s(text) + \" ms\")])];\n      }\n    }])\n  })], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "active", "loading", "paragraph", "rows", "staticClass", "bordered", "type", "showIcon", "slot", "_v", "_s", "dataSource", "length", "on", "click", "handleClickUpdate", "staticStyle", "columns", "pagination", "tableLoading", "scroll", "x", "change", "handleTableChange", "scopedSlots", "_u", "key", "fn", "text", "color", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/modules/monitor/HttpTrace.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-skeleton\",\n    { attrs: { active: \"\", loading: _vm.loading, paragraph: { rows: 17 } } },\n    [\n      _c(\n        \"a-card\",\n        { staticClass: \"card-area\", attrs: { bordered: false } },\n        [\n          _c(\"a-alert\", { attrs: { type: \"info\", showIcon: true } }, [\n            _c(\n              \"div\",\n              { attrs: { slot: \"message\" }, slot: \"message\" },\n              [\n                _vm._v(\n                  \"\\n        共追踪到 \" +\n                    _vm._s(_vm.dataSource.length) +\n                    \" 条近期HTTP请求记录\\n        \"\n                ),\n                _c(\"a-divider\", { attrs: { type: \"vertical\" } }),\n                _c(\"a\", { on: { click: _vm.handleClickUpdate } }, [\n                  _vm._v(\"立即刷新\"),\n                ]),\n              ],\n              1\n            ),\n          ]),\n          _c(\"a-table\", {\n            staticStyle: { \"margin-top\": \"20px\" },\n            attrs: {\n              columns: _vm.columns,\n              dataSource: _vm.dataSource,\n              pagination: _vm.pagination,\n              loading: _vm.tableLoading,\n              scroll: { x: 900 },\n            },\n            on: { change: _vm.handleTableChange },\n            scopedSlots: _vm._u([\n              {\n                key: \"timeTaken\",\n                fn: function (text) {\n                  return [\n                    text < 500\n                      ? _c(\"a-tag\", { attrs: { color: \"green\" } }, [\n                          _vm._v(_vm._s(text) + \" ms\"),\n                        ])\n                      : text < 1000\n                      ? _c(\"a-tag\", { attrs: { color: \"cyan\" } }, [\n                          _vm._v(_vm._s(text) + \" ms\"),\n                        ])\n                      : text < 1500\n                      ? _c(\"a-tag\", { attrs: { color: \"orange\" } }, [\n                          _vm._v(_vm._s(text) + \" ms\"),\n                        ])\n                      : _c(\"a-tag\", { attrs: { color: \"red\" } }, [\n                          _vm._v(_vm._s(text) + \" ms\"),\n                        ]),\n                  ]\n                },\n              },\n              {\n                key: \"responseStatus\",\n                fn: function (text) {\n                  return [\n                    text < 200\n                      ? _c(\"a-tag\", { attrs: { color: \"pink\" } }, [\n                          _vm._v(_vm._s(text) + \" \"),\n                        ])\n                      : text < 201\n                      ? _c(\"a-tag\", { attrs: { color: \"green\" } }, [\n                          _vm._v(_vm._s(text) + \" \"),\n                        ])\n                      : text < 399\n                      ? _c(\"a-tag\", { attrs: { color: \"cyan\" } }, [\n                          _vm._v(_vm._s(text) + \" \"),\n                        ])\n                      : text < 403\n                      ? _c(\"a-tag\", { attrs: { color: \"orange\" } }, [\n                          _vm._v(_vm._s(text) + \" \"),\n                        ])\n                      : text < 501\n                      ? _c(\"a-tag\", { attrs: { color: \"red\" } }, [\n                          _vm._v(_vm._s(text) + \" \"),\n                        ])\n                      : _c(\"span\", [_vm._v(_vm._s(text))]),\n                  ]\n                },\n              },\n              {\n                key: \"requestMethod\",\n                fn: function (text) {\n                  return [\n                    text === \"GET\"\n                      ? _c(\"a-tag\", { attrs: { color: \"#87d068\" } }, [\n                          _vm._v(_vm._s(text)),\n                        ])\n                      : text === \"POST\"\n                      ? _c(\"a-tag\", { attrs: { color: \"#2db7f5\" } }, [\n                          _vm._v(_vm._s(text)),\n                        ])\n                      : text === \"PUT\"\n                      ? _c(\"a-tag\", { attrs: { color: \"#ffba5a\" } }, [\n                          _vm._v(_vm._s(text)),\n                        ])\n                      : text === \"DELETE\"\n                      ? _c(\"a-tag\", { attrs: { color: \"#f50\" } }, [\n                          _vm._v(_vm._s(text)),\n                        ])\n                      : _c(\"span\", [_vm._v(_vm._s(text) + \" ms\")]),\n                  ]\n                },\n              },\n            ]),\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,YAAY,EACZ;IAAEE,KAAK,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,OAAO,EAAEL,GAAG,CAACK,OAAO;MAAEC,SAAS,EAAE;QAAEC,IAAI,EAAE;MAAG;IAAE;EAAE,CAAC,EACxE,CACEN,EAAE,CACA,QAAQ,EACR;IAAEO,WAAW,EAAE,WAAW;IAAEL,KAAK,EAAE;MAAEM,QAAQ,EAAE;IAAM;EAAE,CAAC,EACxD,CACER,EAAE,CAAC,SAAS,EAAE;IAAEE,KAAK,EAAE;MAAEO,IAAI,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAK;EAAE,CAAC,EAAE,CACzDV,EAAE,CACA,KAAK,EACL;IAAEE,KAAK,EAAE;MAAES,IAAI,EAAE;IAAU,CAAC;IAAEA,IAAI,EAAE;EAAU,CAAC,EAC/C,CACEZ,GAAG,CAACa,EAAE,CACJ,iBAAiB,GACfb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,UAAU,CAACC,MAAM,CAAC,GAC7B,wBACJ,CAAC,EACDf,EAAE,CAAC,WAAW,EAAE;IAAEE,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,EAChDT,EAAE,CAAC,GAAG,EAAE;IAAEgB,EAAE,EAAE;MAAEC,KAAK,EAAElB,GAAG,CAACmB;IAAkB;EAAE,CAAC,EAAE,CAChDnB,GAAG,CAACa,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFZ,EAAE,CAAC,SAAS,EAAE;IACZmB,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCjB,KAAK,EAAE;MACLkB,OAAO,EAAErB,GAAG,CAACqB,OAAO;MACpBN,UAAU,EAAEf,GAAG,CAACe,UAAU;MAC1BO,UAAU,EAAEtB,GAAG,CAACsB,UAAU;MAC1BjB,OAAO,EAAEL,GAAG,CAACuB,YAAY;MACzBC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAI;IACnB,CAAC;IACDR,EAAE,EAAE;MAAES,MAAM,EAAE1B,GAAG,CAAC2B;IAAkB,CAAC;IACrCC,WAAW,EAAE5B,GAAG,CAAC6B,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,WAAW;MAChBC,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAE;QAClB,OAAO,CACLA,IAAI,GAAG,GAAG,GACN/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAQ;QAAE,CAAC,EAAE,CACzCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,KAAK,CAAC,CAC7B,CAAC,GACFA,IAAI,GAAG,IAAI,GACX/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAO;QAAE,CAAC,EAAE,CACxCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,KAAK,CAAC,CAC7B,CAAC,GACFA,IAAI,GAAG,IAAI,GACX/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAS;QAAE,CAAC,EAAE,CAC1CjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,KAAK,CAAC,CAC7B,CAAC,GACF/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAM;QAAE,CAAC,EAAE,CACvCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,KAAK,CAAC,CAC7B,CAAC,CACP;MACH;IACF,CAAC,EACD;MACEF,GAAG,EAAE,gBAAgB;MACrBC,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAE;QAClB,OAAO,CACLA,IAAI,GAAG,GAAG,GACN/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAO;QAAE,CAAC,EAAE,CACxCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,GAAG,CAAC,CAC3B,CAAC,GACFA,IAAI,GAAG,GAAG,GACV/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAQ;QAAE,CAAC,EAAE,CACzCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,GAAG,CAAC,CAC3B,CAAC,GACFA,IAAI,GAAG,GAAG,GACV/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAO;QAAE,CAAC,EAAE,CACxCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,GAAG,CAAC,CAC3B,CAAC,GACFA,IAAI,GAAG,GAAG,GACV/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAS;QAAE,CAAC,EAAE,CAC1CjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,GAAG,CAAC,CAC3B,CAAC,GACFA,IAAI,GAAG,GAAG,GACV/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAM;QAAE,CAAC,EAAE,CACvCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,GAAG,CAAC,CAC3B,CAAC,GACF/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC,CACvC;MACH;IACF,CAAC,EACD;MACEF,GAAG,EAAE,eAAe;MACpBC,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAE;QAClB,OAAO,CACLA,IAAI,KAAK,KAAK,GACV/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAU;QAAE,CAAC,EAAE,CAC3CjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,CAAC,CACrB,CAAC,GACFA,IAAI,KAAK,MAAM,GACf/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAU;QAAE,CAAC,EAAE,CAC3CjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,CAAC,CACrB,CAAC,GACFA,IAAI,KAAK,KAAK,GACd/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAU;QAAE,CAAC,EAAE,CAC3CjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,CAAC,CACrB,CAAC,GACFA,IAAI,KAAK,QAAQ,GACjB/B,EAAE,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE;YAAE8B,KAAK,EAAE;UAAO;QAAE,CAAC,EAAE,CACxCjC,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,CAAC,CACrB,CAAC,GACF/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACkB,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAC/C;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIE,eAAe,GAAG,EAAE;AACxBnC,MAAM,CAACoC,aAAa,GAAG,IAAI;AAE3B,SAASpC,MAAM,EAAEmC,eAAe", "ignoreList": []}]}