{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\MineWorks.vue?vue&type=style&index=0&id=9973fa86&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\MineWorks.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.filter-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 12px;\n  background: #f8f8f8;\n  border-radius: 4px;\n  \n  .filter-item {\n    margin-right: 15px;\n    display: flex;\n    align-items: center;\n    \n    .filter-label {\n      margin-right: 8px;\n      white-space: nowrap;\n    }\n  }\n}\n\n.app-list {\n  /deep/.ant-card-extra{\n    margin-left:0!important;\n    height: 55px;\n  }\n  /deep/.ant-card{\n    width: 300px;\n    display: inline-block;\n    margin: 20px;\n  }\n  /deep/.ant-card-body{\n    padding: 5px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    border-bottom: solid #e9e9e9 1px;\n    .ant-tag{\n      position: absolute;\n      margin: 5px;\n    }\n    .status-tag {\n      position: absolute;\n      right: 5px;\n      top: 5px;\n      margin: 0;\n      z-index: 1;\n    }\n    img {\n      width: 100%;\n      max-height: 100%;\n      min-height: 100px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n  /deep/.ant-card-actions li{\n    margin: 5px 0;\n  }\n  \n  /* 多种选择器组合，确保高优先级 */\n  /deep/ .delete-icon-wrapper.important-delete {\n    .anticon.delete-icon-red {\n      color: #ff4d4f !important;\n    }\n  }\n  \n  /deep/ .anticon.delete-icon-red {\n    color: #ff4d4f !important;\n  }\n  \n  /deep/ .important-delete .anticon {\n    color: #ff4d4f !important;\n  }\n  \n  /* 直接修改ant-design卡片操作区删除图标样式 */\n  /deep/ .ant-card-actions {\n    li:first-child {\n      .anticon-delete {\n        &.delete-icon-red {\n          color: #ff4d4f !important;\n        }\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["MineWorks.vue"], "names": [], "mappings": ";AAmOA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MineWorks.vue", "sourceRoot": "src/views/account/center/page", "sourcesContent": ["<template>\n  <div class=\"app-list\">\n    <!-- 添加筛选条件区域 -->\n    <div class=\"filter-container\">\n      <div class=\"filter-item\">\n        <span class=\"filter-label\">作品类型：</span>\n        <a-select\n          v-model=\"filterType\"\n          style=\"width: 120px\"\n          placeholder=\"选择作品类型\"\n          @change=\"handleFilterChange\"\n        >\n          <a-select-option value=\"\">全部</a-select-option>\n          <a-select-option value=\"2\">Scratch</a-select-option>\n          <a-select-option value=\"4\">Python</a-select-option>\n          <a-select-option value=\"5\">C++</a-select-option>\n        </a-select>\n      </div>\n      <div class=\"filter-item\">\n        <span class=\"filter-label\">作品状态：</span>\n        <a-select\n          v-model=\"filterStatus\"\n          style=\"width: 120px\"\n          placeholder=\"选择作品状态\"\n          @change=\"handleFilterChange\"\n        >\n          <a-select-option value=\"\">全部</a-select-option>\n          <a-select-option value=\"4\">精选作品</a-select-option>\n          <a-select-option value=\"3\">首页展示</a-select-option>\n          <a-select-option value=\"2\">已批改</a-select-option>\n          <a-select-option value=\"other\">其他</a-select-option>\n        </a-select>\n      </div>\n      <div class=\"filter-item\">\n        <a-button type=\"primary\" icon=\"reload\" @click=\"refreshWorks\">刷新</a-button>\n      </div>\n    </div>\n\n    <a-card hoverable v-for=\"item in filteredDataSource\" :key=\"item.id\">\n      <div slot=\"cover\" class=\"meta-cardInfo\">\n        <a-tag color=\"blue\">{{item.workType_dictText}}</a-tag>\n        <a-tag v-if=\"item.workStatus == 2\" class=\"status-tag\" color=\"green\">已批改</a-tag>\n        <a-tag v-if=\"item.workStatus == 3\" class=\"status-tag\" color=\"orange\">首页展示</a-tag>\n        <a-tag v-if=\"item.workStatus == 4\" class=\"status-tag\" color=\"purple\">精选作品</a-tag>\n        <a :href=\"getEditorHref(item)\" target=\"_blank\">\n          <img v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n          <img v-else src=\"@/assets/code.png\" alt=\"\">\n        </a>\n        </div>\n      <a-card-meta>\n        <a slot=\"description\" :href=\"getEditorHref(item)\" target=\"_blank\">\n          <h3><j-ellipsis :value=\"item.workName\" :length=\"35\" /></h3>\n        </a>\n      </a-card-meta>\n      <template class=\"ant-card-actions\" slot=\"actions\">\n        <a-popconfirm \n          :title=\"getDeleteConfirmTitle(item)\" \n          @confirm=\"() => handleDelete(item.id)\"\n        >\n          <span class=\"delete-icon-wrapper\" :class=\"{'important-delete': isImportantWork(item)}\">\n            <a-icon \n              type=\"delete\" \n              :style=\"isImportantWork(item) ? { color: '#ff4d4f !important', fontSize: '16px' } : {}\"\n              :class=\"{'delete-icon-red': isImportantWork(item)}\"\n            />\n          </span>\n        </a-popconfirm>\n        <a :href=\"getEditorHref(item)\" target=\"_blank\">\n          <a-icon type=\"edit\"/>\n        </a>\n        <a-popover trigger=\"click\" v-if=\"item.workType==1||item.workType==2\">\n          <template slot=\"content\">\n            <qrcode :value=\"url.shareUrl + item.id\" :size=\"250\"></qrcode>\n          </template>\n          <a><a-icon type=\"share-alt\"/></a>\n        </a-popover>\n      </template>\n    </a-card>\n    \n    <div v-if=\"filteredDataSource.length === 0\" style=\"text-align: center; margin-top: 20px;\">\n      暂无作品\n    </div>\n  </div>\n</template>\n\n<script>\nimport { deleteAction, getAction, downFile,getFileAccessHttpUrl } from '@/api/manage'\nimport QrCode from '@/components/tools/QrCode'\nimport JEllipsis from '@/components/jeecg/JEllipsis'\nexport default {\n  name: 'MineWorksCard',\n  components: {\n    qrcode: QrCode,\n    JEllipsis\n  },\n  data() {\n    return {\n      pagination: {\n        onChange: page => {\n          console.log(page);\n        },\n        pageSize: 12,\n      },\n      dataSource: [],\n      loading: false,\n      // 添加筛选相关的数据\n      filterType: '', // 作品类型筛选\n      filterStatus: '', // 作品状态筛选\n      url: {\n        list: '/teaching/teachingWork/mine',\n        delete: '/teaching/teachingWork/delete',\n        shareUrl: window._CONFIG['webURL'] + \"/work-detail?id=\",\n      }\n    }\n  },\n  computed: {\n    filteredDataSource() {\n      // 筛选逻辑\n      return this.dataSource.filter(item => {\n        // 只显示自由创作的作品\n        if(item.workScene !== 'create') return false;\n        \n        // 作品类型筛选\n        if(this.filterType && item.workType != this.filterType) return false;\n        \n        // 作品状态筛选\n        if(this.filterStatus) {\n          if(this.filterStatus === 'other') {\n            // \"其他\"状态为非2/3/4的状态\n            if(item.workStatus == 2 || item.workStatus == 3 || item.workStatus == 4) return false;\n          } else {\n            // 精选作品/首页展示/已批改状态的直接匹配\n            if(item.workStatus != this.filterStatus) return false;\n          }\n        }\n        \n        return true;\n      });\n    }\n  },\n  mounted() {\n    this.getWorkList()\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    getWorkList: function() {\n      var that = this;\n      that.loading = true\n      getAction(that.url.list, null).then(res => {\n        if (res.success) {\n          that.dataSource = res.result.records\n          // 检查作品状态并调试输出\n          if (that.dataSource && that.dataSource.length > 0) {\n            console.log(\"作品数据示例:\", that.dataSource[0]);\n            that.dataSource.forEach(item => {\n              if (item.workStatus == 2 || item.workStatus == 3 || item.workStatus == 4) {\n                console.log(`发现特殊状态作品: ID=${item.id}, 状态=${item.workStatus}, 名称=${item.workName}`);\n              }\n            });\n          }\n        }\n        if (res.code === 510) {\n          that.$message.warning(res.message)\n        }\n        that.loading = false\n      })\n    },\n    // 筛选变化处理函数\n    handleFilterChange() {\n      console.log('筛选条件变化:', this.filterType, this.filterStatus);\n    },\n    // 刷新作品列表\n    refreshWorks() {\n      this.$message.info('已刷新完毕');\n      this.getWorkList();\n    },\n    isImportantWork(item) {\n      // 确保数据类型转换，避免严格比较的问题\n      const status = parseInt(item.workStatus);\n      return status === 2 || status === 3 || status === 4;\n    },\n    getDeleteConfirmTitle(item) {\n      const status = parseInt(item.workStatus);\n      if (status === 2) {\n        return \"此作品已批改完成，确定要删除吗?\";\n      } else if (status === 3) {\n        return \"此作品正在首页展示中，确定要删除吗?\";\n      } else if (status === 4) {\n        return \"此作品是精选作品，确定要删除吗?\";\n      } else {\n        return \"确定删除吗?\";\n      }\n    },\n    handleDelete: function(id){\n      var that = this;\n      deleteAction(that.url.delete, {id: id}).then((res) => {\n        if (res.success) {\n          that.$message.success(res.message);\n          that.getWorkList();\n        } else {\n          that.$message.warning(res.message);\n        }\n      });\n    },\n    getEditorHref(item){\n      switch(item.workType){\n        case '1':\n          return '/scratch3/index.html?workId='+item.id+'&workName='+encodeURIComponent(item.workName)\n        case '2':\n          return '/scratch3/index.html?workId='+item.id+'&workName='+encodeURIComponent(item.workName)\n        case '3':\n          return '/scratchjr/editor.html?mode=edit&workFile=' + item.workFileKey_url + '&workName='+encodeURIComponent(item.workName)\n        case '4':\n          return '/python/index.html?workId=' + item.id + '&workName='+encodeURIComponent(item.workName)\n        case '5':  // 添加C++支持\n          return '/cpp/index.html?workId=' + item.id + '&workName='+encodeURIComponent(item.workName)\n        case '10':\n          return '/blockly/index.html?lang=zh-hans&workId=' + item.id + '&workName='+encodeURIComponent(item.workName)\n        default:\n          return item.workFileKey_url\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.filter-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 12px;\n  background: #f8f8f8;\n  border-radius: 4px;\n  \n  .filter-item {\n    margin-right: 15px;\n    display: flex;\n    align-items: center;\n    \n    .filter-label {\n      margin-right: 8px;\n      white-space: nowrap;\n    }\n  }\n}\n\n.app-list {\n  /deep/.ant-card-extra{\n    margin-left:0!important;\n    height: 55px;\n  }\n  /deep/.ant-card{\n    width: 300px;\n    display: inline-block;\n    margin: 20px;\n  }\n  /deep/.ant-card-body{\n    padding: 5px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    border-bottom: solid #e9e9e9 1px;\n    .ant-tag{\n      position: absolute;\n      margin: 5px;\n    }\n    .status-tag {\n      position: absolute;\n      right: 5px;\n      top: 5px;\n      margin: 0;\n      z-index: 1;\n    }\n    img {\n      width: 100%;\n      max-height: 100%;\n      min-height: 100px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n  /deep/.ant-card-actions li{\n    margin: 5px 0;\n  }\n  \n  /* 多种选择器组合，确保高优先级 */\n  /deep/ .delete-icon-wrapper.important-delete {\n    .anticon.delete-icon-red {\n      color: #ff4d4f !important;\n    }\n  }\n  \n  /deep/ .anticon.delete-icon-red {\n    color: #ff4d4f !important;\n  }\n  \n  /deep/ .important-delete .anticon {\n    color: #ff4d4f !important;\n  }\n  \n  /* 直接修改ant-design卡片操作区删除图标样式 */\n  /deep/ .ant-card-actions {\n    li:first-child {\n      .anticon-delete {\n        &.delete-icon-red {\n          color: #ff4d4f !important;\n        }\n      }\n    }\n  }\n}\n</style>"]}]}