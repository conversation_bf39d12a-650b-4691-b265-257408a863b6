{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoTabsModal.vue?vue&type=style&index=0&id=1304afe1&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoTabsModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.ant-modal-body {\n  padding: 8px!important;\n}\n", {"version": 3, "sources": ["JeecgDemoTabsModal.vue"], "names": [], "mappings": ";AA6RA;AACA;AACA", "file": "JeecgDemoTabsModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"1200\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-card class=\"card\"  :bordered=\"false\">\n          <a-row class=\"form-row\" :gutter=\"16\">\n            <a-col :lg=\"8\">\n              <a-form-item label=\"任务名\">\n                <a-input placeholder=\"请输入任务名称\"  v-decorator=\"[ 'task.name', {rules: [{ required: true, message: '请输入任务名称', whitespace: true}]} ]\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :lg=\"8\">\n              <a-form-item label=\"任务描述\">\n                <a-input placeholder=\"请输入任务描述\"  v-decorator=\"['task.description', {rules: [{ required: true, message: '请输入任务描述', whitespace: true}]} ]\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :lg=\"8\">\n              <a-form-item label=\"执行人\">\n                <a-select placeholder=\"请选择执行人\" v-decorator=\"['task.executor',{rules: [{ required: true, message: '请选择执行人'}]}  ]\">\n                  <a-select-option value=\"黄丽丽\">黄丽丽</a-select-option>\n                  <a-select-option value=\"李大刀\">李大刀</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n          </a-row>\n          <a-row class=\"form-row\" :gutter=\"16\">\n            <a-col :lg=\"8\">\n              <a-form-item label=\"责任人\">\n                <a-select placeholder=\"请选择责任人\" v-decorator=\"['task.manager',  {rules: [{ required: true, message: '请选择责任人'}]} ]\">\n                  <a-select-option value=\"王伟\">王伟</a-select-option>\n                  <a-select-option value=\"李红军\">李红军</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n            <a-col :lg=\"8\">\n              <a-form-item label=\"提醒时间\">\n                <a-time-picker style=\"width: 100%\" v-decorator=\"['task.time', {rules: [{ required: true, message: '请选择提醒时间'}]} ]\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :lg=\"8\">\n              <a-form-item\n                label=\"任务类型\">\n                <a-select placeholder=\"请选择任务类型\" v-decorator=\"['task.type', {rules: [{ required: true, message: '请选择任务类型'}]} ]\">\n                  <a-select-option value=\"定时执行\">定时执行</a-select-option>\n                  <a-select-option value=\"周期执行\">周期执行</a-select-option>\n                </a-select>\n              </a-form-item>\n            </a-col>\n          </a-row>\n        </a-card>\n\n        <a-tabs defaultActiveKey=\"1\" >\n          <a-tab-pane tab=\"Tab 1\" key=\"1\">\n\n            <a-table :columns=\"columns\" :dataSource=\"data\" :pagination=\"false\" size=\"middle\">\n              <template v-for=\"(col, i) in ['name', 'workId', 'department']\" :slot=\"col\" slot-scope=\"text, record, index\">\n                <a-tooltip  title=\"必填项\" :defaultVisible=\"false\" overlayStyle=\"{ color: 'red' }\">\n                  <a-input :key=\"col\" v-if=\"record.editable\" style=\"margin: -5px 0\"  :value=\"text\" :placeholder=\"columns[i].title\" @change=\"e => handlerRowChange(e.target.value, record.key, col)\"/>\n                <template v-else>{{ text }}</template>\n                </a-tooltip>\n              </template>\n              <template slot=\"operation\" slot-scope=\"text, record, index\">\n                <template v-if=\"record.editable\">\n                  <span v-if=\"record.isNew\">\n                    <a @click=\"saveRow(record.key)\">添加</a>\n                    <a-divider type=\"vertical\"/>\n                    <a-popconfirm title=\"是否要删除此行？\" @confirm=\"removeRow(record.key)\"><a>删除</a></a-popconfirm>\n                  </span>\n                  <span v-else>\n                    <a @click=\"saveRow(record.key)\">保存</a>\n                    <a-divider type=\"vertical\"/>\n                    <a @click=\"cancelEditRow(record.key)\">取消</a>\n                  </span>\n                </template>\n                <span v-else>\n                  <a @click=\"editRow(record.key)\">编辑</a>\n                  <a-divider type=\"vertical\"/>\n                  <a-popconfirm title=\"是否要删除此行？\" @confirm=\"removeRow(record.key)\"><a>删除</a></a-popconfirm>\n                </span>\n              </template>\n            </a-table>\n\n            <a-button style=\"width: 100%; margin-top: 16px; margin-bottom: 8px\" type=\"dashed\" icon=\"plus\" @click=\"newRow\">新增成员</a-button>\n          </a-tab-pane>\n          <a-tab-pane tab=\"Tab 2\" key=\"2\" forceRender>\n            Content of Tab Pane 2\n          </a-tab-pane>\n          <a-tab-pane tab=\"Tab 3\" key=\"3\">Content of Tab Pane 3</a-tab-pane>\n        </a-tabs>\n\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgDemoTabsModal\",\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        // table\n        columns: [\n          {\n            title: '成员姓名',\n            dataIndex: 'name',\n            key: 'name',\n            width: '20%',\n            scopedSlots: {customRender: 'name'}\n          },\n          {\n            title: '工号',\n            dataIndex: 'workId',\n            key: 'workId',\n            width: '20%',\n            scopedSlots: {customRender: 'workId'}\n          },\n          {\n            title: '所属部门',\n            dataIndex: 'department',\n            key: 'department',\n            width: '40%',\n            scopedSlots: {customRender: 'department'}\n          },\n          {\n            title: '操作',\n            key: 'action',\n            scopedSlots: {customRender: 'operation'}\n          }\n        ],\n        data: [\n          {\n            key: '1',\n            name: '小明',\n            workId: '001',\n            editable: false,\n            department: '行政部'\n          },\n          {\n            key: '2',\n            name: '李莉',\n            workId: '002',\n            editable: false,\n            department: 'IT部'\n          },\n          {\n            key: '3',\n            name: '王小帅',\n            workId: '003',\n            editable: false,\n            department: '财务部'\n          }\n        ],\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: \"/test/jeecgDemo/add\",\n          edit: \"/test/jeecgDemo/edit\",\n        },\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'name', 'keyWord', 'sex', 'age', 'email', 'content'))\n          //时间格式化\n          this.form.setFieldsValue({punchTime: this.model.punchTime ? moment(this.model.punchTime, 'YYYY-MM-DD HH:mm:ss') : null})\n          this.form.setFieldsValue({birthday: this.model.birthday ? moment(this.model.birthday) : null})\n        });\n\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.punchTime = formData.punchTime ? formData.punchTime.format('YYYY-MM-DD HH:mm:ss') : null;\n            formData.birthday = formData.birthday ? formData.birthday.format() : null;\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      },\n      newRow () {\n        // 通过时间戳生成 UUID\n        let uuid = Math.round(new Date().getTime()).toString();\n        console.log('uuid: '+ uuid)\n        this.data.push({\n          key: uuid,\n          name: '',\n          workId: '',\n          department: '',\n          editable: true,\n          isNew: true\n        })\n      },\n      removeRow (key) {\n        const newData = this.data.filter(item => item.key !== key)\n        this.data = newData\n      },\n      saveRow (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n        target.isNew = false\n      },\n      handlerRowChange (value, key, column) {\n        const newData = [...this.data]\n        const target = newData.filter(item => key === item.key)[0]\n        if (target) {\n          target[column] = value\n          this.data = newData\n        }\n      },\n      editRow (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = !target.editable\n      },\n      cancelEditRow (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n      },\n    }\n  }\n</script>\n\n<style scoped>\n  .ant-modal-body {\n    padding: 8px!important;\n  }\n</style>"]}]}