{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue", "mtime": 1752749894266}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./PracticeModeSelector.vue?vue&type=template&id=1b0d512f&scoped=true\"\nimport script from \"./PracticeModeSelector.vue?vue&type=script&lang=js\"\nexport * from \"./PracticeModeSelector.vue?vue&type=script&lang=js\"\nimport style0 from \"./PracticeModeSelector.vue?vue&type=style&index=0&id=1b0d512f&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1b0d512f\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1b0d512f')) {\n      api.createRecord('1b0d512f', component.options)\n    } else {\n      api.reload('1b0d512f', component.options)\n    }\n    module.hot.accept(\"./PracticeModeSelector.vue?vue&type=template&id=1b0d512f&scoped=true\", function () {\n      api.rerender('1b0d512f', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/examSystem/components/PracticeModeSelector.vue\"\nexport default component.exports"]}