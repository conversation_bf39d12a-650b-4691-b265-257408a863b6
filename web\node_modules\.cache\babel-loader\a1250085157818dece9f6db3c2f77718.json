{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\PdfPreviewModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\PdfPreviewModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport { ACCESS_TOKEN } from \"@/store/mutation-types\";\nexport default {\n  name: \"PdfPreviewModal\",\n  data: function data() {\n    return {\n      url: window._CONFIG['pdfDomainURL'],\n      id: \"pdfPreviewIframe\",\n      headers: {}\n    };\n  },\n  created: function created() {\n    var token = Vue.ls.get(ACCESS_TOKEN);\n    this.headers = {\n      \"X-Access-Token\": token\n    };\n  },\n  computed: {},\n  mounted: function mounted() {\n    window.addEventListener('message', this.handleScanFileMessage);\n  },\n  methods: {\n    handleScanFileMessage: function handleScanFileMessage(event) {\n      // 根据上面制定的结构来解析iframe内部发回来的数据\n      var data = event.data;\n      console.log(data);\n    },\n    previewFiles: function previewFiles(title, token) {\n      var iframe = document.getElementById(\"pdfPreviewIframe\");\n      var json = {\n        \"title\": title,\n        \"token\": token\n      };\n      iframe.contentWindow.postMessage(json, \"*\");\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "ACCESS_TOKEN", "name", "data", "url", "window", "_CONFIG", "id", "headers", "created", "token", "ls", "get", "computed", "mounted", "addEventListener", "handleScanFileMessage", "methods", "event", "console", "log", "previewFiles", "title", "iframe", "document", "getElementById", "json", "contentWindow", "postMessage"], "sources": ["src/views/jeecg/modules/PdfPreviewModal.vue"], "sourcesContent": ["<template>\n  <div style=\"display: none\">\n    <iframe\n      :id=\"id\"\n      :src=\"url\"\n      frameborder=\"0\"\n      width=\"100%\"\n      height=\"550px\"\n      scrolling=\"auto\">\n    </iframe>\n  </div>\n</template>\n\n<script>\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n\n  export default {\n    name: \"PdfPreviewModal\",\n    data () {\n      return {\n        url:  window._CONFIG['pdfDomainURL'],\n        id:\"pdfPreviewIframe\",\n        headers:{}\n      }\n    },\n    created () {\n      const token = Vue.ls.get(ACCESS_TOKEN);\n      this.headers = {\"X-Access-Token\":token}\n    },\n    computed:{\n\n    },\n    mounted(){\n      window.addEventListener('message', this.handleScanFileMessage);\n    },\n    methods: {\n      handleScanFileMessage (event) {\n        // 根据上面制定的结构来解析iframe内部发回来的数据\n        const data = event.data;\n         console.log(data);\n      },\n\n      previewFiles (title,token) {\n        var iframe = document.getElementById(\"pdfPreviewIframe\");\n        var json = {\"title\":title,\"token\":token};\n        iframe.contentWindow.postMessage(json, \"*\");\n      },\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAcA,OAAAA,GAAA;AACA,SAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA,EAAAC,MAAA,CAAAC,OAAA;MACAC,EAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,GAAAV,GAAA,CAAAW,EAAA,CAAAC,GAAA,CAAAX,YAAA;IACA,KAAAO,OAAA;MAAA,kBAAAE;IAAA;EACA;EACAG,QAAA,GAEA;EACAC,OAAA,WAAAA,QAAA;IACAT,MAAA,CAAAU,gBAAA,iBAAAC,qBAAA;EACA;EACAC,OAAA;IACAD,qBAAA,WAAAA,sBAAAE,KAAA;MACA;MACA,IAAAf,IAAA,GAAAe,KAAA,CAAAf,IAAA;MACAgB,OAAA,CAAAC,GAAA,CAAAjB,IAAA;IACA;IAEAkB,YAAA,WAAAA,aAAAC,KAAA,EAAAZ,KAAA;MACA,IAAAa,MAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAC,IAAA;QAAA,SAAAJ,KAAA;QAAA,SAAAZ;MAAA;MACAa,MAAA,CAAAI,aAAA,CAAAC,WAAA,CAAAF,IAAA;IACA;EAEA;AACA", "ignoreList": []}]}