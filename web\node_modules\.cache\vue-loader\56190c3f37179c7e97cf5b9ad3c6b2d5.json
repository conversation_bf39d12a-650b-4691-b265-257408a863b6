{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue?vue&type=template&id=295f035e&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\StepForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <a-steps class=\"steps\" :current=\"currentTab\">\n    <a-step title=\"填写转账信息\" />\n    <a-step title=\"确认转账信息\" />\n    <a-step title=\"完成\" />\n  </a-steps>\n  <div class=\"content\">\n    <step1 v-if=\"currentTab === 0\" @nextStep=\"nextStep\"/>\n    <step2 v-if=\"currentTab === 1\" @nextStep=\"nextStep\" @prevStep=\"prevStep\"/>\n    <step3 v-if=\"currentTab === 2\" @prevStep=\"prevStep\" @finish=\"finish\"/>\n  </div>\n</a-card>\n", null]}