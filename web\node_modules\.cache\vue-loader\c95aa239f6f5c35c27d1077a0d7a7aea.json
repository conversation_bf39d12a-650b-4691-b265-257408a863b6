{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue", "mtime": 1753199398217}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport { mapActions, mapGetters } from 'vuex'\nimport TMenu from '@/components/menu/tmenu'\nimport { getFileAccessHttpUrl } from \"@/api/manage\"\nimport SoftwareDownload from './SoftwareDownload'\nimport ShoppingModal from './ShoppingModal'\nimport HeaderNotice from '@/components/tools/HeaderNotice'\n\nexport default {\n  components: {\n    TMenu,\n    SoftwareDownload,\n    ShoppingModal,\n    HeaderNotice\n  },\n  data() {\n    return {\n      menus: [],\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      menuFixed: false\n    }\n  },\n  created() {\n    this.menus = this.$store.getters.menuList\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo\n      this.avatarUrl = this.logo\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n       this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if(this.getFileAccessHttpUrl(this.avatar())){\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    } \n\n    // 监听软件下载菜单点击事件\n    window.addEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 监听商城菜单点击事件\n    window.addEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  mounted() {\n    window.addEventListener('scroll', this.handleScroll)\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n    // 移除软件下载菜单点击事件监听\n    window.removeEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 移除商城菜单点击事件监听\n    window.removeEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  methods:{\n    ...mapActions([\"Logout\"]),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getFileAccessHttpUrl,\n    handleScroll(){\n      let scrollTop = document.documentElement.scrollTop\n      if (scrollTop >= 105) {\n        this.menuFixed = true\n      } else {\n        this.menuFixed = false\n      }\n    },\n    enter() {\n      this.$router.push('/account/settings/base')\n    },\n    handleLogout() {\n      const that = this;\n      this.$confirm({\n        title: \"提示\",\n        content: \"真的要注销登录吗 ?\",\n        onOk() {\n          return that\n            .Logout({})\n            .then(() => {\n              window.location.reload()\n            })\n            .catch((err) => {\n              that.$message.error({\n                title: \"错误\",\n                description: err.message,\n              });\n            });\n        },\n        onCancel() {},\n      });\n    },\n    showSoftwareDownload() {\n      this.$refs.softwareDownload.showModal()\n    },\n    showShoppingModal() {\n      this.$refs.shoppingModal.showModal()\n    }\n  }\n}\n", {"version": 3, "sources": ["Header.vue"], "names": [], "mappings": ";AA0BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Header.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\n  <div :class=\"['header', menuFixed?'menu-fixed':'']\">\n    <router-link :to=\"{ path: '/home' }\">\n      <img class=\"logo\" :src=\"logo\" alt=\"\" />\n    </router-link>\n    <t-menu class=\"menu\" mode=\"horizontal\" :menu=\"menus\"></t-menu>\n    <div class=\"header-avatar\">\n      <header-notice class=\"action\"/>\n      <img class=\"avatar\" :src=\"avatarUrl\" @click=\"enter\" alt=\"\" />\n      <span v-if=\"$store.state.user.info\">\n        <span @click=\"enter\">{{ $store.state.user.info.realname }}</span>\n        <a-divider type=\"vertical\" />\n        <span @click=\"handleLogout\">退出</span>\n      </span>\n      <span v-else>\n        <span @click=\"enter\">登录</span>\n        <a-divider type=\"vertical\" />\n        <span @click=\"enter\">注册</span>\n      </span>\n    </div>\n    \n    <software-download ref=\"softwareDownload\" />\n    <shopping-modal ref=\"shoppingModal\" />\n  </div>\n</template>\n<script>\nimport { mapActions, mapGetters } from 'vuex'\nimport TMenu from '@/components/menu/tmenu'\nimport { getFileAccessHttpUrl } from \"@/api/manage\"\nimport SoftwareDownload from './SoftwareDownload'\nimport ShoppingModal from './ShoppingModal'\nimport HeaderNotice from '@/components/tools/HeaderNotice'\n\nexport default {\n  components: {\n    TMenu,\n    SoftwareDownload,\n    ShoppingModal,\n    HeaderNotice\n  },\n  data() {\n    return {\n      menus: [],\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n      menuFixed: false\n    }\n  },\n  created() {\n    this.menus = this.$store.getters.menuList\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo\n      this.avatarUrl = this.logo\n    }\n    if (this.$store.getters.sysConfig.logo2 && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.logo2\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n       this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if(this.getFileAccessHttpUrl(this.avatar())){\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    } \n\n    // 监听软件下载菜单点击事件\n    window.addEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 监听商城菜单点击事件\n    window.addEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  mounted() {\n    window.addEventListener('scroll', this.handleScroll)\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n    // 移除软件下载菜单点击事件监听\n    window.removeEventListener('showSoftwareDownload', this.showSoftwareDownload)\n    // 移除商城菜单点击事件监听\n    window.removeEventListener('showShoppingModal', this.showShoppingModal)\n  },\n  methods:{\n    ...mapActions([\"Logout\"]),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getFileAccessHttpUrl,\n    handleScroll(){\n      let scrollTop = document.documentElement.scrollTop\n      if (scrollTop >= 105) {\n        this.menuFixed = true\n      } else {\n        this.menuFixed = false\n      }\n    },\n    enter() {\n      this.$router.push('/account/settings/base')\n    },\n    handleLogout() {\n      const that = this;\n      this.$confirm({\n        title: \"提示\",\n        content: \"真的要注销登录吗 ?\",\n        onOk() {\n          return that\n            .Logout({})\n            .then(() => {\n              window.location.reload()\n            })\n            .catch((err) => {\n              that.$message.error({\n                title: \"错误\",\n                description: err.message,\n              });\n            });\n        },\n        onCancel() {},\n      });\n    },\n    showSoftwareDownload() {\n      this.$refs.softwareDownload.showModal()\n    },\n    showShoppingModal() {\n      this.$refs.shoppingModal.showModal()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"less\">\n.header {\n  padding: 15px;\n  line-height: 30px;\n}\n.logo {\n  max-height: 50px;\n  width: auto;\n  margin-right: 20px;\n  display: inline-block;\n}\n.brand {\n  display: inline-block;\n  vertical-align: middle;\n}\n.brand-title {\n  color: white;\n  font-size: 30px;\n  text-shadow: 0 0 5px #282828;\n  margin-bottom: 10px;\n}\n.brand-desc {\n  color: white;\n  font-size: 18px;\n  font-style: italic;\n}\n.menu-fixed{\n    position: fixed;\n    top: 0px;\n    z-index: 99;\n    padding-bottom: 10px;\n    width: 100%;\n    background: radial-gradient(ellipse at top left, #005dff 10%, #23aeffd9 67%);\n}\n.menu {\n  display: inline-block;\n  background: transparent;\n  max-width: 1200px;\n  margin: auto;\n  .ant-menu-submenu,\n  /deep/.ant-menu-item > a,\n  /deep/.ant-menu-submenu-title > a {\n    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;\n    font-weight: 600;\n    font-size: 16px;\n    color: white;\n  }\n  .ant-menu-submenu-active,\n  .ant-menu-item-active {\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 15px;\n    border-bottom: none !important;\n  }\n}\n.ant-menu-horizontal {\n  border-bottom: none;\n}\n\n.header-avatar{\n  padding: 5px 20px;\n  float: right;\n  cursor: pointer;\n  .ant-avatar {\n    margin-right: 5px;\n  }\n  .avatar{\n    margin-right: 5px;\n    margin-bottom: 5px;\n    max-height: 30px;\n  }\n  span {\n    color: #fff;\n    font-weight: 700;\n  }\n  .action {\n    display: inline-block;\n    margin-right: 10px;\n    vertical-align: middle;\n    \n    /deep/ .header-notice .ant-badge-count {\n      box-shadow: 0 0 0 1px #fff;\n    }\n    \n    /deep/ .header-notice {\n      color: #fff;\n      font-size: 16px;\n      \n      .anticon {\n        padding: 4px;\n        font-size: 18px;\n      }\n      \n      &:hover {\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n      }\n    }\n  }\n}\n</style>"]}]}