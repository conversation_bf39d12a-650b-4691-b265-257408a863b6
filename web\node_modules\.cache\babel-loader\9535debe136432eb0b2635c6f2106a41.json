{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import splitPane from 'vue-splitpane';\nimport SplitPanelA from './SplitPanelA';\nimport SplitPanelB from './SplitPanelB';\nexport default {\n  name: \"SplitPanelModal\",\n  components: {\n    splitPane: splitPane,\n    SplitPanelA: SplitPanelA,\n    SplitPanelB: SplitPanelB\n  },\n  data: function data() {\n    return {\n      visible: false,\n      bodyStyle: {\n        padding: \"0\",\n        height: window.innerHeight - 150 + \"px\"\n      },\n      modalWidth: 800\n    };\n  },\n  created: function created() {\n    this.modalWidth = window.innerWidth - 0;\n  },\n  methods: {\n    show: function show() {\n      this.visible = true;\n    },\n    handleOk: function handleOk() {},\n    handleCancel: function handleCancel() {\n      this.visible = false;\n    }\n  }\n};", {"version": 3, "names": ["splitPane", "SplitPanelA", "SplitPanelB", "name", "components", "data", "visible", "bodyStyle", "padding", "height", "window", "innerHeight", "modalWidth", "created", "innerWidth", "methods", "show", "handleOk", "handleCancel"], "sources": ["src/views/jeecg/modules/SplitPanelModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"分屏\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    :bodyStyle=\"bodyStyle\"\n    style=\"top: 0px;\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <split-pane :min-percent='20' :default-percent='50' split=\"vertical\">\n      <template slot=\"paneL\">\n        <split-panel-a></split-panel-a>\n      </template>\n      <template slot=\"paneR\">\n        <split-panel-b></split-panel-b>\n      </template>\n    </split-pane>\n\n  </a-modal>\n</template>\n\n<script>\n  import splitPane from 'vue-splitpane'\n  import SplitPanelA from './SplitPanelA'\n  import SplitPanelB from './SplitPanelB'\n  export default {\n    name: \"SplitPanelModal\",\n    components:{\n      splitPane,\n      SplitPanelA,\n      SplitPanelB\n    },\n    data () {\n      return {\n        visible: false,\n        bodyStyle:{\n          padding: \"0\",\n          height:(window.innerHeight-150)+\"px\"\n        },\n        modalWidth:800,\n      }\n    },\n    created () {\n      this.modalWidth = window.innerWidth-0;\n    },\n    methods: {\n      show () {\n        this.visible = true;\n      },\n      handleOk(){\n\n      },\n      handleCancel () {\n        this.visible = false;\n      },\n    }\n  }\n</script>\n\n<style scoped>\n</style>"], "mappings": "AAwBA,OAAAA,SAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAJ,SAAA,EAAAA,SAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,WAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,OAAA;QACAC,MAAA,EAAAC,MAAA,CAAAC,WAAA;MACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,UAAA,GAAAF,MAAA,CAAAI,UAAA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAV,OAAA;IACA;IACAW,QAAA,WAAAA,SAAA,GAEA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAZ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}