{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\UserEnter.vue", "mtime": 1753199441584}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport Vue from 'vue';\nimport { mapActions, mapGetters } from 'vuex';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { getFileAccessHttpUrl } from \"@/api/manage\";\nexport default {\n  data: function data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      token: '',\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png'\n    };\n  },\n  created: function created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN);\n    if (this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain) {\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + \"/\" + this.$store.getters.sysConfig.logo2;\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n      this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar;\n    }\n    if (this.getFileAccessHttpUrl(this.avatar())) {\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar());\n    }\n  },\n  methods: _objectSpread(_objectSpread(_objectSpread({}, mapActions([\"Logout\"])), mapGetters(['nickname', 'avatar', 'userInfo'])), {}, {\n    getFileAccessHttpUrl: getFileAccessHttpUrl,\n    login: function login() {\n      this.$router.push('/user/login');\n    },\n    enter: function enter() {\n      this.$router.push('/account/settings/base');\n    },\n    changeAccount: function changeAccount() {\n      var that = this;\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk: function onOk() {\n          return that.Logout({}).then(function () {\n            window.location.href = \"/user/login\";\n          }).catch(function (err) {\n            that.$message.error({\n              title: '错误',\n              description: err.message\n            });\n          });\n        },\n        onCancel: function onCancel() {}\n      });\n    }\n  })\n};", {"version": 3, "names": ["<PERSON><PERSON>", "mapActions", "mapGetters", "ACCESS_TOKEN", "getFileAccessHttpUrl", "data", "brandName", "$store", "getters", "sysConfig", "token", "logo", "logo2", "avatarUrl", "created", "ls", "get", "qiniuDomain", "avatar", "methods", "_objectSpread", "login", "$router", "push", "enter", "changeAccount", "that", "$confirm", "title", "content", "onOk", "Logout", "then", "window", "location", "href", "catch", "err", "$message", "error", "description", "message", "onCancel"], "sources": ["src/views/home/<USER>/UserEnter.vue"], "sourcesContent": ["<template>\n  <div class=\"user-enter\">\n    <div v-if=\"token\">\n      <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"avatarUrl\" />\n      <h3>欢迎您，{{ nickname() }}</h3>\n      <a-button type=\"primary\" @click=\"enter\">进入系统</a-button>\n      <a-button type=\"dashed\" @click=\"changeAccount\">切换账号</a-button>\n    </div>\n    <div v-else>\n      <a-avatar shape=\"square\" class=\"avatar\" :size=\"100\" :src=\"logo2\" />\n      <h3 class=\"welcome\">欢迎来到{{ brandName }}</h3>\n      <a-button type=\"dashed\" @click=\"login\">登录/注册</a-button>\n    </div>\n  </div>\n</template>\n<script>\nimport Vue from 'vue'\nimport { mapActions, mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { getFileAccessHttpUrl } from \"@/api/manage\"\nexport default {\n  data() {\n    return {\n      brandName: this.$store.getters.sysConfig.brandName,\n      token: '',\n      logo: '/logo.png',\n      logo2: '/logo.png',\n      avatarUrl: '/logo.png',\n    }\n  },\n  created() {\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    if(this.$store.getters.sysConfig.logo && this.$store.getters.sysConfig.qiniuDomain){\n      this.logo2 = this.$store.getters.sysConfig.qiniuDomain + \"/\" + this.$store.getters.sysConfig.logo2\n    }\n    if (this.$store.getters.sysConfig.avatar && this.$store.getters.sysConfig.qiniuDomain) {\n       this.avatarUrl = this.$store.getters.sysConfig.qiniuDomain + '/' + this.$store.getters.sysConfig.avatar\n    }\n    if(this.getFileAccessHttpUrl(this.avatar())){\n      this.avatarUrl = this.getFileAccessHttpUrl(this.avatar())\n    }\n  },\n  methods: {\n    ...mapActions([\"Logout\"]),\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    getFileAccessHttpUrl,\n    login(){\n      this.$router.push('/user/login')\n    },\n    enter() {\n      this.$router.push('/account/settings/base')\n    },\n    changeAccount(){\n      const that = this\n      this.$confirm({\n        title: '提示',\n        content: '确定要退出当前账号并登录新的账号吗 ?',\n        onOk() {\n          return that.Logout({}).then(() => {\n            window.location.href=\"/user/login\";\n          }).catch(err => {\n            that.$message.error({\n              title: '错误',\n              description: err.message\n            })\n          })\n        },\n        onCancel() {\n        },\n      });\n    }\n  },\n}\n</script>\n<style scoped>\n.user-enter {\n  background: url(/img/login-bg.png) no-repeat;\n  background-size: 100% 100%;\n  border-radius: 10px;\n  width: 250px;\n  min-height: 360px;\n  text-align: center;\n  padding-top: 110px;\n  padding-bottom: 20px;\n  line-height: 50px;\n}\n.ant-btn {\n  width: 80%;\n}\n.welcome{\n  padding: 0 20px;\n  line-height: 30px;\n}\n</style>"], "mappings": ";;;;;;AAgBA,OAAAA,GAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,YAAA;AACA,SAAAC,oBAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,OAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAH,SAAA;MACAI,KAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAJ,KAAA,GAAAV,GAAA,CAAAe,EAAA,CAAAC,GAAA,CAAAb,YAAA;IACA,SAAAI,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAE,IAAA,SAAAJ,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAQ,WAAA;MACA,KAAAL,KAAA,QAAAL,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAQ,WAAA,cAAAV,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAG,KAAA;IACA;IACA,SAAAL,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAS,MAAA,SAAAX,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAQ,WAAA;MACA,KAAAJ,SAAA,QAAAN,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAQ,WAAA,cAAAV,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAAS,MAAA;IACA;IACA,SAAAd,oBAAA,MAAAc,MAAA;MACA,KAAAL,SAAA,QAAAT,oBAAA,MAAAc,MAAA;IACA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACAnB,UAAA,eACAC,UAAA;IACAE,oBAAA,EAAAA,oBAAA;IACAiB,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MACA,IAAAC,IAAA;MACA,KAAAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA,WAAAA,KAAA;UACA,OAAAJ,IAAA,CAAAK,MAAA,KAAAC,IAAA;YACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;UACA,GAAAC,KAAA,WAAAC,GAAA;YACAX,IAAA,CAAAY,QAAA,CAAAC,KAAA;cACAX,KAAA;cACAY,WAAA,EAAAH,GAAA,CAAAI;YACA;UACA;QACA;QACAC,QAAA,WAAAA,SAAA,GACA;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}