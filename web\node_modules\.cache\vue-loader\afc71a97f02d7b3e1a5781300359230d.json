{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue?vue&type=style&index=0&id=b8ab2606&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue", "mtime": 1749714201887}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\r\n.software-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.software-item {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  transition: all 0.3s;\r\n  background: #ffffff;\r\n  \r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.software-item-content {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.software-icon {\r\n  width: 150px;\r\n  height: 120px;\r\n  object-fit: contain;\r\n  margin-right: 20px;\r\n}\r\n\r\n.software-info {\r\n  flex: 1;\r\n  \r\n  h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 10px;\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  p {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 15px;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.software-button {\r\n  display: flex;\r\n  align-items: center;\r\n  \r\n  .version-info, .size-info {\r\n    margin-left: 15px;\r\n    font-size: 14px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.download-tips {\r\n  margin-top: 20px;\r\n}\r\n\r\n:deep(.ant-modal-header) {\r\n  background-color: #1890ff;\r\n  \r\n  .ant-modal-title {\r\n    color: white;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.ant-btn-primary) {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n  height: 40px;\r\n  font-size: 16px;\r\n  padding: 0 25px;\r\n}\r\n\r\n:deep(.ant-modal-close) {\r\n  color: white;\r\n}\r\n", {"version": 3, "sources": ["SoftwareDownload.vue"], "names": [], "mappings": ";AAgGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "SoftwareDownload.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\r\n  <div class=\"software-download-modal\">\r\n    <a-modal\r\n      title=\"软件下载\"\r\n      :visible=\"visible\"\r\n      :footer=\"null\"\r\n      @cancel=\"handleCancel\"\r\n      width=\"800px\"\r\n      :maskClosable=\"true\"\r\n    >\r\n      <div class=\"software-list\">\r\n        <div class=\"software-item\" v-for=\"(item, index) in softwareList\" :key=\"index\">\r\n          <div class=\"software-item-content\">\r\n            <img :src=\"item.imgUrl\" alt=\"软件图标\" class=\"software-icon\">\r\n            <div class=\"software-info\">\r\n              <h3>{{ item.name }}</h3>\r\n              <p>{{ item.description }}</p>\r\n              <div class=\"software-button\">\r\n                <a-button type=\"primary\" icon=\"download\" @click=\"downloadSoftware(item)\">立即下载</a-button>\r\n                <span class=\"version-info\">版本: {{ item.version }}</span>\r\n                <span class=\"size-info\">大小: {{ item.size }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"download-tips\">\r\n        <a-alert type=\"info\" show-icon>\r\n          <span slot=\"message\">\r\n            点击\"立即下载\"按钮即可开始下载，软件均为官方原版，请放心使用。\r\n          </span>\r\n        </a-alert>\r\n      </div>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SoftwareDownload',\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      softwareList: [\r\n        {\r\n          name: 'Scratch',\r\n          description: 'Scratch是一款由麻省理工学院开发的少儿编程工具，让孩子们通过图形化积木搭建的方式学习编程逻辑。适合8-16岁儿童入门编程学习，无需编程基础。',\r\n          imgUrl: '/img/scratch.jpg',\r\n          downloadUrl: '/Software_Download/Scratch_Desktop_Setup_3.9.0.exe',\r\n          version: '3.9.0',\r\n          size: '112MB'\r\n        },\r\n        {\r\n          name: 'Python',\r\n          description: 'Python是一种面向对象的解释型计算机程序设计语言，具有丰富的库和简洁的语法，是初学者学习编程的理想选择。广泛应用于数据分析、人工智能、网络开发等领域。',\r\n          imgUrl: '/img/python.jpg',\r\n          downloadUrl: '/Software_Download/python-3.9.7-amd64.exe',\r\n          version: '3.9.7',\r\n          size: '28MB'\r\n        },\r\n        {\r\n          name: 'C++',\r\n          description: 'C++是一种面向对象的编程语言，广泛应用于系统软件、应用软件、驱动程序、嵌入式系统等领域。Dev-C++是一个免费的C/C++集成开发环境。',\r\n          imgUrl: '/img/c++.jpg',\r\n          downloadUrl: '/Software_Download/Dev-Cpp_5.11_TDM-GCC_4.9.2_Setup.exe',\r\n          version: '5.11',\r\n          size: '48MB'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    showModal() {\r\n      this.visible = true\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n    downloadSoftware(software) {\r\n      // 创建下载链接\r\n      const link = document.createElement('a')\r\n      link.href = software.downloadUrl\r\n      link.download = software.downloadUrl.split('/').pop()\r\n      document.body.appendChild(link)\r\n      link.click()\r\n      document.body.removeChild(link)\r\n      \r\n      // 显示下载开始提示\r\n      this.$message.success(`${software.name}开始下载，请稍等...`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.software-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.software-item {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  transition: all 0.3s;\r\n  background: #ffffff;\r\n  \r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.software-item-content {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.software-icon {\r\n  width: 150px;\r\n  height: 120px;\r\n  object-fit: contain;\r\n  margin-right: 20px;\r\n}\r\n\r\n.software-info {\r\n  flex: 1;\r\n  \r\n  h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 10px;\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  p {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 15px;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.software-button {\r\n  display: flex;\r\n  align-items: center;\r\n  \r\n  .version-info, .size-info {\r\n    margin-left: 15px;\r\n    font-size: 14px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.download-tips {\r\n  margin-top: 20px;\r\n}\r\n\r\n:deep(.ant-modal-header) {\r\n  background-color: #1890ff;\r\n  \r\n  .ant-modal-title {\r\n    color: white;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.ant-btn-primary) {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n  height: 40px;\r\n  font-size: 16px;\r\n  padding: 0 25px;\r\n}\r\n\r\n:deep(.ant-modal-close) {\r\n  color: white;\r\n}\r\n</style> "]}]}