{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\MyAdditionalWorkList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\MyAdditionalWorkList.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import { getAction } from '@/api/manage';\nimport { mixinDevice } from '@/utils/mixin.js';\nimport JDictSelectTag from '@/components/dict/JDictSelectTag.vue';\nimport TeachingWorkSubmitModal from '@/views/teaching/modules/TeachingWorkSubmitModal';\nexport default {\n  mixins: [mixinDevice],\n  components: {\n    JDictSelectTag: JDictSelectTag,\n    TeachingWorkSubmitModal: TeachingWorkSubmitModal\n  },\n  data: function data() {\n    return {\n      datasource: [],\n      pagination: {\n        onChange: function onChange(page) {\n          console.log(page);\n        },\n        pageSize: 8\n      },\n      loading: true,\n      queryParam: {\n        status: 'false'\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      this.datasource = [];\n      getAction('/teaching/teachingWork/mineAdditionalWork', {\n        pageSize: 999,\n        submit: this.queryParam.status\n      }).then(function (res) {\n        console.log(res);\n        if (res.success) {\n          _this.datasource = res.result;\n        }\n        _this.loading = false;\n      });\n    },\n    handleChangeStatus: function handleChangeStatus(v) {\n      this.queryParam.status = v.target.value;\n      this.getList();\n    },\n    openWorkFile: function openWorkFile(workUrl) {\n      if (workUrl.endsWith('ppt') || workUrl.endsWith('pptx')) {\n        window.open('https://view.officeapps.live.com/op/embed.aspx?src=' + workUrl);\n      } else {\n        window.open(workUrl);\n      }\n    },\n    toAdditionalWork: function toAdditionalWork(item, reset) {\n      console.log(\"作业项信息:\", item);\n      var workUrl;\n      switch (item.codeType) {\n        case 1:\n          workUrl = '/scratch3/index.html?scene=additional&additionalId=' + item.additionalWorkId + '&departId=' + item.departId + '&workName=' + item.workName;\n          break;\n        case 2:\n          workUrl = '/scratch3/index.html?scene=additional&additionalId=' + item.additionalWorkId + '&departId=' + item.departId + '&workName=' + item.workName;\n          break;\n        case 3:\n          workUrl = '/scratchjr/editor.html?scene=additional&mode=edit&additionalId=' + item.additionalWorkId + '&departId=' + item.departId + '&workName=' + item.workName;\n          break;\n        case 4:\n          workUrl = '/python/index.html?scene=additional&lang=turtle&additionalId=' + item.additionalWorkId + '&departId=' + item.departId + '&workName=' + item.workName;\n          break;\n        // 在此添加C++支持\n        case 5:\n          // C++类型\n          workUrl = '/cpp/index.html?scene=additional&additionalId=' + item.additionalWorkId + '&departId=' + item.departId + '&workName=' + item.workName;\n          break;\n        default:\n          //workUrl = item.workUrl_url\n          this.$refs.submitModal.open({\n            workName: item.workName,\n            additionalId: item.additionalWorkId,\n            departId: item.departId,\n            workType: 0\n          });\n          return;\n      }\n\n      // 修改传递参数的方式，优先使用workId\n      if (!reset && item.mineWorkId) {\n        // 如果已有作品，且不是重做，使用workId参数\n        console.log(\"使用已有作业ID打开:\", item.mineWorkId);\n        workUrl += \"&workId=\" + item.mineWorkId;\n      } else if (item.codeType === 3) {\n        // ScratchJr特殊处理，需要传递workFile\n        console.log(\"ScratchJr使用模板文件打开:\", item.workUrl_url);\n        workUrl += \"&workFile=\" + item.workUrl_url;\n      } else {\n        console.log(\"使用模板创建新作业\");\n      }\n      // 对于其他类型，不需要添加workFile参数，编辑器会根据additionalId和其他参数自动加载模板\n\n      console.log(\"最终URL:\", workUrl);\n      window.open(workUrl);\n    }\n  }\n};", {"version": 3, "names": ["getAction", "mixinDevice", "JDictSelectTag", "TeachingWorkSubmitModal", "mixins", "components", "data", "datasource", "pagination", "onChange", "page", "console", "log", "pageSize", "loading", "queryParam", "status", "created", "getList", "methods", "_this", "submit", "then", "res", "success", "result", "handleChangeStatus", "v", "target", "value", "openWorkFile", "workUrl", "endsWith", "window", "open", "toAdditionalWork", "item", "reset", "codeType", "additionalWorkId", "departId", "workName", "$refs", "submitModal", "additionalId", "workType", "mineWorkId", "workUrl_url"], "sources": ["src/views/account/course/MyAdditionalWorkList.vue"], "sourcesContent": ["<template>\n  <div :class=\"['container']\">\n    <a-card class=\"search-card\" :bordered=\"false\">\n      <j-dict-select-tag\n        :defaultShowAll=\"true\"\n        type=\"radioButton\"\n        @change=\"handleChangeStatus\"\n        v-model=\"queryParam.status\"\n        :trigger-change=\"true\"\n        :hide-green-status=\"true\"\n        :defaultDictOptions=\"[{title: '全部', text: '全部', description:'', value: ''},\n                        {title: '未提交', text: '未提交', description:'', value: 'false'},\n                        {title: '已提交', text: '已提交', description:'', value: 'true'}]\"\n      />\n    </a-card>\n    <a-divider />\n    <a-card :bordered=\"false\">\n      <a-list item-layout=\"horizontal\" :dataSource=\"datasource\" :pagination=\"pagination\">\n        <a-list-item slot=\"renderItem\" slot-scope=\"work\">\n          <a-list-item-meta>\n            <img class=\"work-cover\" slot=\"avatar\" :src=\"work.workCover_url\" alt=\"\" />\n            <template slot=\"title\" class=\"title\" href=\"#\">\n              <h3>{{ work.workName }} <a-tag color=\"blue\">{{work.codeType_dictText}}</a-tag></h3>\n            </template>\n            <template slot=\"description\">\n              <pre class=\"work-desc\">{{work.workDesc}}</pre>\n              <div class=\"work-info\">\n                <a-tag>班级：{{ work.departName }}</a-tag>\n                <a-divider type=\"vertical\" />\n                <a-tag>老师：{{ work.createBy_dictText }}</a-tag>\n              </div>\n            </template>\n          </a-list-item-meta>\n          <div slot=\"extra\" class=\"btns\">\n            <a-tooltip>\n              <template slot=\"title\">\n                <p>{{work.comment}}</p>\n              </template>\n              <a-rate v-if=\"work.score\" :disabled=\"true\" :value=\"work.score\" />\n            </a-tooltip>\n            <a-button v-if=\"work.workDocumentUrl\" @click=\"openWorkFile(work.workDocumentUrl_url)\">作业资料</a-button>\n            <!-- <a-divider v-if=\"work.workDocumentUrl != null\" type=\"vertical\" /> -->\n            <a-button type=\"primary\" :disabled=\"work.mineWorkStatus > 1\" @click=\"toAdditionalWork(work, false)\"> {{work.mineWorkStatus==null?'去做作业':'修改作业'}} </a-button>\n            <a-divider v-if=\"work.mineWorkStatus != null && work.mineWorkStatus < 2\" type=\"vertical\" />\n            <a-button type=\"primary\" v-if=\"work.mineWorkStatus != null && work.mineWorkStatus < 2\" @click=\"toAdditionalWork(work, true)\"> 重做 </a-button>\n          </div>\n        </a-list-item>\n      </a-list>\n    </a-card>\n    <TeachingWorkSubmitModal ref=\"submitModal\"/>\n  </div>\n</template>\n\n<script>\nimport { getAction } from '@/api/manage'\nimport { mixinDevice } from '@/utils/mixin.js'\nimport JDictSelectTag from '@/components/dict/JDictSelectTag.vue'\nimport TeachingWorkSubmitModal from '@/views/teaching/modules/TeachingWorkSubmitModal'\nexport default {\n  mixins: [mixinDevice],\n  components: {\n    JDictSelectTag,\n    TeachingWorkSubmitModal\n  },\n  data() {\n    return {\n      datasource: [],\n      pagination: {\n        onChange: (page) => {\n          console.log(page)\n        },\n        pageSize: 8,\n      },\n      loading: true,\n      queryParam: {status:'false'},\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      this.datasource = []\n      getAction('/teaching/teachingWork/mineAdditionalWork', {\n        pageSize: 999,\n        submit: this.queryParam.status\n      }).then((res) => {\n        console.log(res)\n        if (res.success) {\n          this.datasource = res.result\n        }\n        this.loading = false\n      })\n    },\n     handleChangeStatus(v) {\n      this.queryParam.status = v.target.value\n      this.getList()\n    },\n    openWorkFile(workUrl) {\n      if(workUrl.endsWith('ppt')||workUrl.endsWith('pptx')){\n        window.open('https://view.officeapps.live.com/op/embed.aspx?src='+workUrl)\n      }else{\n        window.open(workUrl)\n      }\n    },\n    toAdditionalWork(item, reset) {\n      console.log(\"作业项信息:\", item);\n      var workUrl\n      switch (item.codeType) {\n        case 1:\n          workUrl =\n            '/scratch3/index.html?scene=additional&additionalId=' +\n            item.additionalWorkId +\n            '&departId=' +\n            item.departId +\n            '&workName=' +\n            item.workName\n          break\n        case 2:\n          workUrl =\n            '/scratch3/index.html?scene=additional&additionalId=' +\n            item.additionalWorkId +\n            '&departId=' +\n            item.departId +\n            '&workName=' +\n            item.workName\n          break\n        case 3:\n          workUrl = '/scratchjr/editor.html?scene=additional&mode=edit&additionalId='+\n            item.additionalWorkId +\n            '&departId=' +\n            item.departId +\n            '&workName=' +\n            item.workName\n          break\n        case 4:\n          workUrl =\n            '/python/index.html?scene=additional&lang=turtle&additionalId=' +\n            item.additionalWorkId +\n            '&departId=' +\n            item.departId +\n            '&workName=' +\n            item.workName\n          break\n         // 在此添加C++支持\n        case 5: // C++类型\n          workUrl =\n            '/cpp/index.html?scene=additional&additionalId=' +\n            item.additionalWorkId +\n            '&departId=' +\n            item.departId +\n            '&workName=' +\n            item.workName\n          break\n        default:\n          //workUrl = item.workUrl_url\n          this.$refs.submitModal.open({\n            workName:item.workName,\n            additionalId: item.additionalWorkId,\n            departId: item.departId,\n            workType:0\n           })\n          return\n      }\n\n      // 修改传递参数的方式，优先使用workId\n      if(!reset && item.mineWorkId){\n        // 如果已有作品，且不是重做，使用workId参数\n        console.log(\"使用已有作业ID打开:\", item.mineWorkId);\n        workUrl += \"&workId=\" + item.mineWorkId;\n      } else if(item.codeType === 3) {\n        // ScratchJr特殊处理，需要传递workFile\n        console.log(\"ScratchJr使用模板文件打开:\", item.workUrl_url);\n          workUrl += \"&workFile=\" + item.workUrl_url;\n      } else {\n        console.log(\"使用模板创建新作业\");\n      }\n      // 对于其他类型，不需要添加workFile参数，编辑器会根据additionalId和其他参数自动加载模板\n      \n      console.log(\"最终URL:\", workUrl);\n      window.open(workUrl)\n    },\n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n\n.ant-list-item {\n  height: 180px;\n  .work-cover {\n    height:150px;\n    max-width: 100%;\n  }\n  .title {\n    display: block;\n    margin-top: 20px;\n    font-size: 16px;\n    line-height: 20px;\n    color: #333;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .meta {\n    margin-top: 8px;\n    font-size: 12px;\n    line-height: 16px;\n    color: #999;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n  .work-desc{\n    white-space:pre-wrap;\n    word-wrap:break-word;\n    margin-right: 10px;\n    max-height: 100px;\n  }\n  .work-info{\n\n  }\n  .btns{\n    .ant-rate,.ant-btn{\n      display: block;\n      margin: 10px 0;\n    }\n  }\n}\n\n</style>"], "mappings": "AAsDA,SAAAA,SAAA;AACA,SAAAC,WAAA;AACA,OAAAC,cAAA;AACA,OAAAC,uBAAA;AACA;EACAC,MAAA,GAAAH,WAAA;EACAI,UAAA;IACAH,cAAA,EAAAA,cAAA;IACAC,uBAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;QACAC,QAAA,WAAAA,SAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;QACA;QACAG,QAAA;MACA;MACAC,OAAA;MACAC,UAAA;QAAAC,MAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAN,OAAA;MACA,KAAAP,UAAA;MACAP,SAAA;QACAa,QAAA;QACAQ,MAAA,OAAAN,UAAA,CAAAC;MACA,GAAAM,IAAA,WAAAC,GAAA;QACAZ,OAAA,CAAAC,GAAA,CAAAW,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAJ,KAAA,CAAAb,UAAA,GAAAgB,GAAA,CAAAE,MAAA;QACA;QACAL,KAAA,CAAAN,OAAA;MACA;IACA;IACAY,kBAAA,WAAAA,mBAAAC,CAAA;MACA,KAAAZ,UAAA,CAAAC,MAAA,GAAAW,CAAA,CAAAC,MAAA,CAAAC,KAAA;MACA,KAAAX,OAAA;IACA;IACAY,YAAA,WAAAA,aAAAC,OAAA;MACA,IAAAA,OAAA,CAAAC,QAAA,WAAAD,OAAA,CAAAC,QAAA;QACAC,MAAA,CAAAC,IAAA,yDAAAH,OAAA;MACA;QACAE,MAAA,CAAAC,IAAA,CAAAH,OAAA;MACA;IACA;IACAI,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,KAAA;MACA1B,OAAA,CAAAC,GAAA,WAAAwB,IAAA;MACA,IAAAL,OAAA;MACA,QAAAK,IAAA,CAAAE,QAAA;QACA;UACAP,OAAA,GACA,wDACAK,IAAA,CAAAG,gBAAA,GACA,eACAH,IAAA,CAAAI,QAAA,GACA,eACAJ,IAAA,CAAAK,QAAA;UACA;QACA;UACAV,OAAA,GACA,wDACAK,IAAA,CAAAG,gBAAA,GACA,eACAH,IAAA,CAAAI,QAAA,GACA,eACAJ,IAAA,CAAAK,QAAA;UACA;QACA;UACAV,OAAA,uEACAK,IAAA,CAAAG,gBAAA,GACA,eACAH,IAAA,CAAAI,QAAA,GACA,eACAJ,IAAA,CAAAK,QAAA;UACA;QACA;UACAV,OAAA,GACA,kEACAK,IAAA,CAAAG,gBAAA,GACA,eACAH,IAAA,CAAAI,QAAA,GACA,eACAJ,IAAA,CAAAK,QAAA;UACA;QACA;QACA;UAAA;UACAV,OAAA,GACA,mDACAK,IAAA,CAAAG,gBAAA,GACA,eACAH,IAAA,CAAAI,QAAA,GACA,eACAJ,IAAA,CAAAK,QAAA;UACA;QACA;UACA;UACA,KAAAC,KAAA,CAAAC,WAAA,CAAAT,IAAA;YACAO,QAAA,EAAAL,IAAA,CAAAK,QAAA;YACAG,YAAA,EAAAR,IAAA,CAAAG,gBAAA;YACAC,QAAA,EAAAJ,IAAA,CAAAI,QAAA;YACAK,QAAA;UACA;UACA;MACA;;MAEA;MACA,KAAAR,KAAA,IAAAD,IAAA,CAAAU,UAAA;QACA;QACAnC,OAAA,CAAAC,GAAA,gBAAAwB,IAAA,CAAAU,UAAA;QACAf,OAAA,iBAAAK,IAAA,CAAAU,UAAA;MACA,WAAAV,IAAA,CAAAE,QAAA;QACA;QACA3B,OAAA,CAAAC,GAAA,uBAAAwB,IAAA,CAAAW,WAAA;QACAhB,OAAA,mBAAAK,IAAA,CAAAW,WAAA;MACA;QACApC,OAAA,CAAAC,GAAA;MACA;MACA;;MAEAD,OAAA,CAAAC,GAAA,WAAAmB,OAAA;MACAE,MAAA,CAAAC,IAAA,CAAAH,OAAA;IACA;EACA;AACA", "ignoreList": []}]}