{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\modules\\RoleModal.vue?vue&type=template&id=268580d0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\modules\\RoleModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  title=\"操作\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n>\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"唯一识别码\"\n        hasFeedback\n      >\n        <a-input placeholder=\"唯一识别码\" disabled=\"disabled\" v-decorator=\"[ 'id', {rules: []} ]\" />\n      </a-form-item>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"角色名称\"\n        hasFeedback >\n        <a-input placeholder=\"起一个名字\" v-decorator=\"[ 'name', {rules: [{ required: true, message: '不起一个名字吗？' }] }]\" />\n      </a-form-item>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"状态\"\n        hasFeedback >\n        <a-select v-decorator=\"[ 'status', {rules: []} ]\">\n          <a-select-option :value=\"1\">正常</a-select-option>\n          <a-select-option :value=\"2\">禁用</a-select-option>\n        </a-select>\n      </a-form-item>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"描述\"\n        hasFeedback\n      >\n        <a-textarea :rows=\"5\" placeholder=\"...\" v-decorator=\"[ 'describe', { rules: [] } ]\" />\n      </a-form-item>\n\n      <a-divider/>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"拥有权限\"\n        hasFeedback\n      >\n        <a-row :gutter=\"16\" v-for=\"(permission, index) in permissions\" :key=\"index\">\n          <a-col :span=\"4\">\n            {{ permission.name }}：\n          </a-col>\n          <a-col :span=\"20\">\n            <a-checkbox\n              v-if=\"permission.actionsOptions.length > 0\"\n              :indeterminate=\"permission.indeterminate\"\n              :checked=\"permission.checkedAll\"\n              @change=\"onChangeCheckAll($event, permission)\">\n              全选\n            </a-checkbox>\n            <a-checkbox-group :options=\"permission.actionsOptions\" v-model=\"permission.selected\" @change=\"onChangeCheck(permission)\" />\n          </a-col>\n        </a-row>\n\n      </a-form-item>\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}