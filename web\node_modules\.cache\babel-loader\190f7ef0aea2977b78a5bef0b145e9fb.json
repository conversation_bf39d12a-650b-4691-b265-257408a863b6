{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JModal\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JModal\\index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getClass as _getClass, getStyle as _getStyle } from '@/utils/props-util';\nimport { triggerWindowResizeEvent } from '@/utils/util';\nexport default {\n  name: 'JModal',\n  props: {\n    title: String,\n    // 可使用 .sync 修饰符\n    visible: Boolean,\n    // 是否全屏弹窗，当全屏时无论如何都会禁止 body 滚动。可使用 .sync 修饰符\n    fullscreen: {\n      type: Boolean,\n      default: false\n    },\n    // 是否允许切换全屏（允许后右上角会出现一个按钮）\n    switchFullscreen: {\n      type: Boolean,\n      default: false\n    },\n    // 点击确定按钮的时候是否关闭弹窗\n    okClose: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data: function data() {\n    return {\n      // 内部使用的 slots ，不再处理\n      usedSlots: ['title'],\n      // 实际控制是否全屏的参数\n      innerFullscreen: this.fullscreen\n    };\n  },\n  computed: {\n    // 一些未处理的参数或特殊处理的参数绑定到 a-modal 上\n    _attrs: function _attrs() {\n      var attrs = _objectSpread({}, this.$attrs);\n      // 如果全屏就将宽度设为 100%\n      if (this.innerFullscreen) {\n        attrs['width'] = '100%';\n      }\n      return attrs;\n    },\n    modalClass: function modalClass() {\n      return {\n        'j-modal-box': true,\n        'fullscreen': this.innerFullscreen,\n        'no-title': this.isNoTitle,\n        'no-footer': this.isNoFooter\n      };\n    },\n    modalStyle: function modalStyle() {\n      var style = {};\n      // 如果全屏就将top设为 0\n      if (this.innerFullscreen) {\n        style['top'] = '0';\n      }\n      return style;\n    },\n    isNoTitle: function isNoTitle() {\n      return !this.title && !this.allSlotsKeys.includes('title');\n    },\n    isNoFooter: function isNoFooter() {\n      return this._attrs['footer'] === null;\n    },\n    slotsKeys: function slotsKeys() {\n      var _this = this;\n      return Object.keys(this.$slots).filter(function (key) {\n        return !_this.usedSlots.includes(key);\n      });\n    },\n    scopedSlotsKeys: function scopedSlotsKeys() {\n      var _this2 = this;\n      return Object.keys(this.$scopedSlots).filter(function (key) {\n        return !_this2.usedSlots.includes(key);\n      });\n    },\n    allSlotsKeys: function allSlotsKeys() {\n      return Object.keys(this.$slots).concat(Object.keys(this.$scopedSlots));\n    },\n    // 切换全屏的按钮图标\n    fullscreenButtonIcon: function fullscreenButtonIcon() {\n      return this.innerFullscreen ? 'fullscreen-exit' : 'fullscreen';\n    }\n  },\n  watch: {\n    visible: function visible() {\n      if (this.visible) {\n        this.innerFullscreen = this.fullscreen;\n      }\n    },\n    innerFullscreen: function innerFullscreen(val) {\n      this.$emit('update:fullscreen', val);\n    }\n  },\n  methods: {\n    getClass: function getClass(clazz) {\n      return _objectSpread(_objectSpread({}, _getClass(this)), clazz);\n    },\n    getStyle: function getStyle(style) {\n      return _objectSpread(_objectSpread({}, _getStyle(this)), style);\n    },\n    close: function close() {\n      this.$emit('update:visible', false);\n    },\n    handleOk: function handleOk() {\n      if (this.okClose) {\n        this.close();\n      }\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    /** 切换全屏 */toggleFullscreen: function toggleFullscreen() {\n      this.innerFullscreen = !this.innerFullscreen;\n      triggerWindowResizeEvent();\n    }\n  }\n};", {"version": 3, "names": ["getClass", "getStyle", "triggerWindowResizeEvent", "name", "props", "title", "String", "visible", "Boolean", "fullscreen", "type", "default", "switchFullscreen", "okClose", "data", "usedSlots", "innerFullscreen", "computed", "_attrs", "attrs", "_objectSpread", "$attrs", "modalClass", "isNoTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modalStyle", "style", "allSlotsKeys", "includes", "slots<PERSON><PERSON><PERSON>", "_this", "Object", "keys", "$slots", "filter", "key", "scopedSlotsKeys", "_this2", "$scopedSlots", "concat", "fullscreenButtonIcon", "watch", "val", "$emit", "methods", "clazz", "close", "handleOk", "handleCancel", "toggleFullscreen"], "sources": ["src/components/jeecg/JModal/index.vue"], "sourcesContent": ["<template>\n  <a-modal\n    ref=\"modal\"\n    :class=\"getClass(modalClass)\"\n    :style=\"getStyle(modalStyle)\"\n    :visible=\"visible\"\n    v-bind=\"_attrs\"\n    v-on=\"$listeners\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n  >\n\n    <slot></slot>\n\n    <template v-if=\"!isNoTitle\" slot=\"title\">\n      <a-row class=\"j-modal-title-row\" type=\"flex\">\n        <a-col class=\"left\">\n          <slot name=\"title\">{{ title }}</slot>\n        </a-col>\n        <a-col v-if=\"switchFullscreen\" class=\"right\" @click=\"toggleFullscreen\">\n          <a-button class=\"ant-modal-close ant-modal-close-x\" ghost type=\"link\" :icon=\"fullscreenButtonIcon\"/>\n        </a-col>\n      </a-row>\n    </template>\n\n    <!-- 处理 scopedSlots -->\n    <template v-for=\"slotName of scopedSlotsKeys\" :slot=\"slotName\">\n      <slot :name=\"slotName\"></slot>\n    </template>\n\n    <!-- 处理 slots -->\n    <template v-for=\"slotName of slotsKeys\" v-slot:[slotName]>\n      <slot :name=\"slotName\"></slot>\n    </template>\n\n  </a-modal>\n</template>\n\n<script>\n\n  import { getClass, getStyle } from '@/utils/props-util'\n  import { triggerWindowResizeEvent } from '@/utils/util'\n\n  export default {\n    name: 'JModal',\n    props: {\n      title: String,\n      // 可使用 .sync 修饰符\n      visible: Boolean,\n      // 是否全屏弹窗，当全屏时无论如何都会禁止 body 滚动。可使用 .sync 修饰符\n      fullscreen: {\n        type: Boolean,\n        default: false\n      },\n      // 是否允许切换全屏（允许后右上角会出现一个按钮）\n      switchFullscreen: {\n        type: Boolean,\n        default: false\n      },\n      // 点击确定按钮的时候是否关闭弹窗\n      okClose: {\n        type: Boolean,\n        default: true\n      },\n    },\n    data() {\n      return {\n        // 内部使用的 slots ，不再处理\n        usedSlots: ['title'],\n        // 实际控制是否全屏的参数\n        innerFullscreen: this.fullscreen,\n      }\n    },\n    computed: {\n      // 一些未处理的参数或特殊处理的参数绑定到 a-modal 上\n      _attrs() {\n        let attrs = { ...this.$attrs }\n        // 如果全屏就将宽度设为 100%\n        if (this.innerFullscreen) {\n          attrs['width'] = '100%'\n        }\n        return attrs\n      },\n      modalClass() {\n        return {\n          'j-modal-box': true,\n          'fullscreen': this.innerFullscreen,\n          'no-title': this.isNoTitle,\n          'no-footer': this.isNoFooter,\n        }\n      },\n      modalStyle() {\n        let style = {}\n        // 如果全屏就将top设为 0\n        if (this.innerFullscreen) {\n          style['top'] = '0'\n        }\n        return style\n      },\n      isNoTitle() {\n        return !this.title && !this.allSlotsKeys.includes('title')\n      },\n      isNoFooter() {\n        return this._attrs['footer'] === null\n      },\n      slotsKeys() {\n        return Object.keys(this.$slots).filter(key => !this.usedSlots.includes(key))\n      },\n      scopedSlotsKeys() {\n        return Object.keys(this.$scopedSlots).filter(key => !this.usedSlots.includes(key))\n      },\n      allSlotsKeys() {\n        return Object.keys(this.$slots).concat(Object.keys(this.$scopedSlots))\n      },\n      // 切换全屏的按钮图标\n      fullscreenButtonIcon() {\n        return this.innerFullscreen ? 'fullscreen-exit' : 'fullscreen'\n      },\n    },\n    watch: {\n      visible() {\n        if (this.visible) {\n          this.innerFullscreen = this.fullscreen\n        }\n      },\n      innerFullscreen(val) {\n        this.$emit('update:fullscreen', val)\n      },\n    },\n    methods: {\n\n      getClass(clazz) {\n        return { ...getClass(this), ...clazz }\n      },\n      getStyle(style) {\n        return { ...getStyle(this), ...style }\n      },\n\n      close() {\n        this.$emit('update:visible', false)\n      },\n\n      handleOk() {\n        if (this.okClose) {\n          this.close()\n        }\n      },\n      handleCancel() {\n        this.close()\n      },\n\n      /** 切换全屏 */\n      toggleFullscreen() {\n        this.innerFullscreen = !this.innerFullscreen\n        triggerWindowResizeEvent()\n      },\n\n    }\n  }\n</script>\n\n<style lang=\"less\">\n  .j-modal-box {\n\n    &.fullscreen {\n      top: 0;\n      left: 0;\n      padding: 0;\n\n      // 兼容1.6.2版本的antdv\n      & .ant-modal {\n        top: 0;\n        padding: 0;\n        height: 100vh;\n      }\n\n      & .ant-modal-content {\n        height: 100vh;\n        border-radius: 0;\n\n        & .ant-modal-body {\n          /* title 和 footer 各占 55px */\n          height: calc(100% - 55px - 55px);\n          overflow: auto;\n        }\n      }\n\n      &.no-title, &.no-footer {\n        .ant-modal-body {\n          height: calc(100% - 55px);\n        }\n      }\n\n      &.no-title.no-footer {\n        .ant-modal-body {\n          height: 100%;\n        }\n      }\n    }\n\n    .j-modal-title-row {\n      .left {\n        width: calc(100% - 56px - 56px);\n      }\n\n      .right {\n        width: 56px;\n        position: inherit;\n\n        .ant-modal-close {\n          right: 56px;\n          color: rgba(0, 0, 0, 0.45);\n\n          &:hover {\n            color: rgba(0, 0, 0, 0.75);\n          }\n        }\n      }\n    }\n  }\n\n  @media (max-width: 767px) {\n    .j-modal-box.fullscreen {\n      margin: 0;\n      max-width: 100vw;\n    }\n  }\n</style>"], "mappings": ";;;;;;AAwCA,SAAAA,QAAA,IAAAA,SAAA,EAAAC,QAAA,IAAAA,SAAA;AACA,SAAAC,wBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA,EAAAC,MAAA;IACA;IACAC,OAAA,EAAAC,OAAA;IACA;IACAC,UAAA;MACAC,IAAA,EAAAF,OAAA;MACAG,OAAA;IACA;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAF,OAAA;MACAG,OAAA;IACA;IACA;IACAE,OAAA;MACAH,IAAA,EAAAF,OAAA;MACAG,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,eAAA,OAAAP;IACA;EACA;EACAQ,QAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,IAAAC,KAAA,GAAAC,aAAA,UAAAC,MAAA;MACA;MACA,SAAAL,eAAA;QACAG,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MACA;QACA;QACA,mBAAAN,eAAA;QACA,iBAAAO,SAAA;QACA,kBAAAC;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA;MACA;MACA,SAAAV,eAAA;QACAU,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MACA,aAAAlB,KAAA,UAAAsB,YAAA,CAAAC,QAAA;IACA;IACAJ,UAAA,WAAAA,WAAA;MACA,YAAAN,MAAA;IACA;IACAW,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,OAAAC,MAAA,CAAAC,IAAA,MAAAC,MAAA,EAAAC,MAAA,WAAAC,GAAA;QAAA,QAAAL,KAAA,CAAAf,SAAA,CAAAa,QAAA,CAAAO,GAAA;MAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,OAAAN,MAAA,CAAAC,IAAA,MAAAM,YAAA,EAAAJ,MAAA,WAAAC,GAAA;QAAA,QAAAE,MAAA,CAAAtB,SAAA,CAAAa,QAAA,CAAAO,GAAA;MAAA;IACA;IACAR,YAAA,WAAAA,aAAA;MACA,OAAAI,MAAA,CAAAC,IAAA,MAAAC,MAAA,EAAAM,MAAA,CAAAR,MAAA,CAAAC,IAAA,MAAAM,YAAA;IACA;IACA;IACAE,oBAAA,WAAAA,qBAAA;MACA,YAAAxB,eAAA;IACA;EACA;EACAyB,KAAA;IACAlC,OAAA,WAAAA,QAAA;MACA,SAAAA,OAAA;QACA,KAAAS,eAAA,QAAAP,UAAA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAA0B,GAAA;MACA,KAAAC,KAAA,sBAAAD,GAAA;IACA;EACA;EACAE,OAAA;IAEA5C,QAAA,WAAAA,SAAA6C,KAAA;MACA,OAAAzB,aAAA,CAAAA,aAAA,KAAApB,SAAA,SAAA6C,KAAA;IACA;IACA5C,QAAA,WAAAA,SAAAyB,KAAA;MACA,OAAAN,aAAA,CAAAA,aAAA,KAAAnB,SAAA,SAAAyB,KAAA;IACA;IAEAoB,KAAA,WAAAA,MAAA;MACA,KAAAH,KAAA;IACA;IAEAI,QAAA,WAAAA,SAAA;MACA,SAAAlC,OAAA;QACA,KAAAiC,KAAA;MACA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAF,KAAA;IACA;IAEA,WACAG,gBAAA,WAAAA,iBAAA;MACA,KAAAjC,eAAA,SAAAA,eAAA;MACAd,wBAAA;IACA;EAEA;AACA", "ignoreList": []}]}