{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\List.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\List.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport Avatar from 'ant-design-vue/es/avatar';\nimport AvatarItem from './Item';\nimport { filterEmpty } from '@/components/_util/util';\nexport default {\n  AvatarItem: AvatarItem,\n  name: \"AvatarList\",\n  components: {\n    Avatar: Avatar,\n    AvatarItem: AvatarItem\n  },\n  props: {\n    prefixCls: {\n      type: String,\n      default: 'ant-pro-avatar-list'\n    },\n    /**\n     * 头像大小 类型: large、small 、mini, default\n     * 默认值: default\n     */\n    size: {\n      type: [String, Number],\n      default: 'default'\n    },\n    /**\n     * 要显示的最大项目\n     */\n    maxLength: {\n      type: Number,\n      default: 0\n    },\n    /**\n     * 多余的项目风格\n     */\n    excessItemsStyle: {\n      type: Object,\n      default: function _default() {\n        return {\n          color: '#f56a00',\n          backgroundColor: '#fde3cf'\n        };\n      }\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  methods: {\n    getItems: function getItems(items) {\n      var h = this.$createElement;\n      var classString = _defineProperty(_defineProperty({}, \"\".concat(this.prefixCls, \"-item\"), true), \"\".concat(this.size), true);\n      if (this.maxLength > 0) {\n        items = items.slice(0, this.maxLength);\n        items.push(h(Avatar, {\n          \"attrs\": {\n            \"size\": this.size\n          },\n          \"style\": this.excessItemsStyle\n        }, [\"+\".concat(this.maxLength)]));\n      }\n      var itemList = items.map(function (item) {\n        return h(\"li\", {\n          \"class\": classString\n        }, [item]);\n      });\n      return itemList;\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var _this$$props = this.$props,\n      prefixCls = _this$$props.prefixCls,\n      size = _this$$props.size;\n    var classString = _defineProperty(_defineProperty({}, \"\".concat(prefixCls), true), \"\".concat(size), true);\n    var items = filterEmpty(this.$slots.default);\n    var itemsDom = items && items.length ? h(\"ul\", {\n      \"class\": \"\".concat(prefixCls, \"-items\")\n    }, [this.getItems(items)]) : null;\n    return h(\"div\", {\n      \"class\": classString\n    }, [itemsDom]);\n  }\n};", {"version": 3, "names": ["Avatar", "AvatarItem", "filterEmpty", "name", "components", "props", "prefixCls", "type", "String", "default", "size", "Number", "max<PERSON><PERSON><PERSON>", "excessItemsStyle", "Object", "_default", "color", "backgroundColor", "data", "methods", "getItems", "items", "h", "$createElement", "classString", "_defineProperty", "concat", "slice", "push", "itemList", "map", "item", "render", "arguments", "_this$$props", "$props", "$slots", "itemsDom", "length"], "sources": ["src/components/AvatarList/List.vue"], "sourcesContent": ["<!--\n<template>\n  <div :class=\"[prefixCls]\">\n    <ul>\n      <slot></slot>\n      <template v-for=\"item in filterEmpty($slots.default).slice(0, 3)\"></template>\n\n\n      <template v-if=\"maxLength > 0 && filterEmpty($slots.default).length > maxLength\">\n        <avatar-item :size=\"size\">\n          <avatar :size=\"size !== 'mini' && size || 20\" :style=\"excessItemsStyle\">{{ `+${maxLength}` }}</avatar>\n        </avatar-item>\n      </template>\n    </ul>\n  </div>\n</template>\n-->\n\n<script>\n  import Avatar from 'ant-design-vue/es/avatar'\n  import AvatarItem from './Item'\n  import { filterEmpty } from '@/components/_util/util'\n\n  export default {\n    AvatarItem,\n    name: \"AvatarList\",\n    components: {\n      Avatar,\n      AvatarItem\n    },\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-avatar-list'\n      },\n      /**\n       * 头像大小 类型: large、small 、mini, default\n       * 默认值: default\n       */\n      size: {\n        type: [String, Number],\n        default: 'default'\n      },\n      /**\n       * 要显示的最大项目\n       */\n      maxLength: {\n        type: Number,\n        default: 0\n      },\n      /**\n       * 多余的项目风格\n       */\n      excessItemsStyle: {\n        type: Object,\n        default: () => {\n          return {\n            color: '#f56a00',\n            backgroundColor: '#fde3cf'\n          }\n        }\n      }\n    },\n    data () {\n      return {}\n    },\n    methods: {\n      getItems(items) {\n        const classString = {\n          [`${this.prefixCls}-item`]: true,\n          [`${this.size}`]: true\n        }\n\n        if (this.maxLength > 0) {\n          items = items.slice(0, this.maxLength)\n          items.push((<Avatar size={ this.size } style={ this.excessItemsStyle }>{`+${this.maxLength}`}</Avatar>))\n        }\n        const itemList = items.map((item) => (\n          <li class={ classString }>{ item }</li>\n        ))\n        return itemList\n      }\n    },\n    render () {\n      const { prefixCls, size } = this.$props\n      const classString = {\n        [`${prefixCls}`]: true,\n        [`${size}`]: true,\n      }\n      const items = filterEmpty(this.$slots.default)\n      const itemsDom = items && items.length ? <ul class={`${prefixCls}-items`}>{ this.getItems(items) }</ul> : null\n\n      return (\n        <div class={ classString }>\n          { itemsDom }\n        </div>\n      )\n    }\n  }\n</script>"], "mappings": ";;;;AAmBA,OAAAA,MAAA;AACA,OAAAC,UAAA;AACA,SAAAC,WAAA;AAEA;EACAD,UAAA,EAAAA,UAAA;EACAE,IAAA;EACAC,UAAA;IACAJ,MAAA,EAAAA,MAAA;IACAC,UAAA,EAAAA;EACA;EACAI,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;AACA;AACA;AACA;IACAC,IAAA;MACAH,IAAA,GAAAC,MAAA,EAAAG,MAAA;MACAF,OAAA;IACA;IACA;AACA;AACA;IACAG,SAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;AACA;AACA;IACAI,gBAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA,WAAAM,SAAA;QACA;UACAC,KAAA;UACAC,eAAA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAAC,CAAA,QAAAC,cAAA;MACA,IAAAC,WAAA,GAAAC,eAAA,CAAAA,eAAA,QAAAC,MAAA,CACA,KAAApB,SAAA,sBAAAoB,MAAA,CACA,KAAAhB,IAAA,QACA;MAEA,SAAAE,SAAA;QACAS,KAAA,GAAAA,KAAA,CAAAM,KAAA,SAAAf,SAAA;QACAS,KAAA,CAAAO,IAAA,CAAAN,CAAA,CAAAtB,MAAA;UAAA;YAAA,aAAAU;UAAA;UAAA,cAAAG;QAAA,QAAAa,MAAA,MAAAd,SAAA;MACA;MACA,IAAAiB,QAAA,GAAAR,KAAA,CAAAS,GAAA,WAAAC,IAAA;QAAA,OAAAT,CAAA;UAAA,SACAE;QAAA,IAAAO,IAAA;MAAA,CACA;MACA,OAAAF,QAAA;IACA;EACA;EACAG,MAAA,WAAAA,OAAA;IAAA,IAAAV,CAAA,GAAAW,SAAA;IACA,IAAAC,YAAA,QAAAC,MAAA;MAAA7B,SAAA,GAAA4B,YAAA,CAAA5B,SAAA;MAAAI,IAAA,GAAAwB,YAAA,CAAAxB,IAAA;IACA,IAAAc,WAAA,GAAAC,eAAA,CAAAA,eAAA,QAAAC,MAAA,CACApB,SAAA,aAAAoB,MAAA,CACAhB,IAAA,QACA;IACA,IAAAW,KAAA,GAAAnB,WAAA,MAAAkC,MAAA,CAAA3B,OAAA;IACA,IAAA4B,QAAA,GAAAhB,KAAA,IAAAA,KAAA,CAAAiB,MAAA,GAAAhB,CAAA;MAAA,YAAAI,MAAA,CAAApB,SAAA;IAAA,SAAAc,QAAA,CAAAC,KAAA;IAEA,OAAAC,CAAA;MAAA,SACAE;IAAA,IACAa,QAAA;EAGA;AACA", "ignoreList": []}]}