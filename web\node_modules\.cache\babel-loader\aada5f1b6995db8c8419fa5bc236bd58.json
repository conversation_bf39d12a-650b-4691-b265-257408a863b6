{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\modules\\RoleModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\modules\\RoleModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getPermissions } from '@/api/manage';\nimport { actionToObject } from '@/utils/permissions';\nimport pick from 'lodash.pick';\nexport default {\n  name: \"RoleModal\",\n  data: function data() {\n    return {\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      visible: false,\n      confirmLoading: false,\n      mdl: {},\n      form: this.$form.createForm(this),\n      permissions: []\n    };\n  },\n  created: function created() {\n    this.loadPermissions();\n  },\n  methods: {\n    add: function add() {\n      this.edit({\n        id: 0\n      });\n    },\n    edit: function edit(record) {\n      var _this2 = this;\n      this.mdl = Object.assign({}, record);\n      this.visible = true;\n\n      // 有权限表，处理勾选\n      if (this.mdl.permissions && this.permissions) {\n        // 先处理要勾选的权限结构\n        var permissionsAction = {};\n        this.mdl.permissions.forEach(function (permission) {\n          permissionsAction[permission.permissionId] = permission.actionEntitySet.map(function (entity) {\n            return entity.action;\n          });\n        });\n        // 把权限表遍历一遍，设定要勾选的权限 action\n        this.permissions.forEach(function (permission) {\n          permission.selected = permissionsAction[permission.id];\n        });\n      }\n      this.$nextTick(function () {\n        _this2.form.setFieldsValue(pick(_this2.mdl, 'id', 'name', 'status', 'describe'));\n      });\n      console.log('this.mdl', this.mdl);\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      var _this = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        // 验证表单没错误\n        if (!err) {\n          console.log('form values', values);\n          _this.confirmLoading = true;\n          // 模拟后端请求 2000 毫秒延迟\n          new Promise(function (resolve) {\n            setTimeout(function () {\n              return resolve();\n            }, 2000);\n          }).then(function () {\n            // Do something\n            _this.$message.success('保存成功');\n            _this.$emit('ok');\n          }).catch(function () {\n            // Do something\n          }).finally(function () {\n            _this.confirmLoading = false;\n            _this.close();\n          });\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    onChangeCheck: function onChangeCheck(permission) {\n      permission.indeterminate = !!permission.selected.length && permission.selected.length < permission.actionsOptions.length;\n      permission.checkedAll = permission.selected.length === permission.actionsOptions.length;\n    },\n    onChangeCheckAll: function onChangeCheckAll(e, permission) {\n      Object.assign(permission, {\n        selected: e.target.checked ? permission.actionsOptions.map(function (obj) {\n          return obj.value;\n        }) : [],\n        indeterminate: false,\n        checkedAll: e.target.checked\n      });\n    },\n    loadPermissions: function loadPermissions() {\n      var _this3 = this;\n      getPermissions().then(function (res) {\n        var result = res.result;\n        _this3.permissions = result.map(function (permission) {\n          var options = actionToObject(permission.actionData);\n          permission.checkedAll = false;\n          permission.selected = [];\n          permission.indeterminate = false;\n          permission.actionsOptions = options.map(function (option) {\n            return {\n              label: option.describe,\n              value: option.action\n            };\n          });\n          return permission;\n        });\n      });\n    }\n  }\n};", {"version": 3, "names": ["getPermissions", "actionToObject", "pick", "name", "data", "labelCol", "xs", "span", "sm", "wrapperCol", "visible", "confirmLoading", "mdl", "form", "$form", "createForm", "permissions", "created", "loadPermissions", "methods", "add", "edit", "id", "record", "_this2", "Object", "assign", "permissionsAction", "for<PERSON>ach", "permission", "permissionId", "actionEntitySet", "map", "entity", "action", "selected", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "close", "$emit", "handleOk", "_this", "validateFields", "err", "values", "Promise", "resolve", "setTimeout", "then", "$message", "success", "catch", "finally", "handleCancel", "onChangeCheck", "indeterminate", "length", "actionsOptions", "checkedAll", "onChangeCheckAll", "e", "target", "checked", "obj", "value", "_this3", "res", "result", "options", "actionData", "option", "label", "describe"], "sources": ["src/views/list/modules/RoleModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"操作\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n  >\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"唯一识别码\"\n          hasFeedback\n        >\n          <a-input placeholder=\"唯一识别码\" disabled=\"disabled\" v-decorator=\"[ 'id', {rules: []} ]\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"角色名称\"\n          hasFeedback >\n          <a-input placeholder=\"起一个名字\" v-decorator=\"[ 'name', {rules: [{ required: true, message: '不起一个名字吗？' }] }]\" />\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"状态\"\n          hasFeedback >\n          <a-select v-decorator=\"[ 'status', {rules: []} ]\">\n            <a-select-option :value=\"1\">正常</a-select-option>\n            <a-select-option :value=\"2\">禁用</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"描述\"\n          hasFeedback\n        >\n          <a-textarea :rows=\"5\" placeholder=\"...\" v-decorator=\"[ 'describe', { rules: [] } ]\" />\n        </a-form-item>\n\n        <a-divider/>\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"拥有权限\"\n          hasFeedback\n        >\n          <a-row :gutter=\"16\" v-for=\"(permission, index) in permissions\" :key=\"index\">\n            <a-col :span=\"4\">\n              {{ permission.name }}：\n            </a-col>\n            <a-col :span=\"20\">\n              <a-checkbox\n                v-if=\"permission.actionsOptions.length > 0\"\n                :indeterminate=\"permission.indeterminate\"\n                :checked=\"permission.checkedAll\"\n                @change=\"onChangeCheckAll($event, permission)\">\n                全选\n              </a-checkbox>\n              <a-checkbox-group :options=\"permission.actionsOptions\" v-model=\"permission.selected\" @change=\"onChangeCheck(permission)\" />\n            </a-col>\n          </a-row>\n\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { getPermissions } from '@/api/manage'\n  import { actionToObject } from '@/utils/permissions'\n  import pick from 'lodash.pick'\n\n  export default {\n    name: \"RoleModal\",\n    data () {\n      return {\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 5 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n        visible: false,\n        confirmLoading: false,\n        mdl: {},\n\n        form: this.$form.createForm(this),\n        permissions: []\n      }\n    },\n    created () {\n      this.loadPermissions()\n    },\n    methods: {\n      add () {\n        this.edit({ id: 0 })\n      },\n      edit (record) {\n        this.mdl = Object.assign({}, record)\n        this.visible = true\n\n        // 有权限表，处理勾选\n        if (this.mdl.permissions && this.permissions) {\n          // 先处理要勾选的权限结构\n          const permissionsAction = {}\n          this.mdl.permissions.forEach(permission => {\n            permissionsAction[permission.permissionId] = permission.actionEntitySet.map(entity => entity.action)\n          })\n          // 把权限表遍历一遍，设定要勾选的权限 action\n          this.permissions.forEach(permission => {\n            permission.selected = permissionsAction[permission.id]\n          })\n        }\n\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.mdl, 'id', 'name', 'status', 'describe'))\n        })\n        console.log('this.mdl', this.mdl)\n\n      },\n      close () {\n        this.$emit('close')\n        this.visible = false\n      },\n      handleOk () {\n        const _this = this\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          // 验证表单没错误\n          if (!err) {\n            console.log('form values', values)\n\n            _this.confirmLoading = true\n            // 模拟后端请求 2000 毫秒延迟\n            new Promise((resolve) => {\n              setTimeout(() => resolve(), 2000)\n            }).then(() => {\n              // Do something\n              _this.$message.success('保存成功')\n              _this.$emit('ok')\n            }).catch(() => {\n              // Do something\n            }).finally(() => {\n              _this.confirmLoading = false\n              _this.close()\n            })\n          }\n        })\n      },\n      handleCancel () {\n        this.close()\n      },\n      onChangeCheck (permission) {\n        permission.indeterminate = !!permission.selected.length && (permission.selected.length < permission.actionsOptions.length)\n        permission.checkedAll = permission.selected.length === permission.actionsOptions.length\n      },\n      onChangeCheckAll (e, permission) {\n        Object.assign(permission, {\n          selected: e.target.checked ? permission.actionsOptions.map(obj => obj.value) : [],\n          indeterminate: false,\n          checkedAll: e.target.checked\n        })\n      },\n      loadPermissions () {\n        getPermissions().then(res => {\n          let result = res.result\n          this.permissions = result.map(permission => {\n            const options = actionToObject(permission.actionData)\n            permission.checkedAll = false\n            permission.selected = []\n            permission.indeterminate = false\n            permission.actionsOptions = options.map(option => {\n              return {\n                label: option.describe,\n                value: option.action\n              }\n            })\n            return permission\n          })\n        })\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAgFA,SAAAA,cAAA;AACA,SAAAC,cAAA;AACA,OAAAC,IAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAG,OAAA;MACAC,cAAA;MACAC,GAAA;MAEAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACAC,GAAA,WAAAA,IAAA;MACA,KAAAC,IAAA;QAAAC,EAAA;MAAA;IACA;IACAD,IAAA,WAAAA,KAAAE,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,GAAA,GAAAa,MAAA,CAAAC,MAAA,KAAAH,MAAA;MACA,KAAAb,OAAA;;MAEA;MACA,SAAAE,GAAA,CAAAI,WAAA,SAAAA,WAAA;QACA;QACA,IAAAW,iBAAA;QACA,KAAAf,GAAA,CAAAI,WAAA,CAAAY,OAAA,WAAAC,UAAA;UACAF,iBAAA,CAAAE,UAAA,CAAAC,YAAA,IAAAD,UAAA,CAAAE,eAAA,CAAAC,GAAA,WAAAC,MAAA;YAAA,OAAAA,MAAA,CAAAC,MAAA;UAAA;QACA;QACA;QACA,KAAAlB,WAAA,CAAAY,OAAA,WAAAC,UAAA;UACAA,UAAA,CAAAM,QAAA,GAAAR,iBAAA,CAAAE,UAAA,CAAAP,EAAA;QACA;MACA;MAEA,KAAAc,SAAA;QACAZ,MAAA,CAAAX,IAAA,CAAAwB,cAAA,CAAAnC,IAAA,CAAAsB,MAAA,CAAAZ,GAAA;MACA;MACA0B,OAAA,CAAAC,GAAA,kBAAA3B,GAAA;IAEA;IACA4B,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAA/B,OAAA;IACA;IACAgC,QAAA,WAAAA,SAAA;MACA,IAAAC,KAAA;MACA;MACA,KAAA9B,IAAA,CAAA+B,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA;QACA,KAAAD,GAAA;UACAP,OAAA,CAAAC,GAAA,gBAAAO,MAAA;UAEAH,KAAA,CAAAhC,cAAA;UACA;UACA,IAAAoC,OAAA,WAAAC,OAAA;YACAC,UAAA;cAAA,OAAAD,OAAA;YAAA;UACA,GAAAE,IAAA;YACA;YACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;YACAT,KAAA,CAAAF,KAAA;UACA,GAAAY,KAAA;YACA;UAAA,CACA,EAAAC,OAAA;YACAX,KAAA,CAAAhC,cAAA;YACAgC,KAAA,CAAAH,KAAA;UACA;QACA;MACA;IACA;IACAe,YAAA,WAAAA,aAAA;MACA,KAAAf,KAAA;IACA;IACAgB,aAAA,WAAAA,cAAA3B,UAAA;MACAA,UAAA,CAAA4B,aAAA,KAAA5B,UAAA,CAAAM,QAAA,CAAAuB,MAAA,IAAA7B,UAAA,CAAAM,QAAA,CAAAuB,MAAA,GAAA7B,UAAA,CAAA8B,cAAA,CAAAD,MAAA;MACA7B,UAAA,CAAA+B,UAAA,GAAA/B,UAAA,CAAAM,QAAA,CAAAuB,MAAA,KAAA7B,UAAA,CAAA8B,cAAA,CAAAD,MAAA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,CAAA,EAAAjC,UAAA;MACAJ,MAAA,CAAAC,MAAA,CAAAG,UAAA;QACAM,QAAA,EAAA2B,CAAA,CAAAC,MAAA,CAAAC,OAAA,GAAAnC,UAAA,CAAA8B,cAAA,CAAA3B,GAAA,WAAAiC,GAAA;UAAA,OAAAA,GAAA,CAAAC,KAAA;QAAA;QACAT,aAAA;QACAG,UAAA,EAAAE,CAAA,CAAAC,MAAA,CAAAC;MACA;IACA;IACA9C,eAAA,WAAAA,gBAAA;MAAA,IAAAiD,MAAA;MACAnE,cAAA,GAAAkD,IAAA,WAAAkB,GAAA;QACA,IAAAC,MAAA,GAAAD,GAAA,CAAAC,MAAA;QACAF,MAAA,CAAAnD,WAAA,GAAAqD,MAAA,CAAArC,GAAA,WAAAH,UAAA;UACA,IAAAyC,OAAA,GAAArE,cAAA,CAAA4B,UAAA,CAAA0C,UAAA;UACA1C,UAAA,CAAA+B,UAAA;UACA/B,UAAA,CAAAM,QAAA;UACAN,UAAA,CAAA4B,aAAA;UACA5B,UAAA,CAAA8B,cAAA,GAAAW,OAAA,CAAAtC,GAAA,WAAAwC,MAAA;YACA;cACAC,KAAA,EAAAD,MAAA,CAAAE,QAAA;cACAR,KAAA,EAAAM,MAAA,CAAAtC;YACA;UACA;UACA,OAAAL,UAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}