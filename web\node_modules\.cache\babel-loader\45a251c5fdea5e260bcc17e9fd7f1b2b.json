{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\exception\\404.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\exception\\404.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import ExceptionPage from './ExceptionPage';\nexport default {\n  components: {\n    ExceptionPage: ExceptionPage\n  }\n};", {"version": 3, "names": ["ExceptionPage", "components"], "sources": ["src/views/exception/404.vue"], "sourcesContent": ["<template>\n  <exception-page type=\"404\" />\n</template>\n\n<script>\n  import ExceptionPage from './ExceptionPage'\n\n  export default {\n    components: {\n      ExceptionPage\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAKA,OAAAA,aAAA;AAEA;EACAC,UAAA;IACAD,aAAA,EAAAA;EACA;AACA", "ignoreList": []}]}