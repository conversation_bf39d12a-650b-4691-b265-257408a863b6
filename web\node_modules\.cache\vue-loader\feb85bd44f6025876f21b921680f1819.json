{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue?vue&type=style&index=0&id=1de75ee0&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.alert {\n    margin-bottom: 16px;\n}\n", {"version": 3, "sources": ["StandardTable.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA", "file": "StandardTable.vue", "sourceRoot": "src/components/table", "sourcesContent": ["<template>\n  <div class=\"standard-table\">\n    <div class=\"alert\">\n      <a-alert type=\"info\" :show-icon=\"true\">\n        <div slot=\"message\">\n          已选择&nbsp;<a style=\"font-weight: 600\">{{ selectedRows.length }}</a>&nbsp;&nbsp;\n          <template v-for=\"(item, index) in needTotalList\" v-if=\"item.needTotal\">\n            {{ item.title }} 总计&nbsp;\n            <a :key=\"index\" style=\"font-weight: 600\">\n              {{ item.customRender ? item.customRender(item.total) : item.total }}\n            </a>&nbsp;&nbsp;\n          </template>\n          <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n        </div>\n      </a-alert>\n    </div>\n    <a-table\n      :size=\"size\"\n      :bordered=\"bordered\"\n      :loading=\"loading\"\n      :columns=\"columns\"\n      :dataSource=\"current\"\n      :rowKey=\"rowKey\"\n      :pagination=\"pagination\"\n      :rowSelection=\"{ selectedRowKeys: selectedRowKeys, onChange: updateSelect }\"\n    >\n    </a-table>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"StandardTable\",\n    // props: ['bordered', 'loading', 'columns', 'data', 'rowKey', 'pagination', 'selectedRows'],\n    props: {\n\n      /**\n       * 数据加载函数，返回值必须是 Promise\n       * 默认情况下必须传递 data 参数；\n       *    如果使用本地数据渲染表格，业务代码中将获取本地数据包装为 Promise 即可。\n       *\n       * currentData 用于向外暴露表格当前渲染的数据，\n       * 业务开发中也可以直接修改 currentData，从而重新渲染表格（仅推荐用于客户端排序、数据过滤等场景）\n       */\n      data: {\n        type: Function,\n        required: true\n      },\n      dataSource: {\n        type: Array,\n        default () {\n          return []\n        }\n      },\n      columns: {\n        type: Array,\n        required: true\n      },\n/*      pagination: {\n        type: Object,\n        default () {\n          return {}\n        }\n      },*/\n      pageSize: {\n        type: Number,\n        default: 10\n      },\n      pageNum: {\n        type: Number,\n        default: 1\n      },\n      pageSizeOptions: {\n        type: Array,\n        default () {\n          return ['10', '20', '30', '40', '50']\n        }\n      },\n      responseParamsName: {\n        type: Object,\n        default () {\n          return {}\n        }\n      },\n      bordered: {\n        type: Boolean,\n        default: false\n      },\n      /**\n       * 表格大小风格，default, middle, small\n       */\n      size: {\n        type: String,\n        default: 'default'\n      },\n      rowKey: {\n        type: String,\n        default: ''\n      },\n      selectedRows: {\n        type: Array,\n        default: null\n      }\n    },\n    data () {\n      return {\n        needTotalList: [],\n        selectedRowKeys: [],\n\n        loading: true,\n\n        total: 0,\n        pageNumber: this.pageNum,\n        currentPageSize: this.pageSize,\n        defaultCurrent: 1,\n        sortParams: {},\n\n        current: [],\n        pagination: {},\n        paramsName: {},\n      }\n    },\n    created () {\n      //数据请求参数配置\n      this.paramsName = Object.assign(\n        {},\n        {\n          pageNumber: \"pageNo\",\n          pageSize: \"pageSize\",\n          total: \"totalCount\",\n          results: \"data\",\n          sortColumns: \"sortColumns\"\n        },\n        this.responseParamsName\n      );\n\n      this.needTotalList = this.initTotalList(this.columns)\n\n      // load data\n      this.loadData( { pageNum: this.pageNumber } )\n    },\n    methods: {\n      updateSelect (selectedRowKeys, selectedRows) {\n        this.selectedRowKeys = selectedRowKeys\n        let list = this.needTotalList\n        this.needTotalList = list.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce((sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n        this.$emit('change', selectedRowKeys, selectedRows)\n      },\n      initTotalList (columns) {\n        const totalList = []\n        columns.forEach(column => {\n          if (column.needTotal) {\n            totalList.push({ ...column, total: 0 })\n          }\n        })\n        return totalList\n      },\n\n      loadData (params) {\n        let that = this\n        that.loading = true\n        params = Object.assign({}, params)\n        const remoteParams = Object.assign({}, that.sortParams)\n        remoteParams[that.paramsName.pageNumber] = params.pageNum || that.pageNumber\n        remoteParams[that.paramsName.pageSize] = params.pageSize || that.currentPageSize\n\n        if (params.pageNum) {\n          that.pageNumber = params.pageNum\n        }\n        if (params.pageSize) {\n          that.currentPageSize = params.pageSize\n        }\n\n        let dataPromise = that.data(remoteParams)\n\n        dataPromise.then( response => {\n          if (!response) {\n            that.loading = false\n            return\n          }\n          let results = response[that.paramsName.results]\n          results = (results instanceof Array && results) || []\n\n          that.current = results\n\n          that.$emit(\"update:currentData\", that.current.slice())\n          that.$emit(\"dataloaded\", that.current.slice())\n\n          that.total = response[that.paramsName.total] * 1\n          that.pagination = that.pager()\n          that.loading = false\n        }, () => {\n          // error callback\n          that.loading = false\n        })\n      },\n      // eslint-disable-next-line\n      onPagerChange (page, pageSize) {\n        this.pageNumber = page\n        this.loadData({ pageNum: page })\n      },\n      onPagerSizeChange (current, size) {\n        this.currentPageSize = size\n        /*\n        if (current === this.pageNumber) this.loadData()\n        console.log('page-size-change', current, size)\n        */\n      },\n      onClearSelected () {\n        this.selectedRowKeys = []\n        this.updateSelect([], [])\n      },\n      pager () {\n        return {\n          total: this.total,\n          showTotal: total => `共有 ${total} 条`,\n          showSizeChanger: true,\n          pageSizeOptions: this.pageSizeOptions,\n          pageSize: this.pageSize,\n          defaultCurrent: this.defaultCurrent,\n          onChange: this.onPagerChange,\n          onShowSizeChange: this.onPagerSizeChange\n        }\n      }\n    },\n    watch: {\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n    .alert {\n        margin-bottom: 16px;\n    }\n</style>"]}]}