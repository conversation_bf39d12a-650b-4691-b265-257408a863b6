{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\LineChartMultid.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\LineChartMultid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { DataSet } from '@antv/data-set';\nimport { ChartEventMixins } from './mixins/ChartMixins';\nexport default {\n  name: 'LineChartMultid',\n  mixins: [ChartEventMixins],\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [{\n          type: 'Jan',\n          jeecg: 7.0,\n          jeebt: 3.9\n        }, {\n          type: 'Feb',\n          jeecg: 6.9,\n          jeebt: 4.2\n        }, {\n          type: 'Mar',\n          jeecg: 9.5,\n          jeebt: 5.7\n        }, {\n          type: 'Apr',\n          jeecg: 14.5,\n          jeebt: 8.5\n        }, {\n          type: 'May',\n          jeecg: 18.4,\n          jeebt: 11.9\n        }, {\n          type: 'Jun',\n          jeecg: 21.5,\n          jeebt: 15.2\n        }, {\n          type: 'Jul',\n          jeecg: 25.2,\n          jeebt: 17.0\n        }, {\n          type: 'Aug',\n          jeecg: 26.5,\n          jeebt: 16.6\n        }, {\n          type: 'Sep',\n          jeecg: 23.3,\n          jeebt: 14.2\n        }, {\n          type: 'Oct',\n          jeecg: 18.3,\n          jeebt: 10.3\n        }, {\n          type: 'Nov',\n          jeecg: 13.9,\n          jeebt: 6.6\n        }, {\n          type: 'Dec',\n          jeecg: 9.6,\n          jeebt: 4.8\n        }];\n      }\n    },\n    fields: {\n      type: Array,\n      default: function _default() {\n        return ['jeecg', 'jeebt'];\n      }\n    },\n    // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n    aliases: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    height: {\n      type: Number,\n      default: 254\n    }\n  },\n  data: function data() {\n    return {\n      scale: [{\n        dataKey: 'x',\n        min: 0,\n        max: 1\n      }],\n      style: {\n        stroke: '#fff',\n        lineWidth: 1\n      }\n    };\n  },\n  computed: {\n    data: function data() {\n      var _this = this;\n      var dv = new DataSet.View().source(this.dataSource);\n      dv.transform({\n        type: 'fold',\n        fields: this.fields,\n        key: 'x',\n        value: 'y'\n      });\n      var rows = dv.rows;\n      // 替换别名\n      rows.forEach(function (row) {\n        var _iterator = _createForOfIteratorHelper(_this.aliases),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var item = _step.value;\n            if (item.field === row.x) {\n              row.x = item.alias;\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      });\n      return rows;\n    }\n  }\n};", {"version": 3, "names": ["DataSet", "ChartEventMixins", "name", "mixins", "props", "title", "type", "String", "default", "dataSource", "Array", "_default", "jeecg", "jeebt", "fields", "aliases", "height", "Number", "data", "scale", "dataKey", "min", "max", "style", "stroke", "lineWidth", "computed", "_this", "dv", "View", "source", "transform", "key", "value", "rows", "for<PERSON>ach", "row", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "field", "x", "alias", "err", "e", "f"], "sources": ["src/components/chart/LineChartMultid.vue"], "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :onClick=\"handleClick\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-legend/>\n      <v-line position=\"type*y\" color=\"x\"/>\n      <v-point position=\"type*y\" color=\"x\" :size=\"4\" :v-style=\"style\" :shape=\"'circle'\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'LineChartMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jan', jeecg: 7.0, jeebt: 3.9 },\n          { type: 'Feb', jeecg: 6.9, jeebt: 4.2 },\n          { type: 'Mar', jeecg: 9.5, jeebt: 5.7 },\n          { type: 'Apr', jeecg: 14.5, jeebt: 8.5 },\n          { type: 'May', jeecg: 18.4, jeebt: 11.9 },\n          { type: 'Jun', jeecg: 21.5, jeebt: 15.2 },\n          { type: 'Jul', jeecg: 25.2, jeebt: 17.0 },\n          { type: 'Aug', jeecg: 26.5, jeebt: 16.6 },\n          { type: 'Sep', jeecg: 23.3, jeebt: 14.2 },\n          { type: 'Oct', jeecg: 18.3, jeebt: 10.3 },\n          { type: 'Nov', jeecg: 13.9, jeebt: 6.6 },\n          { type: 'Dec', jeecg: 9.6, jeebt: 4.8 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['jeecg', 'jeebt']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases:{\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        scale: [{\n          dataKey: 'x',\n          min: 0,\n          max: 1\n        }],\n        style: { stroke: '#fff', lineWidth: 1 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n        let rows =  dv.rows\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.x) {\n              row.x = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": ";;;AAcA,SAAAA,OAAA;AACA,SAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAF,gBAAA;EACAG,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA,QACA;UAAAL,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAP,IAAA;UAAAM,KAAA;UAAAC,KAAA;QAAA,EACA;MAAA;IACA;IACAC,MAAA;MACAR,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACA;IACAI,OAAA;MACAT,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACAK,MAAA;MACAV,IAAA,EAAAW,MAAA;MACAT,OAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QACAC,OAAA;QACAC,GAAA;QACAC,GAAA;MACA;MACAC,KAAA;QAAAC,MAAA;QAAAC,SAAA;MAAA;IACA;EACA;EACAC,QAAA;IACAR,IAAA,WAAAA,KAAA;MAAA,IAAAS,KAAA;MACA,IAAAC,EAAA,OAAA5B,OAAA,CAAA6B,IAAA,GAAAC,MAAA,MAAArB,UAAA;MACAmB,EAAA,CAAAG,SAAA;QACAzB,IAAA;QACAQ,MAAA,OAAAA,MAAA;QACAkB,GAAA;QACAC,KAAA;MACA;MACA,IAAAC,IAAA,GAAAN,EAAA,CAAAM,IAAA;MACA;MACAA,IAAA,CAAAC,OAAA,WAAAC,GAAA;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAX,KAAA,CAAAZ,OAAA;UAAAwB,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAN,KAAA;YACA,IAAAU,IAAA,CAAAC,KAAA,KAAAR,GAAA,CAAAS,CAAA;cACAT,GAAA,CAAAS,CAAA,GAAAF,IAAA,CAAAG,KAAA;cACA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;QAAA;UAAAV,SAAA,CAAAY,CAAA;QAAA;MACA;MACA,OAAAf,IAAA;IACA;EACA;AACA", "ignoreList": []}]}