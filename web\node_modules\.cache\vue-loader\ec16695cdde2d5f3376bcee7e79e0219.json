{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue", "mtime": 1750830741902}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./WorkList.vue?vue&type=template&id=f3631116&scoped=true\"\nimport script from \"./WorkList.vue?vue&type=script&lang=js\"\nexport * from \"./WorkList.vue?vue&type=script&lang=js\"\nimport style0 from \"./WorkList.vue?vue&type=style&index=0&id=f3631116&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f3631116\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('f3631116')) {\n      api.createRecord('f3631116', component.options)\n    } else {\n      api.reload('f3631116', component.options)\n    }\n    module.hot.accept(\"./WorkList.vue?vue&type=template&id=f3631116&scoped=true\", function () {\n      api.rerender('f3631116', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/home/<USER>\"\nexport default component.exports"]}