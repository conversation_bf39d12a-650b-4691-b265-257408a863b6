{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderCustomerList.vue?vue&type=template&id=3a4a3d93&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderCustomerList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n\n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\" :md=\"24\" :sm=\"24\" style=\"margin: -25px 0px 10px 0px\">\n    <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n\n    <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n      <a-menu slot=\"overlay\">\n        <a-menu-item key=\"1\" @click=\"batchDel\">\n          <a-icon type=\"delete\"/>\n          删除\n        </a-menu-item>\n      </a-menu>\n      <a-button style=\"margin-left: 8px\"> 批量操作\n        <a-icon type=\"down\"/>\n      </a-button>\n    </a-dropdown>\n  </div>\n\n  <!-- table区域-begin -->\n  <div>\n    <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n      <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n      <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n    </div>\n\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      @change=\"handleTableChange\">\n\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n        <a-divider type=\"vertical\"/>\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">\n            更多 <a-icon type=\"down\"/>\n          </a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a href=\"javascript:;\" @click=\"handleDetail(record)\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n    </a-table>\n  </div>\n  <!-- table区域-end -->\n  <!-- 表单区域 -->\n  <jeecgOrderCustomer-modal ref=\"modalForm\" @ok=\"modalFormOk\"></jeecgOrderCustomer-modal>\n</a-card>\n", null]}