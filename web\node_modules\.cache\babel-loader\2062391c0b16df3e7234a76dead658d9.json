{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Trend.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Trend.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"Trend\",\n  props: {\n    // 同title\n    term: {\n      type: String,\n      default: '',\n      required: true\n    },\n    // 百分比\n    percentage: {\n      type: Number,\n      default: null\n    },\n    type: {\n      type: Boolean,\n      default: null\n    },\n    target: {\n      type: Number,\n      default: 0\n    },\n    value: {\n      type: Number,\n      default: 0\n    },\n    fixed: {\n      type: Number,\n      default: 2\n    }\n  },\n  data: function data() {\n    return {\n      trend: this.type && 'up' || 'down',\n      rate: this.percentage\n    };\n  },\n  created: function created() {\n    var type = this.type === null ? this.value >= this.target : this.type;\n    this.trend = type ? 'up' : 'down';\n    this.rate = (this.percentage === null ? Math.abs(this.value - this.target) * 100 / this.target : this.percentage).toFixed(this.fixed);\n  }\n};", {"version": 3, "names": ["name", "props", "term", "type", "String", "default", "required", "percentage", "Number", "Boolean", "target", "value", "fixed", "data", "trend", "rate", "created", "Math", "abs", "toFixed"], "sources": ["src/components/chart/Trend.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-trend\">\n    {{ term }}\n    <span>{{ rate }}%</span>\n    <span :class=\"['trend-icon', trend]\"><a-icon :type=\"'caret-' + trend\"/></span>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Trend\",\n    props: {\n      // 同title\n      term: {\n        type: String,\n        default: '',\n        required: true\n      },\n      // 百分比\n      percentage: {\n        type: Number,\n        default: null\n      },\n      type: {\n        type: Boolean,\n        default: null\n      },\n      target: {\n        type: Number,\n        default: 0\n      },\n      value: {\n        type: Number,\n        default: 0\n      },\n      fixed: {\n        type: Number,\n        default: 2\n      }\n    },\n    data () {\n      return {\n        trend: this.type && 'up' || 'down',\n        rate: this.percentage\n      }\n    },\n    created () {\n      let type = this.type === null ? this.value >= this.target : this.type\n      this.trend = type ? 'up' : 'down';\n      this.rate = (this.percentage === null ? Math.abs(this.value - this.target) * 100 / this.target : this.percentage).toFixed(this.fixed)\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .chart-trend {\n    display: inline-block;\n    font-size: 14px;\n    line-height: 22px;\n\n    .trend-icon {\n      font-size: 12px;\n\n      &.up, &.down {\n        margin-left: 4px;\n        position: relative;\n        top: 1px;\n\n        i {\n          font-size: 12px;\n          transform: scale(.83);\n        }\n      }\n\n      &.up {\n        color: #f5222d;\n      }\n      &.down {\n        color: #52c41a;\n        top: -1px;\n      }\n    }\n  }\n</style>"], "mappings": "AASA;EACAA,IAAA;EACAC,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACA;IACAC,UAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;IACAK,MAAA;MACAP,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;IACAM,KAAA;MACAR,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;IACAO,KAAA;MACAT,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,OAAAX,IAAA;MACAY,IAAA,OAAAR;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,IAAAb,IAAA,QAAAA,IAAA,iBAAAQ,KAAA,SAAAD,MAAA,QAAAP,IAAA;IACA,KAAAW,KAAA,GAAAX,IAAA;IACA,KAAAY,IAAA,SAAAR,UAAA,YAAAU,IAAA,CAAAC,GAAA,MAAAP,KAAA,QAAAD,MAAA,eAAAA,MAAA,QAAAH,UAAA,EAAAY,OAAA,MAAAP,KAAA;EACA;AACA", "ignoreList": []}]}