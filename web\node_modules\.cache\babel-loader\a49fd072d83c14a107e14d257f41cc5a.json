{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\testManager.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\testManager.js", "mtime": 1753520831327}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 综合测试管理器\n * 负责协调和管理所有功能测试、性能测试和用户体验测试\n */\n\n/**\n * 测试管理器类\n */\nexport var TestManager = /*#__PURE__*/function () {\n  function TestManager() {\n    _classCallCheck(this, TestManager);\n    this.testSuites = new Map();\n    this.testResults = new Map();\n    this.performanceMetrics = new Map();\n    this.isRunning = false;\n    this.currentTest = null;\n    this.startTime = null;\n    this.endTime = null;\n  }\n\n  /**\n   * 注册测试套件\n   * @param {String} name 测试套件名称\n   * @param {Object} testSuite 测试套件对象\n   */\n  return _createClass(TestManager, [{\n    key: \"registerTestSuite\",\n    value: function registerTestSuite(name, testSuite) {\n      this.testSuites.set(name, testSuite);\n    }\n\n    /**\n     * 运行所有测试\n     * @param {Object} options 测试选项\n     * @param {Function} progressCallback 进度回调\n     * @returns {Promise<Object>} 测试结果\n     */\n  }, {\n    key: \"runAllTests\",\n    value: (function () {\n      var _runAllTests = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var options,\n          progressCallback,\n          totalSuites,\n          completedSuites,\n          _iterator,\n          _step,\n          _step$value,\n          suiteName,\n          testSuite,\n          suiteResult,\n          summary,\n          _args = arguments;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              options = _args.length > 0 && _args[0] !== undefined ? _args[0] : {};\n              progressCallback = _args.length > 1 && _args[1] !== undefined ? _args[1] : null;\n              if (!this.isRunning) {\n                _context.next = 4;\n                break;\n              }\n              throw new Error('测试正在运行中，请等待完成');\n            case 4:\n              this.isRunning = true;\n              this.startTime = Date.now();\n              this.testResults.clear();\n              this.performanceMetrics.clear();\n              _context.prev = 8;\n              totalSuites = this.testSuites.size;\n              completedSuites = 0;\n              _iterator = _createForOfIteratorHelper(this.testSuites);\n              _context.prev = 12;\n              _iterator.s();\n            case 14:\n              if ((_step = _iterator.n()).done) {\n                _context.next = 25;\n                break;\n              }\n              _step$value = _slicedToArray(_step.value, 2), suiteName = _step$value[0], testSuite = _step$value[1];\n              if (progressCallback) {\n                progressCallback({\n                  phase: 'running',\n                  currentSuite: suiteName,\n                  progress: Math.round(completedSuites / totalSuites * 100),\n                  message: \"\\u6B63\\u5728\\u8FD0\\u884C \".concat(suiteName, \" \\u6D4B\\u8BD5\\u5957\\u4EF6...\")\n                });\n              }\n              this.currentTest = suiteName;\n              _context.next = 20;\n              return this.runTestSuite(suiteName, testSuite, options);\n            case 20:\n              suiteResult = _context.sent;\n              this.testResults.set(suiteName, suiteResult);\n              completedSuites++;\n            case 23:\n              _context.next = 14;\n              break;\n            case 25:\n              _context.next = 30;\n              break;\n            case 27:\n              _context.prev = 27;\n              _context.t0 = _context[\"catch\"](12);\n              _iterator.e(_context.t0);\n            case 30:\n              _context.prev = 30;\n              _iterator.f();\n              return _context.finish(30);\n            case 33:\n              this.endTime = Date.now();\n              summary = this.generateTestSummary();\n              if (progressCallback) {\n                progressCallback({\n                  phase: 'completed',\n                  currentSuite: null,\n                  progress: 100,\n                  message: '所有测试已完成'\n                });\n              }\n              return _context.abrupt(\"return\", {\n                success: true,\n                summary: summary,\n                results: Object.fromEntries(this.testResults),\n                metrics: Object.fromEntries(this.performanceMetrics),\n                duration: this.endTime - this.startTime\n              });\n            case 39:\n              _context.prev = 39;\n              _context.t1 = _context[\"catch\"](8);\n              console.error('测试运行失败:', _context.t1);\n              return _context.abrupt(\"return\", {\n                success: false,\n                error: _context.t1.message,\n                results: Object.fromEntries(this.testResults),\n                metrics: Object.fromEntries(this.performanceMetrics),\n                duration: Date.now() - this.startTime\n              });\n            case 43:\n              _context.prev = 43;\n              this.isRunning = false;\n              this.currentTest = null;\n              return _context.finish(43);\n            case 47:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this, [[8, 39, 43, 47], [12, 27, 30, 33]]);\n      }));\n      function runAllTests() {\n        return _runAllTests.apply(this, arguments);\n      }\n      return runAllTests;\n    }()\n    /**\n     * 运行单个测试套件\n     * @param {String} suiteName 测试套件名称\n     * @param {Object} testSuite 测试套件对象\n     * @param {Object} options 测试选项\n     * @returns {Promise<Object>} 测试结果\n     */\n    )\n  }, {\n    key: \"runTestSuite\",\n    value: (function () {\n      var _runTestSuite = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(suiteName, testSuite, options) {\n        var suiteStartTime, results, testCases, _iterator2, _step2, testCase, testResult;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              suiteStartTime = Date.now();\n              results = {\n                name: suiteName,\n                status: 'running',\n                tests: [],\n                passed: 0,\n                failed: 0,\n                skipped: 0,\n                duration: 0,\n                errors: []\n              };\n              _context2.prev = 2;\n              if (!(testSuite.setup && typeof testSuite.setup === 'function')) {\n                _context2.next = 6;\n                break;\n              }\n              _context2.next = 6;\n              return testSuite.setup();\n            case 6:\n              // 获取测试用例\n              testCases = testSuite.getTestCases ? testSuite.getTestCases() : []; // 运行每个测试用例\n              _iterator2 = _createForOfIteratorHelper(testCases);\n              _context2.prev = 8;\n              _iterator2.s();\n            case 10:\n              if ((_step2 = _iterator2.n()).done) {\n                _context2.next = 28;\n                break;\n              }\n              testCase = _step2.value;\n              _context2.next = 14;\n              return this.runTestCase(testCase, options);\n            case 14:\n              testResult = _context2.sent;\n              results.tests.push(testResult);\n              _context2.t0 = testResult.status;\n              _context2.next = _context2.t0 === 'passed' ? 19 : _context2.t0 === 'failed' ? 21 : _context2.t0 === 'skipped' ? 24 : 26;\n              break;\n            case 19:\n              results.passed++;\n              return _context2.abrupt(\"break\", 26);\n            case 21:\n              results.failed++;\n              results.errors.push({\n                test: testCase.name,\n                error: testResult.error\n              });\n              return _context2.abrupt(\"break\", 26);\n            case 24:\n              results.skipped++;\n              return _context2.abrupt(\"break\", 26);\n            case 26:\n              _context2.next = 10;\n              break;\n            case 28:\n              _context2.next = 33;\n              break;\n            case 30:\n              _context2.prev = 30;\n              _context2.t1 = _context2[\"catch\"](8);\n              _iterator2.e(_context2.t1);\n            case 33:\n              _context2.prev = 33;\n              _iterator2.f();\n              return _context2.finish(33);\n            case 36:\n              if (!(testSuite.teardown && typeof testSuite.teardown === 'function')) {\n                _context2.next = 39;\n                break;\n              }\n              _context2.next = 39;\n              return testSuite.teardown();\n            case 39:\n              results.status = results.failed > 0 ? 'failed' : 'passed';\n              _context2.next = 46;\n              break;\n            case 42:\n              _context2.prev = 42;\n              _context2.t2 = _context2[\"catch\"](2);\n              results.status = 'error';\n              results.errors.push({\n                test: 'suite',\n                error: _context2.t2.message\n              });\n            case 46:\n              results.duration = Date.now() - suiteStartTime;\n              return _context2.abrupt(\"return\", results);\n            case 48:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this, [[2, 42], [8, 30, 33, 36]]);\n      }));\n      function runTestSuite(_x, _x2, _x3) {\n        return _runTestSuite.apply(this, arguments);\n      }\n      return runTestSuite;\n    }()\n    /**\n     * 运行单个测试用例\n     * @param {Object} testCase 测试用例\n     * @param {Object} options 测试选项\n     * @returns {Promise<Object>} 测试结果\n     */\n    )\n  }, {\n    key: \"runTestCase\",\n    value: (function () {\n      var _runTestCase = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(testCase, options) {\n        var testStartTime, result, performanceStart, testResult, performanceEnd;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              testStartTime = Date.now();\n              result = {\n                name: testCase.name,\n                description: testCase.description,\n                status: 'running',\n                duration: 0,\n                error: null,\n                metrics: {}\n              };\n              _context3.prev = 2;\n              // 运行测试前的性能标记\n              performanceStart = performance.now(); // 执行测试用例\n              if (!(testCase.run && typeof testCase.run === 'function')) {\n                _context3.next = 13;\n                break;\n              }\n              _context3.next = 7;\n              return testCase.run(options);\n            case 7:\n              testResult = _context3.sent;\n              // 记录性能指标\n              performanceEnd = performance.now();\n              result.metrics.executionTime = performanceEnd - performanceStart;\n\n              // 处理测试结果\n              if (testResult === true || testResult && testResult.success) {\n                result.status = 'passed';\n                if (testResult && testResult.metrics) {\n                  result.metrics = _objectSpread(_objectSpread({}, result.metrics), testResult.metrics);\n                }\n              } else {\n                result.status = 'failed';\n                result.error = testResult && testResult.error ? testResult.error : '测试失败';\n              }\n              _context3.next = 15;\n              break;\n            case 13:\n              result.status = 'skipped';\n              result.error = '测试用例未定义运行方法';\n            case 15:\n              _context3.next = 21;\n              break;\n            case 17:\n              _context3.prev = 17;\n              _context3.t0 = _context3[\"catch\"](2);\n              result.status = 'failed';\n              result.error = _context3.t0.message;\n            case 21:\n              result.duration = Date.now() - testStartTime;\n              return _context3.abrupt(\"return\", result);\n            case 23:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[2, 17]]);\n      }));\n      function runTestCase(_x4, _x5) {\n        return _runTestCase.apply(this, arguments);\n      }\n      return runTestCase;\n    }()\n    /**\n     * 生成测试摘要\n     * @returns {Object} 测试摘要\n     */\n    )\n  }, {\n    key: \"generateTestSummary\",\n    value: function generateTestSummary() {\n      var summary = {\n        totalSuites: this.testResults.size,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: this.endTime - this.startTime,\n        successRate: 0,\n        criticalErrors: [],\n        performanceIssues: [],\n        recommendations: []\n      };\n\n      // 统计测试结果\n      var _iterator3 = _createForOfIteratorHelper(this.testResults),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var _step3$value = _slicedToArray(_step3.value, 2),\n            suiteName = _step3$value[0],\n            suiteResult = _step3$value[1];\n          summary.totalTests += suiteResult.tests.length;\n          summary.totalPassed += suiteResult.passed;\n          summary.totalFailed += suiteResult.failed;\n          summary.totalSkipped += suiteResult.skipped;\n\n          // 收集关键错误\n          if (suiteResult.status === 'error' || suiteResult.failed > 0) {\n            summary.criticalErrors.push({\n              suite: suiteName,\n              errors: suiteResult.errors\n            });\n          }\n        }\n\n        // 计算成功率\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      if (summary.totalTests > 0) {\n        summary.successRate = Math.round(summary.totalPassed / summary.totalTests * 100);\n      }\n\n      // 分析性能问题\n      this.analyzePerformanceIssues(summary);\n\n      // 生成建议\n      this.generateRecommendations(summary);\n      return summary;\n    }\n\n    /**\n     * 分析性能问题\n     * @param {Object} summary 测试摘要\n     */\n  }, {\n    key: \"analyzePerformanceIssues\",\n    value: function analyzePerformanceIssues(summary) {\n      var _iterator4 = _createForOfIteratorHelper(this.testResults),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var _step4$value = _slicedToArray(_step4.value, 2),\n            suiteName = _step4$value[0],\n            suiteResult = _step4$value[1];\n          var _iterator5 = _createForOfIteratorHelper(suiteResult.tests),\n            _step5;\n          try {\n            for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n              var test = _step5.value;\n              // 检查执行时间过长的测试\n              if (test.metrics.executionTime > 5000) {\n                // 超过5秒\n                summary.performanceIssues.push({\n                  type: 'slow_execution',\n                  suite: suiteName,\n                  test: test.name,\n                  value: test.metrics.executionTime,\n                  threshold: 5000,\n                  message: \"\\u6D4B\\u8BD5\\u6267\\u884C\\u65F6\\u95F4\\u8FC7\\u957F: \".concat(test.metrics.executionTime, \"ms\")\n                });\n              }\n\n              // 检查内存使用问题\n              if (test.metrics.memoryUsage && test.metrics.memoryUsage > 100 * 1024 * 1024) {\n                // 超过100MB\n                summary.performanceIssues.push({\n                  type: 'high_memory',\n                  suite: suiteName,\n                  test: test.name,\n                  value: test.metrics.memoryUsage,\n                  threshold: 100 * 1024 * 1024,\n                  message: \"\\u5185\\u5B58\\u4F7F\\u7528\\u8FC7\\u9AD8: \".concat(Math.round(test.metrics.memoryUsage / 1024 / 1024), \"MB\")\n                });\n              }\n            }\n          } catch (err) {\n            _iterator5.e(err);\n          } finally {\n            _iterator5.f();\n          }\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n    }\n\n    /**\n     * 生成建议\n     * @param {Object} summary 测试摘要\n     */\n  }, {\n    key: \"generateRecommendations\",\n    value: function generateRecommendations(summary) {\n      // 基于成功率的建议\n      if (summary.successRate < 80) {\n        summary.recommendations.push({\n          type: 'quality',\n          priority: 'high',\n          message: '测试成功率较低，建议优先修复失败的测试用例'\n        });\n      }\n\n      // 基于性能问题的建议\n      if (summary.performanceIssues.length > 0) {\n        summary.recommendations.push({\n          type: 'performance',\n          priority: 'medium',\n          message: '发现性能问题，建议优化相关功能的执行效率'\n        });\n      }\n\n      // 基于错误数量的建议\n      if (summary.criticalErrors.length > 0) {\n        summary.recommendations.push({\n          type: 'stability',\n          priority: 'high',\n          message: '发现关键错误，建议立即修复以确保系统稳定性'\n        });\n      }\n\n      // 基于测试覆盖率的建议\n      if (summary.totalTests < 50) {\n        summary.recommendations.push({\n          type: 'coverage',\n          priority: 'medium',\n          message: '测试用例数量较少，建议增加更多测试用例以提高覆盖率'\n        });\n      }\n    }\n\n    /**\n     * 获取测试状态\n     * @returns {Object} 测试状态\n     */\n  }, {\n    key: \"getTestStatus\",\n    value: function getTestStatus() {\n      return {\n        isRunning: this.isRunning,\n        currentTest: this.currentTest,\n        startTime: this.startTime,\n        registeredSuites: Array.from(this.testSuites.keys())\n      };\n    }\n\n    /**\n     * 停止测试\n     */\n  }, {\n    key: \"stopTests\",\n    value: function stopTests() {\n      this.isRunning = false;\n      this.currentTest = null;\n    }\n\n    /**\n     * 清理测试结果\n     */\n  }, {\n    key: \"clearResults\",\n    value: function clearResults() {\n      this.testResults.clear();\n      this.performanceMetrics.clear();\n      this.startTime = null;\n      this.endTime = null;\n    }\n\n    /**\n     * 导出测试报告\n     * @param {String} format 导出格式 (json, html, csv)\n     * @returns {String} 导出内容\n     */\n  }, {\n    key: \"exportReport\",\n    value: function exportReport() {\n      var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'json';\n      var reportData = {\n        timestamp: new Date().toISOString(),\n        summary: this.generateTestSummary(),\n        results: Object.fromEntries(this.testResults),\n        metrics: Object.fromEntries(this.performanceMetrics)\n      };\n      switch (format) {\n        case 'json':\n          return JSON.stringify(reportData, null, 2);\n        case 'html':\n          return this.generateHTMLReport(reportData);\n        case 'csv':\n          return this.generateCSVReport(reportData);\n        default:\n          throw new Error(\"\\u4E0D\\u652F\\u6301\\u7684\\u5BFC\\u51FA\\u683C\\u5F0F: \".concat(format));\n      }\n    }\n\n    /**\n     * 生成HTML报告\n     * @param {Object} reportData 报告数据\n     * @returns {String} HTML内容\n     */\n  }, {\n    key: \"generateHTMLReport\",\n    value: function generateHTMLReport(reportData) {\n      return \"<!DOCTYPE html>\\n<html>\\n<head>\\n    <title>\\u6D4B\\u8BD5\\u62A5\\u544A</title>\\n    <style>\\n        body { font-family: Arial, sans-serif; margin: 20px; }\\n        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; }\\n        .success { color: #52c41a; }\\n        .error { color: #ff4d4f; }\\n        .warning { color: #faad14; }\\n        table { width: 100%; border-collapse: collapse; margin: 20px 0; }\\n        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n        th { background-color: #f2f2f2; }\\n    </style>\\n</head>\\n<body>\\n    <h1>\\u8003\\u8BD5\\u7CFB\\u7EDF\\u529F\\u80FD\\u6D4B\\u8BD5\\u62A5\\u544A</h1>\\n    <div class=\\\"summary\\\">\\n        <h2>\\u6D4B\\u8BD5\\u6458\\u8981</h2>\\n        <p>\\u6D4B\\u8BD5\\u65F6\\u95F4: \".concat(reportData.timestamp, \"</p>\\n        <p>\\u603B\\u6D4B\\u8BD5\\u5957\\u4EF6: \").concat(reportData.summary.totalSuites, \"</p>\\n        <p>\\u603B\\u6D4B\\u8BD5\\u7528\\u4F8B: \").concat(reportData.summary.totalTests, \"</p>\\n        <p>\\u6210\\u529F\\u7387: <span class=\\\"\").concat(reportData.summary.successRate >= 80 ? 'success' : 'error', \"\\\">\").concat(reportData.summary.successRate, \"%</span></p>\\n        <p>\\u603B\\u8017\\u65F6: \").concat(reportData.summary.totalDuration, \"ms</p>\\n    </div>\\n    <h2>\\u8BE6\\u7EC6\\u7ED3\\u679C</h2>\\n    <pre>\").concat(JSON.stringify(reportData.results, null, 2), \"</pre>\\n</body>\\n</html>\");\n    }\n\n    /**\n     * 生成CSV报告\n     * @param {Object} reportData 报告数据\n     * @returns {String} CSV内容\n     */\n  }, {\n    key: \"generateCSVReport\",\n    value: function generateCSVReport(reportData) {\n      var csv = 'Suite,Test,Status,Duration,Error\\n';\n      for (var _i = 0, _Object$entries = Object.entries(reportData.results); _i < _Object$entries.length; _i++) {\n        var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n          suiteName = _Object$entries$_i[0],\n          suiteResult = _Object$entries$_i[1];\n        var _iterator6 = _createForOfIteratorHelper(suiteResult.tests),\n          _step6;\n        try {\n          for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n            var test = _step6.value;\n            csv += \"\\\"\".concat(suiteName, \"\\\",\\\"\").concat(test.name, \"\\\",\\\"\").concat(test.status, \"\\\",\").concat(test.duration, \",\\\"\").concat(test.error || '', \"\\\"\\n\");\n          }\n        } catch (err) {\n          _iterator6.e(err);\n        } finally {\n          _iterator6.f();\n        }\n      }\n      return csv;\n    }\n  }]);\n}();\nexport default TestManager;", {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "Array", "isArray", "_createForOfIteratorHelper", "_n", "F", "_arrayLikeToArray", "toString", "from", "test", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_toPrimitive", "toPrimitive", "String", "Number", "TestManager", "testSuites", "Map", "testResults", "performanceMetrics", "isRunning", "currentTest", "startTime", "endTime", "registerTestSuite", "testSuite", "set", "_runAllTests", "_callee", "options", "progressCallback", "totalSuites", "completedSuites", "_iterator", "_step", "_step$value", "suiteName", "suiteResult", "summary", "_args", "_callee$", "_context", "undefined", "Date", "now", "clear", "size", "phase", "currentSuite", "progress", "Math", "round", "message", "concat", "runTestSuite", "t0", "generateTestSummary", "success", "results", "fromEntries", "metrics", "duration", "t1", "console", "error", "runAllTests", "_runTestSuite", "_callee2", "suiteStartTime", "testCases", "_iterator2", "_step2", "testCase", "testResult", "_callee2$", "_context2", "status", "tests", "passed", "failed", "skipped", "errors", "setup", "getTestCases", "runTestCase", "teardown", "t2", "_x", "_x2", "_x3", "_runTestCase", "_callee3", "testStartTime", "result", "performanceStart", "performanceEnd", "_callee3$", "_context3", "description", "performance", "run", "executionTime", "_objectSpread", "_x4", "_x5", "totalTests", "totalPassed", "totalFailed", "totalSkipped", "totalDuration", "successRate", "criticalErrors", "performanceIssues", "recommendations", "_iterator3", "_step3", "_step3$value", "suite", "err", "analyzePerformanceIssues", "generateRecommendations", "_iterator4", "_step4", "_step4$value", "_iterator5", "_step5", "threshold", "memoryUsage", "priority", "getTestStatus", "registeredSuites", "stopTests", "clearResults", "exportReport", "format", "reportData", "timestamp", "toISOString", "JSON", "stringify", "generateHTMLReport", "generateCSVReport", "csv", "_i", "_Object$entries", "entries", "_Object$entries$_i", "_iterator6", "_step6"], "sources": ["E:/teachingproject/teaching/web/src/utils/testManager.js"], "sourcesContent": ["/**\n * 综合测试管理器\n * 负责协调和管理所有功能测试、性能测试和用户体验测试\n */\n\n/**\n * 测试管理器类\n */\nexport class TestManager {\n  constructor() {\n    this.testSuites = new Map()\n    this.testResults = new Map()\n    this.performanceMetrics = new Map()\n    this.isRunning = false\n    this.currentTest = null\n    this.startTime = null\n    this.endTime = null\n  }\n\n  /**\n   * 注册测试套件\n   * @param {String} name 测试套件名称\n   * @param {Object} testSuite 测试套件对象\n   */\n  registerTestSuite(name, testSuite) {\n    this.testSuites.set(name, testSuite)\n  }\n\n  /**\n   * 运行所有测试\n   * @param {Object} options 测试选项\n   * @param {Function} progressCallback 进度回调\n   * @returns {Promise<Object>} 测试结果\n   */\n  async runAllTests(options = {}, progressCallback = null) {\n    if (this.isRunning) {\n      throw new Error('测试正在运行中，请等待完成')\n    }\n\n    this.isRunning = true\n    this.startTime = Date.now()\n    this.testResults.clear()\n    this.performanceMetrics.clear()\n\n    try {\n      const totalSuites = this.testSuites.size\n      let completedSuites = 0\n\n      for (const [suiteName, testSuite] of this.testSuites) {\n        if (progressCallback) {\n          progressCallback({\n            phase: 'running',\n            currentSuite: suiteName,\n            progress: Math.round((completedSuites / totalSuites) * 100),\n            message: `正在运行 ${suiteName} 测试套件...`\n          })\n        }\n\n        this.currentTest = suiteName\n        const suiteResult = await this.runTestSuite(suiteName, testSuite, options)\n        this.testResults.set(suiteName, suiteResult)\n        \n        completedSuites++\n      }\n\n      this.endTime = Date.now()\n      const summary = this.generateTestSummary()\n\n      if (progressCallback) {\n        progressCallback({\n          phase: 'completed',\n          currentSuite: null,\n          progress: 100,\n          message: '所有测试已完成'\n        })\n      }\n\n      return {\n        success: true,\n        summary: summary,\n        results: Object.fromEntries(this.testResults),\n        metrics: Object.fromEntries(this.performanceMetrics),\n        duration: this.endTime - this.startTime\n      }\n    } catch (error) {\n      console.error('测试运行失败:', error)\n      return {\n        success: false,\n        error: error.message,\n        results: Object.fromEntries(this.testResults),\n        metrics: Object.fromEntries(this.performanceMetrics),\n        duration: Date.now() - this.startTime\n      }\n    } finally {\n      this.isRunning = false\n      this.currentTest = null\n    }\n  }\n\n  /**\n   * 运行单个测试套件\n   * @param {String} suiteName 测试套件名称\n   * @param {Object} testSuite 测试套件对象\n   * @param {Object} options 测试选项\n   * @returns {Promise<Object>} 测试结果\n   */\n  async runTestSuite(suiteName, testSuite, options) {\n    const suiteStartTime = Date.now()\n    const results = {\n      name: suiteName,\n      status: 'running',\n      tests: [],\n      passed: 0,\n      failed: 0,\n      skipped: 0,\n      duration: 0,\n      errors: []\n    }\n\n    try {\n      // 运行测试套件的初始化方法\n      if (testSuite.setup && typeof testSuite.setup === 'function') {\n        await testSuite.setup()\n      }\n\n      // 获取测试用例\n      const testCases = testSuite.getTestCases ? testSuite.getTestCases() : []\n\n      // 运行每个测试用例\n      for (const testCase of testCases) {\n        const testResult = await this.runTestCase(testCase, options)\n        results.tests.push(testResult)\n\n        switch (testResult.status) {\n          case 'passed':\n            results.passed++\n            break\n          case 'failed':\n            results.failed++\n            results.errors.push({\n              test: testCase.name,\n              error: testResult.error\n            })\n            break\n          case 'skipped':\n            results.skipped++\n            break\n        }\n      }\n\n      // 运行测试套件的清理方法\n      if (testSuite.teardown && typeof testSuite.teardown === 'function') {\n        await testSuite.teardown()\n      }\n\n      results.status = results.failed > 0 ? 'failed' : 'passed'\n    } catch (error) {\n      results.status = 'error'\n      results.errors.push({\n        test: 'suite',\n        error: error.message\n      })\n    }\n\n    results.duration = Date.now() - suiteStartTime\n    return results\n  }\n\n  /**\n   * 运行单个测试用例\n   * @param {Object} testCase 测试用例\n   * @param {Object} options 测试选项\n   * @returns {Promise<Object>} 测试结果\n   */\n  async runTestCase(testCase, options) {\n    const testStartTime = Date.now()\n    const result = {\n      name: testCase.name,\n      description: testCase.description,\n      status: 'running',\n      duration: 0,\n      error: null,\n      metrics: {}\n    }\n\n    try {\n      // 运行测试前的性能标记\n      const performanceStart = performance.now()\n      \n      // 执行测试用例\n      if (testCase.run && typeof testCase.run === 'function') {\n        const testResult = await testCase.run(options)\n        \n        // 记录性能指标\n        const performanceEnd = performance.now()\n        result.metrics.executionTime = performanceEnd - performanceStart\n        \n        // 处理测试结果\n        if (testResult === true || (testResult && testResult.success)) {\n          result.status = 'passed'\n          if (testResult && testResult.metrics) {\n            result.metrics = { ...result.metrics, ...testResult.metrics }\n          }\n        } else {\n          result.status = 'failed'\n          result.error = testResult && testResult.error ? testResult.error : '测试失败'\n        }\n      } else {\n        result.status = 'skipped'\n        result.error = '测试用例未定义运行方法'\n      }\n    } catch (error) {\n      result.status = 'failed'\n      result.error = error.message\n    }\n\n    result.duration = Date.now() - testStartTime\n    return result\n  }\n\n  /**\n   * 生成测试摘要\n   * @returns {Object} 测试摘要\n   */\n  generateTestSummary() {\n    const summary = {\n      totalSuites: this.testResults.size,\n      totalTests: 0,\n      totalPassed: 0,\n      totalFailed: 0,\n      totalSkipped: 0,\n      totalDuration: this.endTime - this.startTime,\n      successRate: 0,\n      criticalErrors: [],\n      performanceIssues: [],\n      recommendations: []\n    }\n\n    // 统计测试结果\n    for (const [suiteName, suiteResult] of this.testResults) {\n      summary.totalTests += suiteResult.tests.length\n      summary.totalPassed += suiteResult.passed\n      summary.totalFailed += suiteResult.failed\n      summary.totalSkipped += suiteResult.skipped\n\n      // 收集关键错误\n      if (suiteResult.status === 'error' || suiteResult.failed > 0) {\n        summary.criticalErrors.push({\n          suite: suiteName,\n          errors: suiteResult.errors\n        })\n      }\n    }\n\n    // 计算成功率\n    if (summary.totalTests > 0) {\n      summary.successRate = Math.round((summary.totalPassed / summary.totalTests) * 100)\n    }\n\n    // 分析性能问题\n    this.analyzePerformanceIssues(summary)\n\n    // 生成建议\n    this.generateRecommendations(summary)\n\n    return summary\n  }\n\n  /**\n   * 分析性能问题\n   * @param {Object} summary 测试摘要\n   */\n  analyzePerformanceIssues(summary) {\n    for (const [suiteName, suiteResult] of this.testResults) {\n      for (const test of suiteResult.tests) {\n        // 检查执行时间过长的测试\n        if (test.metrics.executionTime > 5000) { // 超过5秒\n          summary.performanceIssues.push({\n            type: 'slow_execution',\n            suite: suiteName,\n            test: test.name,\n            value: test.metrics.executionTime,\n            threshold: 5000,\n            message: `测试执行时间过长: ${test.metrics.executionTime}ms`\n          })\n        }\n\n        // 检查内存使用问题\n        if (test.metrics.memoryUsage && test.metrics.memoryUsage > 100 * 1024 * 1024) { // 超过100MB\n          summary.performanceIssues.push({\n            type: 'high_memory',\n            suite: suiteName,\n            test: test.name,\n            value: test.metrics.memoryUsage,\n            threshold: 100 * 1024 * 1024,\n            message: `内存使用过高: ${Math.round(test.metrics.memoryUsage / 1024 / 1024)}MB`\n          })\n        }\n      }\n    }\n  }\n\n  /**\n   * 生成建议\n   * @param {Object} summary 测试摘要\n   */\n  generateRecommendations(summary) {\n    // 基于成功率的建议\n    if (summary.successRate < 80) {\n      summary.recommendations.push({\n        type: 'quality',\n        priority: 'high',\n        message: '测试成功率较低，建议优先修复失败的测试用例'\n      })\n    }\n\n    // 基于性能问题的建议\n    if (summary.performanceIssues.length > 0) {\n      summary.recommendations.push({\n        type: 'performance',\n        priority: 'medium',\n        message: '发现性能问题，建议优化相关功能的执行效率'\n      })\n    }\n\n    // 基于错误数量的建议\n    if (summary.criticalErrors.length > 0) {\n      summary.recommendations.push({\n        type: 'stability',\n        priority: 'high',\n        message: '发现关键错误，建议立即修复以确保系统稳定性'\n      })\n    }\n\n    // 基于测试覆盖率的建议\n    if (summary.totalTests < 50) {\n      summary.recommendations.push({\n        type: 'coverage',\n        priority: 'medium',\n        message: '测试用例数量较少，建议增加更多测试用例以提高覆盖率'\n      })\n    }\n  }\n\n  /**\n   * 获取测试状态\n   * @returns {Object} 测试状态\n   */\n  getTestStatus() {\n    return {\n      isRunning: this.isRunning,\n      currentTest: this.currentTest,\n      startTime: this.startTime,\n      registeredSuites: Array.from(this.testSuites.keys())\n    }\n  }\n\n  /**\n   * 停止测试\n   */\n  stopTests() {\n    this.isRunning = false\n    this.currentTest = null\n  }\n\n  /**\n   * 清理测试结果\n   */\n  clearResults() {\n    this.testResults.clear()\n    this.performanceMetrics.clear()\n    this.startTime = null\n    this.endTime = null\n  }\n\n  /**\n   * 导出测试报告\n   * @param {String} format 导出格式 (json, html, csv)\n   * @returns {String} 导出内容\n   */\n  exportReport(format = 'json') {\n    const reportData = {\n      timestamp: new Date().toISOString(),\n      summary: this.generateTestSummary(),\n      results: Object.fromEntries(this.testResults),\n      metrics: Object.fromEntries(this.performanceMetrics)\n    }\n\n    switch (format) {\n      case 'json':\n        return JSON.stringify(reportData, null, 2)\n      case 'html':\n        return this.generateHTMLReport(reportData)\n      case 'csv':\n        return this.generateCSVReport(reportData)\n      default:\n        throw new Error(`不支持的导出格式: ${format}`)\n    }\n  }\n\n  /**\n   * 生成HTML报告\n   * @param {Object} reportData 报告数据\n   * @returns {String} HTML内容\n   */\n  generateHTMLReport(reportData) {\n    return `<!DOCTYPE html>\n<html>\n<head>\n    <title>测试报告</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; }\n        .success { color: #52c41a; }\n        .error { color: #ff4d4f; }\n        .warning { color: #faad14; }\n        table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n        th { background-color: #f2f2f2; }\n    </style>\n</head>\n<body>\n    <h1>考试系统功能测试报告</h1>\n    <div class=\"summary\">\n        <h2>测试摘要</h2>\n        <p>测试时间: ${reportData.timestamp}</p>\n        <p>总测试套件: ${reportData.summary.totalSuites}</p>\n        <p>总测试用例: ${reportData.summary.totalTests}</p>\n        <p>成功率: <span class=\"${reportData.summary.successRate >= 80 ? 'success' : 'error'}\">${reportData.summary.successRate}%</span></p>\n        <p>总耗时: ${reportData.summary.totalDuration}ms</p>\n    </div>\n    <h2>详细结果</h2>\n    <pre>${JSON.stringify(reportData.results, null, 2)}</pre>\n</body>\n</html>`\n  }\n\n  /**\n   * 生成CSV报告\n   * @param {Object} reportData 报告数据\n   * @returns {String} CSV内容\n   */\n  generateCSVReport(reportData) {\n    let csv = 'Suite,Test,Status,Duration,Error\\n'\n    \n    for (const [suiteName, suiteResult] of Object.entries(reportData.results)) {\n      for (const test of suiteResult.tests) {\n        csv += `\"${suiteName}\",\"${test.name}\",\"${test.status}\",${test.duration},\"${test.error || ''}\"\\n`\n      }\n    }\n    \n    return csv\n  }\n}\n\nexport default TestManager\n"], "mappings": ";;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,gBAAAkB,OAAA,CAAAlB,CAAA,KAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,CAAAoB,OAAA,EAAAC,IAAA,WAAAnD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,EAAAqB,IAAA,WAAAnD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAAgD,2BAAA,eAAArD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAkD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA3B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAqB,KAAA,sCAAA/C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAsD,IAAA,eAAAlD,CAAA,CAAAmD,MAAA,GAAA9C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAoD,QAAA,MAAA3C,CAAA,QAAAE,CAAA,GAAA0C,mBAAA,CAAA5C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAmD,MAAA,EAAAnD,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAuD,KAAA,GAAAvD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAmD,MAAA,QAAAjD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAwD,iBAAA,CAAAxD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAmD,MAAA,IAAAnD,CAAA,CAAAyD,MAAA,WAAAzD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAkD,IAAA,GAAArB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAA0B,IAAA,EAAAlD,CAAA,CAAAkD,IAAA,kBAAAjB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAmD,MAAA,YAAAnD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA6B,oBAAA1D,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAsD,MAAA,EAAAjD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAuD,QAAA,qBAAApD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAkD,MAAA,KAAA7D,CAAA,CAAAsD,MAAA,aAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAyD,mBAAA,CAAA1D,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAsD,MAAA,kBAAAnD,CAAA,KAAAH,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,uCAAA3D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA2C,IAAA,IAAArD,CAAA,CAAAF,CAAA,CAAAiE,UAAA,IAAArD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAAgE,IAAA,GAAAlE,CAAA,CAAAmE,OAAA,eAAAjE,CAAA,CAAAsD,MAAA,KAAAtD,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,sCAAA9D,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,cAAAiC,aAAAnE,CAAA,QAAAD,CAAA,KAAAqE,MAAA,EAAApE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAsE,QAAA,GAAArE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAuE,UAAA,GAAAtE,CAAA,KAAAD,CAAA,CAAAwE,QAAA,GAAAvE,CAAA,WAAAwE,UAAA,CAAAC,IAAA,CAAA1E,CAAA,cAAA2E,cAAA1E,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA2E,UAAA,QAAA5E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA2E,UAAA,GAAA5E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAwE,UAAA,MAAAJ,MAAA,aAAApE,CAAA,CAAA4C,OAAA,CAAAuB,YAAA,cAAAS,KAAA,iBAAAnC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAkE,IAAA,SAAAlE,CAAA,OAAA8E,KAAA,CAAA9E,CAAA,CAAA+E,MAAA,SAAAxE,CAAA,OAAAG,CAAA,YAAAwD,KAAA,aAAA3D,CAAA,GAAAP,CAAA,CAAA+E,MAAA,OAAA1E,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA2D,IAAA,CAAAzD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA2D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAxD,CAAA,CAAAwD,IAAA,GAAAxD,CAAA,gBAAAsD,SAAA,CAAAf,OAAA,CAAAjD,CAAA,kCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA4C,WAAA,GAAA9D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAiF,mBAAA,aAAAhF,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAiF,WAAA,WAAAlF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAAgF,WAAA,IAAAhF,CAAA,CAAAmF,IAAA,OAAAnF,CAAA,CAAAoF,IAAA,aAAAnF,CAAA,WAAAE,MAAA,CAAAkF,cAAA,GAAAlF,MAAA,CAAAkF,cAAA,CAAApF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAqF,SAAA,GAAAjD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,aAAAkD,OAAA,EAAAlD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAwF,KAAA,aAAAvF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA+E,OAAA,OAAA7E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAiF,mBAAA,CAAA/E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAsD,IAAA,GAAAd,IAAA,WAAAnD,CAAA,WAAAA,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAsD,IAAA,WAAAtB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAA0F,IAAA,aAAAzF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAwE,IAAA,CAAArE,CAAA,UAAAH,CAAA,CAAAyF,OAAA,aAAAzB,KAAA,WAAAhE,CAAA,CAAA6E,MAAA,SAAA9E,CAAA,GAAAC,CAAA,CAAA0F,GAAA,QAAA3F,CAAA,IAAAD,CAAA,SAAAkE,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAlE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA8E,WAAA,EAAAzD,OAAA,EAAAoD,KAAA,WAAAA,MAAA7E,CAAA,aAAA6F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA3D,CAAA,OAAAsD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA3B,GAAA,GAAA5B,CAAA,OAAAwE,UAAA,CAAA5B,OAAA,CAAA8B,aAAA,IAAA3E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA4F,MAAA,OAAAzF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA4E,KAAA,EAAA5E,CAAA,CAAA6F,KAAA,cAAA7F,CAAA,IAAAD,CAAA,MAAA+F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAAtD,CAAA,QAAAwE,UAAA,IAAAG,UAAA,kBAAA3E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAoE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA7D,CAAA,aAAAuD,IAAA,QAAAvD,CAAA,MAAAE,CAAA,kBAAAgG,OAAA7F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAAgE,IAAA,GAAA7D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAkE,UAAA,CAAAM,MAAA,MAAAxE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA+D,UAAA,CAAAlE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAkE,UAAA,iBAAAlE,CAAA,CAAA2D,MAAA,SAAA6B,MAAA,aAAAxF,CAAA,CAAA2D,MAAA,SAAAwB,IAAA,QAAA/E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA6E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,gBAAAuB,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,cAAAzD,CAAA,aAAA+E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,qBAAAtD,CAAA,QAAAsC,KAAA,qDAAAuC,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,YAAAT,MAAA,WAAAA,OAAA7D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAuE,UAAA,CAAAM,MAAA,MAAA7E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAkE,UAAA,CAAAvE,CAAA,OAAAK,CAAA,CAAA8D,MAAA,SAAAwB,IAAA,IAAAxF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAsF,IAAA,GAAAtF,CAAA,CAAAgE,UAAA,QAAA7D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA2D,MAAA,IAAArE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA6D,UAAA,KAAA7D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAkE,UAAA,cAAAhE,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA8C,MAAA,gBAAAU,IAAA,GAAAxD,CAAA,CAAA6D,UAAA,EAAApC,CAAA,SAAAgE,QAAA,CAAAvF,CAAA,MAAAuF,QAAA,WAAAA,SAAAlG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAsC,IAAA,GAAAjE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAqE,IAAA,QAAApE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA2B,MAAA,kBAAAU,IAAA,yBAAAjE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAkE,IAAA,GAAAlE,CAAA,GAAAmC,CAAA,KAAAiE,MAAA,WAAAA,OAAAnG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAqE,UAAA,KAAAtE,CAAA,cAAAkG,QAAA,CAAAjG,CAAA,CAAA0E,UAAA,EAAA1E,CAAA,CAAAsE,QAAA,GAAAG,aAAA,CAAAzE,CAAA,GAAAiC,CAAA,OAAAkE,KAAA,WAAAC,OAAArG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAmE,MAAA,KAAApE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAA0E,UAAA,kBAAAvE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA8C,aAAA,CAAAzE,CAAA,YAAAK,CAAA,YAAA+C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAvG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAoD,QAAA,KAAA5C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAiE,UAAA,EAAA/D,CAAA,EAAAiE,OAAA,EAAA9D,CAAA,oBAAAmD,MAAA,UAAA3B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAwG,eAAAtG,CAAA,EAAAF,CAAA,WAAAyG,eAAA,CAAAvG,CAAA,KAAAwG,qBAAA,CAAAxG,CAAA,EAAAF,CAAA,KAAA2G,2BAAA,CAAAzG,CAAA,EAAAF,CAAA,KAAA4G,gBAAA;AAAA,SAAAA,iBAAA,cAAA5C,SAAA;AAAA,SAAA0C,sBAAAxG,CAAA,EAAA8B,CAAA,QAAA/B,CAAA,WAAAC,CAAA,gCAAAS,MAAA,IAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,KAAAX,CAAA,4BAAAD,CAAA,QAAAD,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAM,CAAA,EAAAJ,CAAA,OAAAqB,CAAA,OAAA1B,CAAA,iBAAAG,CAAA,IAAAT,CAAA,GAAAA,CAAA,CAAA6B,IAAA,CAAA5B,CAAA,GAAAgE,IAAA,QAAAlC,CAAA,QAAA7B,MAAA,CAAAF,CAAA,MAAAA,CAAA,UAAAgC,CAAA,uBAAAA,CAAA,IAAAjC,CAAA,GAAAU,CAAA,CAAAoB,IAAA,CAAA7B,CAAA,GAAAsD,IAAA,MAAA3C,CAAA,CAAA8D,IAAA,CAAA1E,CAAA,CAAAS,KAAA,GAAAG,CAAA,CAAAmE,MAAA,KAAA/C,CAAA,GAAAC,CAAA,iBAAA/B,CAAA,IAAAK,CAAA,OAAAF,CAAA,GAAAH,CAAA,yBAAA+B,CAAA,YAAAhC,CAAA,CAAA8D,MAAA,KAAA/C,CAAA,GAAAf,CAAA,CAAA8D,MAAA,IAAA5D,MAAA,CAAAa,CAAA,MAAAA,CAAA,2BAAAT,CAAA,QAAAF,CAAA,aAAAO,CAAA;AAAA,SAAA6F,gBAAAvG,CAAA,QAAA2G,KAAA,CAAAC,OAAA,CAAA5G,CAAA,UAAAA,CAAA;AAAA,SAAA6G,2BAAA7G,CAAA,EAAAF,CAAA,QAAAC,CAAA,yBAAAU,MAAA,IAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,KAAAX,CAAA,qBAAAD,CAAA,QAAA4G,KAAA,CAAAC,OAAA,CAAA5G,CAAA,MAAAD,CAAA,GAAA0G,2BAAA,CAAAzG,CAAA,MAAAF,CAAA,IAAAE,CAAA,uBAAAA,CAAA,CAAA6E,MAAA,IAAA9E,CAAA,KAAAC,CAAA,GAAAD,CAAA,OAAA+G,EAAA,MAAAC,CAAA,YAAAA,EAAA,eAAA/E,CAAA,EAAA+E,CAAA,EAAA5G,CAAA,WAAAA,EAAA,WAAA2G,EAAA,IAAA9G,CAAA,CAAA6E,MAAA,KAAAxB,IAAA,WAAAA,IAAA,MAAA9C,KAAA,EAAAP,CAAA,CAAA8G,EAAA,UAAAhH,CAAA,WAAAA,EAAAE,CAAA,UAAAA,CAAA,KAAA+B,CAAA,EAAAgF,CAAA,gBAAAjD,SAAA,iJAAAzD,CAAA,EAAAK,CAAA,OAAAI,CAAA,gBAAAkB,CAAA,WAAAA,EAAA,IAAAjC,CAAA,GAAAA,CAAA,CAAA6B,IAAA,CAAA5B,CAAA,MAAAG,CAAA,WAAAA,EAAA,QAAAH,CAAA,GAAAD,CAAA,CAAAiE,IAAA,WAAAtD,CAAA,GAAAV,CAAA,CAAAqD,IAAA,EAAArD,CAAA,KAAAF,CAAA,WAAAA,EAAAE,CAAA,IAAAc,CAAA,OAAAT,CAAA,GAAAL,CAAA,KAAA+B,CAAA,WAAAA,EAAA,UAAArB,CAAA,YAAAX,CAAA,CAAA8D,MAAA,IAAA9D,CAAA,CAAA8D,MAAA,oBAAA/C,CAAA,QAAAT,CAAA;AAAA,SAAAoG,4BAAAzG,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA6F,KAAA,6BAAA9F,CAAA,IAAAC,CAAA,CAAAgF,WAAA,KAAAjF,CAAA,GAAAC,CAAA,CAAAgF,WAAA,CAAAC,IAAA,aAAAlF,CAAA,cAAAA,CAAA,GAAA4G,KAAA,CAAAO,IAAA,CAAAlH,CAAA,oBAAAD,CAAA,+CAAAoH,IAAA,CAAApH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAsG,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA6E,MAAA,MAAAnE,CAAA,GAAAV,CAAA,CAAA6E,MAAA,YAAA/E,CAAA,MAAAK,CAAA,GAAAwG,KAAA,CAAAjG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAAiH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA6C,IAAA,GAAAtD,CAAA,CAAAe,CAAA,IAAAyE,OAAA,CAAAvC,OAAA,CAAAlC,CAAA,EAAAoC,IAAA,CAAAlD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAwH,SAAA,aAAA/B,OAAA,WAAAvF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAoH,KAAA,CAAAxH,CAAA,EAAAD,CAAA,YAAA0H,MAAArH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAmH,KAAA,EAAAC,MAAA,UAAAtH,CAAA,cAAAsH,OAAAtH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAmH,KAAA,EAAAC,MAAA,WAAAtH,CAAA,KAAAqH,KAAA;AAAA,SAAAE,gBAAAhH,CAAA,EAAAP,CAAA,UAAAO,CAAA,YAAAP,CAAA,aAAA2D,SAAA;AAAA,SAAA6D,kBAAA7H,CAAA,EAAAE,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAA6E,MAAA,EAAA9E,CAAA,UAAAM,CAAA,GAAAL,CAAA,CAAAD,CAAA,GAAAM,CAAA,CAAAY,UAAA,GAAAZ,CAAA,CAAAY,UAAA,QAAAZ,CAAA,CAAAa,YAAA,kBAAAb,CAAA,KAAAA,CAAA,CAAAc,QAAA,QAAAlB,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAA8H,cAAA,CAAAvH,CAAA,CAAAwH,GAAA,GAAAxH,CAAA;AAAA,SAAAyH,aAAAhI,CAAA,EAAAE,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA2H,iBAAA,CAAA7H,CAAA,CAAAI,SAAA,EAAAF,CAAA,GAAAD,CAAA,IAAA4H,iBAAA,CAAA7H,CAAA,EAAAC,CAAA,GAAAE,MAAA,CAAAK,cAAA,CAAAR,CAAA,iBAAAqB,QAAA,SAAArB,CAAA;AAAA,SAAA8H,eAAA7H,CAAA,QAAAS,CAAA,GAAAuH,YAAA,CAAAhI,CAAA,gCAAAgD,OAAA,CAAAvC,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAuH,aAAAhI,CAAA,EAAAC,CAAA,oBAAA+C,OAAA,CAAAhD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAuH,WAAA,kBAAAlI,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,gCAAA+C,OAAA,CAAAvC,CAAA,UAAAA,CAAA,YAAAsD,SAAA,yEAAA9D,CAAA,GAAAiI,MAAA,GAAAC,MAAA,EAAAnI,CAAA;AADA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAaoI,WAAW;EACtB,SAAAA,YAAA,EAAc;IAAAT,eAAA,OAAAS,WAAA;IACZ,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACE,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACnC,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;EACrB;;EAEA;AACF;AACA;AACA;AACA;EAJE,OAAAb,YAAA,CAAAK,WAAA;IAAAN,GAAA;IAAAtH,KAAA,EAKA,SAAAqI,kBAAkB3D,IAAI,EAAE4D,SAAS,EAAE;MACjC,IAAI,CAACT,UAAU,CAACU,GAAG,CAAC7D,IAAI,EAAE4D,SAAS,CAAC;IACtC;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAhB,GAAA;IAAAtH,KAAA;MAAA,IAAAwI,YAAA,GAAA1B,iBAAA,eAAAxH,mBAAA,GAAAqF,IAAA,CAMA,SAAA8D,QAAA;QAAA,IAAAC,OAAA;UAAAC,gBAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,SAAA;UAAAC,KAAA;UAAAC,WAAA;UAAAC,SAAA;UAAAX,SAAA;UAAAY,WAAA;UAAAC,OAAA;UAAAC,KAAA,GAAArC,SAAA;QAAA,OAAAzH,mBAAA,GAAAuB,IAAA,UAAAwI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAlE,IAAA,GAAAkE,QAAA,CAAA7F,IAAA;YAAA;cAAkBiF,OAAO,GAAAU,KAAA,CAAA9E,MAAA,QAAA8E,KAAA,QAAAG,SAAA,GAAAH,KAAA,MAAG,CAAC,CAAC;cAAET,gBAAgB,GAAAS,KAAA,CAAA9E,MAAA,QAAA8E,KAAA,QAAAG,SAAA,GAAAH,KAAA,MAAG,IAAI;cAAA,KACjD,IAAI,CAACnB,SAAS;gBAAAqB,QAAA,CAAA7F,IAAA;gBAAA;cAAA;cAAA,MACV,IAAIZ,KAAK,CAAC,eAAe,CAAC;YAAA;cAGlC,IAAI,CAACoF,SAAS,GAAG,IAAI;cACrB,IAAI,CAACE,SAAS,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC;cAC3B,IAAI,CAAC1B,WAAW,CAAC2B,KAAK,CAAC,CAAC;cACxB,IAAI,CAAC1B,kBAAkB,CAAC0B,KAAK,CAAC,CAAC;cAAAJ,QAAA,CAAAlE,IAAA;cAGvBwD,WAAW,GAAG,IAAI,CAACf,UAAU,CAAC8B,IAAI;cACpCd,eAAe,GAAG,CAAC;cAAAC,SAAA,GAAAxC,0BAAA,CAEc,IAAI,CAACuB,UAAU;cAAAyB,QAAA,CAAAlE,IAAA;cAAA0D,SAAA,CAAArH,CAAA;YAAA;cAAA,KAAAsH,KAAA,GAAAD,SAAA,CAAAlJ,CAAA,IAAAkD,IAAA;gBAAAwG,QAAA,CAAA7F,IAAA;gBAAA;cAAA;cAAAuF,WAAA,GAAAjD,cAAA,CAAAgD,KAAA,CAAA/I,KAAA,MAAxCiJ,SAAS,GAAAD,WAAA,KAAEV,SAAS,GAAAU,WAAA;cAC9B,IAAIL,gBAAgB,EAAE;gBACpBA,gBAAgB,CAAC;kBACfiB,KAAK,EAAE,SAAS;kBAChBC,YAAY,EAAEZ,SAAS;kBACvBa,QAAQ,EAAEC,IAAI,CAACC,KAAK,CAAEnB,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC;kBAC3DqB,OAAO,8BAAAC,MAAA,CAAUjB,SAAS;gBAC5B,CAAC,CAAC;cACJ;cAEA,IAAI,CAACf,WAAW,GAAGe,SAAS;cAAAK,QAAA,CAAA7F,IAAA;cAAA,OACF,IAAI,CAAC0G,YAAY,CAAClB,SAAS,EAAEX,SAAS,EAAEI,OAAO,CAAC;YAAA;cAApEQ,WAAW,GAAAI,QAAA,CAAApG,IAAA;cACjB,IAAI,CAAC6E,WAAW,CAACQ,GAAG,CAACU,SAAS,EAAEC,WAAW,CAAC;cAE5CL,eAAe,EAAE;YAAA;cAAAS,QAAA,CAAA7F,IAAA;cAAA;YAAA;cAAA6F,QAAA,CAAA7F,IAAA;cAAA;YAAA;cAAA6F,QAAA,CAAAlE,IAAA;cAAAkE,QAAA,CAAAc,EAAA,GAAAd,QAAA;cAAAR,SAAA,CAAAvJ,CAAA,CAAA+J,QAAA,CAAAc,EAAA;YAAA;cAAAd,QAAA,CAAAlE,IAAA;cAAA0D,SAAA,CAAAtH,CAAA;cAAA,OAAA8H,QAAA,CAAA3D,MAAA;YAAA;cAGnB,IAAI,CAACyC,OAAO,GAAGoB,IAAI,CAACC,GAAG,CAAC,CAAC;cACnBN,OAAO,GAAG,IAAI,CAACkB,mBAAmB,CAAC,CAAC;cAE1C,IAAI1B,gBAAgB,EAAE;gBACpBA,gBAAgB,CAAC;kBACfiB,KAAK,EAAE,WAAW;kBAClBC,YAAY,EAAE,IAAI;kBAClBC,QAAQ,EAAE,GAAG;kBACbG,OAAO,EAAE;gBACX,CAAC,CAAC;cACJ;cAAC,OAAAX,QAAA,CAAAjG,MAAA,WAEM;gBACLiH,OAAO,EAAE,IAAI;gBACbnB,OAAO,EAAEA,OAAO;gBAChBoB,OAAO,EAAE7K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACzC,WAAW,CAAC;gBAC7C0C,OAAO,EAAE/K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACxC,kBAAkB,CAAC;gBACpD0C,QAAQ,EAAE,IAAI,CAACtC,OAAO,GAAG,IAAI,CAACD;cAChC,CAAC;YAAA;cAAAmB,QAAA,CAAAlE,IAAA;cAAAkE,QAAA,CAAAqB,EAAA,GAAArB,QAAA;cAEDsB,OAAO,CAACC,KAAK,CAAC,SAAS,EAAAvB,QAAA,CAAAqB,EAAO,CAAC;cAAA,OAAArB,QAAA,CAAAjG,MAAA,WACxB;gBACLiH,OAAO,EAAE,KAAK;gBACdO,KAAK,EAAEvB,QAAA,CAAAqB,EAAA,CAAMV,OAAO;gBACpBM,OAAO,EAAE7K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACzC,WAAW,CAAC;gBAC7C0C,OAAO,EAAE/K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACxC,kBAAkB,CAAC;gBACpD0C,QAAQ,EAAElB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACtB;cAC9B,CAAC;YAAA;cAAAmB,QAAA,CAAAlE,IAAA;cAED,IAAI,CAAC6C,SAAS,GAAG,KAAK;cACtB,IAAI,CAACC,WAAW,GAAG,IAAI;cAAA,OAAAoB,QAAA,CAAA3D,MAAA;YAAA;YAAA;cAAA,OAAA2D,QAAA,CAAA/D,IAAA;UAAA;QAAA,GAAAkD,OAAA;MAAA,CAE1B;MAAA,SAAAqC,YAAA;QAAA,OAAAtC,YAAA,CAAAxB,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA+D,WAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IANE;EAAA;IAAAxD,GAAA;IAAAtH,KAAA;MAAA,IAAA+K,aAAA,GAAAjE,iBAAA,eAAAxH,mBAAA,GAAAqF,IAAA,CAOA,SAAAqG,SAAmB/B,SAAS,EAAEX,SAAS,EAAEI,OAAO;QAAA,IAAAuC,cAAA,EAAAV,OAAA,EAAAW,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,UAAA;QAAA,OAAAhM,mBAAA,GAAAuB,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApG,IAAA,GAAAoG,SAAA,CAAA/H,IAAA;YAAA;cACxCwH,cAAc,GAAGzB,IAAI,CAACC,GAAG,CAAC,CAAC;cAC3Bc,OAAO,GAAG;gBACd7F,IAAI,EAAEuE,SAAS;gBACfwC,MAAM,EAAE,SAAS;gBACjBC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,CAAC;gBACTC,MAAM,EAAE,CAAC;gBACTC,OAAO,EAAE,CAAC;gBACVnB,QAAQ,EAAE,CAAC;gBACXoB,MAAM,EAAE;cACV,CAAC;cAAAN,SAAA,CAAApG,IAAA;cAAA,MAIKkD,SAAS,CAACyD,KAAK,IAAI,OAAOzD,SAAS,CAACyD,KAAK,KAAK,UAAU;gBAAAP,SAAA,CAAA/H,IAAA;gBAAA;cAAA;cAAA+H,SAAA,CAAA/H,IAAA;cAAA,OACpD6E,SAAS,CAACyD,KAAK,CAAC,CAAC;YAAA;cAGzB;cACMb,SAAS,GAAG5C,SAAS,CAAC0D,YAAY,GAAG1D,SAAS,CAAC0D,YAAY,CAAC,CAAC,GAAG,EAAE,EAExE;cAAAb,UAAA,GAAA7E,0BAAA,CACuB4E,SAAS;cAAAM,SAAA,CAAApG,IAAA;cAAA+F,UAAA,CAAA1J,CAAA;YAAA;cAAA,KAAA2J,MAAA,GAAAD,UAAA,CAAAvL,CAAA,IAAAkD,IAAA;gBAAA0I,SAAA,CAAA/H,IAAA;gBAAA;cAAA;cAArB4H,QAAQ,GAAAD,MAAA,CAAApL,KAAA;cAAAwL,SAAA,CAAA/H,IAAA;cAAA,OACQ,IAAI,CAACwI,WAAW,CAACZ,QAAQ,EAAE3C,OAAO,CAAC;YAAA;cAAtD4C,UAAU,GAAAE,SAAA,CAAAtI,IAAA;cAChBqH,OAAO,CAACmB,KAAK,CAACzH,IAAI,CAACqH,UAAU,CAAC;cAAAE,SAAA,CAAApB,EAAA,GAEtBkB,UAAU,CAACG,MAAM;cAAAD,SAAA,CAAA/H,IAAA,GAAA+H,SAAA,CAAApB,EAAA,KAClB,QAAQ,QAAAoB,SAAA,CAAApB,EAAA,KAGR,QAAQ,QAAAoB,SAAA,CAAApB,EAAA,KAOR,SAAS;cAAA;YAAA;cATZG,OAAO,CAACoB,MAAM,EAAE;cAAA,OAAAH,SAAA,CAAAnI,MAAA;YAAA;cAGhBkH,OAAO,CAACqB,MAAM,EAAE;cAChBrB,OAAO,CAACuB,MAAM,CAAC7H,IAAI,CAAC;gBAClB2C,IAAI,EAAEyE,QAAQ,CAAC3G,IAAI;gBACnBmG,KAAK,EAAES,UAAU,CAACT;cACpB,CAAC,CAAC;cAAA,OAAAW,SAAA,CAAAnI,MAAA;YAAA;cAGFkH,OAAO,CAACsB,OAAO,EAAE;cAAA,OAAAL,SAAA,CAAAnI,MAAA;YAAA;cAAAmI,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAApG,IAAA;cAAAoG,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAAAL,UAAA,CAAA5L,CAAA,CAAAiM,SAAA,CAAAb,EAAA;YAAA;cAAAa,SAAA,CAAApG,IAAA;cAAA+F,UAAA,CAAA3J,CAAA;cAAA,OAAAgK,SAAA,CAAA7F,MAAA;YAAA;cAAA,MAMnB2C,SAAS,CAAC4D,QAAQ,IAAI,OAAO5D,SAAS,CAAC4D,QAAQ,KAAK,UAAU;gBAAAV,SAAA,CAAA/H,IAAA;gBAAA;cAAA;cAAA+H,SAAA,CAAA/H,IAAA;cAAA,OAC1D6E,SAAS,CAAC4D,QAAQ,CAAC,CAAC;YAAA;cAG5B3B,OAAO,CAACkB,MAAM,GAAGlB,OAAO,CAACqB,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ;cAAAJ,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAApG,IAAA;cAAAoG,SAAA,CAAAW,EAAA,GAAAX,SAAA;cAEzDjB,OAAO,CAACkB,MAAM,GAAG,OAAO;cACxBlB,OAAO,CAACuB,MAAM,CAAC7H,IAAI,CAAC;gBAClB2C,IAAI,EAAE,OAAO;gBACbiE,KAAK,EAAEW,SAAA,CAAAW,EAAA,CAAMlC;cACf,CAAC,CAAC;YAAA;cAGJM,OAAO,CAACG,QAAQ,GAAGlB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGwB,cAAc;cAAA,OAAAO,SAAA,CAAAnI,MAAA,WACvCkH,OAAO;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAjG,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA,CACf;MAAA,SAAAb,aAAAiC,EAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAvB,aAAA,CAAA/D,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAoD,YAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;IALE;EAAA;IAAA7C,GAAA;IAAAtH,KAAA;MAAA,IAAAuM,YAAA,GAAAzF,iBAAA,eAAAxH,mBAAA,GAAAqF,IAAA,CAMA,SAAA6H,SAAkBnB,QAAQ,EAAE3C,OAAO;QAAA,IAAA+D,aAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAArB,UAAA,EAAAsB,cAAA;QAAA,OAAAtN,mBAAA,GAAAuB,IAAA,UAAAgM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAArJ,IAAA;YAAA;cAC3BgJ,aAAa,GAAGjD,IAAI,CAACC,GAAG,CAAC,CAAC;cAC1BiD,MAAM,GAAG;gBACbhI,IAAI,EAAE2G,QAAQ,CAAC3G,IAAI;gBACnBqI,WAAW,EAAE1B,QAAQ,CAAC0B,WAAW;gBACjCtB,MAAM,EAAE,SAAS;gBACjBf,QAAQ,EAAE,CAAC;gBACXG,KAAK,EAAE,IAAI;gBACXJ,OAAO,EAAE,CAAC;cACZ,CAAC;cAAAqC,SAAA,CAAA1H,IAAA;cAGC;cACMuH,gBAAgB,GAAGK,WAAW,CAACvD,GAAG,CAAC,CAAC,EAE1C;cAAA,MACI4B,QAAQ,CAAC4B,GAAG,IAAI,OAAO5B,QAAQ,CAAC4B,GAAG,KAAK,UAAU;gBAAAH,SAAA,CAAArJ,IAAA;gBAAA;cAAA;cAAAqJ,SAAA,CAAArJ,IAAA;cAAA,OAC3B4H,QAAQ,CAAC4B,GAAG,CAACvE,OAAO,CAAC;YAAA;cAAxC4C,UAAU,GAAAwB,SAAA,CAAA5J,IAAA;cAEhB;cACM0J,cAAc,GAAGI,WAAW,CAACvD,GAAG,CAAC,CAAC;cACxCiD,MAAM,CAACjC,OAAO,CAACyC,aAAa,GAAGN,cAAc,GAAGD,gBAAgB;;cAEhE;cACA,IAAIrB,UAAU,KAAK,IAAI,IAAKA,UAAU,IAAIA,UAAU,CAAChB,OAAQ,EAAE;gBAC7DoC,MAAM,CAACjB,MAAM,GAAG,QAAQ;gBACxB,IAAIH,UAAU,IAAIA,UAAU,CAACb,OAAO,EAAE;kBACpCiC,MAAM,CAACjC,OAAO,GAAA0C,aAAA,CAAAA,aAAA,KAAQT,MAAM,CAACjC,OAAO,GAAKa,UAAU,CAACb,OAAO,CAAE;gBAC/D;cACF,CAAC,MAAM;gBACLiC,MAAM,CAACjB,MAAM,GAAG,QAAQ;gBACxBiB,MAAM,CAAC7B,KAAK,GAAGS,UAAU,IAAIA,UAAU,CAACT,KAAK,GAAGS,UAAU,CAACT,KAAK,GAAG,MAAM;cAC3E;cAACiC,SAAA,CAAArJ,IAAA;cAAA;YAAA;cAEDiJ,MAAM,CAACjB,MAAM,GAAG,SAAS;cACzBiB,MAAM,CAAC7B,KAAK,GAAG,aAAa;YAAA;cAAAiC,SAAA,CAAArJ,IAAA;cAAA;YAAA;cAAAqJ,SAAA,CAAA1H,IAAA;cAAA0H,SAAA,CAAA1C,EAAA,GAAA0C,SAAA;cAG9BJ,MAAM,CAACjB,MAAM,GAAG,QAAQ;cACxBiB,MAAM,CAAC7B,KAAK,GAAGiC,SAAA,CAAA1C,EAAA,CAAMH,OAAO;YAAA;cAG9ByC,MAAM,CAAChC,QAAQ,GAAGlB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGgD,aAAa;cAAA,OAAAK,SAAA,CAAAzJ,MAAA,WACrCqJ,MAAM;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA,CACd;MAAA,SAAAP,YAAAmB,GAAA,EAAAC,GAAA;QAAA,OAAAd,YAAA,CAAAvF,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAkF,WAAA;IAAA;IAED;AACF;AACA;AACA;IAHE;EAAA;IAAA3E,GAAA;IAAAtH,KAAA,EAIA,SAAAqK,oBAAA,EAAsB;MACpB,IAAMlB,OAAO,GAAG;QACdP,WAAW,EAAE,IAAI,CAACb,WAAW,CAAC4B,IAAI;QAClC2D,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,IAAI,CAACtF,OAAO,GAAG,IAAI,CAACD,SAAS;QAC5CwF,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAE,EAAE;QACrBC,eAAe,EAAE;MACnB,CAAC;;MAED;MAAA,IAAAC,UAAA,GAAAzH,0BAAA,CACuC,IAAI,CAACyB,WAAW;QAAAiG,MAAA;MAAA;QAAvD,KAAAD,UAAA,CAAAtM,CAAA,MAAAuM,MAAA,GAAAD,UAAA,CAAAnO,CAAA,IAAAkD,IAAA,GAAyD;UAAA,IAAAmL,YAAA,GAAAlI,cAAA,CAAAiI,MAAA,CAAAhO,KAAA;YAA7CiJ,SAAS,GAAAgF,YAAA;YAAE/E,WAAW,GAAA+E,YAAA;UAChC9E,OAAO,CAACmE,UAAU,IAAIpE,WAAW,CAACwC,KAAK,CAACpH,MAAM;UAC9C6E,OAAO,CAACoE,WAAW,IAAIrE,WAAW,CAACyC,MAAM;UACzCxC,OAAO,CAACqE,WAAW,IAAItE,WAAW,CAAC0C,MAAM;UACzCzC,OAAO,CAACsE,YAAY,IAAIvE,WAAW,CAAC2C,OAAO;;UAE3C;UACA,IAAI3C,WAAW,CAACuC,MAAM,KAAK,OAAO,IAAIvC,WAAW,CAAC0C,MAAM,GAAG,CAAC,EAAE;YAC5DzC,OAAO,CAACyE,cAAc,CAAC3J,IAAI,CAAC;cAC1BiK,KAAK,EAAEjF,SAAS;cAChB6C,MAAM,EAAE5C,WAAW,CAAC4C;YACtB,CAAC,CAAC;UACJ;QACF;;QAEA;MAAA,SAAAqC,GAAA;QAAAJ,UAAA,CAAAxO,CAAA,CAAA4O,GAAA;MAAA;QAAAJ,UAAA,CAAAvM,CAAA;MAAA;MACA,IAAI2H,OAAO,CAACmE,UAAU,GAAG,CAAC,EAAE;QAC1BnE,OAAO,CAACwE,WAAW,GAAG5D,IAAI,CAACC,KAAK,CAAEb,OAAO,CAACoE,WAAW,GAAGpE,OAAO,CAACmE,UAAU,GAAI,GAAG,CAAC;MACpF;;MAEA;MACA,IAAI,CAACc,wBAAwB,CAACjF,OAAO,CAAC;;MAEtC;MACA,IAAI,CAACkF,uBAAuB,CAAClF,OAAO,CAAC;MAErC,OAAOA,OAAO;IAChB;;IAEA;AACF;AACA;AACA;EAHE;IAAA7B,GAAA;IAAAtH,KAAA,EAIA,SAAAoO,yBAAyBjF,OAAO,EAAE;MAAA,IAAAmF,UAAA,GAAAhI,0BAAA,CACO,IAAI,CAACyB,WAAW;QAAAwG,MAAA;MAAA;QAAvD,KAAAD,UAAA,CAAA7M,CAAA,MAAA8M,MAAA,GAAAD,UAAA,CAAA1O,CAAA,IAAAkD,IAAA,GAAyD;UAAA,IAAA0L,YAAA,GAAAzI,cAAA,CAAAwI,MAAA,CAAAvO,KAAA;YAA7CiJ,SAAS,GAAAuF,YAAA;YAAEtF,WAAW,GAAAsF,YAAA;UAAA,IAAAC,UAAA,GAAAnI,0BAAA,CACb4C,WAAW,CAACwC,KAAK;YAAAgD,MAAA;UAAA;YAApC,KAAAD,UAAA,CAAAhN,CAAA,MAAAiN,MAAA,GAAAD,UAAA,CAAA7O,CAAA,IAAAkD,IAAA,GAAsC;cAAA,IAA3B8D,IAAI,GAAA8H,MAAA,CAAA1O,KAAA;cACb;cACA,IAAI4G,IAAI,CAAC6D,OAAO,CAACyC,aAAa,GAAG,IAAI,EAAE;gBAAE;gBACvC/D,OAAO,CAAC0E,iBAAiB,CAAC5J,IAAI,CAAC;kBAC7B9C,IAAI,EAAE,gBAAgB;kBACtB+M,KAAK,EAAEjF,SAAS;kBAChBrC,IAAI,EAAEA,IAAI,CAAClC,IAAI;kBACf1E,KAAK,EAAE4G,IAAI,CAAC6D,OAAO,CAACyC,aAAa;kBACjCyB,SAAS,EAAE,IAAI;kBACf1E,OAAO,uDAAAC,MAAA,CAAetD,IAAI,CAAC6D,OAAO,CAACyC,aAAa;gBAClD,CAAC,CAAC;cACJ;;cAEA;cACA,IAAItG,IAAI,CAAC6D,OAAO,CAACmE,WAAW,IAAIhI,IAAI,CAAC6D,OAAO,CAACmE,WAAW,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE;gBAAE;gBAC9EzF,OAAO,CAAC0E,iBAAiB,CAAC5J,IAAI,CAAC;kBAC7B9C,IAAI,EAAE,aAAa;kBACnB+M,KAAK,EAAEjF,SAAS;kBAChBrC,IAAI,EAAEA,IAAI,CAAClC,IAAI;kBACf1E,KAAK,EAAE4G,IAAI,CAAC6D,OAAO,CAACmE,WAAW;kBAC/BD,SAAS,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;kBAC5B1E,OAAO,2CAAAC,MAAA,CAAaH,IAAI,CAACC,KAAK,CAACpD,IAAI,CAAC6D,OAAO,CAACmE,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;gBACxE,CAAC,CAAC;cACJ;YACF;UAAC,SAAAT,GAAA;YAAAM,UAAA,CAAAlP,CAAA,CAAA4O,GAAA;UAAA;YAAAM,UAAA,CAAAjN,CAAA;UAAA;QACH;MAAC,SAAA2M,GAAA;QAAAG,UAAA,CAAA/O,CAAA,CAAA4O,GAAA;MAAA;QAAAG,UAAA,CAAA9M,CAAA;MAAA;IACH;;IAEA;AACF;AACA;AACA;EAHE;IAAA8F,GAAA;IAAAtH,KAAA,EAIA,SAAAqO,wBAAwBlF,OAAO,EAAE;MAC/B;MACA,IAAIA,OAAO,CAACwE,WAAW,GAAG,EAAE,EAAE;QAC5BxE,OAAO,CAAC2E,eAAe,CAAC7J,IAAI,CAAC;UAC3B9C,IAAI,EAAE,SAAS;UACf0N,QAAQ,EAAE,MAAM;UAChB5E,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;;MAEA;MACA,IAAId,OAAO,CAAC0E,iBAAiB,CAACvJ,MAAM,GAAG,CAAC,EAAE;QACxC6E,OAAO,CAAC2E,eAAe,CAAC7J,IAAI,CAAC;UAC3B9C,IAAI,EAAE,aAAa;UACnB0N,QAAQ,EAAE,QAAQ;UAClB5E,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;;MAEA;MACA,IAAId,OAAO,CAACyE,cAAc,CAACtJ,MAAM,GAAG,CAAC,EAAE;QACrC6E,OAAO,CAAC2E,eAAe,CAAC7J,IAAI,CAAC;UAC3B9C,IAAI,EAAE,WAAW;UACjB0N,QAAQ,EAAE,MAAM;UAChB5E,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;;MAEA;MACA,IAAId,OAAO,CAACmE,UAAU,GAAG,EAAE,EAAE;QAC3BnE,OAAO,CAAC2E,eAAe,CAAC7J,IAAI,CAAC;UAC3B9C,IAAI,EAAE,UAAU;UAChB0N,QAAQ,EAAE,QAAQ;UAClB5E,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;;IAEA;AACF;AACA;AACA;EAHE;IAAA3C,GAAA;IAAAtH,KAAA,EAIA,SAAA8O,cAAA,EAAgB;MACd,OAAO;QACL7G,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB4G,gBAAgB,EAAE3I,KAAK,CAACO,IAAI,CAAC,IAAI,CAACkB,UAAU,CAAC5C,IAAI,CAAC,CAAC;MACrD,CAAC;IACH;;IAEA;AACF;AACA;EAFE;IAAAqC,GAAA;IAAAtH,KAAA,EAGA,SAAAgP,UAAA,EAAY;MACV,IAAI,CAAC/G,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACzB;;IAEA;AACF;AACA;EAFE;IAAAZ,GAAA;IAAAtH,KAAA,EAGA,SAAAiP,aAAA,EAAe;MACb,IAAI,CAAClH,WAAW,CAAC2B,KAAK,CAAC,CAAC;MACxB,IAAI,CAAC1B,kBAAkB,CAAC0B,KAAK,CAAC,CAAC;MAC/B,IAAI,CAACvB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACrB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAd,GAAA;IAAAtH,KAAA,EAKA,SAAAkP,aAAA,EAA8B;MAAA,IAAjBC,MAAM,GAAApI,SAAA,CAAAzC,MAAA,QAAAyC,SAAA,QAAAwC,SAAA,GAAAxC,SAAA,MAAG,MAAM;MAC1B,IAAMqI,UAAU,GAAG;QACjBC,SAAS,EAAE,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,WAAW,CAAC,CAAC;QACnCnG,OAAO,EAAE,IAAI,CAACkB,mBAAmB,CAAC,CAAC;QACnCE,OAAO,EAAE7K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACzC,WAAW,CAAC;QAC7C0C,OAAO,EAAE/K,MAAM,CAAC8K,WAAW,CAAC,IAAI,CAACxC,kBAAkB;MACrD,CAAC;MAED,QAAQmH,MAAM;QACZ,KAAK,MAAM;UACT,OAAOI,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5C,KAAK,MAAM;UACT,OAAO,IAAI,CAACK,kBAAkB,CAACL,UAAU,CAAC;QAC5C,KAAK,KAAK;UACR,OAAO,IAAI,CAACM,iBAAiB,CAACN,UAAU,CAAC;QAC3C;UACE,MAAM,IAAIvM,KAAK,sDAAAqH,MAAA,CAAciF,MAAM,CAAE,CAAC;MAC1C;IACF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA7H,GAAA;IAAAtH,KAAA,EAKA,SAAAyP,mBAAmBL,UAAU,EAAE;MAC7B,4vBAAAlF,MAAA,CAmBekF,UAAU,CAACC,SAAS,uDAAAnF,MAAA,CACnBkF,UAAU,CAACjG,OAAO,CAACP,WAAW,uDAAAsB,MAAA,CAC9BkF,UAAU,CAACjG,OAAO,CAACmE,UAAU,yDAAApD,MAAA,CAClBkF,UAAU,CAACjG,OAAO,CAACwE,WAAW,IAAI,EAAE,GAAG,SAAS,GAAG,OAAO,SAAAzD,MAAA,CAAKkF,UAAU,CAACjG,OAAO,CAACwE,WAAW,mDAAAzD,MAAA,CAC1GkF,UAAU,CAACjG,OAAO,CAACuE,aAAa,0EAAAxD,MAAA,CAGvCqF,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC7E,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAGpD;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAjD,GAAA;IAAAtH,KAAA,EAKA,SAAA0P,kBAAkBN,UAAU,EAAE;MAC5B,IAAIO,GAAG,GAAG,oCAAoC;MAE9C,SAAAC,EAAA,MAAAC,eAAA,GAAuCnQ,MAAM,CAACoQ,OAAO,CAACV,UAAU,CAAC7E,OAAO,CAAC,EAAAqF,EAAA,GAAAC,eAAA,CAAAvL,MAAA,EAAAsL,EAAA,IAAE;QAAtE,IAAAG,kBAAA,GAAAhK,cAAA,CAAA8J,eAAA,CAAAD,EAAA;UAAO3G,SAAS,GAAA8G,kBAAA;UAAE7G,WAAW,GAAA6G,kBAAA;QAAC,IAAAC,UAAA,GAAA1J,0BAAA,CACd4C,WAAW,CAACwC,KAAK;UAAAuE,MAAA;QAAA;UAApC,KAAAD,UAAA,CAAAvO,CAAA,MAAAwO,MAAA,GAAAD,UAAA,CAAApQ,CAAA,IAAAkD,IAAA,GAAsC;YAAA,IAA3B8D,IAAI,GAAAqJ,MAAA,CAAAjQ,KAAA;YACb2P,GAAG,SAAAzF,MAAA,CAAQjB,SAAS,WAAAiB,MAAA,CAAMtD,IAAI,CAAClC,IAAI,WAAAwF,MAAA,CAAMtD,IAAI,CAAC6E,MAAM,SAAAvB,MAAA,CAAKtD,IAAI,CAAC8D,QAAQ,SAAAR,MAAA,CAAKtD,IAAI,CAACiE,KAAK,IAAI,EAAE,SAAK;UAClG;QAAC,SAAAsD,GAAA;UAAA6B,UAAA,CAAAzQ,CAAA,CAAA4O,GAAA;QAAA;UAAA6B,UAAA,CAAAxO,CAAA;QAAA;MACH;MAEA,OAAOmO,GAAG;IACZ;EAAC;AAAA;AAGH,eAAe/H,WAAW", "ignoreList": []}]}