{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SuperQueryModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SuperQueryModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { httpAction } from '@/api/manage';\nexport default {\n  name: \"SuperQueryModal\",\n  data: function data() {\n    return {\n      visible: false,\n      queryParamsModel: [{}, {}],\n      confirmLoading: false\n    };\n  },\n  created: function created() {},\n  methods: {\n    show: function show() {\n      this.visible = true;\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      console.log(this.queryParamsModel);\n      // 子组件中触发父组件方法ee并传值cc12345\n      this.$emit('handleSuperQuery', this.queryParamsModel);\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    handleAdd: function handleAdd() {\n      this.queryParamsModel.push({});\n    },\n    handleDel: function handleDel(index) {\n      console.log(index);\n      this.queryParamsModel.splice(index, 1);\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "name", "data", "visible", "queryParamsModel", "confirmLoading", "created", "methods", "show", "close", "$emit", "handleOk", "console", "log", "handleCancel", "handleAdd", "push", "handleDel", "index", "splice"], "sources": ["src/views/jeecg/modules/SuperQueryModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"高级查询构造器\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    :mask=\"false\"\n    okText=\"查询\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form>\n        <div>\n          <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in queryParamsModel\" :key=\"index\">\n            <a-col :span=\"6\">\n              <a-select  placeholder=\"选择查询字段\" v-model=\"item.field\">\n                <a-select-option value=\"name\">用户名</a-select-option>\n                <a-select-option value=\"key_word\">关键词</a-select-option>\n                <a-select-option value=\"birthday\">生日</a-select-option>\n                <a-select-option value=\"age\">年龄</a-select-option>\n              </a-select>\n            </a-col>\n            <a-col :span=\"6\">\n              <a-select placeholder=\"选择匹配规则\"  v-model=\"item.rule\">\n                <a-select-option value=\"=\">等于</a-select-option>\n                <a-select-option value=\"!=\">不等于</a-select-option>\n                <a-select-option value=\">\">大于</a-select-option>\n                <a-select-option value=\">=\">大于等于</a-select-option>\n                <a-select-option value=\"<\">小于</a-select-option>\n                <a-select-option value=\"<=\">小于等于</a-select-option>\n                <a-select-option value=\"LEFT_LIKE\">以..开始</a-select-option>\n                <a-select-option value=\"RIGHT_LIKE\">以..结尾</a-select-option>\n                <a-select-option value=\"LIKE\">包含</a-select-option>\n                <a-select-option value=\"IN\">在...中</a-select-option>\n              </a-select>\n            </a-col>\n\n            <a-col :span=\"6\"><a-input placeholder=\"请输入值\" v-model=\"item.val\"/></a-col>\n            <a-col :span=\"6\">\n              <a-button @click=\"handleAdd\"  icon=\"plus\"></a-button>&nbsp;\n              <a-button @click=\"handleDel( index )\"  icon=\"minus\"></a-button>\n            </a-col>\n          </a-row>\n        </div>\n\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { httpAction } from '@/api/manage'\n\n  export default {\n    name: \"SuperQueryModal\",\n    data () {\n      return {\n        visible: false,\n        queryParamsModel: [{},{}],\n        confirmLoading: false\n      }\n    },\n    created () {\n    },\n    methods: {\n      show () {\n        this.visible = true;\n      },\n      close () {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        console.log(this.queryParamsModel)\n        // 子组件中触发父组件方法ee并传值cc12345\n        this.$emit('handleSuperQuery', this.queryParamsModel)\n      },\n      handleCancel () {\n        this.close()\n      },\n      handleAdd () {\n        this.queryParamsModel.push({});\n      },\n      handleDel (index) {\n        console.log(index)\n        this.queryParamsModel.splice(index,1);\n      }\n    }\n  }\n</script>\n\n<style scoped>\n</style>"], "mappings": "AAqDA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,gBAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAL,OAAA;IACA;IACAM,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAAP,OAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACAC,OAAA,CAAAC,GAAA,MAAAT,gBAAA;MACA;MACA,KAAAM,KAAA,0BAAAN,gBAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MACA,KAAAL,KAAA;IACA;IACAM,SAAA,WAAAA,UAAA;MACA,KAAAX,gBAAA,CAAAY,IAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACAN,OAAA,CAAAC,GAAA,CAAAK,KAAA;MACA,KAAAd,gBAAA,CAAAe,MAAA,CAAAD,KAAA;IACA;EACA;AACA", "ignoreList": []}]}