{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\AvatarList\\Item.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import Avatar from 'ant-design-vue/es/avatar'\n  import Tooltip from 'ant-design-vue/es/tooltip'\n\n  export default {\n    name: \"AvatarItem\",\n    components: {\n      Avatar,\n      Tooltip\n    },\n    props: {\n      tips: {\n        type: String,\n        default: '',\n        required: false\n      },\n      src: {\n        type: String,\n        default: ''\n      }\n    },\n    data () {\n      return {\n        size: this.$parent.size\n      }\n    },\n    computed: {\n      avatarSize () {\n        return this.size !== 'mini' && this.size || 20\n      }\n    },\n    watch: {\n      '$parent.size' (val) {\n        this.size = val\n      }\n    }\n  }\n", {"version": 3, "sources": ["Item.vue"], "names": [], "mappings": ";AASA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Item.vue", "sourceRoot": "src/components/AvatarList", "sourcesContent": ["<template>\n  <tooltip v-if=\"tips !== ''\">\n    <template slot=\"title\">{{ tips }}</template>\n    <avatar :size=\"avatarSize\" :src=\"src\" />\n  </tooltip>\n  <avatar v-else :size=\"avatarSize\" :src=\"src\" />\n</template>\n\n<script>\n  import Avatar from 'ant-design-vue/es/avatar'\n  import Tooltip from 'ant-design-vue/es/tooltip'\n\n  export default {\n    name: \"AvatarItem\",\n    components: {\n      Avatar,\n      Tooltip\n    },\n    props: {\n      tips: {\n        type: String,\n        default: '',\n        required: false\n      },\n      src: {\n        type: String,\n        default: ''\n      }\n    },\n    data () {\n      return {\n        size: this.$parent.size\n      }\n    },\n    computed: {\n      avatarSize () {\n        return this.size !== 'mini' && this.size || 20\n      }\n    },\n    watch: {\n      '$parent.size' (val) {\n        this.size = val\n      }\n    }\n  }\n</script>"]}]}