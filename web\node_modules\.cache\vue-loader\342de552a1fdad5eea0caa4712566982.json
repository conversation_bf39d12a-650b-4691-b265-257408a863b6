{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkDetail.vue", "mtime": 1749627321850}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport Vue from 'vue'\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\nimport { mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport QrCode from '@/components/tools/QrCode'\nimport Keyboard from '@/components/tools/Keyboard'\nimport Header from './modules/Header'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\n\nimport moment from 'moment'\nimport { postAction } from '../../api/manage'\nexport default {\n  name:\"WorkDetail\",\n  components: {\n    qrcode: QrCode,\n    Keyboard,\n    Header,\n    Footer,\n    UserEnter\n  },\n  data() {\n    return {\n      workId: '',\n      workInfo: {},\n      frameHref: '',\n      token: '',\n      commentContent: '',\n      showLoadingMore: true,\n      loadingMore: false,\n      commentsPage: 0,\n      comments: [],\n      shareHtml: '',\n      sysConfig: {},\n    }\n  },\n  created() {\n    this.workId = this.$route.query.id\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    this.sysConfig = this.$store.getters.sysConfig\n    this.getWorkInfo(this.workId)\n    this.getWorkShareHtml()\n  },\n  mounted() {\n    var that = this\n    //scratch全屏\n    document.addEventListener('scratchFullScreen', function (e) {\n      window.launchIntoFullscreen(document.getElementById('player'))\n    })\n    //scratch退出全屏\n    document.addEventListener('scratchUnFullScreen', function (e) {\n      window.exitFullscreen()\n    })\n    document.addEventListener('scratchInit', function (e) {\n      var p = document.getElementById('player')\n      var s = p.contentDocument.getElementById('scratch')\n      s.addEventListener('click', () => {\n        p.focus()\n      })\n    })\n    //计算播放器高度\n    let playerDom = document.getElementById(\"player\");\n    playerDom.style.height = playerDom.clientWidth * 0.9 + \"px\";\n  },\n  methods: {\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    moment,\n    getFileAccessHttpUrl,\n    keyEvent(key, keyCode, isDown){\n      let player = document.getElementById(\"player\");\n      player.contentWindow.vm.postIOData(\"keyboard\", {\n        keyCode: keyCode,\n        key: key,\n        isDown: isDown,\n      });\n    },\n    getWorkInfo(workId) {\n      getAction('/teaching/teachingWork/studentWorkInfo?workId=' + workId).then((res) => {\n        if (res.success) {\n          this.workInfo = res.result\n          this.previewCode(this.workInfo)\n          this.workComments()\n        } else {\n          this.$message.error('作品获取失败')\n        }\n      })\n    },\n    previewCode(record) {\n      this.visible = true\n      // this.frameHref = '/scratch3/player.html?workId=' + record.id\n      this.frameHref = ''\n      switch (record.workType) {\n        case '1':\n          this.frameHref = '/scratch3/player.html?workId=' + record.id\n          return\n        case '2':\n          this.frameHref = '/scratch3/player.html?workId=' + record.id\n          return\n        case '3':\n          this.frameHref = '/scratchjr/editor.html?mode=look&workFile=' + record.workFileKey_url\n          return\n        case '4':\n          this.frameHref = '/python/player.html?lang=turtle&url=' + record.workFileKey_url\n          return\n        case '5': // 添加C++支持\n          this.frameHref = '/cpp/player.html?url=' + record.workFileKey_url\n          return\n        case '10':\n          this.frameHref = '/blockly/index.html?lang=zh-hans&workId=' + record.id\n          return\n      }\n    },\n    starWork() {\n      getAction('/teaching/teachingWork/starWork?workId=' + this.workId).then((res) => {\n        if (res.success) {\n          if (res.message == '点赞成功') {\n            this.workInfo.starNum += 1\n            \n            // 调用任务接口，更新点赞任务状态\n            postAction('/teaching/dailyTask/likeTask', { workId: this.workId }).then((taskRes) => {\n              if (taskRes.success && taskRes.result > 0) {\n                this.$message.success('完成每日点赞任务，获得' + taskRes.result + '金币')\n              }\n            })\n          }\n          this.$message.success(res.message)\n        }\n      })\n    },\n    workComments() {\n      this.commentsPage += 1\n      this.loadingMore = true\n      getAction('/teaching/teachingWork/getWorkComments', { workId: this.workId, page: this.commentsPage }).then(\n        (res) => {\n          this.loadingMore = false\n          if (res.result.length == 0 && this.commentsPage>1) {\n              this.$message.info('已加载完啦！')\n          } else {\n            this.comments = this.comments.concat(res.result)\n          }\n        }\n      )\n    },\n    comment() {\n      if (this.commentContent != '') {\n        postAction('/teaching/teachingWork/saveComment', { workId: this.workId, comment: this.commentContent }).then(\n          (res) => {\n            if (res.success) {\n              this.comments.unshift({\n                  comment: this.commentContent,\n                  realname: this.nickname(),\n                  avatar_url: this.getFileAccessHttpUrl(this.avatar()),\n                  createTime: new Date()\n              })\n              this.commentContent = ''\n              this.$message.success(res.message)\n              \n              // 调用任务接口，更新评论任务状态\n              postAction('/teaching/dailyTask/commentTask', { workId: this.workId }).then((taskRes) => {\n                if (taskRes.success && taskRes.result > 0) {\n                  this.$message.success('完成每日评论任务，获得' + taskRes.result + '金币')\n                }\n              })\n            }\n          }\n        )\n      }\n    },\n    getWorkShareHtml(){\n      getAction(\"/sys/config/getConfig?key=_workShareHtml\").then(res=>{\n        if(res.success){\n          this.shareHtml = res.result\n        }\n      })\n    },\n    getShareUrl() {\n      return window.location.protocol + '//' + window.location.host + '/work-detail?id=' + this.workId\n    },\n    enter() {\n      this.$router.push('/account/center')\n    },\n    _isMobile() {\n      return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i) != null\n    },\n    \n  },\n}\n", {"version": 3, "sources": ["WorkDetail.vue"], "names": [], "mappings": ";AA6IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "WorkDetail.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div class=\"container\"\n    :style=\"{\n      backgroundColor: sysConfig.homeBgColor,\n      backgroundImage: sysConfig.file_homeBg ? 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')' : '',\n      backgroundRepeat: sysConfig.homeBgRepeat ? sysConfig.homeBgRepeat : '',\n    }\"\n  >\n    <a-layout>\n      <a-layout-header>\n        <Header/>\n      </a-layout-header>\n      <a-layout>\n        <a-layout-content>\n          <div class=\"project-detail\">\n            <!-- 播放器 -->\n            <div class=\"scratch-player\">\n              <iframe\n                :src=\"frameHref\"\n                id=\"player\"\n                frameborder=\"0\"\n                width=\"100%\"\n                height=\"100%\"\n                :scrolling=\"workInfo.workType==4||workInfo.workType==5||workInfo.workType==10?'auto':'no'\"\n              ></iframe>\n            </div>\n            <keyboard v-if=\"_isMobile() && workInfo.workType==2\" @event=\"keyEvent\"/>\n            <!-- 作品信息 -->\n            <div class=\"project-info\">\n              <a-row type=\"flex\" justify=\"space-around\">\n                <a-col :span=\"4\">\n                  <a-avatar shape=\"square\" class=\"avatar\" :size=\"60\" :src=\"workInfo.avatar_url\" />\n                  <p>{{ workInfo.realname || workInfo.username }}</p>\n                </a-col>\n                <a-col :span=\"14\" v-if=\"!_isMobile()\">\n                  <div class=\"project-meta\">\n                    <h2 class=\"title\">{{ workInfo.workName }}</h2>\n                    <p class=\"time\">{{ workInfo.createTime }}</p>\n                  </div>\n                </a-col>\n                <a-col :span=\"_isMobile()?12:6\">\n                  <div class=\"project-op\">\n                    <a-icon type=\"eye\" theme=\"twoTone\" />\n                    <span class=\"gap\">{{ workInfo.viewNum }}</span>\n\n                    <a-icon type=\"like\" theme=\"twoTone\" @click=\"starWork\" />\n                    <span class=\"gap\">{{ workInfo.starNum }}</span>\n\n                    <a-popover v-if=\"!_isMobile()\" title=\"微信扫一扫手机体验和分享\">\n                      <template slot=\"content\">\n                        <qrcode :value=\"getShareUrl()\" :size=\"200\" level=\"H\"></qrcode>\n                      </template>\n                      <a-icon type=\"mobile\" theme=\"twoTone\" />\n                    </a-popover>\n                  </div>\n                </a-col>\n                <a-col :span=\"24\" v-if=\"_isMobile()\">\n                  <div class=\"project-meta\">\n                    <h2 class=\"title\">{{ workInfo.workName }}</h2>\n                    <p class=\"time\">{{ workInfo.createTime }}</p>\n                  </div>\n                </a-col>\n              </a-row>\n            </div>\n\n            <!-- 评论区 -->\n            <div class=\"project-comment\">\n              <div class=\"publish\">\n                <a-row type=\"flex\" justify=\"space-between\">\n                  <a-col :span=\"3\" class=\"comment-user\"  v-if=\"!_isMobile()\">\n                    <a-avatar shape=\"square\" :size=\"60\" icon=\"user\" :src=\"getFileAccessHttpUrl(avatar())\" />\n                    <p>\n                      {{ token ? nickname() : '未登录' }}\n                    </p>\n                  </a-col>\n                  <a-col :span=\"16\">\n                    <a-textarea\n                      v-model=\"commentContent\"\n                      :rows=\"5\"\n                      :maxLength=\"500\"\n                      placeholder=\"说说这个作品怎么样吧\"\n                    ></a-textarea>\n                  </a-col>\n                  <a-col :span=\"_isMobile()?6:4\">\n                    <div class=\"comment-btn\">\n                      <a-button :disabled=\"!token\" type=\"dashed\" @click=\"comment\">发表评论</a-button>\n                    </div>\n                  </a-col>\n                </a-row>\n              </div>\n              <a-divider />\n              <a-list\n                class=\"comment-list\"\n                item-layout=\"horizontal\"\n                :locale=\"{ emptyText: '暂无评论' }\"\n                :data-source=\"comments\"\n              >\n                <a-list-item slot=\"renderItem\" slot-scope=\"item\">\n                  <a-comment :author=\"item.realname || item.username\">\n                    <a-avatar shape=\"square\" :size=\"40\" slot=\"avatar\" icon=\"user\" :src=\"item.avatar_url\" />\n                    <p class=\"comment-content\" slot=\"content\">\n                      {{ item.comment }}\n                    </p>\n                    <a-tooltip slot=\"datetime\" :title=\"moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')\">\n                      <span>{{ moment(item.createTime).fromNow() }}</span>\n                    </a-tooltip>\n                  </a-comment>\n                </a-list-item>\n                <div\n                  v-if=\"showLoadingMore\"\n                  slot=\"loadMore\"\n                  :style=\"{\n                    textAlign: 'center',\n                    marginTop: '12px',\n                    height: '32px',\n                    lineHeight: '32px',\n                  }\"\n                >\n                  <a-spin v-if=\"loadingMore\" />\n                  <a-button type=\"link\" v-else @click=\"workComments\"> 加载更多 </a-button>\n                </div>\n              </a-list>\n            </div>\n          </div>\n          <div v-if=\"shareHtml\" class=\"work-share-html\">\n            <a-divider></a-divider>\n            <div v-html=\"shareHtml\"></div>\n          </div>\n        </a-layout-content>\n        <a-layout-sider v-if=\"!_isMobile()\">\n          <UserEnter/>\n        </a-layout-sider>\n      </a-layout>\n      <a-layout-footer>\n        <Footer/>\n      </a-layout-footer>\n    </a-layout>\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue'\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\nimport { mapGetters } from 'vuex'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport QrCode from '@/components/tools/QrCode'\nimport Keyboard from '@/components/tools/Keyboard'\nimport Header from './modules/Header'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\n\nimport moment from 'moment'\nimport { postAction } from '../../api/manage'\nexport default {\n  name:\"WorkDetail\",\n  components: {\n    qrcode: QrCode,\n    Keyboard,\n    Header,\n    Footer,\n    UserEnter\n  },\n  data() {\n    return {\n      workId: '',\n      workInfo: {},\n      frameHref: '',\n      token: '',\n      commentContent: '',\n      showLoadingMore: true,\n      loadingMore: false,\n      commentsPage: 0,\n      comments: [],\n      shareHtml: '',\n      sysConfig: {},\n    }\n  },\n  created() {\n    this.workId = this.$route.query.id\n    this.token = Vue.ls.get(ACCESS_TOKEN)\n    this.sysConfig = this.$store.getters.sysConfig\n    this.getWorkInfo(this.workId)\n    this.getWorkShareHtml()\n  },\n  mounted() {\n    var that = this\n    //scratch全屏\n    document.addEventListener('scratchFullScreen', function (e) {\n      window.launchIntoFullscreen(document.getElementById('player'))\n    })\n    //scratch退出全屏\n    document.addEventListener('scratchUnFullScreen', function (e) {\n      window.exitFullscreen()\n    })\n    document.addEventListener('scratchInit', function (e) {\n      var p = document.getElementById('player')\n      var s = p.contentDocument.getElementById('scratch')\n      s.addEventListener('click', () => {\n        p.focus()\n      })\n    })\n    //计算播放器高度\n    let playerDom = document.getElementById(\"player\");\n    playerDom.style.height = playerDom.clientWidth * 0.9 + \"px\";\n  },\n  methods: {\n    ...mapGetters(['nickname', 'avatar', 'userInfo']),\n    moment,\n    getFileAccessHttpUrl,\n    keyEvent(key, keyCode, isDown){\n      let player = document.getElementById(\"player\");\n      player.contentWindow.vm.postIOData(\"keyboard\", {\n        keyCode: keyCode,\n        key: key,\n        isDown: isDown,\n      });\n    },\n    getWorkInfo(workId) {\n      getAction('/teaching/teachingWork/studentWorkInfo?workId=' + workId).then((res) => {\n        if (res.success) {\n          this.workInfo = res.result\n          this.previewCode(this.workInfo)\n          this.workComments()\n        } else {\n          this.$message.error('作品获取失败')\n        }\n      })\n    },\n    previewCode(record) {\n      this.visible = true\n      // this.frameHref = '/scratch3/player.html?workId=' + record.id\n      this.frameHref = ''\n      switch (record.workType) {\n        case '1':\n          this.frameHref = '/scratch3/player.html?workId=' + record.id\n          return\n        case '2':\n          this.frameHref = '/scratch3/player.html?workId=' + record.id\n          return\n        case '3':\n          this.frameHref = '/scratchjr/editor.html?mode=look&workFile=' + record.workFileKey_url\n          return\n        case '4':\n          this.frameHref = '/python/player.html?lang=turtle&url=' + record.workFileKey_url\n          return\n        case '5': // 添加C++支持\n          this.frameHref = '/cpp/player.html?url=' + record.workFileKey_url\n          return\n        case '10':\n          this.frameHref = '/blockly/index.html?lang=zh-hans&workId=' + record.id\n          return\n      }\n    },\n    starWork() {\n      getAction('/teaching/teachingWork/starWork?workId=' + this.workId).then((res) => {\n        if (res.success) {\n          if (res.message == '点赞成功') {\n            this.workInfo.starNum += 1\n            \n            // 调用任务接口，更新点赞任务状态\n            postAction('/teaching/dailyTask/likeTask', { workId: this.workId }).then((taskRes) => {\n              if (taskRes.success && taskRes.result > 0) {\n                this.$message.success('完成每日点赞任务，获得' + taskRes.result + '金币')\n              }\n            })\n          }\n          this.$message.success(res.message)\n        }\n      })\n    },\n    workComments() {\n      this.commentsPage += 1\n      this.loadingMore = true\n      getAction('/teaching/teachingWork/getWorkComments', { workId: this.workId, page: this.commentsPage }).then(\n        (res) => {\n          this.loadingMore = false\n          if (res.result.length == 0 && this.commentsPage>1) {\n              this.$message.info('已加载完啦！')\n          } else {\n            this.comments = this.comments.concat(res.result)\n          }\n        }\n      )\n    },\n    comment() {\n      if (this.commentContent != '') {\n        postAction('/teaching/teachingWork/saveComment', { workId: this.workId, comment: this.commentContent }).then(\n          (res) => {\n            if (res.success) {\n              this.comments.unshift({\n                  comment: this.commentContent,\n                  realname: this.nickname(),\n                  avatar_url: this.getFileAccessHttpUrl(this.avatar()),\n                  createTime: new Date()\n              })\n              this.commentContent = ''\n              this.$message.success(res.message)\n              \n              // 调用任务接口，更新评论任务状态\n              postAction('/teaching/dailyTask/commentTask', { workId: this.workId }).then((taskRes) => {\n                if (taskRes.success && taskRes.result > 0) {\n                  this.$message.success('完成每日评论任务，获得' + taskRes.result + '金币')\n                }\n              })\n            }\n          }\n        )\n      }\n    },\n    getWorkShareHtml(){\n      getAction(\"/sys/config/getConfig?key=_workShareHtml\").then(res=>{\n        if(res.success){\n          this.shareHtml = res.result\n        }\n      })\n    },\n    getShareUrl() {\n      return window.location.protocol + '//' + window.location.host + '/work-detail?id=' + this.workId\n    },\n    enter() {\n      this.$router.push('/account/center')\n    },\n    _isMobile() {\n      return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i) != null\n    },\n    \n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n.container {\n  background: url(/img/bg_blue.png) no-repeat;\n  background-color: #f6f6f6;\n  background-size: 100% 250px;\n}\n.ant-layout-header,\n.ant-layout-content,\n.ant-layout-sider,\n.ant-layout-sider-children,\n.ant-layout-footer {\n  background: transparent;\n}\n.ant-layout {\n  background: transparent;\n  min-height: calc(100vh - 200px);\n}\n.ant-layout-header {\n  height: 250px;\n  width: 100%;\n  padding: 0;\n}\n\n.ant-layout-has-sider {\n  max-width: 1100px;\n  width: 100%;\n  margin: -100px auto 0;\n}\n\n.project-detail {\n  max-width: 780px;\n  background: #fff;\n  border-radius: 10px;\n  padding: 20px;\n  .scratch-player {\n    margin: auto;\n    iframe {\n      max-width: 720px;\n      max-height: 600px;\n    }\n  }\n  .project-info {\n    padding: 10px;\n    text-align: center;\n    .project-meta {\n      text-align: left;\n    }\n    .project-op {\n      color: #797979;\n      .anticon {\n        font-size: 28px;\n      }\n      .gap {\n        margin-right: 20px;\n        font-size: 24px;\n      }\n    }\n  }\n  .project-comment {\n    border: #e0e0e0 solid 1px;\n    padding: 10px;\n    .publish {\n      .comment-user {\n        padding-top: 20px;\n      }\n      .comment-btn {\n        padding-top: 10px;\n        button {\n          height: 80px;\n        }\n      }\n    }\n    .comment-list {\n      color: #999;\n      border: 1px solid #e9e9e9;\n      border-top: none;\n      padding: 0 20px;\n      .comment-content {\n        width: 600px;\n      }\n      .item {\n        padding: 20px;\n        font-size: 13px;\n        border-bottom: 1px solid #e9e9e9;\n\n        .people {\n          font-size: 30px;\n        }\n        .user {\n          color: #666;\n          font-size: 14px;\n          margin-right: 5px;\n        }\n        .time {\n          font-size: 12px;\n        }\n        span {\n          margin-right: 30px;\n        }\n        p {\n          margin: 10px 0;\n        }\n      }\n      .more {\n        font-size: 13px;\n        text-align: center;\n        padding-top: 40px;\n        padding-bottom: 20px;\n        > span {\n          border: 1px solid #e9e9e9;\n          line-height: 47px;\n          width: 140px;\n          height: 47px;\n          display: inline-block;\n          color: #999;\n        }\n      }\n    }\n  }\n}\n\n.work-share-html{\n  max-width: 780px;\n}\n\n.ant-layout-sider {\n  max-width: 300px !important;\n  width: 300px !important;\n}\n\n</style>"]}]}