{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue?vue&type=style&index=1&id=1c8278da&lang=less", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue", "mtime": 1753093743655}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n// 全局样式，用于覆盖 Ant Design 的默认样式\n.result-modal {\n  .ant-modal-content {\n    border-radius: 16px;\n    overflow: hidden;\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n\n  // 按钮样式\n  .ant-btn {\n    font-weight: 500;\n    height: 40px;\n    border-radius: 20px;\n    padding: 0 20px;\n    transition: all 0.3s;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    &.ant-btn-primary {\n      background: linear-gradient(135deg, #1890ff, #096dd9);\n      border: none;\n      box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);\n\n      &:hover {\n        background: linear-gradient(135deg, #40a9ff, #1890ff);\n        box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);\n      }\n    }\n\n    &:not(.ant-btn-primary) {\n      background: #fff;\n      border: 1px solid #d9d9d9;\n\n      &:hover {\n        border-color: #40a9ff;\n        color: #40a9ff;\n      }\n    }\n\n    @media screen and (max-width: 768px) {\n      flex: 1;\n      min-width: 120px;\n    }\n  }\n}\n", {"version": 3, "sources": ["PracticeResultModal.vue"], "names": [], "mappings": ";AAwNA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "PracticeResultModal.vue", "sourceRoot": "src/views/examSystem/components", "sourcesContent": ["<template>\n  <a-modal\n    v-model=\"visible\"\n    :footer=\"null\"\n    width=\"680px\"\n    :maskClosable=\"false\"\n    :keyboard=\"false\"\n    :closable=\"false\"\n    class=\"result-modal\"\n  >\n    <!-- 自定义标题栏 -->\n    <template slot=\"title\">\n      <div style=\"display: flex; align-items: center;\">\n        <a-icon type=\"trophy\" theme=\"filled\" style=\"color: #52c41a; font-size: 20px; margin-right: 8px;\" />\n        <span style=\"font-size: 18px; font-weight: 500;\">练习完成</span>\n      </div>\n    </template>\n    \n    <!-- 现代化设计的结果页 -->\n    <div style=\"padding: 0;\">\n      <!-- 顶部祝贺信息 -->\n      <div style=\"background: linear-gradient(135deg, #f6ffed, #e6f7ff); padding: 24px 0; text-align: center; border-radius: 8px; margin-bottom: 20px;\">\n        <a-icon type=\"check-circle\" theme=\"filled\" style=\"font-size: 48px; color: #52c41a; margin-bottom: 16px;\" />\n        <h2 style=\"margin: 0; font-size: 24px; color: #262626; font-weight: 500;\">恭喜您完成本次刷题!</h2>\n      </div>\n      \n      <!-- 主要数据展示 -->\n      <div style=\"display: flex; margin-bottom: 24px;\">\n        <!-- 左侧正确率环形图 -->\n        <div style=\"width: 38%; display: flex; flex-direction: column; align-items: center; justify-content: center;\">\n          <a-progress \n            type=\"circle\" \n            :percent=\"accuracyPercent\" \n            :width=\"120\"\n            :format=\"percent => `${percent}%`\"\n            :strokeColor=\"accuracyStrokeColor\"\n            :status=\"accuracyPercent > 60 ? 'success' : accuracyPercent > 30 ? 'normal' : 'exception'\"\n          />\n          <div style=\"margin-top: 12px; font-size: 16px; color: #262626; font-weight: 500;\">\n            正确率\n          </div>\n        </div>\n        \n        <!-- 右侧数据卡片 -->\n        <div style=\"width: 62%; display: flex; flex-wrap: wrap; gap: 12px;\">\n          <div style=\"flex: 1; min-width: 45%; background: #f6ffed; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n            <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n              <span style=\"color: #52c41a; font-weight: 600; font-size: 28px;\">{{ correctCount }}</span>\n              <a-icon type=\"check-circle\" style=\"font-size: 24px; color: #52c41a;\" />\n            </div>\n            <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">正确题目</div>\n          </div>\n          \n          <div style=\"flex: 1; min-width: 45%; background: #fff2f0; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n            <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n              <span style=\"color: #ff4d4f; font-weight: 600; font-size: 28px;\">{{ incorrectCount }}</span>\n              <a-icon type=\"close-circle\" style=\"font-size: 24px; color: #ff4d4f;\" />\n            </div>\n            <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">错误题目</div>\n          </div>\n          \n          <div style=\"flex: 1; min-width: 45%; background: #f5f5f5; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n            <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n              <span style=\"color: #8c8c8c; font-weight: 600; font-size: 28px;\">{{ unfinishedCount }}</span>\n              <a-icon type=\"minus-circle\" style=\"font-size: 24px; color: #8c8c8c;\" />\n            </div>\n            <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">未完成题目</div>\n          </div>\n          \n          <div style=\"flex: 1; min-width: 45%; background: #e6f7ff; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n            <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n              <span style=\"color: #1890ff; font-weight: 600; font-size: 28px;\">{{ totalCount }}</span>\n              <a-icon type=\"file-text\" style=\"font-size: 24px; color: #1890ff;\" />\n            </div>\n            <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">总题目数</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 底部按钮区 -->\n      <div style=\"display: flex; justify-content: center; gap: 12px; flex-wrap: wrap;\">\n        <a-button type=\"primary\" @click=\"startNewPractise\" size=\"large\" style=\"min-width: 140px;\">\n          <a-icon type=\"redo\" /> 再来一组\n        </a-button>\n        \n        <a-button v-if=\"hasAnsweredQuestions\" type=\"primary\" @click=\"enterReviewMode\" size=\"large\" style=\"min-width: 140px; background-color: #722ed1; border-color: #722ed1;\">\n          <a-icon type=\"solution\" /> 查阅答题\n        </a-button>\n        \n        <a-button @click=\"handleExitConfirm\" size=\"large\" style=\"min-width: 140px;\">\n          <a-icon type=\"rollback\" /> {{ isWrongPracticeMode ? '退出错题练习' : '退出刷题' }}\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script>\nexport default {\n  name: 'PracticeResultModal',\n  props: {\n    // 显示状态\n    value: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 统计数据\n    correctCount: {\n      type: Number,\n      default: 0\n    },\n    incorrectCount: {\n      type: Number,\n      default: 0\n    },\n    unfinishedCount: {\n      type: Number,\n      default: 0\n    },\n    totalCount: {\n      type: Number,\n      default: 0\n    },\n    \n    // 是否有已答题目\n    hasAnsweredQuestions: {\n      type: Boolean,\n      default: false\n    },\n\n    // 是否为错题练习模式\n    isWrongPracticeMode: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      // 正确率显示的颜色配置\n      accuracyStrokeColor: { \n        '0%': '#ff4d4f',\n        '50%': '#faad14',\n        '100%': '#52c41a'\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.value\n      },\n      set(val) {\n        this.$emit('input', val)\n      }\n    },\n    \n    // 计算正确率百分比\n    accuracyPercent() {\n      return this.totalCount > 0 \n        ? Math.round((this.correctCount / this.totalCount) * 100) \n        : 0\n    }\n  },\n  methods: {\n    // 开始新一轮练习\n    startNewPractise() {\n      this.$emit('start-new-practise')\n    },\n    \n    // 进入查阅模式\n    enterReviewMode() {\n      this.$emit('enter-review-mode')\n    },\n\n    // 处理退出确认\n    handleExitConfirm() {\n      const title = this.isWrongPracticeMode ? '确定要退出错题练习吗？' : '确定要退出刷题吗？';\n      const content = this.isWrongPracticeMode ? '退出后将返回到错题记录页面' : '退出后将返回练习选择页面';\n\n      this.$confirm({\n        title: title,\n        content: content,\n        okText: '确定退出',\n        cancelText: '继续练习',\n        onOk: () => {\n          this.$emit('close')\n        },\n        onCancel: () => {\n          // 用户选择继续练习，不做任何操作，保持结果窗口打开\n        }\n      })\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.result-modal {\n  .ant-modal-content {\n    border-radius: 16px;\n    overflow: hidden;\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n}\n</style>\n\n<style lang=\"less\">\n// 全局样式，用于覆盖 Ant Design 的默认样式\n.result-modal {\n  .ant-modal-content {\n    border-radius: 16px;\n    overflow: hidden;\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n\n  // 按钮样式\n  .ant-btn {\n    font-weight: 500;\n    height: 40px;\n    border-radius: 20px;\n    padding: 0 20px;\n    transition: all 0.3s;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    &.ant-btn-primary {\n      background: linear-gradient(135deg, #1890ff, #096dd9);\n      border: none;\n      box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);\n\n      &:hover {\n        background: linear-gradient(135deg, #40a9ff, #1890ff);\n        box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);\n      }\n    }\n\n    &:not(.ant-btn-primary) {\n      background: #fff;\n      border: 1px solid #d9d9d9;\n\n      &:hover {\n        border-color: #40a9ff;\n        color: #40a9ff;\n      }\n    }\n\n    @media screen and (max-width: 768px) {\n      flex: 1;\n      min-width: 120px;\n    }\n  }\n}\n</style>\n"]}]}