{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\StackBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\StackBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  const DataSet = require('@antv/data-set');\n\n  export default {\n    name: 'StackBar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true,\n        default: () => [\n          { 'State': '请假', '流转中': 25, '已归档': 18 },\n          { 'State': '出差', '流转中': 30, '已归档': 20 },\n          { 'State': '加班', '流转中': 38, '已归档': 42},\n          { 'State': '用车', '流转中': 51, '已归档': 67}\n        ]\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        label: { offset: 12 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource);\n        dv.transform({\n          type: 'fold',\n          fields: ['流转中', '已归档'],\n          key: '流程状态',\n          value: '流程数量',\n          retains: ['State'],\n        });\n       return dv.rows;\n      }\n    }\n  }\n", {"version": 3, "sources": ["StackBar.vue"], "names": [], "mappings": ";AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "StackBar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div>\n    <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\">\n      <v-coord type=\"rect\" direction=\"LB\" />\n      <v-tooltip />\n      <v-legend />\n      <v-axis dataKey=\"State\" :label=\"label\" />\n      <v-stack-bar position=\"State*流程数量\"  color=\"流程状态\" />\n    </v-chart>\n  </div>\n\n</template>\n\n<script>\n  const DataSet = require('@antv/data-set');\n\n  export default {\n    name: 'StackBar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true,\n        default: () => [\n          { 'State': '请假', '流转中': 25, '已归档': 18 },\n          { 'State': '出差', '流转中': 30, '已归档': 20 },\n          { 'State': '加班', '流转中': 38, '已归档': 42},\n          { 'State': '用车', '流转中': 51, '已归档': 67}\n        ]\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        label: { offset: 12 }\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource);\n        dv.transform({\n          type: 'fold',\n          fields: ['流转中', '已归档'],\n          key: '流程状态',\n          value: '流程数量',\n          retains: ['State'],\n        });\n       return dv.rows;\n      }\n    }\n  }\n</script>"]}]}