{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\JeecgDemoList.vue?vue&type=style&index=0&id=6b3102f8&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\JeecgDemoList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n@import '~@assets/less/common.less';\n", {"version": 3, "sources": ["JeecgDemoList.vue"], "names": [], "mappings": ";AAmXA", "file": "JeecgDemoList.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n        <a-row :gutter=\"24\">\n\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"用户名\">\n              <j-input placeholder=\"请输入名称模糊查询\" v-model=\"queryParam.name\"></j-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"年龄\">\n             <!-- <a-input placeholder=\"请输入名称查询\" v-model=\"queryParam.age\"></a-input>-->\n              <a-input placeholder=\"最小年龄\" type=\"ge\" v-model=\"queryParam.age_begin\" style=\"width:calc(50% - 15px);\"></a-input>\n              <span class=\"group-query-strig\">~</span>\n              <a-input placeholder=\"最大年龄\" type=\"le\" v-model=\"queryParam.age_end\" style=\"width:calc(50% - 15px);\"></a-input>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"toggleSearchStatus\">\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"生日\">\n                <a-range-picker v-model=\"queryParam.birthdayRange\"\n                                format=\"YYYY-MM-DD\"\n                                :placeholder=\"['开始时间', '结束时间']\"\n                                @change=\"onBirthdayChange\" />\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"性别\">\n                <j-dict-select-tag v-model=\"queryParam.sex\" placeholder=\"请选择性别\" dictCode=\"sex\"/>\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"选择用户\">\n                <j-dict-select-tag v-model=\"queryParam.id\" placeholder=\"请选择用户\" dictCode=\"demo,name,id\"/>\n              </a-form-item>\n            </a-col>\n          </template>\n          <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n                {{ toggleSearchStatus ? '收起' : '展开' }}\n                <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n              </a>\n            </a-col>\n          </span>\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n      <a-button type=\"primary\" icon=\"plus\" @click=\"jump\">创建单据</a-button>\n      <a-button type=\"primary\" icon=\"plus\" @click=\"onetomany\">一对多</a-button>\n      <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('单表示例')\">导出</a-button>\n      <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\" @change=\"handleImportExcel\">\n        <a-button type=\"primary\" icon=\"import\">导入</a-button>\n      </a-upload>\n      <!-- 高级查询区域 -->\n      <j-super-query :fieldList=\"fieldList\" ref=\"superQueryModal\" @handleSuperQuery=\"handleSuperQuery\"></j-super-query>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{\n          selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n        <span style=\"float:right;\">\n          <a @click=\"loadData()\"><a-icon type=\"sync\" />刷新</a>\n          <a-divider type=\"vertical\" />\n          <a-popover title=\"自定义列\" trigger=\"click\" placement=\"leftBottom\">\n            <template slot=\"content\">\n              <a-checkbox-group @change=\"onColSettingsChange\" v-model=\"settingColumns\" :defaultValue=\"settingColumns\">\n                <a-row>\n                  <template v-for=\"(item,index) in defColumns\">\n                    <template v-if=\"item.key!='rowIndex'&& item.dataIndex!='action'\">\n                        <a-col :span=\"12\"><a-checkbox :value=\"item.dataIndex\">{{ item.title }}</a-checkbox></a-col>\n                    </template>\n                  </template>\n                </a-row>\n              </a-checkbox-group>\n            </template>\n            <a><a-icon type=\"setting\" />设置</a>\n          </a-popover>\n        </span>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <div slot=\"filterDropdown\">\n          <a-card>\n            <a-checkbox-group @change=\"onColSettingsChange\" v-model=\"settingColumns\" :defaultValue=\"settingColumns\">\n              <a-row>\n                <template v-for=\"(item,index) in defColumns\">\n                  <template v-if=\"item.key!='rowIndex'&& item.dataIndex!='action'\">\n                    <a-col :span=\"12\"><a-checkbox :value=\"item.dataIndex\">{{ item.title }}</a-checkbox></a-col>\n                  </template>\n                </template>\n              </a-row>\n            </a-checkbox-group>\n          </a-card>\n        </div>\n        <a-icon slot=\"filterIcon\" type='setting' :style=\"{ fontSize:'16px',color:  '#108ee9' }\" />\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n\n          <a-divider type=\"vertical\"/>\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\"/></a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <!-- 表单区域 -->\n    <jeecgDemo-modal ref=\"modalForm\" @ok=\"modalFormOk\"></jeecgDemo-modal>\n\n    <!-- 一对多表单区域 -->\n    <JeecgDemoTabsModal ref=\"jeecgDemoTabsModal\" @ok=\"modalFormOk\"></JeecgDemoTabsModal>\n\n  </a-card>\n</template>\n\n<script>\n  import JeecgDemoModal from './modules/JeecgDemoModal'\n  import JSuperQuery from '@/components/jeecg/JSuperQuery.vue';\n  import JInput from '@/components/jeecg/JInput.vue';\n  import JeecgDemoTabsModal from './modules/JeecgDemoTabsModal'\n  import {initDictOptions, filterDictText,filterDictTextByCache} from '@/components/dict/JDictSelectUtil'\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import Vue from 'vue'\n  import { filterObj } from '@/utils/util';\n\n  //高级查询modal需要参数\n  const superQueryFieldList=[\n    {\n      type: \"string\",\n      value: \"name\",\n      text: \"用户名\"\n    }, {\n      type: \"int\",\n      value: \"age\",\n      text: \"年龄\"\n    }, {\n      type: \"date\",\n      value: \"birthday\",\n      text: \"生日\"\n    }\n  ]\n  export default {\n    name: \"JeecgDemoList\",\n    mixins:[JeecgListMixin],\n    components: {\n      JeecgDemoModal,\n      JSuperQuery,\n      JeecgDemoTabsModal,\n      JInput\n    },\n    data() {\n      return {\n        description: '单表示例列表',\n        //字典数组缓存\n        sexDictOptions: [],\n        importExcelUrl:`${window._CONFIG['domianURL']}/test/jeecgDemo/importExcel`,\n        //表头\n        columns:[],\n        //列设置\n        settingColumns:[],\n        //列定义\n        defColumns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key: 'rowIndex',\n            width: 60,\n            align: \"center\",\n            customRender: function (t, r, index) {\n              return parseInt(index) + 1;\n            }\n          },\n          {\n            title: '姓名',\n            align: \"center\",\n            dataIndex: 'name'\n          },\n          {\n            title: '关键词',\n            align: \"center\",\n            dataIndex: 'keyWord'\n          },\n          {\n            title: '打卡时间',\n            align: \"center\",\n            dataIndex: 'punchTime'\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex',\n            customRender: (text) => {\n              //字典值替换通用方法\n              return filterDictTextByCache('sex', text);\n            }\n          },\n          {\n            title: '年龄',\n            align: \"center\",\n            dataIndex: 'age'\n          },\n          {\n            title: '生日',\n            align: \"center\",\n            dataIndex: 'birthday'\n          },\n          {\n            title: '邮箱',\n            align: \"center\",\n            dataIndex: 'email'\n          },\n          {\n            title: '个人简介',\n            align: \"center\",\n            dataIndex: 'content'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: \"center\",\n            scopedSlots: {\n              filterDropdown: 'filterDropdown',\n              filterIcon: 'filterIcon',\n              customRender: 'action'},\n          }\n        ],\n        url: {\n          list: \"/test/jeecgDemo/list\",\n          delete: \"/test/jeecgDemo/delete\",\n          deleteBatch: \"/test/jeecgDemo/deleteBatch\",\n          exportXlsUrl: \"/test/jeecgDemo/exportXls\"\n        },\n        fieldList:superQueryFieldList\n      }\n    },\n    methods: {\n      getQueryParams(){\n        //高级查询器\n        let sqp = {}\n        if(this.superQueryParams){\n          sqp['superQueryParams']=encodeURI(this.superQueryParams)\n          sqp['superQueryMatchType'] = this.superQueryMatchType\n        }\n        var param = Object.assign(sqp, this.queryParam, this.isorter ,this.filters);\n\n        param.field = this.getQueryField();\n        param.pageNo = this.ipagination.current;\n        param.pageSize = this.ipagination.pageSize;\n        delete param.birthdayRange; //范围参数不传递后台\n        return filterObj(param);\n      },\n      initDictConfig() {\n        console.log(\"--我才是真的方法!--\")\n        //初始化字典 - 性别\n        initDictOptions('sex').then((res) => {\n          if (res.success) {\n            this.sexDictOptions = res.result;\n          }\n        });\n      },\n      onetomany: function () {\n        this.$refs.jeecgDemoTabsModal.add();\n        this.$refs.jeecgDemoTabsModal.title = \"编辑\";\n      },\n      //跳转单据页面\n      jump() {\n        this.$router.push({path: '/jeecg/helloworld'})\n      },\n      onBirthdayChange: function (value, dateString) {\n        console.log(dateString[0],dateString[1]);\n        this.queryParam.birthday_begin=dateString[0];\n        this.queryParam.birthday_end=dateString[1];\n      },\n      //列设置更改事件\n      onColSettingsChange (checkedValues) {\n        var key = this.$route.name+\":colsettings\";\n        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)\n        this.settingColumns = checkedValues;\n        const cols = this.defColumns.filter(item => {\n          if(item.key =='rowIndex'|| item.dataIndex=='action'){\n            return true\n          }\n          if (this.settingColumns.includes(item.dataIndex)) {\n            return true\n          }\n          return false\n        })\n        this.columns =  cols;\n      },\n      initColumns(){\n        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）\n        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');\n\n        var key = this.$route.name+\":colsettings\";\n        let colSettings= Vue.ls.get(key);\n        if(colSettings==null||colSettings==undefined){\n          let allSettingColumns = [];\n          this.defColumns.forEach(function (item,i,array ) {\n            allSettingColumns.push(item.dataIndex);\n          })\n          this.settingColumns = allSettingColumns;\n          this.columns = this.defColumns;\n        }else{\n          this.settingColumns = colSettings;\n          const cols = this.defColumns.filter(item => {\n            if(item.key =='rowIndex'|| item.dataIndex=='action'){\n              return true;\n            }\n            if (colSettings.includes(item.dataIndex)) {\n              return true;\n            }\n            return false;\n          })\n          this.columns =  cols;\n        }\n      }\n    },\n    created() {\n      this.initColumns();\n    },\n  }\n</script>\n<style scoped>\n  @import '~@assets/less/common.less';\n</style>"]}]}