{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\SelectUserListModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\SelectUserListModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import {getUserList} from '@/api/api'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n\n  export default {\n    name: \"SelectUserListModal\",\n    mixins: [JeecgListMixin],\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        confirmLoading: false,\n        url: {\n          add: \"/act/model/create\",\n          list: \"/sys/user/list\"\n        },\n        columns: [\n          {\n            title: '用户账号',\n            align: \"center\",\n            dataIndex: 'username',\n            fixed: 'left',\n            width: 200\n          },\n          {\n            title: '用户姓名',\n            align: \"center\",\n            dataIndex: 'realname',\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex_dictText'\n          },\n          {\n            title: '手机号码',\n            align: \"center\",\n            dataIndex: 'phone'\n          },\n          {\n            title: '邮箱',\n            align: \"center\",\n            dataIndex: 'email'\n          },\n          {\n            title: '状态',\n            align: \"center\",\n            dataIndex: 'status_dictText'\n          }\n        ]\n      }\n    },\n    created() {\n      //Step.2 加载用户数据\n      getUserList().then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records;\n          this.ipagination.total = res.result.total;\n        }\n      })\n    },\n    methods: {\n      open() {\n        this.visible = true;\n\n        //Step.1 清空选中用户\n        this.selectedRowKeys = []\n        this.selectedRows = []\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleChange(info) {\n        let file = info.file;\n        if (file.response.success) {\n          this.$message.success(file.response.message);\n          this.$emit('ok');\n          this.close()\n        } else {\n          this.$message.warn(file.response.message);\n          this.close()\n        }\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleSubmit() {\n        this.$emit('ok', this.selectionRows);\n        this.close()\n      },\n    }\n  }\n", {"version": 3, "sources": ["SelectUserListModal.vue"], "names": [], "mappings": ";AAuBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SelectUserListModal.vue", "sourceRoot": "src/components/jeecgbiz/modal", "sourcesContent": ["<template>\n  <a-modal\n    title=\"用户列表\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleSubmit\"\n    @cancel=\"handleCancel\">\n\n    <a-table\n      ref=\"table\"\n      bordered\n      size=\"middle\"\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"></a-table>\n  </a-modal>\n</template>\n\n<script>\n  import {getUserList} from '@/api/api'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n\n  export default {\n    name: \"SelectUserListModal\",\n    mixins: [JeecgListMixin],\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        confirmLoading: false,\n        url: {\n          add: \"/act/model/create\",\n          list: \"/sys/user/list\"\n        },\n        columns: [\n          {\n            title: '用户账号',\n            align: \"center\",\n            dataIndex: 'username',\n            fixed: 'left',\n            width: 200\n          },\n          {\n            title: '用户姓名',\n            align: \"center\",\n            dataIndex: 'realname',\n          },\n          {\n            title: '性别',\n            align: \"center\",\n            dataIndex: 'sex_dictText'\n          },\n          {\n            title: '手机号码',\n            align: \"center\",\n            dataIndex: 'phone'\n          },\n          {\n            title: '邮箱',\n            align: \"center\",\n            dataIndex: 'email'\n          },\n          {\n            title: '状态',\n            align: \"center\",\n            dataIndex: 'status_dictText'\n          }\n        ]\n      }\n    },\n    created() {\n      //Step.2 加载用户数据\n      getUserList().then((res) => {\n        if (res.success) {\n          this.dataSource = res.result.records;\n          this.ipagination.total = res.result.total;\n        }\n      })\n    },\n    methods: {\n      open() {\n        this.visible = true;\n\n        //Step.1 清空选中用户\n        this.selectedRowKeys = []\n        this.selectedRows = []\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleChange(info) {\n        let file = info.file;\n        if (file.response.success) {\n          this.$message.success(file.response.message);\n          this.$emit('ok');\n          this.close()\n        } else {\n          this.$message.warn(file.response.message);\n          this.close()\n        }\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleSubmit() {\n        this.$emit('ok', this.selectionRows);\n        this.close()\n      },\n    }\n  }\n</script>\n\n<style>\n\n</style>\n"]}]}