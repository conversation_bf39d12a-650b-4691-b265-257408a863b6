{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\Trend\\Trend.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\Trend\\Trend.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"Trend\",\n  props: {\n    prefixCls: {\n      type: String,\n      default: 'ant-pro-trend'\n    },\n    /**\n     * 上升下降标识：up|down\n     */\n    flag: {\n      type: String,\n      required: true\n    },\n    /**\n     * 颜色反转\n     */\n    reverseColor: {\n      type: Boolean,\n      default: false\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "prefixCls", "type", "String", "default", "flag", "required", "reverseColor", "Boolean"], "sources": ["src/components/Trend/Trend.vue"], "sourcesContent": ["<template>\n  <div :class=\"[prefixCls, reverseColor && 'reverse-color' ]\">\n    <span>\n      <slot name=\"term\"></slot>\n      <span class=\"item-text\">\n        <slot></slot>\n      </span>\n    </span>\n    <span :class=\"[flag]\"><a-icon :type=\"`caret-${flag}`\"/></span>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Trend\",\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-trend'\n      },\n      /**\n       * 上升下降标识：up|down\n       */\n      flag: {\n        type: String,\n        required: true\n      },\n      /**\n       * 颜色反转\n       */\n      reverseColor: {\n        type: Boolean,\n        default: false\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"index\";\n</style>"], "mappings": "AAaA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;AACA;AACA;IACAC,IAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,QAAA;IACA;IACA;AACA;AACA;IACAC,YAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}