{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Trend.vue?vue&type=style&index=0&id=566c702c&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Trend.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.chart-trend {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 22px;\n\n  .trend-icon {\n    font-size: 12px;\n\n    &.up, &.down {\n      margin-left: 4px;\n      position: relative;\n      top: 1px;\n\n      i {\n        font-size: 12px;\n        transform: scale(.83);\n      }\n    }\n\n    &.up {\n      color: #f5222d;\n    }\n    &.down {\n      color: #52c41a;\n      top: -1px;\n    }\n  }\n}\n", {"version": 3, "sources": ["Trend.vue"], "names": [], "mappings": ";AAoFA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Trend.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div class=\"chart-trend\">\n    {{ term }}\n    <span>{{ rate }}%</span>\n    <span :class=\"['trend-icon', trend]\"><a-icon :type=\"'caret-' + trend\"/></span>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Trend\",\n    props: {\n      // 同title\n      term: {\n        type: String,\n        default: '',\n        required: true\n      },\n      // 百分比\n      percentage: {\n        type: Number,\n        default: null\n      },\n      type: {\n        type: Boolean,\n        default: null\n      },\n      target: {\n        type: Number,\n        default: 0\n      },\n      value: {\n        type: Number,\n        default: 0\n      },\n      fixed: {\n        type: Number,\n        default: 2\n      }\n    },\n    data () {\n      return {\n        trend: this.type && 'up' || 'down',\n        rate: this.percentage\n      }\n    },\n    created () {\n      let type = this.type === null ? this.value >= this.target : this.type\n      this.trend = type ? 'up' : 'down';\n      this.rate = (this.percentage === null ? Math.abs(this.value - this.target) * 100 / this.target : this.percentage).toFixed(this.fixed)\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .chart-trend {\n    display: inline-block;\n    font-size: 14px;\n    line-height: 22px;\n\n    .trend-icon {\n      font-size: 12px;\n\n      &.up, &.down {\n        margin-left: 4px;\n        position: relative;\n        top: 1px;\n\n        i {\n          font-size: 12px;\n          transform: scale(.83);\n        }\n      }\n\n      &.up {\n        color: #f5222d;\n      }\n      &.down {\n        color: #52c41a;\n        top: -1px;\n      }\n    }\n  }\n</style>"]}]}