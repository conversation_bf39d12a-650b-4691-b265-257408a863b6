{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue?vue&type=template&id=79059638&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ViserChartDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <a-tabs defaultActiveKey=\"1\">\n    <!-- 柱状图 -->\n    <a-tab-pane tab=\"柱状图\" key=\"1\">\n      <bar title=\"销售额排行\" :dataSource=\"barData\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 多列柱状图 -->\n    <a-tab-pane tab=\"多列柱状图\" key=\"2\">\n      <bar-multid title=\"多列柱状图\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 迷你柱状图 -->\n    <a-tab-pane tab=\"迷你柱状图\" key=\"3\">\n      <mini-bar :dataSource=\"barData\" :width=\"400\" :height=\"200\"/>\n    </a-tab-pane>\n    <!-- 面积图 -->\n    <a-tab-pane tab=\"面积图\" key=\"4\">\n      <area-chart-ty title=\"销售额排行\" :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 迷你面积图 -->\n    <a-tab-pane tab=\"迷你面积图\" key=\"5\">\n      <div style=\"padding-top: 100px;width:600px;height:200px\">\n        <mini-area :dataSource=\"areaData\" x=\"月份\" y=\"销售额\" :height=\"height\"/>\n      </div>\n    </a-tab-pane>\n    <!-- 多行折线图 -->\n    <a-tab-pane tab=\"多行折线图\" key=\"6\">\n      <line-chart-multid title=\"多行折线图\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 饼图 -->\n    <a-tab-pane tab=\"饼图\" key=\"7\">\n      <pie title=\"饼图\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 雷达图 -->\n    <a-tab-pane tab=\"雷达图\" key=\"8\">\n      <radar title=\"雷达图\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 仪表盘 -->\n    <a-tab-pane tab=\"仪表盘\" key=\"9\">\n      <dash-chart-demo title=\"仪表盘\" :value=\"9\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- 进度条 -->\n    <a-tab-pane tab=\"进度条\" key=\"10\">\n      <mini-progress :percentage=\"30\" :target=\"40\" :height=\"30\"/>\n      <mini-progress :percentage=\"51\" :target=\"60\" :height=\"30\" color=\"#FFA500\"/>\n      <mini-progress :percentage=\"66\" :target=\"80\" :height=\"30\" color=\"#1E90FF\"/>\n      <mini-progress :percentage=\"74\" :target=\"70\" :height=\"30\" color=\"#FF4500\"/>\n      <mini-progress :percentage=\"92\" :target=\"100\" :height=\"30\" color=\"#49CC49\"/>\n    </a-tab-pane>\n    <!-- 排名列表 -->\n    <a-tab-pane tab=\"排名列表\" key=\"11\">\n      <rank-list title=\"门店销售排行榜\" :list=\"rankList\" style=\"width: 600px;margin: 0 auto;\"/>\n    </a-tab-pane>\n    <!-- TransferBar -->\n    <a-tab-pane tab=\"TransferBar\" key=\"12\">\n      <transfer-bar title=\"年度消耗流量一览表\" :data=\"barData\" x=\"月份\" y=\"流量(Mb)\" :height=\"height\"/>\n    </a-tab-pane>\n    <!-- Trend -->\n    <a-tab-pane tab=\"Trend\" key=\"13\">\n      <trend title=\"Trend\" term=\"Trend：\" :percentage=\"30\"/>\n    </a-tab-pane>\n    <!-- Liquid -->\n    <a-tab-pane tab=\"Liquid\" key=\"14\">\n      <liquid :height=\"height\"/>\n    </a-tab-pane>\n    <!-- BarAndLine -->\n    <a-tab-pane tab=\"BarAndLine\" key=\"15\">\n      <bar-and-line :height=\"height\"/>\n    </a-tab-pane>\n  </a-tabs>\n</a-card>\n", null]}