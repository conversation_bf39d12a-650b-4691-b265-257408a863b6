{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue?vue&type=template&id=9078f37c&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"a-card\", {\n    attrs: {\n      bordered: false\n    }\n  }, [_c(\"a-row\", [_c(\"a-col\", {\n    attrs: {\n      sm: 8,\n      xs: 24\n    }\n  }, [_c(\"head-info\", {\n    attrs: {\n      title: \"我的待办\",\n      content: \"8个任务\",\n      bordered: true\n    }\n  })], 1), _c(\"a-col\", {\n    attrs: {\n      sm: 8,\n      xs: 24\n    }\n  }, [_c(\"head-info\", {\n    attrs: {\n      title: \"本周任务平均处理时间\",\n      content: \"32分钟\",\n      bordered: true\n    }\n  })], 1), _c(\"a-col\", {\n    attrs: {\n      sm: 8,\n      xs: 24\n    }\n  }, [_c(\"head-info\", {\n    attrs: {\n      title: \"本周完成任务数\",\n      content: \"24个\"\n    }\n  })], 1)], 1)], 1), _c(\"a-card\", {\n    staticStyle: {\n      \"margin-top\": \"24px\"\n    },\n    attrs: {\n      bordered: false,\n      title: \"标准列表\"\n    }\n  }, [_c(\"div\", {\n    attrs: {\n      slot: \"extra\"\n    },\n    slot: \"extra\"\n  }, [_c(\"a-radio-group\", [_c(\"a-radio-button\", [_vm._v(\"全部\")]), _c(\"a-radio-button\", [_vm._v(\"进行中\")]), _c(\"a-radio-button\", [_vm._v(\"等待中\")])], 1), _c(\"a-input-search\", {\n    staticStyle: {\n      \"margin-left\": \"16px\",\n      width: \"272px\"\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"operate\"\n  }, [_c(\"a-button\", {\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      type: \"dashed\",\n      icon: \"plus\"\n    }\n  }, [_vm._v(\"添加\")])], 1), _c(\"a-list\", {\n    attrs: {\n      size: \"large\",\n      pagination: {\n        showSizeChanger: true,\n        showQuickJumper: true,\n        pageSize: 5,\n        total: 50\n      }\n    }\n  }, _vm._l(_vm.data, function (item, index) {\n    return _c(\"a-list-item\", {\n      key: index\n    }, [_c(\"a-list-item-meta\", {\n      attrs: {\n        description: item.description\n      }\n    }, [_c(\"a-avatar\", {\n      attrs: {\n        slot: \"avatar\",\n        size: \"large\",\n        shape: \"square\",\n        src: item.avatar\n      },\n      slot: \"avatar\"\n    }), _c(\"a\", {\n      attrs: {\n        slot: \"title\"\n      },\n      slot: \"title\"\n    }, [_vm._v(_vm._s(item.title))])], 1), _c(\"div\", {\n      attrs: {\n        slot: \"actions\"\n      },\n      slot: \"actions\"\n    }, [_c(\"a\", [_vm._v(\"编辑\")])]), _c(\"div\", {\n      attrs: {\n        slot: \"actions\"\n      },\n      slot: \"actions\"\n    }, [_c(\"a-dropdown\", [_c(\"a-menu\", {\n      attrs: {\n        slot: \"overlay\"\n      },\n      slot: \"overlay\"\n    }, [_c(\"a-menu-item\", [_c(\"a\", [_vm._v(\"编辑\")])]), _c(\"a-menu-item\", [_c(\"a\", [_vm._v(\"删除\")])])], 1), _c(\"a\", [_vm._v(\"更多\"), _c(\"a-icon\", {\n      attrs: {\n        type: \"down\"\n      }\n    })], 1)], 1)], 1), _c(\"div\", {\n      staticClass: \"list-content\"\n    }, [_c(\"div\", {\n      staticClass: \"list-content-item\"\n    }, [_c(\"span\", [_vm._v(\"Owner\")]), _c(\"p\", [_vm._v(_vm._s(item.owner))])]), _c(\"div\", {\n      staticClass: \"list-content-item\"\n    }, [_c(\"span\", [_vm._v(\"开始时间\")]), _c(\"p\", [_vm._v(_vm._s(item.startAt))])]), _c(\"div\", {\n      staticClass: \"list-content-item\"\n    }, [_c(\"a-progress\", {\n      staticStyle: {\n        width: \"180px\"\n      },\n      attrs: {\n        percent: item.progress.value,\n        status: !item.progress.status ? null : item.progress.status\n      }\n    })], 1)])], 1);\n  }), 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "bordered", "sm", "xs", "title", "content", "staticStyle", "slot", "_v", "width", "staticClass", "type", "icon", "size", "pagination", "showSizeChanger", "showQuickJumper", "pageSize", "total", "_l", "data", "item", "index", "key", "description", "shape", "src", "avatar", "_s", "owner", "startAt", "percent", "progress", "value", "status", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/list/StandardList.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"a-card\",\n        { attrs: { bordered: false } },\n        [\n          _c(\n            \"a-row\",\n            [\n              _c(\n                \"a-col\",\n                { attrs: { sm: 8, xs: 24 } },\n                [\n                  _c(\"head-info\", {\n                    attrs: {\n                      title: \"我的待办\",\n                      content: \"8个任务\",\n                      bordered: true,\n                    },\n                  }),\n                ],\n                1\n              ),\n              _c(\n                \"a-col\",\n                { attrs: { sm: 8, xs: 24 } },\n                [\n                  _c(\"head-info\", {\n                    attrs: {\n                      title: \"本周任务平均处理时间\",\n                      content: \"32分钟\",\n                      bordered: true,\n                    },\n                  }),\n                ],\n                1\n              ),\n              _c(\n                \"a-col\",\n                { attrs: { sm: 8, xs: 24 } },\n                [\n                  _c(\"head-info\", {\n                    attrs: { title: \"本周完成任务数\", content: \"24个\" },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"a-card\",\n        {\n          staticStyle: { \"margin-top\": \"24px\" },\n          attrs: { bordered: false, title: \"标准列表\" },\n        },\n        [\n          _c(\n            \"div\",\n            { attrs: { slot: \"extra\" }, slot: \"extra\" },\n            [\n              _c(\n                \"a-radio-group\",\n                [\n                  _c(\"a-radio-button\", [_vm._v(\"全部\")]),\n                  _c(\"a-radio-button\", [_vm._v(\"进行中\")]),\n                  _c(\"a-radio-button\", [_vm._v(\"等待中\")]),\n                ],\n                1\n              ),\n              _c(\"a-input-search\", {\n                staticStyle: { \"margin-left\": \"16px\", width: \"272px\" },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"operate\" },\n            [\n              _c(\n                \"a-button\",\n                {\n                  staticStyle: { width: \"100%\" },\n                  attrs: { type: \"dashed\", icon: \"plus\" },\n                },\n                [_vm._v(\"添加\")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"a-list\",\n            {\n              attrs: {\n                size: \"large\",\n                pagination: {\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  pageSize: 5,\n                  total: 50,\n                },\n              },\n            },\n            _vm._l(_vm.data, function (item, index) {\n              return _c(\n                \"a-list-item\",\n                { key: index },\n                [\n                  _c(\n                    \"a-list-item-meta\",\n                    { attrs: { description: item.description } },\n                    [\n                      _c(\"a-avatar\", {\n                        attrs: {\n                          slot: \"avatar\",\n                          size: \"large\",\n                          shape: \"square\",\n                          src: item.avatar,\n                        },\n                        slot: \"avatar\",\n                      }),\n                      _c(\"a\", { attrs: { slot: \"title\" }, slot: \"title\" }, [\n                        _vm._v(_vm._s(item.title)),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { attrs: { slot: \"actions\" }, slot: \"actions\" }, [\n                    _c(\"a\", [_vm._v(\"编辑\")]),\n                  ]),\n                  _c(\n                    \"div\",\n                    { attrs: { slot: \"actions\" }, slot: \"actions\" },\n                    [\n                      _c(\n                        \"a-dropdown\",\n                        [\n                          _c(\n                            \"a-menu\",\n                            { attrs: { slot: \"overlay\" }, slot: \"overlay\" },\n                            [\n                              _c(\"a-menu-item\", [_c(\"a\", [_vm._v(\"编辑\")])]),\n                              _c(\"a-menu-item\", [_c(\"a\", [_vm._v(\"删除\")])]),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"a\",\n                            [\n                              _vm._v(\"更多\"),\n                              _c(\"a-icon\", { attrs: { type: \"down\" } }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"list-content\" }, [\n                    _c(\"div\", { staticClass: \"list-content-item\" }, [\n                      _c(\"span\", [_vm._v(\"Owner\")]),\n                      _c(\"p\", [_vm._v(_vm._s(item.owner))]),\n                    ]),\n                    _c(\"div\", { staticClass: \"list-content-item\" }, [\n                      _c(\"span\", [_vm._v(\"开始时间\")]),\n                      _c(\"p\", [_vm._v(_vm._s(item.startAt))]),\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"list-content-item\" },\n                      [\n                        _c(\"a-progress\", {\n                          staticStyle: { width: \"180px\" },\n                          attrs: {\n                            percent: item.progress.value,\n                            status: !item.progress.status\n                              ? null\n                              : item.progress.status,\n                          },\n                        }),\n                      ],\n                      1\n                    ),\n                  ]),\n                ],\n                1\n              )\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,QAAQ,EACR;IAAEE,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAM;EAAE,CAAC,EAC9B,CACEH,EAAE,CACA,OAAO,EACP,CACEA,EAAE,CACA,OAAO,EACP;IAAEE,KAAK,EAAE;MAAEE,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAG;EAAE,CAAC,EAC5B,CACEL,EAAE,CAAC,WAAW,EAAE;IACdE,KAAK,EAAE;MACLI,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACfJ,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDH,EAAE,CACA,OAAO,EACP;IAAEE,KAAK,EAAE;MAA<PERSON>,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAG;EAAE,CAAC,EAC5B,CACEL,EAAE,CAAC,WAAW,EAAE;IACdE,KAAK,EAAE;MACLI,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAE,MAAM;MACfJ,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDH,EAAE,CACA,OAAO,EACP;IAAEE,KAAK,EAAE;MAAEE,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAG;EAAE,CAAC,EAC5B,CACEL,EAAE,CAAC,WAAW,EAAE;IACdE,KAAK,EAAE;MAAEI,KAAK,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAM;EAC5C,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDP,EAAE,CACA,QAAQ,EACR;IACEQ,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO,CAAC;IACrCN,KAAK,EAAE;MAAEC,QAAQ,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAO;EAC1C,CAAC,EACD,CACEN,EAAE,CACA,KAAK,EACL;IAAEE,KAAK,EAAE;MAAEO,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EAC3C,CACET,EAAE,CACA,eAAe,EACf,CACEA,EAAE,CAAC,gBAAgB,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACpCV,EAAE,CAAC,gBAAgB,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EACrCV,EAAE,CAAC,gBAAgB,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CACtC,EACD,CACF,CAAC,EACDV,EAAE,CAAC,gBAAgB,EAAE;IACnBQ,WAAW,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAQ;EACvD,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IAAEY,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEZ,EAAE,CACA,UAAU,EACV;IACEQ,WAAW,EAAE;MAAEG,KAAK,EAAE;IAAO,CAAC;IAC9BT,KAAK,EAAE;MAAEW,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAO;EACxC,CAAC,EACD,CAACf,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,EACDV,EAAE,CACA,QAAQ,EACR;IACEE,KAAK,EAAE;MACLa,IAAI,EAAE,OAAO;MACbC,UAAU,EAAE;QACVC,eAAe,EAAE,IAAI;QACrBC,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;MACT;IACF;EACF,CAAC,EACDrB,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,IAAI,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACtC,OAAOxB,EAAE,CACP,aAAa,EACb;MAAEyB,GAAG,EAAED;IAAM,CAAC,EACd,CACExB,EAAE,CACA,kBAAkB,EAClB;MAAEE,KAAK,EAAE;QAAEwB,WAAW,EAAEH,IAAI,CAACG;MAAY;IAAE,CAAC,EAC5C,CACE1B,EAAE,CAAC,UAAU,EAAE;MACbE,KAAK,EAAE;QACLO,IAAI,EAAE,QAAQ;QACdM,IAAI,EAAE,OAAO;QACbY,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAEL,IAAI,CAACM;MACZ,CAAC;MACDpB,IAAI,EAAE;IACR,CAAC,CAAC,EACFT,EAAE,CAAC,GAAG,EAAE;MAAEE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAQ,CAAC;MAAEA,IAAI,EAAE;IAAQ,CAAC,EAAE,CACnDV,GAAG,CAACW,EAAE,CAACX,GAAG,CAAC+B,EAAE,CAACP,IAAI,CAACjB,KAAK,CAAC,CAAC,CAC3B,CAAC,CACH,EACD,CACF,CAAC,EACDN,EAAE,CAAC,KAAK,EAAE;MAAEE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAU,CAAC;MAAEA,IAAI,EAAE;IAAU,CAAC,EAAE,CACzDT,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACxB,CAAC,EACFV,EAAE,CACA,KAAK,EACL;MAAEE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAU,CAAC;MAAEA,IAAI,EAAE;IAAU,CAAC,EAC/C,CACET,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CACA,QAAQ,EACR;MAAEE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAU,CAAC;MAAEA,IAAI,EAAE;IAAU,CAAC,EAC/C,CACET,EAAE,CAAC,aAAa,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5CV,EAAE,CAAC,aAAa,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,EACD,CACF,CAAC,EACDV,EAAE,CACA,GAAG,EACH,CACED,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,EACZV,EAAE,CAAC,QAAQ,EAAE;MAAEE,KAAK,EAAE;QAAEW,IAAI,EAAE;MAAO;IAAE,CAAC,CAAC,CAC1C,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDb,EAAE,CAAC,KAAK,EAAE;MAAEY,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCZ,EAAE,CAAC,KAAK,EAAE;MAAEY,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC9CZ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7BV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAACX,GAAG,CAAC+B,EAAE,CAACP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC,EACF/B,EAAE,CAAC,KAAK,EAAE;MAAEY,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC9CZ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAACX,GAAG,CAAC+B,EAAE,CAACP,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC,CACxC,CAAC,EACFhC,EAAE,CACA,KAAK,EACL;MAAEY,WAAW,EAAE;IAAoB,CAAC,EACpC,CACEZ,EAAE,CAAC,YAAY,EAAE;MACfQ,WAAW,EAAE;QAAEG,KAAK,EAAE;MAAQ,CAAC;MAC/BT,KAAK,EAAE;QACL+B,OAAO,EAAEV,IAAI,CAACW,QAAQ,CAACC,KAAK;QAC5BC,MAAM,EAAE,CAACb,IAAI,CAACW,QAAQ,CAACE,MAAM,GACzB,IAAI,GACJb,IAAI,CAACW,QAAQ,CAACE;MACpB;IACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBvC,MAAM,CAACwC,aAAa,GAAG,IAAI;AAE3B,SAASxC,MAAM,EAAEuC,eAAe", "ignoreList": []}]}