{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframePageView.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframePageView.vue", "mtime": 1750916319326}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n  import PageLayout from '../page/PageLayout'\n  import RouteView from './RouteView'\n\n  export default {\n    name: \"IframePageContent\",\n    inject:['closeCurrent'],\n    data () {\n      return {\n        url: \"\",\n        id:\"\"\n      }\n    },\n    created () {\n      this.goUrl()\n    },\n    updated () {\n      this.goUrl()\n    },\n    watch: {\n      $route(to, from) {\n        this.goUrl();\n      }\n    },\n    methods: {\n      goUrl () {\n        let url = this.$route.meta.url\n        let id = this.$route.path\n        this.id = id\n        //url = \"http://www.baidu.com\"\n        console.log(\"------url------\"+url)\n        if (url !== null && url !== undefined) {\n          this.url = url;\n          /*update_begin author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n          if(this.$route.meta.internalOrExternal != undefined && this.$route.meta.internalOrExternal==true){\n            this.closeCurrent();\n            //外部url加入token\n            let tokenStr = \"${token}\";\n            if(url.indexOf(tokenStr)!=-1){\n              let token = Vue.ls.get(ACCESS_TOKEN);\n               this.url = url.replace(tokenStr,token);\n            }\n            window.open(this.url);\n          }\n          /*update_end author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n\n        }\n      }\n    }\n  }\n", {"version": 3, "sources": ["IframePageView.vue"], "names": [], "mappings": ";AAOA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "IframePageView.vue", "sourceRoot": "src/components/layouts", "sourcesContent": ["<template>\n\n    <iframe  :id=\"id\" :src=\"url\" frameborder=\"0\" width=\"100%\" height=\"800px\" scrolling=\"auto\"></iframe>\n\n</template>\n\n<script>\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n  import PageLayout from '../page/PageLayout'\n  import RouteView from './RouteView'\n\n  export default {\n    name: \"IframePageContent\",\n    inject:['closeCurrent'],\n    data () {\n      return {\n        url: \"\",\n        id:\"\"\n      }\n    },\n    created () {\n      this.goUrl()\n    },\n    updated () {\n      this.goUrl()\n    },\n    watch: {\n      $route(to, from) {\n        this.goUrl();\n      }\n    },\n    methods: {\n      goUrl () {\n        let url = this.$route.meta.url\n        let id = this.$route.path\n        this.id = id\n        //url = \"http://www.baidu.com\"\n        console.log(\"------url------\"+url)\n        if (url !== null && url !== undefined) {\n          this.url = url;\n          /*update_begin author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n          if(this.$route.meta.internalOrExternal != undefined && this.$route.meta.internalOrExternal==true){\n            this.closeCurrent();\n            //外部url加入token\n            let tokenStr = \"${token}\";\n            if(url.indexOf(tokenStr)!=-1){\n              let token = Vue.ls.get(ACCESS_TOKEN);\n               this.url = url.replace(tokenStr,token);\n            }\n            window.open(this.url);\n          }\n          /*update_end author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n\n        }\n      }\n    }\n  }\n</script>\n\n<style>\n</style>"]}]}