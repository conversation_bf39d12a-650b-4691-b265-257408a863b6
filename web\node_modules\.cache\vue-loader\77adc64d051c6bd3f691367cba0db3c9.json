{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\BaseSetting.vue?vue&type=template&id=f6709922&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\BaseSetting.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"account-settings-info-view\">\n  <a-row :gutter=\"16\">\n    <a-col :md=\"24\" :lg=\"16\">\n      <a-form layout=\"vertical\" :form=\"form\">\n        <a-form-item label=\"真实姓名\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input placeholder=\"请输入真实姓名\" v-decorator=\"[ 'realname', validatorRules.realname]\" />\n        </a-form-item>\n        <a-form-item label=\"头像\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <j-upload\n            v-decorator=\"['avatar']\"\n            :fileType=\"'image'\"\n            :number=\"1\"\n            :trigger-change=\"true\"\n          ></j-upload>\n        </a-form-item>\n\n        <a-form-item label=\"金币数\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input\n            :value=\"userCoins\"\n            disabled\n            placeholder=\"金币数量\"\n            style=\"color: #1890ff; font-weight: bold;\"\n          />\n        </a-form-item>\n\n        <a-form-item label=\"生日\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-date-picker\n            style=\"width: 100%\"\n            placeholder=\"请选择生日\"\n            v-decorator=\"['birthday', {initialValue:!userInfo.birthday?null:moment(userInfo.birthday,dateFormat)}]\"\n          />\n        </a-form-item>\n\n        <a-form-item label=\"性别\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select v-decorator=\"[ 'sex', {}]\" placeholder=\"请选择性别\">\n            <a-select-option :value=\"1\">男</a-select-option>\n            <a-select-option :value=\"2\">女</a-select-option>\n          </a-select>\n        </a-form-item>\n\n        <a-form-item label=\"邮箱\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input placeholder=\"请输入邮箱\" v-decorator=\"[ 'email', validatorRules.email]\" />\n        </a-form-item>\n\n        <a-form-item label=\"手机号码\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input placeholder=\"请输入手机号码\" v-decorator=\"[ 'phone', validatorRules.phone]\" />\n        </a-form-item>\n        <a-form-item>\n          <a-button type=\"primary\" @click=\"handleSubmit\">提交</a-button>\n        </a-form-item>\n      </a-form>\n    </a-col>\n  </a-row>\n\n  <avatar-modal ref=\"modal\"></avatar-modal>\n</div>\n", null]}