{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'JTreeTable',\n    props: {\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 根据什么查询，如果传递 id 就根据 id 查询\n      queryKey: {\n        type: String,\n        default: 'parentId'\n      },\n      queryParams: {\n        type: Object,\n        default: () => ({})\n      },\n      // 查询顶级时的值，如果顶级为0，则传0\n      topValue: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true\n      },\n      url: {\n        type: String,\n        required: true\n      },\n      childrenUrl: {\n        type: String,\n        default: null\n      },\n      tableProps: {\n        type: Object,\n        default: () => ({})\n      },\n      /** 是否在创建组件的时候就查询数据 */\n      immediateRequest: {\n        type: Boolean,\n        default: true\n      },\n      condition:{\n        type:String,\n        default:'',\n        required:false\n      }\n    },\n    data() {\n      return {\n        dataSource: [],\n        expandedRowKeys: []\n      }\n    },\n    computed: {\n      getChildrenUrl() {\n        if (this.childrenUrl) {\n          return this.childrenUrl\n        } else {\n          return this.url\n        }\n      },\n      slots() {\n        let slots = []\n        for (let column of this.columns) {\n          if (column.scopedSlots && column.scopedSlots.customRender) {\n            slots.push(column.scopedSlots.customRender)\n          }\n        }\n        return slots\n      },\n      tableAttrs() {\n        return Object.assign(this.$attrs, this.tableProps)\n      }\n    },\n    watch: {\n      queryParams: {\n        deep: true,\n        handler() {\n          this.loadData()\n        }\n      }\n    },\n    created() {\n      if (this.immediateRequest) this.loadData()\n    },\n    methods: {\n\n      /** 加载数据*/\n      loadData(id = this.topValue, first = true, url = this.url) {\n        this.$emit('requestBefore', { first })\n\n        if (first) {\n          this.expandedRowKeys = []\n        }\n\n        let params = Object.assign({}, this.queryParams || {})\n        params[this.queryKey] = id\n        if(this.condition && this.condition.length>0){\n          params['condition'] = this.condition\n        }\n\n        return getAction(url, params).then(res => {\n          let list = []\n          if (res.result instanceof Array) {\n            list = res.result\n          } else if (res.result.records instanceof Array) {\n            list = res.result.records\n          } else {\n            throw '返回数据类型不识别'\n          }\n          let dataSource = list.map(item => {\n            // 判断是否标记了带有子级\n            if (item.hasChildren === true) {\n              // 查找第一个带有dataIndex的值的列\n              let firstColumn\n              for (let column of this.columns) {\n                firstColumn = column.dataIndex\n                if (firstColumn) break\n              }\n              // 定义默认展开时显示的loading子级，实际子级数据只在展开时加载\n              let loadChild = { id: `${item.id}_loadChild`, [firstColumn]: 'loading...', isLoading: true }\n              item.children = [loadChild]\n            }\n            return item\n          })\n          if (first) {\n            this.dataSource = dataSource\n          }\n          this.$emit('requestSuccess', { first, dataSource, res })\n          return Promise.resolve(dataSource)\n        }).finally(() => this.$emit('requestFinally', { first }))\n      },\n\n      /** 点击展开图标时触发 */\n      handleExpand(expanded, record) {\n        // 判断是否是展开状态\n        if (expanded) {\n          // 判断子级的首个项的标记是否是“正在加载中”，如果是就加载数据\n          if (record.children[0].isLoading === true) {\n            this.loadData(record.id, false, this.getChildrenUrl).then(dataSource => {\n              // 处理好的数据可直接赋值给children\n              if (dataSource.length === 0) {\n                record.children = null\n              } else {\n                record.children = dataSource\n              }\n            })\n          }\n        }\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["JTreeTable.vue"], "names": [], "mappings": ";AAmBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "JTreeTable.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-table\n    :rowKey=\"rowKey\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :expandedRowKeys=\"expandedRowKeys\"\n    v-bind=\"tableAttrs\"\n    v-on=\"$listeners\"\n    @expand=\"handleExpand\"\n    @expandedRowsChange=\"expandedRowKeys=$event\">\n\n    <template v-for=\"(slotItem) of slots\" :slot=\"slotItem\" slot-scope=\"text, record, index\">\n      <slot :name=\"slotItem\" v-bind=\"{text,record,index}\"></slot>\n    </template>\n\n  </a-table>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'JTreeTable',\n    props: {\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 根据什么查询，如果传递 id 就根据 id 查询\n      queryKey: {\n        type: String,\n        default: 'parentId'\n      },\n      queryParams: {\n        type: Object,\n        default: () => ({})\n      },\n      // 查询顶级时的值，如果顶级为0，则传0\n      topValue: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true\n      },\n      url: {\n        type: String,\n        required: true\n      },\n      childrenUrl: {\n        type: String,\n        default: null\n      },\n      tableProps: {\n        type: Object,\n        default: () => ({})\n      },\n      /** 是否在创建组件的时候就查询数据 */\n      immediateRequest: {\n        type: Boolean,\n        default: true\n      },\n      condition:{\n        type:String,\n        default:'',\n        required:false\n      }\n    },\n    data() {\n      return {\n        dataSource: [],\n        expandedRowKeys: []\n      }\n    },\n    computed: {\n      getChildrenUrl() {\n        if (this.childrenUrl) {\n          return this.childrenUrl\n        } else {\n          return this.url\n        }\n      },\n      slots() {\n        let slots = []\n        for (let column of this.columns) {\n          if (column.scopedSlots && column.scopedSlots.customRender) {\n            slots.push(column.scopedSlots.customRender)\n          }\n        }\n        return slots\n      },\n      tableAttrs() {\n        return Object.assign(this.$attrs, this.tableProps)\n      }\n    },\n    watch: {\n      queryParams: {\n        deep: true,\n        handler() {\n          this.loadData()\n        }\n      }\n    },\n    created() {\n      if (this.immediateRequest) this.loadData()\n    },\n    methods: {\n\n      /** 加载数据*/\n      loadData(id = this.topValue, first = true, url = this.url) {\n        this.$emit('requestBefore', { first })\n\n        if (first) {\n          this.expandedRowKeys = []\n        }\n\n        let params = Object.assign({}, this.queryParams || {})\n        params[this.queryKey] = id\n        if(this.condition && this.condition.length>0){\n          params['condition'] = this.condition\n        }\n\n        return getAction(url, params).then(res => {\n          let list = []\n          if (res.result instanceof Array) {\n            list = res.result\n          } else if (res.result.records instanceof Array) {\n            list = res.result.records\n          } else {\n            throw '返回数据类型不识别'\n          }\n          let dataSource = list.map(item => {\n            // 判断是否标记了带有子级\n            if (item.hasChildren === true) {\n              // 查找第一个带有dataIndex的值的列\n              let firstColumn\n              for (let column of this.columns) {\n                firstColumn = column.dataIndex\n                if (firstColumn) break\n              }\n              // 定义默认展开时显示的loading子级，实际子级数据只在展开时加载\n              let loadChild = { id: `${item.id}_loadChild`, [firstColumn]: 'loading...', isLoading: true }\n              item.children = [loadChild]\n            }\n            return item\n          })\n          if (first) {\n            this.dataSource = dataSource\n          }\n          this.$emit('requestSuccess', { first, dataSource, res })\n          return Promise.resolve(dataSource)\n        }).finally(() => this.$emit('requestFinally', { first }))\n      },\n\n      /** 点击展开图标时触发 */\n      handleExpand(expanded, record) {\n        // 判断是否是展开状态\n        if (expanded) {\n          // 判断子级的首个项的标记是否是“正在加载中”，如果是就加载数据\n          if (record.children[0].isLoading === true) {\n            this.loadData(record.id, false, this.getChildrenUrl).then(dataSource => {\n              // 处理好的数据可直接赋值给children\n              if (dataSource.length === 0) {\n                record.children = null\n              } else {\n                record.children = dataSource\n              }\n            })\n          }\n        }\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}