{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\WrongQuestionPractice.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\WrongQuestionPractice.vue", "mtime": 1753195991411}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./WrongQuestionPractice.vue?vue&type=template&id=ae609f68&scoped=true\"\nimport script from \"./WrongQuestionPractice.vue?vue&type=script&lang=js\"\nexport * from \"./WrongQuestionPractice.vue?vue&type=script&lang=js\"\nimport style0 from \"./WrongQuestionPractice.vue?vue&type=style&index=0&id=ae609f68&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ae609f68\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('ae609f68')) {\n      api.createRecord('ae609f68', component.options)\n    } else {\n      api.reload('ae609f68', component.options)\n    }\n    module.hot.accept(\"./WrongQuestionPractice.vue?vue&type=template&id=ae609f68&scoped=true\", function () {\n      api.rerender('ae609f68', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/examSystem/components/WrongQuestionPractice.vue\"\nexport default component.exports"]}