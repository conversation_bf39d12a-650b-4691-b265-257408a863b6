{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from 'moment'\n  import JDate from '@/components/jeecg/JDate'\n\n  export default {\n    components: {\n      JDate\n    },\n    name: 'JeecgOrderTicketModal',\n    data() {\n      return {\n        title: '操作',\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5}\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16}\n        },\n        moment,\n        format: 'YYYY-MM-DD HH:mm:ss',\n        disableSubmit: false,\n        orderId: '',\n        hiding: false,\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: '/test/order/addTicket',\n          edit: '/test/order/editTicket'\n        }\n      }\n    },\n    created() {\n    },\n    methods: {\n      add(orderId) {\n        if (orderId) {\n          this.edit({orderId}, '')\n        } else {\n          this.$message.warning('请选择一条航班数据')\n        }\n      },\n      detail(record) {\n        this.edit(record, 'd')\n      },\n      edit(record, v) {\n        if (v == 'e') {\n          this.hiding = false;\n          this.disableSubmit = false;\n        } else if (v == 'd') {\n          this.hiding = false;\n          this.disableSubmit = true;\n        } else {\n          this.hiding = true;\n          this.disableSubmit = false;\n        }\n        this.form.resetFields();\n        this.orderId = record.orderId;\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'ticketCode', 'tickectDate', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'))\n        })\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            formData.mainId = this.orderId;\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok')\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      }\n    }\n  }\n", {"version": 3, "sources": ["JeecgOrderTicketModal.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JeecgOrderTicketModal.vue", "sourceRoot": "src/views/jeecg/tablist/form", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :okButtonProps=\"{ props: {disabled: disableSubmit} }\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"航班号\"\n          hasFeedback>\n          <a-input\n            placeholder=\"请输入航班号\"\n            :readOnly=\"disableSubmit\"\n            v-decorator=\"['ticketCode', {rules:[{ required: true,message: '请输入航班号!'}]}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"航班时间\"\n          hasFeedback>\n          <j-date :trigger-change=\"true\"  v-decorator=\"['tickectDate',{rules:[{ required: true,message: '请输入航班号!'}]}]\"></j-date>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单号码\"\n          v-model=\"this.orderId\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'orderId', {}]\" disabled=\"disabled\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"创建人\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'createBy', {}]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"创建时间\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'createTime', {}]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from 'moment'\n  import JDate from '@/components/jeecg/JDate'\n\n  export default {\n    components: {\n      JDate\n    },\n    name: 'JeecgOrderTicketModal',\n    data() {\n      return {\n        title: '操作',\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5}\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16}\n        },\n        moment,\n        format: 'YYYY-MM-DD HH:mm:ss',\n        disableSubmit: false,\n        orderId: '',\n        hiding: false,\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: '/test/order/addTicket',\n          edit: '/test/order/editTicket'\n        }\n      }\n    },\n    created() {\n    },\n    methods: {\n      add(orderId) {\n        if (orderId) {\n          this.edit({orderId}, '')\n        } else {\n          this.$message.warning('请选择一条航班数据')\n        }\n      },\n      detail(record) {\n        this.edit(record, 'd')\n      },\n      edit(record, v) {\n        if (v == 'e') {\n          this.hiding = false;\n          this.disableSubmit = false;\n        } else if (v == 'd') {\n          this.hiding = false;\n          this.disableSubmit = true;\n        } else {\n          this.hiding = true;\n          this.disableSubmit = false;\n        }\n        this.form.resetFields();\n        this.orderId = record.orderId;\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'ticketCode', 'tickectDate', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'))\n        })\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            formData.mainId = this.orderId;\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok')\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}