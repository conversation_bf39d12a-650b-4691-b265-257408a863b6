{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue?vue&type=style&index=0&id=663767e8&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.toolbar {\n  position: fixed;\n  width: 100%;\n  bottom: 0;\n  right: 0;\n  height: 56px;\n  line-height: 56px;\n  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);\n  background: #fff;\n  border-top: 1px solid #e8e8e8;\n  padding: 0 24px;\n  z-index: 9;\n}\n", {"version": 3, "sources": ["FooterToolBar.vue"], "names": [], "mappings": ";AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FooterToolBar.vue", "sourceRoot": "src/components/tools", "sourcesContent": ["<template>\n  <div class=\"toolbar\">\n    <div style=\"float: left\">\n      <slot name=\"extra\"></slot>\n    </div>\n    <div style=\"float: right\">\n      <slot></slot>\n    </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"FooterToolBar\"\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .toolbar {\n    position: fixed;\n    width: 100%;\n    bottom: 0;\n    right: 0;\n    height: 56px;\n    line-height: 56px;\n    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);\n    background: #fff;\n    border-top: 1px solid #e8e8e8;\n    padding: 0 24px;\n    z-index: 9;\n  }\n</style>"]}]}