{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue?vue&type=style&index=0&id=5b82e1da&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue", "mtime": 1751031689617}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.carousel {\n  height: 300px;\n  border-radius: 20px;\n  img {\n    width: 100%;\n    height: 300px;\n  }\n  .swiper-slide {\n    background-position: center;\n    background-size: cover;\n  }\n}\n", {"version": 3, "sources": ["Banner.vue"], "names": [], "mappings": ";AAyGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Banner.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\n  <div class=\"banner\">\n    <!-- 添加公告文字轮播组件 -->\n    <notice-marquee ref=\"noticeMarquee\" @showNotice=\"showNoticeDetail\"></notice-marquee>\n    <div v-if=\"banner.length > 0\">\n      <swiper ref=\"mySwiper\" :options=\"swiperOptions\" class=\"swiper-wrappe carousel\">\n        <swiper-slide class=\"swiper-slide\" v-for=\"(b, i) in banner\" :key=\"i\">\n          <a v-if=\"b.href\" :href=\"b.href\" target=\"_blank\">\n            <img :src=\"b.img\" alt=\"\" />\n          </a>\n          <img v-else :src=\"b.img\" alt=\"\" />\n        </swiper-slide>\n\n        <!-- 分页器 -->\n        <div\n          class=\"swiper-pagination swiper-pagination-white\"\n          slot=\"pagination\"\n        ></div>\n        <!-- 左右箭头 -->\n        <div\n          class=\"swiper-button-prev swiper-button-white\"\n          slot=\"button-prev\"\n        ></div>\n        <div\n          class=\"swiper-button-next swiper-button-white\"\n          slot=\"button-next\"\n        ></div>\n      </swiper>\n    </div>\n    <!-- 添加公告详情对话框组件 -->\n    <show-announcement ref=\"showAnnouncement\"></show-announcement>\n  </div>\n</template>\n\n<script>\nimport { getFileAccessHttpUrl } from '@/api/manage'\nimport NoticeMarquee from './NoticeMarquee'\nimport ShowAnnouncement from '@/components/tools/ShowAnnouncement'\n\nexport default {\n  name: 'Banner',\n  components: {\n    NoticeMarquee,\n    ShowAnnouncement\n  },\n  data() {\n    return {\n      swiperOptions: {\n        speed: 800,\n        effect: 'coverflow', //\"fade\"（淡入）、\"cube\"（方块）、\"coverflow\"（3d流）、\"flip\"（3d翻转）\n        //显示分页\n        pagination: {\n          el: '.swiper-pagination',\n          clickable: true,\n        },\n        //设置点击箭头\n        navigation: {\n          nextEl: '.swiper-button-next',\n          prevEl: '.swiper-button-prev',\n        },\n        //自动轮播\n        autoplay: {\n          delay: 5000,\n          //当用户滑动图片后继续自动轮播\n          disableOnInteraction: false,\n        },\n        //开启循环模式\n        loop: true,\n      },\n      banner: [],\n      sysConfig: {},\n      loading: true,\n    }\n  },\n  created() {\n    //获取banner数据\n    this.sysConfig = this.$store.getters.sysConfig\n\n    if (this.sysConfig.banner) {\n      var banners = this.sysConfig.banner.split(',')\n      if (this.sysConfig.bannerLinks) {\n        var bannerLinks = this.sysConfig.bannerLinks.split('\\n')\n      } else {\n        var bannerLinks = []\n      }\n      for (var i in banners) {\n        var b = {}\n        b.img = getFileAccessHttpUrl(banners[i])\n        if (bannerLinks.length > i) {\n          b.href = bannerLinks[i]\n        }\n        this.banner.push(b)\n      }\n    }\n  },\n  methods: {\n    // 显示公告详情\n    showNoticeDetail(notice) {\n      this.$refs.showAnnouncement.detail(notice)\n    }\n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n.carousel {\n  height: 300px;\n  border-radius: 20px;\n  img {\n    width: 100%;\n    height: 300px;\n  }\n  .swiper-slide {\n    background-position: center;\n    background-size: cover;\n  }\n}\n</style>"]}]}