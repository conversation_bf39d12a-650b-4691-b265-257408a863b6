{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\Ellipsis.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\Ellipsis.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { cutStrByFullLength, getStrFullLength } from '@/components/_util/StringUtil';\nexport default {\n  name: 'Ellipsis',\n  props: {\n    prefixCls: {\n      type: String,\n      default: 'ant-pro-ellipsis'\n    },\n    tooltip: {\n      type: Boolean,\n      default: true\n    },\n    length: {\n      type: Number,\n      default: 25\n    },\n    lines: {\n      type: Number,\n      default: 1\n    },\n    fullWidthRecognition: {\n      type: Boolean,\n      default: false\n    }\n  },\n  methods: {},\n  render: function render() {\n    var h = arguments[0];\n    var _this$$props = this.$props,\n      tooltip = _this$$props.tooltip,\n      length = _this$$props.length;\n    var text = '';\n    // 处理没有default插槽时的特殊情况\n    if (this.$slots.default) {\n      text = this.$slots.default.map(function (vNode) {\n        return vNode.text;\n      }).join('');\n    }\n    // 判断是否显示 tooltip\n    if (tooltip && getStrFullLength(text) > length) {\n      return h(\"a-tooltip\", [h(\"template\", {\n        \"slot\": \"title\"\n      }, [text]), h(\"span\", [cutStrByFullLength(text, this.length) + '…'])]);\n    } else {\n      return h(\"span\", [text]);\n    }\n  }\n};", {"version": 3, "names": ["cut<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getStr<PERSON>ull<PERSON><PERSON>th", "name", "props", "prefixCls", "type", "String", "default", "tooltip", "Boolean", "length", "Number", "lines", "fullWidthRecognition", "methods", "render", "h", "arguments", "_this$$props", "$props", "text", "$slots", "map", "vNode", "join"], "sources": ["src/components/Ellipsis/Ellipsis.vue"], "sourcesContent": ["<script>\n  import { cutStrByFullLength, getStrFullLength } from '@/components/_util/StringUtil'\n\n  export default {\n    name: 'Ellipsis',\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-ellipsis'\n      },\n      tooltip: {\n        type: Boolean,\n        default: true,\n      },\n      length: {\n        type: Number,\n        default: 25,\n      },\n      lines: {\n        type: Number,\n        default: 1\n      },\n      fullWidthRecognition: {\n        type: Boolean,\n        default: false\n      }\n    },\n    methods: {},\n    render() {\n      const { tooltip, length } = this.$props\n      let text = ''\n      // 处理没有default插槽时的特殊情况\n      if (this.$slots.default) {\n        text = this.$slots.default.map(vNode => vNode.text).join('')\n      }\n      // 判断是否显示 tooltip\n      if (tooltip && getStrFullLength(text) > length) {\n        return (\n          <a-tooltip>\n            <template slot=\"title\">{text}</template>\n            <span>{cutStrByFullLength(text, this.length) + '…'}</span>\n          </a-tooltip>\n        )\n      } else {\n        return (<span>{text}</span>)\n      }\n    }\n  }\n</script>"], "mappings": "AACA,SAAAA,kBAAA,EAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAK,KAAA;MACAP,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAM,oBAAA;MACAR,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAO,OAAA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,CAAA,GAAAC,SAAA;IACA,IAAAC,YAAA,QAAAC,MAAA;MAAAX,OAAA,GAAAU,YAAA,CAAAV,OAAA;MAAAE,MAAA,GAAAQ,YAAA,CAAAR,MAAA;IACA,IAAAU,IAAA;IACA;IACA,SAAAC,MAAA,CAAAd,OAAA;MACAa,IAAA,QAAAC,MAAA,CAAAd,OAAA,CAAAe,GAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAH,IAAA;MAAA,GAAAI,IAAA;IACA;IACA;IACA,IAAAhB,OAAA,IAAAP,gBAAA,CAAAmB,IAAA,IAAAV,MAAA;MACA,OAAAM,CAAA,eAAAA,CAAA;QAAA,QAEA;MAAA,IAAAI,IAAA,IAAAJ,CAAA,UACAhB,kBAAA,CAAAoB,IAAA,OAAAV,MAAA;IAGA;MACA,OAAAM,CAAA,UAAAI,IAAA;IACA;EACA;AACA", "ignoreList": []}]}