{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\CodingQuestion.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\CodingQuestion.vue", "mtime": 1753195937252}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport CodeMirror from '@/components/exam/CodeMirror'\nimport { marked } from 'marked'\n\nexport default {\n  name: 'CodingQuestion',\n  components: {\n    CodeMirror\n  },\n  props: {\n    // 题目数据\n    currentQuestion: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 题目索引和列表\n    currentQuestionIndex: {\n      type: Number,\n      default: 1\n    },\n    questionList: {\n      type: Array,\n      default: () => []\n    },\n\n    // 显示状态\n    showAnswer: {\n      type: Boolean,\n      default: false\n    },\n    isFullScreen: {\n      type: Boolean,\n      default: false\n    },\n    isReviewMode: {\n      type: Boolean,\n      default: false\n    },\n    currentQuestionStatus: {\n      type: String,\n      default: ''\n    },\n    \n    // 代码相关\n    code: {\n      type: String,\n      default: ''\n    },\n    selectedLanguage: {\n      type: String,\n      default: 'C++'\n    },\n    supportedLanguages: {\n      type: Array,\n      default: () => ['C', 'C++', 'Java', 'Python3', 'Python2']\n    },\n    \n    // 编辑器配置\n    editorHeight: {\n      type: Number,\n      default: 600\n    },\n    editorTheme: {\n      type: String,\n      default: 'solarized'\n    },\n    editorFontSize: {\n      type: String,\n      default: '14px'\n    },\n    editorTabSize: {\n      type: Number,\n      default: 4\n    },\n    \n    // 测试相关\n    openTestCaseDrawer: {\n      type: Boolean,\n      default: false\n    },\n    testInputMap: {\n      type: Object,\n      default: () => ({})\n    },\n    testResultMap: {\n      type: Object,\n      default: () => ({})\n    },\n    activeTestCaseIndexMap: {\n      type: Object,\n      default: () => ({})\n    },\n    \n    // 提交状态\n    isSubmitting: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    // 获取当前题目的测试用例\n    getProblemTestCases() {\n      if (!this.currentQuestion || !this.currentQuestion.content || !this.currentQuestion.content.sample_cases) {\n        return []\n      }\n      \n      return this.currentQuestion.content.sample_cases.map((sample, index) => {\n        return {\n          input: sample.input,\n          output: sample.output,\n          active: false\n        }\n      })\n    }\n  },\n  methods: {\n    // Markdown转HTML\n    markdownToHtml(text) {\n      if (!text) return ''\n      try {\n        return marked(text, {\n          gfm: true,\n          breaks: true\n        })\n      } catch (error) {\n        console.error('Markdown转换错误：', error)\n        return text\n      }\n    },\n    \n    // 更新代码\n    updateCode(newCode) {\n      this.$emit('update:code', newCode)\n    },\n    \n    // 处理语言切换\n    handleLanguageChange(newLang) {\n      this.$emit('language-change', newLang)\n    },\n\n    // 处理主题变更\n    handleThemeChange(newTheme) {\n      this.$emit('update:editorTheme', newTheme)\n    },\n\n    // 处理字体大小变更\n    handleFontSizeChange(newFontSize) {\n      this.$emit('update:editorFontSize', newFontSize)\n    },\n    \n    // 处理测试抽屉状态更新\n    handleDrawerUpdate(newVal) {\n      this.$emit('update:openTestCaseDrawer', newVal)\n    },\n    \n    // 重置代码\n    resetCode() {\n      // 直接发出重置事件，让CodeMirror组件处理具体逻辑和消息提示\n      this.$emit('reset-code');\n    },\n    \n    // 获取用户最近通过的代码\n    getUserLastAcceptedCode() {\n      this.$emit('get-last-accepted-code')\n    },\n    \n    // 切换焦点模式\n    switchFocusMode(isOpen) {\n      this.$emit('switch-focus-mode', isOpen)\n    },\n    \n    // 处理正式提交\n    handleFormalSubmission(data) {\n      this.$emit('formal-submission', data)\n    },\n    \n    // 更新测试输入\n    updateTestInput(input) {\n      this.$emit('update-test-input', input)\n    },\n    \n    // 更新测试结果\n    updateTestResult(result) {\n      this.$emit('update-test-result', result)\n    },\n    \n    // 更新当前激活的测试用例索引\n    updateActiveTestCaseIndex(index) {\n      this.$emit('update-active-test-case-index', index)\n    },\n\n    // 获取题目状态样式类\n    getStatusClass(status) {\n      switch (status) {\n        case 'correct': return 'status-success'\n        case 'incorrect': return 'status-error'\n        case 'unfinished': return 'status-warning'\n        default: return ''\n      }\n    },\n\n    // 获取题目状态图标\n    getStatusIcon(status) {\n      switch (status) {\n        case 'correct': return 'check-circle'\n        case 'incorrect': return 'close-circle'\n        case 'unfinished': return 'clock-circle'\n        default: return 'question-circle'\n      }\n    },\n\n    // 获取题目状态文本\n    getStatusText(status) {\n      switch (status) {\n        case 'correct': return '正确'\n        case 'incorrect': return '错误'\n        case 'unfinished': return '未完成'\n        default: return ''\n      }\n    },\n\n    // 处理上一题点击\n    handlePrevQuestion() {\n      this.$emit('prev-question')\n    },\n\n    // 处理下一题点击\n    handleNextQuestion() {\n      this.$emit('next-question')\n    }\n  }\n}\n", {"version": 3, "sources": ["CodingQuestion.vue"], "names": [], "mappings": ";AA+IA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "CodingQuestion.vue", "sourceRoot": "src/views/examSystem/components", "sourcesContent": ["<template>\n  <div class=\"coding-question-wrapper\">\n    <!-- 题目头部信息 -->\n    <div class=\"question-header\">\n      <div class=\"question-type-info\">\n        <a-tag color=\"purple\">\n          <a-icon type=\"code\" />\n          编程题\n        </a-tag>\n      </div>\n      <div class=\"question-nav-container\">\n        <div class=\"navigation-buttons\">\n          <a-button\n            :disabled=\"currentQuestionIndex === 1\"\n            @click=\"handlePrevQuestion\"\n            size=\"small\"\n          >\n            <a-icon type=\"left\" /> 上一题\n          </a-button>\n          <a-button\n            :type=\"(isReviewMode && currentQuestionIndex === questionList.length) ? '' : 'primary'\"\n            :disabled=\"isReviewMode && currentQuestionIndex === questionList.length\"\n            @click=\"handleNextQuestion\"\n            size=\"small\"\n          >\n            {{ isReviewMode ? '下一题' : (currentQuestionIndex === questionList.length ? '完成练习' : '下一题') }} <a-icon type=\"right\" />\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 题目标题 -->\n    <div class=\"question-title\">\n      <div class=\"title-header\">\n        <span class=\"question-number\">{{ currentQuestionIndex }}</span>\n        <h3>\n          {{ currentQuestion.title }}\n          <!-- 题目状态标签 -->\n          <span v-if=\"isReviewMode\" :class=\"['status-tag', getStatusClass(currentQuestionStatus)]\">\n            <a-icon :type=\"getStatusIcon(currentQuestionStatus)\" />\n            {{ getStatusText(currentQuestionStatus) }}\n          </span>\n        </h3>\n        <div class=\"difficulty title-difficulty\">\n          难度: <a-rate :value=\"currentQuestion.difficulty\" :count=\"3\" disabled size=\"small\" />\n        </div>\n      </div>\n    </div>\n\n    <div class=\"coding-question-container\">\n    <a-row :gutter=\"20\">\n      <!-- 题目描述区域 -->\n      <a-col :md=\"10\" :sm=\"24\">\n        <div class=\"problem-description\" v-html=\"markdownToHtml(currentQuestion.content.description)\"></div>\n        <a-divider />\n        <div class=\"problem-section\">\n          <p><strong>输入格式：</strong></p>\n          <div v-html=\"markdownToHtml(currentQuestion.content.input_format)\"></div>\n        </div>\n        <a-divider />\n        <div class=\"problem-section\">\n          <p><strong>输出格式：</strong></p>\n          <div v-html=\"markdownToHtml(currentQuestion.content.output_format)\"></div>\n        </div>\n        <a-divider />\n        <div v-for=\"(sample, index) in currentQuestion.content.sample_cases\" :key=\"index\" class=\"problem-section\">\n          <p><strong>样例 {{ index + 1 }}：</strong></p>\n          <div class=\"sample-container\">\n            <div class=\"sample-input\">\n              <div class=\"sample-header\">输入：</div>\n              <pre>{{ sample.input }}</pre>\n            </div>\n            <div class=\"sample-output\">\n              <div class=\"sample-header\">输出：</div>\n              <pre>{{ sample.output }}</pre>\n            </div>\n          </div>\n        </div>\n        <a-divider v-if=\"currentQuestion.content.hint\" />\n        <div v-if=\"currentQuestion.content.hint\" class=\"problem-section\">\n          <p><strong>提示：</strong></p>\n          <div v-html=\"markdownToHtml(currentQuestion.content.hint)\"></div>\n        </div>\n        \n        <!-- 显示答案和解析 -->\n        <div v-if=\"showAnswer\" class=\"problem-section solution-section\">\n          <a-divider />\n          <div class=\"solution-header\">\n            <a-icon type=\"solution\" /> 参考解答\n          </div>\n          <div class=\"solution-content\" v-if=\"currentQuestion.content.analysis\">\n            <div v-html=\"markdownToHtml(currentQuestion.content.analysis)\"></div>\n          </div>\n          <div class=\"solution-content\" v-else>\n            <p>暂无解析</p>\n          </div>\n        </div>\n      </a-col>\n      \n      <!-- 代码编辑器区域 -->\n      <a-col :md=\"14\" :sm=\"24\">\n        <div class=\"code-editor-container\">\n          <code-mirror\n            ref=\"codeMirror\"\n            :value=\"code\"\n            @update:value=\"updateCode\"\n            :languages=\"supportedLanguages\"\n            :language=\"selectedLanguage\"\n            @changeLang=\"handleLanguageChange\"\n            :openTestCaseDrawer=\"openTestCaseDrawer\"\n            @update:openTestCaseDrawer=\"handleDrawerUpdate\"\n            :pid=\"currentQuestion.id\"\n            :question=\"currentQuestion\"\n            :type=\"'practice'\"\n            :problemTestCase=\"getProblemTestCases\"\n            :isAuthenticated=\"true\"\n            :height=\"editorHeight\"\n            :theme=\"editorTheme\"\n            @changeTheme=\"handleThemeChange\"\n            :fontSize=\"editorFontSize\"\n            @update:fontSize=\"handleFontSizeChange\"\n            :tabSize=\"editorTabSize\"\n            @resetCode=\"resetCode\"\n            @getUserLastAcceptedCode=\"getUserLastAcceptedCode\"\n            @switchFocusMode=\"switchFocusMode\"\n            :openFocusMode=\"isFullScreen\"\n            @submitToEvaluation=\"handleFormalSubmission\"\n            :userInput=\"testInputMap[currentQuestion.id] || ''\"\n            :testJudgeRes=\"testResultMap[currentQuestion.id] || {status: -10, problemJudgeMode: 'default'}\"\n            :activeTestCaseIndex=\"activeTestCaseIndexMap[currentQuestion.id] || -1\"\n            @updateTestInput=\"updateTestInput\"\n            @updateTestResult=\"updateTestResult\"\n            @updateActiveTestCaseIndex=\"updateActiveTestCaseIndex\"\n            :submitDisabled=\"isSubmitting\"\n          />\n        </div>\n      </a-col>\n    </a-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CodeMirror from '@/components/exam/CodeMirror'\nimport { marked } from 'marked'\n\nexport default {\n  name: 'CodingQuestion',\n  components: {\n    CodeMirror\n  },\n  props: {\n    // 题目数据\n    currentQuestion: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 题目索引和列表\n    currentQuestionIndex: {\n      type: Number,\n      default: 1\n    },\n    questionList: {\n      type: Array,\n      default: () => []\n    },\n\n    // 显示状态\n    showAnswer: {\n      type: Boolean,\n      default: false\n    },\n    isFullScreen: {\n      type: Boolean,\n      default: false\n    },\n    isReviewMode: {\n      type: Boolean,\n      default: false\n    },\n    currentQuestionStatus: {\n      type: String,\n      default: ''\n    },\n    \n    // 代码相关\n    code: {\n      type: String,\n      default: ''\n    },\n    selectedLanguage: {\n      type: String,\n      default: 'C++'\n    },\n    supportedLanguages: {\n      type: Array,\n      default: () => ['C', 'C++', 'Java', 'Python3', 'Python2']\n    },\n    \n    // 编辑器配置\n    editorHeight: {\n      type: Number,\n      default: 600\n    },\n    editorTheme: {\n      type: String,\n      default: 'solarized'\n    },\n    editorFontSize: {\n      type: String,\n      default: '14px'\n    },\n    editorTabSize: {\n      type: Number,\n      default: 4\n    },\n    \n    // 测试相关\n    openTestCaseDrawer: {\n      type: Boolean,\n      default: false\n    },\n    testInputMap: {\n      type: Object,\n      default: () => ({})\n    },\n    testResultMap: {\n      type: Object,\n      default: () => ({})\n    },\n    activeTestCaseIndexMap: {\n      type: Object,\n      default: () => ({})\n    },\n    \n    // 提交状态\n    isSubmitting: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    // 获取当前题目的测试用例\n    getProblemTestCases() {\n      if (!this.currentQuestion || !this.currentQuestion.content || !this.currentQuestion.content.sample_cases) {\n        return []\n      }\n      \n      return this.currentQuestion.content.sample_cases.map((sample, index) => {\n        return {\n          input: sample.input,\n          output: sample.output,\n          active: false\n        }\n      })\n    }\n  },\n  methods: {\n    // Markdown转HTML\n    markdownToHtml(text) {\n      if (!text) return ''\n      try {\n        return marked(text, {\n          gfm: true,\n          breaks: true\n        })\n      } catch (error) {\n        console.error('Markdown转换错误：', error)\n        return text\n      }\n    },\n    \n    // 更新代码\n    updateCode(newCode) {\n      this.$emit('update:code', newCode)\n    },\n    \n    // 处理语言切换\n    handleLanguageChange(newLang) {\n      this.$emit('language-change', newLang)\n    },\n\n    // 处理主题变更\n    handleThemeChange(newTheme) {\n      this.$emit('update:editorTheme', newTheme)\n    },\n\n    // 处理字体大小变更\n    handleFontSizeChange(newFontSize) {\n      this.$emit('update:editorFontSize', newFontSize)\n    },\n    \n    // 处理测试抽屉状态更新\n    handleDrawerUpdate(newVal) {\n      this.$emit('update:openTestCaseDrawer', newVal)\n    },\n    \n    // 重置代码\n    resetCode() {\n      // 直接发出重置事件，让CodeMirror组件处理具体逻辑和消息提示\n      this.$emit('reset-code');\n    },\n    \n    // 获取用户最近通过的代码\n    getUserLastAcceptedCode() {\n      this.$emit('get-last-accepted-code')\n    },\n    \n    // 切换焦点模式\n    switchFocusMode(isOpen) {\n      this.$emit('switch-focus-mode', isOpen)\n    },\n    \n    // 处理正式提交\n    handleFormalSubmission(data) {\n      this.$emit('formal-submission', data)\n    },\n    \n    // 更新测试输入\n    updateTestInput(input) {\n      this.$emit('update-test-input', input)\n    },\n    \n    // 更新测试结果\n    updateTestResult(result) {\n      this.$emit('update-test-result', result)\n    },\n    \n    // 更新当前激活的测试用例索引\n    updateActiveTestCaseIndex(index) {\n      this.$emit('update-active-test-case-index', index)\n    },\n\n    // 获取题目状态样式类\n    getStatusClass(status) {\n      switch (status) {\n        case 'correct': return 'status-success'\n        case 'incorrect': return 'status-error'\n        case 'unfinished': return 'status-warning'\n        default: return ''\n      }\n    },\n\n    // 获取题目状态图标\n    getStatusIcon(status) {\n      switch (status) {\n        case 'correct': return 'check-circle'\n        case 'incorrect': return 'close-circle'\n        case 'unfinished': return 'clock-circle'\n        default: return 'question-circle'\n      }\n    },\n\n    // 获取题目状态文本\n    getStatusText(status) {\n      switch (status) {\n        case 'correct': return '正确'\n        case 'incorrect': return '错误'\n        case 'unfinished': return '未完成'\n        default: return ''\n      }\n    },\n\n    // 处理上一题点击\n    handlePrevQuestion() {\n      this.$emit('prev-question')\n    },\n\n    // 处理下一题点击\n    handleNextQuestion() {\n      this.$emit('next-question')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.coding-question-wrapper {\n  .question-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 12px;\n\n    .question-type-info {\n      .ant-tag {\n        font-size: 14px;\n        font-weight: 600;\n        padding: 4px 12px;\n        border-radius: 16px;\n        border: none;\n        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\n\n        .anticon {\n          margin-right: 4px;\n        }\n      }\n    }\n\n    .question-nav-container {\n      display: flex;\n      align-items: center;\n\n      .navigation-buttons {\n        display: flex;\n        gap: 10px;\n        align-items: center;\n\n        button {\n          font-size: 13px;\n          height: 28px;\n          padding: 0 10px;\n          border-radius: 4px;\n          transition: all 0.2s;\n\n          &.ant-btn-primary {\n            background: #1890ff;\n            border-color: #1890ff;\n            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);\n\n            &:hover {\n              background: #40a9ff;\n              border-color: #40a9ff;\n            }\n          }\n\n          &:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          }\n        }\n      }\n    }\n  }\n\n  .question-title {\n    margin-bottom: 12px;\n\n    .title-header {\n      display: flex;\n      align-items: center;\n      flex-wrap: wrap;\n\n      .question-number {\n        width: 32px !important;\n        height: 32px !important;\n        font-size: 16px !important;\n        background: linear-gradient(135deg, #1890ff, #096dd9);\n        color: white;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-weight: 600;\n        margin-right: 12px;\n        flex-shrink: 0;\n        box-shadow: 0 3px 8px rgba(24, 144, 255, 0.3);\n      }\n\n      h3 {\n        font-size: 18px;\n        font-weight: 600;\n        color: #262626;\n        margin: 6px 0 0 0;\n        line-height: 1.5;\n        flex: 1;\n      }\n\n      .title-difficulty {\n        background: rgba(250, 250, 250, 0.8);\n        padding: 4px 12px;\n        border-radius: 16px;\n        font-weight: 500;\n        display: flex;\n        align-items: center;\n        margin-left: 15px;\n\n        .ant-rate {\n          margin-left: 6px;\n        }\n\n        @media screen and (max-width: 768px) {\n          margin-top: 8px;\n          margin-left: 48px;\n        }\n      }\n    }\n  }\n\n  .status-tag {\n    display: inline-block;\n    vertical-align: baseline;\n    margin-left: 10px;\n    padding: 0 6px;\n    font-size: inherit;\n    font-weight: 600;\n    line-height: inherit;\n\n    .anticon {\n      margin-right: 4px;\n      font-size: 0.9em;\n    }\n\n    &.status-success {\n      color: #52c41a;\n    }\n\n    &.status-error {\n      color: #ff4d4f;\n    }\n\n    &.status-warning {\n      color: #1890ff;\n    }\n\n    @media screen and (max-width: 768px) {\n      margin-left: 5px;\n      padding: 0 4px;\n    }\n  }\n}\n\n.coding-question-container {\n  margin-top: 12px;\n\n  // 为左侧题目描述区域添加固定高度和滚动条\n  .ant-col-md-10 {\n    max-height: 700px;\n    overflow-y: auto;\n    padding-right: 10px;\n    border-right: 1px solid #f0f0f0;\n  }\n\n  .problem-section {\n    margin-bottom: 12px;\n\n    strong {\n      font-size: 16px;\n      color: #262626;\n    }\n  }\n\n  .sample-container {\n    display: flex;\n    gap: 16px;\n    margin-top: 8px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .sample-input, .sample-output {\n      flex: 1;\n      background: #f8f9fa;\n      border-radius: 8px;\n      overflow: hidden;\n\n      .sample-header {\n        padding: 8px 12px;\n        background: #e6f7ff;\n        color: #1890ff;\n        font-weight: 600;\n      }\n\n      pre {\n        margin: 0;\n        padding: 12px;\n        background: transparent;\n        border-radius: 0;\n      }\n    }\n  }\n\n  .solution-section {\n    background: #f6ffed;\n    padding: 16px;\n    border-radius: 8px;\n    border-left: 4px solid #52c41a;\n\n    .solution-header {\n      font-weight: 600;\n      color: #52c41a;\n      margin-bottom: 12px;\n      font-size: 16px;\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n\n  .code-editor-container {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    overflow: hidden;\n    max-height: 700px;\n    overflow: auto;\n  }\n}\n</style>\n"]}]}