{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoTabsModal.vue?vue&type=template&id=1304afe1&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoTabsModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"1200\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n\n      <a-card class=\"card\"  :bordered=\"false\">\n        <a-row class=\"form-row\" :gutter=\"16\">\n          <a-col :lg=\"8\">\n            <a-form-item label=\"任务名\">\n              <a-input placeholder=\"请输入任务名称\"  v-decorator=\"[ 'task.name', {rules: [{ required: true, message: '请输入任务名称', whitespace: true}]} ]\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item label=\"任务描述\">\n              <a-input placeholder=\"请输入任务描述\"  v-decorator=\"['task.description', {rules: [{ required: true, message: '请输入任务描述', whitespace: true}]} ]\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item label=\"执行人\">\n              <a-select placeholder=\"请选择执行人\" v-decorator=\"['task.executor',{rules: [{ required: true, message: '请选择执行人'}]}  ]\">\n                <a-select-option value=\"黄丽丽\">黄丽丽</a-select-option>\n                <a-select-option value=\"李大刀\">李大刀</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"16\">\n          <a-col :lg=\"8\">\n            <a-form-item label=\"责任人\">\n              <a-select placeholder=\"请选择责任人\" v-decorator=\"['task.manager',  {rules: [{ required: true, message: '请选择责任人'}]} ]\">\n                <a-select-option value=\"王伟\">王伟</a-select-option>\n                <a-select-option value=\"李红军\">李红军</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item label=\"提醒时间\">\n              <a-time-picker style=\"width: 100%\" v-decorator=\"['task.time', {rules: [{ required: true, message: '请选择提醒时间'}]} ]\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :lg=\"8\">\n            <a-form-item\n              label=\"任务类型\">\n              <a-select placeholder=\"请选择任务类型\" v-decorator=\"['task.type', {rules: [{ required: true, message: '请选择任务类型'}]} ]\">\n                <a-select-option value=\"定时执行\">定时执行</a-select-option>\n                <a-select-option value=\"周期执行\">周期执行</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n        </a-row>\n      </a-card>\n\n      <a-tabs defaultActiveKey=\"1\" >\n        <a-tab-pane tab=\"Tab 1\" key=\"1\">\n\n          <a-table :columns=\"columns\" :dataSource=\"data\" :pagination=\"false\" size=\"middle\">\n            <template v-for=\"(col, i) in ['name', 'workId', 'department']\" :slot=\"col\" slot-scope=\"text, record, index\">\n              <a-tooltip  title=\"必填项\" :defaultVisible=\"false\" overlayStyle=\"{ color: 'red' }\">\n                <a-input :key=\"col\" v-if=\"record.editable\" style=\"margin: -5px 0\"  :value=\"text\" :placeholder=\"columns[i].title\" @change=\"e => handlerRowChange(e.target.value, record.key, col)\"/>\n              <template v-else>{{ text }}</template>\n              </a-tooltip>\n            </template>\n            <template slot=\"operation\" slot-scope=\"text, record, index\">\n              <template v-if=\"record.editable\">\n                <span v-if=\"record.isNew\">\n                  <a @click=\"saveRow(record.key)\">添加</a>\n                  <a-divider type=\"vertical\"/>\n                  <a-popconfirm title=\"是否要删除此行？\" @confirm=\"removeRow(record.key)\"><a>删除</a></a-popconfirm>\n                </span>\n                <span v-else>\n                  <a @click=\"saveRow(record.key)\">保存</a>\n                  <a-divider type=\"vertical\"/>\n                  <a @click=\"cancelEditRow(record.key)\">取消</a>\n                </span>\n              </template>\n              <span v-else>\n                <a @click=\"editRow(record.key)\">编辑</a>\n                <a-divider type=\"vertical\"/>\n                <a-popconfirm title=\"是否要删除此行？\" @confirm=\"removeRow(record.key)\"><a>删除</a></a-popconfirm>\n              </span>\n            </template>\n          </a-table>\n\n          <a-button style=\"width: 100%; margin-top: 16px; margin-bottom: 8px\" type=\"dashed\" icon=\"plus\" @click=\"newRow\">新增成员</a-button>\n        </a-tab-pane>\n        <a-tab-pane tab=\"Tab 2\" key=\"2\" forceRender>\n          Content of Tab Pane 2\n        </a-tab-pane>\n        <a-tab-pane tab=\"Tab 3\" key=\"3\">Content of Tab Pane 3</a-tab-pane>\n      </a-tabs>\n\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}