{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\TransferBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\TransferBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: '<PERSON>',\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    x: {\n      type: String,\n      default: 'x'\n    },\n    y: {\n      type: String,\n      default: 'y'\n    },\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    height: {\n      type: Number,\n      default: 254\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  computed: {\n    scale: function scale() {\n      return [{\n        dataKey: 'x',\n        title: this.x,\n        alias: this.x\n      }, {\n        dataKey: 'y',\n        title: this.y,\n        alias: this.y\n      }];\n    }\n  },\n  created: function created() {\n    // this.getMonthBar()\n  },\n  methods: {\n    // getMonthBar() {\n    //   this.$http.get('/analysis/month-bar')\n    //     .then(res => {\n    //       this.data = res.result\n    //     })\n    // }\n  }\n};", {"version": 3, "names": ["name", "props", "title", "type", "String", "default", "x", "y", "data", "Array", "_default", "height", "Number", "computed", "scale", "dataKey", "alias", "created", "methods"], "sources": ["src/components/chart/TransferBar.vue"], "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart\n      :height=\"height\"\n      :data=\"data\"\n      :scale=\"scale\"\n      :forceFit=\"true\"\n      :padding=\"['auto', 'auto', '40', '50']\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n\n  export default {\n    name: 'Bar',\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      x: {\n        type: String,\n        default: 'x'\n      },\n      y: {\n        type: String,\n        default: 'y'\n      },\n      data: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {}\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      // this.getMonthBar()\n    },\n    methods: {\n      // getMonthBar() {\n      //   this.$http.get('/analysis/month-bar')\n      //     .then(res => {\n      //       this.data = res.result\n      //     })\n      // }\n    }\n  }\n</script>"], "mappings": "AAkBA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,CAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,CAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAG,IAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACAC,MAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;EACA;EACAK,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,QACA;QAAAC,OAAA;QAAAb,KAAA,OAAAI,CAAA;QAAAU,KAAA,OAAAV;MAAA,GACA;QAAAS,OAAA;QAAAb,KAAA,OAAAK,CAAA;QAAAS,KAAA,OAAAT;MAAA,EACA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;AAEA", "ignoreList": []}]}