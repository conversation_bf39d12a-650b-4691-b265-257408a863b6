{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\Analysis.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\Analysis.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import ChartCard from '@/components/ChartCard'\n  import ACol from \"ant-design-vue/es/grid/Col\"\n  import ATooltip from \"ant-design-vue/es/tooltip/Tooltip\"\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniBar from '@/components/chart/MiniBar'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import AreaChartTy from '@/components/chart/AreaChartTy'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n  import BarMultid from '@/components/chart/BarMultid'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import RankList from '@/components/chart/RankList'\n  import Bar from '@/components/chart/Bar'\n  import Trend from '@/components/Trend'\n  import { getAction } from '@/api/manage'\n  import { filterObj } from '@/utils/util'\n  import moment from 'dayjs'\n\n  const rankList = []\n  for (let i = 0; i < 7; i++) {\n    rankList.push({\n      name: '白鹭岛 ' + (i+1) + ' 号店',\n      total: 1234.56 - i * 100\n    })\n  }\n\n  const dataCol1 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '权利类型',\n    align:\"center\",\n    dataIndex: 'droitType'\n  },{\n    title: '登记类型',\n    align:\"center\",\n    dataIndex: 'registeType'\n  },{\n    title: '座落',\n    align:\"center\",\n    dataIndex: 'beLocated'\n  },{\n    title: '权利人',\n    align:\"center\",\n    dataIndex: 'qlr'\n  },{\n    title: '义务人',\n    align:\"center\",\n    dataIndex: 'ywr'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '受理时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '办理进度',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n\n  const dataCol2 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '权利类型',\n    align:\"center\",\n    dataIndex: 'droitType'\n  },{\n    title: '登记类型',\n    align:\"center\",\n    dataIndex: 'registeType'\n  },{\n    title: '座落',\n    align:\"center\",\n    dataIndex: 'beLocated'\n  },{\n    title: '权利人',\n    align:\"center\",\n    dataIndex: 'qlr'\n  },{\n    title: '义务人',\n    align:\"center\",\n    dataIndex: 'ywr'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '发起时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '超时时间',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n\n  const jhjgData = [\n    { type: '房管', '一月': 900, '二月': 1120, '三月': 1380, '四月': 1480, '五月': 1450, '六月': 1100, '七月':1300, '八月':900,'九月':1000 ,'十月':1200 ,'十一月':600 ,'十二月':900 },\n    { type: '税务', '一月':1200, '二月': 1500, '三月': 1980, '四月': 2000, '五月': 1000, '六月': 600, '七月':900, '八月':1100,'九月':1300 ,'十月':2000 ,'十一月':900 ,'十二月':1100 },\n    { type: '不动产', '一月':2000, '二月': 1430, '三月': 1300, '四月': 1400, '五月': 900, '六月': 500, '七月':600, '八月':1000,'九月':600 ,'十月':1000 ,'十一月':1500 ,'十二月':1200 }\n  ]\n  const jhjgFields=[\n    '一月','二月','三月','四月','五月','六月',\n    '七月','八月','九月','十月','十一月','十二月'\n  ]\n\n\n  const xljgData = [\n    {type:'一月',\"房管\":1.12,\"税务\":1.55,\"不动产\":1.2},\n    {type:'二月',\"房管\":1.65,\"税务\":1.32,\"不动产\":1.42},\n    {type:'三月',\"房管\":1.85,\"税务\":1.1,\"不动产\":1.5},\n\n    {type:'四月',\"房管\":1.33,\"税务\":1.63,\"不动产\":1.4},\n    {type:'五月',\"房管\":1.63,\"税务\":1.8,\"不动产\":1.7},\n    {type:'六月',\"房管\":1.85,\"税务\":1.98,\"不动产\":1.8},\n\n    {type:'七月',\"房管\":1.98,\"税务\":1.5,\"不动产\":1.76},\n    {type:'八月',\"房管\":1.48,\"税务\":1.2,\"不动产\":1.3},\n    {type:'九月',\"房管\":1.41,\"税务\":1.9,\"不动产\":1.6},\n\n    {type:'十月',\"房管\":1.1,\"税务\":1.1,\"不动产\":1.4},\n    {type:'十一月',\"房管\":1.85,\"税务\":1.6,\"不动产\":1.5},\n    {type:'十二月',\"房管\":1.5,\"税务\":1.4,\"不动产\":1.3}\n  ]\n  const xljgFields=[\"房管\",\"税务\",\"不动产\"]\n  export default {\n    name: \"Analysis\",\n    components: {\n      ATooltip,\n      ACol,\n      ChartCard,\n      MiniArea,\n      MiniBar,\n      MiniProgress,\n      RankList,\n      Bar,\n      Trend,\n      LineChartMultid,\n      AreaChartTy,\n      DashChartDemo,\n      BarMultid\n    },\n    data() {\n      return {\n        xljgData,\n        xljgFields,\n        jhjgData,\n        jhjgFields,\n        loading: true,\n        rankList,\n        zsll:0,\n        zbjl:0,\n        todaySll:0,\n        todayBjl:0,\n        todayISll:0,\n        todayIBjl:0,\n      registerTypeList:[{\n          text:\"业务受理\"\n        },{\n          text:\"业务管理\"\n        },{\n          text:\"文件管理\"\n        },{\n          text:\"信息查询\"\n        }],\n        // 分页参数\n        ipagination:{\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        //数据集\n        dataSource:[],\n        dataSource1:[],\n        dataSource2:[],\n        url:{\n          analysis:\"/sps/register/analysis\",\n          list:\"sps/register/virtualList\",\n          countSll:\"sps/register/sllTenDaysCount\",\n          countBjl:\"sps/register/bjlTenDaysCount\",\n          countISll:'sps/register/ISllTenDaysCount',\n          countIBjl:'sps/register/IBjlTenDaysCount',\n          queryDiskInfo:'api/queryDiskInfo'\n        },\n        chartData:{\n          sll:[],\n          bjl:[],\n          isll:[],\n          ibjl:[]\n        },\n        cardCount:{\n          sll:0,\n          bjl:0,\n          isll:0,\n          ibjl:0\n        },\n        columns:dataCol1,\n        columns2:dataCol2,\n        diskInfo:[]\n      }\n    },\n    methods:{\n      goPage(index){\n        if(index==0){\n          this.$router.push({\n            path: '/isps/registerStepForm',\n            name: 'isps-registerStepForm',\n          });\n        }else if(index==1){\n          this.$router.push({\n            path: '/isps/registerList',\n            name: 'isps-registerList',\n          });\n\n        }else if(index==2){\n          this.$router.push({\n            path: '/isps/fileManage',\n            name: 'isps-fileManage',\n          });\n        }else if(index==3){\n          this.$router.push({\n            path: '/isps/infoSearch',\n            name: 'isps-infoSearch',\n          });\n        }\n      },\n      loadList (arg){\n        if(arg===1){\n          this.ipagination.current = 1;\n        }\n        var params = this.getQueryParams();//查询条件\n        getAction(this.url.list,params).then((res)=>{\n          console.log(\"dsdsd\",res.result)\n          this.dataSource1 = res.result.data1;\n          this.dataSource2 = res.result.data2;\n          this.ipagination.total = 5;\n        });\n      },\n      getQueryParams(){\n        var param = {flowStatus:\"-3\"};\n        param.pageNo = this.ipagination.current;\n        param.pageSize = this.ipagination.pageSize;\n        return filterObj(param);\n      },\n      formatRespectiveHoldCert(value){\n        return (value==\"1\"||eval(value))?\"是\":\"否\"\n      },\n      formatCertFormat(value){\n        if(value==\"1\"){\n          return \"单一版\"\n        }else if(value==\"2\"){\n          return \"集成版\"\n        }else{\n          return value;\n        }\n      },\n      getFlowRateNumber(value){\n        return Number(value)\n      },\n      getFlowPercent(record){\n        if(record.flowStatus==\"3\"){\n          return 100\n        }else if(record.flowStatus==\"0\"){\n          return 0\n        }else{\n          return record.flowRate\n        }\n      },\n      getFlowStatus(status){\n        if(status==\"4\"){\n          return \"exception\";\n        }else if(status==\"0\"){\n          return \"normal\";\n        }else if(status==\"3\"){\n          return \"success\";\n        }else{\n          return \"active\";\n        }\n\n      },\n      queryCount(){\n        getAction(this.url.analysis).then((res)=>{\n          if(res.success){\n            this.cardCount = res.result\n          }\n          console.log(res);\n        });\n      },\n\n      loadDiskInfo(){\n        getAction(this.url.queryDiskInfo).then((res)=>{\n          if(res.success){\n            console.log(res.result)\n            let one=0,two=\"\";\n            for(let a in res.result){\n              let tempNum = Number(res.result[a].max)\n              if(tempNum>one){\n                one = tempNum\n                two = res.result[a].name\n              }\n            }\n            let ontItem = res.result.filter((item)=>{return item.name == two})[0]\n            ontItem.restPPT = ontItem.restPPT/10\n            this.diskInfo.push(ontItem);\n\n            if(res.result.length>1){\n              let one2=0,two2=\"\";\n              for(let a in res.result){\n                if(res.result[a].name == two){\n                  continue;\n                }\n                let tempNum = Number(res.result[a].max)\n                if(tempNum>one2){\n                  one2 = tempNum\n                  two2 = res.result[a].name\n                }\n              }\n              let one2Item = res.result.filter((item)=>{return item.name == two2})[0]\n              one2Item.restPPT = one2Item.restPPT/10\n              this.diskInfo.push(one2Item);\n            }\n\n          }else{\n            console.log(res.message)\n          }\n        })\n      },\n      loadChartData(){\n        getAction(this.url.countSll).then((res)=>{\n          if(res.success){\n            let map = res.result;\n            for(var key in map){\n              let dataStr = key;\n              let value = map[key];\n              let today = moment(new Date()).format('YYYY-MM-DD');\n              if(dataStr == today){\n                this.todaySll = map[today];\n              }\n              this.chartData.sll.push({\n                x: dataStr,\n                y: value\n              });\n            }\n          }\n\n        }),\n          getAction(this.url.countBjl).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayBjl = map[today];\n                }\n                this.chartData.bjl.push({\n                  x: dataStr,\n                  y: value\n                });\n              }\n            }\n          }),\n          getAction(this.url.countISll).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayISll = map[today];\n                }\n                this.chartData.isll.push({\n                  x: key,\n                  y: value\n                });\n              }\n            }\n          }),\n          getAction(this.url.countIBjl).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayIBjl = map[today];\n                }\n                this.chartData.ibjl.push({\n                  x: key,\n                  y: value\n                });\n              }\n            }\n          })\n      }\n    },\n    created() {\n      this.loadDiskInfo()\n      this.queryCount();\n      this.loadChartData();\n      this.loadList(1);\n      setTimeout(() => {\n        this.loading = !this.loading\n      }, 1000)\n    }\n  }\n", {"version": 3, "sources": ["Analysis.vue"], "names": [], "mappings": ";AAm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file": "Analysis.vue", "sourceRoot": "src/views/jeecg/report", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-row :gutter=\"24\">\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"受理量\" :total=\"cardCount.sll | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-area :dataSource=\"chartData.sll\" />\n          </div>\n          <template slot=\"footer\">今日受理量：<span>{{ todaySll }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"办结量\" :total=\"cardCount.bjl | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-area :dataSource=\"chartData.bjl\"/>\n          </div>\n          <template slot=\"footer\">今日办结量：<span>{{ todayBjl }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"用户受理量\" :total=\"cardCount.isll | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-bar :dataSource=\"chartData.isll\"/>\n          </div>\n          <template slot=\"footer\">用户今日受理量：<span>{{ todayISll }}</span></template>\n        </chart-card>\n      </a-col>\n      <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n        <chart-card :loading=\"loading\" title=\"用户办结量\" :total=\"cardCount.ibjl | NumberFormat\">\n          <a-tooltip title=\"指标说明\" slot=\"action\">\n            <a-icon type=\"info-circle-o\" />\n          </a-tooltip>\n          <div>\n            <mini-bar :dataSource=\"chartData.ibjl\"/>\n          </div>\n          <template slot=\"footer\">用户今日办结量：<span>{{ todayIBjl }}</span></template>\n        </chart-card>\n      </a-col>\n    </a-row>\n\n    <a-card :loading=\"loading\" :bordered=\"false\" :body-style=\"{padding: '0'}\">\n      <div class=\"salesCard\">\n        <a-tabs default-active-key=\"1\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n          <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n            <div class=\"extra-item\">\n              <a>今日</a>\n              <a>本周</a>\n              <a>本月</a>\n              <a>本年</a>\n            </div>\n            <a-range-picker :style=\"{width: '256px'}\" />\n          </div>\n\n          <a-tab-pane loading=\"true\" tab=\"受理监管\" key=\"1\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <bar title=\"受理量统计\" />\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"交互监管\" key=\"2\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <bar-multid :dataSource=\"jhjgData\" :fields=\"jhjgFields\" title=\"平台与部门交互量统计\"></bar-multid>\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"效率监管\" key=\"3\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <line-chart-multid :dataSource=\"xljgData\" :fields=\"xljgFields\" title=\"平台与部门交互效率统计\"></line-chart-multid>\n              </a-col>\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n          <a-tab-pane tab=\"存储监管\" key=\"4\">\n            <a-row>\n              <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n                <a-row>\n                  <template v-if=\"diskInfo && diskInfo.length>0\">\n                    <a-col :span=\"12\" v-for=\"(item,index) in diskInfo\" :key=\" 'diskInfo'+index \">\n                      <dash-chart-demo :title=\"item.name\" :dataSource=\"item.restPPT\"></dash-chart-demo>\n                    </a-col>\n                  </template>\n                </a-row>\n              </a-col>\n\n              <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n                <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                  <div class=\"item-group\">\n                    <a-row>\n                      <a-col :class=\"'more-btn'\" :span=\"10\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                        <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n\n              </a-col>\n            </a-row>\n          </a-tab-pane>\n\n        </a-tabs>\n\n      </div>\n    </a-card>\n\n    <a-row :gutter=\"12\">\n      <a-card :loading=\"loading\" :class=\"{ 'anty-list-cust':true }\" :bordered=\"false\" :style=\"{ marginTop: '24px' }\">\n\n        <a-tabs default-active-key=\"1\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n          <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n            <a-radio-group defaultValue=\"1\">\n              <a-radio-button value=\"1\">转移登记</a-radio-button>\n              <a-radio-button value=\"2\">抵押登记</a-radio-button>\n            </a-radio-group>\n          </div>\n\n          <a-tab-pane loading=\"true\" tab=\"业务流程限时监管\" key=\"1\">\n\n            <a-table :dataSource=\"dataSource1\" size=\"default\" rowKey=\"id\" :columns=\"columns\" :pagination=\"ipagination\">\n              <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n                <a-progress :percent=\"getFlowRateNumber(record.flowRate)\" style=\"width:80px\" />\n              </template>\n            </a-table>\n          </a-tab-pane>\n\n          <a-tab-pane loading=\"true\" tab=\"业务节点限时监管\" key=\"2\">\n            <a-table :dataSource=\"dataSource2\" size=\"default\" rowKey=\"id\" :columns=\"columns2\" :pagination=\"ipagination\">\n              <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n                <span style=\"color: red;\">{{ record.flowRate }}分钟</span>\n              </template>\n            </a-table>\n          </a-tab-pane>\n\n        </a-tabs>\n\n\n      </a-card>\n    </a-row>\n  </div>\n</template>\n\n<script>\n  import ChartCard from '@/components/ChartCard'\n  import ACol from \"ant-design-vue/es/grid/Col\"\n  import ATooltip from \"ant-design-vue/es/tooltip/Tooltip\"\n  import MiniArea from '@/components/chart/MiniArea'\n  import MiniBar from '@/components/chart/MiniBar'\n  import LineChartMultid from '@/components/chart/LineChartMultid'\n  import AreaChartTy from '@/components/chart/AreaChartTy'\n  import DashChartDemo from '@/components/chart/DashChartDemo'\n  import BarMultid from '@/components/chart/BarMultid'\n  import MiniProgress from '@/components/chart/MiniProgress'\n  import RankList from '@/components/chart/RankList'\n  import Bar from '@/components/chart/Bar'\n  import Trend from '@/components/Trend'\n  import { getAction } from '@/api/manage'\n  import { filterObj } from '@/utils/util'\n  import moment from 'dayjs'\n\n  const rankList = []\n  for (let i = 0; i < 7; i++) {\n    rankList.push({\n      name: '白鹭岛 ' + (i+1) + ' 号店',\n      total: 1234.56 - i * 100\n    })\n  }\n\n  const dataCol1 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '权利类型',\n    align:\"center\",\n    dataIndex: 'droitType'\n  },{\n    title: '登记类型',\n    align:\"center\",\n    dataIndex: 'registeType'\n  },{\n    title: '座落',\n    align:\"center\",\n    dataIndex: 'beLocated'\n  },{\n    title: '权利人',\n    align:\"center\",\n    dataIndex: 'qlr'\n  },{\n    title: '义务人',\n    align:\"center\",\n    dataIndex: 'ywr'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '受理时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '办理进度',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n\n  const dataCol2 = [{\n    title: '业务号',\n    align:\"center\",\n    dataIndex: 'reBizCode'\n  },{\n    title: '权利类型',\n    align:\"center\",\n    dataIndex: 'droitType'\n  },{\n    title: '登记类型',\n    align:\"center\",\n    dataIndex: 'registeType'\n  },{\n    title: '座落',\n    align:\"center\",\n    dataIndex: 'beLocated'\n  },{\n    title: '权利人',\n    align:\"center\",\n    dataIndex: 'qlr'\n  },{\n    title: '义务人',\n    align:\"center\",\n    dataIndex: 'ywr'\n  },{\n    title: '受理人',\n    align:\"center\",\n    dataIndex: 'acceptBy'\n  },{\n    title: '发起时间',\n    align:\"center\",\n    dataIndex: 'acceptDate'\n  },{\n    title: '当前节点',\n    align:\"center\",\n    dataIndex: 'curNode'\n  },{\n    title: '超时时间',\n    align:\"center\",\n    dataIndex: 'flowRate',\n    scopedSlots: { customRender: 'flowRate' }\n  }];\n\n  const jhjgData = [\n    { type: '房管', '一月': 900, '二月': 1120, '三月': 1380, '四月': 1480, '五月': 1450, '六月': 1100, '七月':1300, '八月':900,'九月':1000 ,'十月':1200 ,'十一月':600 ,'十二月':900 },\n    { type: '税务', '一月':1200, '二月': 1500, '三月': 1980, '四月': 2000, '五月': 1000, '六月': 600, '七月':900, '八月':1100,'九月':1300 ,'十月':2000 ,'十一月':900 ,'十二月':1100 },\n    { type: '不动产', '一月':2000, '二月': 1430, '三月': 1300, '四月': 1400, '五月': 900, '六月': 500, '七月':600, '八月':1000,'九月':600 ,'十月':1000 ,'十一月':1500 ,'十二月':1200 }\n  ]\n  const jhjgFields=[\n    '一月','二月','三月','四月','五月','六月',\n    '七月','八月','九月','十月','十一月','十二月'\n  ]\n\n\n  const xljgData = [\n    {type:'一月',\"房管\":1.12,\"税务\":1.55,\"不动产\":1.2},\n    {type:'二月',\"房管\":1.65,\"税务\":1.32,\"不动产\":1.42},\n    {type:'三月',\"房管\":1.85,\"税务\":1.1,\"不动产\":1.5},\n\n    {type:'四月',\"房管\":1.33,\"税务\":1.63,\"不动产\":1.4},\n    {type:'五月',\"房管\":1.63,\"税务\":1.8,\"不动产\":1.7},\n    {type:'六月',\"房管\":1.85,\"税务\":1.98,\"不动产\":1.8},\n\n    {type:'七月',\"房管\":1.98,\"税务\":1.5,\"不动产\":1.76},\n    {type:'八月',\"房管\":1.48,\"税务\":1.2,\"不动产\":1.3},\n    {type:'九月',\"房管\":1.41,\"税务\":1.9,\"不动产\":1.6},\n\n    {type:'十月',\"房管\":1.1,\"税务\":1.1,\"不动产\":1.4},\n    {type:'十一月',\"房管\":1.85,\"税务\":1.6,\"不动产\":1.5},\n    {type:'十二月',\"房管\":1.5,\"税务\":1.4,\"不动产\":1.3}\n  ]\n  const xljgFields=[\"房管\",\"税务\",\"不动产\"]\n  export default {\n    name: \"Analysis\",\n    components: {\n      ATooltip,\n      ACol,\n      ChartCard,\n      MiniArea,\n      MiniBar,\n      MiniProgress,\n      RankList,\n      Bar,\n      Trend,\n      LineChartMultid,\n      AreaChartTy,\n      DashChartDemo,\n      BarMultid\n    },\n    data() {\n      return {\n        xljgData,\n        xljgFields,\n        jhjgData,\n        jhjgFields,\n        loading: true,\n        rankList,\n        zsll:0,\n        zbjl:0,\n        todaySll:0,\n        todayBjl:0,\n        todayISll:0,\n        todayIBjl:0,\n      registerTypeList:[{\n          text:\"业务受理\"\n        },{\n          text:\"业务管理\"\n        },{\n          text:\"文件管理\"\n        },{\n          text:\"信息查询\"\n        }],\n        // 分页参数\n        ipagination:{\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        //数据集\n        dataSource:[],\n        dataSource1:[],\n        dataSource2:[],\n        url:{\n          analysis:\"/sps/register/analysis\",\n          list:\"sps/register/virtualList\",\n          countSll:\"sps/register/sllTenDaysCount\",\n          countBjl:\"sps/register/bjlTenDaysCount\",\n          countISll:'sps/register/ISllTenDaysCount',\n          countIBjl:'sps/register/IBjlTenDaysCount',\n          queryDiskInfo:'api/queryDiskInfo'\n        },\n        chartData:{\n          sll:[],\n          bjl:[],\n          isll:[],\n          ibjl:[]\n        },\n        cardCount:{\n          sll:0,\n          bjl:0,\n          isll:0,\n          ibjl:0\n        },\n        columns:dataCol1,\n        columns2:dataCol2,\n        diskInfo:[]\n      }\n    },\n    methods:{\n      goPage(index){\n        if(index==0){\n          this.$router.push({\n            path: '/isps/registerStepForm',\n            name: 'isps-registerStepForm',\n          });\n        }else if(index==1){\n          this.$router.push({\n            path: '/isps/registerList',\n            name: 'isps-registerList',\n          });\n\n        }else if(index==2){\n          this.$router.push({\n            path: '/isps/fileManage',\n            name: 'isps-fileManage',\n          });\n        }else if(index==3){\n          this.$router.push({\n            path: '/isps/infoSearch',\n            name: 'isps-infoSearch',\n          });\n        }\n      },\n      loadList (arg){\n        if(arg===1){\n          this.ipagination.current = 1;\n        }\n        var params = this.getQueryParams();//查询条件\n        getAction(this.url.list,params).then((res)=>{\n          console.log(\"dsdsd\",res.result)\n          this.dataSource1 = res.result.data1;\n          this.dataSource2 = res.result.data2;\n          this.ipagination.total = 5;\n        });\n      },\n      getQueryParams(){\n        var param = {flowStatus:\"-3\"};\n        param.pageNo = this.ipagination.current;\n        param.pageSize = this.ipagination.pageSize;\n        return filterObj(param);\n      },\n      formatRespectiveHoldCert(value){\n        return (value==\"1\"||eval(value))?\"是\":\"否\"\n      },\n      formatCertFormat(value){\n        if(value==\"1\"){\n          return \"单一版\"\n        }else if(value==\"2\"){\n          return \"集成版\"\n        }else{\n          return value;\n        }\n      },\n      getFlowRateNumber(value){\n        return Number(value)\n      },\n      getFlowPercent(record){\n        if(record.flowStatus==\"3\"){\n          return 100\n        }else if(record.flowStatus==\"0\"){\n          return 0\n        }else{\n          return record.flowRate\n        }\n      },\n      getFlowStatus(status){\n        if(status==\"4\"){\n          return \"exception\";\n        }else if(status==\"0\"){\n          return \"normal\";\n        }else if(status==\"3\"){\n          return \"success\";\n        }else{\n          return \"active\";\n        }\n\n      },\n      queryCount(){\n        getAction(this.url.analysis).then((res)=>{\n          if(res.success){\n            this.cardCount = res.result\n          }\n          console.log(res);\n        });\n      },\n\n      loadDiskInfo(){\n        getAction(this.url.queryDiskInfo).then((res)=>{\n          if(res.success){\n            console.log(res.result)\n            let one=0,two=\"\";\n            for(let a in res.result){\n              let tempNum = Number(res.result[a].max)\n              if(tempNum>one){\n                one = tempNum\n                two = res.result[a].name\n              }\n            }\n            let ontItem = res.result.filter((item)=>{return item.name == two})[0]\n            ontItem.restPPT = ontItem.restPPT/10\n            this.diskInfo.push(ontItem);\n\n            if(res.result.length>1){\n              let one2=0,two2=\"\";\n              for(let a in res.result){\n                if(res.result[a].name == two){\n                  continue;\n                }\n                let tempNum = Number(res.result[a].max)\n                if(tempNum>one2){\n                  one2 = tempNum\n                  two2 = res.result[a].name\n                }\n              }\n              let one2Item = res.result.filter((item)=>{return item.name == two2})[0]\n              one2Item.restPPT = one2Item.restPPT/10\n              this.diskInfo.push(one2Item);\n            }\n\n          }else{\n            console.log(res.message)\n          }\n        })\n      },\n      loadChartData(){\n        getAction(this.url.countSll).then((res)=>{\n          if(res.success){\n            let map = res.result;\n            for(var key in map){\n              let dataStr = key;\n              let value = map[key];\n              let today = moment(new Date()).format('YYYY-MM-DD');\n              if(dataStr == today){\n                this.todaySll = map[today];\n              }\n              this.chartData.sll.push({\n                x: dataStr,\n                y: value\n              });\n            }\n          }\n\n        }),\n          getAction(this.url.countBjl).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayBjl = map[today];\n                }\n                this.chartData.bjl.push({\n                  x: dataStr,\n                  y: value\n                });\n              }\n            }\n          }),\n          getAction(this.url.countISll).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayISll = map[today];\n                }\n                this.chartData.isll.push({\n                  x: key,\n                  y: value\n                });\n              }\n            }\n          }),\n          getAction(this.url.countIBjl).then((res)=>{\n            if(res.success){\n              let map = res.result;\n              for(var key in map){\n                let dataStr = key;\n                let value = map[key];\n                let today = moment(new Date()).format('YYYY-MM-DD');\n                if(dataStr == today){\n                  this.todayIBjl = map[today];\n                }\n                this.chartData.ibjl.push({\n                  x: key,\n                  y: value\n                });\n              }\n            }\n          })\n      }\n    },\n    created() {\n      this.loadDiskInfo()\n      this.queryCount();\n      this.loadChartData();\n      this.loadList(1);\n      setTimeout(() => {\n        this.loading = !this.loading\n      }, 1000)\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .extra-wrapper {\n    line-height: 55px;\n    padding-right: 24px;\n\n    .extra-item {\n      display: inline-block;\n      margin-right: 24px;\n\n      a {\n        margin-left: 24px;\n      }\n    }\n  }\n\n  .item-group {\n    padding: 20px 0 8px 24px;\n    font-size: 0;\n    a {\n      color: rgba(0, 0, 0, 0.65);\n      display: inline-block;\n      font-size: 14px;\n      margin-bottom: 13px;\n      width: 25%;\n    }\n  }\n\n  .item-group {\n    .more-btn {\n      margin-bottom: 13px;\n      text-align: center;\n    }\n  }\n\n  .list-content-item {\n    color: rgba(0, 0, 0, .45);\n    display: inline-block;\n    vertical-align: middle;\n    font-size: 14px;\n    margin-left: 40px;\n  }\n\n  @media only screen and (min-width: 1600px) {\n    .list-content-item{\n      margin-left:60px;\n    }\n  }\n\n  @media only screen and (max-width: 1300px) {\n    .list-content-item{\n      margin-left:20px;\n    }\n    .width-hidden4{\n      display:none\n    }\n  }\n  .list-content-item{\n    span{line-height: 20px;}\n  }\n  .list-content-item{\n    p{margin-top: 4px;margin-bottom:0;line-height:22px;}\n  }\n  .anty-list-cust {\n    .ant-list-item-meta{flex: 0.3 !important;}\n  }\n  .anty-list-cust {\n    .ant-list-item-content{flex:1 !important; justify-content:flex-start !important;margin-left: 20px;}\n  }\n\n\n</style>"]}]}