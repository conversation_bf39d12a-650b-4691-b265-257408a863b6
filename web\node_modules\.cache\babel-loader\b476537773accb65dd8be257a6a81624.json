{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\JSelectClassModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\JSelectClassModal.vue", "mtime": 1750647514565}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { queryDepartTreeList, queryMyDepartTreeList } from '@/api/api';\nexport default {\n  name: 'JSelectClassModal',\n  props: {\n    modalWidth: {\n      type: Number,\n      default: 500\n    },\n    multi: {\n      type: Boolean,\n      default: false\n    },\n    rootOpened: {\n      type: Boolean,\n      default: true\n    },\n    onlyLeaf: {\n      type: Boolean,\n      default: false\n    },\n    departId: {\n      type: String\n    },\n    // 默认传入班级分类，避免显示其他类型机构\n    onlyCategory: {\n      type: Number,\n      default: 3 // 假设3是班级类型\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      treeData: [],\n      autoExpandParent: true,\n      expandedKeys: [],\n      dataList: [],\n      checkedKeys: [],\n      checkedRows: [],\n      searchValue: \"\"\n    };\n  },\n  created: function created() {\n    this.loadClasses();\n  },\n  watch: {\n    departId: function departId() {\n      this.initClassComponent();\n    },\n    visible: {\n      handler: function handler(val) {\n        if (this.departId) {\n          this.checkedKeys = this.departId.split(\",\");\n        } else {\n          this.checkedKeys = [];\n        }\n        // 修改：当模态框显示时，重新加载班级数据\n        if (val === true) {\n          this.loadClasses();\n        }\n      }\n    }\n  },\n  methods: {\n    show: function show() {\n      this.visible = true;\n      this.checkedRows = [];\n      this.checkedKeys = [];\n      this.searchValue = '';\n      this.loadClasses();\n    },\n    loadClasses: function loadClasses() {\n      var _this = this;\n      this.loading = true;\n      // 注释：恢复使用queryMyDepartTreeList()方法，确保教师角色只能看到自己关联的班级\n      queryMyDepartTreeList().then(function (res) {\n        _this.loading = false;\n        if (res.success) {\n          var arr = _toConsumableArray(res.result);\n          // 清空之前的数据\n          _this.dataList = [];\n          _this.reWriterWithSlot(arr);\n          _this.treeData = arr;\n          _this.initClassComponent();\n          if (_this.onlyLeaf || _this.onlyCategory) {\n            _this.disableNoneChildNode(_this.treeData);\n          }\n          if (_this.rootOpened) {\n            _this.initExpandedKeys(res.result);\n          }\n          console.log('班级树数据:', _this.treeData);\n          console.log('扁平化数据列表:', _this.dataList);\n        }\n      }).catch(function () {\n        _this.loading = false;\n      });\n    },\n    // 递归禁用节点\n    disableNoneChildNode: function disableNoneChildNode(treeData) {\n      for (var i = 0; i < treeData.length; i++) {\n        if (this.onlyCategory != null) {\n          if (treeData[i].orgCategory == this.onlyCategory) {\n            treeData[i].disabled = false;\n          } else {\n            treeData[i].disabled = true;\n          }\n        }\n        if (treeData[i].isLeaf) {\n          // treeData[i].disabled = false\n        } else {\n          if (this.onlyLeaf) {\n            treeData[i].disabled = true;\n          }\n          this.disableNoneChildNode(treeData[i].children);\n        }\n      }\n    },\n    initClassComponent: function initClassComponent() {\n      var names = '';\n      if (this.departId) {\n        var currDepartId = this.departId;\n        var _iterator = _createForOfIteratorHelper(this.dataList),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var item = _step.value;\n            if (currDepartId.indexOf(item.key) >= 0) {\n              names += \",\" + item.title;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n        if (names) {\n          names = names.substring(1);\n        }\n      }\n      this.$emit(\"initComp\", names);\n    },\n    reWriterWithSlot: function reWriterWithSlot(arr) {\n      var _iterator2 = _createForOfIteratorHelper(arr),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var item = _step2.value;\n          // 为每个节点添加scopedSlots，确保能正确渲染标题\n          item.scopedSlots = {\n            title: 'title'\n          };\n\n          // 添加到扁平化列表中\n          this.dataList.push(item);\n\n          // 递归处理子节点\n          if (item.children && item.children.length > 0) {\n            this.reWriterWithSlot(item.children);\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n    },\n    initExpandedKeys: function initExpandedKeys(arr) {\n      if (arr && arr.length > 0) {\n        var keys = [];\n        var _iterator3 = _createForOfIteratorHelper(arr),\n          _step3;\n        try {\n          for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n            var item = _step3.value;\n            if (item.children && item.children.length > 0) {\n              keys.push(item.id);\n            }\n          }\n        } catch (err) {\n          _iterator3.e(err);\n        } finally {\n          _iterator3.f();\n        }\n        this.expandedKeys = [].concat(keys);\n      } else {\n        this.expandedKeys = [];\n      }\n    },\n    onCheck: function onCheck(checkedKeys, info) {\n      var _this2 = this;\n      if (!this.multi) {\n        var arr = checkedKeys.checked.filter(function (item) {\n          return _this2.checkedKeys.indexOf(item) < 0;\n        });\n        this.checkedKeys = _toConsumableArray(arr);\n        this.checkedRows = this.checkedKeys.length === 0 ? [] : [info.node.dataRef];\n      } else {\n        this.checkedKeys = checkedKeys.checked;\n        this.checkedRows = this.getCheckedRows(this.checkedKeys);\n      }\n    },\n    onSelect: function onSelect(selectedKeys, info) {\n      var keys = [];\n      keys.push(selectedKeys[0]);\n      if (!this.checkedKeys || this.checkedKeys.length === 0 || !this.multi) {\n        this.checkedKeys = [].concat(keys);\n        this.checkedRows = [info.node.dataRef];\n      } else {\n        var currKey = info.node.dataRef.key;\n        if (this.checkedKeys.indexOf(currKey) >= 0) {\n          this.checkedKeys = this.checkedKeys.filter(function (item) {\n            return item !== currKey;\n          });\n        } else {\n          var _this$checkedKeys;\n          (_this$checkedKeys = this.checkedKeys).push.apply(_this$checkedKeys, keys);\n        }\n      }\n      this.checkedRows = this.getCheckedRows(this.checkedKeys);\n    },\n    onExpand: function onExpand(expandedKeys) {\n      this.expandedKeys = expandedKeys;\n      this.autoExpandParent = false;\n    },\n    handleSubmit: function handleSubmit() {\n      if (!this.checkedKeys || this.checkedKeys.length == 0) {\n        this.$emit(\"ok\", '');\n      } else {\n        this.$emit(\"ok\", this.checkedRows, this.checkedKeys.join(\",\"));\n      }\n      this.handleClear();\n    },\n    handleCancel: function handleCancel() {\n      this.handleClear();\n    },\n    handleClear: function handleClear() {\n      this.visible = false;\n    },\n    getParentKey: function getParentKey(currKey, treeData) {\n      var parentKey;\n      for (var i = 0; i < treeData.length; i++) {\n        var node = treeData[i];\n        if (node.children) {\n          if (node.children.some(function (item) {\n            return item.key === currKey;\n          })) {\n            parentKey = node.key;\n          } else if (this.getParentKey(currKey, node.children)) {\n            parentKey = this.getParentKey(currKey, node.children);\n          }\n        }\n      }\n      return parentKey;\n    },\n    onSearch: function onSearch(value) {\n      // 重置搜索状态\n      if (!value) {\n        this.searchValue = '';\n        // 如果rootOpened为true，保持原有展开状态，否则折叠所有节点\n        if (!this.rootOpened) {\n          this.expandedKeys = [];\n        }\n        this.autoExpandParent = false;\n        return;\n      }\n\n      // 转换为小写进行不区分大小写搜索\n      var lowerValue = value.toLowerCase();\n\n      // 存储匹配的节点和需要展开的父节点\n      var expandedKeys = [];\n      var matchedKeys = [];\n\n      // 遍历所有节点查找匹配项\n      var _iterator4 = _createForOfIteratorHelper(this.dataList),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var item = _step4.value;\n          if (item.title && item.title.toLowerCase().indexOf(lowerValue) > -1) {\n            // 找到匹配的节点\n            matchedKeys.push(item.key);\n\n            // 查找并添加所有父节点到展开列表\n            var parentKey = this.getParentKey(item.key, this.treeData);\n            while (parentKey) {\n              expandedKeys.push(parentKey);\n              parentKey = this.getParentKey(parentKey, this.treeData);\n            }\n          }\n        }\n\n        // 合并展开的键并去重\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n      expandedKeys = _toConsumableArray(new Set([].concat(_toConsumableArray(expandedKeys), matchedKeys)));\n\n      // 更新组件状态\n      this.expandedKeys = expandedKeys;\n      this.searchValue = value;\n      this.autoExpandParent = true;\n      console.log('搜索值:', value);\n      console.log('展开的节点:', expandedKeys);\n    },\n    // 根据 checkedKeys 获取 rows\n    getCheckedRows: function getCheckedRows(checkedKeys) {\n      var forChildren = function forChildren(list, key) {\n        var _iterator5 = _createForOfIteratorHelper(list),\n          _step5;\n        try {\n          for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n            var item = _step5.value;\n            if (item.id === key) {\n              return item;\n            }\n            if (item.children instanceof Array) {\n              var value = forChildren(item.children, key);\n              if (value != null) {\n                return value;\n              }\n            }\n          }\n        } catch (err) {\n          _iterator5.e(err);\n        } finally {\n          _iterator5.f();\n        }\n        return null;\n      };\n      var rows = [];\n      var _iterator6 = _createForOfIteratorHelper(checkedKeys),\n        _step6;\n      try {\n        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {\n          var key = _step6.value;\n          var row = forChildren(this.treeData, key);\n          if (row != null) {\n            rows.push(row);\n          }\n        }\n      } catch (err) {\n        _iterator6.e(err);\n      } finally {\n        _iterator6.f();\n      }\n      return rows;\n    }\n  }\n};", {"version": 3, "names": ["queryDepartTreeList", "queryMyDepartTreeList", "name", "props", "modalWidth", "type", "Number", "default", "multi", "Boolean", "rootOpened", "<PERSON><PERSON><PERSON><PERSON>", "departId", "String", "onlyCategory", "data", "visible", "loading", "treeData", "autoExpandParent", "expandedKeys", "dataList", "checked<PERSON>eys", "checkedRows", "searchValue", "created", "loadClasses", "watch", "initClassComponent", "handler", "val", "split", "methods", "show", "_this", "then", "res", "success", "arr", "_toConsumableArray", "result", "reWriterWithSlot", "disableNoneChildNode", "initExpandedKeys", "console", "log", "catch", "i", "length", "orgCategory", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "children", "names", "currDepartId", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "value", "indexOf", "key", "title", "err", "e", "f", "substring", "$emit", "_iterator2", "_step2", "scopedSlots", "push", "keys", "_iterator3", "_step3", "id", "concat", "onCheck", "info", "_this2", "checked", "filter", "node", "dataRef", "getCheckedRows", "onSelect", "<PERSON><PERSON><PERSON><PERSON>", "curr<PERSON><PERSON>", "_this$checkedKeys", "apply", "onExpand", "handleSubmit", "join", "handleClear", "handleCancel", "get<PERSON><PERSON>nt<PERSON>ey", "parent<PERSON><PERSON>", "some", "onSearch", "lowerValue", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "_iterator4", "_step4", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list", "_iterator5", "_step5", "Array", "rows", "_iterator6", "_step6", "row"], "sources": ["src/components/jeecgbiz/modal/JSelectClassModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"选择班级\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    @ok=\"handleSubmit\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n    <a-spin tip=\"Loading...\" :spinning=\"loading\">\n      <a-input-search style=\"margin-bottom: 8px\" placeholder=\"请输入班级名称按回车进行搜索\" @search=\"onSearch\" />\n      <a-tree\n        checkable\n        class=\"my-class-select-tree\"\n        :treeData=\"treeData\"\n        :checkStrictly=\"true\"\n        @check=\"onCheck\"\n        @select=\"onSelect\"\n        @expand=\"onExpand\"\n        :autoExpandParent=\"autoExpandParent\"\n        :expandedKeys=\"expandedKeys\"\n        :checkedKeys=\"checkedKeys\">\n\n        <template slot=\"title\" slot-scope=\"{title}\">\n          <template v-if=\"searchValue && title\">\n            <template v-if=\"title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1\">\n              {{title.substr(0, title.toLowerCase().indexOf(searchValue.toLowerCase()))}}\n              <span style=\"color: #f50\">{{title.substr(title.toLowerCase().indexOf(searchValue.toLowerCase()), searchValue.length)}}</span>\n              {{title.substr(title.toLowerCase().indexOf(searchValue.toLowerCase()) + searchValue.length)}}\n            </template>\n            <template v-else>{{title}}</template>\n          </template>\n          <template v-else>{{title}}</template>\n        </template>\n      </a-tree>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { queryDepartTreeList, queryMyDepartTreeList } from '@/api/api'\n  export default {\n    name: 'JSelectClassModal',\n    props:{\n      modalWidth: {\n        type: Number,\n        default: 500\n      },\n      multi: {\n        type: Boolean,\n        default: false\n      },\n      rootOpened: {\n        type: Boolean,\n        default: true\n      },\n      onlyLeaf: {\n        type: Boolean,\n        default: false\n      },\n      departId: {\n        type: String\n      },\n      // 默认传入班级分类，避免显示其他类型机构\n      onlyCategory: {\n        type: Number,\n        default: 3 // 假设3是班级类型\n      }\n    },\n    data(){\n      return {\n        visible: false,\n        loading: false,\n        treeData: [],\n        autoExpandParent: true,\n        expandedKeys: [],\n        dataList: [],\n        checkedKeys: [],\n        checkedRows: [],\n        searchValue: \"\"\n      }\n    },\n    created(){\n      this.loadClasses();\n    },\n    watch:{\n      departId(){\n        this.initClassComponent()\n      },\n      visible: {\n        handler(val) {\n          if (this.departId) {\n            this.checkedKeys = this.departId.split(\",\");\n          } else {\n            this.checkedKeys = [];\n          }\n          // 修改：当模态框显示时，重新加载班级数据\n          if (val === true) {\n            this.loadClasses();\n          }\n        }\n      }\n    },\n    methods:{\n      show(){\n        this.visible = true\n        this.checkedRows = []\n        this.checkedKeys = []\n        this.searchValue = ''\n        this.loadClasses()\n      },\n      loadClasses(){\n        this.loading = true\n        // 注释：恢复使用queryMyDepartTreeList()方法，确保教师角色只能看到自己关联的班级\n        queryMyDepartTreeList().then(res=>{\n          this.loading = false\n          if(res.success){\n            let arr = [...res.result]\n            // 清空之前的数据\n            this.dataList = []\n            this.reWriterWithSlot(arr)\n            this.treeData = arr\n            this.initClassComponent()\n            if(this.onlyLeaf || this.onlyCategory){\n              this.disableNoneChildNode(this.treeData)\n            }\n            if(this.rootOpened){\n              this.initExpandedKeys(res.result)\n            }\n            console.log('班级树数据:', this.treeData)\n            console.log('扁平化数据列表:', this.dataList)\n          }\n        }).catch(() => {\n          this.loading = false\n        })\n      },\n      // 递归禁用节点\n      disableNoneChildNode(treeData){\n        for(var i=0; i<treeData.length; i++){\n          if(this.onlyCategory!=null){\n            if(treeData[i].orgCategory==this.onlyCategory){\n              treeData[i].disabled = false\n            }else{\n              treeData[i].disabled = true\n            }\n          }\n          if(treeData[i].isLeaf){\n            // treeData[i].disabled = false\n          }else{\n            if(this.onlyLeaf){\n              treeData[i].disabled = true\n            }\n            this.disableNoneChildNode(treeData[i].children)\n          }\n        }\n      },\n      initClassComponent(){\n        let names = ''\n        if(this.departId){\n          let currDepartId = this.departId\n          for(let item of this.dataList){\n            if(currDepartId.indexOf(item.key)>=0){\n              names+=\",\"+item.title\n            }\n          }\n          if(names){\n            names = names.substring(1)\n          }\n        }\n        this.$emit(\"initComp\",names)\n      },\n      reWriterWithSlot(arr){\n        for(let item of arr){\n          // 为每个节点添加scopedSlots，确保能正确渲染标题\n          item.scopedSlots = { title: 'title' }\n          \n          // 添加到扁平化列表中\n          this.dataList.push(item)\n          \n          // 递归处理子节点\n          if(item.children && item.children.length>0){\n            this.reWriterWithSlot(item.children)\n          }\n        }\n      },\n      initExpandedKeys(arr){\n        if(arr && arr.length>0){\n          let keys = []\n          for(let item of arr){\n            if(item.children && item.children.length>0){\n              keys.push(item.id)\n            }\n          }\n          this.expandedKeys=[...keys]\n        }else{\n          this.expandedKeys=[]\n        }\n      },\n      onCheck (checkedKeys,info) {\n        if(!this.multi){\n          let arr = checkedKeys.checked.filter(item => this.checkedKeys.indexOf(item) < 0)\n          this.checkedKeys = [...arr]\n          this.checkedRows = (this.checkedKeys.length === 0) ? [] : [info.node.dataRef]\n        }else{\n          this.checkedKeys = checkedKeys.checked\n          this.checkedRows = this.getCheckedRows(this.checkedKeys)\n        }\n      },\n      onSelect(selectedKeys,info) {\n        let keys = []\n        keys.push(selectedKeys[0])\n        if(!this.checkedKeys || this.checkedKeys.length===0 || !this.multi){\n          this.checkedKeys = [...keys]\n          this.checkedRows=[info.node.dataRef]\n        }else{\n          let currKey = info.node.dataRef.key\n          if(this.checkedKeys.indexOf(currKey)>=0){\n            this.checkedKeys = this.checkedKeys.filter(item=> item !==currKey)\n          }else{\n            this.checkedKeys.push(...keys)\n          }\n        }\n        this.checkedRows = this.getCheckedRows(this.checkedKeys)\n      },\n      onExpand (expandedKeys) {\n        this.expandedKeys = expandedKeys\n        this.autoExpandParent = false\n      },\n      handleSubmit(){\n        if(!this.checkedKeys || this.checkedKeys.length==0){\n          this.$emit(\"ok\",'')\n        }else{\n          this.$emit(\"ok\",this.checkedRows,this.checkedKeys.join(\",\"))\n        }\n        this.handleClear()\n      },\n      handleCancel(){\n        this.handleClear()\n      },\n      handleClear(){\n        this.visible=false\n      },\n      getParentKey(currKey,treeData){\n        let parentKey\n        for (let i = 0; i < treeData.length; i++) {\n          const node = treeData[i]\n          if (node.children) {\n            if (node.children.some(item => item.key === currKey)) {\n              parentKey = node.key\n            } else if (this.getParentKey(currKey, node.children)) {\n              parentKey = this.getParentKey(currKey, node.children)\n            }\n          }\n        }\n        return parentKey\n      },\n      onSearch(value){\n        // 重置搜索状态\n        if (!value) {\n          this.searchValue = ''\n          // 如果rootOpened为true，保持原有展开状态，否则折叠所有节点\n          if (!this.rootOpened) {\n            this.expandedKeys = []\n          }\n          this.autoExpandParent = false\n          return\n        }\n        \n        // 转换为小写进行不区分大小写搜索\n        const lowerValue = value.toLowerCase()\n        \n        // 存储匹配的节点和需要展开的父节点\n        let expandedKeys = []\n        let matchedKeys = []\n        \n        // 遍历所有节点查找匹配项\n        for (let item of this.dataList) {\n          if (item.title && item.title.toLowerCase().indexOf(lowerValue) > -1) {\n            // 找到匹配的节点\n            matchedKeys.push(item.key)\n            \n            // 查找并添加所有父节点到展开列表\n            let parentKey = this.getParentKey(item.key, this.treeData)\n            while (parentKey) {\n              expandedKeys.push(parentKey)\n              parentKey = this.getParentKey(parentKey, this.treeData)\n            }\n          }\n        }\n        \n        // 合并展开的键并去重\n        expandedKeys = [...new Set([...expandedKeys, ...matchedKeys])]\n        \n        // 更新组件状态\n        this.expandedKeys = expandedKeys\n        this.searchValue = value\n        this.autoExpandParent = true\n        \n        console.log('搜索值:', value)\n        console.log('展开的节点:', expandedKeys)\n      },\n      // 根据 checkedKeys 获取 rows\n      getCheckedRows(checkedKeys) {\n        const forChildren = (list, key) => {\n          for (let item of list) {\n            if (item.id === key) {\n              return item\n            }\n            if (item.children instanceof Array) {\n              let value = forChildren(item.children, key)\n              if (value != null) {\n                return value\n              }\n            }\n          }\n          return null\n        }\n\n        let rows = []\n        for (let key of checkedKeys) {\n          let row = forChildren(this.treeData, key)\n          if (row != null) {\n            rows.push(row)\n          }\n        }\n        return rows\n      }\n    }\n  }\n\n</script>\n\n<style lang=\"less\" scoped>\n  // 限制班级选择树高度，避免班级太多时点击确定不便\n  .my-class-select-tree{\n    height: 350px;\n    overflow-y: scroll;\n  }\n\n</style> "], "mappings": ";;;;;;;AAuCA,SAAAA,mBAAA,EAAAC,qBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAQ;IACA;IACA;IACAC,YAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,QAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,KAAA;IACAf,QAAA,WAAAA,SAAA;MACA,KAAAgB,kBAAA;IACA;IACAZ,OAAA;MACAa,OAAA,WAAAA,QAAAC,GAAA;QACA,SAAAlB,QAAA;UACA,KAAAU,WAAA,QAAAV,QAAA,CAAAmB,KAAA;QACA;UACA,KAAAT,WAAA;QACA;QACA;QACA,IAAAQ,GAAA;UACA,KAAAJ,WAAA;QACA;MACA;IACA;EACA;EACAM,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAjB,OAAA;MACA,KAAAO,WAAA;MACA,KAAAD,WAAA;MACA,KAAAE,WAAA;MACA,KAAAE,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAQ,KAAA;MACA,KAAAjB,OAAA;MACA;MACAhB,qBAAA,GAAAkC,IAAA,WAAAC,GAAA;QACAF,KAAA,CAAAjB,OAAA;QACA,IAAAmB,GAAA,CAAAC,OAAA;UACA,IAAAC,GAAA,GAAAC,kBAAA,CAAAH,GAAA,CAAAI,MAAA;UACA;UACAN,KAAA,CAAAb,QAAA;UACAa,KAAA,CAAAO,gBAAA,CAAAH,GAAA;UACAJ,KAAA,CAAAhB,QAAA,GAAAoB,GAAA;UACAJ,KAAA,CAAAN,kBAAA;UACA,IAAAM,KAAA,CAAAvB,QAAA,IAAAuB,KAAA,CAAApB,YAAA;YACAoB,KAAA,CAAAQ,oBAAA,CAAAR,KAAA,CAAAhB,QAAA;UACA;UACA,IAAAgB,KAAA,CAAAxB,UAAA;YACAwB,KAAA,CAAAS,gBAAA,CAAAP,GAAA,CAAAI,MAAA;UACA;UACAI,OAAA,CAAAC,GAAA,WAAAX,KAAA,CAAAhB,QAAA;UACA0B,OAAA,CAAAC,GAAA,aAAAX,KAAA,CAAAb,QAAA;QACA;MACA,GAAAyB,KAAA;QACAZ,KAAA,CAAAjB,OAAA;MACA;IACA;IACA;IACAyB,oBAAA,WAAAA,qBAAAxB,QAAA;MACA,SAAA6B,CAAA,MAAAA,CAAA,GAAA7B,QAAA,CAAA8B,MAAA,EAAAD,CAAA;QACA,SAAAjC,YAAA;UACA,IAAAI,QAAA,CAAA6B,CAAA,EAAAE,WAAA,SAAAnC,YAAA;YACAI,QAAA,CAAA6B,CAAA,EAAAG,QAAA;UACA;YACAhC,QAAA,CAAA6B,CAAA,EAAAG,QAAA;UACA;QACA;QACA,IAAAhC,QAAA,CAAA6B,CAAA,EAAAI,MAAA;UACA;QAAA,CACA;UACA,SAAAxC,QAAA;YACAO,QAAA,CAAA6B,CAAA,EAAAG,QAAA;UACA;UACA,KAAAR,oBAAA,CAAAxB,QAAA,CAAA6B,CAAA,EAAAK,QAAA;QACA;MACA;IACA;IACAxB,kBAAA,WAAAA,mBAAA;MACA,IAAAyB,KAAA;MACA,SAAAzC,QAAA;QACA,IAAA0C,YAAA,QAAA1C,QAAA;QAAA,IAAA2C,SAAA,GAAAC,0BAAA,CACA,KAAAnC,QAAA;UAAAoC,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;YACA,IAAAR,YAAA,CAAAS,OAAA,CAAAF,IAAA,CAAAG,GAAA;cACAX,KAAA,UAAAQ,IAAA,CAAAI,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;QAAA;UAAAX,SAAA,CAAAa,CAAA;QAAA;QACA,IAAAf,KAAA;UACAA,KAAA,GAAAA,KAAA,CAAAgB,SAAA;QACA;MACA;MACA,KAAAC,KAAA,aAAAjB,KAAA;IACA;IACAZ,gBAAA,WAAAA,iBAAAH,GAAA;MAAA,IAAAiC,UAAA,GAAAf,0BAAA,CACAlB,GAAA;QAAAkC,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAb,CAAA,MAAAc,MAAA,GAAAD,UAAA,CAAAZ,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAW,MAAA,CAAAV,KAAA;UACA;UACAD,IAAA,CAAAY,WAAA;YAAAR,KAAA;UAAA;;UAEA;UACA,KAAA5C,QAAA,CAAAqD,IAAA,CAAAb,IAAA;;UAEA;UACA,IAAAA,IAAA,CAAAT,QAAA,IAAAS,IAAA,CAAAT,QAAA,CAAAJ,MAAA;YACA,KAAAP,gBAAA,CAAAoB,IAAA,CAAAT,QAAA;UACA;QACA;MAAA,SAAAc,GAAA;QAAAK,UAAA,CAAAJ,CAAA,CAAAD,GAAA;MAAA;QAAAK,UAAA,CAAAH,CAAA;MAAA;IACA;IACAzB,gBAAA,WAAAA,iBAAAL,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAAU,MAAA;QACA,IAAA2B,IAAA;QAAA,IAAAC,UAAA,GAAApB,0BAAA,CACAlB,GAAA;UAAAuC,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAlB,CAAA,MAAAmB,MAAA,GAAAD,UAAA,CAAAjB,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAgB,MAAA,CAAAf,KAAA;YACA,IAAAD,IAAA,CAAAT,QAAA,IAAAS,IAAA,CAAAT,QAAA,CAAAJ,MAAA;cACA2B,IAAA,CAAAD,IAAA,CAAAb,IAAA,CAAAiB,EAAA;YACA;UACA;QAAA,SAAAZ,GAAA;UAAAU,UAAA,CAAAT,CAAA,CAAAD,GAAA;QAAA;UAAAU,UAAA,CAAAR,CAAA;QAAA;QACA,KAAAhD,YAAA,MAAA2D,MAAA,CAAAJ,IAAA;MACA;QACA,KAAAvD,YAAA;MACA;IACA;IACA4D,OAAA,WAAAA,QAAA1D,WAAA,EAAA2D,IAAA;MAAA,IAAAC,MAAA;MACA,UAAA1E,KAAA;QACA,IAAA8B,GAAA,GAAAhB,WAAA,CAAA6D,OAAA,CAAAC,MAAA,WAAAvB,IAAA;UAAA,OAAAqB,MAAA,CAAA5D,WAAA,CAAAyC,OAAA,CAAAF,IAAA;QAAA;QACA,KAAAvC,WAAA,GAAAiB,kBAAA,CAAAD,GAAA;QACA,KAAAf,WAAA,QAAAD,WAAA,CAAA0B,MAAA,eAAAiC,IAAA,CAAAI,IAAA,CAAAC,OAAA;MACA;QACA,KAAAhE,WAAA,GAAAA,WAAA,CAAA6D,OAAA;QACA,KAAA5D,WAAA,QAAAgE,cAAA,MAAAjE,WAAA;MACA;IACA;IACAkE,QAAA,WAAAA,SAAAC,YAAA,EAAAR,IAAA;MACA,IAAAN,IAAA;MACAA,IAAA,CAAAD,IAAA,CAAAe,YAAA;MACA,UAAAnE,WAAA,SAAAA,WAAA,CAAA0B,MAAA,gBAAAxC,KAAA;QACA,KAAAc,WAAA,MAAAyD,MAAA,CAAAJ,IAAA;QACA,KAAApD,WAAA,IAAA0D,IAAA,CAAAI,IAAA,CAAAC,OAAA;MACA;QACA,IAAAI,OAAA,GAAAT,IAAA,CAAAI,IAAA,CAAAC,OAAA,CAAAtB,GAAA;QACA,SAAA1C,WAAA,CAAAyC,OAAA,CAAA2B,OAAA;UACA,KAAApE,WAAA,QAAAA,WAAA,CAAA8D,MAAA,WAAAvB,IAAA;YAAA,OAAAA,IAAA,KAAA6B,OAAA;UAAA;QACA;UAAA,IAAAC,iBAAA;UACA,CAAAA,iBAAA,QAAArE,WAAA,EAAAoD,IAAA,CAAAkB,KAAA,CAAAD,iBAAA,EAAAhB,IAAA;QACA;MACA;MACA,KAAApD,WAAA,QAAAgE,cAAA,MAAAjE,WAAA;IACA;IACAuE,QAAA,WAAAA,SAAAzE,YAAA;MACA,KAAAA,YAAA,GAAAA,YAAA;MACA,KAAAD,gBAAA;IACA;IACA2E,YAAA,WAAAA,aAAA;MACA,UAAAxE,WAAA,SAAAA,WAAA,CAAA0B,MAAA;QACA,KAAAsB,KAAA;MACA;QACA,KAAAA,KAAA,YAAA/C,WAAA,OAAAD,WAAA,CAAAyE,IAAA;MACA;MACA,KAAAC,WAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAD,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAhF,OAAA;IACA;IACAkF,YAAA,WAAAA,aAAAR,OAAA,EAAAxE,QAAA;MACA,IAAAiF,SAAA;MACA,SAAApD,CAAA,MAAAA,CAAA,GAAA7B,QAAA,CAAA8B,MAAA,EAAAD,CAAA;QACA,IAAAsC,IAAA,GAAAnE,QAAA,CAAA6B,CAAA;QACA,IAAAsC,IAAA,CAAAjC,QAAA;UACA,IAAAiC,IAAA,CAAAjC,QAAA,CAAAgD,IAAA,WAAAvC,IAAA;YAAA,OAAAA,IAAA,CAAAG,GAAA,KAAA0B,OAAA;UAAA;YACAS,SAAA,GAAAd,IAAA,CAAArB,GAAA;UACA,gBAAAkC,YAAA,CAAAR,OAAA,EAAAL,IAAA,CAAAjC,QAAA;YACA+C,SAAA,QAAAD,YAAA,CAAAR,OAAA,EAAAL,IAAA,CAAAjC,QAAA;UACA;QACA;MACA;MACA,OAAA+C,SAAA;IACA;IACAE,QAAA,WAAAA,SAAAvC,KAAA;MACA;MACA,KAAAA,KAAA;QACA,KAAAtC,WAAA;QACA;QACA,UAAAd,UAAA;UACA,KAAAU,YAAA;QACA;QACA,KAAAD,gBAAA;QACA;MACA;;MAEA;MACA,IAAAmF,UAAA,GAAAxC,KAAA,CAAAyC,WAAA;;MAEA;MACA,IAAAnF,YAAA;MACA,IAAAoF,WAAA;;MAEA;MAAA,IAAAC,UAAA,GAAAjD,0BAAA,CACA,KAAAnC,QAAA;QAAAqF,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA/C,CAAA,MAAAgD,MAAA,GAAAD,UAAA,CAAA9C,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAA6C,MAAA,CAAA5C,KAAA;UACA,IAAAD,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAAI,KAAA,CAAAsC,WAAA,GAAAxC,OAAA,CAAAuC,UAAA;YACA;YACAE,WAAA,CAAA9B,IAAA,CAAAb,IAAA,CAAAG,GAAA;;YAEA;YACA,IAAAmC,SAAA,QAAAD,YAAA,CAAArC,IAAA,CAAAG,GAAA,OAAA9C,QAAA;YACA,OAAAiF,SAAA;cACA/E,YAAA,CAAAsD,IAAA,CAAAyB,SAAA;cACAA,SAAA,QAAAD,YAAA,CAAAC,SAAA,OAAAjF,QAAA;YACA;UACA;QACA;;QAEA;MAAA,SAAAgD,GAAA;QAAAuC,UAAA,CAAAtC,CAAA,CAAAD,GAAA;MAAA;QAAAuC,UAAA,CAAArC,CAAA;MAAA;MACAhD,YAAA,GAAAmB,kBAAA,KAAAoE,GAAA,IAAA5B,MAAA,CAAAxC,kBAAA,CAAAnB,YAAA,GAAAoF,WAAA;;MAEA;MACA,KAAApF,YAAA,GAAAA,YAAA;MACA,KAAAI,WAAA,GAAAsC,KAAA;MACA,KAAA3C,gBAAA;MAEAyB,OAAA,CAAAC,GAAA,SAAAiB,KAAA;MACAlB,OAAA,CAAAC,GAAA,WAAAzB,YAAA;IACA;IACA;IACAmE,cAAA,WAAAA,eAAAjE,WAAA;MACA,IAAAsF,WAAA,YAAAA,YAAAC,IAAA,EAAA7C,GAAA;QAAA,IAAA8C,UAAA,GAAAtD,0BAAA,CACAqD,IAAA;UAAAE,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAApD,CAAA,MAAAqD,MAAA,GAAAD,UAAA,CAAAnD,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAkD,MAAA,CAAAjD,KAAA;YACA,IAAAD,IAAA,CAAAiB,EAAA,KAAAd,GAAA;cACA,OAAAH,IAAA;YACA;YACA,IAAAA,IAAA,CAAAT,QAAA,YAAA4D,KAAA;cACA,IAAAlD,KAAA,GAAA8C,WAAA,CAAA/C,IAAA,CAAAT,QAAA,EAAAY,GAAA;cACA,IAAAF,KAAA;gBACA,OAAAA,KAAA;cACA;YACA;UACA;QAAA,SAAAI,GAAA;UAAA4C,UAAA,CAAA3C,CAAA,CAAAD,GAAA;QAAA;UAAA4C,UAAA,CAAA1C,CAAA;QAAA;QACA;MACA;MAEA,IAAA6C,IAAA;MAAA,IAAAC,UAAA,GAAA1D,0BAAA,CACAlC,WAAA;QAAA6F,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxD,CAAA,MAAAyD,MAAA,GAAAD,UAAA,CAAAvD,CAAA,IAAAC,IAAA;UAAA,IAAAI,GAAA,GAAAmD,MAAA,CAAArD,KAAA;UACA,IAAAsD,GAAA,GAAAR,WAAA,MAAA1F,QAAA,EAAA8C,GAAA;UACA,IAAAoD,GAAA;YACAH,IAAA,CAAAvC,IAAA,CAAA0C,GAAA;UACA;QACA;MAAA,SAAAlD,GAAA;QAAAgD,UAAA,CAAA/C,CAAA,CAAAD,GAAA;MAAA;QAAAgD,UAAA,CAAA9C,CAAA;MAAA;MACA,OAAA6C,IAAA;IACA;EACA;AACA", "ignoreList": []}]}