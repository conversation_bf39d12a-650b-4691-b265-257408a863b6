{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue?vue&type=template&id=7d6a815f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div v-if=\"!reloading\" class=\"j-area-linkage\">\n  <area-cascader\n    v-if=\"_type === enums.type[0]\"\n    :value=\"innerValue\"\n    :data=\"pcaa\"\n    :level=\"1\"\n    :style=\"{width}\"\n    v-bind=\"$attrs\"\n    v-on=\"_listeners\"\n    @change=\"handleChange\"\n  />\n  <area-select\n    v-else-if=\"_type === enums.type[1]\"\n    :value=\"innerValue\"\n    :data=\"pcaa\"\n    :level=\"2\"\n    v-bind=\"$attrs\"\n    v-on=\"_listeners\"\n    @change=\"handleChange\"\n  />\n  <div v-else>\n    <span style=\"color:red;\"> Bad type value: {{_type}}</span>\n  </div>\n</div>\n", null]}