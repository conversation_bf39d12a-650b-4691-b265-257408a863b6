{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JCheckbox.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  export default {\n    name: 'JCheckbox',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      /*label value*/\n      options:{\n        type: Array,\n        required: true\n      }\n    },\n    data(){\n      return {\n        checkboxArray:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.checkboxArray = []\n        }else{\n          this.checkboxArray = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (checkedValues) {\n        this.$emit('change', checkedValues.join(\",\"));\n      },\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n", {"version": 3, "sources": ["JCheckbox.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JCheckbox.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-checkbox-group :options=\"options\" :value=\"checkboxArray\" v-bind=\"$attrs\" @change=\"onChange\" />\n</template>\n\n<script>\n  export default {\n    name: 'JCheckbox',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      /*label value*/\n      options:{\n        type: Array,\n        required: true\n      }\n    },\n    data(){\n      return {\n        checkboxArray:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.checkboxArray = []\n        }else{\n          this.checkboxArray = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (checkedValues) {\n        this.$emit('change', checkedValues.join(\",\"));\n      },\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n</script>\n"]}]}