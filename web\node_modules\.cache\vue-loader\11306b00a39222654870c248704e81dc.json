{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue?vue&type=template&id=2f95bc45&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"antv-chart-mini\">\n  <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\n    <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :padding=\"[36, 0, 18, 0]\">\n      <v-tooltip/>\n      <v-smooth-area position=\"x*y\"/>\n    </v-chart>\n  </div>\n</div>\n", null]}