{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import { duplicateCheck } from '@/api/api'\n  import JEditor from '@/components/jeecg/JEditor'\n\n  export default {\n    name: \"SysMessageTemplateModal\",\n    components:{\n      JEditor\n    },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        disable: true,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {\n        templateCode: {rules: [{required: true, message: '请输入模板CODE!' },{validator: this.validateTemplateCode}]},\n        templateName: {rules: [{required: true, message: '请输入模板标题!'}]},\n        templateContent: {rules: []},\n        templateType: {rules: [{required: true, message: '请输入模板类型!'}]},\n        },\n        url: {\n          add: \"/message/sysMessageTemplate/add\",\n          edit: \"/message/sysMessageTemplate/edit\",\n        },\n        useEditor:false,\n        templateEditorContent:\"\"\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.disable = false;\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.useEditor = (record.templateType==2 || record.templateType==4)\n        if(this.useEditor){\n          this.templateEditorContent=record.templateContent\n        }else{\n          this.templateEditorContent=''\n        }\n        this.visible = true;\n        this.$nextTick(() => {\n          if(this.useEditor){\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateName', 'templateTestJson', 'templateType'))\n          }else{\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateContent', 'templateName', 'templateTestJson', 'templateType'))\n          }\n        });\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n        this.disable = true;\n      },\n      handleOk() {\n        this.model.templateType = this.templateType;\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n\n            if(this.useEditor){\n              formData.templateContent=this.templateEditorContent\n            }\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      validateTemplateCode(rule, value, callback){\n        var params = {\n          tableName: \"sys_sms_template\",\n          fieldName: \"template_code\",\n          fieldVal: value,\n          dataId: this.model.id\n        }\n        duplicateCheck(params).then((res)=>{\n          if(res.success){\n            callback();\n          }else{\n            callback(res.message);\n          }\n        })\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleChangeTemplateType(value){\n        //如果是邮件类型那么则改变模板内容是富文本编辑器\n        this.useEditor = (value==2 || value==4)\n      }\n\n    }\n  }\n", {"version": 3, "sources": ["SysMessageTemplateModal.vue"], "names": [], "mappings": ";AAmFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "SysMessageTemplateModal.vue", "sourceRoot": "src/views/modules/message/modules", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <a-row :gutter=\"{ xs: 8, sm: 16, md: 24, lg: 32 }\">\n          <a-col :span=\"12\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板CODE\"\n              style=\"margin-right: -35px\"\n            >\n              <a-input\n                :disabled=\"disable\"\n                placeholder=\"请输入模板编码\"\n                v-decorator=\"['templateCode', validatorRules.templateCode ]\"\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板类型\">\n              <j-dict-select-tag  @change=\"handleChangeTemplateType\" :triggerChange=\"true\" dictCode=\"msgType\" v-decorator=\"['templateType', validatorRules.templateType ]\" placeholder=\"请选择模板类型\">\n              </j-dict-select-tag>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\" >\n          <a-col :span=\"24\" pull=\"2\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板标题\"\n              style=\"margin-left: -15px\">\n              <a-input\n                placeholder=\"请输入模板标题\"\n                v-decorator=\"['templateName', validatorRules.templateName]\"\n                style=\"width: 122%\"\n              />\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\">\n          <a-col :span=\"24\" pull=\"4\">\n            <a-form-item\n              v-show=\"!useEditor\"\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板内容\"\n              style=\"margin-left: 4px;width: 126%\">\n              <a-textarea placeholder=\"请输入模板内容\" v-decorator=\"['templateContent', validatorRules.templateContent ]\" :autosize=\"{ minRows: 8, maxRows: 8 }\"/>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\">\n          <a-col :span=\"24\" pull=\"4\">\n            <a-form-item\n              v-show=\"useEditor\"\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板内容\"\n              style=\"margin-left: 4px;width: 126%\">\n              <j-editor  v-model=\"templateEditorContent\"></j-editor>\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import { duplicateCheck } from '@/api/api'\n  import JEditor from '@/components/jeecg/JEditor'\n\n  export default {\n    name: \"SysMessageTemplateModal\",\n    components:{\n      JEditor\n    },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        disable: true,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {\n        templateCode: {rules: [{required: true, message: '请输入模板CODE!' },{validator: this.validateTemplateCode}]},\n        templateName: {rules: [{required: true, message: '请输入模板标题!'}]},\n        templateContent: {rules: []},\n        templateType: {rules: [{required: true, message: '请输入模板类型!'}]},\n        },\n        url: {\n          add: \"/message/sysMessageTemplate/add\",\n          edit: \"/message/sysMessageTemplate/edit\",\n        },\n        useEditor:false,\n        templateEditorContent:\"\"\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.disable = false;\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.useEditor = (record.templateType==2 || record.templateType==4)\n        if(this.useEditor){\n          this.templateEditorContent=record.templateContent\n        }else{\n          this.templateEditorContent=''\n        }\n        this.visible = true;\n        this.$nextTick(() => {\n          if(this.useEditor){\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateName', 'templateTestJson', 'templateType'))\n          }else{\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateContent', 'templateName', 'templateTestJson', 'templateType'))\n          }\n        });\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n        this.disable = true;\n      },\n      handleOk() {\n        this.model.templateType = this.templateType;\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n\n            if(this.useEditor){\n              formData.templateContent=this.templateEditorContent\n            }\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      validateTemplateCode(rule, value, callback){\n        var params = {\n          tableName: \"sys_sms_template\",\n          fieldName: \"template_code\",\n          fieldVal: value,\n          dataId: this.model.id\n        }\n        duplicateCheck(params).then((res)=>{\n          if(res.success){\n            callback();\n          }else{\n            callback(res.message);\n          }\n        })\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleChangeTemplateType(value){\n        //如果是邮件类型那么则改变模板内容是富文本编辑器\n        this.useEditor = (value==2 || value==4)\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}