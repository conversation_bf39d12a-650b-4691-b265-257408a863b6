{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue?vue&type=template&id=b8ab2606&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue", "mtime": 1749714201887}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"software-download-modal\"\n  }, [_c(\"a-modal\", {\n    attrs: {\n      title: \"软件下载\",\n      visible: _vm.visible,\n      footer: null,\n      width: \"800px\",\n      maskClosable: true\n    },\n    on: {\n      cancel: _vm.handleCancel\n    }\n  }, [_c(\"div\", {\n    staticClass: \"software-list\"\n  }, _vm._l(_vm.softwareList, function (item, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"software-item\"\n    }, [_c(\"div\", {\n      staticClass: \"software-item-content\"\n    }, [_c(\"img\", {\n      staticClass: \"software-icon\",\n      attrs: {\n        src: item.imgUrl,\n        alt: \"软件图标\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"software-info\"\n    }, [_c(\"h3\", [_vm._v(_vm._s(item.name))]), _c(\"p\", [_vm._v(_vm._s(item.description))]), _c(\"div\", {\n      staticClass: \"software-button\"\n    }, [_c(\"a-button\", {\n      attrs: {\n        type: \"primary\",\n        icon: \"download\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.downloadSoftware(item);\n        }\n      }\n    }, [_vm._v(\"立即下载\")]), _c(\"span\", {\n      staticClass: \"version-info\"\n    }, [_vm._v(\"版本: \" + _vm._s(item.version))]), _c(\"span\", {\n      staticClass: \"size-info\"\n    }, [_vm._v(\"大小: \" + _vm._s(item.size))])], 1)])])]);\n  }), 0), _c(\"div\", {\n    staticClass: \"download-tips\"\n  }, [_c(\"a-alert\", {\n    attrs: {\n      type: \"info\",\n      \"show-icon\": \"\"\n    }\n  }, [_c(\"span\", {\n    attrs: {\n      slot: \"message\"\n    },\n    slot: \"message\"\n  }, [_vm._v('\\n          点击\"立即下载\"按钮即可开始下载，软件均为官方原版，请放心使用。\\n        ')])])], 1)])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "visible", "footer", "width", "maskClosable", "on", "cancel", "handleCancel", "_l", "softwareList", "item", "index", "key", "src", "imgUrl", "alt", "_v", "_s", "name", "description", "type", "icon", "click", "$event", "downloadSoftware", "version", "size", "slot", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/SoftwareDownload.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"software-download-modal\" },\n    [\n      _c(\n        \"a-modal\",\n        {\n          attrs: {\n            title: \"软件下载\",\n            visible: _vm.visible,\n            footer: null,\n            width: \"800px\",\n            maskClosable: true,\n          },\n          on: { cancel: _vm.handleCancel },\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"software-list\" },\n            _vm._l(_vm.softwareList, function (item, index) {\n              return _c(\"div\", { key: index, staticClass: \"software-item\" }, [\n                _c(\"div\", { staticClass: \"software-item-content\" }, [\n                  _c(\"img\", {\n                    staticClass: \"software-icon\",\n                    attrs: { src: item.imgUrl, alt: \"软件图标\" },\n                  }),\n                  _c(\"div\", { staticClass: \"software-info\" }, [\n                    _c(\"h3\", [_vm._v(_vm._s(item.name))]),\n                    _c(\"p\", [_vm._v(_vm._s(item.description))]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"software-button\" },\n                      [\n                        _c(\n                          \"a-button\",\n                          {\n                            attrs: { type: \"primary\", icon: \"download\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.downloadSoftware(item)\n                              },\n                            },\n                          },\n                          [_vm._v(\"立即下载\")]\n                        ),\n                        _c(\"span\", { staticClass: \"version-info\" }, [\n                          _vm._v(\"版本: \" + _vm._s(item.version)),\n                        ]),\n                        _c(\"span\", { staticClass: \"size-info\" }, [\n                          _vm._v(\"大小: \" + _vm._s(item.size)),\n                        ]),\n                      ],\n                      1\n                    ),\n                  ]),\n                ]),\n              ])\n            }),\n            0\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"download-tips\" },\n            [\n              _c(\"a-alert\", { attrs: { type: \"info\", \"show-icon\": \"\" } }, [\n                _c(\"span\", { attrs: { slot: \"message\" }, slot: \"message\" }, [\n                  _vm._v(\n                    '\\n          点击\"立即下载\"按钮即可开始下载，软件均为官方原版，请放心使用。\\n        '\n                  ),\n                ]),\n              ]),\n            ],\n            1\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAC1C,CACEF,EAAE,CACA,SAAS,EACT;IACEG,KAAK,EAAE;MACLC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAEN,GAAG,CAACM,OAAO;MACpBC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE;IAChB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEX,GAAG,CAACY;IAAa;EACjC,CAAC,EACD,CACEX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChCH,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,YAAY,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOf,EAAE,CAAC,KAAK,EAAE;MAAEgB,GAAG,EAAED,KAAK;MAAEb,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC7DF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAwB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,eAAe;MAC5BC,KAAK,EAAE;QAAEc,GAAG,EAAEH,IAAI,CAACI,MAAM;QAAEC,GAAG,EAAE;MAAO;IACzC,CAAC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACsB,EAAE,CAACP,IAAI,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,EACrCtB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACsB,EAAE,CAACP,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,CAAC,EAC3CvB,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEF,EAAE,CACA,UAAU,EACV;MACEG,KAAK,EAAE;QAAEqB,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC;MAC5ChB,EAAE,EAAE;QACFiB,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAO5B,GAAG,CAAC6B,gBAAgB,CAACd,IAAI,CAAC;QACnC;MACF;IACF,CAAC,EACD,CAACf,GAAG,CAACqB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDpB,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CAC1CH,GAAG,CAACqB,EAAE,CAAC,MAAM,GAAGrB,GAAG,CAACsB,EAAE,CAACP,IAAI,CAACe,OAAO,CAAC,CAAC,CACtC,CAAC,EACF7B,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACvCH,GAAG,CAACqB,EAAE,CAAC,MAAM,GAAGrB,GAAG,CAACsB,EAAE,CAACP,IAAI,CAACgB,IAAI,CAAC,CAAC,CACnC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACD9B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,SAAS,EAAE;IAAEG,KAAK,EAAE;MAAEqB,IAAI,EAAE,MAAM;MAAE,WAAW,EAAE;IAAG;EAAE,CAAC,EAAE,CAC1DxB,EAAE,CAAC,MAAM,EAAE;IAAEG,KAAK,EAAE;MAAE4B,IAAI,EAAE;IAAU,CAAC;IAAEA,IAAI,EAAE;EAAU,CAAC,EAAE,CAC1DhC,GAAG,CAACqB,EAAE,CACJ,wDACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIY,eAAe,GAAG,EAAE;AACxBlC,MAAM,CAACmC,aAAa,GAAG,IAAI;AAE3B,SAASnC,MAAM,EAAEkC,eAAe", "ignoreList": []}]}