{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\exception\\500.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\exception\\500.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import ExceptionPage from './ExceptionPage'\n\n  export default {\n    components: {\n      ExceptionPage\n    }\n  }\n", {"version": 3, "sources": ["500.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA", "file": "500.vue", "sourceRoot": "src/views/exception", "sourcesContent": ["<template>\n  <exception-page type=\"500\" />\n</template>\n\n<script>\n  import ExceptionPage from './ExceptionPage'\n\n  export default {\n    components: {\n      ExceptionPage\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}