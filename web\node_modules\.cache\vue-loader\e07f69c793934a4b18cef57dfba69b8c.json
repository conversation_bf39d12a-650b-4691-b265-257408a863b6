{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\DefaultTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\DefaultTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import moment from 'moment'\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import { randomUUID, randomNumber } from '@/utils/util'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'DefaultTable',\n    components: { JEditableTable },\n    data() {\n      return {\n        loading: false,\n        columns: [\n          {\n            title: '字段名称',\n            key: 'dbFieldName',\n            // width: '19%',\n            width: '300px',\n            type: FormTypes.input,\n            defaultValue: '',\n            placeholder: '请输入${title}',\n            validateRules: [\n              {\n                required: true, // 必填\n                message: '请输入${title}' // 显示的文本\n              },\n              {\n                pattern: /^[a-z|A-Z][a-z|A-Z\\d_-]{0,}$/, // 正则\n                message: '${title}必须以字母开头，可包含数字、下划线、横杠'\n              },\n              {\n                unique: true,\n                message: '${title}不能重复'\n              },\n              {\n                handler(type, value, row, column, callback, target) {\n                  // type 触发校验的类型（input、change、blur）\n                  // value 当前校验的值\n                  // callback(flag, message) 方法必须执行且只能执行一次\n                  //          flag = 是否通过了校验，不填写或者填写 null 代表不进行任何操作\n                  //          message = 提示的类型，默认使用配置的 message\n                  // target 行编辑的实例对象\n\n                  if (type === 'blur') {\n                    if (value === 'abc') {\n                      callback(false, '${title}不能是abc')  // false = 未通过校验\n                    } else {\n                      callback(true) // true = 通过验证\n                    }\n                  } else {\n                    callback(true) // 不填写或者填写 null 代表不进行任何操作\n                  }\n                },\n                message: '${title}默认提示'\n              }\n            ]\n          },\n          {\n            title: '文件域',\n            key: 'upload',\n            type: FormTypes.upload,\n            // width: '19%',\n            width: '300px',\n            placeholder: '点击上传',\n            token: true,\n            responseName: 'message',\n            action: window._CONFIG['domianURL'] + '/sys/common/upload'\n          },\n          {\n            title: '字段类型',\n            key: 'dbFieldType',\n            // width: '18%',\n            width: '300px',\n            type: FormTypes.select,\n            options: [ // 下拉选项\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            allowInput: true,\n            defaultValue: '',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '性别（字典）',\n            key: 'sex_dict',\n            width: '300px',\n            type: FormTypes.select,\n            options: [],\n            dictCode: 'sex',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '多选测试',\n            key: 'multipleSelect',\n            // width: '18%',\n            width: '300px',\n            type: FormTypes.select,\n            props: { 'mode': 'multiple' }, // 支持多选\n            options: [\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            defaultValue: ['int', 'boolean'], // 多个默认项\n            // defaultValue: 'string,double,int', // 也可使用这种方式\n            placeholder: '这里可以多选',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '字段长度',\n            key: 'dbLength',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.inputNumber,\n            defaultValue: 32,\n            placeholder: '${title}',\n            // 是否是统计列，只有 inputNumber 才能设置统计列\n            statistics: true,\n            validateRules: [{ required: true, message: '请输入${title}' }]\n          },\n          {\n            title: '日期',\n            key: 'datetime',\n            // width: '22%',\n            width: '320px',\n            type: FormTypes.datetime,\n            defaultValue: '2019-4-30 14:52:22',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '数字',\n            key: 'money',\n            width: '320px',\n            type: FormTypes.inputNumber,\n            defaultValue: '100.32',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '可以为空',\n            key: 'isNull',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.checkbox,\n            customValue: ['Y', 'N'], // true ,false\n            defaultChecked: false\n          },\n          {\n            type: FormTypes.popup,\n            key: 'popup',\n            title: 'JPopup',\n            width: '180px',\n            popupCode: 'demo',\n            field: 'name',\n            orgFields: 'name',\n            destFields: 'name'\n          },\n          {\n            title: '操作',\n            key: 'action',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.slot,\n            slotName: 'action',\n          }\n\n        ],\n        dataSource: [],\n        selectedRowIds: []\n      }\n    },\n    mounted() {\n      this.randomData(23, false)\n    },\n    methods: {\n\n      /** 表单验证 */\n      handleTableCheck() {\n        this.$refs.editableTable.getValues((error) => {\n          if (error === 0) {\n            this.$message.success('验证通过')\n          } else {\n            this.$message.error('验证未通过')\n          }\n        })\n      },\n      /** 获取值，忽略表单验证 */\n      handleTableGet() {\n        this.$refs.editableTable.getValues((error, values) => {\n          console.log('values:', values)\n        }, false)\n        console.log('deleteIds:', this.$refs.editableTable.getDeleteIds())\n\n        this.$message.info('获取值成功，请看控制台输出')\n\n      },\n      /** 模拟加载1000条数据 */\n      handleTableSet() {\n        this.randomData(1000, true)\n      },\n\n      handleSelectRowChange(selectedRowIds) {\n        this.selectedRowIds = selectedRowIds\n      },\n\n      /* 随机生成数据 */\n      randomData(size, loading = false) {\n        if (loading) {\n          this.loading = true\n        }\n\n        let randomDatetime = () => {\n          let time = parseInt(randomNumber(1000, 9999999999999))\n          return moment(new Date(time)).format('YYYY-MM-DD HH:mm:ss')\n        }\n\n        let begin = Date.now()\n        let values = []\n        for (let i = 0; i < size; i++) {\n          values.push({\n            id: randomUUID(),\n            dbFieldName: `name_${i + 1}`,\n            // dbFieldTxt: randomString(10),\n            multipleSelect: ['string', ['int', 'double', 'boolean'][randomNumber(0, 2)]],\n            dbFieldType: ['string', 'int', 'double', 'boolean'][randomNumber(0, 3)],\n            dbLength: randomNumber(0, 233),\n            datetime: randomDatetime(),\n            isNull: ['Y', 'N'][randomNumber(0, 1)]\n          })\n        }\n\n        this.dataSource = values\n        let end = Date.now()\n        let diff = end - begin\n\n        if (loading && diff < size) {\n          setTimeout(() => {\n            this.loading = false\n          }, size - diff)\n        }\n\n      },\n\n      handleDelete(props) {\n        let { rowId, target } = props\n        target.removeRows(rowId)\n      }\n    }\n  }\n", {"version": 3, "sources": ["DefaultTable.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DefaultTable.vue", "sourceRoot": "src/views/jeecg/modules/JEditableTable", "sourcesContent": ["<template>\n\n  <div>\n    <a-button @click=\"handleTableCheck\" type=\"primary\">表单验证</a-button>\n    <span style=\"padding-left:8px;\"></span>\n    <a-tooltip placement=\"top\" title=\"获取值，忽略表单验证\" :autoAdjustOverflow=\"true\">\n      <a-button @click=\"handleTableGet\" type=\"primary\">获取值</a-button>\n    </a-tooltip>\n    <span style=\"padding-left:8px;\"></span>\n    <a-tooltip placement=\"top\" title=\"模拟加载1000条数据\" :autoAdjustOverflow=\"true\">\n      <a-button @click=\"handleTableSet\" type=\"primary\">设置值</a-button>\n    </a-tooltip>\n\n\n    <j-editable-table\n      ref=\"editableTable\"\n      :loading=\"loading\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :rowNumber=\"true\"\n      :rowSelection=\"true\"\n      :actionButton=\"true\"\n      :dragSort=\"true\"\n      style=\"margin-top: 8px;\"\n      @selectRowChange=\"handleSelectRowChange\">\n\n      <template v-slot:action=\"props\">\n        <a @click=\"handleDelete(props)\">删除</a>\n      </template>\n\n    </j-editable-table>\n  </div>\n\n</template>\n\n<script>\n  import moment from 'moment'\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import { randomUUID, randomNumber } from '@/utils/util'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'DefaultTable',\n    components: { JEditableTable },\n    data() {\n      return {\n        loading: false,\n        columns: [\n          {\n            title: '字段名称',\n            key: 'dbFieldName',\n            // width: '19%',\n            width: '300px',\n            type: FormTypes.input,\n            defaultValue: '',\n            placeholder: '请输入${title}',\n            validateRules: [\n              {\n                required: true, // 必填\n                message: '请输入${title}' // 显示的文本\n              },\n              {\n                pattern: /^[a-z|A-Z][a-z|A-Z\\d_-]{0,}$/, // 正则\n                message: '${title}必须以字母开头，可包含数字、下划线、横杠'\n              },\n              {\n                unique: true,\n                message: '${title}不能重复'\n              },\n              {\n                handler(type, value, row, column, callback, target) {\n                  // type 触发校验的类型（input、change、blur）\n                  // value 当前校验的值\n                  // callback(flag, message) 方法必须执行且只能执行一次\n                  //          flag = 是否通过了校验，不填写或者填写 null 代表不进行任何操作\n                  //          message = 提示的类型，默认使用配置的 message\n                  // target 行编辑的实例对象\n\n                  if (type === 'blur') {\n                    if (value === 'abc') {\n                      callback(false, '${title}不能是abc')  // false = 未通过校验\n                    } else {\n                      callback(true) // true = 通过验证\n                    }\n                  } else {\n                    callback(true) // 不填写或者填写 null 代表不进行任何操作\n                  }\n                },\n                message: '${title}默认提示'\n              }\n            ]\n          },\n          {\n            title: '文件域',\n            key: 'upload',\n            type: FormTypes.upload,\n            // width: '19%',\n            width: '300px',\n            placeholder: '点击上传',\n            token: true,\n            responseName: 'message',\n            action: window._CONFIG['domianURL'] + '/sys/common/upload'\n          },\n          {\n            title: '字段类型',\n            key: 'dbFieldType',\n            // width: '18%',\n            width: '300px',\n            type: FormTypes.select,\n            options: [ // 下拉选项\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            allowInput: true,\n            defaultValue: '',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '性别（字典）',\n            key: 'sex_dict',\n            width: '300px',\n            type: FormTypes.select,\n            options: [],\n            dictCode: 'sex',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '多选测试',\n            key: 'multipleSelect',\n            // width: '18%',\n            width: '300px',\n            type: FormTypes.select,\n            props: { 'mode': 'multiple' }, // 支持多选\n            options: [\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            defaultValue: ['int', 'boolean'], // 多个默认项\n            // defaultValue: 'string,double,int', // 也可使用这种方式\n            placeholder: '这里可以多选',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '字段长度',\n            key: 'dbLength',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.inputNumber,\n            defaultValue: 32,\n            placeholder: '${title}',\n            // 是否是统计列，只有 inputNumber 才能设置统计列\n            statistics: true,\n            validateRules: [{ required: true, message: '请输入${title}' }]\n          },\n          {\n            title: '日期',\n            key: 'datetime',\n            // width: '22%',\n            width: '320px',\n            type: FormTypes.datetime,\n            defaultValue: '2019-4-30 14:52:22',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '数字',\n            key: 'money',\n            width: '320px',\n            type: FormTypes.inputNumber,\n            defaultValue: '100.32',\n            placeholder: '请选择${title}',\n            validateRules: [{ required: true, message: '请选择${title}' }]\n          },\n          {\n            title: '可以为空',\n            key: 'isNull',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.checkbox,\n            customValue: ['Y', 'N'], // true ,false\n            defaultChecked: false\n          },\n          {\n            type: FormTypes.popup,\n            key: 'popup',\n            title: 'JPopup',\n            width: '180px',\n            popupCode: 'demo',\n            field: 'name',\n            orgFields: 'name',\n            destFields: 'name'\n          },\n          {\n            title: '操作',\n            key: 'action',\n            // width: '8%',\n            width: '100px',\n            type: FormTypes.slot,\n            slotName: 'action',\n          }\n\n        ],\n        dataSource: [],\n        selectedRowIds: []\n      }\n    },\n    mounted() {\n      this.randomData(23, false)\n    },\n    methods: {\n\n      /** 表单验证 */\n      handleTableCheck() {\n        this.$refs.editableTable.getValues((error) => {\n          if (error === 0) {\n            this.$message.success('验证通过')\n          } else {\n            this.$message.error('验证未通过')\n          }\n        })\n      },\n      /** 获取值，忽略表单验证 */\n      handleTableGet() {\n        this.$refs.editableTable.getValues((error, values) => {\n          console.log('values:', values)\n        }, false)\n        console.log('deleteIds:', this.$refs.editableTable.getDeleteIds())\n\n        this.$message.info('获取值成功，请看控制台输出')\n\n      },\n      /** 模拟加载1000条数据 */\n      handleTableSet() {\n        this.randomData(1000, true)\n      },\n\n      handleSelectRowChange(selectedRowIds) {\n        this.selectedRowIds = selectedRowIds\n      },\n\n      /* 随机生成数据 */\n      randomData(size, loading = false) {\n        if (loading) {\n          this.loading = true\n        }\n\n        let randomDatetime = () => {\n          let time = parseInt(randomNumber(1000, 9999999999999))\n          return moment(new Date(time)).format('YYYY-MM-DD HH:mm:ss')\n        }\n\n        let begin = Date.now()\n        let values = []\n        for (let i = 0; i < size; i++) {\n          values.push({\n            id: randomUUID(),\n            dbFieldName: `name_${i + 1}`,\n            // dbFieldTxt: randomString(10),\n            multipleSelect: ['string', ['int', 'double', 'boolean'][randomNumber(0, 2)]],\n            dbFieldType: ['string', 'int', 'double', 'boolean'][randomNumber(0, 3)],\n            dbLength: randomNumber(0, 233),\n            datetime: randomDatetime(),\n            isNull: ['Y', 'N'][randomNumber(0, 1)]\n          })\n        }\n\n        this.dataSource = values\n        let end = Date.now()\n        let diff = end - begin\n\n        if (loading && diff < size) {\n          setTimeout(() => {\n            this.loading = false\n          }, size - diff)\n        }\n\n      },\n\n      handleDelete(props) {\n        let { rowId, target } = props\n        target.removeRows(rowId)\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}