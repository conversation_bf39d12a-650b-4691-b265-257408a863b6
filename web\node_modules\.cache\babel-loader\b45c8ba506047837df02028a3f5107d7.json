{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue", "mtime": 1753249833907}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { addPaper, editPaper, queryPaperById, getProblemList, getPaperQuestions } from '@/api/examSystem';\nimport moment from 'moment';\nexport default {\n  name: 'PaperModal',\n  data: function data() {\n    return {\n      // 表单参数\n      title: \"新增试卷\",\n      visible: false,\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      model: {},\n      labelCol: {\n        span: 4\n      },\n      wrapperCol: {\n        span: 18\n      },\n      smallLabelCol: {\n        span: 12\n      },\n      smallWrapperCol: {\n        span: 12\n      },\n      modalWidth: 900,\n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      // 科目级别选项\n      subjectSelected: '',\n      levelOptions: [],\n      // 选择的题目\n      selectedQuestions: {\n        singleChoiceQuestions: [],\n        // 单选题\n        judgmentQuestions: [],\n        // 判断题\n        programmingQuestions: [] // 编程题\n      },\n      // 记录原始表单值和选中题目，用于检测表单是否被修改\n      originalFormValues: null,\n      originalSelectedQuestions: null,\n      formChanged: false,\n      // 题目表格列定义\n      singleChoiceColumns: [{\n        title: '序号',\n        width: '60px',\n        dataIndex: '',\n        customRender: function customRender(t, r, index) {\n          return index + 1;\n        }\n      }, {\n        title: '题目标题',\n        dataIndex: 'title'\n      }, {\n        title: '难度',\n        width: '80px',\n        dataIndex: 'difficulty',\n        customRender: this.renderDifficulty\n      }, {\n        title: '操作',\n        width: '60px',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      judgmentColumns: [{\n        title: '序号',\n        width: '60px',\n        dataIndex: '',\n        customRender: function customRender(t, r, index) {\n          return index + 1;\n        }\n      }, {\n        title: '题目标题',\n        dataIndex: 'title'\n      }, {\n        title: '难度',\n        width: '80px',\n        dataIndex: 'difficulty',\n        customRender: this.renderDifficulty\n      }, {\n        title: '操作',\n        width: '60px',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      programmingColumns: [{\n        title: '序号',\n        width: '60px',\n        dataIndex: '',\n        customRender: function customRender(t, r, index) {\n          return index + 1;\n        }\n      }, {\n        title: '题目标题',\n        dataIndex: 'title'\n      }, {\n        title: '难度',\n        width: '80px',\n        dataIndex: 'difficulty',\n        customRender: this.renderDifficulty\n      }, {\n        title: '操作',\n        width: '60px',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 题目选择器相关\n      questionSelectorVisible: false,\n      questionSelectorTitle: '选择题目',\n      questionType: 0,\n      // 当前选择的题目类型 1:单选题 2:判断题 3:编程题\n      questionQueryParam: {\n        title: '',\n        subject: '',\n        level: '',\n        difficulty: undefined,\n        questionType: undefined\n      },\n      questionList: [],\n      questionLoading: false,\n      questionPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      tempSelectedQuestionKeys: [],\n      // 临时选中的题目ID\n      tempSelectedQuestions: [],\n      // 临时选中的题目对象\n      questionSelectorColumns: [{\n        title: '题目标题',\n        dataIndex: 'title'\n      }, {\n        title: '难度',\n        dataIndex: 'difficulty',\n        width: '80px',\n        scopedSlots: {\n          customRender: 'difficultySlot'\n        }\n      }, {\n        title: '作者',\n        dataIndex: 'author',\n        width: '120px'\n      }],\n      // URL\n      url: {\n        add: \"/teaching/examSystem/testManage/add\",\n        edit: \"/teaching/examSystem/testManage/edit\",\n        queryById: \"/teaching/examSystem/testManage/queryById\"\n      }\n    };\n  },\n  methods: {\n    // 保存原始表单值和选中题目\n    saveOriginalFormValues: function saveOriginalFormValues() {\n      // 获取当前表单的所有值\n      this.originalFormValues = this.form.getFieldsValue();\n\n      // 深拷贝当前选中题目\n      this.originalSelectedQuestions = {\n        singleChoiceQuestions: _toConsumableArray(this.selectedQuestions.singleChoiceQuestions),\n        judgmentQuestions: _toConsumableArray(this.selectedQuestions.judgmentQuestions),\n        programmingQuestions: _toConsumableArray(this.selectedQuestions.programmingQuestions)\n      };\n\n      // 重置表单变更标志\n      this.formChanged = false;\n    },\n    // 添加表单变更监听\n    addFormChangeListener: function addFormChangeListener() {\n      var _this = this;\n      // 先移除之前可能存在的事件监听器\n      this.removeFormChangeListener();\n\n      // 监听表单字段变化 - 使用更可靠的方式\n      var formValueChangeHandler = function formValueChangeHandler() {\n        // 标记表单已变更\n        _this.formChanged = true;\n      };\n\n      // 保存处理函数引用，以便后续可以移除\n      this.formValueChangeHandler = formValueChangeHandler;\n\n      // 为所有表单控件添加change事件监听\n      this.$nextTick(function () {\n        // 获取表单所有input, select, textarea元素\n        var formInputs = _this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n\n        // 为每个元素添加change和input事件监听\n        formInputs.forEach(function (element) {\n          element.addEventListener('change', _this.formValueChangeHandler);\n          element.addEventListener('input', _this.formValueChangeHandler);\n        });\n\n        // 特别处理富文本编辑器和其他特殊组件的变化\n        var specialElements = _this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n        specialElements.forEach(function (element) {\n          element.addEventListener('click', _this.formValueChangeHandler);\n        });\n      });\n    },\n    // 移除表单变更监听器，避免内存泄露\n    removeFormChangeListener: function removeFormChangeListener() {\n      var _this2 = this;\n      if (this.formValueChangeHandler) {\n        this.$nextTick(function () {\n          // 获取表单所有input, select, textarea元素\n          var formInputs = _this2.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n\n          // 移除事件监听\n          formInputs.forEach(function (element) {\n            element.removeEventListener('change', _this2.formValueChangeHandler);\n            element.removeEventListener('input', _this2.formValueChangeHandler);\n          });\n\n          // 移除特殊元素的监听\n          var specialElements = _this2.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n          specialElements.forEach(function (element) {\n            element.removeEventListener('click', _this2.formValueChangeHandler);\n          });\n        });\n      }\n    },\n    // 检测表单是否发生变化\n    hasFormChanged: function hasFormChanged() {\n      if (this.formChanged) return true;\n\n      // 检查题目选择是否变化\n      if (this.originalSelectedQuestions) {\n        // 检查各类题目数量是否变化\n        if (this.selectedQuestions.singleChoiceQuestions.length !== this.originalSelectedQuestions.singleChoiceQuestions.length || this.selectedQuestions.judgmentQuestions.length !== this.originalSelectedQuestions.judgmentQuestions.length || this.selectedQuestions.programmingQuestions.length !== this.originalSelectedQuestions.programmingQuestions.length) {\n          return true;\n        }\n\n        // 检查具体题目是否变化（通过ID比较）\n        var currentSingleIds = this.selectedQuestions.singleChoiceQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        var originalSingleIds = this.originalSelectedQuestions.singleChoiceQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        var currentJudgmentIds = this.selectedQuestions.judgmentQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        var originalJudgmentIds = this.originalSelectedQuestions.judgmentQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        var currentProgrammingIds = this.selectedQuestions.programmingQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        var originalProgrammingIds = this.originalSelectedQuestions.programmingQuestions.map(function (q) {\n          return q.id;\n        }).sort().join(',');\n        if (currentSingleIds !== originalSingleIds || currentJudgmentIds !== originalJudgmentIds || currentProgrammingIds !== originalProgrammingIds) {\n          return true;\n        }\n      }\n      return false;\n    },\n    // 打开添加模态框\n    add: function add() {\n      var _this3 = this;\n      this.form.resetFields();\n      this.model = {};\n      this.visible = true;\n      this.title = \"新增试卷\";\n\n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      };\n      this.$nextTick(function () {\n        // 默认分数\n        _this3.form.setFieldsValue({\n          singleChoiceScore: 2,\n          judgmentScore: 2,\n          programmingScore: 25,\n          examDuration: 120 // 默认120分钟\n        });\n\n        // 保存原始表单值和选中题目，用于检测变更\n        _this3.saveOriginalFormValues();\n\n        // 添加表单变更监听\n        _this3.addFormChangeListener();\n      });\n    },\n    // 打开编辑模态框\n    edit: function edit(record) {\n      var _this4 = this;\n      this.form.resetFields();\n      this.model = Object.assign({}, record);\n      this.visible = true;\n      this.title = \"编辑试卷\";\n\n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      };\n      this.confirmLoading = true;\n\n      // 先获取试卷基本信息\n      queryPaperById(record.id).then(function (res) {\n        if (res.success) {\n          var paper = res.result;\n\n          // 设置科目和级别\n          _this4.subjectSelected = paper.subject;\n          _this4.updateLevelOptions();\n\n          // 解析year字段，将字符串转为moment对象\n          var year = paper.year ? moment(paper.year.toString(), 'YYYY') : null;\n          var formValues = {\n            id: paper.id,\n            title: paper.title,\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          };\n\n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              var content = JSON.parse(paper.content);\n\n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2;\n              formValues.judgmentScore = content.judgmentScore || 2;\n              formValues.programmingScore = content.programmingScore || 25;\n\n              // 获取试卷题目详情\n              _this4.loadPaperQuestions(paper.id, content);\n            } catch (e) {\n              console.error('解析试卷内容失败', e);\n              _this4.confirmLoading = false;\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2;\n            formValues.judgmentScore = 2;\n            formValues.programmingScore = 25;\n            _this4.confirmLoading = false;\n          }\n          _this4.$nextTick(function () {\n            _this4.form.setFieldsValue(formValues);\n\n            // 保存原始表单值和选中题目，用于检测变更\n            _this4.saveOriginalFormValues();\n\n            // 添加表单变更监听\n            _this4.addFormChangeListener();\n          });\n        } else {\n          _this4.$message.warning(res.message || '获取试卷详情失败');\n          _this4.confirmLoading = false;\n        }\n      }).catch(function () {\n        _this4.$message.warning('获取试卷详情失败');\n        _this4.confirmLoading = false;\n      });\n    },\n    // 加载试卷题目详情\n    loadPaperQuestions: function loadPaperQuestions(paperId, content) {\n      var _this5 = this;\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then(function (res) {\n        if (res.success) {\n          // 将题目按类型分组\n          var questionData = res.result || [];\n\n          // 创建题目类型到分数的映射\n          var scoreMap = {};\n          if (content && content.questions) {\n            content.questions.forEach(function (item) {\n              scoreMap[item.questionId] = item.score;\n            });\n          }\n\n          // 按题目类型分组\n          questionData.forEach(function (item) {\n            var question = item.question;\n            var score = item.score;\n\n            // 处理题目数据\n            if (question) {\n              // 根据题型分组\n              switch (question.questionType) {\n                case 1:\n                  // 单选题\n                  _this5.selectedQuestions.singleChoiceQuestions.push(question);\n                  break;\n                case 2:\n                  // 判断题\n                  _this5.selectedQuestions.judgmentQuestions.push(question);\n                  break;\n                case 3:\n                  // 编程题\n                  _this5.selectedQuestions.programmingQuestions.push(question);\n                  break;\n              }\n            }\n          });\n\n          // 保存原始表单值和选中题目，用于检测变更\n          _this5.saveOriginalFormValues();\n        } else {\n          _this5.$message.warning('获取试卷题目失败');\n        }\n        _this5.confirmLoading = false;\n      }).catch(function () {\n        _this5.$message.warning('获取试卷题目失败');\n        _this5.confirmLoading = false;\n      });\n    },\n    // 复制试卷\n    copy: function copy(record) {\n      var _this6 = this;\n      this.form.resetFields();\n      this.model = {};\n      this.visible = true;\n      this.title = \"复制试卷\";\n\n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      };\n      this.confirmLoading = true;\n      queryPaperById(record.id).then(function (res) {\n        if (res.success) {\n          var paper = res.result;\n\n          // 设置科目和级别\n          _this6.subjectSelected = paper.subject;\n          _this6.updateLevelOptions();\n\n          // 解析year字段，将字符串转为moment对象\n          var year = paper.year ? moment(paper.year.toString(), 'YYYY') : null;\n          var formValues = {\n            title: paper.title + ' (复制)',\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          };\n\n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              var content = JSON.parse(paper.content);\n\n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2;\n              formValues.judgmentScore = content.judgmentScore || 2;\n              formValues.programmingScore = content.programmingScore || 25;\n\n              // 设置题目列表\n              if (content.singleChoiceQuestions) {\n                _this6.selectedQuestions.singleChoiceQuestions = content.singleChoiceQuestions;\n              }\n              if (content.judgmentQuestions) {\n                _this6.selectedQuestions.judgmentQuestions = content.judgmentQuestions;\n              }\n              if (content.programmingQuestions) {\n                _this6.selectedQuestions.programmingQuestions = content.programmingQuestions;\n              }\n            } catch (e) {\n              console.error('解析试卷内容失败', e);\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2;\n            formValues.judgmentScore = 2;\n            formValues.programmingScore = 25;\n          }\n          _this6.$nextTick(function () {\n            _this6.form.setFieldsValue(formValues);\n\n            // 保存原始表单值和选中题目，用于检测变更\n            _this6.saveOriginalFormValues();\n\n            // 添加表单变更监听\n            _this6.addFormChangeListener();\n          });\n        }\n        _this6.confirmLoading = false;\n      }).catch(function () {\n        _this6.confirmLoading = false;\n      });\n    },\n    // 科目变更\n    onSubjectChange: function onSubjectChange(value) {\n      var _this7 = this;\n      this.subjectSelected = value;\n      this.updateLevelOptions();\n\n      // 清空级别选择\n      this.$nextTick(function () {\n        _this7.form.setFieldsValue({\n          level: undefined\n        });\n      });\n    },\n    // 级别变更\n    onLevelChange: function onLevelChange(value) {\n      var _this8 = this;\n      // 根据级别自动设置考试时长\n      if (value) {\n        var examDuration = this.getExamDurationByLevel(value);\n        this.$nextTick(function () {\n          _this8.form.setFieldsValue({\n            examDuration: examDuration\n          });\n        });\n      }\n    },\n    // 更新级别选项\n    updateLevelOptions: function updateLevelOptions() {\n      if (this.subjectSelected === 'Scratch') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级'];\n      } else if (this.subjectSelected === 'Python' || this.subjectSelected === 'C++') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级'];\n      } else {\n        this.levelOptions = [];\n      }\n    },\n    // 根据级别计算考试时长\n    getExamDurationByLevel: function getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n\n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.startsWith('五') || level.startsWith('六') || level.startsWith('七') || level.startsWith('八')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    // 年份选择面板变化\n    handleYearPanelChange: function handleYearPanelChange(value, mode) {\n      this.form.setFieldsValue({\n        year: value\n      });\n      this.yearMode = 'year';\n    },\n    // 年份选择变化\n    handleYearChange: function handleYearChange(date, dateString) {\n      this.form.setFieldsValue({\n        year: date\n      });\n    },\n    // 渲染难度标签\n    renderDifficulty: function renderDifficulty(difficulty) {\n      var h = this.$createElement;\n      var color = 'default';\n      var text = '未知';\n      if (difficulty === 1) {\n        color = 'green';\n        text = '简单';\n      } else if (difficulty === 2) {\n        color = 'orange';\n        text = '中等';\n      } else if (difficulty === 3) {\n        color = 'red';\n        text = '困难';\n      }\n      return h(\"a-tag\", {\n        \"attrs\": {\n          \"color\": color\n        }\n      }, [text]);\n    },\n    // 打开题目选择器\n    showQuestionSelector: function showQuestionSelector(type) {\n      this.questionType = type;\n\n      // 根据题目类型设置标题\n      switch (type) {\n        case 1:\n          this.questionSelectorTitle = '选择单选题';\n          break;\n        case 2:\n          this.questionSelectorTitle = '选择判断题';\n          break;\n        case 3:\n          this.questionSelectorTitle = '选择编程题';\n          break;\n      }\n\n      // 获取当前表单的科目和级别\n      var values = this.form.getFieldsValue(['subject', 'level']);\n      if (!values.subject || !values.level) {\n        this.$message.warning('请先选择科目和级别');\n        return;\n      }\n\n      // 设置查询参数\n      this.questionQueryParam = {\n        title: '',\n        subject: values.subject,\n        level: values.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      };\n      this.questionPagination.current = 1;\n      this.tempSelectedQuestionKeys = [];\n      this.tempSelectedQuestions = [];\n\n      // 显示模态框\n      this.questionSelectorVisible = true;\n\n      // 加载题目列表\n      this.loadQuestions(1);\n    },\n    // 加载题目列表\n    loadQuestions: function loadQuestions(page) {\n      var _this9 = this;\n      if (page) {\n        this.questionPagination.current = page;\n      }\n      this.questionLoading = true;\n      var params = _objectSpread(_objectSpread({}, this.questionQueryParam), {}, {\n        pageNo: this.questionPagination.current,\n        pageSize: this.questionPagination.pageSize\n      });\n\n      // 调用接口查询题目列表\n      getProblemList(params).then(function (res) {\n        if (res.success) {\n          _this9.questionList = res.result.records || [];\n          _this9.questionPagination.total = res.result.total || 0;\n\n          // 更新已选题目的选中状态\n          _this9.updateSelectedQuestionKeys();\n        } else {\n          _this9.$message.warning(res.message || '获取题目列表失败');\n        }\n        _this9.questionLoading = false;\n      }).catch(function () {\n        _this9.questionLoading = false;\n      });\n    },\n    // 重置题目查询条件\n    resetQuestionQuery: function resetQuestionQuery() {\n      // 保留科目和级别、题型\n      this.questionQueryParam = {\n        title: '',\n        subject: this.questionQueryParam.subject,\n        level: this.questionQueryParam.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      };\n      this.loadQuestions(1);\n    },\n    // 题目表格变化\n    handleQuestionTableChange: function handleQuestionTableChange(pagination) {\n      this.questionPagination.current = pagination.current;\n      this.loadQuestions();\n    },\n    // 题目选择变化\n    onQuestionSelectionChange: function onQuestionSelectionChange(selectedRowKeys, selectedRows) {\n      this.tempSelectedQuestionKeys = selectedRowKeys;\n      this.tempSelectedQuestions = selectedRows;\n    },\n    // 更新已选题目的选中状态\n    updateSelectedQuestionKeys: function updateSelectedQuestionKeys() {\n      // 获取当前类型的已选题目ID列表\n      var selectedIds = [];\n      switch (this.questionType) {\n        case 1:\n          selectedIds = this.selectedQuestions.singleChoiceQuestions.map(function (q) {\n            return q.id;\n          });\n          break;\n        case 2:\n          selectedIds = this.selectedQuestions.judgmentQuestions.map(function (q) {\n            return q.id;\n          });\n          break;\n        case 3:\n          selectedIds = this.selectedQuestions.programmingQuestions.map(function (q) {\n            return q.id;\n          });\n          break;\n      }\n\n      // 查找当前页面中已选择的题目\n      var currentPageSelectedIds = this.questionList.filter(function (q) {\n        return selectedIds.includes(q.id);\n      }).map(function (q) {\n        return q.id;\n      });\n      this.tempSelectedQuestionKeys = currentPageSelectedIds;\n    },\n    // 确认题目选择\n    confirmQuestionSelection: function confirmQuestionSelection() {\n      // 根据题目类型，将选择的题目添加到对应的列表中\n      switch (this.questionType) {\n        case 1:\n          // 过滤掉重复的题目\n          var existingSingleChoiceIds = this.selectedQuestions.singleChoiceQuestions.map(function (q) {\n            return q.id;\n          });\n          var newSingleChoice = this.tempSelectedQuestions.filter(function (q) {\n            return !existingSingleChoiceIds.includes(q.id);\n          });\n\n          // 添加新题目\n          this.selectedQuestions.singleChoiceQuestions = [].concat(_toConsumableArray(this.selectedQuestions.singleChoiceQuestions), _toConsumableArray(newSingleChoice));\n          break;\n        case 2:\n          // 过滤掉重复的题目\n          var existingJudgmentIds = this.selectedQuestions.judgmentQuestions.map(function (q) {\n            return q.id;\n          });\n          var newJudgment = this.tempSelectedQuestions.filter(function (q) {\n            return !existingJudgmentIds.includes(q.id);\n          });\n\n          // 添加新题目\n          this.selectedQuestions.judgmentQuestions = [].concat(_toConsumableArray(this.selectedQuestions.judgmentQuestions), _toConsumableArray(newJudgment));\n          break;\n        case 3:\n          // 过滤掉重复的题目\n          var existingProgrammingIds = this.selectedQuestions.programmingQuestions.map(function (q) {\n            return q.id;\n          });\n          var newProgramming = this.tempSelectedQuestions.filter(function (q) {\n            return !existingProgrammingIds.includes(q.id);\n          });\n\n          // 添加新题目\n          this.selectedQuestions.programmingQuestions = [].concat(_toConsumableArray(this.selectedQuestions.programmingQuestions), _toConsumableArray(newProgramming));\n          break;\n      }\n\n      // 标记表单已变更\n      this.formChanged = true;\n\n      // 关闭模态框\n      this.handleQuestionSelectorCancel();\n    },\n    // 关闭题目选择器\n    handleQuestionSelectorCancel: function handleQuestionSelectorCancel() {\n      this.questionSelectorVisible = false;\n      this.tempSelectedQuestionKeys = [];\n      this.tempSelectedQuestions = [];\n    },\n    // 移除已选题目\n    removeQuestion: function removeQuestion(questionType, index) {\n      this.selectedQuestions[questionType].splice(index, 1);\n\n      // 标记表单已变更\n      this.formChanged = true;\n    },\n    // 提交表单\n    handleOk: function handleOk() {\n      var _this10 = this;\n      var that = this;\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n\n          // 检查是否有题目\n          var hasQuestions = _this10.selectedQuestions.singleChoiceQuestions.length > 0 || _this10.selectedQuestions.judgmentQuestions.length > 0 || _this10.selectedQuestions.programmingQuestions.length > 0;\n          if (!hasQuestions) {\n            _this10.$message.warning('请至少添加一道题目');\n            that.confirmLoading = false;\n            return;\n          }\n\n          // 处理年份，转为字符串\n          var year = values.year ? moment(values.year).format('YYYY') : moment().format('YYYY');\n\n          // 构建content字段\n          // 收集所有题目ID和分数\n          var questions = [];\n\n          // 处理单选题\n          _this10.selectedQuestions.singleChoiceQuestions.forEach(function (question) {\n            questions.push({\n              questionId: question.id,\n              score: values.singleChoiceScore || 2\n            });\n          });\n\n          // 处理判断题\n          _this10.selectedQuestions.judgmentQuestions.forEach(function (question) {\n            questions.push({\n              questionId: question.id,\n              score: values.judgmentScore || 2\n            });\n          });\n\n          // 处理编程题\n          _this10.selectedQuestions.programmingQuestions.forEach(function (question) {\n            questions.push({\n              questionId: question.id,\n              score: values.programmingScore || 25\n            });\n          });\n          var content = {\n            singleChoiceScore: values.singleChoiceScore,\n            judgmentScore: values.judgmentScore,\n            programmingScore: values.programmingScore,\n            questions: questions\n          };\n\n          // 构建提交数据\n          var params = _objectSpread(_objectSpread({}, _this10.model), {}, {\n            title: values.title,\n            subject: values.subject,\n            level: values.level,\n            difficulty: values.difficulty,\n            type: values.type,\n            year: year,\n            author: values.author,\n            examDuration: values.examDuration,\n            content: JSON.stringify(content)\n          });\n\n          // 发送请求\n          var apiMethod = params.id ? editPaper : addPaper;\n          apiMethod(params).then(function (res) {\n            if (res.success) {\n              that.$message.success(res.message || (params.id ? '编辑成功' : '添加成功'));\n              that.$emit('ok');\n              that.close();\n            } else {\n              that.$message.warning(res.message || (params.id ? '编辑失败' : '添加失败'));\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n          });\n        }\n      });\n    },\n    // 处理取消操作\n    handleCancel: function handleCancel() {\n      var _this11 = this;\n      // 检查表单是否有变更\n      if (this.hasFormChanged()) {\n        // 有变更，显示确认对话框\n        this.$confirm({\n          title: '提示',\n          content: '还未提交，是否退出？',\n          okText: '是',\n          cancelText: '否',\n          onOk: function onOk() {\n            _this11.close();\n          }\n        });\n      } else {\n        // 无变更，直接关闭\n        this.close();\n      }\n    },\n    close: function close() {\n      this.visible = false;\n      this.questionSelectorVisible = false;\n\n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      };\n\n      // 清理事件监听器，避免内存泄漏\n      this.removeFormChangeListener();\n    }\n  }\n};", {"version": 3, "names": ["addPaper", "editPaper", "queryPaperById", "getProblemList", "getPaperQuestions", "moment", "name", "data", "title", "visible", "confirmLoading", "form", "$form", "createForm", "model", "labelCol", "span", "wrapperCol", "smallLabelCol", "smallWrapperCol", "modalWidth", "yearMode", "yearFormat", "subjectSelected", "levelOptions", "selectedQuestions", "singleChoiceQuestions", "judgmentQuestions", "programmingQuestions", "originalFormValues", "originalSelectedQuestions", "formChanged", "singleChoiceColumns", "width", "dataIndex", "customRender", "t", "r", "index", "renderDifficulty", "scopedSlots", "judgmentColumns", "programmingColumns", "questionSelectorVisible", "questionSelectorTitle", "questionType", "questionQueryParam", "subject", "level", "difficulty", "undefined", "questionList", "questionLoading", "questionPagination", "current", "pageSize", "total", "tempSelectedQuestionKeys", "tempSelectedQuestions", "questionSelectorColumns", "url", "add", "edit", "queryById", "methods", "saveOriginalFormValues", "getFieldsValue", "_toConsumableArray", "addFormChangeListener", "_this", "removeFormChangeListener", "formValueChangeHandler", "$nextTick", "formInputs", "$el", "querySelectorAll", "for<PERSON>ach", "element", "addEventListener", "specialElements", "_this2", "removeEventListener", "hasFormChanged", "length", "currentSingleIds", "map", "q", "id", "sort", "join", "originalSingleIds", "currentJudgmentIds", "originalJudgmentIds", "currentProgrammingIds", "originalProgrammingIds", "_this3", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleChoiceScore", "judgmentScore", "programmingScore", "examDuration", "record", "_this4", "Object", "assign", "then", "res", "success", "paper", "result", "updateLevelOptions", "year", "toString", "formValues", "type", "author", "content", "JSON", "parse", "loadPaperQuestions", "e", "console", "error", "$message", "warning", "message", "catch", "paperId", "_this5", "questionData", "scoreMap", "questions", "item", "questionId", "score", "question", "push", "copy", "_this6", "onSubjectChange", "value", "_this7", "onLevelChange", "_this8", "getExamDurationByLevel", "startsWith", "handleYearPanelChange", "mode", "handleYearChange", "date", "dateString", "h", "$createElement", "color", "text", "showQuestionSelector", "values", "loadQuestions", "page", "_this9", "params", "_objectSpread", "pageNo", "records", "updateSelectedQuestionKeys", "resetQuestionQuery", "handleQuestionTableChange", "pagination", "onQuestionSelectionChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "selectedIds", "currentPageSelectedIds", "filter", "includes", "confirmQuestionSelection", "existingSingleChoiceIds", "newSingleChoice", "concat", "existingJudgmentIds", "newJudgment", "existingProgrammingIds", "newProgramming", "handleQuestionSelectorCancel", "removeQuestion", "splice", "handleOk", "_this10", "that", "validateFields", "err", "hasQuestions", "format", "stringify", "apiMethod", "$emit", "close", "finally", "handleCancel", "_this11", "$confirm", "okText", "cancelText", "onOk"], "sources": ["src/views/examSystem/modules/PaperModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    :maskClosable=\"false\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n    \n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 基本信息 -->\n        <a-form-item label=\"试卷标题\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input\n            placeholder=\"请输入试卷标题\"\n            v-decorator=\"['title', {rules: [{required: true, message: '请输入试卷标题!'}]}]\" />\n        </a-form-item>\n        \n        <a-form-item label=\"所属科目\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择所属科目\"\n            v-decorator=\"['subject', {rules: [{required: true, message: '请选择所属科目!'}]}]\"\n            @change=\"onSubjectChange\">\n            <a-select-option value=\"Scratch\">Scratch</a-select-option>\n            <a-select-option value=\"Python\">Python</a-select-option>\n            <a-select-option value=\"C++\">C++</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"所属级别\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择所属级别\"\n            v-decorator=\"['level', {rules: [{required: true, message: '请选择所属级别!'}]}]\"\n            @change=\"onLevelChange\">\n            <a-select-option v-for=\"(level, index) in levelOptions\" :key=\"index\" :value=\"level\">\n              {{ level }}\n            </a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"难度\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择难度\"\n            v-decorator=\"['difficulty', {rules: [{required: true, message: '请选择难度!'}]}]\">\n            <a-select-option :value=\"1\">简单</a-select-option>\n            <a-select-option :value=\"2\">中等</a-select-option>\n            <a-select-option :value=\"3\">困难</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-select\n            placeholder=\"请选择类型\"\n            v-decorator=\"['type', {rules: [{required: true, message: '请选择类型!'}]}]\">\n            <a-select-option value=\"真题\">真题</a-select-option>\n            <a-select-option value=\"模拟\">模拟</a-select-option>\n          </a-select>\n        </a-form-item>\n        \n        <a-form-item label=\"年份\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-date-picker\n            placeholder=\"请选择年份\"\n            v-decorator=\"['year', {rules: [{required: true, message: '请选择年份!'}]}]\"\n            :format=\"yearFormat\"\n            :mode=\"yearMode\"\n            @panelChange=\"handleYearPanelChange\"\n            @change=\"handleYearChange\" />\n        </a-form-item>\n        \n        <a-form-item label=\"作者\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input\n            placeholder=\"请输入作者\"\n            v-decorator=\"['author', {rules: [{required: true, message: '请输入作者!'}]}]\" />\n        </a-form-item>\n        \n        <a-form-item label=\"考试时长(分钟)\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n          <a-input-number\n            placeholder=\"请输入考试时长\"\n            v-decorator=\"['examDuration', {rules: [{required: true, message: '请输入考试时长!'}]}]\"\n            :min=\"1\"\n            :max=\"1440\"\n            :step=\"10\" />\n        </a-form-item>\n        \n        <!-- 题目分数设置 -->\n        <a-divider>题目分数设置</a-divider>\n        <a-row :gutter=\"24\">\n          <a-col :span=\"8\">\n            <a-form-item label=\"单选题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"单选题每题分数\"\n                v-decorator=\"['singleChoiceScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"8\">\n            <a-form-item label=\"判断题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"判断题每题分数\"\n                v-decorator=\"['judgmentScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"8\">\n            <a-form-item label=\"编程题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n              <a-input-number\n                placeholder=\"编程题每题分数\"\n                v-decorator=\"['programmingScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n                :min=\"1\"\n                :max=\"100\" />\n            </a-form-item>\n          </a-col>\n        </a-row>\n        \n        <!-- 题目选择区域 -->\n        <a-divider>题目选择</a-divider>\n        \n        <!-- 单选题选择 -->\n        <a-collapse defaultActiveKey=\"1\">\n          <a-collapse-panel key=\"1\" header=\"单选题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(1)\">\n              添加单选题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"singleChoiceColumns\"\n              :dataSource=\"selectedQuestions.singleChoiceQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('singleChoiceQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n          \n          <!-- 判断题选择 -->\n          <a-collapse-panel key=\"2\" header=\"判断题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(2)\">\n              添加判断题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"judgmentColumns\"\n              :dataSource=\"selectedQuestions.judgmentQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('judgmentQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n          \n          <!-- 编程题选择 -->\n          <a-collapse-panel key=\"3\" header=\"编程题\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(3)\">\n              添加编程题\n            </a-button>\n            <a-table\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"programmingColumns\"\n              :dataSource=\"selectedQuestions.programmingQuestions\"\n              :pagination=\"false\"\n              style=\"margin-top: 10px\"\n            >\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"() => removeQuestion('programmingQuestions', index)\">删除</a>\n              </span>\n            </a-table>\n          </a-collapse-panel>\n        </a-collapse>\n      </a-form>\n    </a-spin>\n    \n    <!-- 题目选择器模态框 -->\n    <a-modal\n      :title=\"questionSelectorTitle\"\n      :width=\"800\"\n      :visible=\"questionSelectorVisible\"\n      :footer=\"null\"\n      @cancel=\"handleQuestionSelectorCancel\"\n    >\n      <!-- 查询条件 -->\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"12\">\n          <a-col :md=\"12\">\n            <a-form-item label=\"题目标题\">\n              <a-input v-model=\"questionQueryParam.title\" placeholder=\"请输入题目标题\" />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-form-item label=\"科目/级别\">\n              <a-input v-model=\"questionQueryParam.subject\" disabled />\n              <a-input v-model=\"questionQueryParam.level\" disabled style=\"width: 80px; margin-left: 8px;\" />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-form-item label=\"难度\">\n              <a-select v-model=\"questionQueryParam.difficulty\" placeholder=\"请选择难度\" allowClear style=\"width: 160px;\">\n                <a-select-option :value=\"1\">简单</a-select-option>\n                <a-select-option :value=\"2\">中等</a-select-option>\n                <a-select-option :value=\"3\">困难</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"12\">\n            <a-button type=\"primary\" @click=\"loadQuestions(1)\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"resetQuestionQuery\">重置</a-button>\n          </a-col>\n        </a-row>\n      </a-form>\n      \n      <!-- 题目列表 -->\n      <a-table\n        size=\"middle\"\n        rowKey=\"id\"\n        :columns=\"questionSelectorColumns\"\n        :dataSource=\"questionList\"\n        :pagination=\"questionPagination\"\n        :loading=\"questionLoading\"\n        :rowSelection=\"{\n          selectedRowKeys: tempSelectedQuestionKeys,\n          onChange: onQuestionSelectionChange\n        }\"\n        @change=\"handleQuestionTableChange\"\n        style=\"margin-top: 16px;\"\n      >\n        <!-- 自定义难度展示 -->\n        <template slot=\"difficultySlot\" slot-scope=\"text\">\n          <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n          <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n          <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n          <a-tag v-else>未知</a-tag>\n        </template>\n      </a-table>\n      \n      <!-- 操作按钮 -->\n      <div style=\"text-align: right; margin-top: 16px;\">\n        <a-button @click=\"handleQuestionSelectorCancel\">取消</a-button>\n        <a-button type=\"primary\" style=\"margin-left: 8px;\" @click=\"confirmQuestionSelection\">\n          确认选择\n        </a-button>\n      </div>\n    </a-modal>\n  </a-modal>\n</template>\n\n<script>\nimport { \n  addPaper, \n  editPaper, \n  queryPaperById,\n  getProblemList,\n  getPaperQuestions\n} from '@/api/examSystem'\nimport moment from 'moment'\n\nexport default {\n  name: 'PaperModal',\n  data() {\n    return {\n      // 表单参数\n      title: \"新增试卷\",\n      visible: false,\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      model: {},\n      labelCol: { span: 4 },\n      wrapperCol: { span: 18 },\n      smallLabelCol: { span: 12 },\n      smallWrapperCol: { span: 12 },\n      modalWidth: 900,\n      \n      // 年份选择相关\n      yearMode: 'year',\n      yearFormat: 'YYYY',\n      \n      // 科目级别选项\n      subjectSelected: '',\n      levelOptions: [],\n      \n      // 选择的题目\n      selectedQuestions: {\n        singleChoiceQuestions: [], // 单选题\n        judgmentQuestions: [], // 判断题\n        programmingQuestions: [] // 编程题\n      },\n      \n      // 记录原始表单值和选中题目，用于检测表单是否被修改\n      originalFormValues: null,\n      originalSelectedQuestions: null,\n      formChanged: false,\n      \n      // 题目表格列定义\n      singleChoiceColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      judgmentColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      programmingColumns: [\n        { title: '序号', width: '60px', dataIndex: '', customRender: (t, r, index) => index + 1 },\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', width: '80px', dataIndex: 'difficulty', customRender: this.renderDifficulty },\n        { title: '操作', width: '60px', dataIndex: 'action', scopedSlots: { customRender: 'action' }}\n      ],\n      \n      // 题目选择器相关\n      questionSelectorVisible: false,\n      questionSelectorTitle: '选择题目',\n      questionType: 0, // 当前选择的题目类型 1:单选题 2:判断题 3:编程题\n      questionQueryParam: {\n        title: '',\n        subject: '',\n        level: '',\n        difficulty: undefined,\n        questionType: undefined\n      },\n      questionList: [],\n      questionLoading: false,\n      questionPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      tempSelectedQuestionKeys: [], // 临时选中的题目ID\n      tempSelectedQuestions: [], // 临时选中的题目对象\n      questionSelectorColumns: [\n        { title: '题目标题', dataIndex: 'title' },\n        { title: '难度', dataIndex: 'difficulty', width: '80px', scopedSlots: { customRender: 'difficultySlot' } },\n        { title: '作者', dataIndex: 'author', width: '120px' }\n      ],\n      \n      // URL\n      url: {\n        add: \"/teaching/examSystem/testManage/add\",\n        edit: \"/teaching/examSystem/testManage/edit\",\n        queryById: \"/teaching/examSystem/testManage/queryById\"\n      },\n    }\n  },\n  methods: {\n    // 保存原始表单值和选中题目\n    saveOriginalFormValues() {\n      // 获取当前表单的所有值\n      this.originalFormValues = this.form.getFieldsValue();\n      \n      // 深拷贝当前选中题目\n      this.originalSelectedQuestions = {\n        singleChoiceQuestions: [...this.selectedQuestions.singleChoiceQuestions],\n        judgmentQuestions: [...this.selectedQuestions.judgmentQuestions],\n        programmingQuestions: [...this.selectedQuestions.programmingQuestions]\n      };\n      \n      // 重置表单变更标志\n      this.formChanged = false;\n    },\n    \n    // 添加表单变更监听\n    addFormChangeListener() {\n      // 先移除之前可能存在的事件监听器\n      this.removeFormChangeListener();\n      \n      // 监听表单字段变化 - 使用更可靠的方式\n      const formValueChangeHandler = () => {\n        // 标记表单已变更\n        this.formChanged = true;\n      };\n      \n      // 保存处理函数引用，以便后续可以移除\n      this.formValueChangeHandler = formValueChangeHandler;\n      \n      // 为所有表单控件添加change事件监听\n      this.$nextTick(() => {\n        // 获取表单所有input, select, textarea元素\n        const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n        \n        // 为每个元素添加change和input事件监听\n        formInputs.forEach(element => {\n          element.addEventListener('change', this.formValueChangeHandler);\n          element.addEventListener('input', this.formValueChangeHandler);\n        });\n        \n        // 特别处理富文本编辑器和其他特殊组件的变化\n        const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n        specialElements.forEach(element => {\n          element.addEventListener('click', this.formValueChangeHandler);\n        });\n      });\n    },\n    \n    // 移除表单变更监听器，避免内存泄露\n    removeFormChangeListener() {\n      if (this.formValueChangeHandler) {\n        this.$nextTick(() => {\n          // 获取表单所有input, select, textarea元素\n          const formInputs = this.$el.querySelectorAll('input, select, textarea, .ant-input-number');\n          \n          // 移除事件监听\n          formInputs.forEach(element => {\n            element.removeEventListener('change', this.formValueChangeHandler);\n            element.removeEventListener('input', this.formValueChangeHandler);\n          });\n          \n          // 移除特殊元素的监听\n          const specialElements = this.$el.querySelectorAll('.ant-select-selection, .ant-checkbox, .ant-radio');\n          specialElements.forEach(element => {\n            element.removeEventListener('click', this.formValueChangeHandler);\n          });\n        });\n      }\n    },\n    \n    // 检测表单是否发生变化\n    hasFormChanged() {\n      if (this.formChanged) return true;\n      \n      // 检查题目选择是否变化\n      if (this.originalSelectedQuestions) {\n        // 检查各类题目数量是否变化\n        if (\n          this.selectedQuestions.singleChoiceQuestions.length !== this.originalSelectedQuestions.singleChoiceQuestions.length ||\n          this.selectedQuestions.judgmentQuestions.length !== this.originalSelectedQuestions.judgmentQuestions.length ||\n          this.selectedQuestions.programmingQuestions.length !== this.originalSelectedQuestions.programmingQuestions.length\n        ) {\n          return true;\n        }\n        \n        // 检查具体题目是否变化（通过ID比较）\n        const currentSingleIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        const originalSingleIds = this.originalSelectedQuestions.singleChoiceQuestions.map(q => q.id).sort().join(',');\n        \n        const currentJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        const originalJudgmentIds = this.originalSelectedQuestions.judgmentQuestions.map(q => q.id).sort().join(',');\n        \n        const currentProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        const originalProgrammingIds = this.originalSelectedQuestions.programmingQuestions.map(q => q.id).sort().join(',');\n        \n        if (\n          currentSingleIds !== originalSingleIds ||\n          currentJudgmentIds !== originalJudgmentIds ||\n          currentProgrammingIds !== originalProgrammingIds\n        ) {\n          return true;\n        }\n      }\n      \n      return false;\n    },\n    \n    // 打开添加模态框\n    add() {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"新增试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.$nextTick(() => {\n        // 默认分数\n        this.form.setFieldsValue({\n          singleChoiceScore: 2,\n          judgmentScore: 2,\n          programmingScore: 25,\n          examDuration: 120 // 默认120分钟\n        })\n        \n        // 保存原始表单值和选中题目，用于检测变更\n        this.saveOriginalFormValues()\n        \n        // 添加表单变更监听\n        this.addFormChangeListener()\n      })\n    },\n    \n    // 打开编辑模态框\n    edit(record) {\n      this.form.resetFields()\n      this.model = Object.assign({}, record)\n      this.visible = true\n      this.title = \"编辑试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      // 先获取试卷基本信息\n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            id: paper.id,\n            title: paper.title,\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 获取试卷题目详情\n              this.loadPaperQuestions(paper.id, content)\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n              this.confirmLoading = false\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n            this.confirmLoading = false\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        } else {\n          this.$message.warning(res.message || '获取试卷详情失败')\n          this.confirmLoading = false\n        }\n      }).catch(() => {\n        this.$message.warning('获取试卷详情失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 加载试卷题目详情\n    loadPaperQuestions(paperId, content) {\n      // 调用获取试卷题目的接口\n      getPaperQuestions(paperId).then((res) => {\n        if (res.success) {\n          // 将题目按类型分组\n          const questionData = res.result || []\n          \n          // 创建题目类型到分数的映射\n          const scoreMap = {}\n          if (content && content.questions) {\n            content.questions.forEach(item => {\n              scoreMap[item.questionId] = item.score\n            })\n          }\n          \n          // 按题目类型分组\n          questionData.forEach(item => {\n            const question = item.question\n            const score = item.score\n            \n            // 处理题目数据\n            if (question) {\n              // 根据题型分组\n              switch (question.questionType) {\n                case 1: // 单选题\n                  this.selectedQuestions.singleChoiceQuestions.push(question)\n                  break\n                case 2: // 判断题\n                  this.selectedQuestions.judgmentQuestions.push(question)\n                  break\n                case 3: // 编程题\n                  this.selectedQuestions.programmingQuestions.push(question)\n                  break\n              }\n            }\n          })\n          \n          // 保存原始表单值和选中题目，用于检测变更\n          this.saveOriginalFormValues()\n        } else {\n          this.$message.warning('获取试卷题目失败')\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.$message.warning('获取试卷题目失败')\n        this.confirmLoading = false\n      })\n    },\n    \n    // 复制试卷\n    copy(record) {\n      this.form.resetFields()\n      this.model = {}\n      this.visible = true\n      this.title = \"复制试卷\"\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      this.confirmLoading = true\n      \n      queryPaperById(record.id).then((res) => {\n        if (res.success) {\n          const paper = res.result\n          \n          // 设置科目和级别\n          this.subjectSelected = paper.subject\n          this.updateLevelOptions()\n          \n          // 解析year字段，将字符串转为moment对象\n          const year = paper.year ? moment(paper.year.toString(), 'YYYY') : null\n          \n          const formValues = {\n            title: paper.title + ' (复制)',\n            subject: paper.subject,\n            level: paper.level,\n            difficulty: paper.difficulty,\n            type: paper.type,\n            year: year,\n            author: paper.author,\n            examDuration: paper.examDuration || 120\n          }\n          \n          // 解析content字段的内容\n          if (paper.content) {\n            try {\n              const content = JSON.parse(paper.content)\n              \n              // 设置分数\n              formValues.singleChoiceScore = content.singleChoiceScore || 2\n              formValues.judgmentScore = content.judgmentScore || 2\n              formValues.programmingScore = content.programmingScore || 25\n              \n              // 设置题目列表\n              if (content.singleChoiceQuestions) {\n                this.selectedQuestions.singleChoiceQuestions = content.singleChoiceQuestions\n              }\n              if (content.judgmentQuestions) {\n                this.selectedQuestions.judgmentQuestions = content.judgmentQuestions\n              }\n              if (content.programmingQuestions) {\n                this.selectedQuestions.programmingQuestions = content.programmingQuestions\n              }\n            } catch (e) {\n              console.error('解析试卷内容失败', e)\n            }\n          } else {\n            // 默认分数\n            formValues.singleChoiceScore = 2\n            formValues.judgmentScore = 2\n            formValues.programmingScore = 25\n          }\n          \n          this.$nextTick(() => {\n            this.form.setFieldsValue(formValues)\n            \n            // 保存原始表单值和选中题目，用于检测变更\n            this.saveOriginalFormValues()\n            \n            // 添加表单变更监听\n            this.addFormChangeListener()\n          })\n        }\n        this.confirmLoading = false\n      }).catch(() => {\n        this.confirmLoading = false\n      })\n    },\n    \n    // 科目变更\n    onSubjectChange(value) {\n      this.subjectSelected = value\n      this.updateLevelOptions()\n\n      // 清空级别选择\n      this.$nextTick(() => {\n        this.form.setFieldsValue({\n          level: undefined\n        })\n      })\n    },\n\n    // 级别变更\n    onLevelChange(value) {\n      // 根据级别自动设置考试时长\n      if (value) {\n        const examDuration = this.getExamDurationByLevel(value)\n        this.$nextTick(() => {\n          this.form.setFieldsValue({\n            examDuration: examDuration\n          })\n        })\n      }\n    },\n    \n    // 更新级别选项\n    updateLevelOptions() {\n      if (this.subjectSelected === 'Scratch') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级']\n      } else if (this.subjectSelected === 'Python' || this.subjectSelected === 'C++') {\n        // 将数字级别改为中文格式\n        this.levelOptions = ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级']\n      } else {\n        this.levelOptions = []\n      }\n    },\n    \n    // 根据级别计算考试时长\n    getExamDurationByLevel(level) {\n      // 1-4级：考试时长为120分钟\n      // 5-8级：考试时长为180分钟\n      \n      // 将中文级别转换为数字进行判断\n      if (level) {\n        if (level.startsWith('五') || level.startsWith('六') || level.startsWith('七') || level.startsWith('八')) {\n          return 180;\n        }\n      }\n      // 默认返回120分钟\n      return 120;\n    },\n    \n    // 年份选择面板变化\n    handleYearPanelChange(value, mode) {\n      this.form.setFieldsValue({ year: value })\n      this.yearMode = 'year'\n    },\n    \n    // 年份选择变化\n    handleYearChange(date, dateString) {\n      this.form.setFieldsValue({ year: date })\n    },\n    \n    // 渲染难度标签\n    renderDifficulty(difficulty) {\n      let color = 'default'\n      let text = '未知'\n      \n      if (difficulty === 1) {\n        color = 'green'\n        text = '简单'\n      } else if (difficulty === 2) {\n        color = 'orange'\n        text = '中等'\n      } else if (difficulty === 3) {\n        color = 'red'\n        text = '困难'\n      }\n      \n      return <a-tag color={color}>{text}</a-tag>\n    },\n    \n    // 打开题目选择器\n    showQuestionSelector(type) {\n      this.questionType = type\n      \n      // 根据题目类型设置标题\n      switch (type) {\n        case 1:\n          this.questionSelectorTitle = '选择单选题'\n          break\n        case 2:\n          this.questionSelectorTitle = '选择判断题'\n          break\n        case 3:\n          this.questionSelectorTitle = '选择编程题'\n          break\n      }\n      \n      // 获取当前表单的科目和级别\n      const values = this.form.getFieldsValue(['subject', 'level'])\n      \n      if (!values.subject || !values.level) {\n        this.$message.warning('请先选择科目和级别')\n        return\n      }\n      \n      // 设置查询参数\n      this.questionQueryParam = {\n        title: '',\n        subject: values.subject,\n        level: values.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.questionPagination.current = 1\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n      \n      // 显示模态框\n      this.questionSelectorVisible = true\n      \n      // 加载题目列表\n      this.loadQuestions(1)\n    },\n    \n    // 加载题目列表\n    loadQuestions(page) {\n      if (page) {\n        this.questionPagination.current = page\n      }\n      \n      this.questionLoading = true\n      \n      const params = {\n        ...this.questionQueryParam,\n        pageNo: this.questionPagination.current,\n        pageSize: this.questionPagination.pageSize\n      }\n      \n      // 调用接口查询题目列表\n      getProblemList(params).then((res) => {\n        if (res.success) {\n          this.questionList = res.result.records || []\n          this.questionPagination.total = res.result.total || 0\n          \n          // 更新已选题目的选中状态\n          this.updateSelectedQuestionKeys()\n        } else {\n          this.$message.warning(res.message || '获取题目列表失败')\n        }\n        this.questionLoading = false\n      }).catch(() => {\n        this.questionLoading = false\n      })\n    },\n    \n    // 重置题目查询条件\n    resetQuestionQuery() {\n      // 保留科目和级别、题型\n      this.questionQueryParam = {\n        title: '',\n        subject: this.questionQueryParam.subject,\n        level: this.questionQueryParam.level,\n        difficulty: undefined,\n        questionType: this.questionType\n      }\n      \n      this.loadQuestions(1)\n    },\n    \n    // 题目表格变化\n    handleQuestionTableChange(pagination) {\n      this.questionPagination.current = pagination.current\n      this.loadQuestions()\n    },\n    \n    // 题目选择变化\n    onQuestionSelectionChange(selectedRowKeys, selectedRows) {\n      this.tempSelectedQuestionKeys = selectedRowKeys\n      this.tempSelectedQuestions = selectedRows\n    },\n    \n    // 更新已选题目的选中状态\n    updateSelectedQuestionKeys() {\n      // 获取当前类型的已选题目ID列表\n      let selectedIds = []\n      switch (this.questionType) {\n        case 1:\n          selectedIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          break\n        case 2:\n          selectedIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          break\n        case 3:\n          selectedIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          break\n      }\n      \n      // 查找当前页面中已选择的题目\n      const currentPageSelectedIds = this.questionList\n        .filter(q => selectedIds.includes(q.id))\n        .map(q => q.id)\n      \n      this.tempSelectedQuestionKeys = currentPageSelectedIds\n    },\n    \n    // 确认题目选择\n    confirmQuestionSelection() {\n      // 根据题目类型，将选择的题目添加到对应的列表中\n      switch (this.questionType) {\n        case 1:\n          // 过滤掉重复的题目\n          const existingSingleChoiceIds = this.selectedQuestions.singleChoiceQuestions.map(q => q.id)\n          const newSingleChoice = this.tempSelectedQuestions.filter(q => !existingSingleChoiceIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.singleChoiceQuestions = [\n            ...this.selectedQuestions.singleChoiceQuestions,\n            ...newSingleChoice\n          ]\n          break\n        case 2:\n          // 过滤掉重复的题目\n          const existingJudgmentIds = this.selectedQuestions.judgmentQuestions.map(q => q.id)\n          const newJudgment = this.tempSelectedQuestions.filter(q => !existingJudgmentIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.judgmentQuestions = [\n            ...this.selectedQuestions.judgmentQuestions,\n            ...newJudgment\n          ]\n          break\n        case 3:\n          // 过滤掉重复的题目\n          const existingProgrammingIds = this.selectedQuestions.programmingQuestions.map(q => q.id)\n          const newProgramming = this.tempSelectedQuestions.filter(q => !existingProgrammingIds.includes(q.id))\n          \n          // 添加新题目\n          this.selectedQuestions.programmingQuestions = [\n            ...this.selectedQuestions.programmingQuestions,\n            ...newProgramming\n          ]\n          break\n      }\n      \n      // 标记表单已变更\n      this.formChanged = true;\n      \n      // 关闭模态框\n      this.handleQuestionSelectorCancel()\n    },\n    \n    // 关闭题目选择器\n    handleQuestionSelectorCancel() {\n      this.questionSelectorVisible = false\n      this.tempSelectedQuestionKeys = []\n      this.tempSelectedQuestions = []\n    },\n    \n    // 移除已选题目\n    removeQuestion(questionType, index) {\n      this.selectedQuestions[questionType].splice(index, 1)\n      \n      // 标记表单已变更\n      this.formChanged = true;\n    },\n    \n    // 提交表单\n    handleOk() {\n      const that = this\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          that.confirmLoading = true\n          \n          // 检查是否有题目\n          const hasQuestions = this.selectedQuestions.singleChoiceQuestions.length > 0 ||\n                              this.selectedQuestions.judgmentQuestions.length > 0 ||\n                              this.selectedQuestions.programmingQuestions.length > 0\n          \n          if (!hasQuestions) {\n            this.$message.warning('请至少添加一道题目')\n            that.confirmLoading = false\n            return\n          }\n          \n          // 处理年份，转为字符串\n          const year = values.year ? moment(values.year).format('YYYY') : moment().format('YYYY')\n          \n          // 构建content字段\n          // 收集所有题目ID和分数\n          const questions = []\n          \n          // 处理单选题\n          this.selectedQuestions.singleChoiceQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.singleChoiceScore || 2\n            })\n          })\n          \n          // 处理判断题\n          this.selectedQuestions.judgmentQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.judgmentScore || 2\n            })\n          })\n          \n          // 处理编程题\n          this.selectedQuestions.programmingQuestions.forEach(question => {\n            questions.push({\n              questionId: question.id,\n              score: values.programmingScore || 25\n            })\n          })\n          \n          const content = {\n            singleChoiceScore: values.singleChoiceScore,\n            judgmentScore: values.judgmentScore,\n            programmingScore: values.programmingScore,\n            questions: questions\n          }\n          \n          // 构建提交数据\n          const params = {\n            ...this.model,\n            title: values.title,\n            subject: values.subject,\n            level: values.level,\n            difficulty: values.difficulty,\n            type: values.type,\n            year: year,\n            author: values.author,\n            examDuration: values.examDuration,\n            content: JSON.stringify(content)\n          }\n          \n          // 发送请求\n          let apiMethod = params.id ? editPaper : addPaper\n          apiMethod(params).then((res) => {\n            if (res.success) {\n              that.$message.success(res.message || (params.id ? '编辑成功' : '添加成功'))\n              that.$emit('ok')\n              that.close()\n            } else {\n              that.$message.warning(res.message || (params.id ? '编辑失败' : '添加失败'))\n            }\n          }).finally(() => {\n            that.confirmLoading = false\n          })\n        }\n      })\n    },\n    \n    // 处理取消操作\n    handleCancel() {\n      // 检查表单是否有变更\n      if (this.hasFormChanged()) {\n        // 有变更，显示确认对话框\n      this.$confirm({\n        title: '提示',\n        content: '还未提交，是否退出？',\n        okText: '是',\n        cancelText: '否',\n        onOk: () => {\n          this.close();\n        }\n      });\n      } else {\n        // 无变更，直接关闭\n        this.close();\n      }\n    },\n    \n    close() {\n      this.visible = false\n      this.questionSelectorVisible = false\n      \n      // 重置选择的题目\n      this.selectedQuestions = {\n        singleChoiceQuestions: [],\n        judgmentQuestions: [],\n        programmingQuestions: []\n      }\n      \n      // 清理事件监听器，避免内存泄漏\n      this.removeFormChangeListener();\n    }\n  }\n}\n</script>\n\n<style scoped>\n</style> "], "mappings": ";;;;;;;;;;;;AA+PA,SACAA,QAAA,EACAC,SAAA,EACAC,cAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,cAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,KAAA;MACAC,QAAA;QAAAC,IAAA;MAAA;MACAC,UAAA;QAAAD,IAAA;MAAA;MACAE,aAAA;QAAAF,IAAA;MAAA;MACAG,eAAA;QAAAH,IAAA;MAAA;MACAI,UAAA;MAEA;MACAC,QAAA;MACAC,UAAA;MAEA;MACAC,eAAA;MACAC,YAAA;MAEA;MACAC,iBAAA;QACAC,qBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,oBAAA;MACA;MAEA;MACAC,kBAAA;MACAC,yBAAA;MACAC,WAAA;MAEA;MACAC,mBAAA,GACA;QAAAxB,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,WAAAA,aAAAC,CAAA,EAAAC,CAAA,EAAAC,KAAA;UAAA,OAAAA,KAAA;QAAA;MAAA,GACA;QAAA9B,KAAA;QAAA0B,SAAA;MAAA,GACA;QAAA1B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,OAAAI;MAAA,GACA;QAAA/B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAM,WAAA;UAAAL,YAAA;QAAA;MAAA,EACA;MACAM,eAAA,GACA;QAAAjC,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,WAAAA,aAAAC,CAAA,EAAAC,CAAA,EAAAC,KAAA;UAAA,OAAAA,KAAA;QAAA;MAAA,GACA;QAAA9B,KAAA;QAAA0B,SAAA;MAAA,GACA;QAAA1B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,OAAAI;MAAA,GACA;QAAA/B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAM,WAAA;UAAAL,YAAA;QAAA;MAAA,EACA;MACAO,kBAAA,GACA;QAAAlC,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,WAAAA,aAAAC,CAAA,EAAAC,CAAA,EAAAC,KAAA;UAAA,OAAAA,KAAA;QAAA;MAAA,GACA;QAAA9B,KAAA;QAAA0B,SAAA;MAAA,GACA;QAAA1B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAC,YAAA,OAAAI;MAAA,GACA;QAAA/B,KAAA;QAAAyB,KAAA;QAAAC,SAAA;QAAAM,WAAA;UAAAL,YAAA;QAAA;MAAA,EACA;MAEA;MACAQ,uBAAA;MACAC,qBAAA;MACAC,YAAA;MAAA;MACAC,kBAAA;QACAtC,KAAA;QACAuC,OAAA;QACAC,KAAA;QACAC,UAAA,EAAAC,SAAA;QACAL,YAAA,EAAAK;MACA;MACAC,YAAA;MACAC,eAAA;MACAC,kBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,wBAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,uBAAA,GACA;QAAAnD,KAAA;QAAA0B,SAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,SAAA;QAAAD,KAAA;QAAAO,WAAA;UAAAL,YAAA;QAAA;MAAA,GACA;QAAA3B,KAAA;QAAA0B,SAAA;QAAAD,KAAA;MAAA,EACA;MAEA;MACA2B,GAAA;QACAC,GAAA;QACAC,IAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA;MACA,KAAApC,kBAAA,QAAAlB,IAAA,CAAAuD,cAAA;;MAEA;MACA,KAAApC,yBAAA;QACAJ,qBAAA,EAAAyC,kBAAA,MAAA1C,iBAAA,CAAAC,qBAAA;QACAC,iBAAA,EAAAwC,kBAAA,MAAA1C,iBAAA,CAAAE,iBAAA;QACAC,oBAAA,EAAAuC,kBAAA,MAAA1C,iBAAA,CAAAG,oBAAA;MACA;;MAEA;MACA,KAAAG,WAAA;IACA;IAEA;IACAqC,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACA;MACA,KAAAC,wBAAA;;MAEA;MACA,IAAAC,sBAAA,YAAAA,uBAAA;QACA;QACAF,KAAA,CAAAtC,WAAA;MACA;;MAEA;MACA,KAAAwC,sBAAA,GAAAA,sBAAA;;MAEA;MACA,KAAAC,SAAA;QACA;QACA,IAAAC,UAAA,GAAAJ,KAAA,CAAAK,GAAA,CAAAC,gBAAA;;QAEA;QACAF,UAAA,CAAAG,OAAA,WAAAC,OAAA;UACAA,OAAA,CAAAC,gBAAA,WAAAT,KAAA,CAAAE,sBAAA;UACAM,OAAA,CAAAC,gBAAA,UAAAT,KAAA,CAAAE,sBAAA;QACA;;QAEA;QACA,IAAAQ,eAAA,GAAAV,KAAA,CAAAK,GAAA,CAAAC,gBAAA;QACAI,eAAA,CAAAH,OAAA,WAAAC,OAAA;UACAA,OAAA,CAAAC,gBAAA,UAAAT,KAAA,CAAAE,sBAAA;QACA;MACA;IACA;IAEA;IACAD,wBAAA,WAAAA,yBAAA;MAAA,IAAAU,MAAA;MACA,SAAAT,sBAAA;QACA,KAAAC,SAAA;UACA;UACA,IAAAC,UAAA,GAAAO,MAAA,CAAAN,GAAA,CAAAC,gBAAA;;UAEA;UACAF,UAAA,CAAAG,OAAA,WAAAC,OAAA;YACAA,OAAA,CAAAI,mBAAA,WAAAD,MAAA,CAAAT,sBAAA;YACAM,OAAA,CAAAI,mBAAA,UAAAD,MAAA,CAAAT,sBAAA;UACA;;UAEA;UACA,IAAAQ,eAAA,GAAAC,MAAA,CAAAN,GAAA,CAAAC,gBAAA;UACAI,eAAA,CAAAH,OAAA,WAAAC,OAAA;YACAA,OAAA,CAAAI,mBAAA,UAAAD,MAAA,CAAAT,sBAAA;UACA;QACA;MACA;IACA;IAEA;IACAW,cAAA,WAAAA,eAAA;MACA,SAAAnD,WAAA;;MAEA;MACA,SAAAD,yBAAA;QACA;QACA,IACA,KAAAL,iBAAA,CAAAC,qBAAA,CAAAyD,MAAA,UAAArD,yBAAA,CAAAJ,qBAAA,CAAAyD,MAAA,IACA,KAAA1D,iBAAA,CAAAE,iBAAA,CAAAwD,MAAA,UAAArD,yBAAA,CAAAH,iBAAA,CAAAwD,MAAA,IACA,KAAA1D,iBAAA,CAAAG,oBAAA,CAAAuD,MAAA,UAAArD,yBAAA,CAAAF,oBAAA,CAAAuD,MAAA,EACA;UACA;QACA;;QAEA;QACA,IAAAC,gBAAA,QAAA3D,iBAAA,CAAAC,qBAAA,CAAA2D,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QACA,IAAAC,iBAAA,QAAA5D,yBAAA,CAAAJ,qBAAA,CAAA2D,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QAEA,IAAAE,kBAAA,QAAAlE,iBAAA,CAAAE,iBAAA,CAAA0D,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QACA,IAAAG,mBAAA,QAAA9D,yBAAA,CAAAH,iBAAA,CAAA0D,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QAEA,IAAAI,qBAAA,QAAApE,iBAAA,CAAAG,oBAAA,CAAAyD,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QACA,IAAAK,sBAAA,QAAAhE,yBAAA,CAAAF,oBAAA,CAAAyD,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA,GAAAC,IAAA,GAAAC,IAAA;QAEA,IACAL,gBAAA,KAAAM,iBAAA,IACAC,kBAAA,KAAAC,mBAAA,IACAC,qBAAA,KAAAC,sBAAA,EACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAjC,GAAA,WAAAA,IAAA;MAAA,IAAAkC,MAAA;MACA,KAAApF,IAAA,CAAAqF,WAAA;MACA,KAAAlF,KAAA;MACA,KAAAL,OAAA;MACA,KAAAD,KAAA;;MAEA;MACA,KAAAiB,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,oBAAA;MACA;MAEA,KAAA4C,SAAA;QACA;QACAuB,MAAA,CAAApF,IAAA,CAAAsF,cAAA;UACAC,iBAAA;UACAC,aAAA;UACAC,gBAAA;UACAC,YAAA;QACA;;QAEA;QACAN,MAAA,CAAA9B,sBAAA;;QAEA;QACA8B,MAAA,CAAA3B,qBAAA;MACA;IACA;IAEA;IACAN,IAAA,WAAAA,KAAAwC,MAAA;MAAA,IAAAC,MAAA;MACA,KAAA5F,IAAA,CAAAqF,WAAA;MACA,KAAAlF,KAAA,GAAA0F,MAAA,CAAAC,MAAA,KAAAH,MAAA;MACA,KAAA7F,OAAA;MACA,KAAAD,KAAA;;MAEA;MACA,KAAAiB,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,oBAAA;MACA;MAEA,KAAAlB,cAAA;;MAEA;MACAR,cAAA,CAAAoG,MAAA,CAAAf,EAAA,EAAAmB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA,IAAAC,KAAA,GAAAF,GAAA,CAAAG,MAAA;;UAEA;UACAP,MAAA,CAAAhF,eAAA,GAAAsF,KAAA,CAAA9D,OAAA;UACAwD,MAAA,CAAAQ,kBAAA;;UAEA;UACA,IAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA,GAAA3G,MAAA,CAAAwG,KAAA,CAAAG,IAAA,CAAAC,QAAA;UAEA,IAAAC,UAAA;YACA3B,EAAA,EAAAsB,KAAA,CAAAtB,EAAA;YACA/E,KAAA,EAAAqG,KAAA,CAAArG,KAAA;YACAuC,OAAA,EAAA8D,KAAA,CAAA9D,OAAA;YACAC,KAAA,EAAA6D,KAAA,CAAA7D,KAAA;YACAC,UAAA,EAAA4D,KAAA,CAAA5D,UAAA;YACAkE,IAAA,EAAAN,KAAA,CAAAM,IAAA;YACAH,IAAA,EAAAA,IAAA;YACAI,MAAA,EAAAP,KAAA,CAAAO,MAAA;YACAf,YAAA,EAAAQ,KAAA,CAAAR,YAAA;UACA;;UAEA;UACA,IAAAQ,KAAA,CAAAQ,OAAA;YACA;cACA,IAAAA,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAV,KAAA,CAAAQ,OAAA;;cAEA;cACAH,UAAA,CAAAhB,iBAAA,GAAAmB,OAAA,CAAAnB,iBAAA;cACAgB,UAAA,CAAAf,aAAA,GAAAkB,OAAA,CAAAlB,aAAA;cACAe,UAAA,CAAAd,gBAAA,GAAAiB,OAAA,CAAAjB,gBAAA;;cAEA;cACAG,MAAA,CAAAiB,kBAAA,CAAAX,KAAA,CAAAtB,EAAA,EAAA8B,OAAA;YACA,SAAAI,CAAA;cACAC,OAAA,CAAAC,KAAA,aAAAF,CAAA;cACAlB,MAAA,CAAA7F,cAAA;YACA;UACA;YACA;YACAwG,UAAA,CAAAhB,iBAAA;YACAgB,UAAA,CAAAf,aAAA;YACAe,UAAA,CAAAd,gBAAA;YACAG,MAAA,CAAA7F,cAAA;UACA;UAEA6F,MAAA,CAAA/B,SAAA;YACA+B,MAAA,CAAA5F,IAAA,CAAAsF,cAAA,CAAAiB,UAAA;;YAEA;YACAX,MAAA,CAAAtC,sBAAA;;YAEA;YACAsC,MAAA,CAAAnC,qBAAA;UACA;QACA;UACAmC,MAAA,CAAAqB,QAAA,CAAAC,OAAA,CAAAlB,GAAA,CAAAmB,OAAA;UACAvB,MAAA,CAAA7F,cAAA;QACA;MACA,GAAAqH,KAAA;QACAxB,MAAA,CAAAqB,QAAA,CAAAC,OAAA;QACAtB,MAAA,CAAA7F,cAAA;MACA;IACA;IAEA;IACA8G,kBAAA,WAAAA,mBAAAQ,OAAA,EAAAX,OAAA;MAAA,IAAAY,MAAA;MACA;MACA7H,iBAAA,CAAA4H,OAAA,EAAAtB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA;UACA,IAAAsB,YAAA,GAAAvB,GAAA,CAAAG,MAAA;;UAEA;UACA,IAAAqB,QAAA;UACA,IAAAd,OAAA,IAAAA,OAAA,CAAAe,SAAA;YACAf,OAAA,CAAAe,SAAA,CAAAxD,OAAA,WAAAyD,IAAA;cACAF,QAAA,CAAAE,IAAA,CAAAC,UAAA,IAAAD,IAAA,CAAAE,KAAA;YACA;UACA;;UAEA;UACAL,YAAA,CAAAtD,OAAA,WAAAyD,IAAA;YACA,IAAAG,QAAA,GAAAH,IAAA,CAAAG,QAAA;YACA,IAAAD,KAAA,GAAAF,IAAA,CAAAE,KAAA;;YAEA;YACA,IAAAC,QAAA;cACA;cACA,QAAAA,QAAA,CAAA3F,YAAA;gBACA;kBAAA;kBACAoF,MAAA,CAAAxG,iBAAA,CAAAC,qBAAA,CAAA+G,IAAA,CAAAD,QAAA;kBACA;gBACA;kBAAA;kBACAP,MAAA,CAAAxG,iBAAA,CAAAE,iBAAA,CAAA8G,IAAA,CAAAD,QAAA;kBACA;gBACA;kBAAA;kBACAP,MAAA,CAAAxG,iBAAA,CAAAG,oBAAA,CAAA6G,IAAA,CAAAD,QAAA;kBACA;cACA;YACA;UACA;;UAEA;UACAP,MAAA,CAAAhE,sBAAA;QACA;UACAgE,MAAA,CAAAL,QAAA,CAAAC,OAAA;QACA;QACAI,MAAA,CAAAvH,cAAA;MACA,GAAAqH,KAAA;QACAE,MAAA,CAAAL,QAAA,CAAAC,OAAA;QACAI,MAAA,CAAAvH,cAAA;MACA;IACA;IAEA;IACAgI,IAAA,WAAAA,KAAApC,MAAA;MAAA,IAAAqC,MAAA;MACA,KAAAhI,IAAA,CAAAqF,WAAA;MACA,KAAAlF,KAAA;MACA,KAAAL,OAAA;MACA,KAAAD,KAAA;;MAEA;MACA,KAAAiB,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,oBAAA;MACA;MAEA,KAAAlB,cAAA;MAEAR,cAAA,CAAAoG,MAAA,CAAAf,EAAA,EAAAmB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACA,IAAAC,KAAA,GAAAF,GAAA,CAAAG,MAAA;;UAEA;UACA6B,MAAA,CAAApH,eAAA,GAAAsF,KAAA,CAAA9D,OAAA;UACA4F,MAAA,CAAA5B,kBAAA;;UAEA;UACA,IAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA,GAAA3G,MAAA,CAAAwG,KAAA,CAAAG,IAAA,CAAAC,QAAA;UAEA,IAAAC,UAAA;YACA1G,KAAA,EAAAqG,KAAA,CAAArG,KAAA;YACAuC,OAAA,EAAA8D,KAAA,CAAA9D,OAAA;YACAC,KAAA,EAAA6D,KAAA,CAAA7D,KAAA;YACAC,UAAA,EAAA4D,KAAA,CAAA5D,UAAA;YACAkE,IAAA,EAAAN,KAAA,CAAAM,IAAA;YACAH,IAAA,EAAAA,IAAA;YACAI,MAAA,EAAAP,KAAA,CAAAO,MAAA;YACAf,YAAA,EAAAQ,KAAA,CAAAR,YAAA;UACA;;UAEA;UACA,IAAAQ,KAAA,CAAAQ,OAAA;YACA;cACA,IAAAA,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAV,KAAA,CAAAQ,OAAA;;cAEA;cACAH,UAAA,CAAAhB,iBAAA,GAAAmB,OAAA,CAAAnB,iBAAA;cACAgB,UAAA,CAAAf,aAAA,GAAAkB,OAAA,CAAAlB,aAAA;cACAe,UAAA,CAAAd,gBAAA,GAAAiB,OAAA,CAAAjB,gBAAA;;cAEA;cACA,IAAAiB,OAAA,CAAA3F,qBAAA;gBACAiH,MAAA,CAAAlH,iBAAA,CAAAC,qBAAA,GAAA2F,OAAA,CAAA3F,qBAAA;cACA;cACA,IAAA2F,OAAA,CAAA1F,iBAAA;gBACAgH,MAAA,CAAAlH,iBAAA,CAAAE,iBAAA,GAAA0F,OAAA,CAAA1F,iBAAA;cACA;cACA,IAAA0F,OAAA,CAAAzF,oBAAA;gBACA+G,MAAA,CAAAlH,iBAAA,CAAAG,oBAAA,GAAAyF,OAAA,CAAAzF,oBAAA;cACA;YACA,SAAA6F,CAAA;cACAC,OAAA,CAAAC,KAAA,aAAAF,CAAA;YACA;UACA;YACA;YACAP,UAAA,CAAAhB,iBAAA;YACAgB,UAAA,CAAAf,aAAA;YACAe,UAAA,CAAAd,gBAAA;UACA;UAEAuC,MAAA,CAAAnE,SAAA;YACAmE,MAAA,CAAAhI,IAAA,CAAAsF,cAAA,CAAAiB,UAAA;;YAEA;YACAyB,MAAA,CAAA1E,sBAAA;;YAEA;YACA0E,MAAA,CAAAvE,qBAAA;UACA;QACA;QACAuE,MAAA,CAAAjI,cAAA;MACA,GAAAqH,KAAA;QACAY,MAAA,CAAAjI,cAAA;MACA;IACA;IAEA;IACAkI,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAAvH,eAAA,GAAAsH,KAAA;MACA,KAAA9B,kBAAA;;MAEA;MACA,KAAAvC,SAAA;QACAsE,MAAA,CAAAnI,IAAA,CAAAsF,cAAA;UACAjD,KAAA,EAAAE;QACA;MACA;IACA;IAEA;IACA6F,aAAA,WAAAA,cAAAF,KAAA;MAAA,IAAAG,MAAA;MACA;MACA,IAAAH,KAAA;QACA,IAAAxC,YAAA,QAAA4C,sBAAA,CAAAJ,KAAA;QACA,KAAArE,SAAA;UACAwE,MAAA,CAAArI,IAAA,CAAAsF,cAAA;YACAI,YAAA,EAAAA;UACA;QACA;MACA;IACA;IAEA;IACAU,kBAAA,WAAAA,mBAAA;MACA,SAAAxF,eAAA;QACA;QACA,KAAAC,YAAA;MACA,gBAAAD,eAAA,sBAAAA,eAAA;QACA;QACA,KAAAC,YAAA;MACA;QACA,KAAAA,YAAA;MACA;IACA;IAEA;IACAyH,sBAAA,WAAAA,uBAAAjG,KAAA;MACA;MACA;;MAEA;MACA,IAAAA,KAAA;QACA,IAAAA,KAAA,CAAAkG,UAAA,SAAAlG,KAAA,CAAAkG,UAAA,SAAAlG,KAAA,CAAAkG,UAAA,SAAAlG,KAAA,CAAAkG,UAAA;UACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAN,KAAA,EAAAO,IAAA;MACA,KAAAzI,IAAA,CAAAsF,cAAA;QAAAe,IAAA,EAAA6B;MAAA;MACA,KAAAxH,QAAA;IACA;IAEA;IACAgI,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,UAAA;MACA,KAAA5I,IAAA,CAAAsF,cAAA;QAAAe,IAAA,EAAAsC;MAAA;IACA;IAEA;IACA/G,gBAAA,WAAAA,iBAAAU,UAAA;MAAA,IAAAuG,CAAA,QAAAC,cAAA;MACA,IAAAC,KAAA;MACA,IAAAC,IAAA;MAEA,IAAA1G,UAAA;QACAyG,KAAA;QACAC,IAAA;MACA,WAAA1G,UAAA;QACAyG,KAAA;QACAC,IAAA;MACA,WAAA1G,UAAA;QACAyG,KAAA;QACAC,IAAA;MACA;MAEA,OAAAH,CAAA;QAAA;UAAA,SAAAE;QAAA;MAAA,IAAAC,IAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAAzC,IAAA;MACA,KAAAtE,YAAA,GAAAsE,IAAA;;MAEA;MACA,QAAAA,IAAA;QACA;UACA,KAAAvE,qBAAA;UACA;QACA;UACA,KAAAA,qBAAA;UACA;QACA;UACA,KAAAA,qBAAA;UACA;MACA;;MAEA;MACA,IAAAiH,MAAA,QAAAlJ,IAAA,CAAAuD,cAAA;MAEA,KAAA2F,MAAA,CAAA9G,OAAA,KAAA8G,MAAA,CAAA7G,KAAA;QACA,KAAA4E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAA/E,kBAAA;QACAtC,KAAA;QACAuC,OAAA,EAAA8G,MAAA,CAAA9G,OAAA;QACAC,KAAA,EAAA6G,MAAA,CAAA7G,KAAA;QACAC,UAAA,EAAAC,SAAA;QACAL,YAAA,OAAAA;MACA;MAEA,KAAAQ,kBAAA,CAAAC,OAAA;MACA,KAAAG,wBAAA;MACA,KAAAC,qBAAA;;MAEA;MACA,KAAAf,uBAAA;;MAEA;MACA,KAAAmH,aAAA;IACA;IAEA;IACAA,aAAA,WAAAA,cAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,IAAA;QACA,KAAA1G,kBAAA,CAAAC,OAAA,GAAAyG,IAAA;MACA;MAEA,KAAA3G,eAAA;MAEA,IAAA6G,MAAA,GAAAC,aAAA,CAAAA,aAAA,KACA,KAAApH,kBAAA;QACAqH,MAAA,OAAA9G,kBAAA,CAAAC,OAAA;QACAC,QAAA,OAAAF,kBAAA,CAAAE;MAAA,EACA;;MAEA;MACApD,cAAA,CAAA8J,MAAA,EAAAvD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAoD,MAAA,CAAA7G,YAAA,GAAAwD,GAAA,CAAAG,MAAA,CAAAsD,OAAA;UACAJ,MAAA,CAAA3G,kBAAA,CAAAG,KAAA,GAAAmD,GAAA,CAAAG,MAAA,CAAAtD,KAAA;;UAEA;UACAwG,MAAA,CAAAK,0BAAA;QACA;UACAL,MAAA,CAAApC,QAAA,CAAAC,OAAA,CAAAlB,GAAA,CAAAmB,OAAA;QACA;QACAkC,MAAA,CAAA5G,eAAA;MACA,GAAA2E,KAAA;QACAiC,MAAA,CAAA5G,eAAA;MACA;IACA;IAEA;IACAkH,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAxH,kBAAA;QACAtC,KAAA;QACAuC,OAAA,OAAAD,kBAAA,CAAAC,OAAA;QACAC,KAAA,OAAAF,kBAAA,CAAAE,KAAA;QACAC,UAAA,EAAAC,SAAA;QACAL,YAAA,OAAAA;MACA;MAEA,KAAAiH,aAAA;IACA;IAEA;IACAS,yBAAA,WAAAA,0BAAAC,UAAA;MACA,KAAAnH,kBAAA,CAAAC,OAAA,GAAAkH,UAAA,CAAAlH,OAAA;MACA,KAAAwG,aAAA;IACA;IAEA;IACAW,yBAAA,WAAAA,0BAAAC,eAAA,EAAAC,YAAA;MACA,KAAAlH,wBAAA,GAAAiH,eAAA;MACA,KAAAhH,qBAAA,GAAAiH,YAAA;IACA;IAEA;IACAN,0BAAA,WAAAA,2BAAA;MACA;MACA,IAAAO,WAAA;MACA,aAAA/H,YAAA;QACA;UACA+H,WAAA,QAAAnJ,iBAAA,CAAAC,qBAAA,CAAA2D,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA;QACA;UACAqF,WAAA,QAAAnJ,iBAAA,CAAAE,iBAAA,CAAA0D,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA;QACA;UACAqF,WAAA,QAAAnJ,iBAAA,CAAAG,oBAAA,CAAAyD,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA;MACA;;MAEA;MACA,IAAAsF,sBAAA,QAAA1H,YAAA,CACA2H,MAAA,WAAAxF,CAAA;QAAA,OAAAsF,WAAA,CAAAG,QAAA,CAAAzF,CAAA,CAAAC,EAAA;MAAA,GACAF,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA;MAAA;MAEA,KAAA9B,wBAAA,GAAAoH,sBAAA;IACA;IAEA;IACAG,wBAAA,WAAAA,yBAAA;MACA;MACA,aAAAnI,YAAA;QACA;UACA;UACA,IAAAoI,uBAAA,QAAAxJ,iBAAA,CAAAC,qBAAA,CAAA2D,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA,IAAA2F,eAAA,QAAAxH,qBAAA,CAAAoH,MAAA,WAAAxF,CAAA;YAAA,QAAA2F,uBAAA,CAAAF,QAAA,CAAAzF,CAAA,CAAAC,EAAA;UAAA;;UAEA;UACA,KAAA9D,iBAAA,CAAAC,qBAAA,MAAAyJ,MAAA,CAAAhH,kBAAA,CACA,KAAA1C,iBAAA,CAAAC,qBAAA,GAAAyC,kBAAA,CACA+G,eAAA,EACA;UACA;QACA;UACA;UACA,IAAAE,mBAAA,QAAA3J,iBAAA,CAAAE,iBAAA,CAAA0D,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA,IAAA8F,WAAA,QAAA3H,qBAAA,CAAAoH,MAAA,WAAAxF,CAAA;YAAA,QAAA8F,mBAAA,CAAAL,QAAA,CAAAzF,CAAA,CAAAC,EAAA;UAAA;;UAEA;UACA,KAAA9D,iBAAA,CAAAE,iBAAA,MAAAwJ,MAAA,CAAAhH,kBAAA,CACA,KAAA1C,iBAAA,CAAAE,iBAAA,GAAAwC,kBAAA,CACAkH,WAAA,EACA;UACA;QACA;UACA;UACA,IAAAC,sBAAA,QAAA7J,iBAAA,CAAAG,oBAAA,CAAAyD,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA;UACA,IAAAgG,cAAA,QAAA7H,qBAAA,CAAAoH,MAAA,WAAAxF,CAAA;YAAA,QAAAgG,sBAAA,CAAAP,QAAA,CAAAzF,CAAA,CAAAC,EAAA;UAAA;;UAEA;UACA,KAAA9D,iBAAA,CAAAG,oBAAA,MAAAuJ,MAAA,CAAAhH,kBAAA,CACA,KAAA1C,iBAAA,CAAAG,oBAAA,GAAAuC,kBAAA,CACAoH,cAAA,EACA;UACA;MACA;;MAEA;MACA,KAAAxJ,WAAA;;MAEA;MACA,KAAAyJ,4BAAA;IACA;IAEA;IACAA,4BAAA,WAAAA,6BAAA;MACA,KAAA7I,uBAAA;MACA,KAAAc,wBAAA;MACA,KAAAC,qBAAA;IACA;IAEA;IACA+H,cAAA,WAAAA,eAAA5I,YAAA,EAAAP,KAAA;MACA,KAAAb,iBAAA,CAAAoB,YAAA,EAAA6I,MAAA,CAAApJ,KAAA;;MAEA;MACA,KAAAP,WAAA;IACA;IAEA;IACA4J,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,IAAA;MACA,KAAAlL,IAAA,CAAAmL,cAAA,WAAAC,GAAA,EAAAlC,MAAA;QACA,KAAAkC,GAAA;UACAF,IAAA,CAAAnL,cAAA;;UAEA;UACA,IAAAsL,YAAA,GAAAJ,OAAA,CAAAnK,iBAAA,CAAAC,qBAAA,CAAAyD,MAAA,QACAyG,OAAA,CAAAnK,iBAAA,CAAAE,iBAAA,CAAAwD,MAAA,QACAyG,OAAA,CAAAnK,iBAAA,CAAAG,oBAAA,CAAAuD,MAAA;UAEA,KAAA6G,YAAA;YACAJ,OAAA,CAAAhE,QAAA,CAAAC,OAAA;YACAgE,IAAA,CAAAnL,cAAA;YACA;UACA;;UAEA;UACA,IAAAsG,IAAA,GAAA6C,MAAA,CAAA7C,IAAA,GAAA3G,MAAA,CAAAwJ,MAAA,CAAA7C,IAAA,EAAAiF,MAAA,WAAA5L,MAAA,GAAA4L,MAAA;;UAEA;UACA;UACA,IAAA7D,SAAA;;UAEA;UACAwD,OAAA,CAAAnK,iBAAA,CAAAC,qBAAA,CAAAkD,OAAA,WAAA4D,QAAA;YACAJ,SAAA,CAAAK,IAAA;cACAH,UAAA,EAAAE,QAAA,CAAAjD,EAAA;cACAgD,KAAA,EAAAsB,MAAA,CAAA3D,iBAAA;YACA;UACA;;UAEA;UACA0F,OAAA,CAAAnK,iBAAA,CAAAE,iBAAA,CAAAiD,OAAA,WAAA4D,QAAA;YACAJ,SAAA,CAAAK,IAAA;cACAH,UAAA,EAAAE,QAAA,CAAAjD,EAAA;cACAgD,KAAA,EAAAsB,MAAA,CAAA1D,aAAA;YACA;UACA;;UAEA;UACAyF,OAAA,CAAAnK,iBAAA,CAAAG,oBAAA,CAAAgD,OAAA,WAAA4D,QAAA;YACAJ,SAAA,CAAAK,IAAA;cACAH,UAAA,EAAAE,QAAA,CAAAjD,EAAA;cACAgD,KAAA,EAAAsB,MAAA,CAAAzD,gBAAA;YACA;UACA;UAEA,IAAAiB,OAAA;YACAnB,iBAAA,EAAA2D,MAAA,CAAA3D,iBAAA;YACAC,aAAA,EAAA0D,MAAA,CAAA1D,aAAA;YACAC,gBAAA,EAAAyD,MAAA,CAAAzD,gBAAA;YACAgC,SAAA,EAAAA;UACA;;UAEA;UACA,IAAA6B,MAAA,GAAAC,aAAA,CAAAA,aAAA,KACA0B,OAAA,CAAA9K,KAAA;YACAN,KAAA,EAAAqJ,MAAA,CAAArJ,KAAA;YACAuC,OAAA,EAAA8G,MAAA,CAAA9G,OAAA;YACAC,KAAA,EAAA6G,MAAA,CAAA7G,KAAA;YACAC,UAAA,EAAA4G,MAAA,CAAA5G,UAAA;YACAkE,IAAA,EAAA0C,MAAA,CAAA1C,IAAA;YACAH,IAAA,EAAAA,IAAA;YACAI,MAAA,EAAAyC,MAAA,CAAAzC,MAAA;YACAf,YAAA,EAAAwD,MAAA,CAAAxD,YAAA;YACAgB,OAAA,EAAAC,IAAA,CAAA4E,SAAA,CAAA7E,OAAA;UAAA,EACA;;UAEA;UACA,IAAA8E,SAAA,GAAAlC,MAAA,CAAA1E,EAAA,GAAAtF,SAAA,GAAAD,QAAA;UACAmM,SAAA,CAAAlC,MAAA,EAAAvD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAiF,IAAA,CAAAjE,QAAA,CAAAhB,OAAA,CAAAD,GAAA,CAAAmB,OAAA,KAAAmC,MAAA,CAAA1E,EAAA;cACAsG,IAAA,CAAAO,KAAA;cACAP,IAAA,CAAAQ,KAAA;YACA;cACAR,IAAA,CAAAjE,QAAA,CAAAC,OAAA,CAAAlB,GAAA,CAAAmB,OAAA,KAAAmC,MAAA,CAAA1E,EAAA;YACA;UACA,GAAA+G,OAAA;YACAT,IAAA,CAAAnL,cAAA;UACA;QACA;MACA;IACA;IAEA;IACA6L,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA;MACA,SAAAtH,cAAA;QACA;QACA,KAAAuH,QAAA;UACAjM,KAAA;UACA6G,OAAA;UACAqF,MAAA;UACAC,UAAA;UACAC,IAAA,WAAAA,KAAA;YACAJ,OAAA,CAAAH,KAAA;UACA;QACA;MACA;QACA;QACA,KAAAA,KAAA;MACA;IACA;IAEAA,KAAA,WAAAA,MAAA;MACA,KAAA5L,OAAA;MACA,KAAAkC,uBAAA;;MAEA;MACA,KAAAlB,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,oBAAA;MACA;;MAEA;MACA,KAAA0C,wBAAA;IACA;EACA;AACA", "ignoreList": []}]}