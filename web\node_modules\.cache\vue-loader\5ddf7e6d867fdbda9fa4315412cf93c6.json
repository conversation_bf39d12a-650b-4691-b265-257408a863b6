{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue?vue&type=style&index=0&id=6d5cf9b0&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step2.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.stepFormText {\n  margin-bottom: 24px;\n\n  .ant-form-item-label,\n  .ant-form-item-control {\n    line-height: 22px;\n  }\n}\n\n", {"version": 3, "sources": ["Step2.vue"], "names": [], "mappings": ";AAkFA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Step2.vue", "sourceRoot": "src/views/form/stepForm", "sourcesContent": ["<template>\n  <div>\n    <a-form style=\"max-width: 500px; margin: 40px auto 0;\">\n      <a-alert\n        :closable=\"true\"\n        message=\"确认转账后，资金将直接打入对方账户，无法退回。\"\n        style=\"margin-bottom: 24px;\"\n      />\n      <a-form-item\n        label=\"付款账户\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n        class=\"stepFormText\"\n      >\n        <EMAIL>\n      </a-form-item>\n      <a-form-item\n        label=\"收款账户\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n        class=\"stepFormText\"\n      >\n        <EMAIL>\n      </a-form-item>\n      <a-form-item\n        label=\"收款人姓名\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n        class=\"stepFormText\"\n      >\n        Alex\n      </a-form-item>\n      <a-form-item\n        label=\"转账金额\"\n        :labelCol=\"{span: 5}\"\n        :wrapperCol=\"{span: 19}\"\n        class=\"stepFormText\"\n      >\n        ￥ 5,000.00\n      </a-form-item>\n      <a-form-item :wrapperCol=\"{span: 19, offset: 5}\">\n        <a-button :loading=\"loading\" type=\"primary\" @click=\"nextStep\">提交</a-button>\n        <a-button style=\"margin-left: 8px\" @click=\"prevStep\">上一步</a-button>\n      </a-form-item>\n    </a-form>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Step2\",\n    data () {\n      return {\n        loading: false\n      }\n    },\n    methods: {\n      nextStep () {\n        let that = this\n        that.loading = true\n        setTimeout(function () {\n          that.$emit('nextStep')\n        }, 1500)\n      },\n      prevStep () {\n        this.$emit('prevStep')\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .stepFormText {\n    margin-bottom: 24px;\n\n    .ant-form-item-label,\n    .ant-form-item-control {\n      line-height: 22px;\n    }\n  }\n\n</style>"]}]}