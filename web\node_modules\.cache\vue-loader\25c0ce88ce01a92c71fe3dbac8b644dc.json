{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue?vue&type=template&id=4a6dc917&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n      <a-row :gutter=\"{ xs: 8, sm: 16, md: 24, lg: 32 }\">\n        <a-col :span=\"12\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"模板CODE\"\n            style=\"margin-right: -35px\"\n          >\n            <a-input\n              :disabled=\"disable\"\n              placeholder=\"请输入模板编码\"\n              v-decorator=\"['templateCode', validatorRules.templateCode ]\"\n            />\n          </a-form-item>\n        </a-col>\n        <a-col :span=\"12\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"模板类型\">\n            <j-dict-select-tag  @change=\"handleChangeTemplateType\" :triggerChange=\"true\" dictCode=\"msgType\" v-decorator=\"['templateType', validatorRules.templateType ]\" placeholder=\"请选择模板类型\">\n            </j-dict-select-tag>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      <a-row class=\"form-row\" :gutter=\"24\" >\n        <a-col :span=\"24\" pull=\"2\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"模板标题\"\n            style=\"margin-left: -15px\">\n            <a-input\n              placeholder=\"请输入模板标题\"\n              v-decorator=\"['templateName', validatorRules.templateName]\"\n              style=\"width: 122%\"\n            />\n          </a-form-item>\n        </a-col>\n      </a-row>\n      <a-row class=\"form-row\" :gutter=\"24\">\n        <a-col :span=\"24\" pull=\"4\">\n          <a-form-item\n            v-show=\"!useEditor\"\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"模板内容\"\n            style=\"margin-left: 4px;width: 126%\">\n            <a-textarea placeholder=\"请输入模板内容\" v-decorator=\"['templateContent', validatorRules.templateContent ]\" :autosize=\"{ minRows: 8, maxRows: 8 }\"/>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      <a-row class=\"form-row\" :gutter=\"24\">\n        <a-col :span=\"24\" pull=\"4\">\n          <a-form-item\n            v-show=\"useEditor\"\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"模板内容\"\n            style=\"margin-left: 4px;width: 126%\">\n            <j-editor  v-model=\"templateEditorContent\"></j-editor>\n          </a-form-item>\n        </a-col>\n      </a-row>\n\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}