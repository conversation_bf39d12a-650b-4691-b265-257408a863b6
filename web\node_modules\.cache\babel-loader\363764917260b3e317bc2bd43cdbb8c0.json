{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ReadOnlyTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ReadOnlyTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { FormTypes } from '@/utils/JEditableTableUtil';\nimport JEditableTable from '@/components/jeecg/JEditableTable';\nexport default {\n  name: 'ReadOnlyTable',\n  components: {\n    JEditableTable: JEditableTable\n  },\n  data: function data() {\n    return {\n      columns: [{\n        title: '输入框',\n        key: 'input',\n        type: FormTypes.input,\n        placeholder: '清输入'\n      }, {\n        title: '下拉框',\n        key: 'select',\n        type: FormTypes.select,\n        options: [{\n          title: 'String',\n          value: 'string'\n        }, {\n          title: 'Integer',\n          value: 'int'\n        }, {\n          title: 'Double',\n          value: 'double'\n        }, {\n          title: 'Boolean',\n          value: 'boolean'\n        }],\n        placeholder: '请选择'\n      }, {\n        title: '多选框',\n        key: 'checkbox',\n        type: FormTypes.checkbox,\n        customValue: [true, false]\n      }, {\n        title: '日期',\n        key: 'datetime',\n        type: FormTypes.datetime\n      }],\n      dataSource: [{\n        input: 'hello',\n        select: 'int',\n        checkbox: true,\n        datetime: '2019-6-17 14:50:48'\n      }, {\n        input: 'world',\n        select: 'string',\n        checkbox: false,\n        datetime: '2019-6-16 14:50:48'\n      }, {\n        input: 'one',\n        select: 'double',\n        checkbox: true,\n        datetime: '2019-6-17 15:50:48'\n      }, {\n        input: 'two',\n        select: 'boolean',\n        checkbox: false,\n        datetime: '2019-6-14 14:50:48'\n      }, {\n        input: 'three',\n        select: '',\n        checkbox: false,\n        datetime: '2019-6-13 14:50:48'\n      }]\n    };\n  },\n  mounted: function mounted() {}\n};", {"version": 3, "names": ["FormTypes", "JEditableTable", "name", "components", "data", "columns", "title", "key", "type", "input", "placeholder", "select", "options", "value", "checkbox", "customValue", "datetime", "dataSource", "mounted"], "sources": ["src/views/jeecg/modules/JEditableTable/ReadOnlyTable.vue"], "sourcesContent": ["<template>\n  <j-editable-table\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :rowNumber=\"true\"\n    :rowSelection=\"true\"\n    :maxHeight=\"400\"\n    :disabled=\"true\"\n  />\n</template>\n\n<script>\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'ReadOnlyTable',\n    components: { JEditableTable },\n    data() {\n      return {\n\n        columns: [\n          {\n            title: '输入框',\n            key: 'input',\n            type: FormTypes.input,\n            placeholder: '清输入'\n          },\n          {\n            title: '下拉框',\n            key: 'select',\n            type: FormTypes.select,\n            options: [\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            placeholder: '请选择'\n          },\n          {\n            title: '多选框',\n            key: 'checkbox',\n            type: FormTypes.checkbox,\n            customValue: [true, false]\n          },\n          {\n            title: '日期',\n            key: 'datetime',\n            type: FormTypes.datetime\n          }\n        ],\n        dataSource: [\n          { input: 'hello', select: 'int', checkbox: true, datetime: '2019-6-17 14:50:48' },\n          { input: 'world', select: 'string', checkbox: false, datetime: '2019-6-16 14:50:48' },\n          { input: 'one', select: 'double', checkbox: true, datetime: '2019-6-17 15:50:48' },\n          { input: 'two', select: 'boolean', checkbox: false, datetime: '2019-6-14 14:50:48' },\n          { input: 'three', select: '', checkbox: false, datetime: '2019-6-13 14:50:48' }\n        ]\n      }\n    },\n    mounted() {\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAYA,SAAAA,SAAA;AACA,OAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,cAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MAEAC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAS,KAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAW,MAAA;QACAC,OAAA,GACA;UAAAN,KAAA;UAAAO,KAAA;QAAA,GACA;UAAAP,KAAA;UAAAO,KAAA;QAAA,GACA;UAAAP,KAAA;UAAAO,KAAA;QAAA,GACA;UAAAP,KAAA;UAAAO,KAAA;QAAA,EACA;QACAH,WAAA;MACA,GACA;QACAJ,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAc,QAAA;QACAC,WAAA;MACA,GACA;QACAT,KAAA;QACAC,GAAA;QACAC,IAAA,EAAAR,SAAA,CAAAgB;MACA,EACA;MACAC,UAAA,GACA;QAAAR,KAAA;QAAAE,MAAA;QAAAG,QAAA;QAAAE,QAAA;MAAA,GACA;QAAAP,KAAA;QAAAE,MAAA;QAAAG,QAAA;QAAAE,QAAA;MAAA,GACA;QAAAP,KAAA;QAAAE,MAAA;QAAAG,QAAA;QAAAE,QAAA;MAAA,GACA;QAAAP,KAAA;QAAAE,MAAA;QAAAG,QAAA;QAAAE,QAAA;MAAA,GACA;QAAAP,KAAA;QAAAE,MAAA;QAAAG,QAAA;QAAAE,QAAA;MAAA;IAEA;EACA;EACAE,OAAA,WAAAA,QAAA,GAEA;AACA", "ignoreList": []}]}