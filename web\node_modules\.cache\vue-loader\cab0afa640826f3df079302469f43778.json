{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameModal.vue?vue&type=template&id=2418bfd5&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameModal.vue", "mtime": 1749743107576}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"game-modal-container\"\n  }, [_c(\"div\", {\n    staticClass: \"modal\"\n  }, [_c(\"div\", {\n    staticClass: \"modal-content\"\n  }, [_c(\"div\", {\n    staticClass: \"modal-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"modal-title\"\n  }, [_vm._v(_vm._s(_vm.game.title))]), _c(\"div\", {\n    staticClass: \"header-buttons\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-display\"\n  }, [_c(\"span\", {\n    staticClass: \"coin-icon\"\n  }), _c(\"span\", {\n    staticClass: \"coin-amount\"\n  }, [_vm._v(_vm._s(_vm.userCoins))])]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: !_vm.gameOver,\n      expression: \"!gameOver\"\n    }],\n    staticClass: \"game-timer\",\n    class: {\n      warning: _vm.isTimeWarning\n    }\n  }, [_vm._v(\"\\n            剩余时间: \" + _vm._s(_vm.formatTime) + \"\\n          \")]), _c(\"button\", {\n    staticClass: \"header-button\",\n    on: {\n      click: _vm.showGameTips\n    }\n  }, [_c(\"i\", [_vm._v(\"❓\")]), _vm._v(\" 游戏提示\\n          \")]), _c(\"button\", {\n    staticClass: \"header-button\",\n    on: {\n      click: _vm.toggleFullscreen\n    }\n  }, [_c(\"i\", [_vm._v(\"⛶\")]), _vm._v(\" 全屏模式\\n          \")]), _c(\"button\", {\n    staticClass: \"close-button\",\n    on: {\n      click: _vm.closeModal\n    }\n  }, [_vm._v(\"×\")])])]), _c(\"div\", {\n    staticClass: \"modal-body\"\n  }, [_c(\"iframe\", {\n    ref: \"gameFrame\",\n    staticClass: \"game-frame\",\n    attrs: {\n      src: _vm.game.url,\n      title: \"游戏\",\n      frameborder: \"0\",\n      sandbox: \"allow-scripts allow-same-origin allow-forms\",\n      allow: \"gamepad *; fullscreen\",\n      allowfullscreen: \"\"\n    }\n  })])])]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showTips,\n      expression: \"showTips\"\n    }],\n    staticClass: \"tip-modal\"\n  }, [_c(\"div\", {\n    staticClass: \"tip-content\"\n  }, [_c(\"div\", {\n    staticClass: \"tip-header\"\n  }, [_c(\"h3\", {\n    staticClass: \"tip-title\"\n  }, [_vm._v(\"游戏提示 - \" + _vm._s(_vm.game.title))]), _c(\"button\", {\n    staticClass: \"close-tip\",\n    on: {\n      click: _vm.closeTipModal\n    }\n  }, [_vm._v(\"×\")])]), _c(\"div\", {\n    staticClass: \"tip-body\",\n    domProps: {\n      innerHTML: _vm._s(_vm.game.tips)\n    }\n  })])]), _vm.gameOver ? _c(\"div\", {\n    staticClass: \"game-over-modal\"\n  }, [_c(\"div\", {\n    staticClass: \"game-over-content\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"game-over-body\"\n  }, [_c(\"p\", [_vm._v(\"您的游戏时间已用完，需要继续游玩吗？\")]), _c(\"p\", {\n    staticClass: \"coin-info\"\n  }, [_vm._v(\"继续游戏需要消费 \" + _vm._s(_vm.game.cost) + \" 金币\")]), _c(\"div\", {\n    staticClass: \"game-over-buttons\"\n  }, [_c(\"button\", {\n    staticClass: \"continue-button\",\n    on: {\n      click: _vm.continueGame\n    }\n  }, [_vm._v(\"继续游戏\")]), _c(\"button\", {\n    staticClass: \"exit-button\",\n    on: {\n      click: _vm.closeModal\n    }\n  }, [_vm._v(\"退出游戏\")])])])])]) : _vm._e()]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"game-over-header\"\n  }, [_c(\"h3\", [_vm._v(\"游戏时间已结束\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "game", "title", "userCoins", "directives", "name", "rawName", "value", "gameOver", "expression", "class", "warning", "isTimeWarning", "formatTime", "on", "click", "showGameTips", "toggleFullscreen", "closeModal", "ref", "attrs", "src", "url", "frameborder", "sandbox", "allow", "allowfullscreen", "showTips", "closeTipModal", "domProps", "innerHTML", "tips", "_m", "cost", "continueGame", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/game/components/GameModal.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"game-modal-container\" }, [\n    _c(\"div\", { staticClass: \"modal\" }, [\n      _c(\"div\", { staticClass: \"modal-content\" }, [\n        _c(\"div\", { staticClass: \"modal-header\" }, [\n          _c(\"h2\", { staticClass: \"modal-title\" }, [\n            _vm._v(_vm._s(_vm.game.title)),\n          ]),\n          _c(\"div\", { staticClass: \"header-buttons\" }, [\n            _c(\"div\", { staticClass: \"coin-display\" }, [\n              _c(\"span\", { staticClass: \"coin-icon\" }),\n              _c(\"span\", { staticClass: \"coin-amount\" }, [\n                _vm._v(_vm._s(_vm.userCoins)),\n              ]),\n            ]),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"show\",\n                    rawName: \"v-show\",\n                    value: !_vm.gameOver,\n                    expression: \"!gameOver\",\n                  },\n                ],\n                staticClass: \"game-timer\",\n                class: { warning: _vm.isTimeWarning },\n              },\n              [\n                _vm._v(\n                  \"\\n            剩余时间: \" +\n                    _vm._s(_vm.formatTime) +\n                    \"\\n          \"\n                ),\n              ]\n            ),\n            _c(\n              \"button\",\n              { staticClass: \"header-button\", on: { click: _vm.showGameTips } },\n              [_c(\"i\", [_vm._v(\"❓\")]), _vm._v(\" 游戏提示\\n          \")]\n            ),\n            _c(\n              \"button\",\n              {\n                staticClass: \"header-button\",\n                on: { click: _vm.toggleFullscreen },\n              },\n              [_c(\"i\", [_vm._v(\"⛶\")]), _vm._v(\" 全屏模式\\n          \")]\n            ),\n            _c(\n              \"button\",\n              { staticClass: \"close-button\", on: { click: _vm.closeModal } },\n              [_vm._v(\"×\")]\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"modal-body\" }, [\n          _c(\"iframe\", {\n            ref: \"gameFrame\",\n            staticClass: \"game-frame\",\n            attrs: {\n              src: _vm.game.url,\n              title: \"游戏\",\n              frameborder: \"0\",\n              sandbox: \"allow-scripts allow-same-origin allow-forms\",\n              allow: \"gamepad *; fullscreen\",\n              allowfullscreen: \"\",\n            },\n          }),\n        ]),\n      ]),\n    ]),\n    _c(\n      \"div\",\n      {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.showTips,\n            expression: \"showTips\",\n          },\n        ],\n        staticClass: \"tip-modal\",\n      },\n      [\n        _c(\"div\", { staticClass: \"tip-content\" }, [\n          _c(\"div\", { staticClass: \"tip-header\" }, [\n            _c(\"h3\", { staticClass: \"tip-title\" }, [\n              _vm._v(\"游戏提示 - \" + _vm._s(_vm.game.title)),\n            ]),\n            _c(\n              \"button\",\n              { staticClass: \"close-tip\", on: { click: _vm.closeTipModal } },\n              [_vm._v(\"×\")]\n            ),\n          ]),\n          _c(\"div\", {\n            staticClass: \"tip-body\",\n            domProps: { innerHTML: _vm._s(_vm.game.tips) },\n          }),\n        ]),\n      ]\n    ),\n    _vm.gameOver\n      ? _c(\"div\", { staticClass: \"game-over-modal\" }, [\n          _c(\"div\", { staticClass: \"game-over-content\" }, [\n            _vm._m(0),\n            _c(\"div\", { staticClass: \"game-over-body\" }, [\n              _c(\"p\", [_vm._v(\"您的游戏时间已用完，需要继续游玩吗？\")]),\n              _c(\"p\", { staticClass: \"coin-info\" }, [\n                _vm._v(\"继续游戏需要消费 \" + _vm._s(_vm.game.cost) + \" 金币\"),\n              ]),\n              _c(\"div\", { staticClass: \"game-over-buttons\" }, [\n                _c(\n                  \"button\",\n                  {\n                    staticClass: \"continue-button\",\n                    on: { click: _vm.continueGame },\n                  },\n                  [_vm._v(\"继续游戏\")]\n                ),\n                _c(\n                  \"button\",\n                  { staticClass: \"exit-button\", on: { click: _vm.closeModal } },\n                  [_vm._v(\"退出游戏\")]\n                ),\n              ]),\n            ]),\n          ]),\n        ])\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"game-over-header\" }, [\n      _c(\"h3\", [_vm._v(\"游戏时间已结束\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACxDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,IAAI,CAACC,KAAK,CAAC,CAAC,CAC/B,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACzCH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACQ,SAAS,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,EACFP,EAAE,CACA,KAAK,EACL;IACEQ,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,CAACZ,GAAG,CAACa,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC,CACF;IACDX,WAAW,EAAE,YAAY;IACzBY,KAAK,EAAE;MAAEC,OAAO,EAAEhB,GAAG,CAACiB;IAAc;EACtC,CAAC,EACD,CACEjB,GAAG,CAACI,EAAE,CACJ,sBAAsB,GACpBJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACkB,UAAU,CAAC,GACtB,cACJ,CAAC,CAEL,CAAC,EACDjB,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,eAAe;IAAEgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACqB;IAAa;EAAE,CAAC,EACjE,CAACpB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAACI,EAAE,CAAC,mBAAmB,CAAC,CACtD,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,eAAe;IAC5BgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACsB;IAAiB;EACpC,CAAC,EACD,CAACrB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAACI,EAAE,CAAC,mBAAmB,CAAC,CACtD,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,cAAc;IAAEgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACuB;IAAW;EAAE,CAAC,EAC9D,CAACvB,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CACd,CAAC,CACF,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,QAAQ,EAAE;IACXuB,GAAG,EAAE,WAAW;IAChBrB,WAAW,EAAE,YAAY;IACzBsB,KAAK,EAAE;MACLC,GAAG,EAAE1B,GAAG,CAACM,IAAI,CAACqB,GAAG;MACjBpB,KAAK,EAAE,IAAI;MACXqB,WAAW,EAAE,GAAG;MAChBC,OAAO,EAAE,6CAA6C;MACtDC,KAAK,EAAE,uBAAuB;MAC9BC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF9B,EAAE,CACA,KAAK,EACL;IACEQ,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEZ,GAAG,CAACgC,QAAQ;MACnBlB,UAAU,EAAE;IACd,CAAC,CACF;IACDX,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCH,GAAG,CAACI,EAAE,CAAC,SAAS,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,IAAI,CAACC,KAAK,CAAC,CAAC,CAC3C,CAAC,EACFN,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACiC;IAAc;EAAE,CAAC,EAC9D,CAACjC,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CACd,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,UAAU;IACvB+B,QAAQ,EAAE;MAAEC,SAAS,EAAEnC,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,IAAI,CAAC8B,IAAI;IAAE;EAC/C,CAAC,CAAC,CACH,CAAC,CAEN,CAAC,EACDpC,GAAG,CAACa,QAAQ,GACRZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACqC,EAAE,CAAC,CAAC,CAAC,EACTpC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,EACvCH,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACpCH,GAAG,CAACI,EAAE,CAAC,WAAW,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,IAAI,CAACgC,IAAI,CAAC,GAAG,KAAK,CAAC,CACpD,CAAC,EACFrC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,iBAAiB;IAC9BgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACuC;IAAa;EAChC,CAAC,EACD,CAACvC,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,aAAa;IAAEgB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAACuB;IAAW;EAAE,CAAC,EAC7D,CAACvB,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,GACFJ,GAAG,CAACwC,EAAE,CAAC,CAAC,CACb,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIzC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CACpDF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAC9B,CAAC;AACJ,CAAC,CACF;AACDL,MAAM,CAAC2C,aAAa,GAAG,IAAI;AAE3B,SAAS3C,MAAM,EAAE0C,eAAe", "ignoreList": []}]}