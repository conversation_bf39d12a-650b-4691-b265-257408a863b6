{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarMultid.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarMultid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { DataSet } from '@antv/data-set';\nimport { ChartEventMixins } from './mixins/ChartMixins';\nexport default {\n  name: 'BarMultid',\n  mixins: [ChartEventMixins],\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [{\n          type: 'Jeecg',\n          'Jan.': 18.9,\n          'Feb.': 28.8,\n          'Mar.': 39.3,\n          'Apr.': 81.4,\n          'May': 47,\n          'Jun.': 20.3,\n          'Jul.': 24,\n          'Aug.': 35.6\n        }, {\n          type: 'Jeebt',\n          'Jan.': 12.4,\n          'Feb.': 23.2,\n          'Mar.': 34.5,\n          'Apr.': 99.7,\n          'May': 52.6,\n          'Jun.': 35.5,\n          'Jul.': 37.4,\n          'Aug.': 42.4\n        }];\n      }\n    },\n    fields: {\n      type: Array,\n      default: function _default() {\n        return ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.'];\n      }\n    },\n    // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n    aliases: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    height: {\n      type: Number,\n      default: 254\n    }\n  },\n  data: function data() {\n    return {\n      adjust: [{\n        type: 'dodge',\n        marginRatio: 1 / 32\n      }]\n    };\n  },\n  computed: {\n    data: function data() {\n      var _this = this;\n      var dv = new DataSet.View().source(this.dataSource);\n      dv.transform({\n        type: 'fold',\n        fields: this.fields,\n        key: 'x',\n        value: 'y'\n      });\n\n      // bar 使用不了 - 和 / 所以替换下\n      var rows = dv.rows.map(function (row) {\n        if (typeof row.x === 'string') {\n          row.x = row.x.replace(/[-/]/g, '_');\n        }\n        return row;\n      });\n      // 替换别名\n      rows.forEach(function (row) {\n        var _iterator = _createForOfIteratorHelper(_this.aliases),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var item = _step.value;\n            if (item.field === row.type) {\n              row.type = item.alias;\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      });\n      return rows;\n    }\n  }\n};", {"version": 3, "names": ["DataSet", "ChartEventMixins", "name", "mixins", "props", "title", "type", "String", "default", "dataSource", "Array", "_default", "fields", "aliases", "height", "Number", "data", "adjust", "marginRatio", "computed", "_this", "dv", "View", "source", "transform", "key", "value", "rows", "map", "row", "x", "replace", "for<PERSON>ach", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "field", "alias", "err", "e", "f"], "sources": ["src/components/chart/BarMultid.vue"], "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :data=\"data\" :height=\"height\" :force-fit=\"true\" :onClick=\"handleClick\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-legend/>\n      <v-bar position=\"x*y\" color=\"type\" :adjust=\"adjust\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'BarMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jeecg', 'Jan.': 18.9, 'Feb.': 28.8, 'Mar.': 39.3, 'Apr.': 81.4, 'May': 47, 'Jun.': 20.3, 'Jul.': 24, 'Aug.': 35.6 },\n          { type: 'Jeeb<PERSON>', 'Jan.': 12.4, 'Feb.': 23.2, 'Mar.': 34.5, 'Apr.': 99.7, 'May': 52.6, 'Jun.': 35.5, 'Jul.': 37.4, 'Aug.': 42.4 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        adjust: [{\n          type: 'dodge',\n          marginRatio: 1 / 32\n        }]\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n\n        // bar 使用不了 - 和 / 所以替换下\n        let rows = dv.rows.map(row => {\n          if (typeof row.x === 'string') {\n            row.x = row.x.replace(/[-/]/g, '_')\n          }\n          return row\n        })\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.type) {\n              row.type = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": ";;;AAaA,SAAAA,OAAA;AACA,SAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAF,gBAAA;EACAG,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA,QACA;UAAAL,IAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA,GACA;UAAAA,IAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA,EACA;MAAA;IACA;IACAM,MAAA;MACAN,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACA;IACAE,OAAA;MACAP,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACAG,MAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;QACAX,IAAA;QACAY,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACAH,IAAA,WAAAA,KAAA;MAAA,IAAAI,KAAA;MACA,IAAAC,EAAA,OAAArB,OAAA,CAAAsB,IAAA,GAAAC,MAAA,MAAAd,UAAA;MACAY,EAAA,CAAAG,SAAA;QACAlB,IAAA;QACAM,MAAA,OAAAA,MAAA;QACAa,GAAA;QACAC,KAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAN,EAAA,CAAAM,IAAA,CAAAC,GAAA,WAAAC,GAAA;QACA,WAAAA,GAAA,CAAAC,CAAA;UACAD,GAAA,CAAAC,CAAA,GAAAD,GAAA,CAAAC,CAAA,CAAAC,OAAA;QACA;QACA,OAAAF,GAAA;MACA;MACA;MACAF,IAAA,CAAAK,OAAA,WAAAH,GAAA;QAAA,IAAAI,SAAA,GAAAC,0BAAA,CACAd,KAAA,CAAAP,OAAA;UAAAsB,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAT,KAAA;YACA,IAAAa,IAAA,CAAAC,KAAA,KAAAX,GAAA,CAAAvB,IAAA;cACAuB,GAAA,CAAAvB,IAAA,GAAAiC,IAAA,CAAAE,KAAA;cACA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;QAAA;UAAAT,SAAA,CAAAW,CAAA;QAAA;MACA;MACA,OAAAjB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}