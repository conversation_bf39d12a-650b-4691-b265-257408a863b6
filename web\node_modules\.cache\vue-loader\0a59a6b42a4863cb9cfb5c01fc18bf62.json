{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue?vue&type=template&id=5ebc2214&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"j-super-query-box\">\n\n  <slot name=\"button\" :isActive=\"superQueryFlag\" :isMobile=\"izMobile\" :open=\"handleOpen\" :reset=\"handleReset\">\n    <a-tooltip v-if=\"superQueryFlag\" v-bind=\"tooltipProps\" :mouseLeaveDelay=\"0.2\">\n      <!-- begin 不知道为什么不加上这段代码就无法生效 -->\n      <span v-show=\"false\">{{tooltipProps}}</span>\n      <!-- end 不知道为什么不加上这段代码就无法生效 -->\n      <template slot=\"title\">\n        <span>已有高级查询条件生效</span>\n        <a-divider type=\"vertical\"/>\n        <a @click=\"handleReset\">清空</a>\n      </template>\n      <a-button-group>\n        <a-button type=\"primary\" @click=\"handleOpen\">\n          <a-icon type=\"appstore\" theme=\"twoTone\" spin/>\n          <span>高级查询</span>\n        </a-button>\n        <a-button v-if=\"izMobile\" type=\"primary\" icon=\"delete\" @click=\"handleReset\"/>\n      </a-button-group>\n    </a-tooltip>\n    <a-button v-else type=\"primary\" icon=\"filter\" @click=\"handleOpen\">高级查询</a-button>\n  </slot>\n\n  <j-modal\n    title=\"高级查询构造器\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    @cancel=\"handleCancel\"\n    :mask=\"false\"\n    :fullscreen=\"izMobile\"\n    class=\"j-super-query-modal\"\n    style=\"top:5%;max-height: 95%;\"\n  >\n\n    <template slot=\"footer\">\n      <div style=\"float: left\">\n        <a-button :loading=\"loading\" @click=\"handleReset\">重置</a-button>\n        <a-button :loading=\"loading\" @click=\"handleSave\">保存查询条件</a-button>\n      </div>\n      <a-button :loading=\"loading\" @click=\"handleCancel\">关闭</a-button>\n      <a-button :loading=\"loading\" type=\"primary\" @click=\"handleOk\">查询</a-button>\n    </template>\n\n    <a-spin :spinning=\"loading\">\n      <a-row>\n        <a-col :sm=\"24\" :md=\"24-5\">\n\n          <a-empty v-if=\"queryParamsModel.length === 0\" style=\"margin-bottom: 12px;\">\n            <div slot=\"description\">\n              <span>没有任何查询条件</span>\n              <a-divider type=\"vertical\"/>\n              <a @click=\"handleAdd\">点击新增</a>\n            </div>\n          </a-empty>\n\n          <a-form v-else layout=\"inline\">\n\n            <a-row style=\"margin-bottom: 12px;\">\n              <a-col :md=\"12\" :xs=\"24\">\n                <a-form-item label=\"过滤条件匹配\" :labelCol=\"{md: 6,xs:24}\" :wrapperCol=\"{md: 18,xs:24}\" style=\"width: 100%;\">\n                  <a-select v-model=\"matchType\" :getPopupContainer=\"node=>node.parentNode\" style=\"width: 100%;\">\n                    <a-select-option value=\"and\">AND（所有条件都要求匹配）</a-select-option>\n                    <a-select-option value=\"or\">OR（条件中的任意一个匹配）</a-select-option>\n                  </a-select>\n                </a-form-item>\n              </a-col>\n            </a-row>\n\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in queryParamsModel\" :key=\"index\">\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-tree-select\n                  showSearch\n                  v-model=\"item.field\"\n                  :treeData=\"fieldTreeData\"\n                  :dropdownStyle=\"{ maxHeight: '400px', overflow: 'auto' }\"\n                  placeholder=\"选择查询字段\"\n                  allowClear\n                  treeDefaultExpandAll\n                  :getPopupContainer=\"node=>node.parentNode\"\n                  style=\"width: 100%\"\n                  @select=\"(val,option)=>handleSelected(option,item)\"\n                >\n                </a-tree-select>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-select placeholder=\"匹配规则\" :value=\"item.rule\" :getPopupContainer=\"node=>node.parentNode\" @change=\"handleRuleChange(item,$event)\">\n                  <a-select-option value=\"eq\">等于</a-select-option>\n                  <a-select-option value=\"like\">包含</a-select-option>\n                  <a-select-option value=\"right_like\">以..开始</a-select-option>\n                  <a-select-option value=\"left_like\">以..结尾</a-select-option>\n                  <a-select-option value=\"in\">在...中</a-select-option>\n                  <a-select-option value=\"ne\">不等于</a-select-option>\n                  <a-select-option value=\"gt\">大于</a-select-option>\n                  <a-select-option value=\"ge\">大于等于</a-select-option>\n                  <a-select-option value=\"lt\">小于</a-select-option>\n                  <a-select-option value=\"le\">小于等于</a-select-option>\n                </a-select>\n              </a-col>\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <template v-if=\"item.dictCode\">\n                  <template v-if=\"item.type === 'table-dict'\">\n                    <j-popup\n                      v-model=\"item.val\"\n                      :code=\"item.dictTable\"\n                      :field=\"item.dictCode\"\n                      :orgFields=\"item.dictCode\"\n                      :destFields=\"item.dictCode\"\n                    ></j-popup>\n                  </template>\n                  <template v-else>\n                    <j-multi-select-tag v-show=\"allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                    <j-dict-select-tag v-show=\"!allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                  </template>\n                </template>\n                <j-popup v-else-if=\"item.type === 'popup'\" :value=\"item.val\" v-bind=\"item.popup\" group-id=\"superQuery\" @input=\"(e,v)=>handleChangeJPopup(item,e,v)\"/>\n                <j-select-multi-user\n                  v-else-if=\"item.type === 'select-user' || item.type === 'sel_user'\"\n                  v-model=\"item.val\"\n                  :buttons=\"false\"\n                  :multiple=\"false\"\n                  placeholder=\"请选择用户\"\n                  :returnKeys=\"['id', item.customReturnField || 'username']\"\n                />\n                <j-select-depart\n                  v-else-if=\"item.type === 'select-depart' || item.type === 'sel_depart'\"\n                  v-model=\"item.val\"\n                  :multi=\"false\"\n                  placeholder=\"请选择部门\"\n                  :customReturnField=\"item.customReturnField || 'id'\"\n                />\n                <a-select\n                  v-else-if=\"item.options instanceof Array\"\n                  v-model=\"item.val\"\n                  :options=\"item.options\"\n                  allowClear\n                  placeholder=\"请选择\"\n                  :mode=\"allowMultiple(item)?'multiple':''\"\n                />\n                <j-area-linkage v-model=\"item.val\" v-else-if=\"item.type==='area-linkage' || item.type==='pca'\" style=\"width: 100%\"/>\n                <j-date v-else-if=\" item.type=='date' \" v-model=\"item.val\" placeholder=\"请选择日期\" style=\"width: 100%\"></j-date>\n                <j-date v-else-if=\" item.type=='datetime' \" v-model=\"item.val\" placeholder=\"请选择时间\" :show-time=\"true\" date-format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\"></j-date>\n                <a-time-picker v-else-if=\"item.type==='time'\" :value=\"item.val ? moment(item.val,'HH:mm:ss') : null\" format=\"HH:mm:ss\" style=\"width: 100%\" @change=\"(time,value)=>item.val=value\"/>\n                <a-input-number v-else-if=\" item.type=='int'||item.type=='number' \" style=\"width: 100%\" placeholder=\"请输入数值\" v-model=\"item.val\"/>\n                <a-input v-else v-model=\"item.val\" placeholder=\"请输入值\"/>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"0\" style=\"margin-bottom: 12px;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n              <a-col :md=\"0\" :xs=\"24\" style=\"margin-bottom: 12px;text-align: right;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n            </a-row>\n\n          </a-form>\n        </a-col>\n        <a-col :sm=\"24\" :md=\"5\">\n          <!-- 查询记录 -->\n\n          <a-card class=\"j-super-query-history-card\" :bordered=\"true\">\n            <div slot=\"title\">\n              保存的查询\n            </div>\n\n            <a-empty v-if=\"saveTreeData.length === 0\" class=\"j-super-query-history-empty\" description=\"没有保存任何查询\"/>\n            <a-tree\n              v-else\n              class=\"j-super-query-history-tree\"\n              showIcon\n              :treeData=\"saveTreeData\"\n              :selectedKeys=\"[]\"\n              @select=\"handleTreeSelect\"\n            >\n            </a-tree>\n          </a-card>\n\n\n        </a-col>\n      </a-row>\n\n\n    </a-spin>\n\n    <a-modal title=\"请输入保存的名称\" :visible=\"prompt.visible\" @cancel=\"prompt.visible=false\" @ok=\"handlePromptOk\">\n      <a-input v-model=\"prompt.value\"></a-input>\n    </a-modal>\n\n  </j-modal>\n</div>\n", null]}