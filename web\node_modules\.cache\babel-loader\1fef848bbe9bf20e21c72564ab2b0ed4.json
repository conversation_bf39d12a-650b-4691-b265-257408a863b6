{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\examRecords.vue", "mtime": 1753197976376}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getExamRecordList, getExamRecordDetail, getExamRecordPaperPreview } from '@/api/examSystem';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nimport ExamResultModal from './components/ExamResultModal';\nimport ExamPaperPreview from './components/ExamPaperPreview';\nexport default {\n  name: 'ExamRecords',\n  mixins: [JeecgListMixin],\n  // 添加标准化列表状态管理\n  components: {\n    ExamResultModal: ExamResultModal,\n    ExamPaperPreview: ExamPaperPreview\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      description: '考试记录页面',\n      // JeecgListMixin标准化API配置\n      url: {\n        list: '/teaching/examSystem/examRecord/list',\n        delete: '/teaching/examSystem/examRecord/delete',\n        deleteBatch: '/teaching/examSystem/examRecord/deleteBatch'\n      },\n      // 查询参数\n      queryParam: {\n        subject: undefined,\n        level: undefined,\n        status: undefined\n      },\n      // 表格列定义\n      columns: [{\n        title: '试卷标题',\n        dataIndex: 'paperTitle',\n        width: '25%',\n        scopedSlots: {\n          customRender: 'paperTitleSlot'\n        }\n      }, {\n        title: '科目/级别',\n        dataIndex: 'subject',\n        customRender: function customRender(text, record) {\n          return \"\".concat(text, \" \").concat(record.level);\n        }\n      }, {\n        title: '开始时间',\n        dataIndex: 'startTime',\n        sorter: true\n      }, {\n        title: '结束时间',\n        dataIndex: 'endTime',\n        sorter: true\n      }, {\n        title: '用时',\n        dataIndex: 'duration',\n        customRender: function customRender(_, record) {\n          // 使用VO中的getDuration方法计算的结果\n          var duration = record.duration;\n          if (!duration) return '-';\n          // duration现在是秒数，直接格式化\n          return _this.formatDuration(duration);\n        },\n        sorter: true\n      }, {\n        title: '分数',\n        dataIndex: 'score',\n        sorter: true,\n        scopedSlots: {\n          customRender: 'scoreSlot'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        scopedSlots: {\n          customRender: 'statusSlot'\n        }\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        scopedSlots: {\n          customRender: 'actionSlot'\n        }\n      }],\n      // 注意：dataSource, ipagination, loading 等状态\n      // 已由JeecgListMixin提供，无需重复定义\n\n      // 考试结果模态框相关\n      resultModalVisible: false,\n      currentExamResult: {},\n      currentPaperInfo: {},\n      currentExamQuestions: {\n        singleChoice: [],\n        judgment: [],\n        programming: []\n      },\n      currentExamDuration: 0,\n      // 试卷预览相关\n      paperPreviewVisible: false,\n      paperPreviewData: null\n    };\n  },\n  computed: {\n    // 已完成考试数量\n    completedExamCount: function completedExamCount() {\n      return this.dataSource.filter(function (item) {\n        return item.status === 1;\n      }).length;\n    },\n    // 通过考试数量\n    passedExamCount: function passedExamCount() {\n      return this.dataSource.filter(function (item) {\n        return item.status === 1 && item.score >= 60;\n      }).length;\n    },\n    // 通过率\n    passRate: function passRate() {\n      if (this.completedExamCount === 0) return 0;\n      return this.passedExamCount / this.completedExamCount * 100;\n    }\n  },\n  mounted: function mounted() {\n    this.loadData();\n  },\n  methods: {\n    // 加载数据\n    loadData: function loadData(arg) {\n      var _this2 = this;\n      if (arg === 1) {\n        this.ipagination.current = 1;\n      }\n      this.loading = true;\n      var params = _objectSpread({}, this.queryParam);\n      params.pageNo = this.ipagination.current;\n      params.pageSize = this.ipagination.pageSize;\n      console.log('发送考试记录查询请求，参数:', params);\n      getExamRecordList(params).then(function (res) {\n        console.log('考试记录API响应:', res);\n        if (res && res.success) {\n          // 处理分页数据结构\n          if (res.result && res.result.records) {\n            _this2.dataSource = res.result.records;\n            _this2.ipagination.total = res.result.total;\n          } else if (Array.isArray(res.result)) {\n            _this2.dataSource = res.result;\n            _this2.ipagination.total = res.result.length;\n          } else {\n            _this2.dataSource = [];\n            _this2.ipagination.total = 0;\n          }\n          console.log('处理后的数据源:', _this2.dataSource);\n          console.log('总数:', _this2.ipagination.total);\n        } else {\n          console.error('API返回失败:', res);\n          var errorMsg = res && res.message ? res.message : '获取考试记录失败';\n          _this2.$message.error(errorMsg);\n\n          // 如果是认证相关错误，可能需要重新登录\n          if (errorMsg.includes('token') || errorMsg.includes('登录')) {\n            _this2.$message.warning('请重新登录后再试');\n          }\n        }\n        _this2.loading = false;\n      }).catch(function (err) {\n        _this2.loading = false;\n        console.error('获取考试记录网络错误:', err);\n\n        // 检查是否是网络错误或认证错误\n        if (err.response) {\n          console.error('HTTP错误状态:', err.response.status);\n          console.error('HTTP错误数据:', err.response.data);\n          if (err.response.status === 401) {\n            _this2.$message.error('认证失败，请重新登录');\n          } else if (err.response.status === 403) {\n            _this2.$message.error('权限不足');\n          } else {\n            var errorMessage = err.response.data && err.response.data.message ? err.response.data.message : err.message;\n            _this2.$message.error('服务器错误：' + errorMessage);\n          }\n        } else {\n          _this2.$message.error('网络连接失败，请检查网络连接');\n        }\n      });\n    },\n    // 表格变化处理\n    handleTableChange: function handleTableChange(pagination, _, sorter) {\n      this.ipagination.current = pagination.current;\n\n      // 添加排序参数\n      if (sorter && sorter.field) {\n        this.queryParam.sortField = sorter.field;\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';\n      } else {\n        delete this.queryParam.sortField;\n        delete this.queryParam.sortOrder;\n      }\n      this.loadData();\n    },\n    // 重置查询条件\n    resetQuery: function resetQuery() {\n      this.queryParam = {\n        subject: undefined,\n        level: undefined,\n        status: undefined\n      };\n      this.loadData(1);\n    },\n    // 获取状态类型\n    getStatusType: function getStatusType(status) {\n      switch (status) {\n        case 1:\n          return 'success';\n        default:\n          return 'default';\n      }\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status) {\n      switch (status) {\n        case 1:\n          return '已提交';\n        default:\n          return '未知';\n      }\n    },\n    // 获取级别选项\n    getLevelOptions: function getLevelOptions() {\n      var subject = this.queryParam.subject;\n      if (!subject) {\n        // 如果没有选择科目，返回所有级别\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }, {\n          value: '五级',\n          label: '五级'\n        }, {\n          value: '六级',\n          label: '六级'\n        }, {\n          value: '七级',\n          label: '七级'\n        }, {\n          value: '八级',\n          label: '八级'\n        }];\n      }\n      if (subject === 'Scratch') {\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }];\n      } else if (subject === 'Python' || subject === 'C++') {\n        return [{\n          value: '一级',\n          label: '一级'\n        }, {\n          value: '二级',\n          label: '二级'\n        }, {\n          value: '三级',\n          label: '三级'\n        }, {\n          value: '四级',\n          label: '四级'\n        }, {\n          value: '五级',\n          label: '五级'\n        }, {\n          value: '六级',\n          label: '六级'\n        }, {\n          value: '七级',\n          label: '七级'\n        }, {\n          value: '八级',\n          label: '八级'\n        }];\n      }\n      return [];\n    },\n    // 查看结果\n    viewResult: function () {\n      var _viewResult = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(record) {\n        var res, examRecord, scoreDetails;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              this.loading = true;\n\n              // 获取考试记录详情\n              _context.next = 4;\n              return getExamRecordDetail(record.id);\n            case 4:\n              res = _context.sent;\n              if (res.success) {\n                examRecord = res.result; // 构建考试结果数据 - 使用后端返回的详细分数统计\n                scoreDetails = {};\n                if (examRecord.scoreDetails) {\n                  try {\n                    scoreDetails = JSON.parse(examRecord.scoreDetails);\n                    console.log('解析的分数详情:', scoreDetails);\n                  } catch (e) {\n                    console.error('解析分数详情失败:', e);\n                    // 如果解析失败，使用默认值\n                    scoreDetails = {\n                      singleChoice: {\n                        score: 0,\n                        totalScore: 0\n                      },\n                      judgment: {\n                        score: 0,\n                        totalScore: 0\n                      },\n                      programming: {\n                        score: 0,\n                        totalScore: 0\n                      }\n                    };\n                  }\n                } else {\n                  // 如果没有详细分数统计，使用默认值\n                  scoreDetails = {\n                    singleChoice: {\n                      score: 0,\n                      totalScore: 0\n                    },\n                    judgment: {\n                      score: 0,\n                      totalScore: 0\n                    },\n                    programming: {\n                      score: 0,\n                      totalScore: 0\n                    }\n                  };\n                }\n                this.currentExamResult = {\n                  score: examRecord.score || 0,\n                  totalScore: examRecord.score || 0,\n                  isPassed: (examRecord.score || 0) >= 60,\n                  submitTime: examRecord.endTime,\n                  paperTitle: examRecord.paperTitle,\n                  details: scoreDetails\n                };\n\n                // 构建试卷信息\n                this.currentPaperInfo = {\n                  title: examRecord.paperTitle,\n                  subject: examRecord.subject,\n                  level: examRecord.level,\n                  difficulty: examRecord.difficulty,\n                  type: examRecord.type,\n                  year: examRecord.year,\n                  examDuration: examRecord.examDuration\n                };\n\n                // 计算考试用时（秒）\n                if (examRecord.startTime && examRecord.endTime) {\n                  this.currentExamDuration = Math.floor((new Date(examRecord.endTime).getTime() - new Date(examRecord.startTime).getTime()) / 1000);\n                } else {\n                  this.currentExamDuration = 0;\n                }\n\n                // 显示模态框\n                this.resultModalVisible = true;\n              } else {\n                this.$message.error(res.message || '获取考试记录详情失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取考试记录详情失败:', _context.t0);\n              this.$message.error('获取考试记录详情失败，请重试');\n            case 12:\n              _context.prev = 12;\n              this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this, [[0, 8, 12, 15]]);\n      }));\n      function viewResult(_x) {\n        return _viewResult.apply(this, arguments);\n      }\n      return viewResult;\n    }(),\n    // 查看试卷\n    viewPaper: function () {\n      var _viewPaper = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(record) {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              console.log('查看试卷:', record);\n              _context2.prev = 1;\n              this.loading = true;\n\n              // 调用API获取试卷预览数据\n              _context2.next = 5;\n              return getExamRecordPaperPreview(record.id);\n            case 5:\n              res = _context2.sent;\n              if (res && res.success) {\n                this.paperPreviewData = res.result;\n                this.paperPreviewVisible = true;\n                console.log('试卷预览数据:', this.paperPreviewData);\n              } else {\n                this.$message.error(res.message || '获取试卷预览失败');\n              }\n              _context2.next = 13;\n              break;\n            case 9:\n              _context2.prev = 9;\n              _context2.t0 = _context2[\"catch\"](1);\n              console.error('获取试卷预览失败:', _context2.t0);\n              this.$message.error('获取试卷预览失败，请重试');\n            case 13:\n              _context2.prev = 13;\n              this.loading = false;\n              return _context2.finish(13);\n            case 16:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this, [[1, 9, 13, 16]]);\n      }));\n      function viewPaper(_x2) {\n        return _viewPaper.apply(this, arguments);\n      }\n      return viewPaper;\n    }(),\n    // 格式化时长（精确显示）\n    formatDuration: function formatDuration(duration) {\n      if (!duration) return '--';\n      var hours = Math.floor(duration / 3600);\n      var minutes = Math.floor(duration % 3600 / 60);\n      var seconds = duration % 60;\n      if (hours > 0) {\n        // 超过1小时：显示 X时X分X秒\n        return \"\".concat(hours, \"\\u65F6\").concat(minutes, \"\\u5206\").concat(seconds, \"\\u79D2\");\n      } else if (minutes > 0) {\n        // 1小时内但超过1分钟：显示 X分X秒\n        return \"\".concat(minutes, \"\\u5206\").concat(seconds, \"\\u79D2\");\n      } else {\n        // 1分钟内：只显示 X秒\n        return \"\".concat(seconds, \"\\u79D2\");\n      }\n    }\n  }\n};", {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "getExamRecordList", "getExamRecordDetail", "getExamRecordPaperPreview", "JeecgListMixin", "ExamResultModal", "ExamPaperPreview", "mixins", "components", "data", "_this", "description", "url", "list", "delete", "deleteBatch", "queryParam", "subject", "undefined", "level", "status", "columns", "title", "dataIndex", "width", "scopedSlots", "customRender", "text", "record", "concat", "sorter", "_", "duration", "formatDuration", "resultModalVisible", "currentExamResult", "currentPaperInfo", "currentExamQuestions", "singleChoice", "judgment", "programming", "currentExamDuration", "paperPreviewVisible", "paperPreviewData", "computed", "completedExamCount", "dataSource", "item", "passedExamCount", "score", "passRate", "mounted", "loadData", "methods", "_this2", "ipagination", "current", "loading", "params", "pageNo", "pageSize", "console", "log", "res", "success", "result", "records", "total", "Array", "isArray", "error", "errorMsg", "message", "$message", "includes", "warning", "err", "response", "errorMessage", "handleTableChange", "pagination", "field", "sortField", "sortOrder", "order", "reset<PERSON><PERSON>y", "getStatusType", "getStatusText", "getLevelOptions", "label", "viewResult", "_viewResult", "_callee", "examRecord", "scoreDetails", "_callee$", "_context", "id", "JSON", "parse", "totalScore", "isPassed", "submitTime", "endTime", "paperTitle", "details", "difficulty", "year", "examDuration", "startTime", "Math", "floor", "Date", "getTime", "t0", "_x", "viewPaper", "_viewPaper", "_callee2", "_callee2$", "_context2", "_x2", "hours", "minutes", "seconds"], "sources": ["src/views/examSystem/examRecords.vue"], "sourcesContent": ["<template>\r\n  <a-card :bordered=\"false\">\r\n    <div class=\"table-page-search-wrapper\">\r\n      <a-form layout=\"inline\">\r\n        <a-row :gutter=\"24\">\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"科目\">\r\n              <a-select v-model=\"queryParam.subject\" placeholder=\"请选择科目\" allowClear>\r\n                <a-select-option value=\"Scratch\">Scratch</a-select-option>\r\n                <a-select-option value=\"Python\">Python</a-select-option>\r\n                <a-select-option value=\"C++\">C++</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"级别\">\r\n              <a-select v-model=\"queryParam.level\" placeholder=\"请选择级别\" allowClear>\r\n                <a-select-option v-for=\"option in getLevelOptions()\" :key=\"option.value\" :value=\"option.value\">{{ option.label }}</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <a-form-item label=\"考试状态\">\r\n              <a-select v-model=\"queryParam.status\" placeholder=\"请选择状态\" allowClear>\r\n                <a-select-option :value=\"1\">已提交</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :md=\"6\" :sm=\"24\">\r\n            <span class=\"table-page-search-submitButtons\">\r\n              <a-button type=\"primary\" @click=\"loadData(1)\">查询</a-button>\r\n              <a-button style=\"margin-left: 8px\" @click=\"resetQuery\">重置</a-button>\r\n            </span>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n    </div>\r\n\r\n    <!-- 考试统计信息 -->\r\n    <a-card title=\"考试统计\" style=\"margin: 16px 0;\">\r\n      <a-row :gutter=\"16\">\r\n        <a-col :span=\"8\">\r\n          <a-statistic title=\"已完成考试\" :value=\"completedExamCount\" />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic title=\"通过考试\" :value=\"passedExamCount\" />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic\r\n            title=\"通过率\"\r\n            :value=\"passRate\"\r\n            :precision=\"2\"\r\n            suffix=\"%\"\r\n            :valueStyle=\"{ color: passRate >= 60 ? '#3f8600' : '#cf1322' }\"\r\n          />\r\n        </a-col>\r\n      </a-row>\r\n    </a-card>\r\n\r\n    <a-table\r\n      ref=\"table\"\r\n      size=\"middle\"\r\n      bordered\r\n      rowKey=\"id\"\r\n      :columns=\"columns\"\r\n      :dataSource=\"dataSource\"\r\n      :pagination=\"ipagination\"\r\n      :loading=\"loading\"\r\n      @change=\"handleTableChange\">\r\n\r\n      <template slot=\"statusSlot\" slot-scope=\"text\">\r\n        <a-badge :status=\"getStatusType(text)\" :text=\"getStatusText(text)\" />\r\n      </template>\r\n\r\n      <template slot=\"scoreSlot\" slot-scope=\"text, record\">\r\n        <span v-if=\"record.status === 1\">\r\n          <span :style=\"{ color: text >= 60 ? '#52c41a' : '#f5222d' }\">{{ text }}</span>\r\n          <span style=\"margin-left: 8px;\">{{ text >= 60 ? '(通过)' : '(未通过)' }}</span>\r\n        </span>\r\n        <span v-else>-</span>\r\n      </template>\r\n\r\n      <template slot=\"paperTitleSlot\" slot-scope=\"text, record\">\r\n        <span>{{ text }}</span>\r\n        <!-- 暂时移除paperType标签，因为后端VO中没有这个字段 -->\r\n      </template>\r\n\r\n      <template slot=\"actionSlot\" slot-scope=\"text, record\">\r\n        <div v-if=\"record.status === 1\">\r\n          <a @click=\"viewResult(record)\">查看结果</a>\r\n          <a-divider type=\"vertical\" />\r\n          <a @click=\"viewPaper(record)\">查看试卷</a>\r\n        </div>\r\n        <span v-else>-</span>\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- 考试结果模态框 -->\r\n    <exam-result-modal\r\n      v-model=\"resultModalVisible\"\r\n      :examResult=\"currentExamResult\"\r\n      :paperInfo=\"currentPaperInfo\"\r\n      :examQuestions=\"currentExamQuestions\"\r\n      :examDuration=\"currentExamDuration\"\r\n      :fromRecords=\"true\"\r\n    />\r\n\r\n    <!-- 试卷预览组件 -->\r\n    <exam-paper-preview\r\n      v-model=\"paperPreviewVisible\"\r\n      :paperData=\"paperPreviewData\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { getExamRecordList, getExamRecordDetail, getExamRecordPaperPreview } from '@/api/examSystem'\r\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\r\nimport ExamResultModal from './components/ExamResultModal'\r\nimport ExamPaperPreview from './components/ExamPaperPreview'\r\n\r\nexport default {\r\n  name: 'ExamRecords',\r\n  mixins: [JeecgListMixin], // 添加标准化列表状态管理\r\n  components: {\r\n    ExamResultModal,\r\n    ExamPaperPreview\r\n  },\r\n  data() {\r\n    return {\r\n      description: '考试记录页面',\r\n      // JeecgListMixin标准化API配置\r\n      url: {\r\n        list: '/teaching/examSystem/examRecord/list',\r\n        delete: '/teaching/examSystem/examRecord/delete',\r\n        deleteBatch: '/teaching/examSystem/examRecord/deleteBatch'\r\n      },\r\n      // 查询参数\r\n      queryParam: {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      },\r\n      // 表格列定义\r\n      columns: [\r\n        {\r\n          title: '试卷标题',\r\n          dataIndex: 'paperTitle',\r\n          width: '25%',\r\n          scopedSlots: { customRender: 'paperTitleSlot' }\r\n        },\r\n        {\r\n          title: '科目/级别',\r\n          dataIndex: 'subject',\r\n          customRender: (text, record) => {\r\n            return `${text} ${record.level}`\r\n          }\r\n        },\r\n        {\r\n          title: '开始时间',\r\n          dataIndex: 'startTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '结束时间',\r\n          dataIndex: 'endTime',\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '用时',\r\n          dataIndex: 'duration',\r\n          customRender: (_, record) => {\r\n            // 使用VO中的getDuration方法计算的结果\r\n            const duration = record.duration\r\n            if (!duration) return '-'\r\n            // duration现在是秒数，直接格式化\r\n            return this.formatDuration(duration)\r\n          },\r\n          sorter: true\r\n        },\r\n        {\r\n          title: '分数',\r\n          dataIndex: 'score',\r\n          sorter: true,\r\n          scopedSlots: { customRender: 'scoreSlot' }\r\n        },\r\n        {\r\n          title: '状态',\r\n          dataIndex: 'status',\r\n          scopedSlots: { customRender: 'statusSlot' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          scopedSlots: { customRender: 'actionSlot' }\r\n        }\r\n      ],\r\n      // 注意：dataSource, ipagination, loading 等状态\r\n      // 已由JeecgListMixin提供，无需重复定义\r\n\r\n      // 考试结果模态框相关\r\n      resultModalVisible: false,\r\n      currentExamResult: {},\r\n      currentPaperInfo: {},\r\n      currentExamQuestions: {\r\n        singleChoice: [],\r\n        judgment: [],\r\n        programming: []\r\n      },\r\n      currentExamDuration: 0,\r\n\r\n      // 试卷预览相关\r\n      paperPreviewVisible: false,\r\n      paperPreviewData: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 已完成考试数量\r\n    completedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1).length\r\n    },\r\n    // 通过考试数量\r\n    passedExamCount() {\r\n      return this.dataSource.filter(item => item.status === 1 && item.score >= 60).length\r\n    },\r\n    // 通过率\r\n    passRate() {\r\n      if (this.completedExamCount === 0) return 0\r\n      return (this.passedExamCount / this.completedExamCount) * 100\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadData()\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1\r\n      }\r\n      this.loading = true\r\n      const params = {...this.queryParam}\r\n      params.pageNo = this.ipagination.current\r\n      params.pageSize = this.ipagination.pageSize\r\n      \r\n      console.log('发送考试记录查询请求，参数:', params)\r\n\r\n      getExamRecordList(params).then((res) => {\r\n        console.log('考试记录API响应:', res)\r\n        if (res && res.success) {\r\n          // 处理分页数据结构\r\n          if (res.result && res.result.records) {\r\n            this.dataSource = res.result.records\r\n            this.ipagination.total = res.result.total\r\n          } else if (Array.isArray(res.result)) {\r\n            this.dataSource = res.result\r\n            this.ipagination.total = res.result.length\r\n          } else {\r\n            this.dataSource = []\r\n            this.ipagination.total = 0\r\n          }\r\n\r\n          console.log('处理后的数据源:', this.dataSource)\r\n          console.log('总数:', this.ipagination.total)\r\n        } else {\r\n          console.error('API返回失败:', res)\r\n          const errorMsg = res && res.message ? res.message : '获取考试记录失败'\r\n          this.$message.error(errorMsg)\r\n\r\n          // 如果是认证相关错误，可能需要重新登录\r\n          if (errorMsg.includes('token') || errorMsg.includes('登录')) {\r\n            this.$message.warning('请重新登录后再试')\r\n          }\r\n        }\r\n        this.loading = false\r\n      }).catch((err) => {\r\n        this.loading = false\r\n        console.error('获取考试记录网络错误:', err)\r\n\r\n        // 检查是否是网络错误或认证错误\r\n        if (err.response) {\r\n          console.error('HTTP错误状态:', err.response.status)\r\n          console.error('HTTP错误数据:', err.response.data)\r\n\r\n          if (err.response.status === 401) {\r\n            this.$message.error('认证失败，请重新登录')\r\n          } else if (err.response.status === 403) {\r\n            this.$message.error('权限不足')\r\n          } else {\r\n            const errorMessage = err.response.data && err.response.data.message ? err.response.data.message : err.message\r\n            this.$message.error('服务器错误：' + errorMessage)\r\n          }\r\n        } else {\r\n          this.$message.error('网络连接失败，请检查网络连接')\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 表格变化处理\r\n    handleTableChange(pagination, _, sorter) {\r\n      this.ipagination.current = pagination.current\r\n      \r\n      // 添加排序参数\r\n      if (sorter && sorter.field) {\r\n        this.queryParam.sortField = sorter.field\r\n        this.queryParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'\r\n      } else {\r\n        delete this.queryParam.sortField\r\n        delete this.queryParam.sortOrder\r\n      }\r\n      \r\n      this.loadData()\r\n    },\r\n    \r\n    // 重置查询条件\r\n    resetQuery() {\r\n      this.queryParam = {\r\n        subject: undefined,\r\n        level: undefined,\r\n        status: undefined\r\n      }\r\n      this.loadData(1)\r\n    },\r\n    \r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case 1: return 'success'\r\n        default: return 'default'\r\n      }\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 1: return '已提交'\r\n        default: return '未知'\r\n      }\r\n    },\r\n\r\n    // 获取级别选项\r\n    getLevelOptions() {\r\n      const subject = this.queryParam.subject\r\n      if (!subject) {\r\n        // 如果没有选择科目，返回所有级别\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      if (subject === 'Scratch') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' }\r\n        ]\r\n      } else if (subject === 'Python' || subject === 'C++') {\r\n        return [\r\n          { value: '一级', label: '一级' },\r\n          { value: '二级', label: '二级' },\r\n          { value: '三级', label: '三级' },\r\n          { value: '四级', label: '四级' },\r\n          { value: '五级', label: '五级' },\r\n          { value: '六级', label: '六级' },\r\n          { value: '七级', label: '七级' },\r\n          { value: '八级', label: '八级' }\r\n        ]\r\n      }\r\n\r\n      return []\r\n    },\r\n    \r\n\r\n    \r\n    // 查看结果\r\n    async viewResult(record) {\r\n      try {\r\n        this.loading = true\r\n\r\n        // 获取考试记录详情\r\n        const res = await getExamRecordDetail(record.id)\r\n\r\n        if (res.success) {\r\n          const examRecord = res.result\r\n\r\n          // 构建考试结果数据 - 使用后端返回的详细分数统计\r\n          let scoreDetails = {}\r\n          if (examRecord.scoreDetails) {\r\n            try {\r\n              scoreDetails = JSON.parse(examRecord.scoreDetails)\r\n              console.log('解析的分数详情:', scoreDetails)\r\n            } catch (e) {\r\n              console.error('解析分数详情失败:', e)\r\n              // 如果解析失败，使用默认值\r\n              scoreDetails = {\r\n                singleChoice: { score: 0, totalScore: 0 },\r\n                judgment: { score: 0, totalScore: 0 },\r\n                programming: { score: 0, totalScore: 0 }\r\n              }\r\n            }\r\n          } else {\r\n            // 如果没有详细分数统计，使用默认值\r\n            scoreDetails = {\r\n              singleChoice: { score: 0, totalScore: 0 },\r\n              judgment: { score: 0, totalScore: 0 },\r\n              programming: { score: 0, totalScore: 0 }\r\n            }\r\n          }\r\n\r\n          this.currentExamResult = {\r\n            score: examRecord.score || 0,\r\n            totalScore: examRecord.score || 0,\r\n            isPassed: (examRecord.score || 0) >= 60,\r\n            submitTime: examRecord.endTime,\r\n            paperTitle: examRecord.paperTitle,\r\n            details: scoreDetails\r\n          }\r\n\r\n          // 构建试卷信息\r\n          this.currentPaperInfo = {\r\n            title: examRecord.paperTitle,\r\n            subject: examRecord.subject,\r\n            level: examRecord.level,\r\n            difficulty: examRecord.difficulty,\r\n            type: examRecord.type,\r\n            year: examRecord.year,\r\n            examDuration: examRecord.examDuration\r\n          }\r\n\r\n          // 计算考试用时（秒）\r\n          if (examRecord.startTime && examRecord.endTime) {\r\n            this.currentExamDuration = Math.floor((new Date(examRecord.endTime).getTime() - new Date(examRecord.startTime).getTime()) / 1000)\r\n          } else {\r\n            this.currentExamDuration = 0\r\n          }\r\n\r\n          // 显示模态框\r\n          this.resultModalVisible = true\r\n\r\n        } else {\r\n          this.$message.error(res.message || '获取考试记录详情失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取考试记录详情失败:', error)\r\n        this.$message.error('获取考试记录详情失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 查看试卷\r\n    async viewPaper(record) {\r\n      console.log('查看试卷:', record)\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 调用API获取试卷预览数据\r\n        const res = await getExamRecordPaperPreview(record.id)\r\n\r\n        if (res && res.success) {\r\n          this.paperPreviewData = res.result\r\n          this.paperPreviewVisible = true\r\n          console.log('试卷预览数据:', this.paperPreviewData)\r\n        } else {\r\n          this.$message.error(res.message || '获取试卷预览失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取试卷预览失败:', error)\r\n        this.$message.error('获取试卷预览失败，请重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 格式化时长（精确显示）\r\n    formatDuration(duration) {\r\n      if (!duration) return '--'\r\n\r\n      const hours = Math.floor(duration / 3600)\r\n      const minutes = Math.floor((duration % 3600) / 60)\r\n      const seconds = duration % 60\r\n\r\n      if (hours > 0) {\r\n        // 超过1小时：显示 X时X分X秒\r\n        return `${hours}时${minutes}分${seconds}秒`\r\n      } else if (minutes > 0) {\r\n        // 1小时内但超过1分钟：显示 X分X秒\r\n        return `${minutes}分${seconds}秒`\r\n      } else {\r\n        // 1分钟内：只显示 X秒\r\n        return `${seconds}秒`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n</style> "], "mappings": ";+CAoHA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,gBAAAkB,OAAA,CAAAlB,CAAA,KAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,CAAAoB,OAAA,EAAAC,IAAA,WAAAnD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,EAAAqB,IAAA,WAAAnD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAAgD,2BAAA,eAAArD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAkD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA3B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAqB,KAAA,sCAAA/C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAsD,IAAA,eAAAlD,CAAA,CAAAmD,MAAA,GAAA9C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAoD,QAAA,MAAA3C,CAAA,QAAAE,CAAA,GAAA0C,mBAAA,CAAA5C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAmD,MAAA,EAAAnD,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAuD,KAAA,GAAAvD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAmD,MAAA,QAAAjD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAwD,iBAAA,CAAAxD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAmD,MAAA,IAAAnD,CAAA,CAAAyD,MAAA,WAAAzD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAkD,IAAA,GAAArB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAA0B,IAAA,EAAAlD,CAAA,CAAAkD,IAAA,kBAAAjB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAmD,MAAA,YAAAnD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA6B,oBAAA1D,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAsD,MAAA,EAAAjD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAuD,QAAA,qBAAApD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAkD,MAAA,KAAA7D,CAAA,CAAAsD,MAAA,aAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAyD,mBAAA,CAAA1D,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAsD,MAAA,kBAAAnD,CAAA,KAAAH,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,uCAAA3D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA2C,IAAA,IAAArD,CAAA,CAAAF,CAAA,CAAAiE,UAAA,IAAArD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAAgE,IAAA,GAAAlE,CAAA,CAAAmE,OAAA,eAAAjE,CAAA,CAAAsD,MAAA,KAAAtD,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,sCAAA9D,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,cAAAiC,aAAAnE,CAAA,QAAAD,CAAA,KAAAqE,MAAA,EAAApE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAsE,QAAA,GAAArE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAuE,UAAA,GAAAtE,CAAA,KAAAD,CAAA,CAAAwE,QAAA,GAAAvE,CAAA,WAAAwE,UAAA,CAAAC,IAAA,CAAA1E,CAAA,cAAA2E,cAAA1E,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA2E,UAAA,QAAA5E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA2E,UAAA,GAAA5E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAwE,UAAA,MAAAJ,MAAA,aAAApE,CAAA,CAAA4C,OAAA,CAAAuB,YAAA,cAAAS,KAAA,iBAAAnC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAkE,IAAA,SAAAlE,CAAA,OAAA8E,KAAA,CAAA9E,CAAA,CAAA+E,MAAA,SAAAxE,CAAA,OAAAG,CAAA,YAAAwD,KAAA,aAAA3D,CAAA,GAAAP,CAAA,CAAA+E,MAAA,OAAA1E,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA2D,IAAA,CAAAzD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA2D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAxD,CAAA,CAAAwD,IAAA,GAAAxD,CAAA,gBAAAsD,SAAA,CAAAf,OAAA,CAAAjD,CAAA,kCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA4C,WAAA,GAAA9D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAiF,mBAAA,aAAAhF,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAiF,WAAA,WAAAlF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAAgF,WAAA,IAAAhF,CAAA,CAAAmF,IAAA,OAAAnF,CAAA,CAAAoF,IAAA,aAAAnF,CAAA,WAAAE,MAAA,CAAAkF,cAAA,GAAAlF,MAAA,CAAAkF,cAAA,CAAApF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAqF,SAAA,GAAAjD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,aAAAkD,OAAA,EAAAlD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAwF,KAAA,aAAAvF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA+E,OAAA,OAAA7E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAiF,mBAAA,CAAA/E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAsD,IAAA,GAAAd,IAAA,WAAAnD,CAAA,WAAAA,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAsD,IAAA,WAAAtB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAA0F,IAAA,aAAAzF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAwE,IAAA,CAAArE,CAAA,UAAAH,CAAA,CAAAyF,OAAA,aAAAzB,KAAA,WAAAhE,CAAA,CAAA6E,MAAA,SAAA9E,CAAA,GAAAC,CAAA,CAAA0F,GAAA,QAAA3F,CAAA,IAAAD,CAAA,SAAAkE,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAlE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA8E,WAAA,EAAAzD,OAAA,EAAAoD,KAAA,WAAAA,MAAA7E,CAAA,aAAA6F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA3D,CAAA,OAAAsD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA3B,GAAA,GAAA5B,CAAA,OAAAwE,UAAA,CAAA5B,OAAA,CAAA8B,aAAA,IAAA3E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA4F,MAAA,OAAAzF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA4E,KAAA,EAAA5E,CAAA,CAAA6F,KAAA,cAAA7F,CAAA,IAAAD,CAAA,MAAA+F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAAtD,CAAA,QAAAwE,UAAA,IAAAG,UAAA,kBAAA3E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAoE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA7D,CAAA,aAAAuD,IAAA,QAAAvD,CAAA,MAAAE,CAAA,kBAAAgG,OAAA7F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAAgE,IAAA,GAAA7D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAkE,UAAA,CAAAM,MAAA,MAAAxE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA+D,UAAA,CAAAlE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAkE,UAAA,iBAAAlE,CAAA,CAAA2D,MAAA,SAAA6B,MAAA,aAAAxF,CAAA,CAAA2D,MAAA,SAAAwB,IAAA,QAAA/E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA6E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,gBAAAuB,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,cAAAzD,CAAA,aAAA+E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,qBAAAtD,CAAA,QAAAsC,KAAA,qDAAAuC,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,YAAAT,MAAA,WAAAA,OAAA7D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAuE,UAAA,CAAAM,MAAA,MAAA7E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAkE,UAAA,CAAAvE,CAAA,OAAAK,CAAA,CAAA8D,MAAA,SAAAwB,IAAA,IAAAxF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAsF,IAAA,GAAAtF,CAAA,CAAAgE,UAAA,QAAA7D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA2D,MAAA,IAAArE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA6D,UAAA,KAAA7D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAkE,UAAA,cAAAhE,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA8C,MAAA,gBAAAU,IAAA,GAAAxD,CAAA,CAAA6D,UAAA,EAAApC,CAAA,SAAAgE,QAAA,CAAAvF,CAAA,MAAAuF,QAAA,WAAAA,SAAAlG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAsC,IAAA,GAAAjE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAqE,IAAA,QAAApE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA2B,MAAA,kBAAAU,IAAA,yBAAAjE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAkE,IAAA,GAAAlE,CAAA,GAAAmC,CAAA,KAAAiE,MAAA,WAAAA,OAAAnG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAqE,UAAA,KAAAtE,CAAA,cAAAkG,QAAA,CAAAjG,CAAA,CAAA0E,UAAA,EAAA1E,CAAA,CAAAsE,QAAA,GAAAG,aAAA,CAAAzE,CAAA,GAAAiC,CAAA,OAAAkE,KAAA,WAAAC,OAAArG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAmE,MAAA,KAAApE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAA0E,UAAA,kBAAAvE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA8C,aAAA,CAAAzE,CAAA,YAAAK,CAAA,YAAA+C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAvG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAoD,QAAA,KAAA5C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAiE,UAAA,EAAA/D,CAAA,EAAAiE,OAAA,EAAA9D,CAAA,oBAAAmD,MAAA,UAAA3B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAwG,mBAAAnG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA6C,IAAA,GAAAtD,CAAA,CAAAe,CAAA,IAAAyE,OAAA,CAAAvC,OAAA,CAAAlC,CAAA,EAAAoC,IAAA,CAAAlD,CAAA,EAAAK,CAAA;AAAA,SAAAkG,kBAAApG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA0G,SAAA,aAAAjB,OAAA,WAAAvF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAA4G,MAAAvG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,UAAAxG,CAAA,cAAAwG,OAAAxG,CAAA,IAAAmG,kBAAA,CAAA5F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAqG,KAAA,EAAAC,MAAA,WAAAxG,CAAA,KAAAuG,KAAA;AAAA,SAAAE,QAAA9G,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAuF,IAAA,CAAA1F,CAAA,OAAAG,MAAA,CAAA4G,qBAAA,QAAAxG,CAAA,GAAAJ,MAAA,CAAA4G,qBAAA,CAAA/G,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAyG,MAAA,WAAA9G,CAAA,WAAAC,MAAA,CAAA8G,wBAAA,CAAAjH,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAyE,IAAA,CAAAiC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAAiH,cAAAlH,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAwG,SAAA,CAAA3B,MAAA,EAAA7E,CAAA,UAAAD,CAAA,WAAAyG,SAAA,CAAAxG,CAAA,IAAAwG,SAAA,CAAAxG,CAAA,QAAAA,CAAA,OAAA4G,OAAA,CAAA3G,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAAiH,eAAA,CAAAnH,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAAiH,yBAAA,GAAAjH,MAAA,CAAAkH,gBAAA,CAAArH,CAAA,EAAAG,MAAA,CAAAiH,yBAAA,CAAAnH,CAAA,KAAA6G,OAAA,CAAA3G,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAA8G,wBAAA,CAAAhH,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAAmH,gBAAAnH,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAoH,cAAA,CAAApH,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAsH,eAAArH,CAAA,QAAAS,CAAA,GAAA6G,YAAA,CAAAtH,CAAA,gCAAAgD,OAAA,CAAAvC,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAA6G,aAAAtH,CAAA,EAAAC,CAAA,oBAAA+C,OAAA,CAAAhD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAA6G,WAAA,kBAAAxH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,gCAAA+C,OAAA,CAAAvC,CAAA,UAAAA,CAAA,YAAAsD,SAAA,yEAAA9D,CAAA,GAAAuH,MAAA,GAAAC,MAAA,EAAAzH,CAAA;AAAA,SAAA0H,iBAAA,EAAAC,mBAAA,EAAAC,yBAAA;AACA,SAAAC,cAAA;AACA,OAAAC,eAAA;AACA,OAAAC,gBAAA;AAEA;EACA7C,IAAA;EACA8C,MAAA,GAAAH,cAAA;EAAA;EACAI,UAAA;IACAH,eAAA,EAAAA,eAAA;IACAC,gBAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,WAAA;MACA;MACAC,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,WAAA;MACA;MACA;MACAC,UAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAG,YAAA,WAAAA,aAAAC,IAAA,EAAAC,MAAA;UACA,UAAAC,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,MAAA,CAAAT,KAAA;QACA;MACA,GACA;QACAG,KAAA;QACAC,SAAA;QACAO,MAAA;MACA,GACA;QACAR,KAAA;QACAC,SAAA;QACAO,MAAA;MACA,GACA;QACAR,KAAA;QACAC,SAAA;QACAG,YAAA,WAAAA,aAAAK,CAAA,EAAAH,MAAA;UACA;UACA,IAAAI,QAAA,GAAAJ,MAAA,CAAAI,QAAA;UACA,KAAAA,QAAA;UACA;UACA,OAAAtB,KAAA,CAAAuB,cAAA,CAAAD,QAAA;QACA;QACAF,MAAA;MACA,GACA;QACAR,KAAA;QACAC,SAAA;QACAO,MAAA;QACAL,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAE,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAE,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MACA;MACA;;MAEA;MACAQ,kBAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,oBAAA;QACAC,YAAA;QACAC,QAAA;QACAC,WAAA;MACA;MACAC,mBAAA;MAEA;MACAC,mBAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,YAAAC,UAAA,CAAAxD,MAAA,WAAAyD,IAAA;QAAA,OAAAA,IAAA,CAAA3B,MAAA;MAAA,GAAA/D,MAAA;IACA;IACA;IACA2F,eAAA,WAAAA,gBAAA;MACA,YAAAF,UAAA,CAAAxD,MAAA,WAAAyD,IAAA;QAAA,OAAAA,IAAA,CAAA3B,MAAA,UAAA2B,IAAA,CAAAE,KAAA;MAAA,GAAA5F,MAAA;IACA;IACA;IACA6F,QAAA,WAAAA,SAAA;MACA,SAAAL,kBAAA;MACA,YAAAG,eAAA,QAAAH,kBAAA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA;IACAD,QAAA,WAAAA,SAAAjJ,GAAA;MAAA,IAAAmJ,MAAA;MACA,IAAAnJ,GAAA;QACA,KAAAoJ,WAAA,CAAAC,OAAA;MACA;MACA,KAAAC,OAAA;MACA,IAAAC,MAAA,GAAAlE,aAAA,UAAAwB,UAAA;MACA0C,MAAA,CAAAC,MAAA,QAAAJ,WAAA,CAAAC,OAAA;MACAE,MAAA,CAAAE,QAAA,QAAAL,WAAA,CAAAK,QAAA;MAEAC,OAAA,CAAAC,GAAA,mBAAAJ,MAAA;MAEAzD,iBAAA,CAAAyD,MAAA,EAAAhI,IAAA,WAAAqI,GAAA;QACAF,OAAA,CAAAC,GAAA,eAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,OAAA;UACA;UACA,IAAAD,GAAA,CAAAE,MAAA,IAAAF,GAAA,CAAAE,MAAA,CAAAC,OAAA;YACAZ,MAAA,CAAAR,UAAA,GAAAiB,GAAA,CAAAE,MAAA,CAAAC,OAAA;YACAZ,MAAA,CAAAC,WAAA,CAAAY,KAAA,GAAAJ,GAAA,CAAAE,MAAA,CAAAE,KAAA;UACA,WAAAC,KAAA,CAAAC,OAAA,CAAAN,GAAA,CAAAE,MAAA;YACAX,MAAA,CAAAR,UAAA,GAAAiB,GAAA,CAAAE,MAAA;YACAX,MAAA,CAAAC,WAAA,CAAAY,KAAA,GAAAJ,GAAA,CAAAE,MAAA,CAAA5G,MAAA;UACA;YACAiG,MAAA,CAAAR,UAAA;YACAQ,MAAA,CAAAC,WAAA,CAAAY,KAAA;UACA;UAEAN,OAAA,CAAAC,GAAA,aAAAR,MAAA,CAAAR,UAAA;UACAe,OAAA,CAAAC,GAAA,QAAAR,MAAA,CAAAC,WAAA,CAAAY,KAAA;QACA;UACAN,OAAA,CAAAS,KAAA,aAAAP,GAAA;UACA,IAAAQ,QAAA,GAAAR,GAAA,IAAAA,GAAA,CAAAS,OAAA,GAAAT,GAAA,CAAAS,OAAA;UACAlB,MAAA,CAAAmB,QAAA,CAAAH,KAAA,CAAAC,QAAA;;UAEA;UACA,IAAAA,QAAA,CAAAG,QAAA,aAAAH,QAAA,CAAAG,QAAA;YACApB,MAAA,CAAAmB,QAAA,CAAAE,OAAA;UACA;QACA;QACArB,MAAA,CAAAG,OAAA;MACA,GAAA9E,KAAA,WAAAiG,GAAA;QACAtB,MAAA,CAAAG,OAAA;QACAI,OAAA,CAAAS,KAAA,gBAAAM,GAAA;;QAEA;QACA,IAAAA,GAAA,CAAAC,QAAA;UACAhB,OAAA,CAAAS,KAAA,cAAAM,GAAA,CAAAC,QAAA,CAAAzD,MAAA;UACAyC,OAAA,CAAAS,KAAA,cAAAM,GAAA,CAAAC,QAAA,CAAApE,IAAA;UAEA,IAAAmE,GAAA,CAAAC,QAAA,CAAAzD,MAAA;YACAkC,MAAA,CAAAmB,QAAA,CAAAH,KAAA;UACA,WAAAM,GAAA,CAAAC,QAAA,CAAAzD,MAAA;YACAkC,MAAA,CAAAmB,QAAA,CAAAH,KAAA;UACA;YACA,IAAAQ,YAAA,GAAAF,GAAA,CAAAC,QAAA,CAAApE,IAAA,IAAAmE,GAAA,CAAAC,QAAA,CAAApE,IAAA,CAAA+D,OAAA,GAAAI,GAAA,CAAAC,QAAA,CAAApE,IAAA,CAAA+D,OAAA,GAAAI,GAAA,CAAAJ,OAAA;YACAlB,MAAA,CAAAmB,QAAA,CAAAH,KAAA,YAAAQ,YAAA;UACA;QACA;UACAxB,MAAA,CAAAmB,QAAA,CAAAH,KAAA;QACA;MACA;IACA;IAEA;IACAS,iBAAA,WAAAA,kBAAAC,UAAA,EAAAjD,CAAA,EAAAD,MAAA;MACA,KAAAyB,WAAA,CAAAC,OAAA,GAAAwB,UAAA,CAAAxB,OAAA;;MAEA;MACA,IAAA1B,MAAA,IAAAA,MAAA,CAAAmD,KAAA;QACA,KAAAjE,UAAA,CAAAkE,SAAA,GAAApD,MAAA,CAAAmD,KAAA;QACA,KAAAjE,UAAA,CAAAmE,SAAA,GAAArD,MAAA,CAAAsD,KAAA;MACA;QACA,YAAApE,UAAA,CAAAkE,SAAA;QACA,YAAAlE,UAAA,CAAAmE,SAAA;MACA;MAEA,KAAA/B,QAAA;IACA;IAEA;IACAiC,UAAA,WAAAA,WAAA;MACA,KAAArE,UAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA,KAAAkC,QAAA;IACA;IAEA;IACAkC,aAAA,WAAAA,cAAAlE,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAmE,aAAA,WAAAA,cAAAnE,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAoE,eAAA,WAAAA,gBAAA;MACA,IAAAvE,OAAA,QAAAD,UAAA,CAAAC,OAAA;MACA,KAAAA,OAAA;QACA;QACA,QACA;UAAAlI,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,EACA;MACA;MAEA,IAAAxE,OAAA;QACA,QACA;UAAAlI,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,EACA;MACA,WAAAxE,OAAA,iBAAAA,OAAA;QACA,QACA;UAAAlI,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,GACA;UAAA1M,KAAA;UAAA0M,KAAA;QAAA,EACA;MACA;MAEA;IACA;IAIA;IACAC,UAAA;MAAA,IAAAC,WAAA,GAAA5G,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,UAAAkI,QAAAhE,MAAA;QAAA,IAAAmC,GAAA,EAAA8B,UAAA,EAAAC,YAAA;QAAA,OAAAzN,mBAAA,GAAAuB,IAAA,UAAAmM,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA7H,IAAA,GAAA6H,QAAA,CAAAxJ,IAAA;YAAA;cAAAwJ,QAAA,CAAA7H,IAAA;cAEA,KAAAsF,OAAA;;cAEA;cAAAuC,QAAA,CAAAxJ,IAAA;cAAA,OACA0D,mBAAA,CAAA0B,MAAA,CAAAqE,EAAA;YAAA;cAAAlC,GAAA,GAAAiC,QAAA,CAAA/J,IAAA;cAEA,IAAA8H,GAAA,CAAAC,OAAA;gBACA6B,UAAA,GAAA9B,GAAA,CAAAE,MAAA,EAEA;gBACA6B,YAAA;gBACA,IAAAD,UAAA,CAAAC,YAAA;kBACA;oBACAA,YAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAN,UAAA,CAAAC,YAAA;oBACAjC,OAAA,CAAAC,GAAA,aAAAgC,YAAA;kBACA,SAAAxN,CAAA;oBACAuL,OAAA,CAAAS,KAAA,cAAAhM,CAAA;oBACA;oBACAwN,YAAA;sBACAxD,YAAA;wBAAAW,KAAA;wBAAAmD,UAAA;sBAAA;sBACA7D,QAAA;wBAAAU,KAAA;wBAAAmD,UAAA;sBAAA;sBACA5D,WAAA;wBAAAS,KAAA;wBAAAmD,UAAA;sBAAA;oBACA;kBACA;gBACA;kBACA;kBACAN,YAAA;oBACAxD,YAAA;sBAAAW,KAAA;sBAAAmD,UAAA;oBAAA;oBACA7D,QAAA;sBAAAU,KAAA;sBAAAmD,UAAA;oBAAA;oBACA5D,WAAA;sBAAAS,KAAA;sBAAAmD,UAAA;oBAAA;kBACA;gBACA;gBAEA,KAAAjE,iBAAA;kBACAc,KAAA,EAAA4C,UAAA,CAAA5C,KAAA;kBACAmD,UAAA,EAAAP,UAAA,CAAA5C,KAAA;kBACAoD,QAAA,GAAAR,UAAA,CAAA5C,KAAA;kBACAqD,UAAA,EAAAT,UAAA,CAAAU,OAAA;kBACAC,UAAA,EAAAX,UAAA,CAAAW,UAAA;kBACAC,OAAA,EAAAX;gBACA;;gBAEA;gBACA,KAAA1D,gBAAA;kBACAd,KAAA,EAAAuE,UAAA,CAAAW,UAAA;kBACAvF,OAAA,EAAA4E,UAAA,CAAA5E,OAAA;kBACAE,KAAA,EAAA0E,UAAA,CAAA1E,KAAA;kBACAuF,UAAA,EAAAb,UAAA,CAAAa,UAAA;kBACAxM,IAAA,EAAA2L,UAAA,CAAA3L,IAAA;kBACAyM,IAAA,EAAAd,UAAA,CAAAc,IAAA;kBACAC,YAAA,EAAAf,UAAA,CAAAe;gBACA;;gBAEA;gBACA,IAAAf,UAAA,CAAAgB,SAAA,IAAAhB,UAAA,CAAAU,OAAA;kBACA,KAAA9D,mBAAA,GAAAqE,IAAA,CAAAC,KAAA,MAAAC,IAAA,CAAAnB,UAAA,CAAAU,OAAA,EAAAU,OAAA,SAAAD,IAAA,CAAAnB,UAAA,CAAAgB,SAAA,EAAAI,OAAA;gBACA;kBACA,KAAAxE,mBAAA;gBACA;;gBAEA;gBACA,KAAAP,kBAAA;cAEA;gBACA,KAAAuC,QAAA,CAAAH,KAAA,CAAAP,GAAA,CAAAS,OAAA;cACA;cAAAwB,QAAA,CAAAxJ,IAAA;cAAA;YAAA;cAAAwJ,QAAA,CAAA7H,IAAA;cAAA6H,QAAA,CAAAkB,EAAA,GAAAlB,QAAA;cAEAnC,OAAA,CAAAS,KAAA,gBAAA0B,QAAA,CAAAkB,EAAA;cACA,KAAAzC,QAAA,CAAAH,KAAA;YAAA;cAAA0B,QAAA,CAAA7H,IAAA;cAEA,KAAAsF,OAAA;cAAA,OAAAuC,QAAA,CAAAtH,MAAA;YAAA;YAAA;cAAA,OAAAsH,QAAA,CAAA1H,IAAA;UAAA;QAAA,GAAAsH,OAAA;MAAA;MAAA,SAAAF,WAAAyB,EAAA;QAAA,OAAAxB,WAAA,CAAA1G,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA0G,UAAA;IAAA;IAIA;IACA0B,SAAA;MAAA,IAAAC,UAAA,GAAAtI,iBAAA,eAAA1G,mBAAA,GAAAqF,IAAA,UAAA4J,SAAA1F,MAAA;QAAA,IAAAmC,GAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAA2N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArJ,IAAA,GAAAqJ,SAAA,CAAAhL,IAAA;YAAA;cACAqH,OAAA,CAAAC,GAAA,UAAAlC,MAAA;cAAA4F,SAAA,CAAArJ,IAAA;cAGA,KAAAsF,OAAA;;cAEA;cAAA+D,SAAA,CAAAhL,IAAA;cAAA,OACA2D,yBAAA,CAAAyB,MAAA,CAAAqE,EAAA;YAAA;cAAAlC,GAAA,GAAAyD,SAAA,CAAAvL,IAAA;cAEA,IAAA8H,GAAA,IAAAA,GAAA,CAAAC,OAAA;gBACA,KAAArB,gBAAA,GAAAoB,GAAA,CAAAE,MAAA;gBACA,KAAAvB,mBAAA;gBACAmB,OAAA,CAAAC,GAAA,iBAAAnB,gBAAA;cACA;gBACA,KAAA8B,QAAA,CAAAH,KAAA,CAAAP,GAAA,CAAAS,OAAA;cACA;cAAAgD,SAAA,CAAAhL,IAAA;cAAA;YAAA;cAAAgL,SAAA,CAAArJ,IAAA;cAAAqJ,SAAA,CAAAN,EAAA,GAAAM,SAAA;cAEA3D,OAAA,CAAAS,KAAA,cAAAkD,SAAA,CAAAN,EAAA;cACA,KAAAzC,QAAA,CAAAH,KAAA;YAAA;cAAAkD,SAAA,CAAArJ,IAAA;cAEA,KAAAsF,OAAA;cAAA,OAAA+D,SAAA,CAAA9I,MAAA;YAAA;YAAA;cAAA,OAAA8I,SAAA,CAAAlJ,IAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA;MAAA,SAAAF,UAAAK,GAAA;QAAA,OAAAJ,UAAA,CAAApI,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAoI,SAAA;IAAA;IAIA;IACAnF,cAAA,WAAAA,eAAAD,QAAA;MACA,KAAAA,QAAA;MAEA,IAAA0F,KAAA,GAAAZ,IAAA,CAAAC,KAAA,CAAA/E,QAAA;MACA,IAAA2F,OAAA,GAAAb,IAAA,CAAAC,KAAA,CAAA/E,QAAA;MACA,IAAA4F,OAAA,GAAA5F,QAAA;MAEA,IAAA0F,KAAA;QACA;QACA,UAAA7F,MAAA,CAAA6F,KAAA,YAAA7F,MAAA,CAAA8F,OAAA,YAAA9F,MAAA,CAAA+F,OAAA;MACA,WAAAD,OAAA;QACA;QACA,UAAA9F,MAAA,CAAA8F,OAAA,YAAA9F,MAAA,CAAA+F,OAAA;MACA;QACA;QACA,UAAA/F,MAAA,CAAA+F,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}