{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"SysMessageModal\",\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        disableSubmit: true,\n        url: {\n          add: \"/message/sysMessage/add\",\n          edit: \"/message/sysMessage/edit\",\n        },\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'esContent', 'esParam', 'esReceiver', 'esResult', 'esSendNum', 'esSendStatus', 'esTitle', 'esType', 'remark'))\n          //时间格式化\n          this.form.setFieldsValue({esSendTime: this.model.esSendTime ? moment(this.model.esSendTime) : null})\n        });\n\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.esSendTime = formData.esSendTime ? formData.esSendTime.format('YYYY-MM-DD HH:mm:ss') : null;\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      },\n\n\n    }\n  }\n", {"version": 3, "sources": ["SysMessageModal.vue"], "names": [], "mappings": ";AAsFA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA", "file": "SysMessageModal.vue", "sourceRoot": "src/views/modules/message/modules", "sourcesContent": ["<template>\n  <a-drawer\n    :title=\"title\"\n    :maskClosable=\"true\"\n    width=650\n    placement=\"right\"\n    :closable=\"true\"\n    @close=\"close\"\n    :visible=\"visible\"\n    style=\"height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"消息标题\">\n          <a-input placeholder=\"请输入消息标题\" v-decorator=\"['esTitle', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送内容\">\n          <a-input placeholder=\"请输入发送内容\" v-decorator=\"['esContent', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送所需参数\">\n          <a-input placeholder=\"请输入发送所需参数Json格式\" v-decorator=\"['esParam', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"接收人\">\n          <a-input placeholder=\"请输入接收人\" v-decorator=\"['esReceiver', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送方式\">\n          <j-dict-select-tag :triggerChange=\"true\" dictCode=\"msgType\" v-decorator=\"[ 'esType', {}]\" placeholder=\"请选择发送方式\">\n          </j-dict-select-tag>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送时间\">\n          <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator=\"[ 'esSendTime', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送状态\">\n          <j-dict-select-tag :triggerChange=\"true\" dictCode=\"msgSendStatus\" v-decorator=\"[ 'esSendStatus', {}]\" placeholder=\"请选择发送状态\">\n          </j-dict-select-tag>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送次数\">\n          <a-input-number v-decorator=\"[ 'esSendNum', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送失败原因\">\n          <a-input v-decorator=\"['esResult', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"备注\">\n          <a-input v-decorator=\"['remark', {}]\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n    <div v-show=\"!disableSubmit\">\n      <a-button style=\"margin-right: .8rem\" @confirm=\"handleCancel\">取消</a-button>\n      <a-button @click=\"handleOk\" type=\"primary\" :loading=\"confirmLoading\">提交</a-button>\n    </div>\n  </a-drawer>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"SysMessageModal\",\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        disableSubmit: true,\n        url: {\n          add: \"/message/sysMessage/add\",\n          edit: \"/message/sysMessage/edit\",\n        },\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'esContent', 'esParam', 'esReceiver', 'esResult', 'esSendNum', 'esSendStatus', 'esTitle', 'esType', 'remark'))\n          //时间格式化\n          this.form.setFieldsValue({esSendTime: this.model.esSendTime ? moment(this.model.esSendTime) : null})\n        });\n\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.esSendTime = formData.esSendTime ? formData.esSendTime.format('YYYY-MM-DD HH:mm:ss') : null;\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      },\n\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}