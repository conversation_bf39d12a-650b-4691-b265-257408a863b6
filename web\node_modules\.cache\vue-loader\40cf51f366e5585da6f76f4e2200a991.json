{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseListCard.vue?vue&type=template&id=c5e139de&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"app-list\">\n  <a-card v-for=\"item in dataSource\" :key=\"item.id\" :hoverable=\"true\">\n    <!-- <template class=\"ant-card-extra\" slot=\"extra\">\n      <span class=\"create-time\">{{item.createTime}}</span>\n    </template> -->\n    <a-card-meta>\n      <div style=\"margin-bottom: 3px\" slot=\"title\">\n        <a-icon type=\"info-circle\" @click=\"toDetail(item)\"/>\n        <a-divider type=\"vertical\"></a-divider>\n        <span @click=\"toCourse(item.showType, item.id)\">{{ item.courseName }}</span>\n      </div>\n      <div @click=\"toCourse(item.showType, item.id)\" class=\"meta-cardInfo\" slot=\"description\">\n          <img\n            :src=\"getFileAccessHttpUrl(item.courseCover)\"\n            height=\"25px\"\n            style=\"width: 100%; height: 100%\"\n          />\n      </div>\n    </a-card-meta>\n  </a-card>\n  <j-modal \n    :visible=\"showCourseDetail\" \n    :title=\"currentCourse.courseName\"\n    :width=\"800\"\n    :footer=\"null\"\n    @cancel=\"showCourseDetail=false\"\n    >\n      <div v-html=\"currentCourse.courseDesc\"></div>\n    </j-modal>\n</div>\n", null]}