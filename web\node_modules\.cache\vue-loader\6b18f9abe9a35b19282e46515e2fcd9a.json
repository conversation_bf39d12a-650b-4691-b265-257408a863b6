{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue?vue&type=template&id=3eee41ef&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  title=\"导入EXCEL\"\n  :width=\"600\"\n  :visible=\"visible\"\n  :confirmLoading=\"uploading\"\n  @cancel=\"handleClose\">\n\n  <a-upload\n    name=\"file\"\n    :multiple=\"true\"\n    accept=\".xls,.xlsx\"\n    :fileList=\"fileList\"\n    :remove=\"handleRemove\"\n    :beforeUpload=\"beforeUpload\">\n    <a-button>\n      <a-icon type=\"upload\" />\n      选择导入文件\n    </a-button>\n  </a-upload>\n\n  <template slot=\"footer\">\n    <a-button @click=\"handleClose\">关闭</a-button>\n    <a-button\n      type=\"primary\"\n      @click=\"handleImport\"\n      :disabled=\"fileList.length === 0\"\n      :loading=\"uploading\">\n      {{ uploading ? '上传中...' : '开始上传' }}\n    </a-button>\n  </template>\n\n</a-modal>\n", null]}