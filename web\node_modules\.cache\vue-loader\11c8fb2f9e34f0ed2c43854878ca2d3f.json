{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\RowspanTable.vue?vue&type=template&id=255ac045&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\RowspanTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <!-- table区域-begin -->\n  <a-table\n    ref=\"table\"\n    size=\"default\"\n    bordered\n    rowKey=\"id\"\n    :columns=\"columns\"\n    :pagination=\"false\"\n    :dataSource=\"dataSource\">\n\n  </a-table>\n  <!-- table区域-end -->\n\n\n</a-card>\n", null]}