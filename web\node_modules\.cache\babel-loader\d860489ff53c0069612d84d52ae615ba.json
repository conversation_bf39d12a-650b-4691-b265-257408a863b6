{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageTemplateModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { httpAction } from '@/api/manage';\nimport pick from 'lodash.pick';\nimport { duplicateCheck } from '@/api/api';\nimport JEditor from '@/components/jeecg/JEditor';\nexport default {\n  name: \"SysMessageTemplateModal\",\n  components: {\n    JEditor: JEditor\n  },\n  data: function data() {\n    return {\n      title: \"操作\",\n      visible: false,\n      disable: true,\n      model: {},\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      validatorRules: {\n        templateCode: {\n          rules: [{\n            required: true,\n            message: '请输入模板CODE!'\n          }, {\n            validator: this.validateTemplateCode\n          }]\n        },\n        templateName: {\n          rules: [{\n            required: true,\n            message: '请输入模板标题!'\n          }]\n        },\n        templateContent: {\n          rules: []\n        },\n        templateType: {\n          rules: [{\n            required: true,\n            message: '请输入模板类型!'\n          }]\n        }\n      },\n      url: {\n        add: \"/message/sysMessageTemplate/add\",\n        edit: \"/message/sysMessageTemplate/edit\"\n      },\n      useEditor: false,\n      templateEditorContent: \"\"\n    };\n  },\n  created: function created() {},\n  methods: {\n    add: function add() {\n      this.disable = false;\n      this.edit({});\n    },\n    edit: function edit(record) {\n      var _this = this;\n      this.form.resetFields();\n      this.model = Object.assign({}, record);\n      this.useEditor = record.templateType == 2 || record.templateType == 4;\n      if (this.useEditor) {\n        this.templateEditorContent = record.templateContent;\n      } else {\n        this.templateEditorContent = '';\n      }\n      this.visible = true;\n      this.$nextTick(function () {\n        if (_this.useEditor) {\n          _this.form.setFieldsValue(pick(_this.model, 'templateCode', 'templateName', 'templateTestJson', 'templateType'));\n        } else {\n          _this.form.setFieldsValue(pick(_this.model, 'templateCode', 'templateContent', 'templateName', 'templateTestJson', 'templateType'));\n        }\n      });\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n      this.disable = true;\n    },\n    handleOk: function handleOk() {\n      var _this2 = this;\n      this.model.templateType = this.templateType;\n      var that = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n          var httpurl = '';\n          var method = '';\n          if (!_this2.model.id) {\n            httpurl += _this2.url.add;\n            method = 'post';\n          } else {\n            httpurl += _this2.url.edit;\n            method = 'put';\n          }\n          var formData = Object.assign(_this2.model, values);\n          //时间格式化\n\n          if (_this2.useEditor) {\n            formData.templateContent = _this2.templateEditorContent;\n          }\n          console.log(formData);\n          httpAction(httpurl, formData, method).then(function (res) {\n            if (res.success) {\n              that.$message.success(res.message);\n              that.$emit('ok');\n            } else {\n              that.$message.warning(res.message);\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n            that.close();\n          });\n        }\n      });\n    },\n    validateTemplateCode: function validateTemplateCode(rule, value, callback) {\n      var params = {\n        tableName: \"sys_sms_template\",\n        fieldName: \"template_code\",\n        fieldVal: value,\n        dataId: this.model.id\n      };\n      duplicateCheck(params).then(function (res) {\n        if (res.success) {\n          callback();\n        } else {\n          callback(res.message);\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    handleChangeTemplateType: function handleChangeTemplateType(value) {\n      //如果是邮件类型那么则改变模板内容是富文本编辑器\n      this.useEditor = value == 2 || value == 4;\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "pick", "duplicate<PERSON><PERSON><PERSON>", "JEditor", "name", "components", "data", "title", "visible", "disable", "model", "labelCol", "xs", "span", "sm", "wrapperCol", "confirmLoading", "form", "$form", "createForm", "validatorRules", "templateCode", "rules", "required", "message", "validator", "validateTemplateCode", "templateName", "templateContent", "templateType", "url", "add", "edit", "useEditor", "templateEditorContent", "created", "methods", "record", "_this", "resetFields", "Object", "assign", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "$emit", "handleOk", "_this2", "that", "validateFields", "err", "values", "httpurl", "method", "id", "formData", "console", "log", "then", "res", "success", "$message", "warning", "finally", "rule", "value", "callback", "params", "tableName", "fieldName", "fieldVal", "dataId", "handleCancel", "handleChangeTemplateType"], "sources": ["src/views/modules/message/modules/SysMessageTemplateModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <a-row :gutter=\"{ xs: 8, sm: 16, md: 24, lg: 32 }\">\n          <a-col :span=\"12\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板CODE\"\n              style=\"margin-right: -35px\"\n            >\n              <a-input\n                :disabled=\"disable\"\n                placeholder=\"请输入模板编码\"\n                v-decorator=\"['templateCode', validatorRules.templateCode ]\"\n              />\n            </a-form-item>\n          </a-col>\n          <a-col :span=\"12\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板类型\">\n              <j-dict-select-tag  @change=\"handleChangeTemplateType\" :triggerChange=\"true\" dictCode=\"msgType\" v-decorator=\"['templateType', validatorRules.templateType ]\" placeholder=\"请选择模板类型\">\n              </j-dict-select-tag>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\" >\n          <a-col :span=\"24\" pull=\"2\">\n            <a-form-item\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板标题\"\n              style=\"margin-left: -15px\">\n              <a-input\n                placeholder=\"请输入模板标题\"\n                v-decorator=\"['templateName', validatorRules.templateName]\"\n                style=\"width: 122%\"\n              />\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\">\n          <a-col :span=\"24\" pull=\"4\">\n            <a-form-item\n              v-show=\"!useEditor\"\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板内容\"\n              style=\"margin-left: 4px;width: 126%\">\n              <a-textarea placeholder=\"请输入模板内容\" v-decorator=\"['templateContent', validatorRules.templateContent ]\" :autosize=\"{ minRows: 8, maxRows: 8 }\"/>\n            </a-form-item>\n          </a-col>\n        </a-row>\n        <a-row class=\"form-row\" :gutter=\"24\">\n          <a-col :span=\"24\" pull=\"4\">\n            <a-form-item\n              v-show=\"useEditor\"\n              :labelCol=\"labelCol\"\n              :wrapperCol=\"wrapperCol\"\n              label=\"模板内容\"\n              style=\"margin-left: 4px;width: 126%\">\n              <j-editor  v-model=\"templateEditorContent\"></j-editor>\n            </a-form-item>\n          </a-col>\n        </a-row>\n\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import { duplicateCheck } from '@/api/api'\n  import JEditor from '@/components/jeecg/JEditor'\n\n  export default {\n    name: \"SysMessageTemplateModal\",\n    components:{\n      JEditor\n    },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        disable: true,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {\n        templateCode: {rules: [{required: true, message: '请输入模板CODE!' },{validator: this.validateTemplateCode}]},\n        templateName: {rules: [{required: true, message: '请输入模板标题!'}]},\n        templateContent: {rules: []},\n        templateType: {rules: [{required: true, message: '请输入模板类型!'}]},\n        },\n        url: {\n          add: \"/message/sysMessageTemplate/add\",\n          edit: \"/message/sysMessageTemplate/edit\",\n        },\n        useEditor:false,\n        templateEditorContent:\"\"\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.disable = false;\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.useEditor = (record.templateType==2 || record.templateType==4)\n        if(this.useEditor){\n          this.templateEditorContent=record.templateContent\n        }else{\n          this.templateEditorContent=''\n        }\n        this.visible = true;\n        this.$nextTick(() => {\n          if(this.useEditor){\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateName', 'templateTestJson', 'templateType'))\n          }else{\n            this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateContent', 'templateName', 'templateTestJson', 'templateType'))\n          }\n        });\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n        this.disable = true;\n      },\n      handleOk() {\n        this.model.templateType = this.templateType;\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n\n            if(this.useEditor){\n              formData.templateContent=this.templateEditorContent\n            }\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      validateTemplateCode(rule, value, callback){\n        var params = {\n          tableName: \"sys_sms_template\",\n          fieldName: \"template_code\",\n          fieldVal: value,\n          dataId: this.model.id\n        }\n        duplicateCheck(params).then((res)=>{\n          if(res.success){\n            callback();\n          }else{\n            callback(res.message);\n          }\n        })\n\n      },\n      handleCancel() {\n        this.close()\n      },\n      handleChangeTemplateType(value){\n        //如果是邮件类型那么则改变模板内容是富文本编辑器\n        this.useEditor = (value==2 || value==4)\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAmFA,SAAAA,UAAA;AACA,OAAAC,IAAA;AACA,SAAAC,cAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,OAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAG,cAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,cAAA;QACAC,YAAA;UAAAC,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;YAAAC,SAAA,OAAAC;UAAA;QAAA;QACAC,YAAA;UAAAL,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;QAAA;QACAI,eAAA;UAAAN,KAAA;QAAA;QACAO,YAAA;UAAAP,KAAA;YAAAC,QAAA;YAAAC,OAAA;UAAA;QAAA;MACA;MACAM,GAAA;QACAC,GAAA;QACAC,IAAA;MACA;MACAC,SAAA;MACAC,qBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAL,GAAA,WAAAA,IAAA;MACA,KAAAtB,OAAA;MACA,KAAAuB,IAAA;IACA;IACAA,IAAA,WAAAA,KAAAK,MAAA;MAAA,IAAAC,KAAA;MACA,KAAArB,IAAA,CAAAsB,WAAA;MACA,KAAA7B,KAAA,GAAA8B,MAAA,CAAAC,MAAA,KAAAJ,MAAA;MACA,KAAAJ,SAAA,GAAAI,MAAA,CAAAR,YAAA,SAAAQ,MAAA,CAAAR,YAAA;MACA,SAAAI,SAAA;QACA,KAAAC,qBAAA,GAAAG,MAAA,CAAAT,eAAA;MACA;QACA,KAAAM,qBAAA;MACA;MACA,KAAA1B,OAAA;MACA,KAAAkC,SAAA;QACA,IAAAJ,KAAA,CAAAL,SAAA;UACAK,KAAA,CAAArB,IAAA,CAAA0B,cAAA,CAAA1C,IAAA,CAAAqC,KAAA,CAAA5B,KAAA;QACA;UACA4B,KAAA,CAAArB,IAAA,CAAA0B,cAAA,CAAA1C,IAAA,CAAAqC,KAAA,CAAA5B,KAAA;QACA;MACA;IACA;IACAkC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAArC,OAAA;MACA,KAAAC,OAAA;IACA;IACAqC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,KAAArC,KAAA,CAAAmB,YAAA,QAAAA,YAAA;MACA,IAAAmB,IAAA;MACA;MACA,KAAA/B,IAAA,CAAAgC,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAF,IAAA,CAAAhC,cAAA;UACA,IAAAoC,OAAA;UACA,IAAAC,MAAA;UACA,KAAAN,MAAA,CAAArC,KAAA,CAAA4C,EAAA;YACAF,OAAA,IAAAL,MAAA,CAAAjB,GAAA,CAAAC,GAAA;YACAsB,MAAA;UACA;YACAD,OAAA,IAAAL,MAAA,CAAAjB,GAAA,CAAAE,IAAA;YACAqB,MAAA;UACA;UACA,IAAAE,QAAA,GAAAf,MAAA,CAAAC,MAAA,CAAAM,MAAA,CAAArC,KAAA,EAAAyC,MAAA;UACA;;UAEA,IAAAJ,MAAA,CAAAd,SAAA;YACAsB,QAAA,CAAA3B,eAAA,GAAAmB,MAAA,CAAAb,qBAAA;UACA;UACAsB,OAAA,CAAAC,GAAA,CAAAF,QAAA;UACAvD,UAAA,CAAAoD,OAAA,EAAAG,QAAA,EAAAF,MAAA,EAAAK,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAZ,IAAA,CAAAa,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAnC,OAAA;cACAwB,IAAA,CAAAH,KAAA;YACA;cACAG,IAAA,CAAAa,QAAA,CAAAC,OAAA,CAAAH,GAAA,CAAAnC,OAAA;YACA;UACA,GAAAuC,OAAA;YACAf,IAAA,CAAAhC,cAAA;YACAgC,IAAA,CAAAJ,KAAA;UACA;QAGA;MACA;IACA;IACAlB,oBAAA,WAAAA,qBAAAsC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,MAAA;QACAC,SAAA;QACAC,SAAA;QACAC,QAAA,EAAAL,KAAA;QACAM,MAAA,OAAA7D,KAAA,CAAA4C;MACA;MACApD,cAAA,CAAAiE,MAAA,EAAAT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAM,QAAA;QACA;UACAA,QAAA,CAAAP,GAAA,CAAAnC,OAAA;QACA;MACA;IAEA;IACAgD,YAAA,WAAAA,aAAA;MACA,KAAA5B,KAAA;IACA;IACA6B,wBAAA,WAAAA,yBAAAR,KAAA;MACA;MACA,KAAAhC,SAAA,GAAAgC,KAAA,SAAAA,KAAA;IACA;EAEA;AACA", "ignoreList": []}]}