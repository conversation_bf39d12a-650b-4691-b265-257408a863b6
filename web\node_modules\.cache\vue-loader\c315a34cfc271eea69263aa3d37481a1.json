{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue?vue&type=template&id=1cca1d35&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"app-list\">\n  <a-card v-for=\"item in dataSource\" :key=\"item.id\" @click=\"viewUnit(item)\">\n    <a-card-meta>\n        <div style=\"margin-bottom: 3px\" slot=\"title\">\n          <a-icon type=\"right-circle\"/>&nbsp;&nbsp;\n          {{ item.unitName }}\n        </div>\n        <div class=\"meta-cardInfo\" slot=\"description\">\n          <img :src=\"getFileAccessHttpUrl(item.unitCover)\" height=\"25px\" style=\"width:100%;height:100%;\"/>\n        </div>\n    </a-card-meta>\n  </a-card>\n  <unitView-modal ref=\"unitViewModal\"/>\n</div>\n", null]}