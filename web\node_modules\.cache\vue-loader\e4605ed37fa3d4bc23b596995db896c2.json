{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\exception\\ExceptionPage.vue?vue&type=template&id=75112974&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\exception\\ExceptionPage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"exception\">\n  <div class=\"img\">\n    <img :src=\"config[type].img\"/>\n  </div>\n  <div class=\"content\">\n    <h1>{{ config[type].title }}</h1>\n    <div class=\"desc\">{{ config[type].desc }}</div>\n    <div class=\"action\">\n      <a-button type=\"primary\" @click=\"handleToHome\">返回首页</a-button>\n    </div>\n  </div>\n</div>\n", null]}