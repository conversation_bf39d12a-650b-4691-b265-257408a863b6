{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\MediaWall.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\MediaWall.vue", "mtime": 1749711343444}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'MediaWall',\n  props: {\n    mediaItems: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      mediaViewerVisible: false,\n      currentMediaIndex: 0,\n      localMediaItems: [], // 本地存储的媒体项列表\n      sysConfig: {}\n    }\n  },\n  computed: {\n    currentMedia() {\n      return this.effectiveMediaItems[this.currentMediaIndex] || null\n    },\n    effectiveMediaItems() {\n      // 优先使用父组件传入的数据，如果没有则使用从服务器获取的数据\n      return this.mediaItems.length > 0 ? this.mediaItems : this.localMediaItems\n    }\n  },\n  created() {\n    // 加载系统配置\n    this.sysConfig = this.$store.getters.sysConfig;\n    \n    // 如果没有传入mediaItems，则从服务器获取\n    if (this.mediaItems.length === 0) {\n      this.loadMediaItemsFromServer()\n    }\n  },\n  methods: {\n    loadMediaItemsFromServer() {\n      // 按照Banner组件的方式处理数据\n      if (this.sysConfig.photoWallMedia_urls) {\n        // 使用新格式\n        const urls = this.sysConfig.photoWallMedia_urls.split(',');\n        const thumbnails = this.sysConfig.photoWallMedia_thumbnails ? this.sysConfig.photoWallMedia_thumbnails.split(',') : [];\n        const types = this.sysConfig.photoWallMedia_types ? this.sysConfig.photoWallMedia_types.split(',') : [];\n        const titles = this.sysConfig.photoWallMedia_titles ? this.sysConfig.photoWallMedia_titles.split(',') : [];\n        \n        this.localMediaItems = [];\n        \n        for (let i = 0; i < urls.length; i++) {\n          let item = {\n            id: i + 1,\n            type: types[i] || 'image',\n            title: titles[i] || `媒体${i + 1}`,\n            url: getFileAccessHttpUrl(urls[i]),\n            thumbnail: thumbnails[i] ? getFileAccessHttpUrl(thumbnails[i]) : getFileAccessHttpUrl(urls[i])\n          };\n          this.localMediaItems.push(item);\n        }\n      } else if (this.sysConfig.photoWallMedia) {\n        // 使用旧格式\n        try {\n          let items = JSON.parse(this.sysConfig.photoWallMedia || '[]');\n          this.localMediaItems = items.map(item => ({\n            ...item,\n            url: getFileAccessHttpUrl(item.url),\n            thumbnail: getFileAccessHttpUrl(item.thumbnail || item.url)\n          }));\n        } catch (e) {\n          this.localMediaItems = [];\n        }\n      } else {\n        this.localMediaItems = [];\n      }\n    },\n    \n    openMediaViewer(index) {\n      this.currentMediaIndex = index\n      this.mediaViewerVisible = true\n    },\n    closeMediaViewer() {\n      this.mediaViewerVisible = false\n      // 如果是视频，停止播放\n      if (this.currentMedia && this.currentMedia.type === 'video') {\n        const videoElement = document.querySelector('.viewer-video')\n        if (videoElement) {\n          videoElement.pause()\n        }\n      }\n    },\n    navigateMedia(step) {\n      const newIndex = this.currentMediaIndex + step\n      \n      if (newIndex >= 0 && newIndex < this.effectiveMediaItems.length) {\n        this.currentMediaIndex = newIndex\n        \n        // 如果是视频，暂停当前视频\n        const videoElement = document.querySelector('.viewer-video')\n        if (videoElement) {\n          videoElement.pause()\n        }\n      }\n    },\n    getRandomRotation(index) {\n      // 使用索引来创建更自然的旋转效果，确保相邻照片的角度不同\n      const seeds = [-7, -4, -2, 0, 2, 4, 7];\n      const base = seeds[index % seeds.length];\n      // 再增加一点随机性，但保持在合理范围内\n      return base + Math.random() * 2 - 1;\n    },\n    // 处理图片URL的方法\n    getFileUrl(path) {\n      return getFileAccessHttpUrl(path);\n    }\n  }\n}\n", {"version": 3, "sources": ["MediaWall.vue"], "names": [], "mappings": ";AAiGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MediaWall.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<!-- 照片墙组件 - 展示图片和视频，支持点击放大查看 --><template>\n  <div class=\"media-wall\">\n    <div class=\"media-wall-header\">\n      <h2>小小创客照片墙</h2>\n    </div>\n\n    <div class=\"media-wall-content\">\n      <div v-if=\"effectiveMediaItems.length > 0\" class=\"polaroid-grid\">\n        <div \n          v-for=\"(item, index) in effectiveMediaItems\" \n          :key=\"index\" \n          class=\"media-item polaroid\"\n          :style=\"{ transform: `rotate(${getRandomRotation(index)}deg)` }\"\n          @click=\"openMediaViewer(index)\"\n        >\n          <div class=\"polaroid-wrapper\">\n            <img \n              v-if=\"item.type === 'image'\" \n              :src=\"getFileUrl(item.thumbnail || item.url)\" \n              alt=\"缩略图\" \n              class=\"media-thumbnail\"\n            />\n            <div v-if=\"item.type === 'video'\" class=\"video-thumbnail-container\">\n              <img :src=\"getFileUrl(item.thumbnail)\" alt=\"视频封面\" class=\"media-thumbnail\" />\n              <div class=\"video-play-icon\">\n                <a-icon type=\"play-circle\" />\n              </div>\n            </div>\n            <div class=\"media-caption\">\n              <span>{{ item.title }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"empty-media-container\">\n        <a-empty description=\"暂无照片\" />\n      </div>\n    </div>\n\n    <!-- 媒体查看器弹窗 -->\n    <a-modal\n      v-model=\"mediaViewerVisible\"\n      :footer=\"null\"\n      :width=\"900\"\n      :closable=\"false\"\n      :maskClosable=\"true\"\n      @cancel=\"closeMediaViewer\"\n      centered\n      destroyOnClose\n      class=\"media-viewer-modal\"\n    >\n      <div class=\"media-viewer\">\n        <div class=\"media-viewer-content\">\n          <img \n            v-if=\"currentMedia && currentMedia.type === 'image'\" \n            :src=\"getFileUrl(currentMedia.url)\" \n            class=\"viewer-image\"\n            alt=\"查看大图\"\n          />\n          <video \n            v-if=\"currentMedia && currentMedia.type === 'video'\"\n            :src=\"getFileUrl(currentMedia.url)\"\n            class=\"viewer-video\"\n            controls\n            autoplay\n          ></video>\n        </div>\n\n        <div class=\"media-viewer-footer\">\n          <div class=\"media-info\">\n            <h3>{{ currentMedia ? currentMedia.title : '' }}</h3>\n          </div>\n          <div class=\"media-navigation\">\n            <a-button \n              shape=\"circle\" \n              icon=\"left\"\n              @click=\"navigateMedia(-1)\"\n              :disabled=\"currentMediaIndex === 0\"\n            ></a-button>\n            <span class=\"media-count\">{{ currentMediaIndex + 1 }} / {{ effectiveMediaItems.length }}</span>\n            <a-button \n              shape=\"circle\" \n              icon=\"right\"\n              @click=\"navigateMedia(1)\"\n              :disabled=\"currentMediaIndex === effectiveMediaItems.length - 1\"\n            ></a-button>\n          </div>\n          <div class=\"media-actions\">\n            <a-button type=\"primary\" @click=\"closeMediaViewer\">关闭</a-button>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'MediaWall',\n  props: {\n    mediaItems: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      mediaViewerVisible: false,\n      currentMediaIndex: 0,\n      localMediaItems: [], // 本地存储的媒体项列表\n      sysConfig: {}\n    }\n  },\n  computed: {\n    currentMedia() {\n      return this.effectiveMediaItems[this.currentMediaIndex] || null\n    },\n    effectiveMediaItems() {\n      // 优先使用父组件传入的数据，如果没有则使用从服务器获取的数据\n      return this.mediaItems.length > 0 ? this.mediaItems : this.localMediaItems\n    }\n  },\n  created() {\n    // 加载系统配置\n    this.sysConfig = this.$store.getters.sysConfig;\n    \n    // 如果没有传入mediaItems，则从服务器获取\n    if (this.mediaItems.length === 0) {\n      this.loadMediaItemsFromServer()\n    }\n  },\n  methods: {\n    loadMediaItemsFromServer() {\n      // 按照Banner组件的方式处理数据\n      if (this.sysConfig.photoWallMedia_urls) {\n        // 使用新格式\n        const urls = this.sysConfig.photoWallMedia_urls.split(',');\n        const thumbnails = this.sysConfig.photoWallMedia_thumbnails ? this.sysConfig.photoWallMedia_thumbnails.split(',') : [];\n        const types = this.sysConfig.photoWallMedia_types ? this.sysConfig.photoWallMedia_types.split(',') : [];\n        const titles = this.sysConfig.photoWallMedia_titles ? this.sysConfig.photoWallMedia_titles.split(',') : [];\n        \n        this.localMediaItems = [];\n        \n        for (let i = 0; i < urls.length; i++) {\n          let item = {\n            id: i + 1,\n            type: types[i] || 'image',\n            title: titles[i] || `媒体${i + 1}`,\n            url: getFileAccessHttpUrl(urls[i]),\n            thumbnail: thumbnails[i] ? getFileAccessHttpUrl(thumbnails[i]) : getFileAccessHttpUrl(urls[i])\n          };\n          this.localMediaItems.push(item);\n        }\n      } else if (this.sysConfig.photoWallMedia) {\n        // 使用旧格式\n        try {\n          let items = JSON.parse(this.sysConfig.photoWallMedia || '[]');\n          this.localMediaItems = items.map(item => ({\n            ...item,\n            url: getFileAccessHttpUrl(item.url),\n            thumbnail: getFileAccessHttpUrl(item.thumbnail || item.url)\n          }));\n        } catch (e) {\n          this.localMediaItems = [];\n        }\n      } else {\n        this.localMediaItems = [];\n      }\n    },\n    \n    openMediaViewer(index) {\n      this.currentMediaIndex = index\n      this.mediaViewerVisible = true\n    },\n    closeMediaViewer() {\n      this.mediaViewerVisible = false\n      // 如果是视频，停止播放\n      if (this.currentMedia && this.currentMedia.type === 'video') {\n        const videoElement = document.querySelector('.viewer-video')\n        if (videoElement) {\n          videoElement.pause()\n        }\n      }\n    },\n    navigateMedia(step) {\n      const newIndex = this.currentMediaIndex + step\n      \n      if (newIndex >= 0 && newIndex < this.effectiveMediaItems.length) {\n        this.currentMediaIndex = newIndex\n        \n        // 如果是视频，暂停当前视频\n        const videoElement = document.querySelector('.viewer-video')\n        if (videoElement) {\n          videoElement.pause()\n        }\n      }\n    },\n    getRandomRotation(index) {\n      // 使用索引来创建更自然的旋转效果，确保相邻照片的角度不同\n      const seeds = [-7, -4, -2, 0, 2, 4, 7];\n      const base = seeds[index % seeds.length];\n      // 再增加一点随机性，但保持在合理范围内\n      return base + Math.random() * 2 - 1;\n    },\n    // 处理图片URL的方法\n    getFileUrl(path) {\n      return getFileAccessHttpUrl(path);\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n/* 媒体墙容器样式 */\n.media-wall {\n  display: flex;\n  flex-direction: column;\n  height: 90vh;\n  width: 95%;\n  margin: 0 auto;\n  background-color: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n  top: -20px;\n}\n\n/* 媒体墙头部样式 */\n.media-wall-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 32px;\n  border-bottom: 1px solid #f0f0f0;\n\n  h2 {\n    margin: 0;\n    font-size: 24px;\n    color: #333;\n  }\n}\n\n/* 媒体墙内容区域样式 */\n.media-wall-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 32px;\n}\n\n/* 空数据容器样式 */\n.empty-media-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 300px;\n  \n  .ant-empty {\n    font-size: 16px;\n  }\n}\n\n/* 宝丽来照片网格布局 */\n.polaroid-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 25px;\n  margin: 0 auto;\n  max-width: 100%;\n  \n  .media-item {\n    width: calc(25% - 19px);  /* 每行4个，减去间距 */\n    margin-bottom: 25px;\n    flex-grow: 0;\n    flex-shrink: 0;\n  }\n}\n\n/* 响应式布局 - 大屏幕 */\n@media (max-width: 1200px) {\n  .polaroid-grid {\n    .media-item {\n      width: calc(33.333% - 17px);  /* 每行3个 */\n    }\n  }\n}\n\n/* 响应式布局 - 中小屏幕 */\n@media (max-width: 768px) {\n  .polaroid-grid {\n    .media-item {\n      width: calc(50% - 12.5px);  /* 每行2个 */\n    }\n  }\n}\n\n/* 媒体项目样式 */\n.media-item {\n  position: relative;\n  border-radius: 0;\n  overflow: hidden;\n  margin-bottom: 30px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  cursor: pointer;\n  \n  /* 悬停效果 */\n  &:hover {\n    transform: scale(1.05) rotate(0deg) !important;\n    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);\n    z-index: 10;\n  }\n}\n\n/* 宝丽来照片样式 */\n.polaroid {\n  background-color: #fff;\n  padding: 12px 12px 18px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #f0f0f0;\n  \n  /* 宝丽来照片包装器 */\n  .polaroid-wrapper {\n    position: relative;\n    overflow: hidden;\n    line-height: 0;\n  }\n  \n  /* 媒体缩略图样式 */\n  .media-thumbnail {\n    width: 100%;\n    height: auto;\n    object-fit: contain;\n    max-height: 240px;\n    display: block;\n  }\n  \n  /* 媒体标题样式 */\n  .media-caption {\n    padding: 14px 5px 5px;\n    background-color: #fff;\n    font-size: 14px;\n    text-align: center;\n    color: #555;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    font-family: \"Comic Sans MS\", cursive, sans-serif;\n    line-height: 1.4;\n  }\n  \n  /* 视频缩略图容器样式 */\n  .video-thumbnail-container {\n    position: relative;\n    \n    /* 视频播放图标 */\n    .video-play-icon {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      color: #fff;\n      font-size: 40px;\n      opacity: 0.8;\n      text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);\n      z-index: 2;\n      background-color: rgba(0, 0, 0, 0.3);\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    \n    /* 视频缩略图遮罩层 */\n    &:after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.1);\n      pointer-events: none;\n    }\n  }\n}\n\n/* 媒体查看器样式 */\n.media-viewer {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  \n  /* 媒体查看器内容区域 */\n  .media-viewer-content {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex: 1;\n    min-height: 500px;\n    background-color: #000;\n    border-radius: 4px;\n    overflow: hidden;\n    \n    /* 查看器图片和视频共同样式 */\n    .viewer-image, .viewer-video {\n      max-width: 100%;\n      max-height: 600px;\n    }\n    \n    /* 查看器视频特有样式 */\n    .viewer-video {\n      width: 100%;\n    }\n  }\n  \n  /* 媒体查看器底部 */\n  .media-viewer-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16px 0;\n    \n    /* 媒体信息 */\n    .media-info {\n      h3 {\n        margin: 0;\n        font-size: 16px;\n      }\n    }\n    \n    /* 媒体导航 */\n    .media-navigation {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      \n      /* 媒体计数 */\n      .media-count {\n        font-size: 14px;\n        color: #666;\n      }\n    }\n  }\n}\n\n/* 深度选择器修改模态框样式 */\n:deep(.media-viewer-modal) {\n  .ant-modal-content {\n    background-color: #f5f5f5;\n  }\n  \n  .ant-modal-body {\n    padding: 16px;\n  }\n}\n</style> \n"]}]}