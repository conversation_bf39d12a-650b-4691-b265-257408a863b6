{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    value: {\n      type: String,\n      required: false\n    },\n    disabled: {\n      type: Boolean,\n      required: false,\n      default: false\n    },\n    options: {\n      type: Array,\n      required: false,\n      default: function _default() {\n        return ['Y', 'N'];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      checkStatus: false\n    };\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler: function handler(val) {\n        if (!val) {\n          this.checkStatus = false;\n          this.$emit('change', this.options[1]);\n        } else {\n          if (this.options[0] == val) {\n            this.checkStatus = true;\n          } else {\n            this.checkStatus = false;\n          }\n        }\n      }\n    }\n  },\n  methods: {\n    handleChange: function handleChange(checked) {\n      var flag = checked === false ? this.options[1] : this.options[0];\n      this.$emit('change', flag);\n    }\n  },\n  model: {\n    prop: 'value',\n    event: 'change'\n  }\n};", {"version": 3, "names": ["name", "props", "value", "type", "String", "required", "disabled", "Boolean", "default", "options", "Array", "_default", "data", "checkStatus", "watch", "immediate", "handler", "val", "$emit", "methods", "handleChange", "checked", "flag", "model", "prop", "event"], "sources": ["src/components/jeecg/JSwitch.vue"], "sourcesContent": ["<template>\n  <a-switch v-model=\"checkStatus\" :disabled=\"disabled\" @change=\"handleChange\"/>\n</template>\n<script>\n\n  export default {\n    name: 'JSwitch',\n    props: {\n      value:{\n        type: String,\n        required: false\n      },\n      disabled:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type:Array,\n        required:false,\n        default:()=>['Y','N']\n      }\n    },\n    data () {\n      return {\n        checkStatus: false\n      }\n    },\n    watch: {\n      value:{\n        immediate: true,\n        handler(val){\n          if(!val){\n            this.checkStatus = false\n            this.$emit('change', this.options[1]);\n          }else{\n            if(this.options[0]==val){\n              this.checkStatus = true\n            }else{\n              this.checkStatus = false\n            }\n          }\n        }\n      }\n    },\n    methods: {\n      handleChange(checked){\n        let flag = checked===false?this.options[1]:this.options[0];\n        this.$emit('change', flag);\n      }\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    }\n  }\n</script>\n"], "mappings": "AAKA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,QAAA;MACAG,OAAA;IACA;IACAC,OAAA;MACAN,IAAA,EAAAO,KAAA;MACAL,QAAA;MACAG,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACAZ,KAAA;MACAa,SAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAA,GAAA;UACA,KAAAJ,WAAA;UACA,KAAAK,KAAA,gBAAAT,OAAA;QACA;UACA,SAAAA,OAAA,OAAAQ,GAAA;YACA,KAAAJ,WAAA;UACA;YACA,KAAAA,WAAA;UACA;QACA;MACA;IACA;EACA;EACAM,OAAA;IACAC,YAAA,WAAAA,aAAAC,OAAA;MACA,IAAAC,IAAA,GAAAD,OAAA,kBAAAZ,OAAA,WAAAA,OAAA;MACA,KAAAS,KAAA,WAAAI,IAAA;IACA;EACA;EACAC,KAAA;IACAC,IAAA;IACAC,KAAA;EACA;AACA", "ignoreList": []}]}