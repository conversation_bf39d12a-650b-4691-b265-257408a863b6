{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\RankList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\RankList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"RankList\",\n  // ['title', 'list']\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    list: {\n      type: Array,\n      default: null\n    },\n    height: {\n      type: Number,\n      default: null\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "title", "type", "String", "default", "list", "Array", "height", "Number"], "sources": ["src/components/chart/RankList.vue"], "sourcesContent": ["<template>\n  <div class=\"rank\">\n    <h4 class=\"title\">{{ title }}</h4>\n    <ul class=\"list\" :style=\"{height:height?`${height}px`:'auto',overflow:'auto'}\">\n      <li :key=\"index\" v-for=\"(item, index) in list\">\n        <span :class=\"index < 3 ? 'active' : null\">{{ index + 1 }}</span>\n        <span>{{ item.name }}</span>\n        <span>{{ item.total }}</span>\n      </li>\n    </ul>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"RankList\",\n    // ['title', 'list']\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      list: {\n        type: Array,\n        default: null\n      },\n      height: {\n        type: Number,\n        default: null\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n\n  .rank {\n    padding: 0 32px 32px 72px;\n\n    .list {\n      margin: 25px 0 0;\n      padding: 0;\n      list-style: none;\n\n      li {\n        margin-top: 16px;\n\n        span {\n          color: rgba(0, 0, 0, .65);\n          font-size: 14px;\n          line-height: 22px;\n\n          &:first-child {\n            background-color: #f5f5f5;\n            border-radius: 20px;\n            display: inline-block;\n            font-size: 12px;\n            font-weight: 600;\n            margin-right: 24px;\n            height: 20px;\n            line-height: 20px;\n            width: 20px;\n            text-align: center;\n          }\n          &.active {\n            background-color: #314659;\n            color: #fff;\n          }\n          &:last-child {\n            float: right;\n          }\n        }\n      }\n    }\n  }\n\n  .mobile .rank {\n    padding: 0 32px 32px 32px;\n  }\n\n</style>"], "mappings": "AAcA;EACAA,IAAA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}