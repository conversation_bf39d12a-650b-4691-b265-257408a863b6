{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JModal\\index.vue?vue&type=style&index=0&id=522bead7&lang=less", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JModal\\index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.j-modal-box {\n\n  &.fullscreen {\n    top: 0;\n    left: 0;\n    padding: 0;\n\n    // 兼容1.6.2版本的antdv\n    & .ant-modal {\n      top: 0;\n      padding: 0;\n      height: 100vh;\n    }\n\n    & .ant-modal-content {\n      height: 100vh;\n      border-radius: 0;\n\n      & .ant-modal-body {\n        /* title 和 footer 各占 55px */\n        height: calc(100% - 55px - 55px);\n        overflow: auto;\n      }\n    }\n\n    &.no-title, &.no-footer {\n      .ant-modal-body {\n        height: calc(100% - 55px);\n      }\n    }\n\n    &.no-title.no-footer {\n      .ant-modal-body {\n        height: 100%;\n      }\n    }\n  }\n\n  .j-modal-title-row {\n    .left {\n      width: calc(100% - 56px - 56px);\n    }\n\n    .right {\n      width: 56px;\n      position: inherit;\n\n      .ant-modal-close {\n        right: 56px;\n        color: rgba(0, 0, 0, 0.45);\n\n        &:hover {\n          color: rgba(0, 0, 0, 0.75);\n        }\n      }\n    }\n  }\n}\n\n@media (max-width: 767px) {\n  .j-modal-box.fullscreen {\n    margin: 0;\n    max-width: 100vw;\n  }\n}\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoOA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/jeecg/JModal", "sourcesContent": ["<template>\n  <a-modal\n    ref=\"modal\"\n    :class=\"getClass(modalClass)\"\n    :style=\"getStyle(modalStyle)\"\n    :visible=\"visible\"\n    v-bind=\"_attrs\"\n    v-on=\"$listeners\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n  >\n\n    <slot></slot>\n\n    <template v-if=\"!isNoTitle\" slot=\"title\">\n      <a-row class=\"j-modal-title-row\" type=\"flex\">\n        <a-col class=\"left\">\n          <slot name=\"title\">{{ title }}</slot>\n        </a-col>\n        <a-col v-if=\"switchFullscreen\" class=\"right\" @click=\"toggleFullscreen\">\n          <a-button class=\"ant-modal-close ant-modal-close-x\" ghost type=\"link\" :icon=\"fullscreenButtonIcon\"/>\n        </a-col>\n      </a-row>\n    </template>\n\n    <!-- 处理 scopedSlots -->\n    <template v-for=\"slotName of scopedSlotsKeys\" :slot=\"slotName\">\n      <slot :name=\"slotName\"></slot>\n    </template>\n\n    <!-- 处理 slots -->\n    <template v-for=\"slotName of slotsKeys\" v-slot:[slotName]>\n      <slot :name=\"slotName\"></slot>\n    </template>\n\n  </a-modal>\n</template>\n\n<script>\n\n  import { getClass, getStyle } from '@/utils/props-util'\n  import { triggerWindowResizeEvent } from '@/utils/util'\n\n  export default {\n    name: 'JModal',\n    props: {\n      title: String,\n      // 可使用 .sync 修饰符\n      visible: Boolean,\n      // 是否全屏弹窗，当全屏时无论如何都会禁止 body 滚动。可使用 .sync 修饰符\n      fullscreen: {\n        type: Boolean,\n        default: false\n      },\n      // 是否允许切换全屏（允许后右上角会出现一个按钮）\n      switchFullscreen: {\n        type: Boolean,\n        default: false\n      },\n      // 点击确定按钮的时候是否关闭弹窗\n      okClose: {\n        type: Boolean,\n        default: true\n      },\n    },\n    data() {\n      return {\n        // 内部使用的 slots ，不再处理\n        usedSlots: ['title'],\n        // 实际控制是否全屏的参数\n        innerFullscreen: this.fullscreen,\n      }\n    },\n    computed: {\n      // 一些未处理的参数或特殊处理的参数绑定到 a-modal 上\n      _attrs() {\n        let attrs = { ...this.$attrs }\n        // 如果全屏就将宽度设为 100%\n        if (this.innerFullscreen) {\n          attrs['width'] = '100%'\n        }\n        return attrs\n      },\n      modalClass() {\n        return {\n          'j-modal-box': true,\n          'fullscreen': this.innerFullscreen,\n          'no-title': this.isNoTitle,\n          'no-footer': this.isNoFooter,\n        }\n      },\n      modalStyle() {\n        let style = {}\n        // 如果全屏就将top设为 0\n        if (this.innerFullscreen) {\n          style['top'] = '0'\n        }\n        return style\n      },\n      isNoTitle() {\n        return !this.title && !this.allSlotsKeys.includes('title')\n      },\n      isNoFooter() {\n        return this._attrs['footer'] === null\n      },\n      slotsKeys() {\n        return Object.keys(this.$slots).filter(key => !this.usedSlots.includes(key))\n      },\n      scopedSlotsKeys() {\n        return Object.keys(this.$scopedSlots).filter(key => !this.usedSlots.includes(key))\n      },\n      allSlotsKeys() {\n        return Object.keys(this.$slots).concat(Object.keys(this.$scopedSlots))\n      },\n      // 切换全屏的按钮图标\n      fullscreenButtonIcon() {\n        return this.innerFullscreen ? 'fullscreen-exit' : 'fullscreen'\n      },\n    },\n    watch: {\n      visible() {\n        if (this.visible) {\n          this.innerFullscreen = this.fullscreen\n        }\n      },\n      innerFullscreen(val) {\n        this.$emit('update:fullscreen', val)\n      },\n    },\n    methods: {\n\n      getClass(clazz) {\n        return { ...getClass(this), ...clazz }\n      },\n      getStyle(style) {\n        return { ...getStyle(this), ...style }\n      },\n\n      close() {\n        this.$emit('update:visible', false)\n      },\n\n      handleOk() {\n        if (this.okClose) {\n          this.close()\n        }\n      },\n      handleCancel() {\n        this.close()\n      },\n\n      /** 切换全屏 */\n      toggleFullscreen() {\n        this.innerFullscreen = !this.innerFullscreen\n        triggerWindowResizeEvent()\n      },\n\n    }\n  }\n</script>\n\n<style lang=\"less\">\n  .j-modal-box {\n\n    &.fullscreen {\n      top: 0;\n      left: 0;\n      padding: 0;\n\n      // 兼容1.6.2版本的antdv\n      & .ant-modal {\n        top: 0;\n        padding: 0;\n        height: 100vh;\n      }\n\n      & .ant-modal-content {\n        height: 100vh;\n        border-radius: 0;\n\n        & .ant-modal-body {\n          /* title 和 footer 各占 55px */\n          height: calc(100% - 55px - 55px);\n          overflow: auto;\n        }\n      }\n\n      &.no-title, &.no-footer {\n        .ant-modal-body {\n          height: calc(100% - 55px);\n        }\n      }\n\n      &.no-title.no-footer {\n        .ant-modal-body {\n          height: 100%;\n        }\n      }\n    }\n\n    .j-modal-title-row {\n      .left {\n        width: calc(100% - 56px - 56px);\n      }\n\n      .right {\n        width: 56px;\n        position: inherit;\n\n        .ant-modal-close {\n          right: 56px;\n          color: rgba(0, 0, 0, 0.45);\n\n          &:hover {\n            color: rgba(0, 0, 0, 0.75);\n          }\n        }\n      }\n    }\n  }\n\n  @media (max-width: 767px) {\n    .j-modal-box.fullscreen {\n      margin: 0;\n      max-width: 100vw;\n    }\n  }\n</style>"]}]}