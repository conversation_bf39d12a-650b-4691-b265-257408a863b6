{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue?vue&type=template&id=9964c5c6", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSwitch.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-switch\", {\n    attrs: {\n      disabled: _vm.disabled\n    },\n    on: {\n      change: _vm.handleChange\n    },\n    model: {\n      value: _vm.checkStatus,\n      callback: function callback($$v) {\n        _vm.checkStatus = $$v;\n      },\n      expression: \"checkStatus\"\n    }\n  });\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "disabled", "on", "change", "handleChange", "model", "value", "checkStatus", "callback", "$$v", "expression", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/jeecg/JSwitch.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"a-switch\", {\n    attrs: { disabled: _vm.disabled },\n    on: { change: _vm.handleChange },\n    model: {\n      value: _vm.checkStatus,\n      callback: function ($$v) {\n        _vm.checkStatus = $$v\n      },\n      expression: \"checkStatus\",\n    },\n  })\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,UAAU,EAAE;IACpBE,KAAK,EAAE;MAAEC,QAAQ,EAAEJ,GAAG,CAACI;IAAS,CAAC;IACjCC,EAAE,EAAE;MAAEC,MAAM,EAAEN,GAAG,CAACO;IAAa,CAAC;IAChCC,KAAK,EAAE;MACLC,KAAK,EAAET,GAAG,CAACU,WAAW;MACtBC,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvBZ,GAAG,CAACU,WAAW,GAAGE,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe", "ignoreList": []}]}