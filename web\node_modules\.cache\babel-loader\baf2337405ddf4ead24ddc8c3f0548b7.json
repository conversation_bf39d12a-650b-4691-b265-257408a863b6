{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\_util\\Area.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\_util\\Area.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { pcaa } from 'area-data';\n\n/**\n * 省市区\n */\nvar Area = /*#__PURE__*/function () {\n  /**\n   * 构造器\n   * @param express\n   */\n  function Area() {\n    _classCallCheck(this, Area);\n    var arr = [];\n    var province = pcaa['86'];\n    Object.keys(province).map(function (key) {\n      arr.push({\n        id: key,\n        text: province[key],\n        pid: '86'\n      });\n      var city = pcaa[key];\n      Object.keys(city).map(function (key2) {\n        arr.push({\n          id: key2,\n          text: city[key2],\n          pid: key\n        });\n        var qu = pcaa[key2];\n        Object.keys(qu).map(function (key3) {\n          arr.push({\n            id: key3,\n            text: qu[key3],\n            pid: key2\n          });\n        });\n      });\n    });\n    this.all = arr;\n  }\n  return _createClass(Area, [{\n    key: \"pca\",\n    get: function get() {\n      return this.all;\n    }\n  }, {\n    key: \"getCode\",\n    value: function getCode(text) {\n      if (!text || text.length == 0) {\n        return '';\n      }\n      var _iterator = _createForOfIteratorHelper(this.all),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var item = _step.value;\n          if (item.text === text) {\n            return item.id;\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    }\n  }, {\n    key: \"getText\",\n    value: function getText(code) {\n      if (!code || code.length == 0) {\n        return '';\n      }\n      var arr = [];\n      this.getAreaBycode(code, arr);\n      return arr.join('/');\n    }\n  }, {\n    key: \"getRealCode\",\n    value: function getRealCode(code) {\n      var arr = [];\n      this.getPcode(code, arr);\n      return arr;\n    }\n  }, {\n    key: \"getPcode\",\n    value: function getPcode(id, arr) {\n      var _iterator2 = _createForOfIteratorHelper(this.all),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var item = _step2.value;\n          if (item.id === id) {\n            arr.unshift(id);\n            if (item.pid != '86') {\n              this.getPcode(item.pid, arr);\n            }\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n    }\n  }, {\n    key: \"getAreaBycode\",\n    value: function getAreaBycode(code, arr) {\n      //console.log(\"this.all.length\",this.all)\n      var _iterator3 = _createForOfIteratorHelper(this.all),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var item = _step3.value;\n          if (item.id === code) {\n            arr.unshift(item.text);\n            this.getAreaBycode(item.pid, arr);\n          }\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n    }\n  }]);\n}();\nexport { Area as default };", {"version": 3, "names": ["pcaa", "Area", "_classCallCheck", "arr", "province", "Object", "keys", "map", "key", "push", "id", "text", "pid", "city", "key2", "qu", "key3", "all", "_createClass", "get", "value", "getCode", "length", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "err", "e", "f", "getText", "code", "getAreaBycode", "join", "getRealCode", "getPcode", "_iterator2", "_step2", "unshift", "_iterator3", "_step3", "default"], "sources": ["E:/teachingproject/teaching/web/src/components/_util/Area.js"], "sourcesContent": ["import { pcaa } from 'area-data'\n\n/**\n * 省市区\n */\nexport default class Area {\n  /**\n   * 构造器\n   * @param express\n   */\n  constructor() {\n    let arr = []\n    const province = pcaa['86']\n    Object.keys(province).map(key=>{\n      arr.push({id:key, text:province[key], pid:'86'});\n      const city = pcaa[key];\n      Object.keys(city).map(key2=>{\n        arr.push({id:key2, text:city[key2], pid:key});\n        const qu = pcaa[key2];\n        Object.keys(qu).map(key3=>{\n          arr.push({id:key3, text:qu[key3], pid:key2});\n        })\n      })\n    })\n    this.all = arr;\n  }\n\n  get pca(){\n    return this.all;\n  }\n\n  getCode(text){\n    if(!text || text.length==0){\n      return ''\n    }\n    for(let item of this.all){\n      if(item.text === text){\n        return item.id;\n      }\n    }\n  }\n\n  getText(code){\n    if(!code || code.length==0){\n      return ''\n    }\n    let arr = []\n    this.getAreaBycode(code,arr);\n    return arr.join('/')\n  }\n\n  getRealCode(code){\n    let arr = []\n    this.getPcode(code, arr)\n    return arr;\n  }\n\n  getPcode(id, arr){\n    for(let item of this.all){\n      if(item.id === id){\n        arr.unshift(id)\n        if(item.pid != '86'){\n          this.getPcode(item.pid,arr)\n        }\n      }\n    }\n  }\n\n  getAreaBycode(code,arr){\n    //console.log(\"this.all.length\",this.all)\n    for(let item of this.all){\n      if(item.id === code){\n        arr.unshift(item.text);\n        this.getAreaBycode(item.pid,arr)\n      }\n    }\n  }\n\n}"], "mappings": ";;;;;;;;;AAAA,SAASA,IAAI,QAAQ,WAAW;;AAEhC;AACA;AACA;AAFA,IAGqBC,IAAI;EACvB;AACF;AACA;AACA;EACE,SAAAA,KAAA,EAAc;IAAAC,eAAA,OAAAD,IAAA;IACZ,IAAIE,GAAG,GAAG,EAAE;IACZ,IAAMC,QAAQ,GAAGJ,IAAI,CAAC,IAAI,CAAC;IAC3BK,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,GAAG,CAAC,UAAAC,GAAG,EAAE;MAC7BL,GAAG,CAACM,IAAI,CAAC;QAACC,EAAE,EAACF,GAAG;QAAEG,IAAI,EAACP,QAAQ,CAACI,GAAG,CAAC;QAAEI,GAAG,EAAC;MAAI,CAAC,CAAC;MAChD,IAAMC,IAAI,GAAGb,IAAI,CAACQ,GAAG,CAAC;MACtBH,MAAM,CAACC,IAAI,CAACO,IAAI,CAAC,CAACN,GAAG,CAAC,UAAAO,IAAI,EAAE;QAC1BX,GAAG,CAACM,IAAI,CAAC;UAACC,EAAE,EAACI,IAAI;UAAEH,IAAI,EAACE,IAAI,CAACC,IAAI,CAAC;UAAEF,GAAG,EAACJ;QAAG,CAAC,CAAC;QAC7C,IAAMO,EAAE,GAAGf,IAAI,CAACc,IAAI,CAAC;QACrBT,MAAM,CAACC,IAAI,CAACS,EAAE,CAAC,CAACR,GAAG,CAAC,UAAAS,IAAI,EAAE;UACxBb,GAAG,CAACM,IAAI,CAAC;YAACC,EAAE,EAACM,IAAI;YAAEL,IAAI,EAACI,EAAE,CAACC,IAAI,CAAC;YAAEJ,GAAG,EAACE;UAAI,CAAC,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACG,GAAG,GAAGd,GAAG;EAChB;EAAC,OAAAe,YAAA,CAAAjB,IAAA;IAAAO,GAAA;IAAAW,GAAA,EAED,SAAAA,IAAA,EAAS;MACP,OAAO,IAAI,CAACF,GAAG;IACjB;EAAC;IAAAT,GAAA;IAAAY,KAAA,EAED,SAAAC,QAAQV,IAAI,EAAC;MACX,IAAG,CAACA,IAAI,IAAIA,IAAI,CAACW,MAAM,IAAE,CAAC,EAAC;QACzB,OAAO,EAAE;MACX;MAAC,IAAAC,SAAA,GAAAC,0BAAA,CACe,IAAI,CAACP,GAAG;QAAAQ,KAAA;MAAA;QAAxB,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAjBC,IAAI,GAAAJ,KAAA,CAAAL,KAAA;UACV,IAAGS,IAAI,CAAClB,IAAI,KAAKA,IAAI,EAAC;YACpB,OAAOkB,IAAI,CAACnB,EAAE;UAChB;QACF;MAAC,SAAAoB,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;IACH;EAAC;IAAAxB,GAAA;IAAAY,KAAA,EAED,SAAAa,QAAQC,IAAI,EAAC;MACX,IAAG,CAACA,IAAI,IAAIA,IAAI,CAACZ,MAAM,IAAE,CAAC,EAAC;QACzB,OAAO,EAAE;MACX;MACA,IAAInB,GAAG,GAAG,EAAE;MACZ,IAAI,CAACgC,aAAa,CAACD,IAAI,EAAC/B,GAAG,CAAC;MAC5B,OAAOA,GAAG,CAACiC,IAAI,CAAC,GAAG,CAAC;IACtB;EAAC;IAAA5B,GAAA;IAAAY,KAAA,EAED,SAAAiB,YAAYH,IAAI,EAAC;MACf,IAAI/B,GAAG,GAAG,EAAE;MACZ,IAAI,CAACmC,QAAQ,CAACJ,IAAI,EAAE/B,GAAG,CAAC;MACxB,OAAOA,GAAG;IACZ;EAAC;IAAAK,GAAA;IAAAY,KAAA,EAED,SAAAkB,SAAS5B,EAAE,EAAEP,GAAG,EAAC;MAAA,IAAAoC,UAAA,GAAAf,0BAAA,CACC,IAAI,CAACP,GAAG;QAAAuB,MAAA;MAAA;QAAxB,KAAAD,UAAA,CAAAb,CAAA,MAAAc,MAAA,GAAAD,UAAA,CAAAZ,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAjBC,IAAI,GAAAW,MAAA,CAAApB,KAAA;UACV,IAAGS,IAAI,CAACnB,EAAE,KAAKA,EAAE,EAAC;YAChBP,GAAG,CAACsC,OAAO,CAAC/B,EAAE,CAAC;YACf,IAAGmB,IAAI,CAACjB,GAAG,IAAI,IAAI,EAAC;cAClB,IAAI,CAAC0B,QAAQ,CAACT,IAAI,CAACjB,GAAG,EAACT,GAAG,CAAC;YAC7B;UACF;QACF;MAAC,SAAA2B,GAAA;QAAAS,UAAA,CAAAR,CAAA,CAAAD,GAAA;MAAA;QAAAS,UAAA,CAAAP,CAAA;MAAA;IACH;EAAC;IAAAxB,GAAA;IAAAY,KAAA,EAED,SAAAe,cAAcD,IAAI,EAAC/B,GAAG,EAAC;MACrB;MAAA,IAAAuC,UAAA,GAAAlB,0BAAA,CACgB,IAAI,CAACP,GAAG;QAAA0B,MAAA;MAAA;QAAxB,KAAAD,UAAA,CAAAhB,CAAA,MAAAiB,MAAA,GAAAD,UAAA,CAAAf,CAAA,IAAAC,IAAA,GAAyB;UAAA,IAAjBC,IAAI,GAAAc,MAAA,CAAAvB,KAAA;UACV,IAAGS,IAAI,CAACnB,EAAE,KAAKwB,IAAI,EAAC;YAClB/B,GAAG,CAACsC,OAAO,CAACZ,IAAI,CAAClB,IAAI,CAAC;YACtB,IAAI,CAACwB,aAAa,CAACN,IAAI,CAACjB,GAAG,EAACT,GAAG,CAAC;UAClC;QACF;MAAC,SAAA2B,GAAA;QAAAY,UAAA,CAAAX,CAAA,CAAAD,GAAA;MAAA;QAAAY,UAAA,CAAAV,CAAA;MAAA;IACH;EAAC;AAAA;AAAA,SAvEkB/B,IAAI,IAAA2C,OAAA", "ignoreList": []}]}