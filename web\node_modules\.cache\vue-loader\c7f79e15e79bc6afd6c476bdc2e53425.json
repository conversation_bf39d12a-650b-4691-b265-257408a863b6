{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue?vue&type=template&id=dbb7a860&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  title=\"分屏\"\n  :width=\"modalWidth\"\n  :visible=\"visible\"\n  :bodyStyle=\"bodyStyle\"\n  style=\"top: 0px;\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n\n  <split-pane :min-percent='20' :default-percent='50' split=\"vertical\">\n    <template slot=\"paneL\">\n      <split-panel-a></split-panel-a>\n    </template>\n    <template slot=\"paneR\">\n      <split-panel-b></split-panel-b>\n    </template>\n  </split-pane>\n\n</a-modal>\n", null]}