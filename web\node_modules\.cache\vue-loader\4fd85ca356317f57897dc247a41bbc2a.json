{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Workplace.vue?vue&type=style&index=0&id=cd4631de&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Workplace.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.project-list {\n\n  .card-title {\n    font-size: 0;\n\n    a {\n      color: rgba(0, 0, 0, 0.85);\n      margin-left: 12px;\n      line-height: 24px;\n      height: 24px;\n      display: inline-block;\n      vertical-align: top;\n      font-size: 14px;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n  }\n  .card-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n  .project-item {\n    display: flex;\n    margin-top: 8px;\n    overflow: hidden;\n    font-size: 12px;\n    height: 20px;\n    line-height: 20px;\n    a {\n      color: rgba(0, 0, 0, 0.45);\n      display: inline-block;\n      flex: 1 1 0;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n    .datetime {\n      color: rgba(0, 0, 0, 0.25);\n      flex: 0 0 auto;\n      float: right;\n    }\n  }\n  .ant-card-meta-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n}\n\n.item-group {\n  padding: 20px 0 8px 24px;\n  font-size: 0;\n  a {\n    color: rgba(0, 0, 0, 0.65);\n    display: inline-block;\n    font-size: 14px;\n    margin-bottom: 13px;\n    width: 25%;\n  }\n}\n\n.members {\n  a {\n    display: block;\n    margin: 12px 0;\n    line-height: 24px;\n    height: 24px;\n    .member {\n      font-size: 14px;\n      color: rgba(0, 0, 0, .65);\n      line-height: 24px;\n      max-width: 100px;\n      vertical-align: top;\n      margin-left: 12px;\n      transition: all 0.3s;\n      display: inline-block;\n    }\n    &:hover {\n      span {\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n.mobile {\n\n  .project-list {\n\n    .project-card-grid {\n      width: 100%;\n    }\n  }\n\n  .more-info {\n    border: 0;\n    padding-top: 16px;\n    margin: 16px 0 16px;\n  }\n\n  .headerContent .title .welcome-text {\n    display: none;\n  }\n}\n\n", {"version": 3, "sources": ["Workplace.vue"], "names": [], "mappings": ";AAyWA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Workplace.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <page-layout :avatar=\"avatar\">\n    <div slot=\"headerContent\">\n      <div class=\"title\">{{ timeFix }}，{{ nickname() }}<span class=\"welcome-text\">，{{ welcome() }}</span></div>\n      <div>前端工程师 | 蚂蚁金服 - 某某某事业群 - VUE平台</div>\n    </div>\n    <div slot=\"extra\">\n      <a-row class=\"more-info\">\n        <a-col :span=\"8\">\n          <head-info title=\"项目数\" content=\"56\" :center=\"false\" :bordered=\"false\"/>\n        </a-col>\n        <a-col :span=\"8\">\n          <head-info title=\"团队排名\" content=\"8/24\" :center=\"false\" :bordered=\"false\"/>\n        </a-col>\n        <a-col :span=\"8\">\n          <head-info title=\"项目访问\" content=\"2,223\" :center=\"false\" />\n        </a-col>\n      </a-row>\n    </div>\n\n    <div>\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n          <a-card\n            class=\"project-list\"\n            :loading=\"loading\"\n            style=\"margin-bottom: 24px;\"\n            :bordered=\"false\"\n            title=\"进行中的项目\"\n            :body-style=\"{ padding: 0 }\">\n            <a slot=\"extra\">全部项目</a>\n            <div>\n              <a-card-grid class=\"project-card-grid\" :key=\"i\" v-for=\"(item, i) in projects\">\n                <a-card :bordered=\"false\" :body-style=\"{ padding: 0 }\">\n                  <a-card-meta>\n                    <div slot=\"title\" class=\"card-title\">\n                      <a-avatar size=\"small\" :src=\"item.cover\" icon=\"user\"/>\n                      <a>{{ item.title }}</a>\n                    </div>\n                    <div slot=\"description\" class=\"card-description\">\n                      {{ item.description }}\n                    </div>\n                  </a-card-meta>\n                  <div class=\"project-item\">\n                    <a href=\"/#/\">科学搬砖组</a>\n                    <span class=\"datetime\">9小时前</span>\n                  </div>\n                </a-card>\n              </a-card-grid>\n            </div>\n          </a-card>\n\n          <a-card :loading=\"loading\" title=\"动态\" :bordered=\"false\">\n            <a-list>\n              <a-list-item :key=\"index\" v-for=\"(item, index) in activities\">\n                <a-list-item-meta>\n                  <a-avatar slot=\"avatar\" :src=\"item.user.avatar\" />\n                  <div slot=\"title\">\n                    <span>{{ item.user.nickname }}</span>&nbsp;\n                    在&nbsp;<a href=\"#\">{{ item.project.name }}</a>&nbsp;\n                    <span>{{ item.project.action }}</span>&nbsp;\n                    <a href=\"#\">{{ item.project.event }}</a>\n                  </div>\n                  <div slot=\"description\">{{ item.time }}</div>\n                </a-list-item-meta>\n              </a-list-item>\n            </a-list>\n          </a-card>\n        </a-col>\n        <a-col\n          style=\"padding: 0 12px\"\n          :xl=\"8\"\n          :lg=\"24\"\n          :md=\"24\"\n          :sm=\"24\"\n          :xs=\"24\">\n          <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n            <div class=\"item-group\">\n              <a>操作一</a>\n              <a>操作二</a>\n              <a>操作三</a>\n              <a>操作四</a>\n              <a>操作五</a>\n              <a>操作六</a>\n              <a-button size=\"small\" type=\"primary\" ghost icon=\"plus\">添加</a-button>\n            </div>\n          </a-card>\n          <a-card title=\"XX 指数\" style=\"margin-bottom: 24px\" :loading=\"radarLoading\" :bordered=\"false\" :body-style=\"{ padding: 0 }\">\n            <div style=\"min-height: 400px;\">\n              <!-- :scale=\"scale\" :axis1Opts=\"axis1Opts\" :axis2Opts=\"axis2Opts\"  -->\n              <radar :data=\"radarData\" />\n            </div>\n          </a-card>\n          <a-card :loading=\"loading\" title=\"团队\" :bordered=\"false\">\n            <div class=\"members\">\n              <a-row>\n                <a-col :span=\"12\" v-for=\"(item, index) in teams\" :key=\"index\">\n                  <a>\n                    <a-avatar size=\"small\" :src=\"item.avatar\" />\n                    <span class=\"member\">{{ item.name }}</span>\n                  </a>\n                </a-col>\n              </a-row>\n            </div>\n          </a-card>\n        </a-col>\n      </a-row>\n    </div>\n  </page-layout>\n</template>\n\n<script>\n  import { timeFix } from \"@/utils/util\"\n  import {mapGetters} from \"vuex\"\n\n  import PageLayout from '@/components/page/PageLayout'\n  import HeadInfo from '@/components/tools/HeadInfo'\n  import Radar from '@/components/chart/Radar'\n  import { getRoleList, getServiceList, getFileAccessHttpUrl } from \"@/api/manage\"\n\n  const DataSet = require('@antv/data-set')\n\n  export default {\n    name: \"Workplace\",\n    components: {\n      PageLayout,\n      HeadInfo,\n      Radar\n    },\n    data() {\n      return {\n        timeFix: timeFix(),\n        avatar: '',\n        user: {},\n\n        projects: [],\n        loading: true,\n        radarLoading: true,\n        activities: [],\n        teams: [],\n\n        // data\n        axis1Opts: {\n          dataKey: 'item',\n          line: null,\n          tickLine: null,\n          grid: {\n            lineStyle: {\n              lineDash: null\n            },\n            hideFirstLine: false\n          }\n        },\n        axis2Opts: {\n          dataKey: 'score',\n          line: null,\n          tickLine: null,\n          grid: {\n            type: 'polygon',\n            lineStyle: {\n              lineDash: null\n            }\n          }\n        },\n        scale: [{\n          dataKey: 'score',\n          min: 0,\n          max: 80\n        }],\n        axisData: [\n          { item: '引用', a: 70, b: 30, c: 40 },\n          { item: '口碑', a: 60, b: 70, c: 40 },\n          { item: '产量', a: 50, b: 60, c: 40 },\n          { item: '贡献', a: 40, b: 50, c: 40 },\n          { item: '热度', a: 60, b: 70, c: 40 },\n          { item: '引用', a: 70, b: 50, c: 40 }\n        ],\n        radarData: []\n      }\n    },\n    computed: {\n      userInfo() {\n        return this.$store.getters.userInfo\n      }\n    },\n    created() {\n      this.user = this.userInfo\n      this.avatar = getFileAccessHttpUrl(this.userInfo.avatar)\n      console.log('this.avatar :'+ this.avatar)\n\n      getRoleList().then(res => {\n        console.log('workplace -> call getRoleList()', res)\n      })\n\n      getServiceList().then(res => {\n        console.log('workplace -> call getServiceList()', res)\n      })\n    },\n    mounted() {\n      this.getProjects()\n      this.getActivity()\n      this.getTeams()\n      this.initRadar()\n    },\n    methods: {\n      ...mapGetters([\"nickname\", \"welcome\"]),\n      getProjects() {\n        this.$http.get('/api/list/search/projects')\n          .then(res => {\n            this.projects = res.result && res.result.data\n            this.loading = false\n          })\n      },\n      getActivity() {\n        this.$http.get('/api/workplace/activity')\n          .then(res => {\n            this.activities = res.result\n          })\n      },\n      getTeams() {\n        this.$http.get('/api/workplace/teams')\n          .then(res => {\n            this.teams = res.result\n          })\n      },\n      initRadar() {\n        this.radarLoading = true\n\n        this.$http.get('/api/workplace/radar')\n          .then(res => {\n\n            const dv = new DataSet.View().source(res.result)\n            dv.transform({\n              type: 'fold',\n              fields: ['个人', '团队', '部门'],\n              key: 'user',\n              value: 'score'\n            })\n\n            this.radarData = dv.rows\n            this.radarLoading = false\n          })\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .project-list {\n\n    .card-title {\n      font-size: 0;\n\n      a {\n        color: rgba(0, 0, 0, 0.85);\n        margin-left: 12px;\n        line-height: 24px;\n        height: 24px;\n        display: inline-block;\n        vertical-align: top;\n        font-size: 14px;\n\n        &:hover {\n          color: #1890ff;\n        }\n      }\n    }\n    .card-description {\n      color: rgba(0, 0, 0, 0.45);\n      height: 44px;\n      line-height: 22px;\n      overflow: hidden;\n    }\n    .project-item {\n      display: flex;\n      margin-top: 8px;\n      overflow: hidden;\n      font-size: 12px;\n      height: 20px;\n      line-height: 20px;\n      a {\n        color: rgba(0, 0, 0, 0.45);\n        display: inline-block;\n        flex: 1 1 0;\n\n        &:hover {\n          color: #1890ff;\n        }\n      }\n      .datetime {\n        color: rgba(0, 0, 0, 0.25);\n        flex: 0 0 auto;\n        float: right;\n      }\n    }\n    .ant-card-meta-description {\n      color: rgba(0, 0, 0, 0.45);\n      height: 44px;\n      line-height: 22px;\n      overflow: hidden;\n    }\n  }\n\n  .item-group {\n    padding: 20px 0 8px 24px;\n    font-size: 0;\n    a {\n      color: rgba(0, 0, 0, 0.65);\n      display: inline-block;\n      font-size: 14px;\n      margin-bottom: 13px;\n      width: 25%;\n    }\n  }\n\n  .members {\n    a {\n      display: block;\n      margin: 12px 0;\n      line-height: 24px;\n      height: 24px;\n      .member {\n        font-size: 14px;\n        color: rgba(0, 0, 0, .65);\n        line-height: 24px;\n        max-width: 100px;\n        vertical-align: top;\n        margin-left: 12px;\n        transition: all 0.3s;\n        display: inline-block;\n      }\n      &:hover {\n        span {\n          color: #1890ff;\n        }\n      }\n    }\n  }\n\n  .mobile {\n\n    .project-list {\n\n      .project-card-grid {\n        width: 100%;\n      }\n    }\n\n    .more-info {\n      border: 0;\n      padding-top: 16px;\n      margin: 16px 0 16px;\n    }\n\n    .headerContent .title .welcome-text {\n      display: none;\n    }\n  }\n\n</style>\n"]}]}