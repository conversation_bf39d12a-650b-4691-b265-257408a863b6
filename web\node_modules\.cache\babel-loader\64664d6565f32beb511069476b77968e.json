{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\RowspanTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\RowspanTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"RowspanTable\",\n  components: {},\n  data: function data() {\n    var _this = this;\n    return {\n      description: '存放位置设置表管理页面',\n      levelNum: {},\n      gridNum: 0,\n      boxNum: 0,\n      cabinetNo: \"\",\n      // 表头\n      columns: [{\n        title: '分组一',\n        align: \"center\",\n        dataIndex: 'cabinetNo',\n        customRender: function customRender(value, row, index) {\n          var obj = {\n            children: value,\n            attrs: {}\n          };\n          if (index === 0) {\n            obj.attrs.rowSpan = _this.dataSource.length;\n          } else {\n            obj.attrs.rowSpan = 0;\n          }\n          return obj;\n        }\n      }, {\n        title: '分组二',\n        align: \"center\",\n        dataIndex: 'levelNo',\n        customRender: function customRender(value, row, index) {\n          var obj = {\n            children: value,\n            attrs: {}\n          };\n          //当前列跨行的条数\n          var a = parseInt(_this.levelNum);\n          var b = parseInt(_this.gridNum) * parseInt(_this.boxNum);\n          console.log(a);\n          for (var c = 0; c <= a; c++) {\n            if (index === c * b) {\n              console.log(1);\n              console.log(c * b);\n              obj.attrs.rowSpan = b;\n              break;\n            } else {\n              obj.attrs.rowSpan = 0;\n            }\n          }\n          return obj;\n        }\n      }, {\n        title: '分组三',\n        align: \"center\",\n        dataIndex: 'gridNo',\n        customRender: function customRender(value, row, index) {\n          var obj = {\n            children: value,\n            attrs: {}\n          };\n          var a = parseInt(_this.levelNum) * parseInt(_this.gridNum);\n          var b = parseInt(_this.boxNum);\n          for (var c = 0; c <= a; c++) {\n            if (index === c * b) {\n              obj.attrs.rowSpan = b;\n              break;\n            } else {\n              obj.attrs.rowSpan = 0;\n            }\n          }\n          return obj;\n        }\n      }, {\n        title: '字段一',\n        align: \"center\",\n        dataIndex: 'boxNo'\n      }, {\n        title: '字段二',\n        align: 'center',\n        dataIndex: 'storedNum'\n      }, {\n        title: '字段三',\n        align: \"center\",\n        dataIndex: 'maxNum'\n      }],\n      //数据集\n      dataSource: [{\n        \"id\": \"cb1dfd12cbeca3f8ba121439ee7e2411\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"1\",\n        \"gridNo\": \"1\",\n        \"boxNo\": \"1\",\n        \"storedNum\": 2,\n        \"maxNum\": 2,\n        \"unitNum\": 2,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"1\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }, {\n        \"id\": \"f903d50d02904b14175dccf2a7948777\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"1\",\n        \"gridNo\": \"1\",\n        \"boxNo\": \"2\",\n        \"storedNum\": 2,\n        \"maxNum\": 2,\n        \"unitNum\": 2,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"1\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }, {\n        \"id\": \"4f04c0ca4202535d678871b07e706cf6\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"1\",\n        \"gridNo\": \"2\",\n        \"boxNo\": \"1\",\n        \"storedNum\": 2,\n        \"maxNum\": 2,\n        \"unitNum\": 2,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"1\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }, {\n        \"id\": \"d0c91dabedfc03efad0126e50ea72e80\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"1\",\n        \"gridNo\": \"2\",\n        \"boxNo\": \"2\",\n        \"storedNum\": 2,\n        \"maxNum\": 2,\n        \"unitNum\": 2,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"1\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-08\"\n      }, {\n        \"id\": \"1e8bfcbe4352afbab8878f9fd368e007\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"2\",\n        \"gridNo\": \"1\",\n        \"boxNo\": \"1\",\n        \"storedNum\": 1,\n        \"maxNum\": 2,\n        \"unitNum\": 1,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"0\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-08\"\n      }, {\n        \"id\": \"d76087d8d3ebc7a59d43458588f26941\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"2\",\n        \"gridNo\": \"1\",\n        \"boxNo\": \"2\",\n        \"storedNum\": 0,\n        \"maxNum\": 2,\n        \"unitNum\": 0,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"0\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }, {\n        \"id\": \"7bf7754f12e1bf95edcd501cc6b85e62\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"2\",\n        \"gridNo\": \"2\",\n        \"boxNo\": \"1\",\n        \"storedNum\": 0,\n        \"maxNum\": 2,\n        \"unitNum\": 0,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"0\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }, {\n        \"id\": \"9cd08d733657d5b286bec870f12f6ecf\",\n        \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n        \"cabinetNo\": \"1\",\n        \"levelNo\": \"2\",\n        \"gridNo\": \"2\",\n        \"boxNo\": \"2\",\n        \"storedNum\": 0,\n        \"maxNum\": 2,\n        \"unitNum\": 0,\n        \"assignStatus\": \"1\",\n        \"storageStatus\": \"0\",\n        \"remark\": null,\n        \"createBy\": \"admin\",\n        \"createTime\": \"2019-04-02\",\n        \"updateBy\": \"admin\",\n        \"updateTime\": \"2019-04-02\"\n      }],\n      isorter: {\n        column: 'createTime',\n        order: 'desc'\n      },\n      url: {}\n    };\n  },\n  created: function created() {\n    this.loadData();\n  },\n  methods: {\n    loadData: function loadData() {\n      this.levelNum = 4;\n      this.gridNum = 2;\n      this.boxNum = 2;\n    }\n  }\n};", {"version": 3, "names": ["name", "components", "data", "_this", "description", "levelNum", "gridNum", "boxNum", "cabinetNo", "columns", "title", "align", "dataIndex", "customRender", "value", "row", "index", "obj", "children", "attrs", "rowSpan", "dataSource", "length", "a", "parseInt", "b", "console", "log", "c", "isorter", "column", "order", "url", "created", "loadData", "methods"], "sources": ["src/views/jeecg/RowspanTable.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- table区域-begin -->\n    <a-table\n      ref=\"table\"\n      size=\"default\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :pagination=\"false\"\n      :dataSource=\"dataSource\">\n\n    </a-table>\n    <!-- table区域-end -->\n\n\n  </a-card>\n</template>\n\n<script>\n\n  export default {\n    name: \"RowspanTable\",\n    components: {\n    },\n    data() {\n      return {\n        description: '存放位置设置表管理页面',\n        levelNum:{},\n        gridNum:0,\n        boxNum:0,\n        cabinetNo:\"\",\n        // 表头\n        columns: [ {\n          title: '分组一',\n          align: \"center\",\n          dataIndex: 'cabinetNo',\n          customRender: (value, row, index) => {\n            const obj = {\n              children: value,\n              attrs: {},\n            };\n            if(index===0){\n              obj.attrs.rowSpan = this.dataSource.length;\n            }else{\n              obj.attrs.rowSpan = 0;\n            }\n            return obj;\n          },\n        },\n          {\n            title: '分组二',\n            align: \"center\",\n            dataIndex: 'levelNo',\n            customRender: (value, row, index) => {\n              const obj = {\n                children: value,\n                attrs: {},\n              };\n              //当前列跨行的条数\n              var a = parseInt(this.levelNum);\n              var b = parseInt(this.gridNum)*parseInt(this.boxNum);\n              console.log(a);\n              for(var c=0;c<=a;c++){\n                if(index === (c*b)){\n                  console.log(1);\n                  console.log(c*b);\n                  obj.attrs.rowSpan = b;\n                  break;\n                }else{\n                  obj.attrs.rowSpan = 0;\n                }\n              }\n              return obj;\n            }\n          },\n          {\n            title: '分组三',\n            align: \"center\",\n            dataIndex: 'gridNo',\n            customRender: (value, row, index) => {\n              const obj = {\n                children: value,\n                attrs: {},\n              };\n              var a = parseInt(this.levelNum)*parseInt(this.gridNum);\n              var b = parseInt(this.boxNum);\n              for(var c=0;c<=a;c++){\n                if(index === (c*b)){\n                  obj.attrs.rowSpan = b;\n                  break;\n                }else{\n                  obj.attrs.rowSpan = 0;\n                }\n              }\n              return obj;\n            },\n          }, {\n            title: '字段一',\n            align: \"center\",\n            dataIndex: 'boxNo'\n          }, {\n            title: '字段二',\n            align: 'center',\n            dataIndex: 'storedNum'\n          }, {\n            title: '字段三',\n            align: \"center\",\n            dataIndex: 'maxNum'\n          },],\n        //数据集\n        dataSource: [{\n          \"id\": \"cb1dfd12cbeca3f8ba121439ee7e2411\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"1\",\n          \"gridNo\": \"1\",\n          \"boxNo\": \"1\",\n          \"storedNum\": 2,\n          \"maxNum\": 2,\n          \"unitNum\": 2,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"1\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }, {\n          \"id\": \"f903d50d02904b14175dccf2a7948777\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"1\",\n          \"gridNo\": \"1\",\n          \"boxNo\": \"2\",\n          \"storedNum\": 2,\n          \"maxNum\": 2,\n          \"unitNum\": 2,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"1\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }, {\n          \"id\": \"4f04c0ca4202535d678871b07e706cf6\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"1\",\n          \"gridNo\": \"2\",\n          \"boxNo\": \"1\",\n          \"storedNum\": 2,\n          \"maxNum\": 2,\n          \"unitNum\": 2,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"1\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }, {\n          \"id\": \"d0c91dabedfc03efad0126e50ea72e80\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"1\",\n          \"gridNo\": \"2\",\n          \"boxNo\": \"2\",\n          \"storedNum\": 2,\n          \"maxNum\": 2,\n          \"unitNum\": 2,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"1\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-08\"\n        }, {\n          \"id\": \"1e8bfcbe4352afbab8878f9fd368e007\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"2\",\n          \"gridNo\": \"1\",\n          \"boxNo\": \"1\",\n          \"storedNum\": 1,\n          \"maxNum\": 2,\n          \"unitNum\": 1,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"0\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-08\"\n        }, {\n          \"id\": \"d76087d8d3ebc7a59d43458588f26941\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"2\",\n          \"gridNo\": \"1\",\n          \"boxNo\": \"2\",\n          \"storedNum\": 0,\n          \"maxNum\": 2,\n          \"unitNum\": 0,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"0\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }, {\n          \"id\": \"7bf7754f12e1bf95edcd501cc6b85e62\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"2\",\n          \"gridNo\": \"2\",\n          \"boxNo\": \"1\",\n          \"storedNum\": 0,\n          \"maxNum\": 2,\n          \"unitNum\": 0,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"0\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }, {\n          \"id\": \"9cd08d733657d5b286bec870f12f6ecf\",\n          \"attributeId\": \"e62831f314e1390edbd884e9d9e6aca6\",\n          \"cabinetNo\": \"1\",\n          \"levelNo\": \"2\",\n          \"gridNo\": \"2\",\n          \"boxNo\": \"2\",\n          \"storedNum\": 0,\n          \"maxNum\": 2,\n          \"unitNum\": 0,\n          \"assignStatus\": \"1\",\n          \"storageStatus\": \"0\",\n          \"remark\": null,\n          \"createBy\": \"admin\",\n          \"createTime\": \"2019-04-02\",\n          \"updateBy\": \"admin\",\n          \"updateTime\": \"2019-04-02\"\n        }],\n        isorter: {\n          column: 'createTime',\n          order: 'desc',\n        },\n        url: {\n        },\n      }\n    },\n    created() {\n      this.loadData();\n    },\n    methods: {\n      loadData(){\n        this.levelNum=4;\n        this.gridNum = 2;\n        this.boxNum = 2;\n\n      }\n    }\n  }\n</script>\n<style scoped>\n  .ant-card-body .table-operator{\n    margin-bottom: 18px;\n  }\n  .ant-table-tbody .ant-table-row td{\n    padding-top:15px;\n    padding-bottom:15px;\n  }\n  .anty-row-operator button{margin: 0 5px}\n  .ant-btn-danger{background-color: #ffffff}\n\n  .ant-modal-cust-warp{height: 100%}\n  .ant-modal-cust-warp .ant-modal-body{height:calc(100% - 110px) !important;overflow-y: auto}\n  .ant-modal-cust-warp .ant-modal-content{height:90% !important;overflow-y: hidden}\n</style>\n\n"], "mappings": "AAqBA;EACAA,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,WAAA;MACAC,QAAA;MACAC,OAAA;MACAC,MAAA;MACAC,SAAA;MACA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,KAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,IAAAC,GAAA;YACAC,QAAA,EAAAJ,KAAA;YACAK,KAAA;UACA;UACA,IAAAH,KAAA;YACAC,GAAA,CAAAE,KAAA,CAAAC,OAAA,GAAAjB,KAAA,CAAAkB,UAAA,CAAAC,MAAA;UACA;YACAL,GAAA,CAAAE,KAAA,CAAAC,OAAA;UACA;UACA,OAAAH,GAAA;QACA;MACA,GACA;QACAP,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,KAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,IAAAC,GAAA;YACAC,QAAA,EAAAJ,KAAA;YACAK,KAAA;UACA;UACA;UACA,IAAAI,CAAA,GAAAC,QAAA,CAAArB,KAAA,CAAAE,QAAA;UACA,IAAAoB,CAAA,GAAAD,QAAA,CAAArB,KAAA,CAAAG,OAAA,IAAAkB,QAAA,CAAArB,KAAA,CAAAI,MAAA;UACAmB,OAAA,CAAAC,GAAA,CAAAJ,CAAA;UACA,SAAAK,CAAA,MAAAA,CAAA,IAAAL,CAAA,EAAAK,CAAA;YACA,IAAAZ,KAAA,KAAAY,CAAA,GAAAH,CAAA;cACAC,OAAA,CAAAC,GAAA;cACAD,OAAA,CAAAC,GAAA,CAAAC,CAAA,GAAAH,CAAA;cACAR,GAAA,CAAAE,KAAA,CAAAC,OAAA,GAAAK,CAAA;cACA;YACA;cACAR,GAAA,CAAAE,KAAA,CAAAC,OAAA;YACA;UACA;UACA,OAAAH,GAAA;QACA;MACA,GACA;QACAP,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,KAAA,EAAAC,GAAA,EAAAC,KAAA;UACA,IAAAC,GAAA;YACAC,QAAA,EAAAJ,KAAA;YACAK,KAAA;UACA;UACA,IAAAI,CAAA,GAAAC,QAAA,CAAArB,KAAA,CAAAE,QAAA,IAAAmB,QAAA,CAAArB,KAAA,CAAAG,OAAA;UACA,IAAAmB,CAAA,GAAAD,QAAA,CAAArB,KAAA,CAAAI,MAAA;UACA,SAAAqB,CAAA,MAAAA,CAAA,IAAAL,CAAA,EAAAK,CAAA;YACA,IAAAZ,KAAA,KAAAY,CAAA,GAAAH,CAAA;cACAR,GAAA,CAAAE,KAAA,CAAAC,OAAA,GAAAK,CAAA;cACA;YACA;cACAR,GAAA,CAAAE,KAAA,CAAAC,OAAA;YACA;UACA;UACA,OAAAH,GAAA;QACA;MACA;QACAP,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACA;MACAS,UAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAQ,OAAA;QACAC,MAAA;QACAC,KAAA;MACA;MACAC,GAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MACA,KAAA7B,QAAA;MACA,KAAAC,OAAA;MACA,KAAAC,MAAA;IAEA;EACA;AACA", "ignoreList": []}]}