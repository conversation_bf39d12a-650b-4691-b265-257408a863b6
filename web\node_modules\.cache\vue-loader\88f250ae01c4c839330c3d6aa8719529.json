{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import splitPane from 'vue-splitpane'\n  import SplitPanelA from './SplitPanelA'\n  import SplitPanelB from './SplitPanelB'\n  export default {\n    name: \"SplitPanelModal\",\n    components:{\n      splitPane,\n      SplitPanelA,\n      SplitPanelB\n    },\n    data () {\n      return {\n        visible: false,\n        bodyStyle:{\n          padding: \"0\",\n          height:(window.innerHeight-150)+\"px\"\n        },\n        modalWidth:800,\n      }\n    },\n    created () {\n      this.modalWidth = window.innerWidth-0;\n    },\n    methods: {\n      show () {\n        this.visible = true;\n      },\n      handleOk(){\n\n      },\n      handleCancel () {\n        this.visible = false;\n      },\n    }\n  }\n", {"version": 3, "sources": ["SplitPanelModal.vue"], "names": [], "mappings": ";AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "SplitPanelModal.vue", "sourceRoot": "src/views/jeecg/modules", "sourcesContent": ["<template>\n  <a-modal\n    title=\"分屏\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    :bodyStyle=\"bodyStyle\"\n    style=\"top: 0px;\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <split-pane :min-percent='20' :default-percent='50' split=\"vertical\">\n      <template slot=\"paneL\">\n        <split-panel-a></split-panel-a>\n      </template>\n      <template slot=\"paneR\">\n        <split-panel-b></split-panel-b>\n      </template>\n    </split-pane>\n\n  </a-modal>\n</template>\n\n<script>\n  import splitPane from 'vue-splitpane'\n  import SplitPanelA from './SplitPanelA'\n  import SplitPanelB from './SplitPanelB'\n  export default {\n    name: \"SplitPanelModal\",\n    components:{\n      splitPane,\n      SplitPanelA,\n      SplitPanelB\n    },\n    data () {\n      return {\n        visible: false,\n        bodyStyle:{\n          padding: \"0\",\n          height:(window.innerHeight-150)+\"px\"\n        },\n        modalWidth:800,\n      }\n    },\n    created () {\n      this.modalWidth = window.innerWidth-0;\n    },\n    methods: {\n      show () {\n        this.visible = true;\n      },\n      handleOk(){\n\n      },\n      handleCancel () {\n        this.visible = false;\n      },\n    }\n  }\n</script>\n\n<style scoped>\n</style>"]}]}