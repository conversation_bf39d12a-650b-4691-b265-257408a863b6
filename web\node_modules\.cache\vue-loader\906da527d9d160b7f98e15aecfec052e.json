{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\BaseSetting.vue?vue&type=style&index=0&id=f6709922&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\BaseSetting.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n\n.avatar-upload-wrapper {\n  height: 200px;\n  width: 100%;\n}\n\n.ant-upload-preview {\n  position: relative;\n  margin: 0 auto;\n  width: 100%;\n  max-width: 180px;\n  border-radius: 50%;\n  box-shadow: 0 0 4px #ccc;\n\n  .upload-icon {\n    position: absolute;\n    top: 0;\n    right: 10px;\n    font-size: 1.4rem;\n    padding: 0.5rem;\n    background: rgba(222, 221, 221, 0.7);\n    border-radius: 50%;\n    border: 1px solid rgba(0, 0, 0, 0.2);\n  }\n  .mask {\n    opacity: 0;\n    position: absolute;\n    background: rgba(0, 0, 0, 0.4);\n    cursor: pointer;\n    transition: opacity 0.4s;\n\n    &:hover {\n      opacity: 1;\n    }\n\n    i {\n      font-size: 2rem;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      margin-left: -1rem;\n      margin-top: -1rem;\n      color: #d6d6d6;\n    }\n  }\n\n  img,\n  .mask {\n    width: 100%;\n    max-width: 180px;\n    height: 100%;\n    border-radius: 50%;\n    overflow: hidden;\n  }\n}\n", {"version": 3, "sources": ["BaseSetting.vue"], "names": [], "mappings": ";;AAuRA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BaseSetting.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <div class=\"account-settings-info-view\">\n    <a-row :gutter=\"16\">\n      <a-col :md=\"24\" :lg=\"16\">\n        <a-form layout=\"vertical\" :form=\"form\">\n          <a-form-item label=\"真实姓名\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-input placeholder=\"请输入真实姓名\" v-decorator=\"[ 'realname', validatorRules.realname]\" />\n          </a-form-item>\n          <a-form-item label=\"头像\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <j-upload\n              v-decorator=\"['avatar']\"\n              :fileType=\"'image'\"\n              :number=\"1\"\n              :trigger-change=\"true\"\n            ></j-upload>\n          </a-form-item>\n\n          <a-form-item label=\"金币数\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-input\n              :value=\"userCoins\"\n              disabled\n              placeholder=\"金币数量\"\n              style=\"color: #1890ff; font-weight: bold;\"\n            />\n          </a-form-item>\n\n          <a-form-item label=\"生日\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-date-picker\n              style=\"width: 100%\"\n              placeholder=\"请选择生日\"\n              v-decorator=\"['birthday', {initialValue:!userInfo.birthday?null:moment(userInfo.birthday,dateFormat)}]\"\n            />\n          </a-form-item>\n\n          <a-form-item label=\"性别\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-select v-decorator=\"[ 'sex', {}]\" placeholder=\"请选择性别\">\n              <a-select-option :value=\"1\">男</a-select-option>\n              <a-select-option :value=\"2\">女</a-select-option>\n            </a-select>\n          </a-form-item>\n\n          <a-form-item label=\"邮箱\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-input placeholder=\"请输入邮箱\" v-decorator=\"[ 'email', validatorRules.email]\" />\n          </a-form-item>\n\n          <a-form-item label=\"手机号码\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n            <a-input placeholder=\"请输入手机号码\" v-decorator=\"[ 'phone', validatorRules.phone]\" />\n          </a-form-item>\n          <a-form-item>\n            <a-button type=\"primary\" @click=\"handleSubmit\">提交</a-button>\n          </a-form-item>\n        </a-form>\n      </a-col>\n    </a-row>\n\n    <avatar-modal ref=\"modal\"></avatar-modal>\n  </div>\n</template>\n\n<script>\nimport Vue from 'vue'\nimport AvatarModal from './AvatarModal'\nimport JUpload from '@/components/jeecg/JUpload'\nimport moment from 'moment'\nimport pick from 'lodash.pick'\nimport { getAction,putAction } from '@/api/manage'\nimport { mapMutations } from 'vuex'\nimport { USER_INFO, USER_ROLE } from '@/store/mutation-types'\n\nexport default {\n  components: {\n    AvatarModal,\n    JUpload\n  },\n  data() {\n    return {\n      preview: {},\n      dateFormat:\"YYYY-MM-DD\",\n      labelCol: {\n        xs: { span: 24 },\n        sm: { span: 5 }\n      },\n      wrapperCol: {\n        xs: { span: 24 },\n        sm: { span: 16 }\n      },\n      uploadLoading: false,\n      confirmLoading: false,\n      headers:{},\n      form:this.$form.createForm(this),\n      validatorRules: {\n        realname: { rules: [{ required: true, message: '请输入用户名称!' }] },\n        phone: { rules: [{ validator: this.validatePhone }] },\n        email: {\n          rules: [\n            {\n              validator: this.validateEmail\n            }\n          ]\n        }\n      },\n      userInfo: {},\n      userCoins: 0, // 用户金币数\n      url: {\n        fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',\n        userInfo: \"/teaching/user/info\",\n        userHonor: \"/teaching/teachingUserHonor/userHonor\",\n        editUser: \"/teaching/user/edit\",\n        imgerver: window._CONFIG['domianURL'] + '/sys/common/static'\n      }\n    }\n  },\n  created() {\n    this.getUserInfo()\n    this.loadUserCoins()\n  },\n  methods: {\n    moment,\n    ...mapMutations(['SET_INFO', 'SET_AVATAR', 'SET_NAME']),\n    getUserInfo() {\n      var that = this\n      getAction(this.url.userInfo).then(res => {\n        if (res.success) {\n          that.userInfo = res.result\n          that.$nextTick(() => {\n            that.form.setFieldsValue(pick(that.userInfo, 'username', 'sex', 'realname', 'email', 'phone', 'avatar'))\n          })\n          //that.loadUserHonor(that.userInfo.id)\n        }\n      })\n    },\n    loadUserHonor(userId) {\n      getAction(this.url.userHonor, { userId: userId }).then(res => {\n        console.log(res)\n        if (res.success) {\n          this.selectedHonor = res.result\n        } else {\n          console.log(res.message)\n        }\n      })\n    },\n    // 加载用户金币数量\n    loadUserCoins() {\n      getAction('/teaching/coin/getUserCoin')\n        .then(res => {\n          if (res.success) {\n            this.userCoins = res.result || 0\n          } else {\n            console.log('获取金币数失败:', res.message)\n            this.userCoins = 0\n          }\n        })\n        .catch(err => {\n          console.error('获取金币数出错:', err)\n          this.userCoins = 0\n        })\n    },\n    handleSubmit() {\n      const that = this\n      // that.form.validateFields((err, values) => {\n      //   console.log(err) \n      //   if (!err) {\n          var values = that.form.getFieldsValue();\n          console.log(values)\n          that.confirmLoading = true\n          // let avatar = that.userInfo.avatar\n          if (!values.birthday) {\n            values.birthday = ''\n          } else {\n            values.birthday = values.birthday.format(this.dateFormat)\n          }\n          let formData = Object.assign(this.userInfo, values)\n          // formData.avatar = avatar\n          // that.addDepartsToUser(that,formData); // 调用根据当前用户添加部门信息的方法\n          let obj = putAction(that.url.editUser, formData).then(res => {\n              if (res.success) {\n                that.$message.success(res.message)\n                that.$emit('ok')\n                \n                // 更新Vuex中的用户信息\n                const updatedUserInfo = Object.assign({}, that.userInfo, values)\n                \n                // 更新Vuex中的信息\n                that.SET_INFO(updatedUserInfo)\n                \n                // 如果修改了头像，更新头像\n                if (values.avatar) {\n                  that.SET_AVATAR(values.avatar)\n                }\n                \n                // 如果修改了真实姓名，更新名称\n                if (values.realname) {\n                  that.SET_NAME({\n                    username: updatedUserInfo.username,\n                    realname: values.realname,\n                    welcome: updatedUserInfo.welcome\n                  })\n                }\n                \n                // 更新本地存储中的用户信息，这样其他页面刷新后也能获取到最新信息\n                Vue.ls.set(USER_INFO, updatedUserInfo, 7 * 24 * 60 * 60 * 1000)\n                \n                // 同时更新用户角色信息，确保角色显示正确\n                const userRoles = Vue.ls.get(USER_ROLE) || []\n                Vue.ls.set(USER_ROLE, userRoles, 7 * 24 * 60 * 60 * 1000)\n              } else {\n                that.$message.warning(res.message)\n              }\n            })\n            .finally(() => {\n              that.confirmLoading = false\n            })\n      //   }\n      // })\n    },\n    validatePhone(rule, value, callback) {\n      if (!value) {\n        callback()\n      } else {\n        if (new RegExp(/^1[3|4|5|7|8][0-9]\\d{8}$/).test(value)) {\n          var params = {\n            tableName: 'sys_user',\n            fieldName: 'phone',\n            fieldVal: value,\n            dataId: this.userId\n          }\n          duplicateCheck(params).then(res => {\n            if (res.success) {\n              callback()\n            } else {\n              callback('手机号已存在!')\n            }\n          })\n        } else {\n          callback('请输入正确格式的手机号码!')\n        }\n      }\n    },\n    validateEmail(rule, value, callback) {\n      if (!value) {\n        callback()\n      } else {\n        if (\n          new RegExp(\n            /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\n          ).test(value)\n        ) {\n          var params = {\n            tableName: 'sys_user',\n            fieldName: 'email',\n            fieldVal: value,\n            dataId: this.userId\n          }\n          duplicateCheck(params).then(res => {\n            console.log(res)\n            if (res.success) {\n              callback()\n            } else {\n              callback('邮箱已存在!')\n            }\n          })\n        } else {\n          callback('请输入正确格式的邮箱!')\n        }\n      }\n    },\n    normFile(e) {\n      console.log('Upload event:', e)\n      if (Array.isArray(e)) {\n        return e\n      }\n      return e && e.fileList\n    },\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n\n.avatar-upload-wrapper {\n  height: 200px;\n  width: 100%;\n}\n\n.ant-upload-preview {\n  position: relative;\n  margin: 0 auto;\n  width: 100%;\n  max-width: 180px;\n  border-radius: 50%;\n  box-shadow: 0 0 4px #ccc;\n\n  .upload-icon {\n    position: absolute;\n    top: 0;\n    right: 10px;\n    font-size: 1.4rem;\n    padding: 0.5rem;\n    background: rgba(222, 221, 221, 0.7);\n    border-radius: 50%;\n    border: 1px solid rgba(0, 0, 0, 0.2);\n  }\n  .mask {\n    opacity: 0;\n    position: absolute;\n    background: rgba(0, 0, 0, 0.4);\n    cursor: pointer;\n    transition: opacity 0.4s;\n\n    &:hover {\n      opacity: 1;\n    }\n\n    i {\n      font-size: 2rem;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      margin-left: -1rem;\n      margin-top: -1rem;\n      color: #d6d6d6;\n    }\n  }\n\n  img,\n  .mask {\n    width: 100%;\n    max-width: 180px;\n    height: 100%;\n    border-radius: 50%;\n    overflow: hidden;\n  }\n}\n</style>"]}]}