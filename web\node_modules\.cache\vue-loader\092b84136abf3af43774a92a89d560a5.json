{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\ShoppingModal.vue", "mtime": 1751099917648}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport { getAction, postAction } from '@/api/manage'\nimport moment from 'moment'\nimport { getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'ShoppingModal',\n  data() {\n    return {\n      visible: false,\n      userAvatar: '/logo.png',\n      userName: '账号名',\n      userScore: 0,\n      exchangeRecords: [],\n      loadingRecords: false,\n      // 修改为空数组，将从后端加载数据\n      giftRow1: [],\n      giftRow2: [],\n      giftRow3: [],\n      giftRow4: [],\n      giftRow5: [],\n      // 添加礼物数据加载状态\n      loadingGifts: false,\n      recordsModalVisible: false,\n      loadingAllRecords: false,\n      allExchangeRecords: [],\n      recordColumns: [\n        {\n          title: '礼物名称',\n          dataIndex: 'giftName',\n          key: 'giftName',\n          scopedSlots: { customRender: 'giftName' }\n        },\n        {\n          title: '消费金币',\n          dataIndex: 'coinCount',\n          key: 'coinCount'\n        },\n        {\n          title: '兑换时间',\n          dataIndex: 'exchangeTime',\n          key: 'exchangeTime',\n          scopedSlots: { customRender: 'exchangeTime' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '领取时间',\n          dataIndex: 'receiveTime',\n          key: 'receiveTime',\n          scopedSlots: { customRender: 'receiveTime' }\n        }\n      ],\n      pagination: {\n        pageSize: 20, // 默认每页显示20条\n        current: 1,\n        // 添加更多分页配置\n        pageSizeOptions: ['10', '20', '30', '50'],\n        showSizeChanger: true,\n        showQuickJumper: true,\n        // 修改显示格式，与图片中一致\n        showTotal: (total, range) => `${range[0]}-${range[1]} 共${total}条`\n      },\n      // 添加一个标志来跟踪是否是首次加载\n      isFirstLoad: true\n    }\n  },\n  mounted() {\n    // 获取用户信息\n    if (this.$store.state.user.info) {\n      this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n      \n      // 使用getFileAccessHttpUrl处理头像路径\n      const avatar = this.$store.state.user.avatar || '/logo.png';\n      console.log('原始头像路径:', avatar);\n      \n      // 尝试从用户信息中获取头像路径\n      if (avatar) {\n        this.userAvatar = getFileAccessHttpUrl(avatar);\n        console.log('处理后头像路径:', this.userAvatar);\n      } else if (this.$store.getters.sysConfig.avatar) {\n        // 如果用户没有头像，使用默认头像\n        const defaultAvatar = this.$store.getters.sysConfig.avatar;\n        this.userAvatar = getFileAccessHttpUrl(defaultAvatar);\n        console.log('使用默认头像:', this.userAvatar);\n      } else {\n        // 使用静态默认头像\n        this.userAvatar = getFileAccessHttpUrl('/logo.png');\n        console.log('使用静态默认头像:', this.userAvatar);\n      }\n    }\n  },\n  methods: {\n    showModal() {\n      this.visible = true\n      this.loadUserCoin()\n      this.loadExchangeRecords()\n      this.loadGiftData() // 加载礼物数据\n    },\n    handleCancel() {\n      this.visible = false\n    },\n    // 加载用户金币数\n    loadUserCoin() {\n      getAction('/teaching/coin/getUserCoin').then(res => {\n        if (res.success) {\n          this.userScore = res.result || 0\n        }\n      })\n    },\n    // 加载兑换记录\n    loadExchangeRecords() {\n      this.loadingRecords = true\n      getAction('/teaching/coin/getExchangeRecords', { pageNo: 1, pageSize: 5 }).then(res => {\n        this.loadingRecords = false\n        if (res.success && res.result && res.result.records) {\n          this.exchangeRecords = res.result.records\n        }\n      }).catch(() => {\n        this.loadingRecords = false\n      })\n    },\n    // 新增：加载礼物数据\n    loadGiftData() {\n      console.log('加载礼物数据')\n      this.loadingGifts = true;\n      getAction('/teaching/gift/getShoppingGifts').then(res => {\n        if (res.success && res.result && res.result.length > 0) {\n          console.log('加载礼物数据成功')\n          // 清空原有数据\n          this.giftRow1 = [];\n          this.giftRow2 = [];\n          this.giftRow3 = [];\n          this.giftRow4 = [];\n          this.giftRow5 = [];\n          \n          // 按行号分组\n          res.result.forEach(gift => {\n            // 构建礼物对象\n            const giftItem = {\n              id: gift.id,\n              name: gift.giftName,\n              imgUrl: getFileAccessHttpUrl(gift.giftImg),\n              score: gift.coinCount,\n              type: gift.giftType,\n              desc: gift.giftDesc\n            };\n            \n            // 根据行号分配到对应数组\n            switch(gift.giftRow) {\n              case 1:\n                this.giftRow1.push(giftItem);\n                break;\n              case 2:\n                this.giftRow2.push(giftItem);\n                break;\n              case 3:\n                this.giftRow3.push(giftItem);\n                break;\n              case 4:\n                this.giftRow4.push(giftItem);\n                break;\n              case 5:\n                this.giftRow5.push(giftItem);\n                break;\n              default:\n                this.giftRow1.push(giftItem);\n            }\n          });\n        } else {\n          // 如果加载失败，使用默认数据\n          console.log('加载礼物数据失败')\n          this.useDefaultGiftData();\n        }\n        this.loadingGifts = false;\n      }).catch(() => {\n        // 发生错误时使用默认数据\n        console.log('加载礼物数据失败')\n        this.useDefaultGiftData();\n        this.loadingGifts = false;\n      });\n    },\n    // 使用默认礼物数据（以防后端数据加载失败）\n    useDefaultGiftData() {\n      this.giftRow1 = [\n        {\n          id: 1,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        },\n        {\n          id: 2,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        },\n        {\n          id: 3,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        }\n      ];\n      \n      this.giftRow2 = [\n        {\n          id: 4,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        },\n        {\n          id: 5,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        },\n        {\n          id: 6,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        }\n      ];\n      \n      this.giftRow3 = [\n        {\n          id: 7,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        },\n        {\n          id: 8,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        },\n        {\n          id: 9,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        }\n      ];\n      \n      this.giftRow4 = [\n        {\n          id: 10,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        },\n        {\n          id: 11,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        },\n        {\n          id: 12,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        }\n      ];\n      \n      this.giftRow5 = [\n        {\n          id: 13,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        },\n        {\n          id: 14,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        },\n        {\n          id: 15,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        }\n      ];\n    },\n    exchangeGift(gift) {\n      // 检查金币是否足够\n      if (this.userScore < Number(gift.score)) {\n        this.$message.error('金币不足，无法兑换该礼物')\n        return\n      }\n      \n      // 调用兑换API\n      const params = {\n        giftId: gift.id.toString(),\n        giftName: gift.name,\n        coinCount: Number(gift.score)\n      }\n      \n      postAction('/teaching/coin/exchangeGift', params).then(res => {\n        if (res.success) {\n          this.$message.success(res.message || '兑换成功')\n          // 更新金币数和兑换记录\n          this.loadUserCoin()\n          this.loadExchangeRecords()\n        } else {\n          this.$message.error(res.message || '兑换失败')\n        }\n      })\n    },\n    showRules() {\n      const h = this.$createElement;\n      this.$info({\n        title: '获取金币方式',\n        content: h => {\n          return h('div', {\n            style: {\n              padding: '10px 0'\n            }\n          }, [\n            h('div', { style: { marginBottom: '10px', fontSize: '14px' }}, [\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '作品被评为首页展示：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '作品被评为精选：+10金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成客观题作业：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成编程题作业：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成每日任务：+9金币')\n              ])\n            ])\n          ])\n        },\n        width: 350,\n        okText: '我知道了',\n        centered: true,\n        maskClosable: true\n      })\n    },\n    formatDate(date) {\n      if (!date) return ''\n      return moment(date).format('YYYY-MM-DD HH:mm')\n    },\n    showAllRecords() {\n      // 打开模态框前重置分页状态\n      this.pagination = {\n        ...this.pagination,\n        current: 1,\n        pageSize: 10 // 默认设置为每页20条\n      };\n      this.recordsModalVisible = true;\n      \n      // 如果是首次加载，设置特殊参数\n      if (this.isFirstLoad) {\n        this.isFirstLoad = false;\n      }\n      \n      this.loadAllExchangeRecords();\n    },\n    closeRecordsModal() {\n      this.recordsModalVisible = false;\n      // 清空数据，避免下次打开时显示旧数据\n      this.allExchangeRecords = [];\n    },\n    loadAllExchangeRecords() {\n      this.loadingAllRecords = true;\n      // 使用现有的getExchangeRecords接口，加载所有记录\n      getAction('/teaching/coin/getExchangeRecords', { \n        pageNo: this.pagination.current, \n        pageSize: this.pagination.pageSize,\n        // 添加一个标志，表示获取所有记录，而不是仅限于前5条\n        showAll: true,\n        // 添加时间戳参数，防止缓存\n        _t: new Date().getTime()\n      }).then(res => {\n        this.loadingAllRecords = false;\n        if (res.success && res.result) {\n          this.allExchangeRecords = res.result.records || [];\n          // 确保设置正确的总记录数，但保留当前的分页配置\n          this.pagination = {\n            ...this.pagination,\n            total: res.result.total || 0\n          };\n          \n          // 打印分页信息，用于调试\n          console.log('分页信息:', {\n            current: this.pagination.current,\n            pageSize: this.pagination.pageSize,\n            total: this.pagination.total\n          });\n        }\n      }).catch(() => {\n        this.loadingAllRecords = false;\n      });\n    },\n    handleTableChange(pagination, filters, sorter) {\n      console.log('分页变化:', pagination);\n      // 更新分页信息\n      this.pagination.current = pagination.current;\n      this.pagination.pageSize = pagination.pageSize;\n      // 重新加载数据\n      this.loadAllExchangeRecords();\n    }\n  }\n}\n", {"version": 3, "sources": ["ShoppingModal.vue"], "names": [], "mappings": ";AAu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file": "ShoppingModal.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\n  <div class=\"shopping-modal\">\n    <a-modal\n      :visible=\"visible\"\n      :footer=\"null\"\n      @cancel=\"handleCancel\"\n      width=\"1200px\"\n      :maskClosable=\"true\"\n      :bodyStyle=\"{ padding: '0' }\"\n      class=\"kids-modal\"\n    >\n      <!-- 自定义标题 -->\n      <template #title>\n        <div class=\"modal-title\">\n          <img :src=\"require('@/assets/shopping_img/gift-icon.png')\" alt=\"礼物图标\" class=\"title-small-icon\" />\n          <span>奖品兑换中心</span>\n        </div>\n      </template>\n\n      <div class=\"shopping-container\">\n        <!-- 左侧礼物架区域 -->\n        <div class=\"gift-shelf\">\n          <div class=\"shelf-header\">\n            <!-- <div class=\"shelf-title\">\n              <img :src=\"require('@/assets/shopping_img/gift-icon.png')\" alt=\"礼物图标\" class=\"title-icon\" />\n              <span>奇妙礼物坊</span>\n            </div> -->\n            <div class=\"shelf-subtitle\">用你的金币兑换喜欢的礼物吧！</div>\n          </div>\n          \n          <div class=\"shelf-content\">\n            <!-- 第1行礼物 -->\n            <div class=\"gift-row\" v-if=\"giftRow1 && giftRow1.length > 0\">\n              <div class=\"gift-item\" v-for=\"item in giftRow1\" :key=\"item.id\">\n                <div class=\"gift-box\">\n                  <div class=\"gift-ribbon\">\n                    <span>热门</span>\n                  </div>\n                  <div class=\"gift-image-container\">\n                    <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                    <div class=\"gift-shine\"></div>\n                  </div>\n                  <div class=\"gift-info\">\n                    <div class=\"gift-name\">{{ item.name }}</div>\n                    <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                    <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <!-- 第1行和第2行之间的分割线 -->\n            <div class=\"divider-line\" v-if=\"giftRow1 && giftRow1.length > 0 && giftRow2 && giftRow2.length > 0\">\n              <div class=\"star-divider\">★</div>\n            </div>\n            \n            <!-- 第2行礼物 -->\n            <div class=\"gift-row\" v-if=\"giftRow2 && giftRow2.length > 0\">\n              <div class=\"gift-item\" v-for=\"item in giftRow2\" :key=\"item.id\">\n                <div class=\"gift-box\">\n                  <div class=\"gift-image-container\">\n                    <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                    <div class=\"gift-shine\"></div>\n                  </div>\n                  <div class=\"gift-info\">\n                    <div class=\"gift-name\">{{ item.name }}</div>\n                    <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                    <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <!-- 第2行和第3行之间的分割线 -->\n            <div class=\"divider-line\" v-if=\"giftRow2 && giftRow2.length > 0 && giftRow3 && giftRow3.length > 0\">\n              <div class=\"star-divider\">★</div>\n            </div>\n            \n            <!-- 第3行礼物 -->\n            <div class=\"gift-row\" v-if=\"giftRow3 && giftRow3.length > 0\">\n              <div class=\"gift-item\" v-for=\"item in giftRow3\" :key=\"item.id\">\n                <div class=\"gift-box\">\n                  <div class=\"gift-image-container\">\n                    <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                    <div class=\"gift-shine\"></div>\n                  </div>\n                  <div class=\"gift-info\">\n                    <div class=\"gift-name\">{{ item.name }}</div>\n                    <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                    <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <!-- 第3行和第4行之间的分割线 -->\n            <div class=\"divider-line\" v-if=\"giftRow3 && giftRow3.length > 0 && giftRow4 && giftRow4.length > 0\">\n              <div class=\"star-divider\">★</div>\n            </div>\n            \n            <!-- 第4行礼物 -->\n            <div class=\"gift-row\" v-if=\"giftRow4 && giftRow4.length > 0\">\n              <div class=\"gift-item\" v-for=\"item in giftRow4\" :key=\"item.id\">\n                <div class=\"gift-box\">\n                  <div class=\"gift-image-container\">\n                    <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                    <div class=\"gift-shine\"></div>\n                  </div>\n                  <div class=\"gift-info\">\n                    <div class=\"gift-name\">{{ item.name }}</div>\n                    <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                    <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <!-- 第4行和第5行之间的分割线 -->\n            <div class=\"divider-line\" v-if=\"giftRow4 && giftRow4.length > 0 && giftRow5 && giftRow5.length > 0\">\n              <div class=\"star-divider\">★</div>\n            </div>\n            \n            <!-- 第5行礼物 -->\n            <div class=\"gift-row\" v-if=\"giftRow5 && giftRow5.length > 0\">\n              <div class=\"gift-item\" v-for=\"item in giftRow5\" :key=\"item.id\">\n                <div class=\"gift-box premium\">\n                  <div class=\"gift-ribbon\">\n                    <span>珍稀</span>\n                  </div>\n                  <div class=\"gift-image-container\">\n                    <img :src=\"item.imgUrl\" alt=\"礼物图片\" class=\"gift-image\">\n                    <div class=\"gift-shine\"></div>\n                  </div>\n                  <div class=\"gift-info\">\n                    <div class=\"gift-name\">{{ item.name }}</div>\n                    <div class=\"gift-score\"><i class=\"coin-icon\"></i>所需金币: {{ item.score }}</div>\n                    <a-button type=\"primary\" class=\"exchange-btn\" @click=\"exchangeGift(item)\">立即兑换</a-button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 右侧用户信息区域 -->\n        <div class=\"user-info\">\n          <div class=\"user-card\">\n            <div class=\"card-decoration top-left\"></div>\n            <div class=\"card-decoration top-right\"></div>\n            <div class=\"card-decoration bottom-left\"></div>\n            <div class=\"card-decoration bottom-right\"></div>\n            \n            <div class=\"user-avatar-box\">\n              <div class=\"avatar-frame\">\n                <img :src=\"userAvatar\" alt=\"账号头像\" class=\"user-avatar\">\n              </div>\n            </div>\n            <div class=\"user-name\">{{ userName }}</div>\n            <div class=\"user-score\">\n              <div class=\"coin-container\">\n                <div class=\"coin-stack\"></div>\n                <span>剩余金币: {{ userScore }}</span>\n              </div>\n            </div>\n            \n            <div class=\"exchange-history\">\n              <div class=\"history-title\">\n                <i class=\"history-icon\"></i>兑换记录\n                <a-button type=\"link\" class=\"more-btn\" @click=\"showAllRecords\">更多</a-button>\n              </div>\n              <div class=\"history-list\">\n                <a-spin :spinning=\"loadingRecords\">\n                  <div v-if=\"exchangeRecords && exchangeRecords.length > 0\">\n                    <div v-for=\"(record, index) in exchangeRecords\" :key=\"index\" class=\"history-item\">\n                      <span class=\"gift-name\">{{ record.giftName }}</span>\n                      <span class=\"gift-time\">{{ formatDate(record.exchangeTime) }}</span>\n                      <span class=\"gift-status\" :class=\"record.status === 1 ? 'received' : 'waiting'\">\n                        {{ record.status === 1 ? '已领取' : '待领取' }}\n                      </span>\n                    </div>\n                  </div>\n                  <div v-else class=\"history-empty\">暂无兑换记录</div>\n                </a-spin>\n              </div>\n            </div>\n            \n            <div class=\"coin-rules\">\n              <a-button type=\"link\" @click=\"showRules\">如何获得更多金币?</a-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 添加兑换记录详情模态框 -->\n    <a-modal\n      :title=\"'礼物兑换记录'\"\n      :visible=\"recordsModalVisible\"\n      @cancel=\"closeRecordsModal\"\n      :footer=\"null\"\n      width=\"800px\"\n      :bodyStyle=\"{ maxHeight: '600px', overflow: 'auto', padding: '24px' }\"\n      class=\"records-modal\"\n    >\n      <a-spin :spinning=\"loadingAllRecords\">\n        <a-table\n          :columns=\"recordColumns\"\n          :dataSource=\"allExchangeRecords\"\n          :pagination=\"pagination\"\n          @change=\"handleTableChange\"\n          rowKey=\"id\"\n          size=\"middle\"\n          :scroll=\"{ y: 450 }\"\n        >\n          <template slot=\"giftName\" slot-scope=\"text\">\n            <span>{{ text }}</span>\n          </template>\n          <template slot=\"exchangeTime\" slot-scope=\"text\">\n            <span>{{ formatDate(text) }}</span>\n          </template>\n          <template slot=\"status\" slot-scope=\"text\">\n            <a-tag :color=\"text === 1 ? 'green' : 'orange'\">\n              {{ text === 1 ? '已领取' : '待领取' }}\n            </a-tag>\n          </template>\n          <template slot=\"receiveTime\" slot-scope=\"text\">\n            <span>{{ text ? formatDate(text) : '-' }}</span>\n          </template>\n        </a-table>\n      </a-spin>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport { getAction, postAction } from '@/api/manage'\nimport moment from 'moment'\nimport { getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'ShoppingModal',\n  data() {\n    return {\n      visible: false,\n      userAvatar: '/logo.png',\n      userName: '账号名',\n      userScore: 0,\n      exchangeRecords: [],\n      loadingRecords: false,\n      // 修改为空数组，将从后端加载数据\n      giftRow1: [],\n      giftRow2: [],\n      giftRow3: [],\n      giftRow4: [],\n      giftRow5: [],\n      // 添加礼物数据加载状态\n      loadingGifts: false,\n      recordsModalVisible: false,\n      loadingAllRecords: false,\n      allExchangeRecords: [],\n      recordColumns: [\n        {\n          title: '礼物名称',\n          dataIndex: 'giftName',\n          key: 'giftName',\n          scopedSlots: { customRender: 'giftName' }\n        },\n        {\n          title: '消费金币',\n          dataIndex: 'coinCount',\n          key: 'coinCount'\n        },\n        {\n          title: '兑换时间',\n          dataIndex: 'exchangeTime',\n          key: 'exchangeTime',\n          scopedSlots: { customRender: 'exchangeTime' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '领取时间',\n          dataIndex: 'receiveTime',\n          key: 'receiveTime',\n          scopedSlots: { customRender: 'receiveTime' }\n        }\n      ],\n      pagination: {\n        pageSize: 20, // 默认每页显示20条\n        current: 1,\n        // 添加更多分页配置\n        pageSizeOptions: ['10', '20', '30', '50'],\n        showSizeChanger: true,\n        showQuickJumper: true,\n        // 修改显示格式，与图片中一致\n        showTotal: (total, range) => `${range[0]}-${range[1]} 共${total}条`\n      },\n      // 添加一个标志来跟踪是否是首次加载\n      isFirstLoad: true\n    }\n  },\n  mounted() {\n    // 获取用户信息\n    if (this.$store.state.user.info) {\n      this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n      \n      // 使用getFileAccessHttpUrl处理头像路径\n      const avatar = this.$store.state.user.avatar || '/logo.png';\n      console.log('原始头像路径:', avatar);\n      \n      // 尝试从用户信息中获取头像路径\n      if (avatar) {\n        this.userAvatar = getFileAccessHttpUrl(avatar);\n        console.log('处理后头像路径:', this.userAvatar);\n      } else if (this.$store.getters.sysConfig.avatar) {\n        // 如果用户没有头像，使用默认头像\n        const defaultAvatar = this.$store.getters.sysConfig.avatar;\n        this.userAvatar = getFileAccessHttpUrl(defaultAvatar);\n        console.log('使用默认头像:', this.userAvatar);\n      } else {\n        // 使用静态默认头像\n        this.userAvatar = getFileAccessHttpUrl('/logo.png');\n        console.log('使用静态默认头像:', this.userAvatar);\n      }\n    }\n  },\n  methods: {\n    showModal() {\n      this.visible = true\n      this.loadUserCoin()\n      this.loadExchangeRecords()\n      this.loadGiftData() // 加载礼物数据\n    },\n    handleCancel() {\n      this.visible = false\n    },\n    // 加载用户金币数\n    loadUserCoin() {\n      getAction('/teaching/coin/getUserCoin').then(res => {\n        if (res.success) {\n          this.userScore = res.result || 0\n        }\n      })\n    },\n    // 加载兑换记录\n    loadExchangeRecords() {\n      this.loadingRecords = true\n      getAction('/teaching/coin/getExchangeRecords', { pageNo: 1, pageSize: 5 }).then(res => {\n        this.loadingRecords = false\n        if (res.success && res.result && res.result.records) {\n          this.exchangeRecords = res.result.records\n        }\n      }).catch(() => {\n        this.loadingRecords = false\n      })\n    },\n    // 新增：加载礼物数据\n    loadGiftData() {\n      console.log('加载礼物数据')\n      this.loadingGifts = true;\n      getAction('/teaching/gift/getShoppingGifts').then(res => {\n        if (res.success && res.result && res.result.length > 0) {\n          console.log('加载礼物数据成功')\n          // 清空原有数据\n          this.giftRow1 = [];\n          this.giftRow2 = [];\n          this.giftRow3 = [];\n          this.giftRow4 = [];\n          this.giftRow5 = [];\n          \n          // 按行号分组\n          res.result.forEach(gift => {\n            // 构建礼物对象\n            const giftItem = {\n              id: gift.id,\n              name: gift.giftName,\n              imgUrl: getFileAccessHttpUrl(gift.giftImg),\n              score: gift.coinCount,\n              type: gift.giftType,\n              desc: gift.giftDesc\n            };\n            \n            // 根据行号分配到对应数组\n            switch(gift.giftRow) {\n              case 1:\n                this.giftRow1.push(giftItem);\n                break;\n              case 2:\n                this.giftRow2.push(giftItem);\n                break;\n              case 3:\n                this.giftRow3.push(giftItem);\n                break;\n              case 4:\n                this.giftRow4.push(giftItem);\n                break;\n              case 5:\n                this.giftRow5.push(giftItem);\n                break;\n              default:\n                this.giftRow1.push(giftItem);\n            }\n          });\n        } else {\n          // 如果加载失败，使用默认数据\n          console.log('加载礼物数据失败')\n          this.useDefaultGiftData();\n        }\n        this.loadingGifts = false;\n      }).catch(() => {\n        // 发生错误时使用默认数据\n        console.log('加载礼物数据失败')\n        this.useDefaultGiftData();\n        this.loadingGifts = false;\n      });\n    },\n    // 使用默认礼物数据（以防后端数据加载失败）\n    useDefaultGiftData() {\n      this.giftRow1 = [\n        {\n          id: 1,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        },\n        {\n          id: 2,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        },\n        {\n          id: 3,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '50'\n        }\n      ];\n      \n      this.giftRow2 = [\n        {\n          id: 4,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        },\n        {\n          id: 5,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        },\n        {\n          id: 6,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '100'\n        }\n      ];\n      \n      this.giftRow3 = [\n        {\n          id: 7,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        },\n        {\n          id: 8,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        },\n        {\n          id: 9,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '200'\n        }\n      ];\n      \n      this.giftRow4 = [\n        {\n          id: 10,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        },\n        {\n          id: 11,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        },\n        {\n          id: 12,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '500'\n        }\n      ];\n      \n      this.giftRow5 = [\n        {\n          id: 13,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        },\n        {\n          id: 14,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        },\n        {\n          id: 15,\n          name: '陀螺',\n          imgUrl: require('@/assets/shopping_img/陀螺.png'),\n          score: '1000'\n        }\n      ];\n    },\n    exchangeGift(gift) {\n      // 检查金币是否足够\n      if (this.userScore < Number(gift.score)) {\n        this.$message.error('金币不足，无法兑换该礼物')\n        return\n      }\n      \n      // 调用兑换API\n      const params = {\n        giftId: gift.id.toString(),\n        giftName: gift.name,\n        coinCount: Number(gift.score)\n      }\n      \n      postAction('/teaching/coin/exchangeGift', params).then(res => {\n        if (res.success) {\n          this.$message.success(res.message || '兑换成功')\n          // 更新金币数和兑换记录\n          this.loadUserCoin()\n          this.loadExchangeRecords()\n        } else {\n          this.$message.error(res.message || '兑换失败')\n        }\n      })\n    },\n    showRules() {\n      const h = this.$createElement;\n      this.$info({\n        title: '获取金币方式',\n        content: h => {\n          return h('div', {\n            style: {\n              padding: '10px 0'\n            }\n          }, [\n            h('div', { style: { marginBottom: '10px', fontSize: '14px' }}, [\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '作品被评为首页展示：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '作品被评为精选：+10金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成客观题作业：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center', marginBottom: '8px' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成编程题作业：+5金币')\n              ]),\n              h('div', { style: { display: 'flex', alignItems: 'center' }}, [\n                h('span', { style: { color: '#1890ff', marginRight: '8px' }}, '•'),\n                h('span', '完成每日任务：+9金币')\n              ])\n            ])\n          ])\n        },\n        width: 350,\n        okText: '我知道了',\n        centered: true,\n        maskClosable: true\n      })\n    },\n    formatDate(date) {\n      if (!date) return ''\n      return moment(date).format('YYYY-MM-DD HH:mm')\n    },\n    showAllRecords() {\n      // 打开模态框前重置分页状态\n      this.pagination = {\n        ...this.pagination,\n        current: 1,\n        pageSize: 10 // 默认设置为每页20条\n      };\n      this.recordsModalVisible = true;\n      \n      // 如果是首次加载，设置特殊参数\n      if (this.isFirstLoad) {\n        this.isFirstLoad = false;\n      }\n      \n      this.loadAllExchangeRecords();\n    },\n    closeRecordsModal() {\n      this.recordsModalVisible = false;\n      // 清空数据，避免下次打开时显示旧数据\n      this.allExchangeRecords = [];\n    },\n    loadAllExchangeRecords() {\n      this.loadingAllRecords = true;\n      // 使用现有的getExchangeRecords接口，加载所有记录\n      getAction('/teaching/coin/getExchangeRecords', { \n        pageNo: this.pagination.current, \n        pageSize: this.pagination.pageSize,\n        // 添加一个标志，表示获取所有记录，而不是仅限于前5条\n        showAll: true,\n        // 添加时间戳参数，防止缓存\n        _t: new Date().getTime()\n      }).then(res => {\n        this.loadingAllRecords = false;\n        if (res.success && res.result) {\n          this.allExchangeRecords = res.result.records || [];\n          // 确保设置正确的总记录数，但保留当前的分页配置\n          this.pagination = {\n            ...this.pagination,\n            total: res.result.total || 0\n          };\n          \n          // 打印分页信息，用于调试\n          console.log('分页信息:', {\n            current: this.pagination.current,\n            pageSize: this.pagination.pageSize,\n            total: this.pagination.total\n          });\n        }\n      }).catch(() => {\n        this.loadingAllRecords = false;\n      });\n    },\n    handleTableChange(pagination, filters, sorter) {\n      console.log('分页变化:', pagination);\n      // 更新分页信息\n      this.pagination.current = pagination.current;\n      this.pagination.pageSize = pagination.pageSize;\n      // 重新加载数据\n      this.loadAllExchangeRecords();\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n/* 新增模态框标题样式 */\n.modal-title {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-small-icon {\n  width: 60px;\n  height: 60px;\n  margin-right: 10px;\n}\n\n/* 整体容器 */\n.shopping-container {\n  display: flex;\n  height: 700px;\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &:before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: url('data:image/svg+xml;utf8,<svg width=\"100\" height=\"100\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"10\" cy=\"10\" r=\"2\" fill=\"%23007bff\" opacity=\"0.1\"/><circle cx=\"30\" cy=\"40\" r=\"3\" fill=\"%23ff6b6b\" opacity=\"0.1\"/><circle cx=\"70\" cy=\"20\" r=\"2\" fill=\"%23ff922b\" opacity=\"0.1\"/><circle cx=\"90\" cy=\"80\" r=\"3\" fill=\"%230066ff\" opacity=\"0.1\"/><circle cx=\"50\" cy=\"70\" r=\"2\" fill=\"%23f06595\" opacity=\"0.1\"/></svg>');\n    background-size: 100px 100px;\n    opacity: 0.8;\n    z-index: 0;\n  }\n}\n\n/* 模态框样式 */\n.kids-modal {\n  :deep(.ant-modal-content) {\n    border-radius: 20px;\n    overflow: hidden;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\n  }\n}\n\n/* 左侧礼物架 */\n.gift-shelf {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px;\n  position: relative;\n  z-index: 1;\n  \n  &::-webkit-scrollbar {\n    width: 10px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(0, 0, 0, 0.05);\n    border-radius: 10px;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%);\n    border-radius: 10px;\n    \n    &:hover {\n      background: linear-gradient(180deg, #ff9a9e 0%, #fad0c4 70%, #fad0c4 100%);\n    }\n  }\n}\n\n/* 礼物架标题 */\n.shelf-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 15px;\n  border-bottom: 2px dashed #b8c4d9;\n  position: relative;\n  \n  &:after {\n    content: '';\n    position: absolute;\n    bottom: -10px;\n    left: 50%;\n    transform: translateX(-50%);\n    width: 100px;\n    height: 20px;\n    background-image: url('data:image/svg+xml;utf8,<svg width=\"100\" height=\"20\" viewBox=\"0 0 100 20\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10,10 L30,5 L50,15 L70,5 L90,10\" stroke=\"%23ff6b6b\" stroke-width=\"2\" fill=\"none\" stroke-dasharray=\"5,5\"/></svg>');\n    background-repeat: no-repeat;\n  }\n}\n\n// .shelf-title {\n//   font-size: 28px;\n//   font-weight: bold;\n//   color: #2e86de;\n//   display: flex;\n//   align-items: center;\n//   justify-content: center;\n//   margin-bottom: 8px;\n  \n//   .title-icon {\n//     width: 72px;\n//     height: 72px;\n//     margin-right: 10px;\n//   }\n// }\n\n.shelf-subtitle {\n  font-size: 16px;\n  color: #5f7d95;\n  font-style: italic;\n}\n\n/* 礼物架内容 */\n.shelf-content {\n  padding: 10px;\n}\n\n/* 礼物行 */\n.gift-row {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 25px;\n}\n\n/* 分隔线 */\n.divider-line {\n  height: 2px;\n  background: linear-gradient(90deg, transparent 0%, rgba(136, 84, 208, 0.3) 50%, transparent 100%);\n  margin: 25px 0;\n  position: relative;\n  \n  .star-divider {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    background-color: #f5f7fa;\n    padding: 0 15px;\n    color: #ff6b6b;\n    font-size: 18px;\n  }\n}\n\n/* 礼物项 */\n.gift-item {\n  width: 32%;\n  text-align: center;\n  position: relative;\n}\n\n/* 礼物盒子 */\n.gift-box {\n  border-radius: 15px;\n  padding: 15px;\n  transition: all 0.3s;\n  background: white;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: visible; /* 修改为visible，让ribbon可以溢出显示 */\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);\n    \n    .gift-shine {\n      transform: translateX(150%) translateY(-50%) rotate(45deg);\n    }\n    \n    .exchange-btn {\n      background: linear-gradient(45deg, #ff416c, #ff4b2b);\n    }\n  }\n  \n  &.premium {\n    background: linear-gradient(135deg, #fff6e9 0%, #fffcf9 100%);\n    border: 2px solid #ffd6a5;\n    \n    .gift-name {\n      color: #ff7730;\n    }\n  }\n}\n\n/* 礼物标签 */\n.gift-ribbon {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 80px;\n  height: 80px;\n  overflow: hidden;\n  z-index: 2; /* 确保标签在最上层 */\n  \n  span {\n    position: absolute;\n    display: block;\n    width: 120px;\n    padding: 5px 0;\n    background-color: #ff6b6b;\n    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n    color: white;\n    font-size: 12px;\n    font-weight: bold;\n    text-align: center;\n    right: -30px;\n    top: 20px;\n    transform: rotate(45deg);\n    z-index: 2;\n  }\n}\n\n/* 礼物图片容器 */\n.gift-image-container {\n  position: relative;\n  overflow: hidden;\n  margin-bottom: 15px;\n  border-radius: 10px;\n  background-color: #f8f9fa;\n  padding: 10px;\n  height: 150px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1; /* 确保图片容器在标签下方 */\n}\n\n/* 礼物图片 */\n.gift-image {\n  max-width: 100%;\n  max-height: 130px;\n  object-fit: contain;\n  transition: all 0.3s;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n}\n\n/* 闪光效果 */\n.gift-shine {\n  position: absolute;\n  top: 50%;\n  left: -100%;\n  width: 50px;\n  height: 200%;\n  background: rgba(255, 255, 255, 0.6);\n  transform: translateY(-50%) rotate(45deg);\n  transition: all 0.5s;\n}\n\n/* 礼物信息 */\n.gift-info {\n  padding: 10px 5px 5px;\n}\n\n/* 礼物名称 */\n.gift-name {\n  font-size: 18px;\n  font-weight: bold;\n  color: #3d5af1;\n  margin-bottom: 8px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 金币 */\n.gift-score {\n  font-size: 15px;\n  color: #ff7730;\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .coin-icon {\n    display: inline-block;\n    width: 18px;\n    height: 18px;\n    background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n    background-size: contain;\n    margin-right: 5px;\n  }\n}\n\n/* 兑换按钮 */\n.exchange-btn {\n  width: 100%;\n  border-radius: 30px;\n  height: 36px;\n  font-size: 14px;\n  font-weight: bold;\n  background: linear-gradient(45deg, #3d5af1, #44a4fe);\n  border: none;\n  box-shadow: 0 5px 15px rgba(13, 110, 253, 0.15);\n  transition: all 0.3s;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 15px rgba(13, 110, 253, 0.2);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n}\n\n/* 右侧用户信息 */\n.user-info {\n  width: 280px;\n  padding: 20px;\n  position: relative;\n  z-index: 1;\n}\n\n/* 用户卡片 */\n.user-card {\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);\n  border-radius: 20px;\n  padding: 25px 15px;\n  text-align: center;\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);\n  height: 100%;\n  position: relative;\n  \n  &:after {\n    content: '';\n    position: absolute;\n    top: 10px;\n    left: 10px;\n    right: 10px;\n    bottom: 10px;\n    border: 2px dashed #b8c4d9;\n    border-radius: 15px;\n    pointer-events: none;\n  }\n}\n\n/* 卡片装饰 */\n.card-decoration {\n  position: absolute;\n  width: 30px;\n  height: 30px;\n  z-index: 1;\n  \n  &.top-left {\n    top: 10px;\n    left: 10px;\n    border-top: 4px solid #ff9a9e;\n    border-left: 4px solid #ff9a9e;\n    border-top-left-radius: 10px;\n  }\n  \n  &.top-right {\n    top: 10px;\n    right: 10px;\n    border-top: 4px solid #ff9a9e;\n    border-right: 4px solid #ff9a9e;\n    border-top-right-radius: 10px;\n  }\n  \n  &.bottom-left {\n    bottom: 10px;\n    left: 10px;\n    border-bottom: 4px solid #ff9a9e;\n    border-left: 4px solid #ff9a9e;\n    border-bottom-left-radius: 10px;\n  }\n  \n  &.bottom-right {\n    bottom: 10px;\n    right: 10px;\n    border-bottom: 4px solid #ff9a9e;\n    border-right: 4px solid #ff9a9e;\n    border-bottom-right-radius: 10px;\n  }\n}\n\n/* 用户头像框 */\n.user-avatar-box {\n  margin-bottom: 20px;\n}\n\n/* 头像框架 */\n.avatar-frame {\n  width: 110px;\n  height: 110px;\n  margin: 0 auto;\n  position: relative;\n  \n  &:before {\n    content: '';\n    position: absolute;\n    top: -5px;\n    left: -5px;\n    right: -5px;\n    bottom: -5px;\n    background: linear-gradient(45deg, #3d5af1, #44a4fe, #ff6b6b, #ff9a9e);\n    border-radius: 20px;\n    z-index: -1;\n    animation: rotate 5s linear infinite;\n  }\n  \n  @keyframes rotate {\n    0% { filter: hue-rotate(0deg); }\n    100% { filter: hue-rotate(360deg); }\n  }\n}\n\n/* 用户头像 */\n.user-avatar {\n  width: 110px;\n  height: 110px;\n  object-fit: cover;\n  border-radius: 15px;\n  border: 5px solid white;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n/* 用户名 */\n.user-name {\n  font-size: 22px;\n  font-weight: bold;\n  color: #2e86de;\n  margin-bottom: 15px;\n  position: relative;\n  display: inline-block;\n  \n  &:after {\n    content: '';\n    position: absolute;\n    bottom: -5px;\n    left: 50%;\n    transform: translateX(-50%);\n    width: 50px;\n    height: 3px;\n    background: linear-gradient(90deg, #44a4fe, #3d5af1);\n    border-radius: 3px;\n  }\n}\n\n/* 用户金币 */\n.user-score {\n  font-size: 18px;\n  color: #ff7730;\n  font-weight: bold;\n  margin-bottom: 25px;\n  \n  .coin-container {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .coin-stack {\n    width: 24px;\n    height: 24px;\n    background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n    background-size: contain;\n    margin-right: 8px;\n    position: relative;\n    \n    &:before, &:after {\n      content: '';\n      position: absolute;\n      width: 24px;\n      height: 24px;\n      background: inherit;\n      background-size: contain;\n    }\n    \n    &:before {\n      top: -3px;\n      left: -6px;\n      z-index: -1;\n    }\n    \n    &:after {\n      top: -6px;\n      left: -3px;\n      z-index: -2;\n    }\n  }\n}\n\n/* 兑换历史 */\n.exchange-history {\n  background-color: white;\n  border-radius: 15px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\n  max-height: 430px;\n}\n\n.history-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #2e86de;\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  .history-icon {\n    display: inline-block;\n    width: 18px;\n    height: 18px;\n    background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"%232e86de\" stroke-width=\"2\" fill=\"none\"/><polyline points=\"12,6 12,12 16,14\" stroke=\"%232e86de\" stroke-width=\"2\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>');\n    background-size: contain;\n    margin-right: 5px;\n  }\n  \n  .more-btn {\n    padding: 0;\n    font-size: 14px;\n    height: auto;\n    line-height: 1;\n    color: #3d5af1;\n    \n    &:hover {\n      color: #44a4fe;\n    }\n  }\n}\n\n.history-list {\n  max-height: 280px;\n  overflow-y: auto;\n  padding-right: 5px; /* 添加右侧内边距，防止滚动条挡住内容 */\n  \n  .history-item {\n    padding: 8px 5px 8px 0; /* 调整内边距，左边不变，右边减少 */\n    border-bottom: 1px dashed #eee;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    \n    .gift-name {\n      min-width: 60px; /* 减少最小宽度 */\n      max-width: 80px; /* 减少最大宽度 */\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-right: 5px;\n      font-weight: 500;\n      font-size: 16px;\n    }\n    \n    .gift-time {\n      color: #999;\n      font-size: 12px;\n      margin-right: 10px; /* 增加右边距 */\n      flex-shrink: 1;\n      min-width: 60px;\n      white-space: nowrap; /* 防止日期换行 */\n    }\n    \n    .gift-status {\n      font-size: 12px;\n      min-width: 50px; /* 减少最小宽度 */\n      text-align: right;\n      white-space: nowrap; /* 防止状态文本换行 */\n      \n      &.waiting {\n        color: #faad14;\n      }\n      \n      &.received {\n        color: #52c41a;\n      }\n    }\n}\n\n.history-empty {\n  padding: 20px 0;\n    color: #999;\n  text-align: center;\n  }\n}\n\n/* 金币规则 */\n.coin-rules {\n  margin-top: -10%;\n  \n  a-button {\n    color: #3d5af1;\n    font-weight: 500;\n    \n    &:hover {\n      color: #44a4fe;\n      text-decoration: underline;\n    }\n  }\n}\n\n/* 模态框页脚 */\n.modal-footer {\n  padding: 15px;\n  background-color: #f8f9fa;\n  \n  .info-icon {\n    display: inline-block;\n    width: 18px;\n    height: 18px;\n    background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%233d5af1\"/><text x=\"12\" y=\"17\" font-size=\"14\" text-anchor=\"middle\" fill=\"white\" font-weight=\"bold\">i</text></svg>');\n    background-size: contain;\n    margin-right: 5px;\n    vertical-align: middle;\n  }\n}\n\n/* 模态框头部样式 */\n:deep(.ant-modal-header) {\n  background: linear-gradient(90deg, #3d5af1, #44a4fe);\n  padding: 15px 20px;\n  border-bottom: none;\n  \n  .ant-modal-title {\n    color: white;\n    font-weight: bold;\n    font-size: 22px;\n    text-align: center;\n    font-family: 'Comic Sans MS', 'Microsoft YaHei', sans-serif;\n    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);\n  }\n}\n\n/* 模态框关闭按钮样式 */\n:deep(.ant-modal-close) {\n  color: white;\n  \n  &:hover {\n    color: #ffd6a5;\n  }\n}\n\n/* 控制弹窗高度 */\n:deep(.ant-modal-body) {\n  max-height: 1000px !important;\n  overflow: hidden;\n}\n\n/* 响应式调整 */\n@media (max-width: 1200px) {\n  .user-info {\n    width: 220px;\n  }\n  \n  .gift-name {\n    font-size: 16px;\n  }\n  \n  .gift-score {\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 992px) {\n  .shopping-container {\n    flex-direction: column;\n    height: auto;\n  }\n  \n  .user-info {\n    width: 100%;\n    padding-top: 0;\n  }\n  \n  .user-card {\n    max-height: 300px;\n  }\n  \n  .exchange-history {\n    display: none;\n  }\n}\n\n/* 兑换记录详情模态框样式 */\n:deep(.records-modal) {\n  .ant-modal-header {\n    background: linear-gradient(90deg, #3d5af1, #44a4fe);\n    padding: 15px 20px;\n    border-bottom: none;\n  }\n  \n  .ant-modal-title {\n    color: white;\n    font-weight: bold;\n    font-size: 18px;\n    text-align: center;\n  }\n  \n  .ant-modal-close {\n    color: white;\n    \n    &:hover {\n      color: #ffd6a5;\n    }\n  }\n  \n  .ant-table-thead > tr > th {\n    background-color: #f0f5ff;\n    color: #3d5af1;\n    font-weight: bold;\n  }\n  \n  .ant-pagination-item-active {\n    border-color: #3d5af1;\n    \n    a {\n      color: #3d5af1;\n    }\n  }\n  \n  .table-footer {\n    padding: 12px 0;\n    text-align: right;\n    color: rgba(0, 0, 0, 0.65);\n    font-size: 14px;\n  }\n}\n</style>\n"]}]}