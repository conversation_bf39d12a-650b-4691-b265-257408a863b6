{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SplitPanel.vue?vue&type=template&id=b01243f6&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\SplitPanel.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-card\", [_c(\"a-button\", {\n    attrs: {\n      type: \"primary\",\n      icon: \"desktop\"\n    },\n    on: {\n      click: _vm.splitPane\n    }\n  }, [_vm._v(\"点我分屏\")]), _c(\"split-panel-modal\", {\n    ref: \"splitPanelModal\"\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "type", "icon", "on", "click", "splitPane", "_v", "ref", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/jeecg/SplitPanel.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-card\",\n    [\n      _c(\n        \"a-button\",\n        {\n          attrs: { type: \"primary\", icon: \"desktop\" },\n          on: { click: _vm.splitPane },\n        },\n        [_vm._v(\"点我分屏\")]\n      ),\n      _c(\"split-panel-modal\", { ref: \"splitPanelModal\" }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR,CACEA,EAAE,CACA,UAAU,EACV;IACEE,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC3CC,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACQ;IAAU;EAC7B,CAAC,EACD,CAACR,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CAAC,mBAAmB,EAAE;IAAES,GAAG,EAAE;EAAkB,CAAC,CAAC,CACpD,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}]}