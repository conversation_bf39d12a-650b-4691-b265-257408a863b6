{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\HeadInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\HeadInfo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  export default {\n    name: \"HeadInfo\",\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      content: {\n        type: String,\n        default: ''\n      },\n      bordered: {\n        type: Boolean,\n        default: false\n      },\n      center: {\n        type: Boolean,\n        default: true\n      }\n    }\n  }\n", {"version": 3, "sources": ["HeadInfo.vue"], "names": [], "mappings": ";AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HeadInfo.vue", "sourceRoot": "src/components/tools", "sourcesContent": ["<template>\n  <div class=\"head-info\" :class=\"center && 'center'\">\n    <span>{{ title }}</span>\n    <p>{{ content }}</p>\n    <em v-if=\"bordered\"/>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"HeadInfo\",\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      content: {\n        type: String,\n        default: ''\n      },\n      bordered: {\n        type: Boolean,\n        default: false\n      },\n      center: {\n        type: Boolean,\n        default: true\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .head-info {\n    position: relative;\n    text-align: left;\n    padding: 0 32px 0 0;\n    min-width: 125px;\n\n    &.center {\n      text-align: center;\n      padding: 0 32px;\n    }\n\n    span {\n      color: rgba(0, 0, 0, .45);\n      display: inline-block;\n      font-size: 14px;\n      line-height: 22px;\n      margin-bottom: 4px;\n    }\n    p {\n      color: rgba(0, 0, 0, .85);\n      font-size: 24px;\n      line-height: 32px;\n      margin: 0;\n    }\n    em {\n      background-color: #e8e8e8;\n      position: absolute;\n      height: 56px;\n      width: 1px;\n      top: 0;\n      right: 0;\n    }\n  }\n</style>"]}]}