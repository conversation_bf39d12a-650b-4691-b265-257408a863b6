{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue", "mtime": 1751031689617}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Banner.vue?vue&type=template&id=5b82e1da&scoped=true\"\nimport script from \"./Banner.vue?vue&type=script&lang=js\"\nexport * from \"./Banner.vue?vue&type=script&lang=js\"\nimport style0 from \"./Banner.vue?vue&type=style&index=0&id=5b82e1da&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b82e1da\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('5b82e1da')) {\n      api.createRecord('5b82e1da', component.options)\n    } else {\n      api.reload('5b82e1da', component.options)\n    }\n    module.hot.accept(\"./Banner.vue?vue&type=template&id=5b82e1da&scoped=true\", function () {\n      api.rerender('5b82e1da', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/home/<USER>/Banner.vue\"\nexport default component.exports"]}