{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step3.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step3.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import Result from '../../result/Result';\nexport default {\n  name: \"Step3\",\n  components: {\n    Result: Result\n  },\n  data: function data() {\n    return {\n      loading: false\n    };\n  },\n  methods: {\n    finish: function finish() {\n      this.$emit('finish');\n    },\n    toOrderList: function toOrderList() {\n      this.$router.push('/list/query-list');\n    }\n  }\n};", {"version": 3, "names": ["Result", "name", "components", "data", "loading", "methods", "finish", "$emit", "toOrderList", "$router", "push"], "sources": ["src/views/form/stepForm/Step3.vue"], "sourcesContent": ["<template>\n  <div>\n    <a-form style=\"margin: 40px auto 0;\">\n      <result title=\"操作成功\" :is-success=\"true\" description=\"预计两小时内到账\">\n        <div class=\"information\">\n          <a-row>\n            <a-col :sm=\"8\" :xs=\"24\">付款账户：</a-col>\n            <a-col :sm=\"16\" :xs=\"24\"><EMAIL></a-col>\n          </a-row>\n          <a-row>\n            <a-col :sm=\"8\" :xs=\"24\">收款账户：</a-col>\n            <a-col :sm=\"16\" :xs=\"24\"><EMAIL></a-col>\n          </a-row>\n          <a-row>\n            <a-col :sm=\"8\" :xs=\"24\">收款人姓名：</a-col>\n            <a-col :sm=\"16\" :xs=\"24\">辉夜</a-col>\n          </a-row>\n          <a-row>\n            <a-col :sm=\"8\" :xs=\"24\">转账金额：</a-col>\n            <a-col :sm=\"16\" :xs=\"24\"><span class=\"money\">500</span> 元</a-col>\n          </a-row>\n        </div>\n        <div slot=\"action\">\n          <a-button type=\"primary\" @click=\"finish\">再转一笔</a-button>\n          <a-button style=\"margin-left: 8px\" @click=\"toOrderList\">查看账单</a-button>\n        </div>\n      </result>\n    </a-form>\n  </div>\n</template>\n\n<script>\n  import Result from '../../result/Result'\n\n  export default {\n    name: \"Step3\",\n    components: {\n      Result\n    },\n    data () {\n      return {\n        loading: false\n      }\n    },\n    methods: {\n      finish () {\n        this.$emit('finish')\n      },\n      toOrderList () {\n        this.$router.push('/list/query-list')\n      }\n    }\n  }\n</script>\n<style lang=\"less\" scoped>\n  .information {\n    line-height: 22px;\n\n    .ant-row:not(:last-child) {\n      margin-bottom: 24px;\n    }\n  }\n  .money {\n    font-family: \"Helvetica Neue\",sans-serif;\n    font-weight: 500;\n    font-size: 20px;\n    line-height: 14px;\n  }\n</style>"], "mappings": "AAgCA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,MAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}