{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniBar.vue?vue&type=style&index=0&id=4358318b&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n@import \"chart\";\n", {"version": 3, "sources": ["MiniBar.vue"], "names": [], "mappings": ";AA4EA", "file": "MiniBar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{'width':width==null?'auto':width+'px'}\">\n    <v-chart :forceFit=\"width==null\" :height=\"height\" :data=\"data\" padding=\"0\">\n      <v-tooltip/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import moment from 'dayjs'\n\n  const sourceData = []\n  const beginDay = new Date().getTime()\n\n  for (let i = 0; i < 10; i++) {\n    sourceData.push({\n      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n      y: Math.round(Math.random() * 10)\n    })\n  }\n\n  const tooltip = [\n    'x*y',\n    (x, y) => ({\n      name: x,\n      value: y\n    })\n  ]\n\n  const scale = [{\n    dataKey: 'x',\n    min: 2\n  }, {\n    dataKey: 'y',\n    title: '时间',\n    min: 1,\n    max: 30\n  }]\n\n  export default {\n    name: 'MiniBar',\n    props: {\n      dataSource: {\n        type: Array,\n        default: () => []\n      },\n      width: {\n        type: Number,\n        default: null\n      },\n      height: {\n        type: Number,\n        default: 200\n      }\n    },\n    created() {\n      if (this.dataSource.length === 0) {\n        this.data = sourceData\n      } else {\n        this.data = this.dataSource\n      }\n    },\n    data() {\n      return {\n        tooltip,\n        data: [],\n        scale\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"chart\";\n</style>"]}]}