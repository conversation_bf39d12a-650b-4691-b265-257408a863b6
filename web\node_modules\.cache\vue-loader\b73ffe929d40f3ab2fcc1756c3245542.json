{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SlidingPanel.vue?vue&type=template&id=5316d9a4&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SlidingPanel.vue", "mtime": 1749719933806}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"sliding-panel-container\">\n  <!-- 横向对话泡泡 - 少儿编程风格 -->\n  <div class=\"tooltip-container\" v-if=\"showBubble && !isExpanded\">\n    <div class=\"tooltip kid-friendly-bubble\" @click.stop=\"hideBubble\">\n      <div class=\"bubble-content\">\n        <div class=\"stars1\">\n          <span class=\"star1\">★</span><br>\n          <span class=\"star1\">★</span>\n        </div>\n        点击浏览小朋友们<br>精彩瞬间～\n        <div class=\"stars2\">\n          <span class=\"star2\">★</span><br>\n          <span class=\"star2\">★</span>\n        </div>\n      </div>\n    </div>\n  </div>\n  \n  <transition name=\"slide\">\n    <div \n      class=\"sliding-panel kid-friendly-panel\" \n      :class=\"{ 'expanded': isExpanded }\"\n      @mouseenter=\"expandPanel\"\n      @mouseleave=\"collapsePanel\"\n      @click=\"handlePanelClick\"\n    >\n      <div class=\"panel-avatar\">\n        <div class=\"coding-icon-container\">\n          <a-icon type=\"camera\" theme=\"filled\" />\n        </div>\n      </div>\n      <div v-show=\"isExpanded\" class=\"panel-content\">\n        <div class=\"panel-header\">\n          <h3>小小创客照片墙</h3>\n          <a-button type=\"primary\" size=\"small\" class=\"kid-button\" @click.stop=\"openMediaWall\">查看全部</a-button>\n        </div>\n        <div v-if=\"previewItems.length > 0\" class=\"panel-preview\">\n          <div v-for=\"(item, index) in previewItems\" :key=\"index\" class=\"preview-item\">\n            <div class=\"preview-wrapper\">\n              <img v-if=\"item.type === 'image'\" :src=\"getFileUrl(item.url)\" :alt=\"`预览图片${index+1}`\" />\n              <div v-if=\"item.type === 'video'\" class=\"video-thumbnail-container\">\n                <img :src=\"getFileUrl(item.url)\" :alt=\"`视频封面${index+1}`\" class=\"video-thumbnail\" />\n                <div class=\"video-play-icon\">\n                  <a-icon type=\"play-circle\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div v-else class=\"empty-preview-container\">\n            <a-empty description=\"暂无照片\" />\n        </div>\n      </div>\n    </div>\n  </transition>\n\n  <!-- 照片墙弹窗 -->\n  <a-modal\n    v-model=\"mediaWallVisible\"\n    :width=\"modalWidth\"\n    :footer=\"null\"\n    :maskClosable=\"true\"\n    destroyOnClose\n    @cancel=\"closeMediaWall\"\n    wrapClassName=\"kid-friendly-modal\"\n  >\n    <media-wall\n      :media-items=\"mediaItems\"\n      @close=\"closeMediaWall\"\n    />\n  </a-modal>\n</div>\n", null]}