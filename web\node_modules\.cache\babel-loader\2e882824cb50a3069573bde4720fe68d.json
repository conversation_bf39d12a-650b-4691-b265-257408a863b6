{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframePageView.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframePageView.vue", "mtime": 1750916319326}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport { ACCESS_TOKEN } from \"@/store/mutation-types\";\nimport PageLayout from '../page/PageLayout';\nimport RouteView from './RouteView';\nexport default {\n  name: \"IframePageContent\",\n  inject: ['closeCurrent'],\n  data: function data() {\n    return {\n      url: \"\",\n      id: \"\"\n    };\n  },\n  created: function created() {\n    this.goUrl();\n  },\n  updated: function updated() {\n    this.goUrl();\n  },\n  watch: {\n    $route: function $route(to, from) {\n      this.goUrl();\n    }\n  },\n  methods: {\n    goUrl: function goUrl() {\n      var url = this.$route.meta.url;\n      var id = this.$route.path;\n      this.id = id;\n      //url = \"http://www.baidu.com\"\n      console.log(\"------url------\" + url);\n      if (url !== null && url !== undefined) {\n        this.url = url;\n        /*update_begin author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n        if (this.$route.meta.internalOrExternal != undefined && this.$route.meta.internalOrExternal == true) {\n          this.closeCurrent();\n          //外部url加入token\n          var tokenStr = \"${token}\";\n          if (url.indexOf(tokenStr) != -1) {\n            var token = Vue.ls.get(ACCESS_TOKEN);\n            this.url = url.replace(tokenStr, token);\n          }\n          window.open(this.url);\n        }\n        /*update_end author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n      }\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "ACCESS_TOKEN", "PageLayout", "RouteView", "name", "inject", "data", "url", "id", "created", "goUrl", "updated", "watch", "$route", "to", "from", "methods", "meta", "path", "console", "log", "undefined", "internalOrExternal", "closeCurrent", "tokenStr", "indexOf", "token", "ls", "get", "replace", "window", "open"], "sources": ["src/components/layouts/IframePageView.vue"], "sourcesContent": ["<template>\n\n    <iframe  :id=\"id\" :src=\"url\" frameborder=\"0\" width=\"100%\" height=\"800px\" scrolling=\"auto\"></iframe>\n\n</template>\n\n<script>\n  import Vue from 'vue'\n  import { ACCESS_TOKEN } from \"@/store/mutation-types\"\n  import PageLayout from '../page/PageLayout'\n  import RouteView from './RouteView'\n\n  export default {\n    name: \"IframePageContent\",\n    inject:['closeCurrent'],\n    data () {\n      return {\n        url: \"\",\n        id:\"\"\n      }\n    },\n    created () {\n      this.goUrl()\n    },\n    updated () {\n      this.goUrl()\n    },\n    watch: {\n      $route(to, from) {\n        this.goUrl();\n      }\n    },\n    methods: {\n      goUrl () {\n        let url = this.$route.meta.url\n        let id = this.$route.path\n        this.id = id\n        //url = \"http://www.baidu.com\"\n        console.log(\"------url------\"+url)\n        if (url !== null && url !== undefined) {\n          this.url = url;\n          /*update_begin author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n          if(this.$route.meta.internalOrExternal != undefined && this.$route.meta.internalOrExternal==true){\n            this.closeCurrent();\n            //外部url加入token\n            let tokenStr = \"${token}\";\n            if(url.indexOf(tokenStr)!=-1){\n              let token = Vue.ls.get(ACCESS_TOKEN);\n               this.url = url.replace(tokenStr,token);\n            }\n            window.open(this.url);\n          }\n          /*update_end author:wuxianquan date:20190908 for:判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */\n\n        }\n      }\n    }\n  }\n</script>\n\n<style>\n</style>"], "mappings": "AAOA,OAAAA,GAAA;AACA,SAAAC,YAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AAEA;EACAC,IAAA;EACAC,MAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,KAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,KAAA;EACA;EACAE,KAAA;IACAC,MAAA,WAAAA,OAAAC,EAAA,EAAAC,IAAA;MACA,KAAAL,KAAA;IACA;EACA;EACAM,OAAA;IACAN,KAAA,WAAAA,MAAA;MACA,IAAAH,GAAA,QAAAM,MAAA,CAAAI,IAAA,CAAAV,GAAA;MACA,IAAAC,EAAA,QAAAK,MAAA,CAAAK,IAAA;MACA,KAAAV,EAAA,GAAAA,EAAA;MACA;MACAW,OAAA,CAAAC,GAAA,qBAAAb,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAc,SAAA;QACA,KAAAd,GAAA,GAAAA,GAAA;QACA;QACA,SAAAM,MAAA,CAAAI,IAAA,CAAAK,kBAAA,IAAAD,SAAA,SAAAR,MAAA,CAAAI,IAAA,CAAAK,kBAAA;UACA,KAAAC,YAAA;UACA;UACA,IAAAC,QAAA;UACA,IAAAjB,GAAA,CAAAkB,OAAA,CAAAD,QAAA;YACA,IAAAE,KAAA,GAAA1B,GAAA,CAAA2B,EAAA,CAAAC,GAAA,CAAA3B,YAAA;YACA,KAAAM,GAAA,GAAAA,GAAA,CAAAsB,OAAA,CAAAL,QAAA,EAAAE,KAAA;UACA;UACAI,MAAA,CAAAC,IAAA,MAAAxB,GAAA;QACA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}