{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue?vue&type=style&index=0&id=10e519b4&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.ant-btn {\n  padding: 0 10px;\n  margin-left: 3px;\n}\n\n.ant-form-item-control {\n  line-height: 0px;\n}\n\n/** 主表单行间距 */\n.ant-form .ant-form-item {\n  margin-bottom: 10px;\n}\n\n/** Tab页面行间距 */\n.ant-tabs-content .ant-form-item {\n  margin-bottom: 0px;\n}\n", {"version": 3, "sources": ["JeecgOrderDMainModal.vue"], "names": [], "mappings": ";AAgLA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "JeecgOrderDMainModal.vue", "sourceRoot": "src/views/jeecg/tablist/form", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 主表单区域 -->\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单号\"\n          hasFeedback>\n          <a-input\n            placeholder=\"请输入订单号\"\n            v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\"\n          />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单类型\">\n          <a-select placeholder=\"请输入订单类型\" v-decorator=\"['ctype',{}]\">\n            <a-select-option value=\"1\">国内订单</a-select-option>\n            <a-select-option value=\"2\">国际订单</a-select-option>\n          </a-select>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单日期\">\n          <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator=\"[ 'orderDate',{}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单金额\">\n          <a-input-number style=\"width: 200px\" v-decorator=\"[ 'orderMoney', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单备注\">\n          <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import JDate from '@/components/jeecg/JDate'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgOrderDMainModal\",\n    components: {\n      JDate\n    },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        orderMainModel: {\n          jeecgOrderCustomerList: [{}],\n          jeecgOrderTicketList: [{}]\n        },\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: \"/test/order/add\",\n          edit: \"/test/order/edit\",\n          orderCustomerList: \"/test/order/listOrderCustomerByMainId\",\n          orderTicketList: \"/test/order/listOrderTicketByMainId\",\n        },\n      }\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.orderMainModel = Object.assign({}, record);\n        //初始化明细表数据\n        console.log(this.orderMainModel.id)\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.orderMainModel, 'orderCode', 'ctype', 'orderMoney', 'content'))\n          this.form.setFieldsValue({orderDate: this.orderMainModel.orderDate ? moment(this.orderMainModel.orderDate) : null}) //时间格式化\n        });\n        console.log(this.orderMainModel)\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.orderMainModel.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let orderMainData = Object.assign(this.orderMainModel, values);\n            //时间格式化\n            orderMainData.orderDate = orderMainData.orderDate ? orderMainData.orderDate.format('YYYY-MM-DD HH:mm:ss') : null;\n            let formData = {\n              ...orderMainData\n            }\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .ant-btn {\n    padding: 0 10px;\n    margin-left: 3px;\n  }\n\n  .ant-form-item-control {\n    line-height: 0px;\n  }\n\n  /** 主表单行间距 */\n  .ant-form .ant-form-item {\n    margin-bottom: 10px;\n  }\n\n  /** Tab页面行间距 */\n  .ant-tabs-content .ant-form-item {\n    margin-bottom: 0px;\n  }\n</style>"]}]}