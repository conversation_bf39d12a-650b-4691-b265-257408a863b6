{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport moment from 'moment';\nimport * as utils from '@/utils/util';\nimport { mixinDevice } from '@/utils/mixin';\nimport JDate from '@/components/jeecg/JDate.vue';\nimport JSelectDepart from '@/components/jeecgbiz/JSelectDepart';\nimport JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser';\nimport JMultiSelectTag from '@/components/dict/JMultiSelectTag';\nimport JAreaLinkage from '@comp/jeecg/JAreaLinkage';\nexport default {\n  name: 'JSuperQuery',\n  mixins: [mixinDevice],\n  components: {\n    JAreaLinkage: JAreaLinkage,\n    JMultiSelectTag: JMultiSelectTag,\n    JDate: JDate,\n    JSelectDepart: JSelectDepart,\n    JSelectMultiUser: JSelectMultiUser\n  },\n  props: {\n    /*\n     fieldList: [{\n        value:'',\n        text:'',\n        type:'',\n        dictCode:'' // 只要 dictCode 有值，无论 type 是什么，都显示为字典下拉框\n     }]\n     type:date datetime int number string\n    * */\n    fieldList: {\n      type: Array,\n      required: true\n    },\n    /*\n    * 这个回调函数接收一个数组参数 即查询条件\n    * */\n    callback: {\n      type: String,\n      required: false,\n      default: 'handleSuperQuery'\n    },\n    // 当前是否在加载中\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    // 保存查询条件的唯一 code，通过该 code 区分\n    // 默认为 null，代表以当前路由全路径为区分Code\n    saveCode: {\n      type: String,\n      default: null\n    }\n  },\n  data: function data() {\n    var h = this.$createElement;\n    return {\n      moment: moment,\n      fieldTreeData: [],\n      prompt: {\n        visible: false,\n        value: ''\n      },\n      visible: false,\n      queryParamsModel: [],\n      treeIcon: h(\"a-icon\", {\n        \"attrs\": {\n          \"type\": \"file-text\"\n        }\n      }),\n      // 保存查询条件的treeData\n      saveTreeData: [],\n      // 保存查询条件的前缀名\n      saveCodeBefore: 'JSuperQuerySaved_',\n      // 查询类型，过滤条件匹配（and、or）\n      matchType: 'and',\n      superQueryFlag: false\n    };\n  },\n  computed: {\n    izMobile: function izMobile() {\n      return this.device === 'mobile';\n    },\n    tooltipProps: function tooltipProps() {\n      return this.izMobile ? {\n        visible: false\n      } : {};\n    },\n    fullSaveCode: function fullSaveCode() {\n      var saveCode = this.saveCode;\n      if (saveCode == null || saveCode === '') {\n        saveCode = this.$route.fullPath;\n      }\n      return this.saveCodeBefore + saveCode;\n    }\n  },\n  watch: {\n    // 当 saveCode 变化时，重新查询已保存的条件\n    fullSaveCode: {\n      immediate: true,\n      handler: function handler() {\n        var _this = this;\n        var list = this.$ls.get(this.fullSaveCode);\n        if (list instanceof Array) {\n          this.saveTreeData = list.map(function (i) {\n            return _this.renderSaveTreeData(i);\n          });\n        }\n      }\n    },\n    fieldList: {\n      deep: true,\n      immediate: true,\n      handler: function handler(val) {\n        var mainData = [],\n          subData = [];\n        val.forEach(function (item) {\n          var data = _objectSpread({}, item);\n          data.label = data.label || data.text;\n          var hasChildren = data.children instanceof Array;\n          data.disabled = hasChildren;\n          data.selectable = !hasChildren;\n          if (hasChildren) {\n            data.children = data.children.map(function (item2) {\n              var child = _objectSpread({}, item2);\n              child.label = child.label || child.text;\n              child.label = data.label + '-' + child.label;\n              child.value = data.value + ',' + child.value;\n              child.val = '';\n              return child;\n            });\n            data.val = '';\n            subData.push(data);\n          } else {\n            mainData.push(data);\n          }\n        });\n        this.fieldTreeData = mainData.concat(subData);\n      }\n    }\n  },\n  methods: {\n    show: function show() {\n      if (!this.queryParamsModel || this.queryParamsModel.length === 0) {\n        this.resetLine();\n      }\n      this.visible = true;\n    },\n    handleOk: function handleOk() {\n      if (!this.isNullArray(this.queryParamsModel)) {\n        var event = {\n          matchType: this.matchType,\n          params: this.removeEmptyObject(this.queryParamsModel)\n        };\n        // 移动端模式下关闭弹窗\n        if (this.izMobile) {\n          this.visible = false;\n        }\n        this.emitCallback(event);\n      } else {\n        this.$message.warn(\"不能查询空条件\");\n      }\n    },\n    emitCallback: function emitCallback() {\n      var event = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _event$params = event.params,\n        params = _event$params === void 0 ? [] : _event$params,\n        _event$matchType = event.matchType,\n        matchType = _event$matchType === void 0 ? this.matchType : _event$matchType;\n      this.superQueryFlag = params && params.length > 0;\n      var _iterator = _createForOfIteratorHelper(params),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var param = _step.value;\n          if (Array.isArray(param.val)) {\n            param.val = param.val.join(',');\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      console.debug('---高级查询参数--->', {\n        params: params,\n        matchType: matchType\n      });\n      this.$emit(this.callback, params, matchType);\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleAdd: function handleAdd() {\n      this.addNewLine();\n    },\n    addNewLine: function addNewLine() {\n      this.queryParamsModel.push({\n        rule: 'eq'\n      });\n    },\n    resetLine: function resetLine() {\n      this.superQueryFlag = false;\n      this.queryParamsModel = [];\n      this.addNewLine();\n    },\n    handleDel: function handleDel(index) {\n      this.queryParamsModel.splice(index, 1);\n    },\n    handleSelected: function handleSelected(node, item) {\n      var _node$dataRef = node.dataRef,\n        type = _node$dataRef.type,\n        options = _node$dataRef.options,\n        dictCode = _node$dataRef.dictCode,\n        dictTable = _node$dataRef.dictTable,\n        customReturnField = _node$dataRef.customReturnField,\n        popup = _node$dataRef.popup;\n      item['type'] = type;\n      item['options'] = options;\n      item['dictCode'] = dictCode;\n      item['dictTable'] = dictTable;\n      item['customReturnField'] = customReturnField;\n      if (popup) {\n        item['popup'] = popup;\n      }\n      this.$set(item, 'val', undefined);\n    },\n    handleOpen: function handleOpen() {\n      this.show();\n    },\n    handleReset: function handleReset() {\n      this.resetLine();\n      this.emitCallback();\n    },\n    handleSave: function handleSave() {\n      var queryParams = this.removeEmptyObject(this.queryParamsModel);\n      if (this.isNullArray(queryParams)) {\n        this.$message.warning('空条件不能保存');\n      } else {\n        this.prompt.value = '';\n        this.prompt.visible = true;\n      }\n    },\n    handlePromptOk: function handlePromptOk() {\n      var _this2 = this;\n      var value = this.prompt.value;\n      if (!value) {\n        this.$message.warning('保存名称不能为空');\n        return;\n      }\n      // 取出查询条件\n      var records = this.removeEmptyObject(this.queryParamsModel);\n      // 判断有没有重名的\n      var filterList = this.saveTreeData.filter(function (i) {\n        return i.originTitle === value;\n      });\n      if (filterList.length > 0) {\n        this.$confirm({\n          content: \"\".concat(value, \" \\u5DF2\\u5B58\\u5728\\uFF0C\\u662F\\u5426\\u8986\\u76D6\\uFF1F\"),\n          onOk: function onOk() {\n            _this2.prompt.visible = false;\n            filterList[0].records = records;\n            _this2.saveToLocalStore();\n            _this2.$message.success('保存成功');\n          }\n        });\n      } else {\n        // 没有重名的，直接添加\n        this.prompt.visible = false;\n        // 添加到树列表中\n        this.saveTreeData.push(this.renderSaveTreeData({\n          title: value,\n          matchType: this.matchType,\n          records: records\n        }));\n        // 保存到 LocalStore\n        this.saveToLocalStore();\n        this.$message.success('保存成功');\n      }\n    },\n    handleTreeSelect: function handleTreeSelect(idx, event) {\n      if (event.selectedNodes[0]) {\n        var _event$selectedNodes$ = event.selectedNodes[0].data.props,\n          matchType = _event$selectedNodes$.matchType,\n          records = _event$selectedNodes$.records;\n        // 将保存的matchType取出，兼容旧数据，如果没有保存就还是使用原来的\n        this.matchType = matchType || this.matchType;\n        this.queryParamsModel = utils.cloneObject(records);\n      }\n    },\n    handleRemoveSaveTreeItem: function handleRemoveSaveTreeItem(event, vNode) {\n      var _this3 = this;\n      // 阻止事件冒泡\n      event.stopPropagation();\n      this.$confirm({\n        content: '是否删除当前查询？',\n        onOk: function onOk() {\n          var eventKey = vNode.eventKey;\n          _this3.saveTreeData.splice(Number.parseInt(eventKey.substring(2)), 1);\n          _this3.saveToLocalStore();\n        }\n      });\n    },\n    // 将查询保存到 LocalStore 里\n    saveToLocalStore: function saveToLocalStore() {\n      var saveValue = this.saveTreeData.map(function (_ref) {\n        var originTitle = _ref.originTitle,\n          matchType = _ref.matchType,\n          records = _ref.records;\n        return {\n          title: originTitle,\n          matchType: matchType,\n          records: records\n        };\n      });\n      this.$ls.set(this.fullSaveCode, saveValue);\n    },\n    isNullArray: function isNullArray(array) {\n      //判断是不是空数组对象\n      if (!array || array.length === 0) {\n        return true;\n      }\n      if (array.length === 1) {\n        var obj = array[0];\n        if (!obj.field || obj.val == null || obj.val === '' || !obj.rule) {\n          return true;\n        }\n      }\n      return false;\n    },\n    // 去掉数组中的空对象\n    removeEmptyObject: function removeEmptyObject(arr) {\n      var array = utils.cloneObject(arr);\n      for (var i = 0; i < array.length; i++) {\n        var item = array[i];\n        if (item == null || Object.keys(item).length <= 0) {\n          array.splice(i--, 1);\n        } else {\n          if (Array.isArray(item.options)) {\n            // 如果有字典属性，就不需要保存 options 了\n            if (item.dictCode) {\n              // 去掉特殊属性\n              delete item.options;\n            }\n          }\n        }\n      }\n      return array;\n    },\n    /** 渲染保存查询条件的 title（加个删除按钮） */renderSaveTreeData: function renderSaveTreeData(item) {\n      var _this4 = this;\n      var h = this.$createElement;\n      item.icon = this.treeIcon;\n      item.originTitle = item['title'];\n      item.title = function (fn, vNode) {\n        var originTitle = vNode.dataRef.originTitle;\n        return h(\"div\", {\n          \"class\": \"j-history-tree-title\"\n        }, [h(\"span\", [originTitle]), h(\"div\", {\n          \"class\": \"j-history-tree-title-closer\",\n          \"on\": {\n            \"click\": function click(e) {\n              return _this4.handleRemoveSaveTreeItem(e, vNode);\n            }\n          }\n        }, [h(\"a-icon\", {\n          \"attrs\": {\n            \"type\": \"close-circle\"\n          }\n        })])]);\n      };\n      return item;\n    },\n    /** 判断是否允许多选 */allowMultiple: function allowMultiple(item) {\n      return item.rule === 'in';\n    },\n    handleRuleChange: function handleRuleChange(item, newValue) {\n      var oldValue = item.rule;\n      this.$set(item, 'rule', newValue);\n      // 上一个规则是否是 in，且type是字典或下拉\n      if (oldValue === 'in') {\n        if (item.dictCode || item.options instanceof Array) {\n          var value = item.val;\n          if (typeof item.val === 'string') {\n            value = item.val.split(',')[0];\n          } else if (Array.isArray(item.val)) {\n            value = item.val[0];\n          }\n          this.$set(item, 'val', value);\n        }\n      }\n    },\n    handleChangeJPopup: function handleChangeJPopup(item, e, values) {\n      item.val = values[item.popup['destFields']];\n    }\n  }\n};", {"version": 3, "names": ["moment", "utils", "mixinDevice", "JDate", "JSelectDepart", "JSelectMultiUser", "JMultiSelectTag", "JAreaLinkage", "name", "mixins", "components", "props", "fieldList", "type", "Array", "required", "callback", "String", "default", "loading", "Boolean", "saveCode", "data", "h", "$createElement", "fieldTreeData", "prompt", "visible", "value", "queryParamsModel", "treeIcon", "saveTreeData", "saveCodeBefore", "matchType", "superQueryFlag", "computed", "izMobile", "device", "tooltipProps", "fullSaveCode", "$route", "fullPath", "watch", "immediate", "handler", "_this", "list", "$ls", "get", "map", "i", "renderSaveTreeData", "deep", "val", "mainData", "subData", "for<PERSON>ach", "item", "_objectSpread", "label", "text", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "disabled", "selectable", "item2", "child", "push", "concat", "methods", "show", "length", "resetLine", "handleOk", "isNullArray", "event", "params", "removeEmptyObject", "emitCallback", "$message", "warn", "arguments", "undefined", "_event$params", "_event$matchType", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "param", "isArray", "join", "err", "e", "f", "console", "debug", "$emit", "handleCancel", "close", "handleAdd", "addNewLine", "rule", "handleDel", "index", "splice", "handleSelected", "node", "_node$dataRef", "dataRef", "options", "dictCode", "dictTable", "customReturnField", "popup", "$set", "handleOpen", "handleReset", "handleSave", "queryParams", "warning", "handlePromptOk", "_this2", "records", "filterList", "filter", "originTitle", "$confirm", "content", "onOk", "saveToLocalStore", "success", "title", "handleTreeSelect", "idx", "selectedNodes", "_event$selectedNodes$", "cloneObject", "handleRemoveSaveTreeItem", "vNode", "_this3", "stopPropagation", "eventKey", "Number", "parseInt", "substring", "saveValue", "_ref", "set", "array", "obj", "field", "arr", "Object", "keys", "_this4", "icon", "fn", "click", "allowMultiple", "handleRuleChange", "newValue", "oldValue", "split", "handleChangeJPopup", "values"], "sources": ["src/components/jeecg/JSuperQuery.vue"], "sourcesContent": ["<template>\n<div class=\"j-super-query-box\">\n\n  <slot name=\"button\" :isActive=\"superQueryFlag\" :isMobile=\"izMobile\" :open=\"handleOpen\" :reset=\"handleReset\">\n    <a-tooltip v-if=\"superQueryFlag\" v-bind=\"tooltipProps\" :mouseLeaveDelay=\"0.2\">\n      <!-- begin 不知道为什么不加上这段代码就无法生效 -->\n      <span v-show=\"false\">{{tooltipProps}}</span>\n      <!-- end 不知道为什么不加上这段代码就无法生效 -->\n      <template slot=\"title\">\n        <span>已有高级查询条件生效</span>\n        <a-divider type=\"vertical\"/>\n        <a @click=\"handleReset\">清空</a>\n      </template>\n      <a-button-group>\n        <a-button type=\"primary\" @click=\"handleOpen\">\n          <a-icon type=\"appstore\" theme=\"twoTone\" spin/>\n          <span>高级查询</span>\n        </a-button>\n        <a-button v-if=\"izMobile\" type=\"primary\" icon=\"delete\" @click=\"handleReset\"/>\n      </a-button-group>\n    </a-tooltip>\n    <a-button v-else type=\"primary\" icon=\"filter\" @click=\"handleOpen\">高级查询</a-button>\n  </slot>\n\n  <j-modal\n    title=\"高级查询构造器\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    @cancel=\"handleCancel\"\n    :mask=\"false\"\n    :fullscreen=\"izMobile\"\n    class=\"j-super-query-modal\"\n    style=\"top:5%;max-height: 95%;\"\n  >\n\n    <template slot=\"footer\">\n      <div style=\"float: left\">\n        <a-button :loading=\"loading\" @click=\"handleReset\">重置</a-button>\n        <a-button :loading=\"loading\" @click=\"handleSave\">保存查询条件</a-button>\n      </div>\n      <a-button :loading=\"loading\" @click=\"handleCancel\">关闭</a-button>\n      <a-button :loading=\"loading\" type=\"primary\" @click=\"handleOk\">查询</a-button>\n    </template>\n\n    <a-spin :spinning=\"loading\">\n      <a-row>\n        <a-col :sm=\"24\" :md=\"24-5\">\n\n          <a-empty v-if=\"queryParamsModel.length === 0\" style=\"margin-bottom: 12px;\">\n            <div slot=\"description\">\n              <span>没有任何查询条件</span>\n              <a-divider type=\"vertical\"/>\n              <a @click=\"handleAdd\">点击新增</a>\n            </div>\n          </a-empty>\n\n          <a-form v-else layout=\"inline\">\n\n            <a-row style=\"margin-bottom: 12px;\">\n              <a-col :md=\"12\" :xs=\"24\">\n                <a-form-item label=\"过滤条件匹配\" :labelCol=\"{md: 6,xs:24}\" :wrapperCol=\"{md: 18,xs:24}\" style=\"width: 100%;\">\n                  <a-select v-model=\"matchType\" :getPopupContainer=\"node=>node.parentNode\" style=\"width: 100%;\">\n                    <a-select-option value=\"and\">AND（所有条件都要求匹配）</a-select-option>\n                    <a-select-option value=\"or\">OR（条件中的任意一个匹配）</a-select-option>\n                  </a-select>\n                </a-form-item>\n              </a-col>\n            </a-row>\n\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in queryParamsModel\" :key=\"index\">\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-tree-select\n                  showSearch\n                  v-model=\"item.field\"\n                  :treeData=\"fieldTreeData\"\n                  :dropdownStyle=\"{ maxHeight: '400px', overflow: 'auto' }\"\n                  placeholder=\"选择查询字段\"\n                  allowClear\n                  treeDefaultExpandAll\n                  :getPopupContainer=\"node=>node.parentNode\"\n                  style=\"width: 100%\"\n                  @select=\"(val,option)=>handleSelected(option,item)\"\n                >\n                </a-tree-select>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-select placeholder=\"匹配规则\" :value=\"item.rule\" :getPopupContainer=\"node=>node.parentNode\" @change=\"handleRuleChange(item,$event)\">\n                  <a-select-option value=\"eq\">等于</a-select-option>\n                  <a-select-option value=\"like\">包含</a-select-option>\n                  <a-select-option value=\"right_like\">以..开始</a-select-option>\n                  <a-select-option value=\"left_like\">以..结尾</a-select-option>\n                  <a-select-option value=\"in\">在...中</a-select-option>\n                  <a-select-option value=\"ne\">不等于</a-select-option>\n                  <a-select-option value=\"gt\">大于</a-select-option>\n                  <a-select-option value=\"ge\">大于等于</a-select-option>\n                  <a-select-option value=\"lt\">小于</a-select-option>\n                  <a-select-option value=\"le\">小于等于</a-select-option>\n                </a-select>\n              </a-col>\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <template v-if=\"item.dictCode\">\n                  <template v-if=\"item.type === 'table-dict'\">\n                    <j-popup\n                      v-model=\"item.val\"\n                      :code=\"item.dictTable\"\n                      :field=\"item.dictCode\"\n                      :orgFields=\"item.dictCode\"\n                      :destFields=\"item.dictCode\"\n                    ></j-popup>\n                  </template>\n                  <template v-else>\n                    <j-multi-select-tag v-show=\"allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                    <j-dict-select-tag v-show=\"!allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                  </template>\n                </template>\n                <j-popup v-else-if=\"item.type === 'popup'\" :value=\"item.val\" v-bind=\"item.popup\" group-id=\"superQuery\" @input=\"(e,v)=>handleChangeJPopup(item,e,v)\"/>\n                <j-select-multi-user\n                  v-else-if=\"item.type === 'select-user' || item.type === 'sel_user'\"\n                  v-model=\"item.val\"\n                  :buttons=\"false\"\n                  :multiple=\"false\"\n                  placeholder=\"请选择用户\"\n                  :returnKeys=\"['id', item.customReturnField || 'username']\"\n                />\n                <j-select-depart\n                  v-else-if=\"item.type === 'select-depart' || item.type === 'sel_depart'\"\n                  v-model=\"item.val\"\n                  :multi=\"false\"\n                  placeholder=\"请选择部门\"\n                  :customReturnField=\"item.customReturnField || 'id'\"\n                />\n                <a-select\n                  v-else-if=\"item.options instanceof Array\"\n                  v-model=\"item.val\"\n                  :options=\"item.options\"\n                  allowClear\n                  placeholder=\"请选择\"\n                  :mode=\"allowMultiple(item)?'multiple':''\"\n                />\n                <j-area-linkage v-model=\"item.val\" v-else-if=\"item.type==='area-linkage' || item.type==='pca'\" style=\"width: 100%\"/>\n                <j-date v-else-if=\" item.type=='date' \" v-model=\"item.val\" placeholder=\"请选择日期\" style=\"width: 100%\"></j-date>\n                <j-date v-else-if=\" item.type=='datetime' \" v-model=\"item.val\" placeholder=\"请选择时间\" :show-time=\"true\" date-format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\"></j-date>\n                <a-time-picker v-else-if=\"item.type==='time'\" :value=\"item.val ? moment(item.val,'HH:mm:ss') : null\" format=\"HH:mm:ss\" style=\"width: 100%\" @change=\"(time,value)=>item.val=value\"/>\n                <a-input-number v-else-if=\" item.type=='int'||item.type=='number' \" style=\"width: 100%\" placeholder=\"请输入数值\" v-model=\"item.val\"/>\n                <a-input v-else v-model=\"item.val\" placeholder=\"请输入值\"/>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"0\" style=\"margin-bottom: 12px;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n              <a-col :md=\"0\" :xs=\"24\" style=\"margin-bottom: 12px;text-align: right;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n            </a-row>\n\n          </a-form>\n        </a-col>\n        <a-col :sm=\"24\" :md=\"5\">\n          <!-- 查询记录 -->\n\n          <a-card class=\"j-super-query-history-card\" :bordered=\"true\">\n            <div slot=\"title\">\n              保存的查询\n            </div>\n\n            <a-empty v-if=\"saveTreeData.length === 0\" class=\"j-super-query-history-empty\" description=\"没有保存任何查询\"/>\n            <a-tree\n              v-else\n              class=\"j-super-query-history-tree\"\n              showIcon\n              :treeData=\"saveTreeData\"\n              :selectedKeys=\"[]\"\n              @select=\"handleTreeSelect\"\n            >\n            </a-tree>\n          </a-card>\n\n\n        </a-col>\n      </a-row>\n\n\n    </a-spin>\n\n    <a-modal title=\"请输入保存的名称\" :visible=\"prompt.visible\" @cancel=\"prompt.visible=false\" @ok=\"handlePromptOk\">\n      <a-input v-model=\"prompt.value\"></a-input>\n    </a-modal>\n\n  </j-modal>\n</div>\n</template>\n\n<script>\n  import moment from 'moment'\n  import * as utils from '@/utils/util'\n  import { mixinDevice } from '@/utils/mixin'\n  import JDate from '@/components/jeecg/JDate.vue'\n  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'\n  import JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser'\n  import JMultiSelectTag from '@/components/dict/JMultiSelectTag'\n  import JAreaLinkage from '@comp/jeecg/JAreaLinkage'\n\n  export default {\n    name: 'JSuperQuery',\n    mixins: [mixinDevice],\n    components: { JAreaLinkage, JMultiSelectTag, JDate, JSelectDepart, JSelectMultiUser },\n    props: {\n      /*\n       fieldList: [{\n          value:'',\n          text:'',\n          type:'',\n          dictCode:'' // 只要 dictCode 有值，无论 type 是什么，都显示为字典下拉框\n       }]\n       type:date datetime int number string\n      * */\n      fieldList: {\n        type: Array,\n        required: true\n      },\n      /*\n      * 这个回调函数接收一个数组参数 即查询条件\n      * */\n      callback: {\n        type: String,\n        required: false,\n        default: 'handleSuperQuery'\n      },\n\n      // 当前是否在加载中\n      loading: {\n        type: Boolean,\n        default: false\n      },\n\n      // 保存查询条件的唯一 code，通过该 code 区分\n      // 默认为 null，代表以当前路由全路径为区分Code\n      saveCode: {\n        type: String,\n        default: null\n      }\n\n    },\n    data() {\n      return {\n        moment,\n        fieldTreeData: [],\n\n        prompt: {\n          visible: false,\n          value: ''\n        },\n\n        visible: false,\n        queryParamsModel: [],\n        treeIcon: <a-icon type=\"file-text\"/>,\n        // 保存查询条件的treeData\n        saveTreeData: [],\n        // 保存查询条件的前缀名\n        saveCodeBefore: 'JSuperQuerySaved_',\n        // 查询类型，过滤条件匹配（and、or）\n        matchType: 'and',\n        superQueryFlag: false,\n      }\n    },\n    computed: {\n      izMobile() {\n        return this.device === 'mobile'\n      },\n      tooltipProps() {\n        return this.izMobile ? { visible: false } : {}\n      },\n      fullSaveCode() {\n        let saveCode = this.saveCode\n        if (saveCode == null || saveCode === '') {\n          saveCode = this.$route.fullPath\n        }\n        return this.saveCodeBefore + saveCode\n      },\n    },\n    watch: {\n      // 当 saveCode 变化时，重新查询已保存的条件\n      fullSaveCode: {\n        immediate: true,\n        handler() {\n          let list = this.$ls.get(this.fullSaveCode)\n          if (list instanceof Array) {\n            this.saveTreeData = list.map(i => this.renderSaveTreeData(i))\n          }\n        }\n      },\n      fieldList: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          let mainData = [], subData = []\n          val.forEach(item => {\n            let data = { ...item }\n            data.label = data.label || data.text\n            let hasChildren = (data.children instanceof Array)\n            data.disabled = hasChildren\n            data.selectable = !hasChildren\n            if (hasChildren) {\n              data.children = data.children.map(item2 => {\n                let child = { ...item2 }\n                child.label = child.label || child.text\n                child.label = data.label + '-' + child.label\n                child.value = data.value + ',' + child.value\n                child.val = ''\n                return child\n              })\n              data.val = ''\n              subData.push(data)\n            } else {\n              mainData.push(data)\n            }\n          })\n          this.fieldTreeData = mainData.concat(subData)\n        }\n      }\n    },\n\n    methods: {\n      show() {\n        if (!this.queryParamsModel || this.queryParamsModel.length === 0) {\n          this.resetLine()\n        }\n        this.visible = true\n      },\n      handleOk() {\n        if (!this.isNullArray(this.queryParamsModel)) {\n          let event = {\n            matchType: this.matchType,\n            params: this.removeEmptyObject(this.queryParamsModel)\n          }\n          // 移动端模式下关闭弹窗\n          if (this.izMobile) {\n            this.visible = false\n          }\n          this.emitCallback(event)\n        } else {\n          this.$message.warn(\"不能查询空条件\")\n        }\n      },\n      emitCallback(event = {}) {\n        let { params = [], matchType = this.matchType } = event\n        this.superQueryFlag = (params && params.length > 0)\n        for (let param of params) {\n          if (Array.isArray(param.val)) {\n            param.val = param.val.join(',')\n          }\n        }\n        console.debug('---高级查询参数--->', { params, matchType })\n        this.$emit(this.callback, params, matchType)\n      },\n      handleCancel() {\n        this.close()\n      },\n      close() {\n        this.$emit('close')\n        this.visible = false\n      },\n      handleAdd() {\n        this.addNewLine()\n      },\n      addNewLine() {\n        this.queryParamsModel.push({ rule: 'eq' })\n      },\n      resetLine() {\n        this.superQueryFlag = false\n        this.queryParamsModel = []\n        this.addNewLine()\n      },\n      handleDel(index) {\n        this.queryParamsModel.splice(index, 1)\n      },\n      handleSelected(node, item) {\n        let { type, options, dictCode, dictTable, customReturnField, popup } = node.dataRef\n        item['type'] = type\n        item['options'] = options\n        item['dictCode'] = dictCode\n        item['dictTable'] = dictTable\n        item['customReturnField'] = customReturnField\n        if (popup) {\n          item['popup'] = popup\n        }\n        this.$set(item, 'val', undefined)\n      },\n      handleOpen() {\n        this.show()\n      },\n      handleReset() {\n        this.resetLine()\n        this.emitCallback()\n      },\n      handleSave() {\n        let queryParams = this.removeEmptyObject(this.queryParamsModel)\n        if (this.isNullArray(queryParams)) {\n          this.$message.warning('空条件不能保存')\n        } else {\n          this.prompt.value = ''\n          this.prompt.visible = true\n        }\n      },\n      handlePromptOk() {\n        let { value } = this.prompt\n        if(!value){\n          this.$message.warning('保存名称不能为空')\n          return\n        }\n        // 取出查询条件\n        let records = this.removeEmptyObject(this.queryParamsModel)\n        // 判断有没有重名的\n        let filterList = this.saveTreeData.filter(i => i.originTitle === value)\n        if (filterList.length > 0) {\n          this.$confirm({\n            content: `${value} 已存在，是否覆盖？`,\n            onOk: () => {\n              this.prompt.visible = false\n              filterList[0].records = records\n              this.saveToLocalStore()\n              this.$message.success('保存成功')\n            }\n          })\n        } else {\n          // 没有重名的，直接添加\n          this.prompt.visible = false\n          // 添加到树列表中\n          this.saveTreeData.push(this.renderSaveTreeData({\n            title: value,\n            matchType: this.matchType,\n            records: records\n          }))\n          // 保存到 LocalStore\n          this.saveToLocalStore()\n          this.$message.success('保存成功')\n        }\n      },\n      handleTreeSelect(idx, event) {\n        if (event.selectedNodes[0]) {\n          let { matchType, records } = event.selectedNodes[0].data.props\n          // 将保存的matchType取出，兼容旧数据，如果没有保存就还是使用原来的\n          this.matchType = matchType || this.matchType\n          this.queryParamsModel = utils.cloneObject(records)\n        }\n      },\n      handleRemoveSaveTreeItem(event, vNode) {\n        // 阻止事件冒泡\n        event.stopPropagation()\n\n        this.$confirm({\n          content: '是否删除当前查询？',\n          onOk: () => {\n            let { eventKey } = vNode\n            this.saveTreeData.splice(Number.parseInt(eventKey.substring(2)), 1)\n            this.saveToLocalStore()\n          },\n        })\n      },\n\n      // 将查询保存到 LocalStore 里\n      saveToLocalStore() {\n        let saveValue = this.saveTreeData.map(({ originTitle, matchType, records }) => ({ title: originTitle, matchType, records }))\n        this.$ls.set(this.fullSaveCode, saveValue)\n      },\n\n      isNullArray(array) {\n        //判断是不是空数组对象\n        if (!array || array.length === 0) {\n          return true\n        }\n        if (array.length === 1) {\n          let obj = array[0]\n          if (!obj.field || (obj.val == null || obj.val === '') || !obj.rule) {\n            return true\n          }\n        }\n        return false\n      },\n      // 去掉数组中的空对象\n      removeEmptyObject(arr) {\n        let array = utils.cloneObject(arr)\n        for (let i = 0; i < array.length; i++) {\n          let item = array[i]\n          if (item == null || Object.keys(item).length <= 0) {\n            array.splice(i--, 1)\n          } else {\n            if (Array.isArray(item.options)) {\n              // 如果有字典属性，就不需要保存 options 了\n              if (item.dictCode) {\n                // 去掉特殊属性\n                delete item.options\n              }\n            }\n          }\n        }\n        return array\n      },\n\n      /** 渲染保存查询条件的 title（加个删除按钮） */\n      renderSaveTreeData(item) {\n        item.icon = this.treeIcon\n        item.originTitle = item['title']\n        item.title = (fn, vNode) => {\n          let { originTitle } = vNode.dataRef\n          return (\n            <div class=\"j-history-tree-title\">\n              <span>{originTitle}</span>\n\n              <div class=\"j-history-tree-title-closer\" onClick={e => this.handleRemoveSaveTreeItem(e, vNode)}>\n                <a-icon type=\"close-circle\"/>\n              </div>\n            </div>\n          )\n        }\n        return item\n      },\n\n      /** 判断是否允许多选 */\n      allowMultiple(item) {\n        return item.rule === 'in'\n      },\n\n      handleRuleChange(item, newValue) {\n        let oldValue = item.rule\n        this.$set(item, 'rule', newValue)\n        // 上一个规则是否是 in，且type是字典或下拉\n        if (oldValue === 'in') {\n          if (item.dictCode || item.options instanceof Array) {\n            let value = item.val\n            if (typeof item.val === 'string') {\n              value = item.val.split(',')[0]\n            } else if (Array.isArray(item.val)) {\n              value = item.val[0]\n            }\n            this.$set(item, 'val', value)\n          }\n        }\n      },\n\n      handleChangeJPopup(item, e, values) {\n        item.val = values[item.popup['destFields']]\n      },\n\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n\n  .j-super-query-box {\n    display: inline-block;\n  }\n\n  .j-super-query-modal {\n\n    .j-super-query-history-card {\n      /deep/ .ant-card-body,\n      /deep/ .ant-card-head-title {\n        padding: 0;\n      }\n\n      /deep/ .ant-card-head {\n        padding: 4px 8px;\n        min-height: initial;\n      }\n    }\n\n    .j-super-query-history-empty {\n      /deep/ .ant-empty-image {\n        height: 80px;\n        line-height: 80px;\n        margin-bottom: 0;\n      }\n\n      /deep/ img {\n        width: 80px;\n        height: 65px;\n      }\n\n      /deep/ .ant-empty-description {\n        color: #afafaf;\n        margin: 8px 0;\n      }\n    }\n\n    .j-super-query-history-tree {\n\n      .j-history-tree-title {\n        width: calc(100% - 24px);\n        position: relative;\n        display: inline-block;\n\n        &-closer {\n          color: #999999;\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 24px;\n          height: 24px;\n          text-align: center;\n          opacity: 0;\n          transition: opacity 0.3s, color 0.3s;\n\n          &:hover {\n            color: #666666;\n          }\n\n          &:active {\n            color: #333333;\n          }\n        }\n\n        &:hover {\n          .j-history-tree-title-closer {\n            opacity: 1;\n          }\n        }\n\n      }\n\n      /deep/ .ant-tree-switcher {\n        display: none;\n      }\n\n      /deep/ .ant-tree-node-content-wrapper {\n        width: 100%;\n      }\n    }\n\n  }\n\n</style>"], "mappings": ";;;;;;;;;AAwMA,OAAAA,MAAA;AACA,YAAAC,KAAA;AACA,SAAAC,WAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,eAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAP,WAAA;EACAQ,UAAA;IAAAH,YAAA,EAAAA,YAAA;IAAAD,eAAA,EAAAA,eAAA;IAAAH,KAAA,EAAAA,KAAA;IAAAC,aAAA,EAAAA,aAAA;IAAAC,gBAAA,EAAAA;EAAA;EACAM,KAAA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACA;AACA;AACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;MACAG,OAAA;IACA;IAEA;IACAC,OAAA;MACAN,IAAA,EAAAO,OAAA;MACAF,OAAA;IACA;IAEA;IACA;IACAG,QAAA;MACAR,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;EAEA;EACAI,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA;MACAxB,MAAA,EAAAA,MAAA;MACAyB,aAAA;MAEAC,MAAA;QACAC,OAAA;QACAC,KAAA;MACA;MAEAD,OAAA;MACAE,gBAAA;MACAC,QAAA,EAAAP,CAAA;QAAA;UAAA;QAAA;MAAA;MACA;MACAQ,YAAA;MACA;MACAC,cAAA;MACA;MACAC,SAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAF,QAAA;QAAAT,OAAA;MAAA;IACA;IACAY,YAAA,WAAAA,aAAA;MACA,IAAAlB,QAAA,QAAAA,QAAA;MACA,IAAAA,QAAA,YAAAA,QAAA;QACAA,QAAA,QAAAmB,MAAA,CAAAC,QAAA;MACA;MACA,YAAAT,cAAA,GAAAX,QAAA;IACA;EACA;EACAqB,KAAA;IACA;IACAH,YAAA;MACAI,SAAA;MACAC,OAAA,WAAAA,QAAA;QAAA,IAAAC,KAAA;QACA,IAAAC,IAAA,QAAAC,GAAA,CAAAC,GAAA,MAAAT,YAAA;QACA,IAAAO,IAAA,YAAAhC,KAAA;UACA,KAAAiB,YAAA,GAAAe,IAAA,CAAAG,GAAA,WAAAC,CAAA;YAAA,OAAAL,KAAA,CAAAM,kBAAA,CAAAD,CAAA;UAAA;QACA;MACA;IACA;IACAtC,SAAA;MACAwC,IAAA;MACAT,SAAA;MACAC,OAAA,WAAAA,QAAAS,GAAA;QACA,IAAAC,QAAA;UAAAC,OAAA;QACAF,GAAA,CAAAG,OAAA,WAAAC,IAAA;UACA,IAAAnC,IAAA,GAAAoC,aAAA,KAAAD,IAAA;UACAnC,IAAA,CAAAqC,KAAA,GAAArC,IAAA,CAAAqC,KAAA,IAAArC,IAAA,CAAAsC,IAAA;UACA,IAAAC,WAAA,GAAAvC,IAAA,CAAAwC,QAAA,YAAAhD,KAAA;UACAQ,IAAA,CAAAyC,QAAA,GAAAF,WAAA;UACAvC,IAAA,CAAA0C,UAAA,IAAAH,WAAA;UACA,IAAAA,WAAA;YACAvC,IAAA,CAAAwC,QAAA,GAAAxC,IAAA,CAAAwC,QAAA,CAAAb,GAAA,WAAAgB,KAAA;cACA,IAAAC,KAAA,GAAAR,aAAA,KAAAO,KAAA;cACAC,KAAA,CAAAP,KAAA,GAAAO,KAAA,CAAAP,KAAA,IAAAO,KAAA,CAAAN,IAAA;cACAM,KAAA,CAAAP,KAAA,GAAArC,IAAA,CAAAqC,KAAA,SAAAO,KAAA,CAAAP,KAAA;cACAO,KAAA,CAAAtC,KAAA,GAAAN,IAAA,CAAAM,KAAA,SAAAsC,KAAA,CAAAtC,KAAA;cACAsC,KAAA,CAAAb,GAAA;cACA,OAAAa,KAAA;YACA;YACA5C,IAAA,CAAA+B,GAAA;YACAE,OAAA,CAAAY,IAAA,CAAA7C,IAAA;UACA;YACAgC,QAAA,CAAAa,IAAA,CAAA7C,IAAA;UACA;QACA;QACA,KAAAG,aAAA,GAAA6B,QAAA,CAAAc,MAAA,CAAAb,OAAA;MACA;IACA;EACA;EAEAc,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,UAAAzC,gBAAA,SAAAA,gBAAA,CAAA0C,MAAA;QACA,KAAAC,SAAA;MACA;MACA,KAAA7C,OAAA;IACA;IACA8C,QAAA,WAAAA,SAAA;MACA,UAAAC,WAAA,MAAA7C,gBAAA;QACA,IAAA8C,KAAA;UACA1C,SAAA,OAAAA,SAAA;UACA2C,MAAA,OAAAC,iBAAA,MAAAhD,gBAAA;QACA;QACA;QACA,SAAAO,QAAA;UACA,KAAAT,OAAA;QACA;QACA,KAAAmD,YAAA,CAAAH,KAAA;MACA;QACA,KAAAI,QAAA,CAAAC,IAAA;MACA;IACA;IACAF,YAAA,WAAAA,aAAA;MAAA,IAAAH,KAAA,GAAAM,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,aAAA,GAAAR,KAAA,CAAAC,MAAA;QAAAA,MAAA,GAAAO,aAAA,mBAAAA,aAAA;QAAAC,gBAAA,GAAAT,KAAA,CAAA1C,SAAA;QAAAA,SAAA,GAAAmD,gBAAA,mBAAAnD,SAAA,GAAAmD,gBAAA;MACA,KAAAlD,cAAA,GAAA0C,MAAA,IAAAA,MAAA,CAAAL,MAAA;MAAA,IAAAc,SAAA,GAAAC,0BAAA,CACAV,MAAA;QAAAW,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,KAAA,GAAAJ,KAAA,CAAA3D,KAAA;UACA,IAAAd,KAAA,CAAA8E,OAAA,CAAAD,KAAA,CAAAtC,GAAA;YACAsC,KAAA,CAAAtC,GAAA,GAAAsC,KAAA,CAAAtC,GAAA,CAAAwC,IAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;MAAA;QAAAT,SAAA,CAAAW,CAAA;MAAA;MACAC,OAAA,CAAAC,KAAA;QAAAtB,MAAA,EAAAA,MAAA;QAAA3C,SAAA,EAAAA;MAAA;MACA,KAAAkE,KAAA,MAAAnF,QAAA,EAAA4D,MAAA,EAAA3C,SAAA;IACA;IACAmE,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAF,KAAA;MACA,KAAAxE,OAAA;IACA;IACA2E,SAAA,WAAAA,UAAA;MACA,KAAAC,UAAA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA,KAAA1E,gBAAA,CAAAsC,IAAA;QAAAqC,IAAA;MAAA;IACA;IACAhC,SAAA,WAAAA,UAAA;MACA,KAAAtC,cAAA;MACA,KAAAL,gBAAA;MACA,KAAA0E,UAAA;IACA;IACAE,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAA7E,gBAAA,CAAA8E,MAAA,CAAAD,KAAA;IACA;IACAE,cAAA,WAAAA,eAAAC,IAAA,EAAApD,IAAA;MACA,IAAAqD,aAAA,GAAAD,IAAA,CAAAE,OAAA;QAAAlG,IAAA,GAAAiG,aAAA,CAAAjG,IAAA;QAAAmG,OAAA,GAAAF,aAAA,CAAAE,OAAA;QAAAC,QAAA,GAAAH,aAAA,CAAAG,QAAA;QAAAC,SAAA,GAAAJ,aAAA,CAAAI,SAAA;QAAAC,iBAAA,GAAAL,aAAA,CAAAK,iBAAA;QAAAC,KAAA,GAAAN,aAAA,CAAAM,KAAA;MACA3D,IAAA,WAAA5C,IAAA;MACA4C,IAAA,cAAAuD,OAAA;MACAvD,IAAA,eAAAwD,QAAA;MACAxD,IAAA,gBAAAyD,SAAA;MACAzD,IAAA,wBAAA0D,iBAAA;MACA,IAAAC,KAAA;QACA3D,IAAA,YAAA2D,KAAA;MACA;MACA,KAAAC,IAAA,CAAA5D,IAAA,SAAAyB,SAAA;IACA;IACAoC,UAAA,WAAAA,WAAA;MACA,KAAAhD,IAAA;IACA;IACAiD,WAAA,WAAAA,YAAA;MACA,KAAA/C,SAAA;MACA,KAAAM,YAAA;IACA;IACA0C,UAAA,WAAAA,WAAA;MACA,IAAAC,WAAA,QAAA5C,iBAAA,MAAAhD,gBAAA;MACA,SAAA6C,WAAA,CAAA+C,WAAA;QACA,KAAA1C,QAAA,CAAA2C,OAAA;MACA;QACA,KAAAhG,MAAA,CAAAE,KAAA;QACA,KAAAF,MAAA,CAAAC,OAAA;MACA;IACA;IACAgG,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAhG,KAAA,QAAAF,MAAA,CAAAE,KAAA;MACA,KAAAA,KAAA;QACA,KAAAmD,QAAA,CAAA2C,OAAA;QACA;MACA;MACA;MACA,IAAAG,OAAA,QAAAhD,iBAAA,MAAAhD,gBAAA;MACA;MACA,IAAAiG,UAAA,QAAA/F,YAAA,CAAAgG,MAAA,WAAA7E,CAAA;QAAA,OAAAA,CAAA,CAAA8E,WAAA,KAAApG,KAAA;MAAA;MACA,IAAAkG,UAAA,CAAAvD,MAAA;QACA,KAAA0D,QAAA;UACAC,OAAA,KAAA9D,MAAA,CAAAxC,KAAA;UACAuG,IAAA,WAAAA,KAAA;YACAP,MAAA,CAAAlG,MAAA,CAAAC,OAAA;YACAmG,UAAA,IAAAD,OAAA,GAAAA,OAAA;YACAD,MAAA,CAAAQ,gBAAA;YACAR,MAAA,CAAA7C,QAAA,CAAAsD,OAAA;UACA;QACA;MACA;QACA;QACA,KAAA3G,MAAA,CAAAC,OAAA;QACA;QACA,KAAAI,YAAA,CAAAoC,IAAA,MAAAhB,kBAAA;UACAmF,KAAA,EAAA1G,KAAA;UACAK,SAAA,OAAAA,SAAA;UACA4F,OAAA,EAAAA;QACA;QACA;QACA,KAAAO,gBAAA;QACA,KAAArD,QAAA,CAAAsD,OAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAAC,GAAA,EAAA7D,KAAA;MACA,IAAAA,KAAA,CAAA8D,aAAA;QACA,IAAAC,qBAAA,GAAA/D,KAAA,CAAA8D,aAAA,IAAAnH,IAAA,CAAAX,KAAA;UAAAsB,SAAA,GAAAyG,qBAAA,CAAAzG,SAAA;UAAA4F,OAAA,GAAAa,qBAAA,CAAAb,OAAA;QACA;QACA,KAAA5F,SAAA,GAAAA,SAAA,SAAAA,SAAA;QACA,KAAAJ,gBAAA,GAAA5B,KAAA,CAAA0I,WAAA,CAAAd,OAAA;MACA;IACA;IACAe,wBAAA,WAAAA,yBAAAjE,KAAA,EAAAkE,KAAA;MAAA,IAAAC,MAAA;MACA;MACAnE,KAAA,CAAAoE,eAAA;MAEA,KAAAd,QAAA;QACAC,OAAA;QACAC,IAAA,WAAAA,KAAA;UACA,IAAAa,QAAA,GAAAH,KAAA,CAAAG,QAAA;UACAF,MAAA,CAAA/G,YAAA,CAAA4E,MAAA,CAAAsC,MAAA,CAAAC,QAAA,CAAAF,QAAA,CAAAG,SAAA;UACAL,MAAA,CAAAV,gBAAA;QACA;MACA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MACA,IAAAgB,SAAA,QAAArH,YAAA,CAAAkB,GAAA,WAAAoG,IAAA;QAAA,IAAArB,WAAA,GAAAqB,IAAA,CAAArB,WAAA;UAAA/F,SAAA,GAAAoH,IAAA,CAAApH,SAAA;UAAA4F,OAAA,GAAAwB,IAAA,CAAAxB,OAAA;QAAA;UAAAS,KAAA,EAAAN,WAAA;UAAA/F,SAAA,EAAAA,SAAA;UAAA4F,OAAA,EAAAA;QAAA;MAAA;MACA,KAAA9E,GAAA,CAAAuG,GAAA,MAAA/G,YAAA,EAAA6G,SAAA;IACA;IAEA1E,WAAA,WAAAA,YAAA6E,KAAA;MACA;MACA,KAAAA,KAAA,IAAAA,KAAA,CAAAhF,MAAA;QACA;MACA;MACA,IAAAgF,KAAA,CAAAhF,MAAA;QACA,IAAAiF,GAAA,GAAAD,KAAA;QACA,KAAAC,GAAA,CAAAC,KAAA,IAAAD,GAAA,CAAAnG,GAAA,YAAAmG,GAAA,CAAAnG,GAAA,YAAAmG,GAAA,CAAAhD,IAAA;UACA;QACA;MACA;MACA;IACA;IACA;IACA3B,iBAAA,WAAAA,kBAAA6E,GAAA;MACA,IAAAH,KAAA,GAAAtJ,KAAA,CAAA0I,WAAA,CAAAe,GAAA;MACA,SAAAxG,CAAA,MAAAA,CAAA,GAAAqG,KAAA,CAAAhF,MAAA,EAAArB,CAAA;QACA,IAAAO,IAAA,GAAA8F,KAAA,CAAArG,CAAA;QACA,IAAAO,IAAA,YAAAkG,MAAA,CAAAC,IAAA,CAAAnG,IAAA,EAAAc,MAAA;UACAgF,KAAA,CAAA5C,MAAA,CAAAzD,CAAA;QACA;UACA,IAAApC,KAAA,CAAA8E,OAAA,CAAAnC,IAAA,CAAAuD,OAAA;YACA;YACA,IAAAvD,IAAA,CAAAwD,QAAA;cACA;cACA,OAAAxD,IAAA,CAAAuD,OAAA;YACA;UACA;QACA;MACA;MACA,OAAAuC,KAAA;IACA;IAEA,8BACApG,kBAAA,WAAAA,mBAAAM,IAAA;MAAA,IAAAoG,MAAA;MAAA,IAAAtI,CAAA,QAAAC,cAAA;MACAiC,IAAA,CAAAqG,IAAA,QAAAhI,QAAA;MACA2B,IAAA,CAAAuE,WAAA,GAAAvE,IAAA;MACAA,IAAA,CAAA6E,KAAA,aAAAyB,EAAA,EAAAlB,KAAA;QACA,IAAAb,WAAA,GAAAa,KAAA,CAAA9B,OAAA,CAAAiB,WAAA;QACA,OAAAzG,CAAA;UAAA,SACA;QAAA,IAAAA,CAAA,UACAyG,WAAA,IAAAzG,CAAA;UAAA,SAEA;UAAA;YAAA,kBAAAyI,MAAAjE,CAAA;cAAA,OAAA8D,MAAA,CAAAjB,wBAAA,CAAA7C,CAAA,EAAA8C,KAAA;YAAA;UAAA;QAAA,IAAAtH,CAAA;UAAA;YAAA,QACA;UAAA;QAAA;MAIA;MACA,OAAAkC,IAAA;IACA;IAEA,eACAwG,aAAA,WAAAA,cAAAxG,IAAA;MACA,OAAAA,IAAA,CAAA+C,IAAA;IACA;IAEA0D,gBAAA,WAAAA,iBAAAzG,IAAA,EAAA0G,QAAA;MACA,IAAAC,QAAA,GAAA3G,IAAA,CAAA+C,IAAA;MACA,KAAAa,IAAA,CAAA5D,IAAA,UAAA0G,QAAA;MACA;MACA,IAAAC,QAAA;QACA,IAAA3G,IAAA,CAAAwD,QAAA,IAAAxD,IAAA,CAAAuD,OAAA,YAAAlG,KAAA;UACA,IAAAc,KAAA,GAAA6B,IAAA,CAAAJ,GAAA;UACA,WAAAI,IAAA,CAAAJ,GAAA;YACAzB,KAAA,GAAA6B,IAAA,CAAAJ,GAAA,CAAAgH,KAAA;UACA,WAAAvJ,KAAA,CAAA8E,OAAA,CAAAnC,IAAA,CAAAJ,GAAA;YACAzB,KAAA,GAAA6B,IAAA,CAAAJ,GAAA;UACA;UACA,KAAAgE,IAAA,CAAA5D,IAAA,SAAA7B,KAAA;QACA;MACA;IACA;IAEA0I,kBAAA,WAAAA,mBAAA7G,IAAA,EAAAsC,CAAA,EAAAwE,MAAA;MACA9G,IAAA,CAAAJ,GAAA,GAAAkH,MAAA,CAAA9G,IAAA,CAAA2D,KAAA;IACA;EAEA;AACA", "ignoreList": []}]}