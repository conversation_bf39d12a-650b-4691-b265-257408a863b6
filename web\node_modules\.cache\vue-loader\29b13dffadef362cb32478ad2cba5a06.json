{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue?vue&type=template&id=2de1d165&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\PasswordSetting.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"password-setting\">\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\" class=\"password-form\">\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"旧密码\">\n        <a-input\n          type=\"password\"\n          placeholder=\"请输入旧密码\"\n          v-decorator=\"[ 'oldpassword', validatorRules.oldpassword]\"\n          class=\"password-input\" />\n      </a-form-item>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"新密码\">\n        <a-input\n          type=\"password\"\n          placeholder=\"请输入新密码\"\n          v-decorator=\"[ 'password', validatorRules.password]\"\n          class=\"password-input\" />\n      </a-form-item>\n\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"确认新密码\">\n        <a-input\n          type=\"password\"\n          @blur=\"handleConfirmBlur\"\n          placeholder=\"请确认新密码\"\n          v-decorator=\"[ 'confirmpassword', validatorRules.confirmpassword]\"\n          class=\"password-input\" />\n      </a-form-item>\n\n      <a-form-item :wrapperCol=\"{ offset: 5, span: 12 }\">\n        <div class=\"button-group\">\n          <a-button type=\"primary\" @click=\"handleSubmit\" :loading=\"confirmLoading\">\n            提交\n          </a-button>\n          <a-button @click=\"handleReset\" style=\"margin-left: 8px;\">\n            重置\n          </a-button>\n        </div>\n      </a-form-item>\n\n    </a-form>\n  </a-spin>\n</div>\n", null]}