{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue?vue&type=template&id=76bcb248&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-popover\", {\n    attrs: {\n      trigger: \"click\",\n      placement: \"bottomRight\",\n      overlayStyle: {\n        width: \"300px\"\n      }\n    }\n  }, [_c(\"template\", {\n    slot: \"content\"\n  }, [_c(\"a-spin\", {\n    attrs: {\n      spinning: _vm.loadding\n    }\n  }, [_c(\"a-tabs\", _vm._l(_vm.tabs, function (tab, k) {\n    return _c(\"a-tab-pane\", {\n      key: k,\n      attrs: {\n        tab: tab.title\n      }\n    });\n  }), 1)], 1)], 1), _c(\"span\", {\n    staticClass: \"header-notice\",\n    on: {\n      click: _vm.fetchNotice\n    }\n  }, [_c(\"a-badge\", {\n    attrs: {\n      count: \"12\"\n    }\n  }, [_c(\"a-icon\", {\n    staticStyle: {\n      \"font-size\": \"16px\",\n      padding: \"4px\"\n    },\n    attrs: {\n      type: \"bell\"\n    }\n  })], 1)], 1)], 2);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "trigger", "placement", "overlayStyle", "width", "slot", "spinning", "loadding", "_l", "tabs", "tab", "k", "key", "title", "staticClass", "on", "click", "fetchNotice", "count", "staticStyle", "padding", "type", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/page/SHeaderNotice.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-popover\",\n    {\n      attrs: {\n        trigger: \"click\",\n        placement: \"bottomRight\",\n        overlayStyle: { width: \"300px\" },\n      },\n    },\n    [\n      _c(\n        \"template\",\n        { slot: \"content\" },\n        [\n          _c(\n            \"a-spin\",\n            { attrs: { spinning: _vm.loadding } },\n            [\n              _c(\n                \"a-tabs\",\n                _vm._l(_vm.tabs, function (tab, k) {\n                  return _c(\"a-tab-pane\", { key: k, attrs: { tab: tab.title } })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"span\",\n        { staticClass: \"header-notice\", on: { click: _vm.fetchNotice } },\n        [\n          _c(\n            \"a-badge\",\n            { attrs: { count: \"12\" } },\n            [\n              _c(\"a-icon\", {\n                staticStyle: { \"font-size\": \"16px\", padding: \"4px\" },\n                attrs: { type: \"bell\" },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,WAAW,EACX;IACEE,KAAK,EAAE;MACLC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,aAAa;MACxBC,YAAY,EAAE;QAAEC,KAAK,EAAE;MAAQ;IACjC;EACF,CAAC,EACD,CACEN,EAAE,CACA,UAAU,EACV;IAAEO,IAAI,EAAE;EAAU,CAAC,EACnB,CACEP,EAAE,CACA,QAAQ,EACR;IAAEE,KAAK,EAAE;MAAEM,QAAQ,EAAET,GAAG,CAACU;IAAS;EAAE,CAAC,EACrC,CACET,EAAE,CACA,QAAQ,EACRD,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,IAAI,EAAE,UAAUC,GAAG,EAAEC,CAAC,EAAE;IACjC,OAAOb,EAAE,CAAC,YAAY,EAAE;MAAEc,GAAG,EAAED,CAAC;MAAEX,KAAK,EAAE;QAAEU,GAAG,EAAEA,GAAG,CAACG;MAAM;IAAE,CAAC,CAAC;EAChE,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDf,EAAE,CACA,MAAM,EACN;IAAEgB,WAAW,EAAE,eAAe;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEnB,GAAG,CAACoB;IAAY;EAAE,CAAC,EAChE,CACEnB,EAAE,CACA,SAAS,EACT;IAAEE,KAAK,EAAE;MAAEkB,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACEpB,EAAE,CAAC,QAAQ,EAAE;IACXqB,WAAW,EAAE;MAAE,WAAW,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAM,CAAC;IACpDpB,KAAK,EAAE;MAAEqB,IAAI,EAAE;IAAO;EACxB,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB1B,MAAM,CAAC2B,aAAa,GAAG,IAAI;AAE3B,SAAS3B,MAAM,EAAE0B,eAAe", "ignoreList": []}]}