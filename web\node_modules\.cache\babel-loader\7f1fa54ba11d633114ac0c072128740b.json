{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderTicketModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { httpAction } from '@/api/manage';\nimport pick from 'lodash.pick';\nimport moment from 'moment';\nimport JDate from '@/components/jeecg/JDate';\nexport default {\n  components: {\n    JDate: JDate\n  },\n  name: 'JeecgOrderTicketModal',\n  data: function data() {\n    return {\n      title: '操作',\n      visible: false,\n      model: {},\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      moment: moment,\n      format: 'YYYY-MM-DD HH:mm:ss',\n      disableSubmit: false,\n      orderId: '',\n      hiding: false,\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      validatorRules: {},\n      url: {\n        add: '/test/order/addTicket',\n        edit: '/test/order/editTicket'\n      }\n    };\n  },\n  created: function created() {},\n  methods: {\n    add: function add(orderId) {\n      if (orderId) {\n        this.edit({\n          orderId: orderId\n        }, '');\n      } else {\n        this.$message.warning('请选择一条航班数据');\n      }\n    },\n    detail: function detail(record) {\n      this.edit(record, 'd');\n    },\n    edit: function edit(record, v) {\n      var _this = this;\n      if (v == 'e') {\n        this.hiding = false;\n        this.disableSubmit = false;\n      } else if (v == 'd') {\n        this.hiding = false;\n        this.disableSubmit = true;\n      } else {\n        this.hiding = true;\n        this.disableSubmit = false;\n      }\n      this.form.resetFields();\n      this.orderId = record.orderId;\n      this.model = Object.assign({}, record);\n      this.visible = true;\n      this.$nextTick(function () {\n        _this.form.setFieldsValue(pick(_this.model, 'ticketCode', 'tickectDate', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'));\n      });\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      var _this2 = this;\n      var that = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n          var httpurl = '';\n          var method = '';\n          if (!_this2.model.id) {\n            httpurl += _this2.url.add;\n            method = 'post';\n          } else {\n            httpurl += _this2.url.edit;\n            method = 'put';\n          }\n          var formData = Object.assign(_this2.model, values);\n          formData.mainId = _this2.orderId;\n          httpAction(httpurl, formData, method).then(function (res) {\n            if (res.success) {\n              that.$message.success(res.message);\n              that.$emit('ok');\n            } else {\n              that.$message.warning(res.message);\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n            that.close();\n          });\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "pick", "moment", "JDate", "components", "name", "data", "title", "visible", "model", "labelCol", "xs", "span", "sm", "wrapperCol", "format", "disableSubmit", "orderId", "hiding", "confirmLoading", "form", "$form", "createForm", "validatorRules", "url", "add", "edit", "created", "methods", "$message", "warning", "detail", "record", "v", "_this", "resetFields", "Object", "assign", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "$emit", "handleOk", "_this2", "that", "validateFields", "err", "values", "httpurl", "method", "id", "formData", "mainId", "then", "res", "success", "message", "finally", "handleCancel"], "sources": ["src/views/jeecg/tablist/form/JeecgOrderTicketModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"800\"\n    :visible=\"visible\"\n    :okButtonProps=\"{ props: {disabled: disableSubmit} }\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"航班号\"\n          hasFeedback>\n          <a-input\n            placeholder=\"请输入航班号\"\n            :readOnly=\"disableSubmit\"\n            v-decorator=\"['ticketCode', {rules:[{ required: true,message: '请输入航班号!'}]}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"航班时间\"\n          hasFeedback>\n          <j-date :trigger-change=\"true\"  v-decorator=\"['tickectDate',{rules:[{ required: true,message: '请输入航班号!'}]}]\"></j-date>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单号码\"\n          v-model=\"this.orderId\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'orderId', {}]\" disabled=\"disabled\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"创建人\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'createBy', {}]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"创建时间\"\n          :hidden=\"hiding\"\n          hasFeedback>\n          <a-input v-decorator=\"[ 'createTime', {}]\" :readOnly=\"disableSubmit\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from 'moment'\n  import JDate from '@/components/jeecg/JDate'\n\n  export default {\n    components: {\n      JDate\n    },\n    name: 'JeecgOrderTicketModal',\n    data() {\n      return {\n        title: '操作',\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5}\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16}\n        },\n        moment,\n        format: 'YYYY-MM-DD HH:mm:ss',\n        disableSubmit: false,\n        orderId: '',\n        hiding: false,\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: '/test/order/addTicket',\n          edit: '/test/order/editTicket'\n        }\n      }\n    },\n    created() {\n    },\n    methods: {\n      add(orderId) {\n        if (orderId) {\n          this.edit({orderId}, '')\n        } else {\n          this.$message.warning('请选择一条航班数据')\n        }\n      },\n      detail(record) {\n        this.edit(record, 'd')\n      },\n      edit(record, v) {\n        if (v == 'e') {\n          this.hiding = false;\n          this.disableSubmit = false;\n        } else if (v == 'd') {\n          this.hiding = false;\n          this.disableSubmit = true;\n        } else {\n          this.hiding = true;\n          this.disableSubmit = false;\n        }\n        this.form.resetFields();\n        this.orderId = record.orderId;\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'ticketCode', 'tickectDate', 'orderId', 'createBy', 'createTime', 'updateBy', 'updateTime'))\n        })\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            formData.mainId = this.orderId;\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok')\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AA8DA,SAAAA,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,MAAA;AACA,OAAAC,KAAA;AAEA;EACAC,UAAA;IACAD,KAAA,EAAAA;EACA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAV,MAAA,EAAAA,MAAA;MACAa,MAAA;MACAC,aAAA;MACAC,OAAA;MACAC,MAAA;MACAC,cAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,cAAA;MACAC,GAAA;QACAC,GAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAH,GAAA,WAAAA,IAAAR,OAAA;MACA,IAAAA,OAAA;QACA,KAAAS,IAAA;UAAAT,OAAA,EAAAA;QAAA;MACA;QACA,KAAAY,QAAA,CAAAC,OAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAAC,MAAA;MACA,KAAAN,IAAA,CAAAM,MAAA;IACA;IACAN,IAAA,WAAAA,KAAAM,MAAA,EAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,CAAA;QACA,KAAAf,MAAA;QACA,KAAAF,aAAA;MACA,WAAAiB,CAAA;QACA,KAAAf,MAAA;QACA,KAAAF,aAAA;MACA;QACA,KAAAE,MAAA;QACA,KAAAF,aAAA;MACA;MACA,KAAAI,IAAA,CAAAe,WAAA;MACA,KAAAlB,OAAA,GAAAe,MAAA,CAAAf,OAAA;MACA,KAAAR,KAAA,GAAA2B,MAAA,CAAAC,MAAA,KAAAL,MAAA;MACA,KAAAxB,OAAA;MACA,KAAA8B,SAAA;QACAJ,KAAA,CAAAd,IAAA,CAAAmB,cAAA,CAAAtC,IAAA,CAAAiC,KAAA,CAAAzB,KAAA;MACA;IACA;IACA+B,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAAjC,OAAA;IACA;IACAkC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA;MACA;MACA,KAAAxB,IAAA,CAAAyB,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAF,IAAA,CAAAzB,cAAA;UACA,IAAA6B,OAAA;UACA,IAAAC,MAAA;UACA,KAAAN,MAAA,CAAAlC,KAAA,CAAAyC,EAAA;YACAF,OAAA,IAAAL,MAAA,CAAAnB,GAAA,CAAAC,GAAA;YACAwB,MAAA;UACA;YACAD,OAAA,IAAAL,MAAA,CAAAnB,GAAA,CAAAE,IAAA;YACAuB,MAAA;UACA;UACA,IAAAE,QAAA,GAAAf,MAAA,CAAAC,MAAA,CAAAM,MAAA,CAAAlC,KAAA,EAAAsC,MAAA;UACAI,QAAA,CAAAC,MAAA,GAAAT,MAAA,CAAA1B,OAAA;UACAjB,UAAA,CAAAgD,OAAA,EAAAG,QAAA,EAAAF,MAAA,EAAAI,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAX,IAAA,CAAAf,QAAA,CAAA0B,OAAA,CAAAD,GAAA,CAAAE,OAAA;cACAZ,IAAA,CAAAH,KAAA;YACA;cACAG,IAAA,CAAAf,QAAA,CAAAC,OAAA,CAAAwB,GAAA,CAAAE,OAAA;YACA;UACA,GAAAC,OAAA;YACAb,IAAA,CAAAzB,cAAA;YACAyB,IAAA,CAAAJ,KAAA;UACA;QACA;MACA;IACA;IACAkB,YAAA,WAAAA,aAAA;MACA,KAAAlB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}