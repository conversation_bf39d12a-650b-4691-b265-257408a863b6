{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ArchivesStatisticst.vue?vue&type=template&id=4cb8a0e0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\report\\ArchivesStatisticst.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <a-tabs defaultActiveKey=\"1\" @change=\"callback\">\n    <a-tab-pane tab=\"柱状图\" key=\"1\">\n      <a-row>\n        <a-col :span=\"10\">\n          <a-radio-group :value=\"barType\" @change=\"statisticst\">\n            <a-radio-button value=\"year\">按年统计</a-radio-button>\n            <a-radio-button value=\"month\">按月统计</a-radio-button>\n            <a-radio-button value=\"category\">按类别统计</a-radio-button>\n            <a-radio-button value=\"cabinet\">按柜号统计</a-radio-button>\n          </a-radio-group>\n        </a-col>\n        <a-col :span=\"14\">\n          <a-form v-if=\"barType === 'month' && false\" layout=\"inline\" style=\"margin-top: -4px\">\n            <a-form-item label=\"月份区间\">\n              <a-range-picker\n                :placeholder=\"['开始月份', '结束月份']\"\n                format=\"YYYY-MM\"\n                :value=\"barValue\"\n                :mode=\"barDate\"\n                @panelChange=\"handleBarDate\"/>\n            </a-form-item>\n            <a-button style=\"margin-top: 2px\" type=\"primary\" icon=\"search\" @click=\"queryDatebar\">查询</a-button>\n            <a-button style=\"margin-top: 2px;margin-left: 8px\" type=\"primary\" icon=\"reload\" @click=\"searchReset\">重置</a-button>\n          </a-form>\n        </a-col>\n        <bar class=\"statistic\" title=\"档案统计\" :dataSource=\"countSource\" :height=\"400\"/>\n      </a-row>\n    </a-tab-pane>\n\n    <a-tab-pane tab=\"饼状图\" key=\"2\">\n      <a-row :gutter=\"24\">\n        <a-col :span=\"10\">\n          <a-radio-group :value=\"pieType\" @change=\"statisticst\">\n            <a-radio-button value=\"year\">按年统计</a-radio-button>\n            <a-radio-button value=\"month\">按月统计</a-radio-button>\n            <a-radio-button value=\"category\">按类别统计</a-radio-button>\n            <a-radio-button value=\"cabinet\">按柜号统计</a-radio-button>\n          </a-radio-group>\n        </a-col>\n        <a-col :span=\"14\">\n          <a-form v-if=\"pieType === 'month' && false\" layout=\"inline\" style=\"margin-top: -4px\">\n            <a-row :gutter=\"24\">\n              <a-form-item label=\"月份区间\">\n                <a-range-picker\n                  :placeholder=\"['开始月份', '结束月份']\"\n                  format=\"YYYY-MM\"\n                  :value=\"pieValue\"\n                  :mode=\"pieDate\"\n                  @panelChange=\"handlePieDate\"/>\n              </a-form-item>\n              <a-button style=\"margin-top: 2px\" type=\"primary\" icon=\"search\" @click=\"queryDatepie\">查询</a-button>\n              <a-button style=\"margin-top: 2px;margin-left: 8px\" type=\"primary\" icon=\"reload\" @click=\"searchReset\">重置</a-button>\n            </a-row>\n          </a-form>\n        </a-col>\n        <pie class=\"statistic\" title=\"档案统计\" :dataSource=\"countSource\" :height=\"450\"/>\n      </a-row>\n    </a-tab-pane>\n  </a-tabs>\n</a-card>\n", null]}