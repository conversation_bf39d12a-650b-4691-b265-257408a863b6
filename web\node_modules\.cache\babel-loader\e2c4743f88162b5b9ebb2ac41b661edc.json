{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\HeadInfo.vue?vue&type=template&id=71b15992&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\HeadInfo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"head-info\",\n    class: _vm.center && \"center\"\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.title))]), _c(\"p\", [_vm._v(_vm._s(_vm.content))]), _vm.bordered ? _c(\"em\") : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "center", "_v", "_s", "title", "content", "bordered", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/tools/HeadInfo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"head-info\", class: _vm.center && \"center\" },\n    [\n      _c(\"span\", [_vm._v(_vm._s(_vm.title))]),\n      _c(\"p\", [_vm._v(_vm._s(_vm.content))]),\n      _vm.bordered ? _c(\"em\") : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEC,KAAK,EAAEJ,GAAG,CAACK,MAAM,IAAI;EAAS,CAAC,EAC3D,CACEJ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,EACvCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC,EACtCT,GAAG,CAACU,QAAQ,GAAGT,EAAE,CAAC,IAAI,CAAC,GAAGD,GAAG,CAACW,EAAE,CAAC,CAAC,CAEtC,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBb,MAAM,CAACc,aAAa,GAAG,IAAI;AAE3B,SAASd,MAAM,EAAEa,eAAe", "ignoreList": []}]}