{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Radar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Radar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  const axis1Opts = {\n    dataKey: 'item',\n    line: null,\n    tickLine: null,\n    grid: {\n      lineStyle: {\n        lineDash: null\n      },\n      hideFirstLine: false\n    }\n  }\n  const axis2Opts = {\n    dataKey: 'score',\n    line: null,\n    tickLine: null,\n    grid: {\n      type: 'polygon',\n      lineStyle: {\n        lineDash: null\n      }\n    }\n  }\n\n  const scale = [\n    {\n      dataKey: 'score',\n      min: 0,\n      max: 100\n    }, {\n      dataKey: 'user',\n      alias: '类型'\n    }\n  ]\n\n  const sourceData = [\n    { item: '示例一', score: 40 },\n    { item: '示例二', score: 20 },\n    { item: '示例三', score: 67 },\n    { item: '示例四', score: 43 },\n    { item: '示例五', score: 90 }\n  ]\n\n  export default {\n    name: 'Radar',\n    props: {\n      height: {\n        type: Number,\n        default: 254\n      },\n      dataSource: {\n        type: Array,\n        default: () => []\n      }\n    },\n    data() {\n      return {\n        axis1Opts,\n        axis2Opts,\n        scale,\n        data: sourceData\n      }\n    },\n    watch: {\n      dataSource(newVal) {\n        if (newVal.length === 0) {\n          this.data = sourceData\n        } else {\n          this.data = newVal\n        }\n      }\n    }\n  }\n", {"version": 3, "sources": ["Radar.vue"], "names": [], "mappings": ";AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Radar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <v-chart :forceFit=\"true\" :height=\"height\" :data=\"data\" :padding=\"[20, 20, 95, 20]\" :scale=\"scale\">\n    <v-tooltip></v-tooltip>\n    <v-axis :dataKey=\"axis1Opts.dataKey\" :line=\"axis1Opts.line\" :tickLine=\"axis1Opts.tickLine\" :grid=\"axis1Opts.grid\"/>\n    <v-axis :dataKey=\"axis2Opts.dataKey\" :line=\"axis2Opts.line\" :tickLine=\"axis2Opts.tickLine\" :grid=\"axis2Opts.grid\"/>\n    <v-legend dataKey=\"user\" marker=\"circle\" :offset=\"30\"/>\n    <v-coord type=\"polar\" radius=\"0.8\"/>\n    <v-line position=\"item*score\" color=\"user\" :size=\"2\"/>\n    <v-point position=\"item*score\" color=\"user\" :size=\"4\" shape=\"circle\"/>\n  </v-chart>\n</template>\n\n<script>\n  const axis1Opts = {\n    dataKey: 'item',\n    line: null,\n    tickLine: null,\n    grid: {\n      lineStyle: {\n        lineDash: null\n      },\n      hideFirstLine: false\n    }\n  }\n  const axis2Opts = {\n    dataKey: 'score',\n    line: null,\n    tickLine: null,\n    grid: {\n      type: 'polygon',\n      lineStyle: {\n        lineDash: null\n      }\n    }\n  }\n\n  const scale = [\n    {\n      dataKey: 'score',\n      min: 0,\n      max: 100\n    }, {\n      dataKey: 'user',\n      alias: '类型'\n    }\n  ]\n\n  const sourceData = [\n    { item: '示例一', score: 40 },\n    { item: '示例二', score: 20 },\n    { item: '示例三', score: 67 },\n    { item: '示例四', score: 43 },\n    { item: '示例五', score: 90 }\n  ]\n\n  export default {\n    name: 'Radar',\n    props: {\n      height: {\n        type: Number,\n        default: 254\n      },\n      dataSource: {\n        type: Array,\n        default: () => []\n      }\n    },\n    data() {\n      return {\n        axis1Opts,\n        axis2Opts,\n        scale,\n        data: sourceData\n      }\n    },\n    watch: {\n      dataSource(newVal) {\n        if (newVal.length === 0) {\n          this.data = sourceData\n        } else {\n          this.data = newVal\n        }\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}