{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue?vue&type=template&id=64e22abc&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue", "mtime": 1749742934824}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"confirm-dialog\"\n  }, [_c(\"div\", {\n    staticClass: \"confirm-content\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"confirm-body\"\n  }, [_c(\"p\", [_vm._v(_vm._s(_vm.message))]), _c(\"div\", {\n    staticClass: \"coin-info\"\n  }, [_c(\"span\", {\n    staticClass: \"coin-icon\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.gameCost) + \" 金币\")])])]), _c(\"div\", {\n    staticClass: \"confirm-footer\"\n  }, [_c(\"button\", {\n    staticClass: \"confirm-button\",\n    on: {\n      click: function click($event) {\n        return _vm.$emit(\"confirm\");\n      }\n    }\n  }, [_vm._v(\"确 定\")]), _c(\"button\", {\n    staticClass: \"cancel-button\",\n    on: {\n      click: function click($event) {\n        return _vm.$emit(\"cancel\");\n      }\n    }\n  }, [_vm._v(\"取 消\")])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"confirm-header\"\n  }, [_c(\"div\", {\n    staticClass: \"confirm-icon\"\n  }, [_vm._v(\"❓\")]), _c(\"h3\", [_vm._v(\"确认\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "_s", "message", "gameCost", "on", "click", "$event", "$emit", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/game/components/ConfirmDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"confirm-dialog\" }, [\n    _c(\"div\", { staticClass: \"confirm-content\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"confirm-body\" }, [\n        _c(\"p\", [_vm._v(_vm._s(_vm.message))]),\n        _c(\"div\", { staticClass: \"coin-info\" }, [\n          _c(\"span\", { staticClass: \"coin-icon\" }),\n          _c(\"span\", [_vm._v(_vm._s(_vm.gameCost) + \" 金币\")]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"confirm-footer\" }, [\n        _c(\n          \"button\",\n          {\n            staticClass: \"confirm-button\",\n            on: {\n              click: function ($event) {\n                return _vm.$emit(\"confirm\")\n              },\n            },\n          },\n          [_vm._v(\"确 定\")]\n        ),\n        _c(\n          \"button\",\n          {\n            staticClass: \"cancel-button\",\n            on: {\n              click: function ($event) {\n                return _vm.$emit(\"cancel\")\n              },\n            },\n          },\n          [_vm._v(\"取 消\")]\n        ),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"confirm-header\" }, [\n      _c(\"div\", { staticClass: \"confirm-icon\" }, [_vm._v(\"❓\")]),\n      _c(\"h3\", [_vm._v(\"确认\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,EACtCN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACQ,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CACnD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,gBAAgB;IAC7BM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,KAAK,CAAC,SAAS,CAAC;MAC7B;IACF;EACF,CAAC,EACD,CAACZ,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,EACDJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,eAAe;IAC5BM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;QACvB,OAAOX,GAAG,CAACY,KAAK,CAAC,QAAQ,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CAACZ,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIQ,eAAe,GAAG,CACpB,YAAY;EACV,IAAIb,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACzDJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACzB,CAAC;AACJ,CAAC,CACF;AACDN,MAAM,CAACe,aAAa,GAAG,IAAI;AAE3B,SAASf,MAAM,EAAEc,eAAe", "ignoreList": []}]}