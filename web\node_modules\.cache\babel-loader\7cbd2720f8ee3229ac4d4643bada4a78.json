{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\GameModal.vue", "mtime": 1749743107576}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { getFileAccessHttpUrl } from '@/api/manage';\nexport default {\n  name: 'GameModal',\n  props: {\n    game: {\n      type: Object,\n      required: true\n    },\n    userCoins: {\n      type: Number,\n      required: true\n    },\n    userName: {\n      type: String,\n      default: '账号名'\n    },\n    userAvatar: {\n      type: String,\n      default: '/logo.png'\n    }\n  },\n  data: function data() {\n    return {\n      gameTimeLeft: 600,\n      // 10分钟 = 600秒\n      gameTimer: null,\n      gameOver: false,\n      showTips: false,\n      isProcessing: false // 防止重复点击\n    };\n  },\n  computed: {\n    formatTime: function formatTime() {\n      var minutes = Math.floor(this.gameTimeLeft / 60);\n      var seconds = this.gameTimeLeft % 60;\n      return \"\".concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    },\n    isTimeWarning: function isTimeWarning() {\n      // 当剩余时间少于1分钟时返回true\n      return this.gameTimeLeft <= 60;\n    }\n  },\n  mounted: function mounted() {\n    this.startGame();\n    this.setupIframeProtection();\n  },\n  beforeUnmount: function beforeUnmount() {\n    this.clearGameTimer();\n  },\n  methods: {\n    startGame: function startGame() {\n      var _this = this;\n      // 消费金币\n      this.$emit('consume-coins', this.game.cost, this.game.title);\n\n      // 重置游戏状态\n      this.gameOver = false;\n      this.gameTimeLeft = 600; // 10分钟\n\n      // 清除之前的计时器\n      this.clearGameTimer();\n\n      // 启动新的计时器\n      this.gameTimer = setInterval(function () {\n        _this.gameTimeLeft--;\n\n        // 当剩余时间为1分钟时，显示提醒\n        if (_this.gameTimeLeft === 60) {\n          _this.$message.warning('游戏时间还剩1分钟！');\n        }\n\n        // 游戏时间结束\n        if (_this.gameTimeLeft <= 0) {\n          _this.clearGameTimer();\n          _this.handleGameOver();\n        }\n      }, 1000);\n    },\n    clearGameTimer: function clearGameTimer() {\n      if (this.gameTimer) {\n        clearInterval(this.gameTimer);\n        this.gameTimer = null;\n      }\n    },\n    handleGameOver: function handleGameOver() {\n      // 显示游戏结束弹窗\n      this.gameOver = true;\n\n      // 暂停游戏（尝试暂停iframe内容）\n      try {\n        var gameFrame = this.$refs.gameFrame;\n        if (gameFrame && gameFrame.contentWindow) {\n          // 尝试发送暂停消息到iframe\n          gameFrame.contentWindow.postMessage({\n            type: 'PAUSE_GAME'\n          }, '*');\n\n          // 尝试覆盖iframe内容使其无法交互\n          var overlay = document.createElement('div');\n          overlay.style.cssText = 'position:absolute;top:0;left:0;width:100%;height:100%;background:transparent;z-index:1000;';\n          gameFrame.parentNode.insertBefore(overlay, gameFrame.nextSibling);\n        }\n      } catch (error) {\n        console.log('无法暂停游戏:', error);\n      }\n    },\n    continueGame: function continueGame() {\n      var _this2 = this;\n      // 防止重复点击\n      if (this.isProcessing) return;\n      this.isProcessing = true;\n\n      // 检查金币是否足够\n      if (this.userCoins >= this.game.cost) {\n        // 直接消费金币并继续游戏\n        this.$emit('consume-coins', this.game.cost, this.game.title);\n\n        // 重置游戏状态\n        this.gameOver = false;\n        this.gameTimeLeft = 600; // 10分钟\n\n        // 启动新的计时器\n        this.gameTimer = setInterval(function () {\n          _this2.gameTimeLeft--;\n          if (_this2.gameTimeLeft === 60) {\n            _this2.$message.warning('游戏时间还剩1分钟！');\n          }\n          if (_this2.gameTimeLeft <= 0) {\n            _this2.clearGameTimer();\n            _this2.handleGameOver();\n          }\n        }, 1000);\n\n        // 移除可能添加的覆盖层\n        try {\n          var gameFrame = this.$refs.gameFrame;\n          if (gameFrame && gameFrame.nextSibling && gameFrame.nextSibling.style && gameFrame.nextSibling.style.position === 'absolute') {\n            gameFrame.parentNode.removeChild(gameFrame.nextSibling);\n          }\n        } catch (error) {\n          console.log('移除覆盖层失败:', error);\n        }\n      } else {\n        this.$message.error('金币不足，无法继续游戏！');\n        setTimeout(function () {\n          _this2.closeModal();\n        }, 1500);\n      }\n\n      // 重置处理状态\n      setTimeout(function () {\n        _this2.isProcessing = false;\n      }, 1000);\n    },\n    closeModal: function closeModal() {\n      this.clearGameTimer();\n      this.gameOver = false;\n      this.$emit('close');\n    },\n    showGameTips: function showGameTips() {\n      this.showTips = true;\n    },\n    closeTipModal: function closeTipModal() {\n      this.showTips = false;\n    },\n    toggleFullscreen: function toggleFullscreen() {\n      var gameFrame = this.$refs.gameFrame;\n      if (!document.fullscreenElement) {\n        if (gameFrame.requestFullscreen) {\n          gameFrame.requestFullscreen();\n        } else if (gameFrame.webkitRequestFullscreen) {\n          gameFrame.webkitRequestFullscreen();\n        } else if (gameFrame.msRequestFullscreen) {\n          gameFrame.msRequestFullscreen();\n        }\n      } else {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitExitFullscreen) {\n          document.webkitExitFullscreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      }\n    },\n    setupIframeProtection: function setupIframeProtection() {\n      var _this3 = this;\n      // 监听iframe加载完成事件\n      this.$nextTick(function () {\n        var gameFrame = _this3.$refs.gameFrame;\n        if (gameFrame) {\n          gameFrame.addEventListener('load', function () {\n            try {\n              var frameWindow = gameFrame.contentWindow;\n              if (frameWindow) {\n                // 尝试注入CSS样式隐藏外部链接\n                var style = document.createElement('style');\n                style.textContent = \"\\n                  a[target=\\\"_blank\\\"], a.external, .crazygames-link, #crazygames, .external-link, .logo-link {\\n                    display: none !important;\\n                    visibility: hidden !important;\\n                    opacity: 0 !important;\\n                    pointer-events: none !important;\\n                  }\\n                  \\n                  iframe {\\n                    pointer-events: auto !important;\\n                  }\\n                \";\n                try {\n                  frameWindow.document.head.appendChild(style);\n                } catch (err) {\n                  console.log('无法注入样式: ', err);\n                }\n\n                // 添加事件监听阻止外部链接点击\n                try {\n                  frameWindow.document.addEventListener('click', function (e) {\n                    if (e.target.tagName === 'A' && (e.target.target === '_blank' || e.target.href.indexOf('crazygames') > -1)) {\n                      e.preventDefault();\n                      e.stopPropagation();\n                      return false;\n                    }\n                  }, true);\n                } catch (err) {\n                  console.log('无法添加事件监听: ', err);\n                }\n              }\n            } catch (err) {\n              console.log('无法访问iframe内容: ', err);\n            }\n          });\n        }\n      });\n\n      // 监听message事件\n      window.addEventListener('message', function (event) {\n        // 忽略来自可信来源的消息\n        if (event.data && event.data.type === 'NAVIGATE') {\n          event.preventDefault();\n          console.log('外链导航已阻止');\n          return false;\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["getFileAccessHttpUrl", "name", "props", "game", "type", "Object", "required", "userCoins", "Number", "userName", "String", "default", "userAvatar", "data", "gameTimeLeft", "gameTimer", "gameOver", "showTips", "isProcessing", "computed", "formatTime", "minutes", "Math", "floor", "seconds", "concat", "toString", "padStart", "isTimeWarning", "mounted", "startGame", "setupIframeProtection", "beforeUnmount", "clearGameTimer", "methods", "_this", "$emit", "cost", "title", "setInterval", "$message", "warning", "handleGameOver", "clearInterval", "gameFrame", "$refs", "contentWindow", "postMessage", "overlay", "document", "createElement", "style", "cssText", "parentNode", "insertBefore", "nextS<PERSON>ling", "error", "console", "log", "continueGame", "_this2", "position", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "closeModal", "showGameTips", "closeTipModal", "toggleFullscreen", "fullscreenElement", "requestFullscreen", "webkitRequestFullscreen", "msRequestFullscreen", "exitFullscreen", "webkitExitFullscreen", "msExitFullscreen", "_this3", "$nextTick", "addEventListener", "frameWindow", "textContent", "head", "append<PERSON><PERSON><PERSON>", "err", "e", "target", "tagName", "href", "indexOf", "preventDefault", "stopPropagation", "window", "event"], "sources": ["src/views/game/components/GameModal.vue"], "sourcesContent": ["<template>\n  <div class=\"game-modal-container\">\n    <div class=\"modal\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h2 class=\"modal-title\">{{ game.title }}</h2>\n          <div class=\"header-buttons\">\n            <div class=\"coin-display\">\n              <span class=\"coin-icon\"></span>\n              <span class=\"coin-amount\">{{ userCoins }}</span>\n            </div>\n            <div class=\"game-timer\" :class=\"{'warning': isTimeWarning}\" v-show=\"!gameOver\">\n              剩余时间: {{ formatTime }}\n            </div>\n            <button class=\"header-button\" @click=\"showGameTips\">\n              <i>❓</i> 游戏提示\n            </button>\n            <button class=\"header-button\" @click=\"toggleFullscreen\">\n              <i>⛶</i> 全屏模式\n            </button>\n            <button class=\"close-button\" @click=\"closeModal\">&times;</button>\n          </div>\n        </div>\n        <div class=\"modal-body\">\n          <iframe ref=\"gameFrame\" class=\"game-frame\" :src=\"game.url\" title=\"游戏\" frameborder=\"0\" sandbox=\"allow-scripts allow-same-origin allow-forms\" allow=\"gamepad *; fullscreen\" allowfullscreen></iframe>\n        </div>\n      </div>\n    </div>\n\n    <!-- 游戏提示弹窗 -->\n    <div class=\"tip-modal\" v-show=\"showTips\">\n      <div class=\"tip-content\">\n        <div class=\"tip-header\">\n          <h3 class=\"tip-title\">游戏提示 - {{ game.title }}</h3>\n          <button class=\"close-tip\" @click=\"closeTipModal\">&times;</button>\n        </div>\n        <div class=\"tip-body\" v-html=\"game.tips\"></div>\n      </div>\n    </div>\n    \n    <!-- 游戏结束弹窗 -->\n    <div class=\"game-over-modal\" v-if=\"gameOver\">\n      <div class=\"game-over-content\">\n        <div class=\"game-over-header\">\n          <h3>游戏时间已结束</h3>\n        </div>\n        <div class=\"game-over-body\">\n          <p>您的游戏时间已用完，需要继续游玩吗？</p>\n          <p class=\"coin-info\">继续游戏需要消费 {{ game.cost }} 金币</p>\n          <div class=\"game-over-buttons\">\n            <button class=\"continue-button\" @click=\"continueGame\">继续游戏</button>\n            <button class=\"exit-button\" @click=\"closeModal\">退出游戏</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'GameModal',\n  props: {\n    game: {\n      type: Object,\n      required: true\n    },\n    userCoins: {\n      type: Number,\n      required: true\n    },\n    userName: {\n      type: String,\n      default: '账号名'\n    },\n    userAvatar: {\n      type: String,\n      default: '/logo.png'\n    }\n  },\n  data() {\n    return {\n      gameTimeLeft: 600, // 10分钟 = 600秒\n      gameTimer: null,\n      gameOver: false,\n      showTips: false,\n      isProcessing: false // 防止重复点击\n    }\n  },\n  computed: {\n    formatTime() {\n      const minutes = Math.floor(this.gameTimeLeft / 60);\n      const seconds = this.gameTimeLeft % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    },\n    isTimeWarning() {\n      // 当剩余时间少于1分钟时返回true\n      return this.gameTimeLeft <= 60;\n    }\n  },\n  mounted() {\n    this.startGame();\n    this.setupIframeProtection();\n  },\n  beforeUnmount() {\n    this.clearGameTimer();\n  },\n  methods: {\n    startGame() {\n      // 消费金币\n      this.$emit('consume-coins', this.game.cost, this.game.title);\n      \n      // 重置游戏状态\n      this.gameOver = false;\n      this.gameTimeLeft = 600; // 10分钟\n      \n      // 清除之前的计时器\n      this.clearGameTimer();\n      \n      // 启动新的计时器\n      this.gameTimer = setInterval(() => {\n        this.gameTimeLeft--;\n        \n        // 当剩余时间为1分钟时，显示提醒\n        if (this.gameTimeLeft === 60) {\n          this.$message.warning('游戏时间还剩1分钟！');\n        }\n        \n        // 游戏时间结束\n        if (this.gameTimeLeft <= 0) {\n          this.clearGameTimer();\n          this.handleGameOver();\n        }\n      }, 1000);\n    },\n    \n    clearGameTimer() {\n      if (this.gameTimer) {\n        clearInterval(this.gameTimer);\n        this.gameTimer = null;\n      }\n    },\n    \n    handleGameOver() {\n      // 显示游戏结束弹窗\n      this.gameOver = true;\n      \n      // 暂停游戏（尝试暂停iframe内容）\n      try {\n        const gameFrame = this.$refs.gameFrame;\n        if (gameFrame && gameFrame.contentWindow) {\n          // 尝试发送暂停消息到iframe\n          gameFrame.contentWindow.postMessage({ type: 'PAUSE_GAME' }, '*');\n          \n          // 尝试覆盖iframe内容使其无法交互\n          const overlay = document.createElement('div');\n          overlay.style.cssText = 'position:absolute;top:0;left:0;width:100%;height:100%;background:transparent;z-index:1000;';\n          gameFrame.parentNode.insertBefore(overlay, gameFrame.nextSibling);\n        }\n      } catch (error) {\n        console.log('无法暂停游戏:', error);\n      }\n    },\n    \n    continueGame() {\n      // 防止重复点击\n      if (this.isProcessing) return;\n      this.isProcessing = true;\n      \n      // 检查金币是否足够\n      if (this.userCoins >= this.game.cost) {\n        // 直接消费金币并继续游戏\n        this.$emit('consume-coins', this.game.cost, this.game.title);\n        \n        // 重置游戏状态\n        this.gameOver = false;\n        this.gameTimeLeft = 600; // 10分钟\n        \n        // 启动新的计时器\n        this.gameTimer = setInterval(() => {\n          this.gameTimeLeft--;\n          \n          if (this.gameTimeLeft === 60) {\n            this.$message.warning('游戏时间还剩1分钟！');\n          }\n          \n          if (this.gameTimeLeft <= 0) {\n            this.clearGameTimer();\n            this.handleGameOver();\n          }\n        }, 1000);\n        \n        // 移除可能添加的覆盖层\n        try {\n          const gameFrame = this.$refs.gameFrame;\n          if (gameFrame && gameFrame.nextSibling && gameFrame.nextSibling.style && \n              gameFrame.nextSibling.style.position === 'absolute') {\n            gameFrame.parentNode.removeChild(gameFrame.nextSibling);\n          }\n        } catch (error) {\n          console.log('移除覆盖层失败:', error);\n        }\n      } else {\n        this.$message.error('金币不足，无法继续游戏！');\n        setTimeout(() => {\n          this.closeModal();\n        }, 1500);\n      }\n      \n      // 重置处理状态\n      setTimeout(() => {\n        this.isProcessing = false;\n      }, 1000);\n    },\n    \n    closeModal() {\n      this.clearGameTimer();\n      this.gameOver = false;\n      this.$emit('close');\n    },\n    \n    showGameTips() {\n      this.showTips = true;\n    },\n    \n    closeTipModal() {\n      this.showTips = false;\n    },\n    \n    toggleFullscreen() {\n      const gameFrame = this.$refs.gameFrame;\n      \n      if (!document.fullscreenElement) {\n        if (gameFrame.requestFullscreen) {\n          gameFrame.requestFullscreen();\n        } else if (gameFrame.webkitRequestFullscreen) {\n          gameFrame.webkitRequestFullscreen();\n        } else if (gameFrame.msRequestFullscreen) {\n          gameFrame.msRequestFullscreen();\n        }\n      } else {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitExitFullscreen) {\n          document.webkitExitFullscreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      }\n    },\n    \n    setupIframeProtection() {\n      // 监听iframe加载完成事件\n      this.$nextTick(() => {\n        const gameFrame = this.$refs.gameFrame;\n        if (gameFrame) {\n          gameFrame.addEventListener('load', () => {\n            try {\n              const frameWindow = gameFrame.contentWindow;\n              if (frameWindow) {\n                // 尝试注入CSS样式隐藏外部链接\n                const style = document.createElement('style');\n                style.textContent = `\n                  a[target=\"_blank\"], a.external, .crazygames-link, #crazygames, .external-link, .logo-link {\n                    display: none !important;\n                    visibility: hidden !important;\n                    opacity: 0 !important;\n                    pointer-events: none !important;\n                  }\n                  \n                  iframe {\n                    pointer-events: auto !important;\n                  }\n                `;\n                \n                try {\n                  frameWindow.document.head.appendChild(style);\n                } catch(err) {\n                  console.log('无法注入样式: ', err);\n                }\n\n                // 添加事件监听阻止外部链接点击\n                try {\n                  frameWindow.document.addEventListener('click', function(e) {\n                    if(e.target.tagName === 'A' && (e.target.target === '_blank' || e.target.href.indexOf('crazygames') > -1)) {\n                      e.preventDefault();\n                      e.stopPropagation();\n                      return false;\n                    }\n                  }, true);\n                } catch(err) {\n                  console.log('无法添加事件监听: ', err);\n                }\n              }\n            } catch(err) {\n              console.log('无法访问iframe内容: ', err);\n            }\n          });\n        }\n      });\n      \n      // 监听message事件\n      window.addEventListener('message', (event) => {\n        // 忽略来自可信来源的消息\n        if (event.data && event.data.type === 'NAVIGATE') {\n          event.preventDefault();\n          console.log('外链导航已阻止');\n          return false;\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 模态窗口样式 */\n.game-modal-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 2000;\n}\n\n.modal {\n  display: block;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.7);\n  z-index: 1000;\n  overflow: auto;\n}\n\n.modal-content {\n  position: relative;\n  background-color: #fefefe;\n  margin: 2% auto;\n  padding: 0;\n  width: 90%;\n  max-width: 1000px;\n  height: 90vh;\n  border-radius: 10px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  overflow: hidden;\n}\n\n.modal-header {\n  padding: 10px 16px;\n  background: linear-gradient(135deg, #3a416f, #4e54c8);\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-title {\n  margin: 0;\n  font-size: 1.5rem;\n  color: #ffffff;\n  font-weight: bold;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.close-button {\n  color: white;\n  font-size: 28px;\n  font-weight: bold;\n  cursor: pointer;\n  background: none;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.close-button:hover {\n  color: #ddd;\n}\n\n.header-buttons {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.header-button {\n  color: white;\n  background: rgba(255, 255, 255, 0.2);\n  border: none;\n  border-radius: 4px;\n  padding: 6px 12px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  transition: background-color 0.2s;\n}\n\n.header-button:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.header-button i {\n  margin-right: 5px;\n  font-size: 16px;\n}\n\n.modal-body {\n  height: calc(100% - 57px);\n  position: relative;\n}\n\n.game-frame {\n  width: 100%;\n  height: 100%;\n  border: none;\n}\n\n/* 金币显示样式 */\n.coin-display {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.3);\n  padding: 5px 10px;\n  border-radius: 20px;\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 18px;\n  height: 18px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 5px;\n}\n\n.coin-amount {\n  font-weight: bold;\n  font-size: 14px;\n}\n\n/* 游戏计时器 */\n.game-timer {\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n}\n\n/* 时间警告样式 */\n.game-timer.warning {\n  background-color: rgba(255, 0, 0, 0.8);\n  color: white;\n  animation: pulse 1s infinite;\n  font-weight: bold;\n  text-shadow: 0 0 3px #000;\n  box-shadow: 0 0 8px rgba(255, 0, 0, 0.5);\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n\n/* 游戏结束弹窗 */\n.game-over-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 2100;\n}\n\n.game-over-content {\n  background-color: white;\n  border-radius: 10px;\n  width: 90%;\n  max-width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n}\n\n.game-over-header {\n  background: linear-gradient(135deg, #3a416f, #4e54c8);\n  color: white;\n  padding: 15px 20px;\n  text-align: center;\n}\n\n.game-over-header h3 {\n  margin: 0;\n  font-size: 20px;\n}\n\n.game-over-body {\n  padding: 20px;\n  text-align: center;\n}\n\n.game-over-body p {\n  margin: 0 0 15px;\n  font-size: 16px;\n  color: #333;\n}\n\n.coin-info {\n  font-weight: bold;\n  color: #ff8800;\n  margin-bottom: 20px !important;\n}\n\n.game-over-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  margin-top: 20px;\n}\n\n.continue-button {\n  background-color: #4CAF50;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.continue-button:hover {\n  background-color: #45a049;\n}\n\n.exit-button {\n  background-color: #f44336;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.exit-button:hover {\n  background-color: #d32f2f;\n}\n\n/* 游戏提示弹窗 */\n.tip-modal {\n  position: fixed;\n  z-index: 2001;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.7);\n}\n\n.tip-content {\n  background-color: white;\n  margin: 5% auto;\n  padding: 20px;\n  width: 80%;\n  max-width: 800px;\n  border-radius: 10px;\n  max-height: 80%;\n  overflow-y: auto;\n}\n\n.tip-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #eee;\n}\n\n.tip-title {\n  font-size: 1.5rem;\n  color: #ff4f4f;\n  margin: 0;\n}\n\n.close-tip {\n  font-size: 22px;\n  font-weight: bold;\n  color: #777;\n  background: none;\n  border: none;\n  cursor: pointer;\n}\n\n.tip-body {\n  color: #333;\n  line-height: 1.6;\n}\n\n.tip-body h3 {\n  margin-top: 15px;\n  margin-bottom: 8px;\n  color: #333;\n}\n\n.tip-body p {\n  margin-bottom: 10px;\n}\n\n.tip-body ul {\n  padding-left: 20px;\n  margin-bottom: 15px;\n}\n</style> "], "mappings": "AA4DA,SAAAA,oBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAM,MAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAR,IAAA,EAAAM,MAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MAAA;MACAC,SAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,MAAAT,YAAA;MACA,IAAAU,OAAA,QAAAV,YAAA;MACA,UAAAW,MAAA,CAAAJ,OAAA,CAAAK,QAAA,GAAAC,QAAA,eAAAF,MAAA,CAAAD,OAAA,CAAAE,QAAA,GAAAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;MACA,YAAAd,YAAA;IACA;EACA;EACAe,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,qBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAJ,SAAA,WAAAA,UAAA;MAAA,IAAAK,KAAA;MACA;MACA,KAAAC,KAAA,uBAAAjC,IAAA,CAAAkC,IAAA,OAAAlC,IAAA,CAAAmC,KAAA;;MAEA;MACA,KAAAtB,QAAA;MACA,KAAAF,YAAA;;MAEA;MACA,KAAAmB,cAAA;;MAEA;MACA,KAAAlB,SAAA,GAAAwB,WAAA;QACAJ,KAAA,CAAArB,YAAA;;QAEA;QACA,IAAAqB,KAAA,CAAArB,YAAA;UACAqB,KAAA,CAAAK,QAAA,CAAAC,OAAA;QACA;;QAEA;QACA,IAAAN,KAAA,CAAArB,YAAA;UACAqB,KAAA,CAAAF,cAAA;UACAE,KAAA,CAAAO,cAAA;QACA;MACA;IACA;IAEAT,cAAA,WAAAA,eAAA;MACA,SAAAlB,SAAA;QACA4B,aAAA,MAAA5B,SAAA;QACA,KAAAA,SAAA;MACA;IACA;IAEA2B,cAAA,WAAAA,eAAA;MACA;MACA,KAAA1B,QAAA;;MAEA;MACA;QACA,IAAA4B,SAAA,QAAAC,KAAA,CAAAD,SAAA;QACA,IAAAA,SAAA,IAAAA,SAAA,CAAAE,aAAA;UACA;UACAF,SAAA,CAAAE,aAAA,CAAAC,WAAA;YAAA3C,IAAA;UAAA;;UAEA;UACA,IAAA4C,OAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,OAAA,CAAAG,KAAA,CAAAC,OAAA;UACAR,SAAA,CAAAS,UAAA,CAAAC,YAAA,CAAAN,OAAA,EAAAJ,SAAA,CAAAW,WAAA;QACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAC,GAAA,YAAAF,KAAA;MACA;IACA;IAEAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA1C,YAAA;MACA,KAAAA,YAAA;;MAEA;MACA,SAAAX,SAAA,SAAAJ,IAAA,CAAAkC,IAAA;QACA;QACA,KAAAD,KAAA,uBAAAjC,IAAA,CAAAkC,IAAA,OAAAlC,IAAA,CAAAmC,KAAA;;QAEA;QACA,KAAAtB,QAAA;QACA,KAAAF,YAAA;;QAEA;QACA,KAAAC,SAAA,GAAAwB,WAAA;UACAqB,MAAA,CAAA9C,YAAA;UAEA,IAAA8C,MAAA,CAAA9C,YAAA;YACA8C,MAAA,CAAApB,QAAA,CAAAC,OAAA;UACA;UAEA,IAAAmB,MAAA,CAAA9C,YAAA;YACA8C,MAAA,CAAA3B,cAAA;YACA2B,MAAA,CAAAlB,cAAA;UACA;QACA;;QAEA;QACA;UACA,IAAAE,SAAA,QAAAC,KAAA,CAAAD,SAAA;UACA,IAAAA,SAAA,IAAAA,SAAA,CAAAW,WAAA,IAAAX,SAAA,CAAAW,WAAA,CAAAJ,KAAA,IACAP,SAAA,CAAAW,WAAA,CAAAJ,KAAA,CAAAU,QAAA;YACAjB,SAAA,CAAAS,UAAA,CAAAS,WAAA,CAAAlB,SAAA,CAAAW,WAAA;UACA;QACA,SAAAC,KAAA;UACAC,OAAA,CAAAC,GAAA,aAAAF,KAAA;QACA;MACA;QACA,KAAAhB,QAAA,CAAAgB,KAAA;QACAO,UAAA;UACAH,MAAA,CAAAI,UAAA;QACA;MACA;;MAEA;MACAD,UAAA;QACAH,MAAA,CAAA1C,YAAA;MACA;IACA;IAEA8C,UAAA,WAAAA,WAAA;MACA,KAAA/B,cAAA;MACA,KAAAjB,QAAA;MACA,KAAAoB,KAAA;IACA;IAEA6B,YAAA,WAAAA,aAAA;MACA,KAAAhD,QAAA;IACA;IAEAiD,aAAA,WAAAA,cAAA;MACA,KAAAjD,QAAA;IACA;IAEAkD,gBAAA,WAAAA,iBAAA;MACA,IAAAvB,SAAA,QAAAC,KAAA,CAAAD,SAAA;MAEA,KAAAK,QAAA,CAAAmB,iBAAA;QACA,IAAAxB,SAAA,CAAAyB,iBAAA;UACAzB,SAAA,CAAAyB,iBAAA;QACA,WAAAzB,SAAA,CAAA0B,uBAAA;UACA1B,SAAA,CAAA0B,uBAAA;QACA,WAAA1B,SAAA,CAAA2B,mBAAA;UACA3B,SAAA,CAAA2B,mBAAA;QACA;MACA;QACA,IAAAtB,QAAA,CAAAuB,cAAA;UACAvB,QAAA,CAAAuB,cAAA;QACA,WAAAvB,QAAA,CAAAwB,oBAAA;UACAxB,QAAA,CAAAwB,oBAAA;QACA,WAAAxB,QAAA,CAAAyB,gBAAA;UACAzB,QAAA,CAAAyB,gBAAA;QACA;MACA;IACA;IAEA3C,qBAAA,WAAAA,sBAAA;MAAA,IAAA4C,MAAA;MACA;MACA,KAAAC,SAAA;QACA,IAAAhC,SAAA,GAAA+B,MAAA,CAAA9B,KAAA,CAAAD,SAAA;QACA,IAAAA,SAAA;UACAA,SAAA,CAAAiC,gBAAA;YACA;cACA,IAAAC,WAAA,GAAAlC,SAAA,CAAAE,aAAA;cACA,IAAAgC,WAAA;gBACA;gBACA,IAAA3B,KAAA,GAAAF,QAAA,CAAAC,aAAA;gBACAC,KAAA,CAAA4B,WAAA,6dAWA;gBAEA;kBACAD,WAAA,CAAA7B,QAAA,CAAA+B,IAAA,CAAAC,WAAA,CAAA9B,KAAA;gBACA,SAAA+B,GAAA;kBACAzB,OAAA,CAAAC,GAAA,aAAAwB,GAAA;gBACA;;gBAEA;gBACA;kBACAJ,WAAA,CAAA7B,QAAA,CAAA4B,gBAAA,oBAAAM,CAAA;oBACA,IAAAA,CAAA,CAAAC,MAAA,CAAAC,OAAA,aAAAF,CAAA,CAAAC,MAAA,CAAAA,MAAA,iBAAAD,CAAA,CAAAC,MAAA,CAAAE,IAAA,CAAAC,OAAA;sBACAJ,CAAA,CAAAK,cAAA;sBACAL,CAAA,CAAAM,eAAA;sBACA;oBACA;kBACA;gBACA,SAAAP,GAAA;kBACAzB,OAAA,CAAAC,GAAA,eAAAwB,GAAA;gBACA;cACA;YACA,SAAAA,GAAA;cACAzB,OAAA,CAAAC,GAAA,mBAAAwB,GAAA;YACA;UACA;QACA;MACA;;MAEA;MACAQ,MAAA,CAAAb,gBAAA,sBAAAc,KAAA;QACA;QACA,IAAAA,KAAA,CAAA9E,IAAA,IAAA8E,KAAA,CAAA9E,IAAA,CAAAT,IAAA;UACAuF,KAAA,CAAAH,cAAA;UACA/B,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}