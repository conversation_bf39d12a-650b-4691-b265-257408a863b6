{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\PrintDemo.vue?vue&type=style&index=0&id=334a0705&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\PrintDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n/*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */\n* {\n  color: #000000!important;\n  -webkit-tap-highlight-color: #000000!important;\n}\n/*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */\n\n.abcdefg .ant-card-body{\n  margin-left: 0%;\n  margin-right: 0%;\n  margin-bottom: 1%;\n  border:0px solid black;\n  min-width: 800px;\n  color:#000000!important;\n}\n.explain{\n  text-align: left;\n  margin-left: 50px;\n  color:#000000!important;\n}\n.explain .ant-input,.sign .ant-input{\n  font-weight:bolder;\n  text-align:center;\n  border-left-width:0px!important;\n  border-top-width:0px!important;\n  border-right-width:0px!important;\n}\n.explain div{\n  margin-bottom: 10px;\n}\n/* you can make up upload button and sample style by using stylesheets */\n.ant-upload-select-picture-card i {\n  font-size: 32px;\n  color: #999;\n}\n\n.ant-upload-select-picture-card .ant-upload-text {\n  margin-top: 8px;\n  color: #666;\n}\n", {"version": 3, "sources": ["PrintDemo.vue"], "names": [], "mappings": ";AA+KA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "PrintDemo.vue", "sourceRoot": "src/views/jeecg", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\" :class=\"{'abcdefg':true}\">\n    <div class=\"no-print\" style=\"text-align: right\">\n      <a-button v-print=\"'#printContent'\" ghost type=\"primary\">打印</a-button>\n    </div>\n    <section ref=\"print\" id=\"printContent\" class=\"abcdefg\">\n      <div style=\"text-align: center\">\n        <p style=\"font-size: 24px;font-weight: 800\">打印测试表单</p>\n      </div>\n      <!--签字-->\n      <a-col :md=\"24\" :sm=\"24\">\n      <div class=\"sign\" style=\"text-align: left;height: inherit\">\n        <a-col :span=\"24\">\n          <span>\n            打印人员:\n          </span>\n          <a-input style=\"width: 30%\" v-model=\"printer\"/>\n          <span style=\"margin-left: 12.5%\">打印日期:</span>\n          <a-input style=\"width: 30%\" v-model=\"printTime\"/>\n        </a-col>\n        <a-col :span=\"24\">\n        </a-col>\n        <a-col :span=\"24\" style=\"margin-top: 20px\">\n          <span>打印内容:</span>\n          <a-input style=\"width: 80%\" v-model=\"printContent\"/>\n        </a-col>\n        <a-col :span=\"24\" style=\"margin-top: 20px\">\n          <span>打印目的:</span>\n          <a-input style=\"width: 80%\" v-model=\"printReason\"/>\n        </a-col>\n        <a-col style=\"margin-top: 20px\" :span=\"24\">\n          <span>打印图片:</span>\n          <br/>\n          <a-upload\n            action=\"/jsonplaceholder.typicode.com/posts/\"\n            listType=\"picture-card\"\n            :fileList=\"fileList\"\n            @preview=\"handlePreview\"\n            @change=\"handleChange\">\n            <div v-if=\"fileList.length < 3\">\n              <a-icon type=\"plus\" />\n              <div class=\"ant-upload-text\">Upload</div>\n            </div>\n          </a-upload>\n          <a-modal :visible=\"previewVisible\" :footer=\"null\" @cancel=\"handleCancel\">\n            <img alt=\"example\" style=\"width: 100%\" :src=\"previewImage\" />\n          </a-modal>\n        </a-col>\n      </div>\n      </a-col>\n    </section>\n  </a-card>\n  <!--</page-layout>-->\n</template>\n<script>\n  import ACol from \"ant-design-vue/es/grid/Col\";\n  import ARow from \"ant-design-vue/es/grid/Row\";\n  import ATextarea from 'ant-design-vue/es/input/TextArea'\n\n  export default {\n    components: {\n      ATextarea,\n      ARow,\n      ACol,\n    },\n    name: 'Printgzsld',\n    props:{\n      reBizCode:{\n        type: String,\n        default: ''\n      }\n    },\n    data(){\n      return {\n        columns: [{\n        }\n        ],\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 2 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 8 },\n        },\n        printer:'张三',\n        printTime:'2019-02-01 12:00:00',\n        printContent:'打印内容就是,做一个打印测试',\n        printReason:'做一个打印测试',\n        previewVisible: false,\n        previewImage: '',\n        fileList: [{\n          uid: '-1',\n          name: 'xxx.png',\n          status: 'done',\n          url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',\n        },\n          {\n            uid:'-2',\n            name:'pic1.png',\n            status:'done',\n            url:'https://www.gizbot.com/img/2016/11/whatsapp-error-lead-image-08-1478607387.jpg',\n          }\n        ],\n        url:{\n          loadApplicant:\"/sps/register/loadApplicants\",\n          loadRegisterFiles:\"/sps/register/getRegisterFilesConfig\",\n        }\n      }\n    },\n    created() {\n      this.getDate();\n    },\n    methods: {\n      loadData(){\n\n      },\n      getDate(){\n        // 当前时间\n      },\n      handleCancel () {\n        this.previewVisible = false\n      },\n      handlePreview (file) {\n        this.previewImage = file.url || file.thumbUrl\n        this.previewVisible = true\n      },\n      handleChange ({ fileList }) {\n        this.fileList = fileList\n      }\n    }\n  }\n</script>\n<style scoped>\n  /*update_begin author:scott date:20191203 for:打印机打印的字体模糊问题 */\n  * {\n    color: #000000!important;\n    -webkit-tap-highlight-color: #000000!important;\n  }\n  /*update_end author:scott date:20191203 for:打印机打印的字体模糊问题 */\n\n  .abcdefg .ant-card-body{\n    margin-left: 0%;\n    margin-right: 0%;\n    margin-bottom: 1%;\n    border:0px solid black;\n    min-width: 800px;\n    color:#000000!important;\n  }\n  .explain{\n    text-align: left;\n    margin-left: 50px;\n    color:#000000!important;\n  }\n  .explain .ant-input,.sign .ant-input{\n    font-weight:bolder;\n    text-align:center;\n    border-left-width:0px!important;\n    border-top-width:0px!important;\n    border-right-width:0px!important;\n  }\n  .explain div{\n    margin-bottom: 10px;\n  }\n  /* you can make up upload button and sample style by using stylesheets */\n  .ant-upload-select-picture-card i {\n    font-size: 32px;\n    color: #999;\n  }\n\n  .ant-upload-select-picture-card .ant-upload-text {\n    margin-top: 8px;\n    color: #666;\n  }\n</style>"]}]}