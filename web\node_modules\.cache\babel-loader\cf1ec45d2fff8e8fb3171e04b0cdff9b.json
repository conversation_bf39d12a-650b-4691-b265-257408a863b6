{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport RepositoryForm from './RepositoryForm';\nimport TaskForm from './TaskForm';\nimport FooterToolBar from '@/components/tools/FooterToolBar';\nexport default {\n  name: \"AdvancedForm\",\n  components: {\n    FooterToolBar: FooterToolBar,\n    RepositoryForm: RepositoryForm,\n    TaskForm: TaskForm\n  },\n  data: function data() {\n    return {\n      description: '高级表单常见于一次性输入和提交大批量数据的场景。',\n      loading: false,\n      // table\n      columns: [{\n        title: '成员姓名',\n        dataIndex: 'name',\n        key: 'name',\n        width: '20%',\n        scopedSlots: {\n          customRender: 'name'\n        }\n      }, {\n        title: '工号',\n        dataIndex: 'workId',\n        key: 'workId',\n        width: '20%',\n        scopedSlots: {\n          customRender: 'workId'\n        }\n      }, {\n        title: '所属部门',\n        dataIndex: 'department',\n        key: 'department',\n        width: '40%',\n        scopedSlots: {\n          customRender: 'department'\n        }\n      }, {\n        title: '操作',\n        key: 'action',\n        scopedSlots: {\n          customRender: 'operation'\n        }\n      }],\n      data: [{\n        key: '1',\n        name: '小明',\n        workId: '001',\n        editable: false,\n        department: '行政部'\n      }, {\n        key: '2',\n        name: '李莉',\n        workId: '002',\n        editable: false,\n        department: 'IT部'\n      }, {\n        key: '3',\n        name: '王小帅',\n        workId: '003',\n        editable: false,\n        department: '财务部'\n      }]\n    };\n  },\n  methods: {\n    handleSubmit: function handleSubmit(e) {\n      e.preventDefault();\n    },\n    newMember: function newMember() {\n      this.data.push({\n        key: '-1',\n        name: '',\n        workId: '',\n        department: '',\n        editable: true,\n        isNew: true\n      });\n    },\n    remove: function remove(key) {\n      var newData = this.data.filter(function (item) {\n        return item.key !== key;\n      });\n      this.data = newData;\n    },\n    saveRow: function saveRow(key) {\n      var target = this.data.filter(function (item) {\n        return item.key === key;\n      })[0];\n      target.editable = false;\n      target.isNew = false;\n    },\n    toggle: function toggle(key) {\n      var target = this.data.filter(function (item) {\n        return item.key === key;\n      })[0];\n      target.editable = !target.editable;\n    },\n    getRowByKey: function getRowByKey(key, newData) {\n      var data = this.data;\n      return (newData || data).filter(function (item) {\n        return item.key === key;\n      })[0];\n    },\n    cancel: function cancel(key) {\n      var target = this.data.filter(function (item) {\n        return item.key === key;\n      })[0];\n      target.editable = false;\n    },\n    handleChange: function handleChange(value, key, column) {\n      var newData = _toConsumableArray(this.data);\n      var target = newData.filter(function (item) {\n        return key === item.key;\n      })[0];\n      if (target) {\n        target[column] = value;\n        this.data = newData;\n      }\n    },\n    // 最终全页面提交\n    validate: function validate() {\n      var _this = this;\n      this.$refs.repository.form.validateFields(function (err, values) {\n        if (!err) {\n          _this.$notification['error']({\n            message: 'Received values of form:',\n            description: values\n          });\n        }\n      });\n      this.$refs.task.form.validateFields(function (err, values) {\n        if (!err) {\n          _this.$notification['error']({\n            message: 'Received values of form:',\n            description: values\n          });\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["RepositoryForm", "TaskForm", "FooterToolBar", "name", "components", "data", "description", "loading", "columns", "title", "dataIndex", "key", "width", "scopedSlots", "customRender", "workId", "editable", "department", "methods", "handleSubmit", "e", "preventDefault", "newMember", "push", "isNew", "remove", "newData", "filter", "item", "saveRow", "target", "toggle", "getRowByKey", "cancel", "handleChange", "value", "column", "_toConsumableArray", "validate", "_this", "$refs", "repository", "form", "validateFields", "err", "values", "$notification", "message", "task"], "sources": ["src/views/form/advancedForm/AdvancedForm.vue"], "sourcesContent": ["<template>\n  <div>\n    <a-card class=\"card\" title=\"仓库管理\" :bordered=\"false\">\n      <repository-form ref=\"repository\" :showSubmit=\"false\" />\n    </a-card>\n    <a-card class=\"card\" title=\"任务管理\" :bordered=\"false\">\n      <task-form ref=\"task\" :showSubmit=\"false\" />\n    </a-card>\n\n    <!-- table -->\n    <a-card>\n      <form :autoFormCreate=\"(form) => this.form = form\">\n        <a-table\n          :columns=\"columns\"\n          :dataSource=\"data\"\n          :pagination=\"false\"\n        >\n          <template v-for=\"(col, i) in ['name', 'workId', 'department']\" :slot=\"col\" slot-scope=\"text, record, index\">\n            <a-input\n              :key=\"col\"\n              v-if=\"record.editable\"\n              style=\"margin: -5px 0\"\n              :value=\"text\"\n              :placeholder=\"columns[i].title\"\n              @change=\"e => handleChange(e.target.value, record.key, col)\"\n            />\n            <template v-else>{{ text }}</template>\n          </template>\n          <template slot=\"operation\" slot-scope=\"text, record, index\">\n            <template v-if=\"record.editable\">\n              <span v-if=\"record.isNew\">\n                <a @click=\"saveRow(record.key)\">添加</a>\n                <a-divider type=\"vertical\" />\n                <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </span>\n              <span v-else>\n                <a @click=\"saveRow(record.key)\">保存</a>\n                <a-divider type=\"vertical\" />\n                <a @click=\"cancel(record.key)\">取消</a>\n              </span>\n            </template>\n            <span v-else>\n              <a @click=\"toggle(record.key)\">编辑</a>\n              <a-divider type=\"vertical\" />\n              <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </span>\n          </template>\n        </a-table>\n        <a-button style=\"width: 100%; margin-top: 16px; margin-bottom: 8px\" type=\"dashed\" icon=\"plus\" @click=\"newMember\">新增成员</a-button>\n      </form>\n    </a-card>\n\n    <!-- fixed footer toolbar -->\n    <footer-tool-bar>\n      <a-button type=\"primary\" @click=\"validate\" :loading=\"loading\">提交</a-button>\n    </footer-tool-bar>\n  </div>\n</template>\n\n<script>\n  import RepositoryForm from './RepositoryForm'\n  import TaskForm from './TaskForm'\n  import FooterToolBar from '@/components/tools/FooterToolBar'\n\n  export default {\n    name: \"AdvancedForm\",\n    components: {\n      FooterToolBar,\n      RepositoryForm,\n      TaskForm\n    },\n    data () {\n      return {\n        description: '高级表单常见于一次性输入和提交大批量数据的场景。',\n        loading: false,\n\n        // table\n        columns: [\n          {\n            title: '成员姓名',\n            dataIndex: 'name',\n            key: 'name',\n            width: '20%',\n            scopedSlots: { customRender: 'name' }\n          },\n          {\n            title: '工号',\n            dataIndex: 'workId',\n            key: 'workId',\n            width: '20%',\n            scopedSlots: { customRender: 'workId' }\n          },\n          {\n            title: '所属部门',\n            dataIndex: 'department',\n            key: 'department',\n            width: '40%',\n            scopedSlots: { customRender: 'department' }\n          },\n          {\n            title: '操作',\n            key: 'action',\n            scopedSlots: { customRender: 'operation' }\n          }\n        ],\n        data: [\n          {\n            key: '1',\n            name: '小明',\n            workId: '001',\n            editable: false,\n            department: '行政部'\n          },\n          {\n            key: '2',\n            name: '李莉',\n            workId: '002',\n            editable: false,\n            department: 'IT部'\n          },\n          {\n            key: '3',\n            name: '王小帅',\n            workId: '003',\n            editable: false,\n            department: '财务部'\n          }\n        ]\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n      },\n      newMember () {\n        this.data.push({\n          key: '-1',\n          name: '',\n          workId: '',\n          department: '',\n          editable: true,\n          isNew: true\n        })\n      },\n      remove (key) {\n        const newData = this.data.filter(item => item.key !== key)\n        this.data = newData\n      },\n      saveRow (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n        target.isNew = false\n      },\n      toggle (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = !target.editable\n      },\n      getRowByKey (key, newData) {\n        const data = this.data\n        return (newData || data).filter(item => item.key === key)[0]\n      },\n      cancel (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n      },\n      handleChange (value, key, column) {\n        const newData = [...this.data]\n        const target = newData.filter(item => key === item.key)[0]\n        if (target) {\n          target[column] = value\n          this.data = newData\n        }\n      },\n\n      // 最终全页面提交\n      validate () {\n        this.$refs.repository.form.validateFields((err, values) => {\n          if (!err) {\n            this.$notification['error']({\n              message: 'Received values of form:',\n              description: values\n            })\n          }\n        })\n        this.$refs.task.form.validateFields((err, values) => {\n          if (!err) {\n            this.$notification['error']({\n              message: 'Received values of form:',\n              description: values\n            })\n          }\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .card{\n    margin-bottom: 24px;\n  }\n</style>"], "mappings": ";;;;;;AAgEA,OAAAA,cAAA;AACA,OAAAC,QAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF,aAAA,EAAAA,aAAA;IACAF,cAAA,EAAAA,cAAA;IACAC,QAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,OAAA;MAEA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAE,GAAA;QACAE,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MACAT,IAAA,GACA;QACAM,GAAA;QACAR,IAAA;QACAY,MAAA;QACAC,QAAA;QACAC,UAAA;MACA,GACA;QACAN,GAAA;QACAR,IAAA;QACAY,MAAA;QACAC,QAAA;QACAC,UAAA;MACA,GACA;QACAN,GAAA;QACAR,IAAA;QACAY,MAAA;QACAC,QAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,CAAA;MACAA,CAAA,CAAAC,cAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAjB,IAAA,CAAAkB,IAAA;QACAZ,GAAA;QACAR,IAAA;QACAY,MAAA;QACAE,UAAA;QACAD,QAAA;QACAQ,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAAd,GAAA;MACA,IAAAe,OAAA,QAAArB,IAAA,CAAAsB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,GAAA,KAAAA,GAAA;MAAA;MACA,KAAAN,IAAA,GAAAqB,OAAA;IACA;IACAG,OAAA,WAAAA,QAAAlB,GAAA;MACA,IAAAmB,MAAA,QAAAzB,IAAA,CAAAsB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,GAAA,KAAAA,GAAA;MAAA;MACAmB,MAAA,CAAAd,QAAA;MACAc,MAAA,CAAAN,KAAA;IACA;IACAO,MAAA,WAAAA,OAAApB,GAAA;MACA,IAAAmB,MAAA,QAAAzB,IAAA,CAAAsB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,GAAA,KAAAA,GAAA;MAAA;MACAmB,MAAA,CAAAd,QAAA,IAAAc,MAAA,CAAAd,QAAA;IACA;IACAgB,WAAA,WAAAA,YAAArB,GAAA,EAAAe,OAAA;MACA,IAAArB,IAAA,QAAAA,IAAA;MACA,QAAAqB,OAAA,IAAArB,IAAA,EAAAsB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,GAAA,KAAAA,GAAA;MAAA;IACA;IACAsB,MAAA,WAAAA,OAAAtB,GAAA;MACA,IAAAmB,MAAA,QAAAzB,IAAA,CAAAsB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,GAAA,KAAAA,GAAA;MAAA;MACAmB,MAAA,CAAAd,QAAA;IACA;IACAkB,YAAA,WAAAA,aAAAC,KAAA,EAAAxB,GAAA,EAAAyB,MAAA;MACA,IAAAV,OAAA,GAAAW,kBAAA,MAAAhC,IAAA;MACA,IAAAyB,MAAA,GAAAJ,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAjB,GAAA,KAAAiB,IAAA,CAAAjB,GAAA;MAAA;MACA,IAAAmB,MAAA;QACAA,MAAA,CAAAM,MAAA,IAAAD,KAAA;QACA,KAAA9B,IAAA,GAAAqB,OAAA;MACA;IACA;IAEA;IACAY,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,IAAA,CAAAC,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAL,KAAA,CAAAO,aAAA;YACAC,OAAA;YACAzC,WAAA,EAAAuC;UACA;QACA;MACA;MACA,KAAAL,KAAA,CAAAQ,IAAA,CAAAN,IAAA,CAAAC,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAL,KAAA,CAAAO,aAAA;YACAC,OAAA;YACAzC,WAAA,EAAAuC;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}