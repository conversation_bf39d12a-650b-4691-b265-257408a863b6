{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue?vue&type=template&id=e2939ff0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue", "mtime": 1753520887545}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"comprehensive-test-suite\">\n  <a-card title=\"阶段五：全面功能测试 - 综合测试套件\" style=\"margin-bottom: 16px;\">\n    <a-alert\n      message=\"综合测试说明\"\n      description=\"此页面提供完整的系统测试功能，包括功能测试、性能测试和用户体验测试。通过自动化测试确保系统的稳定性、性能和用户体验。\"\n      type=\"info\"\n      show-icon\n      style=\"margin-bottom: 16px;\"\n    />\n    \n    <!-- 测试控制面板 -->\n    <div class=\"test-control-panel\" style=\"margin-bottom: 24px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"12\">\n          <a-statistic-group>\n            <a-statistic title=\"测试套件\" :value=\"testSuiteCount\" />\n            <a-statistic title=\"测试用例\" :value=\"totalTestCases\" />\n            <a-statistic title=\"已完成\" :value=\"completedTests\" />\n            <a-statistic title=\"成功率\" :value=\"successRate\" suffix=\"%\" />\n          </a-statistic-group>\n        </a-col>\n        <a-col :span=\"12\">\n          <div class=\"test-actions\">\n            <a-button \n              type=\"primary\" \n              size=\"large\"\n              :loading=\"isRunning\"\n              :disabled=\"isRunning\"\n              @click=\"runAllTests\"\n              icon=\"play-circle\">\n              运行全部测试\n            </a-button>\n            <a-button \n              :disabled=\"!hasResults\"\n              @click=\"exportReport\"\n              icon=\"download\"\n              style=\"margin-left: 8px;\">\n              导出报告\n            </a-button>\n            <a-button \n              :disabled=\"!isRunning\"\n              @click=\"stopTests\"\n              icon=\"stop\"\n              style=\"margin-left: 8px;\">\n              停止测试\n            </a-button>\n            <a-button \n              :disabled=\"!hasResults\"\n              @click=\"clearResults\"\n              icon=\"clear\"\n              style=\"margin-left: 8px;\">\n              清空结果\n            </a-button>\n          </div>\n        </a-col>\n      </a-row>\n    </div>\n\n    <!-- 测试进度 -->\n    <div v-if=\"isRunning\" class=\"test-progress\" style=\"margin-bottom: 24px;\">\n      <a-card title=\"测试进度\" size=\"small\">\n        <a-progress \n          :percent=\"testProgress.progress\" \n          :status=\"testProgress.status\"\n          :strokeWidth=\"8\"\n        />\n        <div style=\"margin-top: 12px;\">\n          <a-tag color=\"blue\">{{ testProgress.phase }}</a-tag>\n          <span style=\"margin-left: 8px;\">{{ testProgress.message }}</span>\n        </div>\n        <div v-if=\"testProgress.currentSuite\" style=\"margin-top: 8px;\">\n          <strong>当前套件：</strong>{{ testProgress.currentSuite }}\n        </div>\n      </a-card>\n    </div>\n  </a-card>\n\n  <!-- 测试套件选择 -->\n  <a-card title=\"测试套件配置\" style=\"margin-bottom: 16px;\">\n    <a-row :gutter=\"16\">\n      <a-col :span=\"8\">\n        <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('functional') }\">\n          <template slot=\"title\">\n            <a-checkbox \n              v-model=\"selectedSuites\" \n              value=\"functional\"\n              @change=\"updateTestCaseCount\">\n              功能测试套件\n            </a-checkbox>\n          </template>\n          <p>测试系统核心功能的正确性和完整性</p>\n          <ul style=\"font-size: 12px; color: #666;\">\n            <li>图片支持测试</li>\n            <li>数学公式测试</li>\n            <li>导入导出测试</li>\n            <li>兼容性测试</li>\n          </ul>\n          <a-tag color=\"green\">{{ functionalTestCount }} 个测试用例</a-tag>\n        </a-card>\n      </a-col>\n      <a-col :span=\"8\">\n        <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('performance') }\">\n          <template slot=\"title\">\n            <a-checkbox \n              v-model=\"selectedSuites\" \n              value=\"performance\"\n              @change=\"updateTestCaseCount\">\n              性能测试套件\n            </a-checkbox>\n          </template>\n          <p>测试系统各组件的性能表现</p>\n          <ul style=\"font-size: 12px; color: #666;\">\n            <li>图片加载性能</li>\n            <li>编辑器性能</li>\n            <li>数据处理性能</li>\n            <li>内存使用监控</li>\n          </ul>\n          <a-tag color=\"orange\">{{ performanceTestCount }} 个测试用例</a-tag>\n        </a-card>\n      </a-col>\n      <a-col :span=\"8\">\n        <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('userExperience') }\">\n          <template slot=\"title\">\n            <a-checkbox \n              v-model=\"selectedSuites\" \n              value=\"userExperience\"\n              @change=\"updateTestCaseCount\">\n              用户体验测试套件\n            </a-checkbox>\n          </template>\n          <p>测试系统的用户界面和交互体验</p>\n          <ul style=\"font-size: 12px; color: #666;\">\n            <li>界面流畅性</li>\n            <li>响应式设计</li>\n            <li>交互体验</li>\n            <li>可访问性</li>\n          </ul>\n          <a-tag color=\"purple\">{{ userExperienceTestCount }} 个测试用例</a-tag>\n        </a-card>\n      </a-col>\n    </a-row>\n  </a-card>\n\n  <!-- 测试结果概览 -->\n  <a-card v-if=\"hasResults\" title=\"测试结果概览\" style=\"margin-bottom: 16px;\">\n    <a-row :gutter=\"16\">\n      <a-col :span=\"6\">\n        <a-statistic \n          title=\"总测试数\" \n          :value=\"testSummary.totalTests\"\n          :value-style=\"{ color: '#1890ff' }\"\n        />\n      </a-col>\n      <a-col :span=\"6\">\n        <a-statistic \n          title=\"通过测试\" \n          :value=\"testSummary.totalPassed\"\n          :value-style=\"{ color: '#52c41a' }\"\n        />\n      </a-col>\n      <a-col :span=\"6\">\n        <a-statistic \n          title=\"失败测试\" \n          :value=\"testSummary.totalFailed\"\n          :value-style=\"{ color: '#ff4d4f' }\"\n        />\n      </a-col>\n      <a-col :span=\"6\">\n        <a-statistic \n          title=\"跳过测试\" \n          :value=\"testSummary.totalSkipped\"\n          :value-style=\"{ color: '#faad14' }\"\n        />\n      </a-col>\n    </a-row>\n\n    <!-- 测试结果图表 -->\n    <div style=\"margin-top: 24px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"12\">\n          <div class=\"test-chart\">\n            <h4>测试结果分布</h4>\n            <div class=\"result-chart\" ref=\"resultChart\" style=\"height: 200px;\"></div>\n          </div>\n        </a-col>\n        <a-col :span=\"12\">\n          <div class=\"performance-chart\">\n            <h4>性能指标</h4>\n            <div class=\"performance-metrics\">\n              <a-progress \n                type=\"circle\" \n                :percent=\"Math.round(testSummary.successRate)\"\n                :status=\"testSummary.successRate >= 80 ? 'success' : 'exception'\"\n                :width=\"80\"\n              />\n              <div style=\"margin-left: 16px;\">\n                <p><strong>总耗时：</strong>{{ formatDuration(testSummary.totalDuration) }}</p>\n                <p><strong>平均耗时：</strong>{{ formatDuration(testSummary.totalDuration / testSummary.totalTests) }}</p>\n                <p><strong>性能评分：</strong>{{ getPerformanceScore() }}</p>\n              </div>\n            </div>\n          </div>\n        </a-col>\n      </a-row>\n    </div>\n  </a-card>\n\n  <!-- 详细测试结果 -->\n  <a-card v-if=\"hasResults\" title=\"详细测试结果\">\n    <a-tabs>\n      <a-tab-pane \n        v-for=\"(suiteResult, suiteName) in testResults\" \n        :key=\"suiteName\" \n        :tab=\"`${getSuiteDisplayName(suiteName)} (${suiteResult.passed}/${suiteResult.tests.length})`\">\n        \n        <!-- 套件概览 -->\n        <div style=\"margin-bottom: 16px;\">\n          <a-descriptions bordered size=\"small\">\n            <a-descriptions-item label=\"套件状态\">\n              <a-tag :color=\"suiteResult.status === 'passed' ? 'green' : 'red'\">\n                {{ suiteResult.status === 'passed' ? '通过' : '失败' }}\n              </a-tag>\n            </a-descriptions-item>\n            <a-descriptions-item label=\"执行时间\">{{ formatDuration(suiteResult.duration) }}</a-descriptions-item>\n            <a-descriptions-item label=\"通过率\">{{ Math.round((suiteResult.passed / suiteResult.tests.length) * 100) }}%</a-descriptions-item>\n          </a-descriptions>\n        </div>\n\n        <!-- 测试用例列表 -->\n        <a-table\n          :dataSource=\"suiteResult.tests\"\n          :columns=\"testCaseColumns\"\n          :pagination=\"{ pageSize: 10 }\"\n          size=\"small\"\n          rowKey=\"name\">\n          \n          <template slot=\"status\" slot-scope=\"text, record\">\n            <a-tag :color=\"getStatusColor(record.status)\">\n              {{ getStatusText(record.status) }}\n            </a-tag>\n          </template>\n          \n          <template slot=\"duration\" slot-scope=\"text\">\n            {{ formatDuration(text) }}\n          </template>\n          \n          <template slot=\"metrics\" slot-scope=\"text, record\">\n            <a-button \n              v-if=\"Object.keys(record.metrics).length > 0\"\n              type=\"link\" \n              size=\"small\"\n              @click=\"showMetricsDetail(record)\">\n              查看指标\n            </a-button>\n          </template>\n          \n          <template slot=\"error\" slot-scope=\"text\">\n            <a-tooltip v-if=\"text\" :title=\"text\">\n              <a-icon type=\"exclamation-circle\" style=\"color: #ff4d4f;\" />\n            </a-tooltip>\n          </template>\n        </a-table>\n      </a-tab-pane>\n    </a-tabs>\n  </a-card>\n\n  <!-- 性能问题和建议 -->\n  <a-card v-if=\"hasResults && (testSummary.performanceIssues.length > 0 || testSummary.recommendations.length > 0)\" title=\"问题和建议\">\n    <a-row :gutter=\"16\">\n      <a-col :span=\"12\" v-if=\"testSummary.performanceIssues.length > 0\">\n        <h4>性能问题</h4>\n        <a-list\n          :dataSource=\"testSummary.performanceIssues\"\n          size=\"small\">\n          <a-list-item slot=\"renderItem\" slot-scope=\"issue\">\n            <a-list-item-meta>\n              <template slot=\"title\">\n                <a-tag color=\"orange\">{{ issue.type }}</a-tag>\n                {{ issue.test }}\n              </template>\n              <template slot=\"description\">\n                {{ issue.message }}\n              </template>\n            </a-list-item-meta>\n          </a-list-item>\n        </a-list>\n      </a-col>\n      <a-col :span=\"12\" v-if=\"testSummary.recommendations.length > 0\">\n        <h4>优化建议</h4>\n        <a-list\n          :dataSource=\"testSummary.recommendations\"\n          size=\"small\">\n          <a-list-item slot=\"renderItem\" slot-scope=\"recommendation\">\n            <a-list-item-meta>\n              <template slot=\"title\">\n                <a-tag :color=\"getPriorityColor(recommendation.priority)\">\n                  {{ recommendation.priority }}\n                </a-tag>\n                {{ recommendation.type }}\n              </template>\n              <template slot=\"description\">\n                {{ recommendation.message }}\n              </template>\n            </a-list-item-meta>\n          </a-list-item>\n        </a-list>\n      </a-col>\n    </a-row>\n  </a-card>\n\n  <!-- 指标详情模态框 -->\n  <a-modal\n    v-model=\"metricsModalVisible\"\n    title=\"测试指标详情\"\n    :footer=\"null\"\n    width=\"600px\">\n    <div v-if=\"selectedTestMetrics\">\n      <h4>{{ selectedTestMetrics.name }}</h4>\n      <a-descriptions bordered size=\"small\">\n        <a-descriptions-item \n          v-for=\"(value, key) in selectedTestMetrics.metrics\" \n          :key=\"key\"\n          :label=\"formatMetricLabel(key)\">\n          {{ formatMetricValue(key, value) }}\n        </a-descriptions-item>\n      </a-descriptions>\n    </div>\n  </a-modal>\n</div>\n", null]}