{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniProgress.vue?vue&type=style&index=0&id=43fe0de5&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniProgress.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.chart-mini-progress {\n  padding: 5px 0;\n  position: relative;\n  width: 100%;\n\n  .target {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n\n    span {\n      border-radius: 100px;\n      position: absolute;\n      top: 0;\n      left: 0;\n      height: 4px;\n      width: 2px;\n\n      &:last-child {\n        top: auto;\n        bottom: 0;\n      }\n    }\n  }\n  .progress-wrapper {\n    background-color: #f5f5f5;\n    position: relative;\n\n    .progress {\n      transition: all .4s cubic-bezier(.08, .82, .17, 1) 0s;\n      border-radius: 1px 0 0 1px;\n      background-color: #1890ff;\n      width: 0;\n      height: 100%;\n    }\n  }\n}\n", {"version": 3, "sources": ["MiniProgress.vue"], "names": [], "mappings": ";AA2EA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MiniProgress.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div class=\"chart-mini-progress\">\n    <div class=\"target\" :style=\"{ left: target + '%'}\">\n      <span :style=\"{ backgroundColor: color }\"/>\n      <span :style=\"{ backgroundColor: color }\"/>\n    </div>\n    <div class=\"progress-wrapper\">\n      <div class=\"progress\" :style=\"{ backgroundColor: color, width: percentage + '%', height: height+'px' }\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'MiniProgress',\n    props: {\n      target: {\n        type: Number,\n        default: 0\n      },\n      height: {\n        type: Number,\n        default: 10\n      },\n      color: {\n        type: String,\n        default: '#13C2C2'\n      },\n      percentage: {\n        type: Number,\n        default: 0\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .chart-mini-progress {\n    padding: 5px 0;\n    position: relative;\n    width: 100%;\n\n    .target {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n\n      span {\n        border-radius: 100px;\n        position: absolute;\n        top: 0;\n        left: 0;\n        height: 4px;\n        width: 2px;\n\n        &:last-child {\n          top: auto;\n          bottom: 0;\n        }\n      }\n    }\n    .progress-wrapper {\n      background-color: #f5f5f5;\n      position: relative;\n\n      .progress {\n        transition: all .4s cubic-bezier(.08, .82, .17, 1) 0s;\n        border-radius: 1px 0 0 1px;\n        background-color: #1890ff;\n        width: 0;\n        height: 100%;\n      }\n    }\n  }\n</style>"]}]}