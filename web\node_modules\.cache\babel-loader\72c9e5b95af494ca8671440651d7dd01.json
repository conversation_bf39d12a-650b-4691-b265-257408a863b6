{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport PageLayout from '@/components/page/PageLayout';\nimport RouteView from \"@/components/layouts/RouteView\";\nimport { mixinDevice } from '@/utils/mixin.js';\nimport { mapGetters } from 'vuex';\nexport default {\n  components: {\n    RouteView: RouteView,\n    PageLayout: PageLayout\n  },\n  mixins: [mixinDevice],\n  data: function data() {\n    return {\n      // horizontal  inline\n      mode: 'inline',\n      mainInfoHeight: \"100%\",\n      openKeys: [],\n      defaultSelectedKeys: [],\n      // cropper\n      preview: {},\n      option: {\n        img: '/avatar2.jpg',\n        info: true,\n        size: 1,\n        outputType: 'jpeg',\n        canScale: false,\n        autoCrop: true,\n        // 只有自动截图开启 宽度高度才生效\n        autoCropWidth: 180,\n        autoCropHeight: 180,\n        fixedBox: true,\n        // 开启宽度和高度比例\n        fixed: true,\n        fixedNumber: [1, 1]\n      },\n      pageTitle: ''\n    };\n  },\n  created: function created() {\n    this.updateMenu();\n  },\n  mounted: function mounted() {\n    this.mainInfoHeight = window.innerHeight - 285 + \"px\";\n  },\n  methods: _objectSpread(_objectSpread({}, mapGetters(['userInfo'])), {}, {\n    onOpenChange: function onOpenChange(openKeys) {\n      this.openKeys = openKeys;\n    },\n    updateMenu: function updateMenu() {\n      var routes = this.$route.matched.concat();\n      this.defaultSelectedKeys = [routes.pop().path];\n    }\n  })\n};", {"version": 3, "names": ["PageLayout", "RouteView", "mixinDevice", "mapGetters", "components", "mixins", "data", "mode", "mainInfoHeight", "openKeys", "defaultSelectedKeys", "preview", "option", "img", "info", "size", "outputType", "canScale", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "fixed", "fixedNumber", "pageTitle", "created", "updateMenu", "mounted", "window", "innerHeight", "methods", "_objectSpread", "onOpenChange", "routes", "$route", "matched", "concat", "pop", "path"], "sources": ["src/views/account/settings/Index.vue"], "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n      <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n        <div class=\"account-settings-info-left\">\n          <a-menu\n            :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n            :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n            :defaultSelectedKeys=\"defaultSelectedKeys\"\n            type=\"inner\"\n            @openChange=\"onOpenChange\"\n          >\n            <a-menu-item key=\"/account/settings/base\">\n              <router-link :to=\"{ name: 'account-settings-base' }\">\n                个人设置\n              </router-link>\n            </a-menu-item>\n            <a-menu-item key=\"/account/settings/password\">\n              <router-link :to=\"{ name: 'account-settings-password' }\">\n                修改密码\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/security\">\n              <router-link :to=\"{ name: 'account-settings-security' }\">\n                安全设置\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/custom\">\n              <router-link :to=\"{ name: 'account-settings-custom' }\">\n                个性化\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/binding\">\n              <router-link :to=\"{ name: 'account-settings-binding' }\">\n                账户绑定\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/notification\">\n              <router-link :to=\"{ name: 'account-settings-notification' }\">\n                新消息通知\n              </router-link>\n            </a-menu-item>\n          </a-menu>\n        </div>\n        <div class=\"account-settings-info-right\">\n          <div class=\"account-settings-info-title\">\n            <span>{{ $route.meta.title }}</span>\n          </div>\n          <route-view></route-view>\n        </div>\n      </div>\n    </a-card>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { mixinDevice } from '@/utils/mixin.js'\n  import { mapGetters } from 'vuex'\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout\n    },\n    mixins: [mixinDevice],\n    data () {\n      return {\n        // horizontal  inline\n        mode: 'inline',\n        mainInfoHeight:\"100%\",\n        openKeys: [],\n        defaultSelectedKeys: [],\n\n        // cropper\n        preview: {},\n        option: {\n          img: '/avatar2.jpg',\n          info: true,\n          size: 1,\n          outputType: 'jpeg',\n          canScale: false,\n          autoCrop: true,\n          // 只有自动截图开启 宽度高度才生效\n          autoCropWidth: 180,\n          autoCropHeight: 180,\n          fixedBox: true,\n          // 开启宽度和高度比例\n          fixed: true,\n          fixedNumber: [1, 1]\n        },\n\n        pageTitle: ''\n      }\n    },\n    created () {\n      this.updateMenu()\n    },\n    mounted(){\n      this.mainInfoHeight = (window.innerHeight-285)+\"px\";\n    },\n    methods: {\n      ...mapGetters(['userInfo']),\n      onOpenChange (openKeys) {\n        this.openKeys = openKeys\n      },\n      updateMenu () {\n        let routes = this.$route.matched.concat()\n        this.defaultSelectedKeys = [ routes.pop().path ]\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .account-settings-info-main {\n    width: 100%;\n    display: flex;\n    height: 100%;\n    overflow: auto;\n\n    &.mobile {\n      display: block;\n\n      .account-settings-info-left {\n        border-right: unset;\n        border-bottom: 1px solid #e8e8e8;\n        width: 100%;\n        height: 50px;\n        overflow-x: auto;\n        overflow-y: scroll;\n      }\n      .account-settings-info-right {\n        padding: 20px 40px;\n      }\n    }\n\n    .account-settings-info-left {\n      border-right: 1px solid #e8e8e8;\n      width: 224px;\n    }\n\n    .account-settings-info-right {\n      flex: 1 1;\n      padding: 8px 40px;\n\n      .account-settings-info-title {\n        color: rgba(0,0,0,.85);\n        font-size: 20px;\n        font-weight: 500;\n        line-height: 28px;\n        margin-bottom: 12px;\n      }\n      .account-settings-info-view {\n        padding-top: 12px;\n      }\n    }\n  }\n\n</style>"], "mappings": ";;;;;;AAwDA,OAAAA,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,WAAA;AACA,SAAAC,UAAA;AAEA;EACAC,UAAA;IACAH,SAAA,EAAAA,SAAA;IACAD,UAAA,EAAAA;EACA;EACAK,MAAA,GAAAH,WAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACAC,cAAA;MACAC,QAAA;MACAC,mBAAA;MAEA;MACAC,OAAA;MACAC,MAAA;QACAC,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACA;QACAC,aAAA;QACAC,cAAA;QACAC,QAAA;QACA;QACAC,KAAA;QACAC,WAAA;MACA;MAEAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAnB,cAAA,GAAAoB,MAAA,CAAAC,WAAA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACA5B,UAAA;IACA6B,YAAA,WAAAA,aAAAvB,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IACAiB,UAAA,WAAAA,WAAA;MACA,IAAAO,MAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA;MACA,KAAA1B,mBAAA,IAAAuB,MAAA,CAAAI,GAAA,GAAAC,IAAA;IACA;EAAA;AAEA", "ignoreList": []}]}