{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Custom.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Custom.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import { colorList } from '@/components/tools/setting';\nimport ASwitch from 'ant-design-vue/es/switch';\nimport AList from \"ant-design-vue/es/list\";\nimport AListItem from \"ant-design-vue/es/list/Item\";\nimport { mixin } from '@/utils/mixin.js';\nvar Meta = AListItem.Meta;\nexport default {\n  components: {\n    AListItem: AListItem,\n    AList: AList,\n    ASwitch: ASwitch,\n    Meta: Meta\n  },\n  mixins: [mixin],\n  data: function data() {\n    return {};\n  },\n  filters: {\n    themeFilter: function themeFilter(theme) {\n      var themeMap = {\n        'dark': '暗色',\n        'light': '白色'\n      };\n      return themeMap[theme];\n    }\n  },\n  methods: {\n    colorFilter: function colorFilter(color) {\n      var c = colorList.filter(function (o) {\n        return o.color === color;\n      })[0];\n      return c && c.key;\n    },\n    onChange: function onChange(checked) {\n      if (checked) {\n        this.$store.dispatch('ToggleTheme', 'dark');\n      } else {\n        this.$store.dispatch('ToggleTheme', 'light');\n      }\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    return h(AList, {\n      \"attrs\": {\n        \"itemLayout\": \"horizontal\"\n      }\n    }, [h(AListItem, [h(Meta, [h(\"a\", {\n      \"slot\": \"title\"\n    }, [\"\\u98CE\\u683C\\u914D\\u8272\"]), h(\"span\", {\n      \"slot\": \"description\"\n    }, [\"\\u6574\\u4F53\\u98CE\\u683C\\u914D\\u8272\\u8BBE\\u7F6E\"])]), h(\"div\", {\n      \"slot\": \"actions\"\n    }, [h(ASwitch, {\n      \"attrs\": {\n        \"checkedChildren\": \"暗色\",\n        \"unCheckedChildren\": \"白色\",\n        \"defaultChecked\": this.navTheme === 'dark' && true || false\n      },\n      \"on\": {\n        \"change\": this.onChange\n      }\n    })])]), h(AListItem, [h(Meta, [h(\"a\", {\n      \"slot\": \"title\"\n    }, [\"\\u4E3B\\u9898\\u8272\"]), h(\"span\", {\n      \"slot\": \"description\"\n    }, [\"\\u9875\\u9762\\u98CE\\u683C\\u914D\\u8272\\uFF1A \", h(\"a\", {\n      \"domProps\": {\n        \"innerHTML\": this.colorFilter(this.primaryColor)\n      }\n    })])])])]);\n  }\n};", {"version": 3, "names": ["colorList", "ASwitch", "AList", "AListItem", "mixin", "Meta", "components", "mixins", "data", "filters", "themeFilter", "theme", "themeMap", "methods", "colorFilter", "color", "c", "filter", "o", "key", "onChange", "checked", "$store", "dispatch", "render", "h", "arguments", "navTheme", "primaryColor"], "sources": ["src/views/account/settings/Custom.vue"], "sourcesContent": ["<script>\n  import { colorList } from '@/components/tools/setting'\n  import ASwitch from 'ant-design-vue/es/switch'\n  import AList from \"ant-design-vue/es/list\"\n  import AListItem from \"ant-design-vue/es/list/Item\"\n  import { mixin } from '@/utils/mixin.js'\n\n  const Meta = AListItem.Meta\n\n  export default {\n    components: {\n      AListItem,\n      AList,\n      ASwitch,\n      Meta\n    },\n    mixins: [mixin],\n    data () {\n      return {\n      }\n    },\n    filters: {\n      themeFilter(theme) {\n        const themeMap = {\n          'dark': '暗色',\n          'light': '白色'\n        }\n        return themeMap[theme]\n      },\n    },\n    methods: {\n      colorFilter(color) {\n        const c = colorList.filter(o => o.color === color)[0]\n        return c && c.key\n      },\n\n      onChange (checked) {\n        if (checked) {\n          this.$store.dispatch('ToggleTheme',  'dark')\n        } else {\n          this.$store.dispatch('ToggleTheme',  'light')\n        }\n      }\n    },\n    render () {\n      return (\n        <AList itemLayout=\"horizontal\">\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">风格配色</a>\n              <span slot=\"description\">\n                整体风格配色设置\n              </span>\n            </Meta>\n            <div slot=\"actions\">\n              <ASwitch checkedChildren=\"暗色\" unCheckedChildren=\"白色\" defaultChecked={this.navTheme === 'dark' && true || false} onChange={this.onChange} />\n            </div>\n          </AListItem>\n          <AListItem>\n            <Meta>\n              <a slot=\"title\">主题色</a>\n              <span slot=\"description\">\n                页面风格配色： <a domPropsInnerHTML={ this.colorFilter(this.primaryColor) }/>\n              </span>\n            </Meta>\n          </AListItem>\n        </AList>\n      )\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AACA,SAAAA,SAAA;AACA,OAAAC,OAAA;AACA,OAAAC,KAAA;AACA,OAAAC,SAAA;AACA,SAAAC,KAAA;AAEA,IAAAC,IAAA,GAAAF,SAAA,CAAAE,IAAA;AAEA;EACAC,UAAA;IACAH,SAAA,EAAAA,SAAA;IACAD,KAAA,EAAAA,KAAA;IACAD,OAAA,EAAAA,OAAA;IACAI,IAAA,EAAAA;EACA;EACAE,MAAA,GAAAH,KAAA;EACAI,IAAA,WAAAA,KAAA;IACA,QACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;EACA;EACAE,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAC,CAAA,GAAAhB,SAAA,CAAAiB,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAH,KAAA,KAAAA,KAAA;MAAA;MACA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA;IACA;IAEAC,QAAA,WAAAA,SAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAC,MAAA,CAAAC,QAAA;MACA;QACA,KAAAD,MAAA,CAAAC,QAAA;MACA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IAAA,IAAAC,CAAA,GAAAC,SAAA;IACA,OAAAD,CAAA,CAAAvB,KAAA;MAAA;QAAA,cACA;MAAA;IAAA,IAAAuB,CAAA,CAAAtB,SAAA,GAAAsB,CAAA,CAAApB,IAAA,GAAAoB,CAAA;MAAA,QAGA;IAAA,kCAAAA,CAAA;MAAA,QACA;IAAA,4DAAAA,CAAA;MAAA,QAIA;IAAA,IAAAA,CAAA,CAAAxB,OAAA;MAAA;QAAA,mBACA;QAAA;QAAA,uBAAA0B,QAAA;MAAA;MAAA;QAAA,eAAAP;MAAA;IAAA,QAAAK,CAAA,CAAAtB,SAAA,GAAAsB,CAAA,CAAApB,IAAA,GAAAoB,CAAA;MAAA,QAKA;IAAA,4BAAAA,CAAA;MAAA,QACA;IAAA,mDAAAA,CAAA;MAAA;QAAA,aACA,KAAAX,WAAA,MAAAc,YAAA;MAAA;IAAA;EAMA;AACA", "ignoreList": []}]}