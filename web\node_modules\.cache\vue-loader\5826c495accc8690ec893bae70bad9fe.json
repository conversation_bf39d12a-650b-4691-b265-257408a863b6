{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\problemManage.vue?vue&type=template&id=0062185d&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\problemManage.vue", "mtime": 1753517009971}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"24\">\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"题目标题\">\n            <a-input placeholder=\"请输入题目标题\" v-model=\"queryParam.title\"></a-input>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"题目类型\">\n            <a-select placeholder=\"请选择题目类型\" v-model=\"queryParam.questionType\" allowClear>\n              <a-select-option :value=\"1\">单选题</a-select-option>\n              <a-select-option :value=\"2\">判断题</a-select-option>\n              <a-select-option :value=\"3\">编程题</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"所属科目\">\n            <a-select placeholder=\"请选择所属科目\" v-model=\"queryParam.subject\" allowClear>\n              <a-select-option value=\"Scratch\">Scratch</a-select-option>\n              <a-select-option value=\"Python\">Python</a-select-option>\n              <a-select-option value=\"C++\">C++</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"题目级别\">\n            <a-select placeholder=\"请选择题目级别\" v-model=\"queryParam.level\" allowClear>\n              <a-select-option v-for=\"(level, index) in getLevelOptions()\" :key=\"index\" :value=\"level\">\n                {{ level }}\n              </a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <a-form-item label=\"难度\">\n            <a-select placeholder=\"请选择难度\" v-model=\"queryParam.difficulty\" allowClear>\n              <a-select-option :value=\"1\">简单</a-select-option>\n              <a-select-option :value=\"2\">中等</a-select-option>\n              <a-select-option :value=\"3\">困难</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"6\" :sm=\"8\">\n          <span class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"searchQuery\">查询</a-button>\n            <a-button style=\"margin-left: 8px\" @click=\"searchReset\">重置</a-button>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n\n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\">\n    <a-button type=\"primary\" icon=\"plus\" @click=\"handleAdd\">新增题目</a-button>\n    <a-button type=\"primary\" icon=\"cloud-upload\" @click=\"handleImport\">批量导入</a-button>\n    <a-button type=\"primary\" icon=\"cloud-download\" @click=\"handleExport\">批量导出</a-button>\n    <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n      <a-menu slot=\"overlay\">\n        <a-menu-item key=\"1\" @click=\"batchDel\">\n          <a-icon type=\"delete\" />删除\n        </a-menu-item>\n      </a-menu>\n      <a-button style=\"margin-left: 8px\">\n        批量操作 <a-icon type=\"down\" />\n      </a-button>\n    </a-dropdown>\n  </div>\n\n  <!-- 表格区域 -->\n  <a-table\n    ref=\"table\"\n    size=\"middle\"\n    bordered\n    rowKey=\"id\"\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :pagination=\"ipagination\"\n    :loading=\"loading\"\n    :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n    @change=\"handleTableChange\">\n    \n    <!-- 自定义标题展示 -->\n    <template slot=\"titleSlot\" slot-scope=\"text, record\">\n      <span :title=\"getQuestionTitle(record)\">{{ getQuestionTitle(record) }}</span>\n    </template>\n\n    <!-- 自定义题目类型展示 -->\n    <template slot=\"questionTypeSlot\" slot-scope=\"text\">\n      <a-tag color=\"blue\" v-if=\"text === 1\">单选题</a-tag>\n      <a-tag color=\"green\" v-else-if=\"text === 2\">判断题</a-tag>\n      <a-tag color=\"purple\" v-else-if=\"text === 3\">编程题</a-tag>\n      <a-tag v-else>未知题型</a-tag>\n    </template>\n    \n    <!-- 自定义难度展示 -->\n    <template slot=\"difficultySlot\" slot-scope=\"text\">\n      <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n      <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n      <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n      <a-tag v-else>未知</a-tag>\n    </template>\n    \n    <!-- 操作列 -->\n    <span slot=\"action\" slot-scope=\"text, record\">\n      <a @click=\"handleEdit(record)\">编辑</a>\n      <a-divider type=\"vertical\" />\n      <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n        <a>删除</a>\n      </a-popconfirm>\n    </span>\n  </a-table>\n\n  <!-- 表单模态框 -->\n  <question-modal ref=\"modalForm\" @ok=\"modalFormOk\"></question-modal>\n  \n  <!-- 导入模态框 -->\n  <a-modal\n    title=\"批量导入题目\"\n    :width=\"800\"\n    :visible=\"importModalVisible\"\n    :maskClosable=\"false\"\n    :confirmLoading=\"importConfirmLoading\"\n    @ok=\"handleImportOk\"\n    @cancel=\"handleImportCancel\"\n  >\n    <!-- 简化的批量导入说明 -->\n      <a-alert\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px\"\n      >\n        <div slot=\"message\">\n          <span>批量导入题目说明</span>\n          <a-tooltip placement=\"right\" overlayClassName=\"import-help-tooltip\">\n            <template slot=\"title\">\n              <div style=\"max-width: 400px;\">\n                <div><strong>标准导入流程详解：</strong></div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤1：获取纯文本模板</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    点击\"下载模板\"获取【纯文本模板】文件\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤2：填写题目数据</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    在【纯文本模板】中按格式填写您的题目内容\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤3：自动格式化</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    点击\"自动格式化\"上传填好的【纯文本模板】<br/>\n                    填写科目、级别、难度等元数据<br/>\n                    下载生成的【格式化题目文件】\n                  </div>\n                </div>\n                <div style=\"margin-top: 8px;\">\n                  <div><strong>步骤4：批量导入</strong></div>\n                  <div style=\"margin-left: 12px; color: #ccc; font-size: 12px;\">\n                    使用【格式化题目文件】进行批量导入\n                  </div>\n                </div>\n              </div>\n            </template>\n            <a-icon\n              type=\"question-circle\"\n              style=\"margin-left: 8px; color: #1890ff; cursor: help;\"\n            />\n          </a-tooltip>\n        </div>\n        <div slot=\"description\">\n          <div style=\"padding: 8px;\">\n            <strong>💡 完整流程</strong>：【纯文本模板】→ 填写数据 → 【自动格式化】→ 【格式化题目文件】→ 批量导入\n          </div>\n        </div>\n      </a-alert>\n      \n    <!-- 减少分隔线上下间距 -->\n    <a-divider style=\"margin: 4px 0\" />\n    \n    <!-- 文件上传区域 -->\n    <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n      <input\n        ref=\"fileInput\"\n        type=\"file\"\n        accept=\".txt\"\n        @change=\"onFileChange\"\n        style=\"display: none\"\n      />\n      <div \n        class=\"upload-drop-area\" \n        @click=\"triggerFileInput\"\n        @dragover.prevent\n        @dragenter.prevent=\"handleDragEnter\"\n        @dragleave.prevent=\"handleDragLeave\"\n        @drop.prevent=\"handleDrop\"\n        :class=\"{'is-dragover': isDragover}\"\n        style=\"padding: 16px 24px;\"\n      >\n        <a-icon type=\"cloud-upload\" class=\"upload-icon\" />\n        <div class=\"upload-text\" style=\"margin: 8px 0;\">\n          <span v-if=\"!selectedFile\">点击或拖拽文件到此区域上传</span>\n          <span v-else class=\"selected-file\">\n            <a-icon type=\"file-text\" /> {{ selectedFile.name }}\n            <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeSelectedFile\" />\n          </span>\n        </div>\n        <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerFileInput\" style=\"margin-top: 8px;\">\n          选择文件\n      </a-button>\n      </div>\n      <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n        <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，遵循标准格式化题目文件格式\n        <a class=\"template-link\" @click=\"downloadTemplate\">下载模板</a>\n        <a-divider type=\"vertical\" />\n        <a class=\"template-link\" @click=\"showAutoTemplateModal\">自动格式化</a>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 自动格式化模态框 -->\n  <a-modal\n    title=\"自动格式化\"\n    :width=\"600\"\n    :visible=\"autoTemplateModalVisible\"\n    :maskClosable=\"false\"\n    :confirmLoading=\"autoTemplateConfirmLoading\"\n    @ok=\"handleAutoTemplateOk\"\n    @cancel=\"handleAutoTemplateCancel\"\n  >\n    <a-alert\n      type=\"info\"\n      show-icon\n      style=\"margin-bottom: 16px\"\n      message=\"自动格式化说明\"\n    >\n      <div slot=\"description\">\n        <div><strong>功能说明</strong>：上传填写好题目数据的【纯文本模板】文件，系统将自动格式化为标准导入格式。</div>\n        <div>支持自动识别单选题、判断题、编程题，并添加相应的格式标记。</div>\n        <div>格式化后生成【格式化题目文件】，可直接用于批量导入。</div>\n      </div>\n    </a-alert>\n\n    <!-- 元数据输入区域 -->\n    <a-form :label-col=\"{ span: 6 }\" :wrapper-col=\"{ span: 18 }\" style=\"margin-bottom: 16px;\">\n      <a-form-item label=\"科目\">\n        <a-select\n          v-model=\"autoTemplateParam.subject\"\n          placeholder=\"请选择科目\"\n          allowClear\n        >\n          <a-select-option value=\"Scratch\">Scratch</a-select-option>\n          <a-select-option value=\"Python\">Python</a-select-option>\n          <a-select-option value=\"C++\">C++</a-select-option>\n        </a-select>\n      </a-form-item>\n      <a-form-item label=\"级别\">\n        <a-input v-model=\"autoTemplateParam.level\" placeholder=\"请输入级别，例如：1、2、3\" />\n      </a-form-item>\n      <a-form-item label=\"难度\">\n        <a-select\n          v-model=\"autoTemplateParam.difficulty\"\n          placeholder=\"请选择难度\"\n          allowClear\n        >\n          <a-select-option :value=\"1\">简单</a-select-option>\n          <a-select-option :value=\"2\">中等</a-select-option>\n          <a-select-option :value=\"3\">困难</a-select-option>\n        </a-select>\n      </a-form-item>\n    </a-form>\n\n    <a-divider style=\"margin: 16px 0\" />\n\n    <!-- 文件上传区域 -->\n    <div class=\"modern-upload-area\" style=\"margin: 0; padding: 8px;\">\n      <input\n        ref=\"autoTemplateFileInput\"\n        type=\"file\"\n        accept=\".txt\"\n        @change=\"onAutoTemplateFileChange\"\n        style=\"display: none\"\n      />\n      <div\n        class=\"upload-drop-area\"\n        @click=\"triggerAutoTemplateFileInput\"\n        @dragover.prevent\n        @dragenter.prevent=\"handleAutoTemplateDragEnter\"\n        @dragleave.prevent=\"handleAutoTemplateDragLeave\"\n        @drop.prevent=\"handleAutoTemplateDrop\"\n        :class=\"{'is-dragover': isAutoTemplateDragover}\"\n        style=\"padding: 16px 24px;\"\n      >\n        <a-icon type=\"file-text\" class=\"upload-icon\" />\n        <div class=\"upload-text\" style=\"margin: 8px 0;\">\n          <span v-if=\"!autoTemplateSelectedFile\">点击或拖拽纯文本文件到此区域上传</span>\n          <span v-else class=\"selected-file\">\n            <a-icon type=\"file-text\" /> {{ autoTemplateSelectedFile.name }}\n            <a-icon type=\"close-circle\" class=\"remove-file\" @click.stop=\"removeAutoTemplateSelectedFile\" />\n          </span>\n        </div>\n        <a-button type=\"primary\" size=\"small\" class=\"upload-button\" @click.stop=\"triggerAutoTemplateFileInput\" style=\"margin-top: 8px;\">\n          选择文件\n        </a-button>\n      </div>\n      <div class=\"upload-tip\" style=\"margin-top: 4px;\">\n        <a-icon type=\"info-circle\" /> 请上传UTF-8编码的.txt文件，系统将自动格式化为导入模板格式\n      </div>\n    </div>\n  </a-modal>\n</a-card>\n", null]}