{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframeFReportView.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\layouts\\IframeFReportView.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import PageLayout from '../page/PageLayout'\n  import RouteView from './RouteView'\n\n  export default {\n    name: \"IframePageContent\",\n    data () {\n      return {\n        url: \"\",\n        id:\"\"\n      }\n    },\n    created () {\n      this.goUrl()\n    },\n    updated () {\n      this.goUrl()\n    },\n    watch: {\n      $route(to, from) {\n        this.goUrl();\n      }\n    },\n    methods: {\n      goUrl () {\n        let url = this.$route.meta.url\n        let id = this.$route.path\n        this.id = id\n        //url = \"http://www.baidu.com\"\n        console.log(\"------url------\"+url)\n        if (url !== null && url !== undefined) {\n          this.url = url;\n          //window.open(this.url);\n        }\n      }\n    }\n  }\n", {"version": 3, "sources": ["IframeFReportView.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IframeFReportView.vue", "sourceRoot": "src/components/layouts", "sourcesContent": ["<template>\n\n    <iframe  :id=\"id\" :src=\"url\" frameborder=\"0\" width=\"100%\" height=\"800px\" scrolling=\"auto\" style=\"background-color: #fff;\"></iframe>\n\n</template>\n\n<script>\n  import PageLayout from '../page/PageLayout'\n  import RouteView from './RouteView'\n\n  export default {\n    name: \"IframePageContent\",\n    data () {\n      return {\n        url: \"\",\n        id:\"\"\n      }\n    },\n    created () {\n      this.goUrl()\n    },\n    updated () {\n      this.goUrl()\n    },\n    watch: {\n      $route(to, from) {\n        this.goUrl();\n      }\n    },\n    methods: {\n      goUrl () {\n        let url = this.$route.meta.url\n        let id = this.$route.path\n        this.id = id\n        //url = \"http://www.baidu.com\"\n        console.log(\"------url------\"+url)\n        if (url !== null && url !== undefined) {\n          this.url = url;\n          //window.open(this.url);\n        }\n      }\n    }\n  }\n</script>\n\n<style>\n</style>"]}]}