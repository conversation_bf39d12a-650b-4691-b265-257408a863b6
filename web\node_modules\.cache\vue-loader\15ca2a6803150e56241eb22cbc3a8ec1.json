{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SoftwareDownload.vue", "mtime": 1749714201887}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./SoftwareDownload.vue?vue&type=template&id=b8ab2606&scoped=true\"\nimport script from \"./SoftwareDownload.vue?vue&type=script&lang=js\"\nexport * from \"./SoftwareDownload.vue?vue&type=script&lang=js\"\nimport style0 from \"./SoftwareDownload.vue?vue&type=style&index=0&id=b8ab2606&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b8ab2606\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('b8ab2606')) {\n      api.createRecord('b8ab2606', component.options)\n    } else {\n      api.reload('b8ab2606', component.options)\n    }\n    module.hot.accept(\"./SoftwareDownload.vue?vue&type=template&id=b8ab2606&scoped=true\", function () {\n      api.rerender('b8ab2606', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/home/<USER>/SoftwareDownload.vue\"\nexport default component.exports"]}