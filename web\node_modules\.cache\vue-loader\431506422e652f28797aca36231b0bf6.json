{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeDict.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeDict.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    data(){\n      return {\n        treeData:[],\n        treeValue: null,\n        url_root:\"/sys/category/loadTreeRoot\",\n        url_children:\"/sys/category/loadTreeChildren\",\n        url_view:'/sys/category/loadOne',\n      }\n    },\n    props:{\n      value:{\n        type: String,\n        required: false\n      },\n      placeholder:{\n        type: String,\n        default: '请选择',\n        required: false\n      },\n      parentCode:{\n        type: String,\n        default: '',\n        required: false\n      },\n      field:{\n        type: String,\n        default: 'id',\n        required: false\n      },\n      root:{\n        type:Object,\n        required:false,\n        default:()=>{\n          return {\n            pid:'0'\n          }\n        }\n      },\n      async:{\n        type:Boolean,\n        default:false,\n        required:false\n      },\n      disabled:{\n        type:Boolean,\n        default:false,\n        required:false\n      }\n    },\n    watch:{\n      root:{\n        handler(val){\n          console.log(\"root-change\",val)\n        },\n        deep:true\n      },\n      parentCode:{\n        handler(){\n          this.loadRoot()\n        }\n      },\n      value:{\n        handler(){\n          this.loadViewInfo()\n        }\n      }\n    },\n    created(){\n      this.loadRoot()\n      this.loadViewInfo()\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    methods:{\n      loadViewInfo(){\n        if(!this.value || this.value==\"0\"){\n          this.treeValue = null\n        }else{\n          let param = {\n            field:this.field,\n            val:this.value\n          }\n          getAction(this.url_view,param).then(res=>{\n            if(res.success){\n              this.treeValue = {\n                value:this.value,\n                label:res.result.name\n              }\n            }\n          })\n        }\n      },\n      loadRoot(){\n        let param = {\n          async:this.async,\n          pcode:this.parentCode\n        }\n        getAction(this.url_root,param).then(res=>{\n          if(res.success){\n            this.handleTreeNodeValue(res.result)\n            console.log(\"aaaa\",res.result)\n            this.treeData = [...res.result]\n          }else{\n            this.$message.error(res.message)\n          }\n        })\n      },\n      asyncLoadTreeData (treeNode) {\n        return new Promise((resolve) => {\n          if(!this.async){\n            resolve()\n            return\n          }\n          if (treeNode.$vnode.children) {\n            resolve()\n            return\n          }\n          let pid = treeNode.$vnode.key\n          let param = {\n            pid:pid\n          }\n          getAction(this.url_children,param).then(res=>{\n            if(res.success){\n              this.handleTreeNodeValue(res.result)\n              this.addChildren(pid,res.result,this.treeData)\n              this.treeData = [...this.treeData]\n            }\n            resolve()\n          })\n        })\n      },\n      addChildren(pid,children,treeArray){\n        if(treeArray && treeArray.length>0){\n          for(let item of treeArray){\n            if(item.key == pid){\n              if(!children || children.length==0){\n                item.leaf = true\n              }else{\n                item.children = children\n              }\n              break\n            }else{\n              this.addChildren(pid,children,item.children)\n            }\n          }\n        }\n      },\n      handleTreeNodeValue(result){\n        let storeField = this.field=='code'?'code':'key'\n        for(let i of result){\n          i.value = i[storeField]\n          i.isLeaf = (!i.leaf)?false:true\n          if(i.children && i.children.length>0){\n            this.handleTreeNodeValue(i.children)\n          }\n        }\n      },\n      onChange(value){\n        console.log(value)\n        if(!value){\n          this.$emit('change', '');\n        }else{\n          this.$emit('change', value.value);\n        }\n        this.treeValue = value\n      },\n      onSearch(value){\n        console.log(value)\n      },\n      getCurrTreeData(){\n        return this.treeData\n      }\n    }\n\n  }\n", {"version": 3, "sources": ["JTreeDict.vue"], "names": [], "mappings": ";AAiBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "JTreeDict.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <a-tree-select\n    allowClear\n    labelInValue\n    style=\"width: 100%\"\n    :disabled=\"disabled\"\n    :dropdownStyle=\"{ maxHeight: '400px', overflow: 'auto' }\"\n    :placeholder=\"placeholder\"\n    :loadData=\"asyncLoadTreeData\"\n    :value=\"treeValue\"\n    :treeData=\"treeData\"\n    @change=\"onChange\"\n    @search=\"onSearch\">\n  </a-tree-select>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n\n  export default {\n    name: 'JTreeDict',\n    data(){\n      return {\n        treeData:[],\n        treeValue: null,\n        url_root:\"/sys/category/loadTreeRoot\",\n        url_children:\"/sys/category/loadTreeChildren\",\n        url_view:'/sys/category/loadOne',\n      }\n    },\n    props:{\n      value:{\n        type: String,\n        required: false\n      },\n      placeholder:{\n        type: String,\n        default: '请选择',\n        required: false\n      },\n      parentCode:{\n        type: String,\n        default: '',\n        required: false\n      },\n      field:{\n        type: String,\n        default: 'id',\n        required: false\n      },\n      root:{\n        type:Object,\n        required:false,\n        default:()=>{\n          return {\n            pid:'0'\n          }\n        }\n      },\n      async:{\n        type:Boolean,\n        default:false,\n        required:false\n      },\n      disabled:{\n        type:Boolean,\n        default:false,\n        required:false\n      }\n    },\n    watch:{\n      root:{\n        handler(val){\n          console.log(\"root-change\",val)\n        },\n        deep:true\n      },\n      parentCode:{\n        handler(){\n          this.loadRoot()\n        }\n      },\n      value:{\n        handler(){\n          this.loadViewInfo()\n        }\n      }\n    },\n    created(){\n      this.loadRoot()\n      this.loadViewInfo()\n    },\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    methods:{\n      loadViewInfo(){\n        if(!this.value || this.value==\"0\"){\n          this.treeValue = null\n        }else{\n          let param = {\n            field:this.field,\n            val:this.value\n          }\n          getAction(this.url_view,param).then(res=>{\n            if(res.success){\n              this.treeValue = {\n                value:this.value,\n                label:res.result.name\n              }\n            }\n          })\n        }\n      },\n      loadRoot(){\n        let param = {\n          async:this.async,\n          pcode:this.parentCode\n        }\n        getAction(this.url_root,param).then(res=>{\n          if(res.success){\n            this.handleTreeNodeValue(res.result)\n            console.log(\"aaaa\",res.result)\n            this.treeData = [...res.result]\n          }else{\n            this.$message.error(res.message)\n          }\n        })\n      },\n      asyncLoadTreeData (treeNode) {\n        return new Promise((resolve) => {\n          if(!this.async){\n            resolve()\n            return\n          }\n          if (treeNode.$vnode.children) {\n            resolve()\n            return\n          }\n          let pid = treeNode.$vnode.key\n          let param = {\n            pid:pid\n          }\n          getAction(this.url_children,param).then(res=>{\n            if(res.success){\n              this.handleTreeNodeValue(res.result)\n              this.addChildren(pid,res.result,this.treeData)\n              this.treeData = [...this.treeData]\n            }\n            resolve()\n          })\n        })\n      },\n      addChildren(pid,children,treeArray){\n        if(treeArray && treeArray.length>0){\n          for(let item of treeArray){\n            if(item.key == pid){\n              if(!children || children.length==0){\n                item.leaf = true\n              }else{\n                item.children = children\n              }\n              break\n            }else{\n              this.addChildren(pid,children,item.children)\n            }\n          }\n        }\n      },\n      handleTreeNodeValue(result){\n        let storeField = this.field=='code'?'code':'key'\n        for(let i of result){\n          i.value = i[storeField]\n          i.isLeaf = (!i.leaf)?false:true\n          if(i.children && i.children.length>0){\n            this.handleTreeNodeValue(i.children)\n          }\n        }\n      },\n      onChange(value){\n        console.log(value)\n        if(!value){\n          this.$emit('change', '');\n        }else{\n          this.$emit('change', value.value);\n        }\n        this.treeValue = value\n      },\n      onSearch(value){\n        console.log(value)\n      },\n      getCurrTreeData(){\n        return this.treeData\n      }\n    }\n\n  }\n</script>"]}]}