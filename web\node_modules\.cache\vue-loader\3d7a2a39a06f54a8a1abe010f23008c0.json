{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue?vue&type=template&id=028e0b12&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JTreeTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-table\n  :rowKey=\"rowKey\"\n  :columns=\"columns\"\n  :dataSource=\"dataSource\"\n  :expandedRowKeys=\"expandedRowKeys\"\n  v-bind=\"tableAttrs\"\n  v-on=\"$listeners\"\n  @expand=\"handleExpand\"\n  @expandedRowsChange=\"expandedRowKeys=$event\">\n\n  <template v-for=\"(slotItem) of slots\" :slot=\"slotItem\" slot-scope=\"text, record, index\">\n    <slot :name=\"slotItem\" v-bind=\"{text,record,index}\"></slot>\n  </template>\n\n</a-table>\n", null]}