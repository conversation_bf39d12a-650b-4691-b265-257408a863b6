{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue?vue&type=template&id=d2093876&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitMap.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"account-settings-info-view\"\n  }, [_c(\"div\", {\n    style: {\n      background: \"url(\" + _vm.getFileAccessHttpUrl(_vm.courseInfo.courseMap) + \") no-repeat\",\n      backgroundSize: \"auto\",\n      height: \"1000px\"\n    }\n  }, [_vm._l(_vm.unitList, function (unit) {\n    return _c(\"div\", {\n      key: unit.id,\n      staticClass: \"unit\",\n      style: {\n        left: unit.mapX - 25 + \"px\",\n        top: unit.mapY - 25 + \"px\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.viewUnit(unit);\n        }\n      }\n    }, [_c(\"i\", {\n      staticClass: \"flag\"\n    }), _c(\"div\", {\n      staticClass: \"unit-title\"\n    }, [_vm._v(_vm._s(unit.unitName))])]);\n  }), _c(\"unitView-modal\", {\n    ref: \"unitViewModal\"\n  })], 2)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "background", "getFileAccessHttpUrl", "courseInfo", "courseMap", "backgroundSize", "height", "_l", "unitList", "unit", "key", "id", "left", "mapX", "top", "mapY", "on", "click", "$event", "viewUnit", "_v", "_s", "unitName", "ref", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/account/course/CourseUnitMap.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"account-settings-info-view\" }, [\n    _c(\n      \"div\",\n      {\n        style: {\n          background:\n            \"url(\" +\n            _vm.getFileAccessHttpUrl(_vm.courseInfo.courseMap) +\n            \") no-repeat\",\n          backgroundSize: \"auto\",\n          height: \"1000px\",\n        },\n      },\n      [\n        _vm._l(_vm.unitList, function (unit) {\n          return _c(\n            \"div\",\n            {\n              key: unit.id,\n              staticClass: \"unit\",\n              style: {\n                left: unit.mapX - 25 + \"px\",\n                top: unit.mapY - 25 + \"px\",\n              },\n              on: {\n                click: function ($event) {\n                  return _vm.viewUnit(unit)\n                },\n              },\n            },\n            [\n              _c(\"i\", { staticClass: \"flag\" }),\n              _c(\"div\", { staticClass: \"unit-title\" }, [\n                _vm._v(_vm._s(unit.unitName)),\n              ]),\n            ]\n          )\n        }),\n        _c(\"unitView-modal\", { ref: \"unitViewModal\" }),\n      ],\n      2\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAAE,CAC9DF,EAAE,CACA,KAAK,EACL;IACEG,KAAK,EAAE;MACLC,UAAU,EACR,MAAM,GACNL,GAAG,CAACM,oBAAoB,CAACN,GAAG,CAACO,UAAU,CAACC,SAAS,CAAC,GAClD,aAAa;MACfC,cAAc,EAAE,MAAM;MACtBC,MAAM,EAAE;IACV;EACF,CAAC,EACD,CACEV,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,QAAQ,EAAE,UAAUC,IAAI,EAAE;IACnC,OAAOZ,EAAE,CACP,KAAK,EACL;MACEa,GAAG,EAAED,IAAI,CAACE,EAAE;MACZZ,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE;QACLY,IAAI,EAAEH,IAAI,CAACI,IAAI,GAAG,EAAE,GAAG,IAAI;QAC3BC,GAAG,EAAEL,IAAI,CAACM,IAAI,GAAG,EAAE,GAAG;MACxB,CAAC;MACDC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOtB,GAAG,CAACuB,QAAQ,CAACV,IAAI,CAAC;QAC3B;MACF;IACF,CAAC,EACD,CACEZ,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAO,CAAC,CAAC,EAChCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCH,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACyB,EAAE,CAACZ,IAAI,CAACa,QAAQ,CAAC,CAAC,CAC9B,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACFzB,EAAE,CAAC,gBAAgB,EAAE;IAAE0B,GAAG,EAAE;EAAgB,CAAC,CAAC,CAC/C,EACD,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB7B,MAAM,CAAC8B,aAAa,GAAG,IAAI;AAE3B,SAAS9B,MAAM,EAAE6B,eAAe", "ignoreList": []}]}