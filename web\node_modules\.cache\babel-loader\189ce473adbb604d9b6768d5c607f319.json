{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\FooterToolBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"FooterToolBar\"\n};", {"version": 3, "names": ["name"], "sources": ["src/components/tools/FooterToolBar.vue"], "sourcesContent": ["<template>\n  <div class=\"toolbar\">\n    <div style=\"float: left\">\n      <slot name=\"extra\"></slot>\n    </div>\n    <div style=\"float: right\">\n      <slot></slot>\n    </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"FooterToolBar\"\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .toolbar {\n    position: fixed;\n    width: 100%;\n    bottom: 0;\n    right: 0;\n    height: 56px;\n    line-height: 56px;\n    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);\n    background: #fff;\n    border-top: 1px solid #e8e8e8;\n    padding: 0 24px;\n    z-index: 9;\n  }\n</style>"], "mappings": "AAYA;EACAA,IAAA;AACA", "ignoreList": []}]}