{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nexport default {\n  name: \"StandardTable\",\n  // props: ['bordered', 'loading', 'columns', 'data', 'rowKey', 'pagination', 'selectedRows'],\n  props: {\n    /**\n     * 数据加载函数，返回值必须是 Promise\n     * 默认情况下必须传递 data 参数；\n     *    如果使用本地数据渲染表格，业务代码中将获取本地数据包装为 Promise 即可。\n     *\n     * currentData 用于向外暴露表格当前渲染的数据，\n     * 业务开发中也可以直接修改 currentData，从而重新渲染表格（仅推荐用于客户端排序、数据过滤等场景）\n     */\n    data: {\n      type: Function,\n      required: true\n    },\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    columns: {\n      type: Array,\n      required: true\n    },\n    /*      pagination: {\n            type: Object,\n            default () {\n              return {}\n            }\n          },*/\n    pageSize: {\n      type: Number,\n      default: 10\n    },\n    pageNum: {\n      type: Number,\n      default: 1\n    },\n    pageSizeOptions: {\n      type: Array,\n      default: function _default() {\n        return ['10', '20', '30', '40', '50'];\n      }\n    },\n    responseParamsName: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    bordered: {\n      type: Boolean,\n      default: false\n    },\n    /**\n     * 表格大小风格，default, middle, small\n     */\n    size: {\n      type: String,\n      default: 'default'\n    },\n    rowKey: {\n      type: String,\n      default: ''\n    },\n    selectedRows: {\n      type: Array,\n      default: null\n    }\n  },\n  data: function data() {\n    return {\n      needTotalList: [],\n      selectedRowKeys: [],\n      loading: true,\n      total: 0,\n      pageNumber: this.pageNum,\n      currentPageSize: this.pageSize,\n      defaultCurrent: 1,\n      sortParams: {},\n      current: [],\n      pagination: {},\n      paramsName: {}\n    };\n  },\n  created: function created() {\n    //数据请求参数配置\n    this.paramsName = Object.assign({}, {\n      pageNumber: \"pageNo\",\n      pageSize: \"pageSize\",\n      total: \"totalCount\",\n      results: \"data\",\n      sortColumns: \"sortColumns\"\n    }, this.responseParamsName);\n    this.needTotalList = this.initTotalList(this.columns);\n\n    // load data\n    this.loadData({\n      pageNum: this.pageNumber\n    });\n  },\n  methods: {\n    updateSelect: function updateSelect(selectedRowKeys, selectedRows) {\n      this.selectedRowKeys = selectedRowKeys;\n      var list = this.needTotalList;\n      this.needTotalList = list.map(function (item) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          total: selectedRows.reduce(function (sum, val) {\n            return sum + val[item.dataIndex];\n          }, 0)\n        });\n      });\n      this.$emit('change', selectedRowKeys, selectedRows);\n    },\n    initTotalList: function initTotalList(columns) {\n      var totalList = [];\n      columns.forEach(function (column) {\n        if (column.needTotal) {\n          totalList.push(_objectSpread(_objectSpread({}, column), {}, {\n            total: 0\n          }));\n        }\n      });\n      return totalList;\n    },\n    loadData: function loadData(params) {\n      var that = this;\n      that.loading = true;\n      params = Object.assign({}, params);\n      var remoteParams = Object.assign({}, that.sortParams);\n      remoteParams[that.paramsName.pageNumber] = params.pageNum || that.pageNumber;\n      remoteParams[that.paramsName.pageSize] = params.pageSize || that.currentPageSize;\n      if (params.pageNum) {\n        that.pageNumber = params.pageNum;\n      }\n      if (params.pageSize) {\n        that.currentPageSize = params.pageSize;\n      }\n      var dataPromise = that.data(remoteParams);\n      dataPromise.then(function (response) {\n        if (!response) {\n          that.loading = false;\n          return;\n        }\n        var results = response[that.paramsName.results];\n        results = results instanceof Array && results || [];\n        that.current = results;\n        that.$emit(\"update:currentData\", that.current.slice());\n        that.$emit(\"dataloaded\", that.current.slice());\n        that.total = response[that.paramsName.total] * 1;\n        that.pagination = that.pager();\n        that.loading = false;\n      }, function () {\n        // error callback\n        that.loading = false;\n      });\n    },\n    // eslint-disable-next-line\n    onPagerChange: function onPagerChange(page, pageSize) {\n      this.pageNumber = page;\n      this.loadData({\n        pageNum: page\n      });\n    },\n    onPagerSizeChange: function onPagerSizeChange(current, size) {\n      this.currentPageSize = size;\n      /*\n      if (current === this.pageNumber) this.loadData()\n      console.log('page-size-change', current, size)\n      */\n    },\n    onClearSelected: function onClearSelected() {\n      this.selectedRowKeys = [];\n      this.updateSelect([], []);\n    },\n    pager: function pager() {\n      return {\n        total: this.total,\n        showTotal: function showTotal(total) {\n          return \"\\u5171\\u6709 \".concat(total, \" \\u6761\");\n        },\n        showSizeChanger: true,\n        pageSizeOptions: this.pageSizeOptions,\n        pageSize: this.pageSize,\n        defaultCurrent: this.defaultCurrent,\n        onChange: this.onPagerChange,\n        onShowSizeChange: this.onPagerSizeChange\n      };\n    }\n  },\n  watch: {\n    'selectedRows': function selectedRows(_selectedRows) {\n      this.needTotalList = this.needTotalList.map(function (item) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          total: _selectedRows.reduce(function (sum, val) {\n            return sum + val[item.dataIndex];\n          }, 0)\n        });\n      });\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "data", "type", "Function", "required", "dataSource", "Array", "default", "_default", "columns", "pageSize", "Number", "pageNum", "pageSizeOptions", "responseParamsName", "Object", "bordered", "Boolean", "size", "String", "<PERSON><PERSON><PERSON>", "selectedRows", "needTotalList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "total", "pageNumber", "currentPageSize", "defaultCurrent", "sortParams", "current", "pagination", "paramsName", "created", "assign", "results", "sortColumns", "initTotalList", "loadData", "methods", "updateSelect", "list", "map", "item", "_objectSpread", "reduce", "sum", "val", "dataIndex", "$emit", "totalList", "for<PERSON>ach", "column", "needTotal", "push", "params", "that", "remoteParams", "dataPromise", "then", "response", "slice", "pager", "onPagerChange", "page", "onPagerSizeChange", "onClearSelected", "showTotal", "concat", "showSizeChanger", "onChange", "onShowSizeChange", "watch"], "sources": ["src/components/table/StandardTable.vue"], "sourcesContent": ["<template>\n  <div class=\"standard-table\">\n    <div class=\"alert\">\n      <a-alert type=\"info\" :show-icon=\"true\">\n        <div slot=\"message\">\n          已选择&nbsp;<a style=\"font-weight: 600\">{{ selectedRows.length }}</a>&nbsp;&nbsp;\n          <template v-for=\"(item, index) in needTotalList\" v-if=\"item.needTotal\">\n            {{ item.title }} 总计&nbsp;\n            <a :key=\"index\" style=\"font-weight: 600\">\n              {{ item.customRender ? item.customRender(item.total) : item.total }}\n            </a>&nbsp;&nbsp;\n          </template>\n          <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n        </div>\n      </a-alert>\n    </div>\n    <a-table\n      :size=\"size\"\n      :bordered=\"bordered\"\n      :loading=\"loading\"\n      :columns=\"columns\"\n      :dataSource=\"current\"\n      :rowKey=\"rowKey\"\n      :pagination=\"pagination\"\n      :rowSelection=\"{ selectedRowKeys: selectedRowKeys, onChange: updateSelect }\"\n    >\n    </a-table>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"StandardTable\",\n    // props: ['bordered', 'loading', 'columns', 'data', 'rowKey', 'pagination', 'selectedRows'],\n    props: {\n\n      /**\n       * 数据加载函数，返回值必须是 Promise\n       * 默认情况下必须传递 data 参数；\n       *    如果使用本地数据渲染表格，业务代码中将获取本地数据包装为 Promise 即可。\n       *\n       * currentData 用于向外暴露表格当前渲染的数据，\n       * 业务开发中也可以直接修改 currentData，从而重新渲染表格（仅推荐用于客户端排序、数据过滤等场景）\n       */\n      data: {\n        type: Function,\n        required: true\n      },\n      dataSource: {\n        type: Array,\n        default () {\n          return []\n        }\n      },\n      columns: {\n        type: Array,\n        required: true\n      },\n/*      pagination: {\n        type: Object,\n        default () {\n          return {}\n        }\n      },*/\n      pageSize: {\n        type: Number,\n        default: 10\n      },\n      pageNum: {\n        type: Number,\n        default: 1\n      },\n      pageSizeOptions: {\n        type: Array,\n        default () {\n          return ['10', '20', '30', '40', '50']\n        }\n      },\n      responseParamsName: {\n        type: Object,\n        default () {\n          return {}\n        }\n      },\n      bordered: {\n        type: Boolean,\n        default: false\n      },\n      /**\n       * 表格大小风格，default, middle, small\n       */\n      size: {\n        type: String,\n        default: 'default'\n      },\n      rowKey: {\n        type: String,\n        default: ''\n      },\n      selectedRows: {\n        type: Array,\n        default: null\n      }\n    },\n    data () {\n      return {\n        needTotalList: [],\n        selectedRowKeys: [],\n\n        loading: true,\n\n        total: 0,\n        pageNumber: this.pageNum,\n        currentPageSize: this.pageSize,\n        defaultCurrent: 1,\n        sortParams: {},\n\n        current: [],\n        pagination: {},\n        paramsName: {},\n      }\n    },\n    created () {\n      //数据请求参数配置\n      this.paramsName = Object.assign(\n        {},\n        {\n          pageNumber: \"pageNo\",\n          pageSize: \"pageSize\",\n          total: \"totalCount\",\n          results: \"data\",\n          sortColumns: \"sortColumns\"\n        },\n        this.responseParamsName\n      );\n\n      this.needTotalList = this.initTotalList(this.columns)\n\n      // load data\n      this.loadData( { pageNum: this.pageNumber } )\n    },\n    methods: {\n      updateSelect (selectedRowKeys, selectedRows) {\n        this.selectedRowKeys = selectedRowKeys\n        let list = this.needTotalList\n        this.needTotalList = list.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce((sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n        this.$emit('change', selectedRowKeys, selectedRows)\n      },\n      initTotalList (columns) {\n        const totalList = []\n        columns.forEach(column => {\n          if (column.needTotal) {\n            totalList.push({ ...column, total: 0 })\n          }\n        })\n        return totalList\n      },\n\n      loadData (params) {\n        let that = this\n        that.loading = true\n        params = Object.assign({}, params)\n        const remoteParams = Object.assign({}, that.sortParams)\n        remoteParams[that.paramsName.pageNumber] = params.pageNum || that.pageNumber\n        remoteParams[that.paramsName.pageSize] = params.pageSize || that.currentPageSize\n\n        if (params.pageNum) {\n          that.pageNumber = params.pageNum\n        }\n        if (params.pageSize) {\n          that.currentPageSize = params.pageSize\n        }\n\n        let dataPromise = that.data(remoteParams)\n\n        dataPromise.then( response => {\n          if (!response) {\n            that.loading = false\n            return\n          }\n          let results = response[that.paramsName.results]\n          results = (results instanceof Array && results) || []\n\n          that.current = results\n\n          that.$emit(\"update:currentData\", that.current.slice())\n          that.$emit(\"dataloaded\", that.current.slice())\n\n          that.total = response[that.paramsName.total] * 1\n          that.pagination = that.pager()\n          that.loading = false\n        }, () => {\n          // error callback\n          that.loading = false\n        })\n      },\n      // eslint-disable-next-line\n      onPagerChange (page, pageSize) {\n        this.pageNumber = page\n        this.loadData({ pageNum: page })\n      },\n      onPagerSizeChange (current, size) {\n        this.currentPageSize = size\n        /*\n        if (current === this.pageNumber) this.loadData()\n        console.log('page-size-change', current, size)\n        */\n      },\n      onClearSelected () {\n        this.selectedRowKeys = []\n        this.updateSelect([], [])\n      },\n      pager () {\n        return {\n          total: this.total,\n          showTotal: total => `共有 ${total} 条`,\n          showSizeChanger: true,\n          pageSizeOptions: this.pageSizeOptions,\n          pageSize: this.pageSize,\n          defaultCurrent: this.defaultCurrent,\n          onChange: this.onPagerChange,\n          onShowSizeChange: this.onPagerSizeChange\n        }\n      }\n    },\n    watch: {\n      'selectedRows': function (selectedRows) {\n        this.needTotalList = this.needTotalList.map(item => {\n          return {\n            ...item,\n            total: selectedRows.reduce( (sum, val) => {\n              return sum + val[item.dataIndex]\n            }, 0)\n          }\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n    .alert {\n        margin-bottom: 16px;\n    }\n</style>"], "mappings": ";;;;;;AA+BA;EACAA,IAAA;EACA;EACAC,KAAA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC,IAAA;MACAC,IAAA,EAAAC,QAAA;MACAC,QAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,KAAA;MACAC,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAC,OAAA;MACAP,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAM,QAAA;MACAR,IAAA,EAAAS,MAAA;MACAJ,OAAA;IACA;IACAK,OAAA;MACAV,IAAA,EAAAS,MAAA;MACAJ,OAAA;IACA;IACAM,eAAA;MACAX,IAAA,EAAAI,KAAA;MACAC,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAM,kBAAA;MACAZ,IAAA,EAAAa,MAAA;MACAR,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAQ,QAAA;MACAd,IAAA,EAAAe,OAAA;MACAV,OAAA;IACA;IACA;AACA;AACA;IACAW,IAAA;MACAhB,IAAA,EAAAiB,MAAA;MACAZ,OAAA;IACA;IACAa,MAAA;MACAlB,IAAA,EAAAiB,MAAA;MACAZ,OAAA;IACA;IACAc,YAAA;MACAnB,IAAA,EAAAI,KAAA;MACAC,OAAA;IACA;EACA;EACAN,IAAA,WAAAA,KAAA;IACA;MACAqB,aAAA;MACAC,eAAA;MAEAC,OAAA;MAEAC,KAAA;MACAC,UAAA,OAAAd,OAAA;MACAe,eAAA,OAAAjB,QAAA;MACAkB,cAAA;MACAC,UAAA;MAEAC,OAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAD,UAAA,GAAAjB,MAAA,CAAAmB,MAAA,CACA,IACA;MACAR,UAAA;MACAhB,QAAA;MACAe,KAAA;MACAU,OAAA;MACAC,WAAA;IACA,GACA,KAAAtB,kBACA;IAEA,KAAAQ,aAAA,QAAAe,aAAA,MAAA5B,OAAA;;IAEA;IACA,KAAA6B,QAAA;MAAA1B,OAAA,OAAAc;IAAA;EACA;EACAa,OAAA;IACAC,YAAA,WAAAA,aAAAjB,eAAA,EAAAF,YAAA;MACA,KAAAE,eAAA,GAAAA,eAAA;MACA,IAAAkB,IAAA,QAAAnB,aAAA;MACA,KAAAA,aAAA,GAAAmB,IAAA,CAAAC,GAAA,WAAAC,IAAA;QACA,OAAAC,aAAA,CAAAA,aAAA,KACAD,IAAA;UACAlB,KAAA,EAAAJ,YAAA,CAAAwB,MAAA,WAAAC,GAAA,EAAAC,GAAA;YACA,OAAAD,GAAA,GAAAC,GAAA,CAAAJ,IAAA,CAAAK,SAAA;UACA;QAAA;MAEA;MACA,KAAAC,KAAA,WAAA1B,eAAA,EAAAF,YAAA;IACA;IACAgB,aAAA,WAAAA,cAAA5B,OAAA;MACA,IAAAyC,SAAA;MACAzC,OAAA,CAAA0C,OAAA,WAAAC,MAAA;QACA,IAAAA,MAAA,CAAAC,SAAA;UACAH,SAAA,CAAAI,IAAA,CAAAV,aAAA,CAAAA,aAAA,KAAAQ,MAAA;YAAA3B,KAAA;UAAA;QACA;MACA;MACA,OAAAyB,SAAA;IACA;IAEAZ,QAAA,WAAAA,SAAAiB,MAAA;MACA,IAAAC,IAAA;MACAA,IAAA,CAAAhC,OAAA;MACA+B,MAAA,GAAAxC,MAAA,CAAAmB,MAAA,KAAAqB,MAAA;MACA,IAAAE,YAAA,GAAA1C,MAAA,CAAAmB,MAAA,KAAAsB,IAAA,CAAA3B,UAAA;MACA4B,YAAA,CAAAD,IAAA,CAAAxB,UAAA,CAAAN,UAAA,IAAA6B,MAAA,CAAA3C,OAAA,IAAA4C,IAAA,CAAA9B,UAAA;MACA+B,YAAA,CAAAD,IAAA,CAAAxB,UAAA,CAAAtB,QAAA,IAAA6C,MAAA,CAAA7C,QAAA,IAAA8C,IAAA,CAAA7B,eAAA;MAEA,IAAA4B,MAAA,CAAA3C,OAAA;QACA4C,IAAA,CAAA9B,UAAA,GAAA6B,MAAA,CAAA3C,OAAA;MACA;MACA,IAAA2C,MAAA,CAAA7C,QAAA;QACA8C,IAAA,CAAA7B,eAAA,GAAA4B,MAAA,CAAA7C,QAAA;MACA;MAEA,IAAAgD,WAAA,GAAAF,IAAA,CAAAvD,IAAA,CAAAwD,YAAA;MAEAC,WAAA,CAAAC,IAAA,WAAAC,QAAA;QACA,KAAAA,QAAA;UACAJ,IAAA,CAAAhC,OAAA;UACA;QACA;QACA,IAAAW,OAAA,GAAAyB,QAAA,CAAAJ,IAAA,CAAAxB,UAAA,CAAAG,OAAA;QACAA,OAAA,GAAAA,OAAA,YAAA7B,KAAA,IAAA6B,OAAA;QAEAqB,IAAA,CAAA1B,OAAA,GAAAK,OAAA;QAEAqB,IAAA,CAAAP,KAAA,uBAAAO,IAAA,CAAA1B,OAAA,CAAA+B,KAAA;QACAL,IAAA,CAAAP,KAAA,eAAAO,IAAA,CAAA1B,OAAA,CAAA+B,KAAA;QAEAL,IAAA,CAAA/B,KAAA,GAAAmC,QAAA,CAAAJ,IAAA,CAAAxB,UAAA,CAAAP,KAAA;QACA+B,IAAA,CAAAzB,UAAA,GAAAyB,IAAA,CAAAM,KAAA;QACAN,IAAA,CAAAhC,OAAA;MACA;QACA;QACAgC,IAAA,CAAAhC,OAAA;MACA;IACA;IACA;IACAuC,aAAA,WAAAA,cAAAC,IAAA,EAAAtD,QAAA;MACA,KAAAgB,UAAA,GAAAsC,IAAA;MACA,KAAA1B,QAAA;QAAA1B,OAAA,EAAAoD;MAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAnC,OAAA,EAAAZ,IAAA;MACA,KAAAS,eAAA,GAAAT,IAAA;MACA;AACA;AACA;AACA;IACA;IACAgD,eAAA,WAAAA,gBAAA;MACA,KAAA3C,eAAA;MACA,KAAAiB,YAAA;IACA;IACAsB,KAAA,WAAAA,MAAA;MACA;QACArC,KAAA,OAAAA,KAAA;QACA0C,SAAA,WAAAA,UAAA1C,KAAA;UAAA,uBAAA2C,MAAA,CAAA3C,KAAA;QAAA;QACA4C,eAAA;QACAxD,eAAA,OAAAA,eAAA;QACAH,QAAA,OAAAA,QAAA;QACAkB,cAAA,OAAAA,cAAA;QACA0C,QAAA,OAAAP,aAAA;QACAQ,gBAAA,OAAAN;MACA;IACA;EACA;EACAO,KAAA;IACA,yBAAAnD,0BAAA;MACA,KAAAC,aAAA,QAAAA,aAAA,CAAAoB,GAAA,WAAAC,IAAA;QACA,OAAAC,aAAA,CAAAA,aAAA,KACAD,IAAA;UACAlB,KAAA,EAAAJ,aAAA,CAAAwB,MAAA,WAAAC,GAAA,EAAAC,GAAA;YACA,OAAAD,GAAA,GAAAC,GAAA,CAAAJ,IAAA,CAAAK,SAAA;UACA;QAAA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}