{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoModal.vue?vue&type=template&id=e9af7a82&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgDemoModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  cancelText=\"关闭\">\n  \n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n    \n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"姓名\"\n        hasFeedback >\n        <a-input placeholder=\"请输入姓名\" v-decorator=\"['name', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"关键词\"\n        hasFeedback >\n        <a-input placeholder=\"请输入关键词\" v-decorator=\"['keyWord', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"打卡时间\"\n        hasFeedback >\n        <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" v-decorator=\"[ 'punchTime', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"性别\">\n       <!-- <a-select v-decorator=\"['sex', {}]\" placeholder=\"请选择性别\">\n          <a-select-option value=\"\">请选择</a-select-option>\n          <a-select-option value=\"1\">男</a-select-option>\n          <a-select-option value=\"2\">女</a-select-option>\n        </a-select>-->\n        <j-dict-select-tag type=\"radio\" v-decorator=\"['sex', {}]\" :trigger-change=\"true\" dictCode=\"sex\"/>\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"年龄\"\n        hasFeedback >\n        <a-input placeholder=\"请输入年龄\" v-decorator=\"['age', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"生日\"\n        hasFeedback >\n        <a-date-picker v-decorator=\"[ 'birthday', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"邮箱\"\n        hasFeedback >\n        <a-input placeholder=\"请输入邮箱\" v-decorator=\"['email', {}]\" />\n      </a-form-item>\n      <a-form-item\n        :labelCol=\"labelCol\"\n        :wrapperCol=\"wrapperCol\"\n        label=\"个人简介\"\n        hasFeedback >\n        <a-input placeholder=\"请输入个人简介\" v-decorator=\"['content', {}]\" />\n      </a-form-item>\n\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}