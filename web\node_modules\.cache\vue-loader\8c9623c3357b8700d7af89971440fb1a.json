{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step3.vue?vue&type=template&id=6d40caae&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\stepForm\\Step3.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div>\n  <a-form style=\"margin: 40px auto 0;\">\n    <result title=\"操作成功\" :is-success=\"true\" description=\"预计两小时内到账\">\n      <div class=\"information\">\n        <a-row>\n          <a-col :sm=\"8\" :xs=\"24\">付款账户：</a-col>\n          <a-col :sm=\"16\" :xs=\"24\"><EMAIL></a-col>\n        </a-row>\n        <a-row>\n          <a-col :sm=\"8\" :xs=\"24\">收款账户：</a-col>\n          <a-col :sm=\"16\" :xs=\"24\"><EMAIL></a-col>\n        </a-row>\n        <a-row>\n          <a-col :sm=\"8\" :xs=\"24\">收款人姓名：</a-col>\n          <a-col :sm=\"16\" :xs=\"24\">辉夜</a-col>\n        </a-row>\n        <a-row>\n          <a-col :sm=\"8\" :xs=\"24\">转账金额：</a-col>\n          <a-col :sm=\"16\" :xs=\"24\"><span class=\"money\">500</span> 元</a-col>\n        </a-row>\n      </div>\n      <div slot=\"action\">\n        <a-button type=\"primary\" @click=\"finish\">再转一笔</a-button>\n        <a-button style=\"margin-left: 8px\" @click=\"toOrderList\">查看账单</a-button>\n      </div>\n    </result>\n  </a-form>\n</div>\n", null]}