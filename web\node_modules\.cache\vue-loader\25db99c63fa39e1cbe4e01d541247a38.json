{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue?vue&type=template&id=1b0d512f&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue", "mtime": 1752749894266}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<a-card class=\"mode-selection\" :bordered=\"false\">\n  <div class=\"mode-title\">\n    <h2><a-icon type=\"rocket\" /> 选择刷题模式</h2>\n    <p>根据您的需求选择最适合的刷题方式</p>\n    <!-- 题库数量标志 -->\n    <div class=\"question-bank-count\">\n      <span class=\"count-number\">{{questionBankCount}}</span>\n      <span class=\"count-label\">题库总量</span>\n    </div>\n  </div>\n  \n  <a-form :form=\"form\" layout=\"vertical\">\n    <a-row :gutter=\"24\">\n      <a-col :span=\"12\">\n        <a-form-item>\n          <!-- 科目选择 -->\n          <div class=\"section-title\">\n            <span class=\"title-icon\"><a-icon type=\"appstore\" /></span>\n            <span class=\"title-text\">科目选择</span>\n          </div>\n          <div class=\"subject-selection-cards\">\n            <div \n              class=\"subject-card\" \n              :class=\"{'subject-selected': queryParam.subject === 'Scratch'}\"\n              @click=\"handleSubjectSelect('Scratch')\">\n              <img src=\"@/assets/scratch.png\" alt=\"Scratch\" class=\"subject-icon\" />\n              <span class=\"subject-name\">Scratch</span>\n            </div>\n            <div \n              class=\"subject-card\" \n              :class=\"{'subject-selected': queryParam.subject === 'Python'}\"\n              @click=\"handleSubjectSelect('Python')\">\n              <img src=\"@/assets/python.png\" alt=\"Python\" class=\"subject-icon\" />\n              <span class=\"subject-name\">Python</span>\n            </div>\n            <div \n              class=\"subject-card\" \n              :class=\"{'subject-selected': queryParam.subject === 'C++'}\"\n              @click=\"handleSubjectSelect('C++')\">\n              <img src=\"@/assets/cpp.png\" alt=\"C++\" class=\"subject-icon\" />\n              <span class=\"subject-name\">C++</span>\n            </div>\n          </div>\n        </a-form-item>\n      </a-col>\n      <a-col :span=\"12\">\n        <a-form-item>\n          <!-- 级别选择 -->\n          <div class=\"section-title\">\n            <span class=\"title-icon\"><a-icon type=\"rise\" /></span>\n            <span class=\"title-text\">级别选择</span>\n          </div>\n          <a-select \n            v-model=\"queryParam.level\" \n            style=\"width: 100%\" \n            placeholder=\"请先选择科目\" \n            allowClear\n            :disabled=\"!queryParam.subject\"\n          >\n            <template v-if=\"queryParam.subject === 'Scratch'\">\n              <a-select-option v-for=\"i in 4\" :key=\"i\" :value=\"i\">{{ ['一', '二', '三', '四'][i-1] }}级</a-select-option>\n            </template>\n            <template v-else>\n              <a-select-option v-for=\"i in 8\" :key=\"i\" :value=\"i\">{{ ['一', '二', '三', '四', '五', '六', '七', '八'][i-1] }}级</a-select-option>\n            </template>\n          </a-select>\n        </a-form-item>\n      </a-col>\n    </a-row>\n    \n    <a-row :gutter=\"24\">\n      <a-col :span=\"12\">\n        <a-form-item>\n          <!-- 题目类型 -->\n          <div class=\"section-title\">\n            <span class=\"title-icon\"><a-icon type=\"file-text\" /></span>\n            <span class=\"title-text\">题目类型</span>\n          </div>\n          <div class=\"custom-checkbox-group\">\n            <a-checkbox-group v-model=\"questionTypes\">\n              <a-checkbox value=\"1\"><a-icon type=\"check-square\" /> 单选题</a-checkbox>\n              <a-checkbox value=\"2\"><a-icon type=\"check-circle\" /> 判断题</a-checkbox>\n              <a-checkbox value=\"3\"><a-icon type=\"code\" /> 编程题</a-checkbox>\n            </a-checkbox-group>\n          </div>\n        </a-form-item>\n      </a-col>\n      <a-col :span=\"12\">\n        <a-form-item>\n          <!-- 难度范围 -->\n          <div class=\"section-title\">\n            <span class=\"title-icon\"><a-icon type=\"dashboard\" /></span>\n            <span class=\"title-text\">难度范围</span>\n          </div>\n          <div class=\"difficulty-selection\">\n            <div class=\"difficulty-labels\">\n              <span class=\"difficulty-value\">{{ difficultyFormatter(difficultyRange[0]) }}</span>\n              <span class=\"difficulty-divider\">至</span>\n              <span class=\"difficulty-value\">{{ difficultyFormatter(difficultyRange[1]) }}</span>\n            </div>\n            <a-row>\n              <a-col :span=\"11\">\n                <a-slider\n                  v-model=\"difficultyRange[0]\"\n                  :min=\"1\"\n                  :max=\"3\"\n                  :marks=\"difficultyMarks\"\n                />\n              </a-col>\n              <a-col :span=\"2\" style=\"text-align: center\">\n                <span class=\"slider-divider\">~</span>\n              </a-col>\n              <a-col :span=\"11\">\n                <a-slider\n                  v-model=\"difficultyRange[1]\"\n                  :min=\"1\"\n                  :max=\"3\"\n                  :marks=\"difficultyMarks\"\n                />\n              </a-col>\n            </a-row>\n          </div>\n        </a-form-item>\n      </a-col>\n    </a-row>\n    \n    <a-form-item>\n      <!-- 刷题模式 -->\n      <div class=\"section-title\">\n        <span class=\"title-icon\"><a-icon type=\"play-circle\" /></span>\n        <span class=\"title-text\">刷题模式</span>\n      </div>\n      <div class=\"mode-cards\">\n        <div class=\"mode-card\" \n          :class=\"{'selected': practiseMode === 'count'}\" \n          @click=\"practiseMode = 'count'; handleModeChange('count')\">\n          <div class=\"mode-card-icon\">\n            <a-icon type=\"ordered-list\" />\n          </div>\n          <div class=\"mode-card-content\">\n            <h4>按题目数量</h4>\n            <p>限定题目数量刷题</p>\n          </div>\n        </div>\n        <div class=\"mode-card\" \n          :class=\"{'selected': practiseMode === 'time'}\"\n          @click=\"practiseMode = 'time'; handleModeChange('time')\">\n          <div class=\"mode-card-icon\">\n            <a-icon type=\"clock-circle\" />\n          </div>\n          <div class=\"mode-card-content\">\n            <h4>按时间限制</h4>\n            <p>限时答题模式</p>\n          </div>\n        </div>\n        <div class=\"mode-card\" \n          :class=\"{'selected': practiseMode === 'free'}\"\n          @click=\"practiseMode = 'free'; handleModeChange('free')\">\n          <div class=\"mode-card-icon\">\n            <a-icon type=\"unlock\" />\n          </div>\n          <div class=\"mode-card-content\">\n            <h4>自由模式</h4>\n            <p>无限制刷题</p>\n          </div>\n        </div>\n      </div>\n    </a-form-item>\n  \n    <a-form-item v-if=\"practiseMode === 'count'\">\n      <!-- 题目数量 -->\n      <div class=\"section-title\">\n        <span class=\"title-icon\"><a-icon type=\"ordered-list\" /></span>\n        <span class=\"title-text\">题目数量</span>\n      </div>\n      <a-input-number \n        v-model=\"practiseCount\" \n        :min=\"5\" \n        :max=\"100\" \n        style=\"width: 100%\"\n      />\n    </a-form-item>\n    \n    <a-form-item v-if=\"practiseMode === 'time'\">\n      <!-- 时间限制 -->\n      <div class=\"section-title\">\n        <span class=\"title-icon\"><a-icon type=\"clock-circle\" /></span>\n        <span class=\"title-text\">时间限制(分钟)</span>\n      </div>\n      <a-input-number \n        v-model=\"timeLimit\" \n        :min=\"5\" \n        :max=\"120\" \n        style=\"width: 100%\"\n      />\n    </a-form-item>\n    \n    <div class=\"practice-actions\">\n      <a-button type=\"primary\" size=\"large\" @click=\"startPractise\" :loading=\"loading\">\n        <a-icon type=\"play-circle\" /> 开始刷题\n      </a-button>\n      <a-button type=\"success\" size=\"large\" @click=\"startQuickPractise\" :loading=\"loading\">\n        <a-icon type=\"thunderbolt\" /> 快速刷题\n      </a-button>\n      <a-button v-if=\"hasSavedProgress\" type=\"primary\" size=\"large\" style=\"background-color: #722ed1; border-color: #722ed1;\" @click=\"continuePractise\" :loading=\"loading\">\n        <a-icon type=\"reload\" /> 继续上次答题\n      </a-button>\n      <a-button size=\"large\" @click=\"resetQuery\">\n        <a-icon type=\"reload\" /> 重置选项\n      </a-button>\n    </div>\n  </a-form>\n</a-card>\n", null]}