{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\SysMessageList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\SysMessageList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import SysMessageModal from './modules/SysMessageModal';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nimport JEllipsis from \"@/components/jeecg/JEllipsis\";\nexport default {\n  name: \"SysMessageList\",\n  mixins: [JeecgListMixin],\n  components: {\n    JEllipsis: JEllipsis,\n    SysMessageModal: SysMessageModal\n  },\n  data: function data() {\n    return {\n      description: '消息管理页面',\n      // 新增修改按钮是否显示\n      show: false,\n      // 表头\n      columns: [{\n        title: '#',\n        dataIndex: '',\n        key: 'rowIndex',\n        width: 60,\n        align: \"center\",\n        customRender: function customRender(t, r, index) {\n          return parseInt(index) + 1;\n        }\n      }, {\n        title: '消息标题',\n        align: \"center\",\n        dataIndex: 'esTitle'\n      }, {\n        title: '发送内容',\n        align: \"center\",\n        dataIndex: 'esContent',\n        scopedSlots: {\n          customRender: 'esContent'\n        }\n      }, {\n        title: '接收人',\n        align: \"center\",\n        dataIndex: 'esReceiver'\n      }, {\n        title: '发送次数',\n        align: \"center\",\n        dataIndex: 'esSendNum'\n      }, {\n        title: '发送状态',\n        align: 'center',\n        dataIndex: 'esSendStatus_dictText'\n      }, {\n        title: '发送时间',\n        align: \"center\",\n        dataIndex: 'esSendTime'\n      }, {\n        title: '发送方式',\n        align: 'center',\n        dataIndex: 'esType_dictText'\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        align: \"center\",\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      url: {\n        list: \"/message/sysMessage/list\",\n        delete: \"/message/sysMessage/delete\",\n        deleteBatch: \"/message/sysMessage/deleteBatch\",\n        exportXlsUrl: \"message/sysMessage/exportXls\",\n        importExcelUrl: \"message/sysMessage/importExcel\"\n      }\n    };\n  },\n  computed: {\n    importExcelUrl: function importExcelUrl() {\n      return \"\".concat(window._CONFIG['domianURL'], \"/\").concat(this.url.importExcelUrl);\n    }\n  },\n  methods: {}\n};", {"version": 3, "names": ["SysMessageModal", "JeecgListMixin", "JElli<PERSON>", "name", "mixins", "components", "data", "description", "show", "columns", "title", "dataIndex", "key", "width", "align", "customRender", "t", "r", "index", "parseInt", "scopedSlots", "url", "list", "delete", "deleteBatch", "exportXlsUrl", "importExcelUrl", "computed", "concat", "window", "_CONFIG", "methods"], "sources": ["src/views/modules/message/SysMessageList.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n        <a-row :gutter=\"24\">\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"消息标题\">\n              <a-input placeholder=\"请输入消息标题\" v-model=\"queryParam.esTitle\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"发送内容\">\n              <a-input placeholder=\"请输入发送内容\" v-model=\"queryParam.esContent\"></a-input>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"toggleSearchStatus\">\n            <a-col :md=\"6\" :sm=\"8\">\n              <a-form-item label=\"接收人\">\n                <a-input placeholder=\"请输入接收人\" v-model=\"queryParam.esReceiver\"></a-input>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :md=\"6\" :sm=\"8\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n                {{ toggleSearchStatus ? '收起' : '展开' }}\n                <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" v-show=\"show\" type=\"primary\" icon=\"plus\">新增</a-button>\n      <a-button type=\"primary\" v-show=\"show\" icon=\"download\" @click=\"handleExportXls('消息')\">导出</a-button>\n      <a-upload v-show=\"show\" name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\"\n                @change=\"handleImportExcel\">\n        <a-button type=\"primary\" icon=\"import\">导入</a-button>\n      </a-upload>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{\n        selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <!-- 字符串超长截取省略号显示-->\n        <span slot=\"esContent\" slot-scope=\"text\">\n          <j-ellipsis :value=\"text\" :length=\"10\" />\n        </span>\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a href=\"javascript:;\" @click=\"handleDetail(record)\">详情</a>\n          <a-divider type=\"vertical\"/>\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多<a-icon type=\"down\"/></a>\n            <a-menu slot=\"overlay\">\n               <a-menu-item v-show=\"show\">\n                <a  @click=\"handleEdit(record)\">编辑</a>\n              </a-menu-item>\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <!-- 表单区域 -->\n    <sysMessage-modal ref=\"modalForm\" @ok=\"modalFormOk\"></sysMessage-modal>\n  </a-card>\n</template>\n\n<script>\n  import SysMessageModal from './modules/SysMessageModal'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n  import JEllipsis from \"@/components/jeecg/JEllipsis\";\n\n  export default {\n    name: \"SysMessageList\",\n    mixins: [JeecgListMixin],\n    components: {\n      JEllipsis,\n      SysMessageModal\n    },\n    data() {\n      return {\n        description: '消息管理页面',\n        // 新增修改按钮是否显示\n        show: false,\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key: 'rowIndex',\n            width: 60,\n            align: \"center\",\n            customRender: function (t, r, index) {\n              return parseInt(index) + 1;\n            }\n          },\n          {\n            title: '消息标题',\n            align: \"center\",\n            dataIndex: 'esTitle'\n          },\n          {\n            title: '发送内容',\n            align: \"center\",\n            dataIndex: 'esContent',\n            scopedSlots: {customRender: 'esContent'},\n          },\n          {\n            title: '接收人',\n            align: \"center\",\n            dataIndex: 'esReceiver'\n          },\n          {\n            title: '发送次数',\n            align: \"center\",\n            dataIndex: 'esSendNum'\n          },\n          {\n            title: '发送状态',\n            align: 'center',\n            dataIndex: 'esSendStatus_dictText'\n          },\n          {\n            title: '发送时间',\n            align: \"center\",\n            dataIndex: 'esSendTime'\n          },\n          {\n            title: '发送方式',\n            align: 'center',\n            dataIndex: 'esType_dictText'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: \"center\",\n            scopedSlots: {customRender: 'action'},\n          }\n        ],\n        url: {\n          list: \"/message/sysMessage/list\",\n          delete: \"/message/sysMessage/delete\",\n          deleteBatch: \"/message/sysMessage/deleteBatch\",\n          exportXlsUrl: \"message/sysMessage/exportXls\",\n          importExcelUrl: \"message/sysMessage/importExcel\",\n        },\n      }\n    },\n    computed: {\n      importExcelUrl: function () {\n        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n      }\n    },\n    methods: {}\n  }\n</script>\n<style lang=\"less\" scoped>\n  /** Button按钮间距 */\n  .ant-btn {\n    margin-left: 3px\n  }\n\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n</style>"], "mappings": "AAiHA,OAAAA,eAAA;AACA,SAAAC,cAAA;AACA,OAAAC,SAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAH,cAAA;EACAI,UAAA;IACAH,SAAA,EAAAA,SAAA;IACAF,eAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,IAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,WAAAA,aAAAC,CAAA,EAAAC,CAAA,EAAAC,KAAA;UACA,OAAAC,QAAA,CAAAD,KAAA;QACA;MACA,GACA;QACAR,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;QACAS,WAAA;UAAAL,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;QACAG,KAAA;QACAM,WAAA;UAAAL,YAAA;QAAA;MACA,EACA;MACAM,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;MACA;IACA;EACA;EACAC,QAAA;IACAD,cAAA,WAAAA,eAAA;MACA,UAAAE,MAAA,CAAAC,MAAA,CAAAC,OAAA,oBAAAF,MAAA,MAAAP,GAAA,CAAAK,cAAA;IACA;EACA;EACAK,OAAA;AACA", "ignoreList": []}]}