{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue?vue&type=template&id=1de75ee0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\table\\StandardTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"standard-table\">\n  <div class=\"alert\">\n    <a-alert type=\"info\" :show-icon=\"true\">\n      <div slot=\"message\">\n        已选择&nbsp;<a style=\"font-weight: 600\">{{ selectedRows.length }}</a>&nbsp;&nbsp;\n        <template v-for=\"(item, index) in needTotalList\" v-if=\"item.needTotal\">\n          {{ item.title }} 总计&nbsp;\n          <a :key=\"index\" style=\"font-weight: 600\">\n            {{ item.customRender ? item.customRender(item.total) : item.total }}\n          </a>&nbsp;&nbsp;\n        </template>\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n    </a-alert>\n  </div>\n  <a-table\n    :size=\"size\"\n    :bordered=\"bordered\"\n    :loading=\"loading\"\n    :columns=\"columns\"\n    :dataSource=\"current\"\n    :rowKey=\"rowKey\"\n    :pagination=\"pagination\"\n    :rowSelection=\"{ selectedRowKeys: selectedRowKeys, onChange: updateSelect }\"\n  >\n  </a-table>\n</div>\n", null]}