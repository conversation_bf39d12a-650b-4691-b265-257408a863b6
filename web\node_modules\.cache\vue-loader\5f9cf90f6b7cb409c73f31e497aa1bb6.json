{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { MineWorksPage } from './page'\n  import { mapGetters } from 'vuex'\n  import { getFileAccessHttpUrl } from '@/api/manage';\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout,\n      MineWorksPage\n    },\n    data() {\n      return {\n        tabListNoTitle: [{\n            key: 'mineWorks',\n            tab: '我的作品',\n          }\n          // , {\n          //   key: 'greatWorks',\n          //   tab: '精选作品',\n          // }\n        ],\n        noTitleKey: 'mineWorks',\n      }\n    },\n    mounted () {\n      this.getWorks()\n    },\n    methods: {\n      ...mapGetters([\"nickname\", \"avatar\"]),\n      getAvatar(){\n          return getFileAccessHttpUrl(this.avatar());\n      },\n      getTeams() {\n        this.$http.get('/api/workplace/teams')\n          .then(res => {\n            this.teams = res.result\n            this.teamSpinning = false\n          })\n      },\n\n      getWorks(){\n\n      },\n\n      handleTabChange (key, type) {\n        this[type] = key\n      },\n\n\n      showTagInput () {\n        this.tagInputVisible = true\n        this.$nextTick(() => {\n          this.$refs.tagInput.focus()\n        })\n      },\n\n      handleInputChange (e) {\n        this.tagInputValue = e.target.value\n      }\n    },\n  }\n", {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";AAmBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Index.vue", "sourceRoot": "src/views/account/center", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide page-header-wrapper-grid-content-main\">\n    <a-row :gutter=\"24\">\n      <a-col :md=\"24\" :lg=\"24\">\n        <a-card\n          style=\"width:100%\"\n          :bordered=\"false\"\n          :tabList=\"tabListNoTitle\"\n          :activeTabKey=\"noTitleKey\"\n          @tabChange=\"key => handleTabChange(key, 'noTitleKey')\"\n        >\n          <mineWorks-page v-if=\"noTitleKey === 'mineWorks'\"></mineWorks-page>\n        </a-card>\n      </a-col>\n    </a-row>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { MineWorksPage } from './page'\n  import { mapGetters } from 'vuex'\n  import { getFileAccessHttpUrl } from '@/api/manage';\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout,\n      MineWorksPage\n    },\n    data() {\n      return {\n        tabListNoTitle: [{\n            key: 'mineWorks',\n            tab: '我的作品',\n          }\n          // , {\n          //   key: 'greatWorks',\n          //   tab: '精选作品',\n          // }\n        ],\n        noTitleKey: 'mineWorks',\n      }\n    },\n    mounted () {\n      this.getWorks()\n    },\n    methods: {\n      ...mapGetters([\"nickname\", \"avatar\"]),\n      getAvatar(){\n          return getFileAccessHttpUrl(this.avatar());\n      },\n      getTeams() {\n        this.$http.get('/api/workplace/teams')\n          .then(res => {\n            this.teams = res.result\n            this.teamSpinning = false\n          })\n      },\n\n      getWorks(){\n\n      },\n\n      handleTabChange (key, type) {\n        this[type] = key\n      },\n\n\n      showTagInput () {\n        this.tagInputVisible = true\n        this.$nextTick(() => {\n          this.$refs.tagInput.focus()\n        })\n      },\n\n      handleInputChange (e) {\n        this.tagInputValue = e.target.value\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .page-header-wrapper-grid-content-main {\n    width: 100%;\n    height: 100%;\n    min-height: 100%;\n    transition: .3s;\n\n    .account-center-avatarHolder {\n      text-align: center;\n      margin-bottom: 24px;\n\n      & > .avatar {\n        margin: 0 auto;\n        width: 104px;\n        height: 104px;\n        margin-bottom: 20px;\n        border-radius: 50%;\n        overflow: hidden;\n        img {\n          height: 100%;\n          width: 100%;\n        }\n      }\n\n      .username {\n        color: rgba(0, 0, 0, 0.85);\n        font-size: 20px;\n        line-height: 28px;\n        font-weight: 500;\n        margin-bottom: 4px;\n      }\n    }\n\n    .account-center-detail {\n\n      p {\n        margin-bottom: 8px;\n        padding-left: 26px;\n        position: relative;\n      }\n\n      i {\n        position: absolute;\n        height: 14px;\n        width: 14px;\n        left: 0;\n        top: 4px;\n        background: url(https://gw.alipayobjects.com/zos/rmsportal/pBjWzVAHnOOtAUvZmZfy.svg)\n      }\n\n      .title {\n        background-position: 0 0;\n      }\n      .group {\n        background-position: 0 -22px;\n      }\n      .address {\n        background-position: 0 -44px;\n      }\n    }\n\n    .account-center-tags {\n      .ant-tag {\n        margin-bottom: 8px;\n      }\n    }\n\n    .account-center-team {\n\n      .members {\n        a {\n          display: block;\n          margin: 12px 0;\n          line-height: 24px;\n          height: 24px;\n          .member {\n            font-size: 14px;\n            color: rgba(0, 0, 0, .65);\n            line-height: 24px;\n            max-width: 100px;\n            vertical-align: top;\n            margin-left: 12px;\n            transition: all 0.3s;\n            display: inline-block;\n          }\n          &:hover {\n            span {\n              color: #1890ff;\n            }\n          }\n        }\n      }\n    }\n\n    .tagsTitle, .teamTitle {\n      font-weight: 500;\n      color: rgba(0,0,0,.85);\n      margin-bottom: 12px;\n    }\n\n  }\n\n</style>"]}]}