{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue?vue&type=template&id=0edfacbb&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Header.vue", "mtime": 1753199398217}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    class: [\"header\", _vm.menuFixed ? \"menu-fixed\" : \"\"]\n  }, [_c(\"router-link\", {\n    attrs: {\n      to: {\n        path: \"/home\"\n      }\n    }\n  }, [_c(\"img\", {\n    staticClass: \"logo\",\n    attrs: {\n      src: _vm.logo,\n      alt: \"\"\n    }\n  })]), _c(\"t-menu\", {\n    staticClass: \"menu\",\n    attrs: {\n      mode: \"horizontal\",\n      menu: _vm.menus\n    }\n  }), _c(\"div\", {\n    staticClass: \"header-avatar\"\n  }, [_c(\"header-notice\", {\n    staticClass: \"action\"\n  }), _c(\"img\", {\n    staticClass: \"avatar\",\n    attrs: {\n      src: _vm.avatarUrl,\n      alt: \"\"\n    },\n    on: {\n      click: _vm.enter\n    }\n  }), _vm.$store.state.user.info ? _c(\"span\", [_c(\"span\", {\n    on: {\n      click: _vm.enter\n    }\n  }, [_vm._v(_vm._s(_vm.$store.state.user.info.realname))]), _c(\"a-divider\", {\n    attrs: {\n      type: \"vertical\"\n    }\n  }), _c(\"span\", {\n    on: {\n      click: _vm.handleLogout\n    }\n  }, [_vm._v(\"退出\")])], 1) : _c(\"span\", [_c(\"span\", {\n    on: {\n      click: _vm.enter\n    }\n  }, [_vm._v(\"登录\")]), _c(\"a-divider\", {\n    attrs: {\n      type: \"vertical\"\n    }\n  }), _c(\"span\", {\n    on: {\n      click: _vm.enter\n    }\n  }, [_vm._v(\"注册\")])], 1)], 1), _c(\"software-download\", {\n    ref: \"softwareDownload\"\n  }), _c(\"shopping-modal\", {\n    ref: \"shoppingModal\"\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "class", "menuFixed", "attrs", "to", "path", "staticClass", "src", "logo", "alt", "mode", "menu", "menus", "avatarUrl", "on", "click", "enter", "$store", "state", "user", "info", "_v", "_s", "realname", "type", "handleLogout", "ref", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/Header.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { class: [\"header\", _vm.menuFixed ? \"menu-fixed\" : \"\"] },\n    [\n      _c(\"router-link\", { attrs: { to: { path: \"/home\" } } }, [\n        _c(\"img\", { staticClass: \"logo\", attrs: { src: _vm.logo, alt: \"\" } }),\n      ]),\n      _c(\"t-menu\", {\n        staticClass: \"menu\",\n        attrs: { mode: \"horizontal\", menu: _vm.menus },\n      }),\n      _c(\n        \"div\",\n        { staticClass: \"header-avatar\" },\n        [\n          _c(\"header-notice\", { staticClass: \"action\" }),\n          _c(\"img\", {\n            staticClass: \"avatar\",\n            attrs: { src: _vm.avatarUrl, alt: \"\" },\n            on: { click: _vm.enter },\n          }),\n          _vm.$store.state.user.info\n            ? _c(\n                \"span\",\n                [\n                  _c(\"span\", { on: { click: _vm.enter } }, [\n                    _vm._v(_vm._s(_vm.$store.state.user.info.realname)),\n                  ]),\n                  _c(\"a-divider\", { attrs: { type: \"vertical\" } }),\n                  _c(\"span\", { on: { click: _vm.handleLogout } }, [\n                    _vm._v(\"退出\"),\n                  ]),\n                ],\n                1\n              )\n            : _c(\n                \"span\",\n                [\n                  _c(\"span\", { on: { click: _vm.enter } }, [_vm._v(\"登录\")]),\n                  _c(\"a-divider\", { attrs: { type: \"vertical\" } }),\n                  _c(\"span\", { on: { click: _vm.enter } }, [_vm._v(\"注册\")]),\n                ],\n                1\n              ),\n        ],\n        1\n      ),\n      _c(\"software-download\", { ref: \"softwareDownload\" }),\n      _c(\"shopping-modal\", { ref: \"shoppingModal\" }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,KAAK,EAAE,CAAC,QAAQ,EAAEH,GAAG,CAACI,SAAS,GAAG,YAAY,GAAG,EAAE;EAAE,CAAC,EACxD,CACEH,EAAE,CAAC,aAAa,EAAE;IAAEI,KAAK,EAAE;MAAEC,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAQ;IAAE;EAAE,CAAC,EAAE,CACtDN,EAAE,CAAC,KAAK,EAAE;IAAEO,WAAW,EAAE,MAAM;IAAEH,KAAK,EAAE;MAAEI,GAAG,EAAET,GAAG,CAACU,IAAI;MAAEC,GAAG,EAAE;IAAG;EAAE,CAAC,CAAC,CACtE,CAAC,EACFV,EAAE,CAAC,QAAQ,EAAE;IACXO,WAAW,EAAE,MAAM;IACnBH,KAAK,EAAE;MAAEO,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAEb,GAAG,CAACc;IAAM;EAC/C,CAAC,CAAC,EACFb,EAAE,CACA,KAAK,EACL;IAAEO,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEP,EAAE,CAAC,eAAe,EAAE;IAAEO,WAAW,EAAE;EAAS,CAAC,CAAC,EAC9CP,EAAE,CAAC,KAAK,EAAE;IACRO,WAAW,EAAE,QAAQ;IACrBH,KAAK,EAAE;MAAEI,GAAG,EAAET,GAAG,CAACe,SAAS;MAAEJ,GAAG,EAAE;IAAG,CAAC;IACtCK,EAAE,EAAE;MAAEC,KAAK,EAAEjB,GAAG,CAACkB;IAAM;EACzB,CAAC,CAAC,EACFlB,GAAG,CAACmB,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,IAAI,GACtBrB,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CAAC,MAAM,EAAE;IAAEe,EAAE,EAAE;MAAEC,KAAK,EAAEjB,GAAG,CAACkB;IAAM;EAAE,CAAC,EAAE,CACvClB,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACmB,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,IAAI,CAACG,QAAQ,CAAC,CAAC,CACpD,CAAC,EACFxB,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEqB,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,EAChDzB,EAAE,CAAC,MAAM,EAAE;IAAEe,EAAE,EAAE;MAAEC,KAAK,EAAEjB,GAAG,CAAC2B;IAAa;EAAE,CAAC,EAAE,CAC9C3B,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,EACD,CACF,CAAC,GACDtB,EAAE,CACA,MAAM,EACN,CACEA,EAAE,CAAC,MAAM,EAAE;IAAEe,EAAE,EAAE;MAAEC,KAAK,EAAEjB,GAAG,CAACkB;IAAM;EAAE,CAAC,EAAE,CAAClB,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACxDtB,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEqB,IAAI,EAAE;IAAW;EAAE,CAAC,CAAC,EAChDzB,EAAE,CAAC,MAAM,EAAE;IAAEe,EAAE,EAAE;MAAEC,KAAK,EAAEjB,GAAG,CAACkB;IAAM;EAAE,CAAC,EAAE,CAAClB,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,EACD,CACF,CAAC,CACN,EACD,CACF,CAAC,EACDtB,EAAE,CAAC,mBAAmB,EAAE;IAAE2B,GAAG,EAAE;EAAmB,CAAC,CAAC,EACpD3B,EAAE,CAAC,gBAAgB,EAAE;IAAE2B,GAAG,EAAE;EAAgB,CAAC,CAAC,CAC/C,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI;AAE3B,SAAS/B,MAAM,EAAE8B,eAAe", "ignoreList": []}]}