{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniArea.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import moment from 'dayjs'\n\n  const sourceData = []\n  const beginDay = new Date().getTime()\n\n  for (let i = 0; i < 10; i++) {\n    sourceData.push({\n      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n      y: Math.round(Math.random() * 10)\n    })\n  }\n\n  export default {\n    name: 'MiniArea',\n    props: {\n      dataSource: {\n        type: Array,\n        default: () => []\n      },\n      // x 轴别名\n      x: {\n        type: String,\n        default: 'x'\n      },\n      // y 轴别名\n      y: {\n        type: String,\n        default: 'y'\n      }\n    },\n    data() {\n      return {\n        data: [],\n        height: 100\n      }\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      if (this.dataSource.length === 0) {\n        this.data = sourceData\n      } else {\n        this.data = this.dataSource\n      }\n    }\n  }\n", {"version": 3, "sources": ["MiniArea.vue"], "names": [], "mappings": ";AAYA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MiniArea.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div class=\"antv-chart-mini\">\n    <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\n      <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :scale=\"scale\" :padding=\"[36, 0, 18, 0]\">\n        <v-tooltip/>\n        <v-smooth-area position=\"x*y\"/>\n      </v-chart>\n    </div>\n  </div>\n</template>\n\n<script>\n  import moment from 'dayjs'\n\n  const sourceData = []\n  const beginDay = new Date().getTime()\n\n  for (let i = 0; i < 10; i++) {\n    sourceData.push({\n      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n      y: Math.round(Math.random() * 10)\n    })\n  }\n\n  export default {\n    name: 'MiniArea',\n    props: {\n      dataSource: {\n        type: Array,\n        default: () => []\n      },\n      // x 轴别名\n      x: {\n        type: String,\n        default: 'x'\n      },\n      // y 轴别名\n      y: {\n        type: String,\n        default: 'y'\n      }\n    },\n    data() {\n      return {\n        data: [],\n        height: 100\n      }\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      if (this.dataSource.length === 0) {\n        this.data = sourceData\n      } else {\n        this.data = this.dataSource\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"chart\";\n</style>"]}]}