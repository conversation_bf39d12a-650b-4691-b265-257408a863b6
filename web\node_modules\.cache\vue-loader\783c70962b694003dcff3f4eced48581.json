{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue?vue&type=style&index=0&id=7d6a815f&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.j-area-linkage {\n  height:40px;\n  /deep/ .area-cascader-wrap .area-select {\n    width: 100%;\n  }\n\n  /deep/ .area-select .area-selected-trigger {\n    line-height: 1.15;\n  }\n}\n\n", {"version": 3, "sources": ["JAreaLinkage.vue"], "names": [], "mappings": ";AA4JA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "JAreaLinkage.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <div v-if=\"!reloading\" class=\"j-area-linkage\">\n    <area-cascader\n      v-if=\"_type === enums.type[0]\"\n      :value=\"innerValue\"\n      :data=\"pcaa\"\n      :level=\"1\"\n      :style=\"{width}\"\n      v-bind=\"$attrs\"\n      v-on=\"_listeners\"\n      @change=\"handleChange\"\n    />\n    <area-select\n      v-else-if=\"_type === enums.type[1]\"\n      :value=\"innerValue\"\n      :data=\"pcaa\"\n      :level=\"2\"\n      v-bind=\"$attrs\"\n      v-on=\"_listeners\"\n      @change=\"handleChange\"\n    />\n    <div v-else>\n      <span style=\"color:red;\"> Bad type value: {{_type}}</span>\n    </div>\n  </div>\n</template>\n\n<script>\n  import { pcaa } from 'area-data'\n  import Area from '@/components/_util/Area'\n\n  export default {\n    name: 'JAreaLinkage',\n    props: {\n      value: {\n        type: String,\n        required:false\n      },\n      // 组件的类型，可选值：\n      // select 下拉样式\n      // cascader 级联样式（默认）\n      type: {\n        type: String,\n        default: 'cascader'\n      },\n      width: {\n        type: String,\n        default: '100%'\n      }\n    },\n    data() {\n      return {\n        pcaa,\n        innerValue: [],\n        usedListeners: ['change'],\n        enums: {\n          type: ['cascader', 'select']\n        },\n        reloading: false,\n        areaData:''\n      }\n    },\n    computed: {\n      _listeners() {\n        let listeners = { ...this.$listeners }\n        // 去掉已使用的事件，防止冲突\n        this.usedListeners.forEach(key => {\n          delete listeners[key]\n        })\n        return listeners\n      },\n      _type() {\n        if (this.enums.type.includes(this.type)) {\n          return this.type\n        } else {\n          console.error(`JAreaLinkage的type属性只能接收指定的值（${this.enums.type.join('|')}）`)\n          return this.enums.type[0]\n        }\n      },\n    },\n    watch: {\n      value: {\n        immediate: true,\n        handler() {\n          this.loadDataByValue(this.value)\n        }\n      },\n    },\n    created() {\n      this.initAreaData();\n    },\n    methods: {\n      /** 通过 value 反推 options */\n      loadDataByValue(value) {\n        if(!value || value.length==0){\n          this.innerValue = []\n          this.reloading = true;\n          setTimeout(()=>{\n            this.reloading = false\n          },100)\n        }else{\n          this.initAreaData();\n          let arr = this.areaData.getRealCode(value);\n          this.innerValue = arr\n        }\n      },\n      /** 通过地区code获取子级 */\n      loadDataByCode(value) {\n        let options = []\n        let data = pcaa[value]\n        if (data) {\n          for (let key in data) {\n            if (data.hasOwnProperty(key)) {\n              options.push({ value: key, label: data[key], })\n            }\n          }\n          return options\n        } else {\n          return []\n        }\n      },\n      /** 判断是否有子节点 */\n      hasChildren(options) {\n        options.forEach(option => {\n          let data = this.loadDataByCode(option.value)\n          option.isLeaf = data.length === 0\n        })\n      },\n      handleChange(values) {\n        let value = values[values.length - 1]\n        this.$emit('change', value)\n      },\n      initAreaData(){\n        if(!this.areaData){\n          this.areaData = new Area();\n        }\n      },\n\n    },\n    model: { prop: 'value', event: 'change' },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .j-area-linkage {\n    height:40px;\n    /deep/ .area-cascader-wrap .area-select {\n      width: 100%;\n    }\n\n    /deep/ .area-select .area-selected-trigger {\n      line-height: 1.15;\n    }\n  }\n\n</style>"]}]}