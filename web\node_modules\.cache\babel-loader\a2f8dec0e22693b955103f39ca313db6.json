{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\Index.vue", "mtime": 1746672706055}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { getFileAccessHttpUrl } from '@/api/manage';\nexport default _defineProperty({\n  components: {},\n  data: function data() {\n    return {\n      sysConfig: {}\n    };\n  },\n  watch: {},\n  methods: {},\n  created: function created() {\n    this.sysConfig = this.$store.getters.sysConfig;\n  },\n  mounted: function mounted() {}\n}, \"methods\", {\n  getFileAccessHttpUrl: getFileAccessHttpUrl\n});", {"version": 3, "names": ["getFileAccessHttpUrl", "_defineProperty", "components", "data", "sysConfig", "watch", "methods", "created", "$store", "getters", "mounted"], "sources": ["src/views/home/<USER>"], "sourcesContent": ["<template>\n  <!-- <div id=\"IndexPage\" :style=\"{ background: sysConfig.homeBgColor }\"> -->\n    <!-- <div :style=\"{ 'background-image': 'url(' + getFileAccessHttpUrl(sysConfig.file_homeBg) + ')', 'background-repeat': sysConfig.homeBgRepeat }\"> -->\n      <div class=\"boxBackground\">\n        <div class=\"boxContent\" v-html=\"sysConfig._homeHtml\"></div>\n      </div>\n    <!-- </div> -->\n  <!-- </div> -->\n</template>\n<script>\nimport { getFileAccessHttpUrl } from '@/api/manage'\nexport default {\n  components: {},\n  data() {\n    return {\n      sysConfig: {},\n    }\n  },\n  watch: {},\n  methods: {},\n  created() {\n    this.sysConfig = this.$store.getters.sysConfig\n  },\n  mounted() {},\n  methods:{\n    getFileAccessHttpUrl\n  }\n}\n</script>\n<style lang='less' scoped>\n.slick-arrow {\n  border-radius: 50%;\n  text-align: center;\n  height: 40px;\n  line-height: 40px;\n  width: 40px;\n  background: #364e799a;\n  overflow: hidden;\n  color: #fff;\n  opacity: 0.5;\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n.slick-arrow:hover {\n  background: #364e79;\n  color: #fff;\n  opacity: 1;\n}\n#IndexPage {\n  width: 100%;\n  height: auto;\n  margin-top: 28px;\n  .banner {\n    width: 100%;\n    height: 100%;\n    background: #ccc;\n  }\n  .boxBackground {\n    width: 100%;\n    // background: #030149;\n    .boxContent {\n      width: 1200px;\n      margin: 0 auto;\n      height: 100%;\n      overflow: hidden;\n      // max-height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n\n      /deep/img {\n        max-width: 100%;\n      }\n    }\n  }\n}\n</style>"], "mappings": ";;;;AAUA,SAAAA,oBAAA;AACA,eAAAC,eAAA;EACAC,UAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAH,SAAA,QAAAI,MAAA,CAAAC,OAAA,CAAAL,SAAA;EACA;EACAM,OAAA,WAAAA,QAAA;AAAA,cACA;EACAV,oBAAA,EAAAA;AACA", "ignoreList": []}]}