{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Footer.vue", "mtime": 1749545283056}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: 'Footer'\n};", {"version": 3, "names": ["name"], "sources": ["src/views/home/<USER>/Footer.vue"], "sourcesContent": ["<template>\n  <div class=\"footer-container\">\n    <div class=\"footer-content\">\n      <div class=\"footer-left\">\n        <div class=\"brand\">\n          <h3>CFish科技工作室</h3>\n          <p>科技少儿编程教学平台</p>\n        </div>\n        <div class=\"slogan\">赋能未来 · 智创童年</div>\n      </div>\n      \n      <div class=\"footer-right\">\n        <div class=\"contact-info\">\n          <a-icon type=\"phone\" /> 联系电话: 19866725905\n          <a-icon type=\"mail\" style=\"margin-left: 15px\" /> 邮箱: <EMAIL>\n        </div>\n        <div class=\"social-media\">\n          <a-popover placement=\"top\" trigger=\"hover\">\n            <template slot=\"content\">\n              <div class=\"qrcode-container\">\n                <img src=\"/img/wechat-qrcode.png\" alt=\"微信二维码\" class=\"qrcode-img\" />\n                <p>微信</p>\n              </div>\n            </template>\n            <a-icon type=\"wechat\" class=\"social-icon\" />\n          </a-popover>\n          \n          <a-popover placement=\"top\" trigger=\"hover\">\n            <template slot=\"content\">\n              <div class=\"qrcode-container\">\n                <img src=\"/img/qq-qrcode.png\" alt=\"QQ二维码\" class=\"qrcode-img\" />\n                <p>QQ</p>\n              </div>\n            </template>\n            <a-icon type=\"qq\" class=\"social-icon\" />\n          </a-popover>\n        </div>\n      </div>\n    </div>\n    <div class=\"footer-bottom\">\n      <p>© {{ new Date().getFullYear() }} CFish科技工作室 - 版权所有</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Footer'\n}\n</script>\n\n<style lang=\"less\" scoped>\n.footer-container {\n  width: 100vw;\n  background: linear-gradient(90deg, #1976d2, #42a5f5);\n  color: #fff;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\n  position: relative;\n  left: 50%;\n  right: 50%;\n  margin-left: -50vw;\n  margin-right: -50vw;\n  padding: 0;\n  overflow: hidden;\n  box-sizing: border-box;\n}\n\n.footer-content {\n  width: 100%;\n  padding: 30px 8%;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  box-sizing: border-box;\n}\n\n.footer-left {\n  padding-left: 10px;\n  min-width: 250px;\n  \n  .brand {\n    h3 {\n      color: #fff;\n      margin: 0;\n      font-size: 20px;\n      font-weight: bold;\n    }\n    p {\n      color: rgba(255, 255, 255, 0.8);\n      margin-top: 5px;\n    }\n  }\n  \n  .slogan {\n    margin-top: 10px;\n    font-size: 14px;\n    color: rgba(255, 255, 255, 0.7);\n    position: relative;\n    padding-left: 10px;\n    \n    &:before {\n      content: \"\";\n      position: absolute;\n      left: 0;\n      top: 2px;\n      height: 15px;\n      width: 3px;\n      background: #4fc3f7;\n    }\n  }\n}\n\n.footer-right {\n  text-align: right;\n  padding-right: 10px;\n  min-width: 250px;\n  \n  .contact-info {\n    color: rgba(255, 255, 255, 0.9);\n    font-size: 14px;\n  }\n  \n  .social-media {\n    margin-top: 10px;\n    \n    .social-icon {\n      font-size: 20px;\n      margin-left: 15px;\n      color: rgba(255, 255, 255, 0.8);\n      cursor: pointer;\n      transition: all 0.3s ease;\n      \n      &:hover {\n        color: #4fc3f7;\n        transform: translateY(-2px);\n      }\n    }\n  }\n}\n\n.qrcode-container {\n  text-align: center;\n  padding: 5px;\n  \n  .qrcode-img {\n    width: 120px;\n    height: 120px;\n    object-fit: cover;\n  }\n  \n  p {\n    margin: 5px 0 0;\n    font-size: 12px;\n    color: #333;\n  }\n}\n\n.footer-bottom {\n  width: 100%;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 15px 0;\n  text-align: center;\n  background-color: rgba(0, 0, 0, 0.1);\n  \n  p {\n    margin: 0;\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 768px) {\n  .footer-content {\n    flex-direction: column;\n    text-align: center;\n    padding: 20px 15px;\n  }\n  \n  .footer-left, .footer-right {\n    width: 100%;\n    margin-bottom: 20px;\n    text-align: center;\n    padding: 0;\n  }\n  \n  .slogan:before {\n    display: none;\n  }\n}\n</style>"], "mappings": "AA8CA;EACAA,IAAA;AACA", "ignoreList": []}]}