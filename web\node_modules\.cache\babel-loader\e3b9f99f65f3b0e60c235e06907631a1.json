{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderDMainList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderDMainList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import JeecgOrderDMainModal from './form/JeecgOrderDMainModal';\nimport JeecgOrderCustomerList from './JeecgOrderCustomerList';\nimport JeecgOrderTicketList from './JeecgOrderTicketList';\nimport { deleteAction } from '@/api/manage';\nimport JeecgOrderCustomerModal from './form/JeecgOrderCustomerModal';\nimport JeecgOrderTicketModal from './form/JeecgOrderTicketModal';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nexport default {\n  name: \"JeecgOrderDMainList\",\n  mixins: [JeecgListMixin],\n  components: {\n    JeecgOrderTicketModal: JeecgOrderTicketModal,\n    JeecgOrderCustomerModal: JeecgOrderCustomerModal,\n    JeecgOrderDMainModal: JeecgOrderDMainModal,\n    JeecgOrderCustomerList: JeecgOrderCustomerList,\n    JeecgOrderTicketList: JeecgOrderTicketList\n  },\n  data: function data() {\n    return {\n      description: '订单管理页面',\n      /* 分页参数 */\n      ipagination: {\n        current: 1,\n        pageSize: 5,\n        pageSizeOptions: ['5', '10', '20'],\n        showTotal: function showTotal(total, range) {\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\";\n        },\n        showQuickJumper: true,\n        showSizeChanger: true,\n        total: 0\n      },\n      // 表头\n      columns: [{\n        title: '#',\n        dataIndex: '',\n        key: 'rowIndex',\n        width: 60,\n        align: \"center\",\n        customRender: function customRender(t, r, index) {\n          return parseInt(index) + 1;\n        }\n      }, {\n        title: '订单号',\n        align: \"center\",\n        dataIndex: 'orderCode'\n      }, {\n        title: '订单类型',\n        align: \"center\",\n        dataIndex: 'ctype',\n        customRender: function customRender(text) {\n          var re = \"\";\n          if (text === '1') {\n            re = \"国内订单\";\n          } else if (text === '2') {\n            re = \"国际订单\";\n          }\n          return re;\n        }\n      }, {\n        title: '订单日期',\n        align: \"center\",\n        dataIndex: 'orderDate'\n      }, {\n        title: '订单金额',\n        align: \"center\",\n        dataIndex: 'orderMoney'\n      }, {\n        title: '订单备注',\n        align: \"center\",\n        dataIndex: 'content'\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        align: \"center\",\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 分页参数\n      type: \"radio\",\n      url: {\n        list: \"/test/order/orderList\",\n        delete: \"/test/order/delete\",\n        deleteBatch: \"/test/order/deleteBatch\"\n      }\n    };\n  },\n  methods: {\n    clickThenCheck: function clickThenCheck(record) {\n      var _this = this;\n      return {\n        on: {\n          click: function click() {\n            _this.onSelectChange(record.id.split(\",\"), [record]);\n          }\n        }\n      };\n    },\n    onSelectChange: function onSelectChange(selectedRowKeys, selectionRows) {\n      this.selectedRowKeys = selectedRowKeys;\n      this.selectionRows = selectionRows;\n      this.$refs.JeecgOrderCustomerList.getOrderMain(this.selectedRowKeys[0]);\n      this.$refs.JeecgOrderTicketList.getOrderMain(this.selectedRowKeys[0]);\n    },\n    onClearSelected: function onClearSelected() {\n      this.selectedRowKeys = [];\n      this.selectionRows = [];\n      this.$refs.JeecgOrderCustomerList.queryParam.mainId = null;\n      this.$refs.JeecgOrderTicketList.queryParam.mainId = null;\n      this.$refs.JeecgOrderCustomerList.loadData();\n      this.$refs.JeecgOrderTicketList.loadData();\n      this.$refs.JeecgOrderCustomerList.selectedRowKeys = [];\n      this.$refs.JeecgOrderCustomerList.selectionRows = [];\n      this.$refs.JeecgOrderTicketList.selectedRowKeys = [];\n      this.$refs.JeecgOrderTicketList.selectionRows = [];\n    },\n    handleDelete: function handleDelete(id) {\n      var _this2 = this;\n      var that = this;\n      deleteAction(that.url.delete, {\n        id: id\n      }).then(function (res) {\n        if (res.success) {\n          that.$message.success(res.message);\n          that.loadData();\n          _this2.$refs.JeecgOrderCustomerList.loadData();\n          _this2.$refs.JeecgOrderTicketList.loadData();\n        } else {\n          that.$message.warning(res.message);\n        }\n      });\n    },\n    searchQuery: function searchQuery() {\n      this.selectedRowKeys = [];\n      this.selectionRows = [];\n      this.$refs.JeecgOrderCustomerList.queryParam.mainId = null;\n      this.$refs.JeecgOrderTicketList.queryParam.mainId = null;\n      this.$refs.JeecgOrderCustomerList.loadData();\n      this.$refs.JeecgOrderTicketList.loadData();\n      this.$refs.JeecgOrderCustomerList.selectedRowKeys = [];\n      this.$refs.JeecgOrderCustomerList.selectionRows = [];\n      this.$refs.JeecgOrderTicketList.selectedRowKeys = [];\n      this.$refs.JeecgOrderTicketList.selectionRows = [];\n      this.loadData();\n    }\n  }\n};", {"version": 3, "names": ["JeecgOrderDMainModal", "JeecgOrderCustomerList", "JeecgOrderTicketList", "deleteAction", "JeecgOrderCustomerModal", "JeecgOrderTicketModal", "JeecgListMixin", "name", "mixins", "components", "data", "description", "ipagination", "current", "pageSize", "pageSizeOptions", "showTotal", "total", "range", "showQuickJumper", "showSizeChanger", "columns", "title", "dataIndex", "key", "width", "align", "customRender", "t", "r", "index", "parseInt", "text", "re", "scopedSlots", "type", "url", "list", "delete", "deleteBatch", "methods", "clickThen<PERSON>heck", "record", "_this", "on", "click", "onSelectChange", "id", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectionRows", "$refs", "getOrderMain", "onClearSelected", "queryParam", "mainId", "loadData", "handleDelete", "_this2", "that", "then", "res", "success", "$message", "message", "warning", "searchQuery"], "sources": ["src/views/jeecg/tablist/JeecgOrderDMainList.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"24\">\n\n          <a-col :md=\"6\" :sm=\"24\">\n            <a-form-item label=\"订单号\">\n              <a-input placeholder=\"请输入订单号\" v-model=\"queryParam.orderCode\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"24\">\n            <a-form-item label=\"订单类型\">\n              <a-select placeholder=\"请输入订单类型\" v-model=\"queryParam.ctype\">\n                <a-select-option value=\"1\">国内订单</a-select-option>\n                <a-select-option value=\"2\">国际订单</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n\n          <a-col :md=\"6\" :sm=\"24\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n            </span>\n          </a-col>\n\n        </a-row>\n      </a-form>\n    </div>\n\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        filterMultiple=\"filterMultiple\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type:type}\"\n        @change=\"handleTableChange\"\n        :customRow=\"clickThenCheck\"\n      >\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n          <a-divider type=\"vertical\"/>\n          <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n            <a>删除</a>\n          </a-popconfirm>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <a-tabs defaultActiveKey=\"1\">\n      <a-tab-pane tab=\"客户信息\" key=\"1\">\n        <Jeecg-Order-Customer-List ref=\"JeecgOrderCustomerList\"></Jeecg-Order-Customer-List>\n      </a-tab-pane>\n      <a-tab-pane tab=\"机票信息\" key=\"2\" forceRender>\n        <Jeecg-Order-Ticket-List ref=\"JeecgOrderTicketList\"></Jeecg-Order-Ticket-List>\n      </a-tab-pane>\n    </a-tabs>\n\n    <!-- 表单区域 -->\n    <jeecgOrderDMain-modal ref=\"modalForm\" @ok=\"modalFormOk\"></jeecgOrderDMain-modal>\n\n  </a-card>\n</template>\n\n<script>\n  import JeecgOrderDMainModal from './form/JeecgOrderDMainModal'\n  import JeecgOrderCustomerList from './JeecgOrderCustomerList'\n  import JeecgOrderTicketList from './JeecgOrderTicketList'\n  import {deleteAction} from '@/api/manage'\n  import JeecgOrderCustomerModal from './form/JeecgOrderCustomerModal'\n  import JeecgOrderTicketModal from './form/JeecgOrderTicketModal'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n\n  export default {\n    name: \"JeecgOrderDMainList\",\n    mixins: [JeecgListMixin],\n    components: {\n      JeecgOrderTicketModal,\n      JeecgOrderCustomerModal,\n      JeecgOrderDMainModal,\n      JeecgOrderCustomerList,\n      JeecgOrderTicketList,\n    },\n    data() {\n      return {\n        description: '订单管理页面',\n        /* 分页参数 */\n        ipagination:{\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['5', '10', '20'],\n          showTotal: (total, range) => {\n            return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        // 表头\n        columns: [{\n          title: '#',\n          dataIndex: '',\n          key: 'rowIndex',\n          width: 60,\n          align: \"center\",\n          customRender: function (t, r, index) {\n            return parseInt(index) + 1;\n          }\n        },\n          {\n            title: '订单号',\n            align: \"center\",\n            dataIndex: 'orderCode'\n          },\n          {\n            title: '订单类型',\n            align: \"center\",\n            dataIndex: 'ctype',\n            customRender: (text) => {\n              let re = \"\";\n              if (text === '1') {\n                re = \"国内订单\";\n              } else if (text === '2') {\n                re = \"国际订单\";\n              }\n              return re;\n            }\n          },\n          {\n            title: '订单日期',\n            align: \"center\",\n            dataIndex: 'orderDate'\n          },\n          {\n            title: '订单金额',\n            align: \"center\",\n            dataIndex: 'orderMoney'\n          },\n          {\n            title: '订单备注',\n            align: \"center\",\n            dataIndex: 'content'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align: \"center\",\n            scopedSlots: {customRender: 'action'},\n          }],\n        // 分页参数\n        type: \"radio\",\n        url: {\n          list: \"/test/order/orderList\",\n          delete: \"/test/order/delete\",\n          deleteBatch: \"/test/order/deleteBatch\",\n        },\n      }\n    },\n    methods: {\n      clickThenCheck(record) {\n        return {\n          on: {\n            click: () => {\n              this.onSelectChange(record.id.split(\",\"), [record]);\n            }\n          }\n        };\n      },\n      onSelectChange(selectedRowKeys, selectionRows) {\n        this.selectedRowKeys = selectedRowKeys;\n        this.selectionRows = selectionRows;\n        this.$refs.JeecgOrderCustomerList.getOrderMain(this.selectedRowKeys[0]);\n        this.$refs.JeecgOrderTicketList.getOrderMain(this.selectedRowKeys[0]);\n      },\n      onClearSelected() {\n        this.selectedRowKeys = [];\n        this.selectionRows = [];\n        this.$refs.JeecgOrderCustomerList.queryParam.mainId = null;\n        this.$refs.JeecgOrderTicketList.queryParam.mainId = null;\n        this.$refs.JeecgOrderCustomerList.loadData();\n        this.$refs.JeecgOrderTicketList.loadData();\n        this.$refs.JeecgOrderCustomerList.selectedRowKeys = [];\n        this.$refs.JeecgOrderCustomerList.selectionRows = [];\n        this.$refs.JeecgOrderTicketList.selectedRowKeys = [];\n        this.$refs.JeecgOrderTicketList.selectionRows = [];\n      },\n\n      handleDelete: function (id) {\n        var that = this;\n        deleteAction(that.url.delete, {id: id}).then((res) => {\n          if (res.success) {\n            that.$message.success(res.message);\n            that.loadData();\n            this.$refs.JeecgOrderCustomerList.loadData();\n            this.$refs.JeecgOrderTicketList.loadData();\n          } else {\n            that.$message.warning(res.message);\n          }\n        });\n      },\n      searchQuery:function(){\n        this.selectedRowKeys = [];\n        this.selectionRows = [];\n        this.$refs.JeecgOrderCustomerList.queryParam.mainId = null;\n        this.$refs.JeecgOrderTicketList.queryParam.mainId = null;\n        this.$refs.JeecgOrderCustomerList.loadData();\n        this.$refs.JeecgOrderTicketList.loadData();\n        this.$refs.JeecgOrderCustomerList.selectedRowKeys = [];\n        this.$refs.JeecgOrderCustomerList.selectionRows = [];\n        this.$refs.JeecgOrderTicketList.selectedRowKeys = [];\n        this.$refs.JeecgOrderTicketList.selectionRows = [];\n        this.loadData();\n      }\n    }\n  }\n</script>\n<style scoped>\n  .ant-card-body .table-operator {\n    margin-bottom: 18px;\n  }\n\n  .ant-table-tbody .ant-table-row td {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n\n  .anty-row-operator button {\n    margin: 0 5px\n  }\n\n  .ant-btn-danger {\n    background-color: #ffffff\n  }\n\n  .ant-modal-cust-warp {\n    height: 100%\n  }\n\n  .ant-modal-cust-warp .ant-modal-body {\n    height: calc(100% - 110px) !important;\n    overflow-y: auto\n  }\n\n  .ant-modal-cust-warp .ant-modal-content {\n    height: 90% !important;\n    overflow-y: hidden\n  }\n</style>"], "mappings": "AAqGA,OAAAA,oBAAA;AACA,OAAAC,sBAAA;AACA,OAAAC,oBAAA;AACA,SAAAC,YAAA;AACA,OAAAC,uBAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAF,cAAA;EACAG,UAAA;IACAJ,qBAAA,EAAAA,qBAAA;IACAD,uBAAA,EAAAA,uBAAA;IACAJ,oBAAA,EAAAA,oBAAA;IACAC,sBAAA,EAAAA,sBAAA;IACAC,oBAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,eAAA;QACAC,SAAA,WAAAA,UAAAC,KAAA,EAAAC,KAAA;UACA,OAAAA,KAAA,YAAAA,KAAA,aAAAD,KAAA;QACA;QACAE,eAAA;QACAC,eAAA;QACAH,KAAA;MACA;MACA;MACAI,OAAA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,WAAAA,aAAAC,CAAA,EAAAC,CAAA,EAAAC,KAAA;UACA,OAAAC,QAAA,CAAAD,KAAA;QACA;MACA,GACA;QACAR,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;QACAI,YAAA,WAAAA,aAAAK,IAAA;UACA,IAAAC,EAAA;UACA,IAAAD,IAAA;YACAC,EAAA;UACA,WAAAD,IAAA;YACAC,EAAA;UACA;UACA,OAAAA,EAAA;QACA;MACA,GACA;QACAX,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;QACAG,KAAA;QACAQ,WAAA;UAAAP,YAAA;QAAA;MACA;MACA;MACAQ,IAAA;MACAC,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,WAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,MAAA;MAAA,IAAAC,KAAA;MACA;QACAC,EAAA;UACAC,KAAA,WAAAA,MAAA;YACAF,KAAA,CAAAG,cAAA,CAAAJ,MAAA,CAAAK,EAAA,CAAAC,KAAA,QAAAN,MAAA;UACA;QACA;MACA;IACA;IACAI,cAAA,WAAAA,eAAAG,eAAA,EAAAC,aAAA;MACA,KAAAD,eAAA,GAAAA,eAAA;MACA,KAAAC,aAAA,GAAAA,aAAA;MACA,KAAAC,KAAA,CAAAlD,sBAAA,CAAAmD,YAAA,MAAAH,eAAA;MACA,KAAAE,KAAA,CAAAjD,oBAAA,CAAAkD,YAAA,MAAAH,eAAA;IACA;IACAI,eAAA,WAAAA,gBAAA;MACA,KAAAJ,eAAA;MACA,KAAAC,aAAA;MACA,KAAAC,KAAA,CAAAlD,sBAAA,CAAAqD,UAAA,CAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAjD,oBAAA,CAAAoD,UAAA,CAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAlD,sBAAA,CAAAuD,QAAA;MACA,KAAAL,KAAA,CAAAjD,oBAAA,CAAAsD,QAAA;MACA,KAAAL,KAAA,CAAAlD,sBAAA,CAAAgD,eAAA;MACA,KAAAE,KAAA,CAAAlD,sBAAA,CAAAiD,aAAA;MACA,KAAAC,KAAA,CAAAjD,oBAAA,CAAA+C,eAAA;MACA,KAAAE,KAAA,CAAAjD,oBAAA,CAAAgD,aAAA;IACA;IAEAO,YAAA,WAAAA,aAAAV,EAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,IAAA;MACAxD,YAAA,CAAAwD,IAAA,CAAAvB,GAAA,CAAAE,MAAA;QAAAS,EAAA,EAAAA;MAAA,GAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAH,IAAA,CAAAI,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAG,OAAA;UACAL,IAAA,CAAAH,QAAA;UACAE,MAAA,CAAAP,KAAA,CAAAlD,sBAAA,CAAAuD,QAAA;UACAE,MAAA,CAAAP,KAAA,CAAAjD,oBAAA,CAAAsD,QAAA;QACA;UACAG,IAAA,CAAAI,QAAA,CAAAE,OAAA,CAAAJ,GAAA,CAAAG,OAAA;QACA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MACA,KAAAjB,eAAA;MACA,KAAAC,aAAA;MACA,KAAAC,KAAA,CAAAlD,sBAAA,CAAAqD,UAAA,CAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAjD,oBAAA,CAAAoD,UAAA,CAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAlD,sBAAA,CAAAuD,QAAA;MACA,KAAAL,KAAA,CAAAjD,oBAAA,CAAAsD,QAAA;MACA,KAAAL,KAAA,CAAAlD,sBAAA,CAAAgD,eAAA;MACA,KAAAE,KAAA,CAAAlD,sBAAA,CAAAiD,aAAA;MACA,KAAAC,KAAA,CAAAjD,oBAAA,CAAA+C,eAAA;MACA,KAAAE,KAAA,CAAAjD,oBAAA,CAAAgD,aAAA;MACA,KAAAM,QAAA;IACA;EACA;AACA", "ignoreList": []}]}