{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\HomeLayout.vue?vue&type=template&id=eb68e6e2&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\HomeLayout.vue", "mtime": 1753199484133}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"container\",\n    style: {\n      backgroundColor: _vm.sysConfig.homeBgColor,\n      backgroundImage: _vm.sysConfig.file_homeBg ? \"url(\" + _vm.getFileAccessHttpUrl(_vm.sysConfig.file_homeBg) + \")\" : \"\",\n      backgroundRepeat: _vm.sysConfig.homeBgRepeat ? _vm.sysConfig.homeBgRepeat : \"\"\n    }\n  }, [_c(\"a-layout\", [_c(\"a-layout-header\", [_c(\"Header\")], 1), _c(\"a-layout\", [_c(\"a-layout-content\", [_c(\"a-row\", {\n    attrs: {\n      type: \"flex\",\n      justify: \"space-between\"\n    }\n  }, [_c(\"a-col\", {\n    attrs: {\n      xs: 24,\n      sm: 16,\n      md: 16,\n      lg: 16,\n      xl: 18\n    }\n  }, [_c(\"Banner\")], 1), _c(\"a-col\", {\n    attrs: {\n      xs: 24,\n      sm: 8,\n      md: 8,\n      lg: 8,\n      xl: 6\n    }\n  }, [_c(\"div\", {\n    staticClass: \"user-enter\"\n  }, [_vm.token ? _c(\"div\", [_c(\"div\", {\n    staticClass: \"tech-card\"\n  }, [_c(\"div\", {\n    staticClass: \"card-header\"\n  }, [_c(\"div\", {\n    staticClass: \"avatar-container\"\n  }, [_c(\"a-avatar\", {\n    staticClass: \"avatar\",\n    attrs: {\n      size: 80,\n      src: _vm.avatarUrl\n    }\n  }), _c(\"div\", {\n    staticClass: \"avatar-glow\"\n  })], 1), _c(\"div\", {\n    staticClass: \"user-role\",\n    class: _vm.getUserRole()\n  }, [_vm._v(\"\\n                      \" + _vm._s(_vm.getRoleText()) + \"\\n                    \")])]), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"h2\", {\n    staticClass: \"username\"\n  }, [_vm._v(_vm._s(_vm.nickname()))]), _c(\"div\", {\n    staticClass: \"welcome-text\"\n  }, [_vm._v(\"欢迎回来！\")]), _c(\"div\", {\n    staticClass: \"user-coin\"\n  }, [_c(\"img\", {\n    staticClass: \"coin-icon\",\n    attrs: {\n      src: require(\"@/assets/coin.png\"),\n      alt: \"金币\"\n    }\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.userCoin) + \" 金币\")])]), _c(\"div\", {\n    staticClass: \"daily-tasks\"\n  }, [_c(\"a\", {\n    staticClass: \"daily-task-btn\",\n    on: {\n      click: _vm.showDailyTasks\n    }\n  }, [_c(\"span\", {\n    staticClass: \"daily-task-text\"\n  }, [_vm._v(\"每日任务\")]), _c(\"span\", {\n    staticClass: \"daily-task-star\"\n  })]), _c(\"a\", {\n    staticClass: \"coin-history-btn\",\n    on: {\n      click: _vm.showCoinHistory\n    }\n  }, [_c(\"span\", {\n    staticClass: \"coin-history-text\"\n  }, [_vm._v(\"金币记录\")]), _c(\"a-icon\", {\n    attrs: {\n      type: \"history\"\n    }\n  })], 1)])]), _c(\"div\", {\n    staticClass: \"card-actions\"\n  }, [_c(\"a-button\", {\n    staticClass: \"btn-my-work action-btn\",\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: function click($event) {\n        return _vm.enter(1);\n      }\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"code\"\n    }\n  }), _vm._v(\"我的作品\\n                    \")], 1), _c(\"a-button\", {\n    staticClass: \"btn-my-course action-btn\",\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: function click($event) {\n        return _vm.enter(2);\n      }\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"book\"\n    }\n  }), _vm._v(\"我的课程\\n                    \")], 1)], 1)])]) : _c(\"div\", [_c(\"div\", {\n    staticClass: \"tech-card\"\n  }, [_c(\"div\", {\n    staticClass: \"card-header\"\n  }, [_c(\"div\", {\n    staticClass: \"avatar-container\"\n  }, [_c(\"a-avatar\", {\n    staticClass: \"avatar\",\n    attrs: {\n      shape: \"square\",\n      size: 80,\n      src: _vm.logo2\n    }\n  }), _c(\"div\", {\n    staticClass: \"avatar-glow\"\n  })], 1)]), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"h2\", {\n    staticClass: \"username welcome\"\n  }, [_vm._v(\"欢迎来到\" + _vm._s(_vm.brandName))])]), _c(\"div\", {\n    staticClass: \"card-actions\"\n  }, [_c(\"a-button\", {\n    staticClass: \"action-btn login-btn\",\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: function click($event) {\n        return _vm.enter(0);\n      }\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"login\"\n    }\n  }), _vm._v(\"登录/注册\\n                    \")], 1)], 1)])])])])], 1), _c(\"router-view\")], 1)], 1), _c(\"a-layout-footer\", [_c(\"Footer\")], 1)], 1), _c(\"SlidingPanel\"), _c(\"a-modal\", {\n    staticClass: \"task-modal\",\n    attrs: {\n      title: null,\n      visible: _vm.dailyTasksVisible,\n      footer: null,\n      bodyStyle: {\n        padding: \"0\"\n      },\n      width: \"450px\",\n      maskStyle: {\n        backdropFilter: \"blur(5px)\"\n      },\n      destroyOnClose: true,\n      closable: false\n    },\n    on: {\n      cancel: _vm.closeDailyTasks\n    }\n  }, [_c(\"div\", {\n    staticClass: \"task-modal-content\"\n  }, [_c(\"div\", {\n    staticClass: \"task-header\"\n  }, [_c(\"div\", {\n    staticClass: \"task-header-bg\"\n  }), _c(\"div\", {\n    staticClass: \"task-title\"\n  }, [_c(\"img\", {\n    staticClass: \"task-coin-icon\",\n    attrs: {\n      src: require(\"@/assets/coin.png\"),\n      alt: \"金币\"\n    }\n  }), _c(\"span\", [_vm._v(\"每日任务\")]), _c(\"img\", {\n    staticClass: \"task-coin-icon\",\n    attrs: {\n      src: require(\"@/assets/coin.png\"),\n      alt: \"金币\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"task-subtitle\"\n  }, [_vm._v(\"完成任务收集金币，解锁更多惊喜！\")]), _c(\"div\", {\n    staticClass: \"close-btn\",\n    on: {\n      click: _vm.closeDailyTasks\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"close-circle\",\n      theme: \"filled\"\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"task-progress\"\n  }, [_c(\"div\", {\n    staticClass: \"progress-text\"\n  }, [_vm._v(\"今日进度：已完成 \" + _vm._s(_vm.getCompletedTaskCount()) + \"/\" + _vm._s(_vm.dailyTasks.length))]), _c(\"a-progress\", {\n    attrs: {\n      percent: _vm.getCompletedPercent(),\n      strokeColor: {\n        \"0%\": \"#FFB800\",\n        \"100%\": \"#FF6161\"\n      },\n      showInfo: false,\n      strokeWidth: \"{12}\"\n    }\n  })], 1)]), _c(\"a-spin\", {\n    attrs: {\n      spinning: _vm.tasksLoading\n    }\n  }, [_c(\"div\", {\n    staticClass: \"daily-tasks-content\"\n  }, _vm._l(_vm.dailyTasks, function (task, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"task-item\",\n      class: {\n        completed: task.completed\n      }\n    }, [_c(\"div\", {\n      staticClass: \"task-icon\"\n    }, [_c(\"a-icon\", {\n      attrs: {\n        type: _vm.getTaskIcon(task.taskId),\n        theme: \"filled\"\n      }\n    })], 1), _c(\"div\", {\n      staticClass: \"task-info\"\n    }, [_c(\"div\", {\n      staticClass: \"task-name\"\n    }, [_vm._v(_vm._s(task.taskName))]), _c(\"div\", {\n      staticClass: \"task-desc\"\n    }, [_vm._v(_vm._s(task.taskDesc))])]), _c(\"div\", {\n      staticClass: \"task-action\"\n    }, [_c(\"div\", {\n      staticClass: \"coin-reward\",\n      class: {\n        completed: task.completed\n      }\n    }, [_c(\"img\", {\n      staticClass: \"coin-icon\",\n      attrs: {\n        src: require(\"@/assets/coin.png\"),\n        alt: \"金币\"\n      }\n    }), _c(\"span\", [_vm._v(_vm._s(task.completed ? \"已领取\" : \"+\" + task.coinReward))])]), !task.completed && task.taskId === 1 ? [_c(\"a-button\", {\n      staticClass: \"sign-btn\",\n      attrs: {\n        type: \"primary\",\n        loading: _vm.signInLoading\n      },\n      on: {\n        click: _vm.doSignIn\n      }\n    }, [_vm._v(\"\\n                  去签到\\n                \")])] : !task.completed ? [_c(\"a-button\", {\n      staticClass: \"do-task-btn\",\n      attrs: {\n        type: \"primary\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.goToDoTask(task.taskId);\n        }\n      }\n    }, [_vm._v(\"\\n                  去完成\\n                \")])] : [_c(\"div\", {\n      staticClass: \"completed-badge\"\n    }, [_c(\"a-icon\", {\n      attrs: {\n        type: \"check-circle\",\n        theme: \"filled\"\n      }\n    }), _c(\"span\", [_vm._v(\"已完成\")])], 1)]], 2)]);\n  }), 0)]), _c(\"div\", {\n    staticClass: \"task-footer\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-stars\"\n  }, [_c(\"span\", {\n    staticClass: \"star star-1\"\n  }), _c(\"span\", {\n    staticClass: \"star star-2\"\n  }), _c(\"span\", {\n    staticClass: \"star star-3\"\n  })])])], 1)]), _c(\"a-modal\", {\n    staticClass: \"coin-history-modal\",\n    attrs: {\n      title: null,\n      visible: _vm.coinHistoryVisible,\n      footer: null,\n      bodyStyle: {\n        padding: \"0\"\n      },\n      width: \"550px\",\n      maskStyle: {\n        backdropFilter: \"blur(5px)\"\n      },\n      destroyOnClose: true,\n      closable: false\n    },\n    on: {\n      cancel: _vm.closeCoinHistory\n    }\n  }, [_c(\"div\", {\n    staticClass: \"coin-history-content\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-history-header\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-history-header-bg\"\n  }), _c(\"div\", {\n    staticClass: \"coin-history-title\"\n  }, [_c(\"img\", {\n    staticClass: \"coin-icon\",\n    attrs: {\n      src: require(\"@/assets/coin.png\"),\n      alt: \"金币\"\n    }\n  }), _c(\"span\", [_vm._v(\"金币记录\")]), _c(\"img\", {\n    staticClass: \"coin-icon\",\n    attrs: {\n      src: require(\"@/assets/coin.png\"),\n      alt: \"金币\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"coin-history-subtitle\"\n  }, [_vm._v(\"查看您最近一个月的金币收支记录\")]), _c(\"div\", {\n    staticClass: \"close-btn\",\n    on: {\n      click: _vm.closeCoinHistory\n    }\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"close-circle\",\n      theme: \"filled\"\n    }\n  })], 1)]), _c(\"a-spin\", {\n    attrs: {\n      spinning: _vm.historyLoading\n    }\n  }, [_c(\"div\", {\n    staticClass: \"coin-records-content\"\n  }, [_vm.coinRecords.length === 0 ? _c(\"a-empty\", {\n    attrs: {\n      description: \"暂无金币记录\"\n    }\n  }) : _c(\"a-list\", {\n    staticClass: \"coin-list\",\n    attrs: {\n      dataSource: _vm.coinRecords,\n      size: \"small\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"renderItem\",\n      fn: function fn(item) {\n        return _c(\"a-list-item\", {\n          staticClass: \"coin-record-item\"\n        }, [_c(\"div\", {\n          staticClass: \"record-content\"\n        }, [_c(\"div\", {\n          staticClass: \"record-icon\",\n          class: {\n            income: item.operationType === 1,\n            expense: item.operationType === 2\n          }\n        }, [_c(\"a-icon\", {\n          attrs: {\n            type: item.operationType === 1 ? \"plus-circle\" : \"minus-circle\",\n            theme: \"filled\"\n          }\n        })], 1), _c(\"div\", {\n          staticClass: \"record-info\"\n        }, [_c(\"div\", {\n          staticClass: \"record-description\"\n        }, [_vm._v(_vm._s(item.description))]), _c(\"div\", {\n          staticClass: \"record-time\"\n        }, [_vm._v(_vm._s(_vm.formatDate(item.createTime)))])]), _c(\"div\", {\n          staticClass: \"record-amount\",\n          class: {\n            income: item.operationType === 1,\n            expense: item.operationType === 2\n          }\n        }, [_vm._v(\"\\n                  \" + _vm._s(item.operationType === 1 ? \"+\" : \"-\") + _vm._s(item.coinCount) + \"\\n                \")])])]);\n      }\n    }])\n  }), _vm.coinRecords.length > 0 ? _c(\"div\", {\n    staticClass: \"load-more\"\n  }, [!_vm.allLoaded ? _c(\"a-button\", {\n    attrs: {\n      type: \"link\",\n      disabled: _vm.historyLoading\n    },\n    on: {\n      click: _vm.loadMoreHistory\n    }\n  }, [_vm._v(\"\\n              \" + _vm._s(_vm.historyLoading ? \"加载中...\" : \"查看更多\") + \"\\n            \")]) : _c(\"span\", {\n    staticClass: \"no-more\"\n  }, [_vm._v(\"没有更多记录了\")])], 1) : _vm._e()], 1)]), _c(\"div\", {\n    staticClass: \"coin-history-footer\"\n  }, [_c(\"div\", {\n    staticClass: \"coin-summary\"\n  }, [_c(\"div\", {\n    staticClass: \"summary-item income\"\n  }, [_c(\"span\", {\n    staticClass: \"label\"\n  }, [_vm._v(\"本月收入：\")]), _c(\"span\", {\n    staticClass: \"value\"\n  }, [_vm._v(\"+\" + _vm._s(_vm.incomeTotal))])]), _c(\"div\", {\n    staticClass: \"summary-item expense\"\n  }, [_c(\"span\", {\n    staticClass: \"label\"\n  }, [_vm._v(\"本月支出：\")]), _c(\"span\", {\n    staticClass: \"value\"\n  }, [_vm._v(\"-\" + _vm._s(_vm.expenseTotal))])])])])], 1)])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "backgroundColor", "sysConfig", "homeBgColor", "backgroundImage", "file_homeBg", "getFileAccessHttpUrl", "backgroundRepeat", "homeBgRepeat", "attrs", "type", "justify", "xs", "sm", "md", "lg", "xl", "token", "size", "src", "avatarUrl", "class", "getUserRole", "_v", "_s", "getRoleText", "nickname", "require", "alt", "userCoin", "on", "click", "showDailyTasks", "showCoinHistory", "$event", "enter", "shape", "logo2", "brandName", "title", "visible", "dailyTasksVisible", "footer", "bodyStyle", "padding", "width", "maskStyle", "<PERSON><PERSON>ilter", "destroyOnClose", "closable", "cancel", "closeDailyTasks", "theme", "getCompletedTaskCount", "dailyTasks", "length", "percent", "getCompletedPercent", "strokeColor", "showInfo", "strokeWidth", "spinning", "tasksLoading", "_l", "task", "index", "key", "completed", "getTaskIcon", "taskId", "taskName", "taskDesc", "coinReward", "loading", "signInLoading", "doSignIn", "goToDoTask", "coinHistoryVisible", "closeCoinHistory", "historyLoading", "coinRecords", "description", "dataSource", "scopedSlots", "_u", "fn", "item", "income", "operationType", "expense", "formatDate", "createTime", "coinCount", "allLoaded", "disabled", "loadMoreHistory", "_e", "incomeTotal", "expenseTotal", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"container\",\n      style: {\n        backgroundColor: _vm.sysConfig.homeBgColor,\n        backgroundImage: _vm.sysConfig.file_homeBg\n          ? \"url(\" + _vm.getFileAccessHttpUrl(_vm.sysConfig.file_homeBg) + \")\"\n          : \"\",\n        backgroundRepeat: _vm.sysConfig.homeBgRepeat\n          ? _vm.sysConfig.homeBgRepeat\n          : \"\",\n      },\n    },\n    [\n      _c(\n        \"a-layout\",\n        [\n          _c(\"a-layout-header\", [_c(\"Header\")], 1),\n          _c(\n            \"a-layout\",\n            [\n              _c(\n                \"a-layout-content\",\n                [\n                  _c(\n                    \"a-row\",\n                    { attrs: { type: \"flex\", justify: \"space-between\" } },\n                    [\n                      _c(\n                        \"a-col\",\n                        { attrs: { xs: 24, sm: 16, md: 16, lg: 16, xl: 18 } },\n                        [_c(\"Banner\")],\n                        1\n                      ),\n                      _c(\n                        \"a-col\",\n                        { attrs: { xs: 24, sm: 8, md: 8, lg: 8, xl: 6 } },\n                        [\n                          _c(\"div\", { staticClass: \"user-enter\" }, [\n                            _vm.token\n                              ? _c(\"div\", [\n                                  _c(\"div\", { staticClass: \"tech-card\" }, [\n                                    _c(\"div\", { staticClass: \"card-header\" }, [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"avatar-container\" },\n                                        [\n                                          _c(\"a-avatar\", {\n                                            staticClass: \"avatar\",\n                                            attrs: {\n                                              size: 80,\n                                              src: _vm.avatarUrl,\n                                            },\n                                          }),\n                                          _c(\"div\", {\n                                            staticClass: \"avatar-glow\",\n                                          }),\n                                        ],\n                                        1\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"user-role\",\n                                          class: _vm.getUserRole(),\n                                        },\n                                        [\n                                          _vm._v(\n                                            \"\\n                      \" +\n                                              _vm._s(_vm.getRoleText()) +\n                                              \"\\n                    \"\n                                          ),\n                                        ]\n                                      ),\n                                    ]),\n                                    _c(\"div\", { staticClass: \"user-info\" }, [\n                                      _c(\"h2\", { staticClass: \"username\" }, [\n                                        _vm._v(_vm._s(_vm.nickname())),\n                                      ]),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"welcome-text\" },\n                                        [_vm._v(\"欢迎回来！\")]\n                                      ),\n                                      _c(\"div\", { staticClass: \"user-coin\" }, [\n                                        _c(\"img\", {\n                                          staticClass: \"coin-icon\",\n                                          attrs: {\n                                            src: require(\"@/assets/coin.png\"),\n                                            alt: \"金币\",\n                                          },\n                                        }),\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            _vm._s(_vm.userCoin) + \" 金币\"\n                                          ),\n                                        ]),\n                                      ]),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"daily-tasks\" },\n                                        [\n                                          _c(\n                                            \"a\",\n                                            {\n                                              staticClass: \"daily-task-btn\",\n                                              on: { click: _vm.showDailyTasks },\n                                            },\n                                            [\n                                              _c(\n                                                \"span\",\n                                                {\n                                                  staticClass:\n                                                    \"daily-task-text\",\n                                                },\n                                                [_vm._v(\"每日任务\")]\n                                              ),\n                                              _c(\"span\", {\n                                                staticClass: \"daily-task-star\",\n                                              }),\n                                            ]\n                                          ),\n                                          _c(\n                                            \"a\",\n                                            {\n                                              staticClass: \"coin-history-btn\",\n                                              on: {\n                                                click: _vm.showCoinHistory,\n                                              },\n                                            },\n                                            [\n                                              _c(\n                                                \"span\",\n                                                {\n                                                  staticClass:\n                                                    \"coin-history-text\",\n                                                },\n                                                [_vm._v(\"金币记录\")]\n                                              ),\n                                              _c(\"a-icon\", {\n                                                attrs: { type: \"history\" },\n                                              }),\n                                            ],\n                                            1\n                                          ),\n                                        ]\n                                      ),\n                                    ]),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"card-actions\" },\n                                      [\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            staticClass:\n                                              \"btn-my-work action-btn\",\n                                            attrs: { type: \"primary\" },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.enter(1)\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"code\" },\n                                            }),\n                                            _vm._v(\n                                              \"我的作品\\n                    \"\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            staticClass:\n                                              \"btn-my-course action-btn\",\n                                            attrs: { type: \"primary\" },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.enter(2)\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"book\" },\n                                            }),\n                                            _vm._v(\n                                              \"我的课程\\n                    \"\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ]),\n                                ])\n                              : _c(\"div\", [\n                                  _c(\"div\", { staticClass: \"tech-card\" }, [\n                                    _c(\"div\", { staticClass: \"card-header\" }, [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"avatar-container\" },\n                                        [\n                                          _c(\"a-avatar\", {\n                                            staticClass: \"avatar\",\n                                            attrs: {\n                                              shape: \"square\",\n                                              size: 80,\n                                              src: _vm.logo2,\n                                            },\n                                          }),\n                                          _c(\"div\", {\n                                            staticClass: \"avatar-glow\",\n                                          }),\n                                        ],\n                                        1\n                                      ),\n                                    ]),\n                                    _c(\"div\", { staticClass: \"user-info\" }, [\n                                      _c(\n                                        \"h2\",\n                                        { staticClass: \"username welcome\" },\n                                        [\n                                          _vm._v(\n                                            \"欢迎来到\" + _vm._s(_vm.brandName)\n                                          ),\n                                        ]\n                                      ),\n                                    ]),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"card-actions\" },\n                                      [\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            staticClass: \"action-btn login-btn\",\n                                            attrs: { type: \"primary\" },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.enter(0)\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"login\" },\n                                            }),\n                                            _vm._v(\n                                              \"登录/注册\\n                    \"\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ]),\n                                ]),\n                          ]),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"router-view\"),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\"a-layout-footer\", [_c(\"Footer\")], 1),\n        ],\n        1\n      ),\n      _c(\"SlidingPanel\"),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"task-modal\",\n          attrs: {\n            title: null,\n            visible: _vm.dailyTasksVisible,\n            footer: null,\n            bodyStyle: { padding: \"0\" },\n            width: \"450px\",\n            maskStyle: { backdropFilter: \"blur(5px)\" },\n            destroyOnClose: true,\n            closable: false,\n          },\n          on: { cancel: _vm.closeDailyTasks },\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"task-modal-content\" },\n            [\n              _c(\"div\", { staticClass: \"task-header\" }, [\n                _c(\"div\", { staticClass: \"task-header-bg\" }),\n                _c(\"div\", { staticClass: \"task-title\" }, [\n                  _c(\"img\", {\n                    staticClass: \"task-coin-icon\",\n                    attrs: { src: require(\"@/assets/coin.png\"), alt: \"金币\" },\n                  }),\n                  _c(\"span\", [_vm._v(\"每日任务\")]),\n                  _c(\"img\", {\n                    staticClass: \"task-coin-icon\",\n                    attrs: { src: require(\"@/assets/coin.png\"), alt: \"金币\" },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"task-subtitle\" }, [\n                  _vm._v(\"完成任务收集金币，解锁更多惊喜！\"),\n                ]),\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"close-btn\",\n                    on: { click: _vm.closeDailyTasks },\n                  },\n                  [\n                    _c(\"a-icon\", {\n                      attrs: { type: \"close-circle\", theme: \"filled\" },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"task-progress\" },\n                  [\n                    _c(\"div\", { staticClass: \"progress-text\" }, [\n                      _vm._v(\n                        \"今日进度：已完成 \" +\n                          _vm._s(_vm.getCompletedTaskCount()) +\n                          \"/\" +\n                          _vm._s(_vm.dailyTasks.length)\n                      ),\n                    ]),\n                    _c(\"a-progress\", {\n                      attrs: {\n                        percent: _vm.getCompletedPercent(),\n                        strokeColor: {\n                          \"0%\": \"#FFB800\",\n                          \"100%\": \"#FF6161\",\n                        },\n                        showInfo: false,\n                        strokeWidth: \"{12}\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\"a-spin\", { attrs: { spinning: _vm.tasksLoading } }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"daily-tasks-content\" },\n                  _vm._l(_vm.dailyTasks, function (task, index) {\n                    return _c(\n                      \"div\",\n                      {\n                        key: index,\n                        staticClass: \"task-item\",\n                        class: { completed: task.completed },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"task-icon\" },\n                          [\n                            _c(\"a-icon\", {\n                              attrs: {\n                                type: _vm.getTaskIcon(task.taskId),\n                                theme: \"filled\",\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                        _c(\"div\", { staticClass: \"task-info\" }, [\n                          _c(\"div\", { staticClass: \"task-name\" }, [\n                            _vm._v(_vm._s(task.taskName)),\n                          ]),\n                          _c(\"div\", { staticClass: \"task-desc\" }, [\n                            _vm._v(_vm._s(task.taskDesc)),\n                          ]),\n                        ]),\n                        _c(\n                          \"div\",\n                          { staticClass: \"task-action\" },\n                          [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"coin-reward\",\n                                class: { completed: task.completed },\n                              },\n                              [\n                                _c(\"img\", {\n                                  staticClass: \"coin-icon\",\n                                  attrs: {\n                                    src: require(\"@/assets/coin.png\"),\n                                    alt: \"金币\",\n                                  },\n                                }),\n                                _c(\"span\", [\n                                  _vm._v(\n                                    _vm._s(\n                                      task.completed\n                                        ? \"已领取\"\n                                        : \"+\" + task.coinReward\n                                    )\n                                  ),\n                                ]),\n                              ]\n                            ),\n                            !task.completed && task.taskId === 1\n                              ? [\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"sign-btn\",\n                                      attrs: {\n                                        type: \"primary\",\n                                        loading: _vm.signInLoading,\n                                      },\n                                      on: { click: _vm.doSignIn },\n                                    },\n                                    [\n                                      _vm._v(\n                                        \"\\n                  去签到\\n                \"\n                                      ),\n                                    ]\n                                  ),\n                                ]\n                              : !task.completed\n                              ? [\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      staticClass: \"do-task-btn\",\n                                      attrs: { type: \"primary\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.goToDoTask(task.taskId)\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _vm._v(\n                                        \"\\n                  去完成\\n                \"\n                                      ),\n                                    ]\n                                  ),\n                                ]\n                              : [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"completed-badge\" },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: {\n                                          type: \"check-circle\",\n                                          theme: \"filled\",\n                                        },\n                                      }),\n                                      _c(\"span\", [_vm._v(\"已完成\")]),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                          ],\n                          2\n                        ),\n                      ]\n                    )\n                  }),\n                  0\n                ),\n              ]),\n              _c(\"div\", { staticClass: \"task-footer\" }, [\n                _c(\"div\", { staticClass: \"footer-stars\" }, [\n                  _c(\"span\", { staticClass: \"star star-1\" }),\n                  _c(\"span\", { staticClass: \"star star-2\" }),\n                  _c(\"span\", { staticClass: \"star star-3\" }),\n                ]),\n              ]),\n            ],\n            1\n          ),\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"coin-history-modal\",\n          attrs: {\n            title: null,\n            visible: _vm.coinHistoryVisible,\n            footer: null,\n            bodyStyle: { padding: \"0\" },\n            width: \"550px\",\n            maskStyle: { backdropFilter: \"blur(5px)\" },\n            destroyOnClose: true,\n            closable: false,\n          },\n          on: { cancel: _vm.closeCoinHistory },\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"coin-history-content\" },\n            [\n              _c(\"div\", { staticClass: \"coin-history-header\" }, [\n                _c(\"div\", { staticClass: \"coin-history-header-bg\" }),\n                _c(\"div\", { staticClass: \"coin-history-title\" }, [\n                  _c(\"img\", {\n                    staticClass: \"coin-icon\",\n                    attrs: { src: require(\"@/assets/coin.png\"), alt: \"金币\" },\n                  }),\n                  _c(\"span\", [_vm._v(\"金币记录\")]),\n                  _c(\"img\", {\n                    staticClass: \"coin-icon\",\n                    attrs: { src: require(\"@/assets/coin.png\"), alt: \"金币\" },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"coin-history-subtitle\" }, [\n                  _vm._v(\"查看您最近一个月的金币收支记录\"),\n                ]),\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"close-btn\",\n                    on: { click: _vm.closeCoinHistory },\n                  },\n                  [\n                    _c(\"a-icon\", {\n                      attrs: { type: \"close-circle\", theme: \"filled\" },\n                    }),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\"a-spin\", { attrs: { spinning: _vm.historyLoading } }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"coin-records-content\" },\n                  [\n                    _vm.coinRecords.length === 0\n                      ? _c(\"a-empty\", {\n                          attrs: { description: \"暂无金币记录\" },\n                        })\n                      : _c(\"a-list\", {\n                          staticClass: \"coin-list\",\n                          attrs: { dataSource: _vm.coinRecords, size: \"small\" },\n                          scopedSlots: _vm._u([\n                            {\n                              key: \"renderItem\",\n                              fn: function (item) {\n                                return _c(\n                                  \"a-list-item\",\n                                  { staticClass: \"coin-record-item\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"record-content\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"record-icon\",\n                                            class: {\n                                              income: item.operationType === 1,\n                                              expense: item.operationType === 2,\n                                            },\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: {\n                                                type:\n                                                  item.operationType === 1\n                                                    ? \"plus-circle\"\n                                                    : \"minus-circle\",\n                                                theme: \"filled\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"record-info\" },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"record-description\",\n                                              },\n                                              [_vm._v(_vm._s(item.description))]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"record-time\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    _vm.formatDate(\n                                                      item.createTime\n                                                    )\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"record-amount\",\n                                            class: {\n                                              income: item.operationType === 1,\n                                              expense: item.operationType === 2,\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \"\\n                  \" +\n                                                _vm._s(\n                                                  item.operationType === 1\n                                                    ? \"+\"\n                                                    : \"-\"\n                                                ) +\n                                                _vm._s(item.coinCount) +\n                                                \"\\n                \"\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                )\n                              },\n                            },\n                          ]),\n                        }),\n                    _vm.coinRecords.length > 0\n                      ? _c(\n                          \"div\",\n                          { staticClass: \"load-more\" },\n                          [\n                            !_vm.allLoaded\n                              ? _c(\n                                  \"a-button\",\n                                  {\n                                    attrs: {\n                                      type: \"link\",\n                                      disabled: _vm.historyLoading,\n                                    },\n                                    on: { click: _vm.loadMoreHistory },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n              \" +\n                                        _vm._s(\n                                          _vm.historyLoading\n                                            ? \"加载中...\"\n                                            : \"查看更多\"\n                                        ) +\n                                        \"\\n            \"\n                                    ),\n                                  ]\n                                )\n                              : _c(\"span\", { staticClass: \"no-more\" }, [\n                                  _vm._v(\"没有更多记录了\"),\n                                ]),\n                          ],\n                          1\n                        )\n                      : _vm._e(),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\"div\", { staticClass: \"coin-history-footer\" }, [\n                _c(\"div\", { staticClass: \"coin-summary\" }, [\n                  _c(\"div\", { staticClass: \"summary-item income\" }, [\n                    _c(\"span\", { staticClass: \"label\" }, [\n                      _vm._v(\"本月收入：\"),\n                    ]),\n                    _c(\"span\", { staticClass: \"value\" }, [\n                      _vm._v(\"+\" + _vm._s(_vm.incomeTotal)),\n                    ]),\n                  ]),\n                  _c(\"div\", { staticClass: \"summary-item expense\" }, [\n                    _c(\"span\", { staticClass: \"label\" }, [\n                      _vm._v(\"本月支出：\"),\n                    ]),\n                    _c(\"span\", { staticClass: \"value\" }, [\n                      _vm._v(\"-\" + _vm._s(_vm.expenseTotal)),\n                    ]),\n                  ]),\n                ]),\n              ]),\n            ],\n            1\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE;MACLC,eAAe,EAAEL,GAAG,CAACM,SAAS,CAACC,WAAW;MAC1CC,eAAe,EAAER,GAAG,CAACM,SAAS,CAACG,WAAW,GACtC,MAAM,GAAGT,GAAG,CAACU,oBAAoB,CAACV,GAAG,CAACM,SAAS,CAACG,WAAW,CAAC,GAAG,GAAG,GAClE,EAAE;MACNE,gBAAgB,EAAEX,GAAG,CAACM,SAAS,CAACM,YAAY,GACxCZ,GAAG,CAACM,SAAS,CAACM,YAAY,GAC1B;IACN;EACF,CAAC,EACD,CACEX,EAAE,CACA,UAAU,EACV,CACEA,EAAE,CAAC,iBAAiB,EAAE,CAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACxCA,EAAE,CACA,UAAU,EACV,CACEA,EAAE,CACA,kBAAkB,EAClB,CACEA,EAAE,CACA,OAAO,EACP;IAAEY,KAAK,EAAE;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAgB;EAAE,CAAC,EACrD,CACEd,EAAE,CACA,OAAO,EACP;IAAEY,KAAK,EAAE;MAAEG,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG;EAAE,CAAC,EACrD,CAACnB,EAAE,CAAC,QAAQ,CAAC,CAAC,EACd,CACF,CAAC,EACDA,EAAE,CACA,OAAO,EACP;IAAEY,KAAK,EAAE;MAAEG,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACjD,CACEnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACqB,KAAK,GACLpB,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,QAAQ;IACrBU,KAAK,EAAE;MACLS,IAAI,EAAE,EAAE;MACRC,GAAG,EAAEvB,GAAG,CAACwB;IACX;EACF,CAAC,CAAC,EACFvB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE;EACf,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBsB,KAAK,EAAEzB,GAAG,CAAC0B,WAAW,CAAC;EACzB,CAAC,EACD,CACE1B,GAAG,CAAC2B,EAAE,CACJ,0BAA0B,GACxB3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAAC6B,WAAW,CAAC,CAAC,CAAC,GACzB,wBACJ,CAAC,CAEL,CAAC,CACF,CAAC,EACF5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACpCH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAAC8B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/B,CAAC,EACF7B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CAACH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MACLU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;MACjCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACF/B,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC2B,EAAE,CACJ3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAACiC,QAAQ,CAAC,GAAG,KACzB,CAAC,CACF,CAAC,CACH,CAAC,EACFhC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,gBAAgB;IAC7B+B,EAAE,EAAE;MAAEC,KAAK,EAAEnC,GAAG,CAACoC;IAAe;EAClC,CAAC,EACD,CACEnC,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACD1B,EAAE,CAAC,MAAM,EAAE;IACTE,WAAW,EAAE;EACf,CAAC,CAAC,CAEN,CAAC,EACDF,EAAE,CACA,GAAG,EACH;IACEE,WAAW,EAAE,kBAAkB;IAC/B+B,EAAE,EAAE;MACFC,KAAK,EAAEnC,GAAG,CAACqC;IACb;EACF,CAAC,EACD,CACEpC,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACD1B,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU;EAC3B,CAAC,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,CAAC,EACFb,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EACT,wBAAwB;IAC1BU,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC1BoB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,MAAUG,MAAM,EAAE;QACvB,OAAOtC,GAAG,CAACuC,KAAK,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EACD,CACEtC,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO;EACxB,CAAC,CAAC,EACFd,GAAG,CAAC2B,EAAE,CACJ,4BACF,CAAC,CACF,EACD,CACF,CAAC,EACD1B,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EACT,0BAA0B;IAC5BU,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC1BoB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,MAAUG,MAAM,EAAE;QACvB,OAAOtC,GAAG,CAACuC,KAAK,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EACD,CACEtC,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAO;EACxB,CAAC,CAAC,EACFd,GAAG,CAAC2B,EAAE,CACJ,4BACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,GACF1B,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,QAAQ;IACrBU,KAAK,EAAE;MACL2B,KAAK,EAAE,QAAQ;MACflB,IAAI,EAAE,EAAE;MACRC,GAAG,EAAEvB,GAAG,CAACyC;IACX;EACF,CAAC,CAAC,EACFxC,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE;EACf,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEH,GAAG,CAAC2B,EAAE,CACJ,MAAM,GAAG3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAAC0C,SAAS,CAC/B,CAAC,CAEL,CAAC,CACF,CAAC,EACFzC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,sBAAsB;IACnCU,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC1BoB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,MAAUG,MAAM,EAAE;QACvB,OAAOtC,GAAG,CAACuC,KAAK,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EACD,CACEtC,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAQ;EACzB,CAAC,CAAC,EACFd,GAAG,CAAC2B,EAAE,CACJ,6BACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACP,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,EACD1B,EAAE,CAAC,aAAa,CAAC,CAClB,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDA,EAAE,CAAC,iBAAiB,EAAE,CAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CACzC,EACD,CACF,CAAC,EACDA,EAAE,CAAC,cAAc,CAAC,EAClBA,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,YAAY;IACzBU,KAAK,EAAE;MACL8B,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE5C,GAAG,CAAC6C,iBAAiB;MAC9BC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAI,CAAC;MAC3BC,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE;QAAEC,cAAc,EAAE;MAAY,CAAC;MAC1CC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACDnB,EAAE,EAAE;MAAEoB,MAAM,EAAEtD,GAAG,CAACuD;IAAgB;EACpC,CAAC,EACD,CACEtD,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BU,KAAK,EAAE;MAAEU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;MAAEC,GAAG,EAAE;IAAK;EACxD,CAAC,CAAC,EACF/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5B1B,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BU,KAAK,EAAE;MAAEU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;MAAEC,GAAG,EAAE;IAAK;EACxD,CAAC,CAAC,CACH,CAAC,EACF/B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAAC2B,EAAE,CAAC,kBAAkB,CAAC,CAC3B,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxB+B,EAAE,EAAE;MAAEC,KAAK,EAAEnC,GAAG,CAACuD;IAAgB;EACnC,CAAC,EACD,CACEtD,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE,cAAc;MAAE0C,KAAK,EAAE;IAAS;EACjD,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDvD,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CH,GAAG,CAAC2B,EAAE,CACJ,WAAW,GACT3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAACyD,qBAAqB,CAAC,CAAC,CAAC,GACnC,GAAG,GACHzD,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAAC0D,UAAU,CAACC,MAAM,CAChC,CAAC,CACF,CAAC,EACF1D,EAAE,CAAC,YAAY,EAAE;IACfY,KAAK,EAAE;MACL+C,OAAO,EAAE5D,GAAG,CAAC6D,mBAAmB,CAAC,CAAC;MAClCC,WAAW,EAAE;QACX,IAAI,EAAE,SAAS;QACf,MAAM,EAAE;MACV,CAAC;MACDC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACF/D,EAAE,CAAC,QAAQ,EAAE;IAAEY,KAAK,EAAE;MAAEoD,QAAQ,EAAEjE,GAAG,CAACkE;IAAa;EAAE,CAAC,EAAE,CACtDjE,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtCH,GAAG,CAACmE,EAAE,CAACnE,GAAG,CAAC0D,UAAU,EAAE,UAAUU,IAAI,EAAEC,KAAK,EAAE;IAC5C,OAAOpE,EAAE,CACP,KAAK,EACL;MACEqE,GAAG,EAAED,KAAK;MACVlE,WAAW,EAAE,WAAW;MACxBsB,KAAK,EAAE;QAAE8C,SAAS,EAAEH,IAAI,CAACG;MAAU;IACrC,CAAC,EACD,CACEtE,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,QAAQ,EAAE;MACXY,KAAK,EAAE;QACLC,IAAI,EAAEd,GAAG,CAACwE,WAAW,CAACJ,IAAI,CAACK,MAAM,CAAC;QAClCjB,KAAK,EAAE;MACT;IACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDvD,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,EAAE,CAACwC,IAAI,CAACM,QAAQ,CAAC,CAAC,CAC9B,CAAC,EACFzE,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,EAAE,CAACwC,IAAI,CAACO,QAAQ,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,EACF1E,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAc,CAAC,EAC9B,CACEF,EAAE,CACA,KAAK,EACL;MACEE,WAAW,EAAE,aAAa;MAC1BsB,KAAK,EAAE;QAAE8C,SAAS,EAAEH,IAAI,CAACG;MAAU;IACrC,CAAC,EACD,CACEtE,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,WAAW;MACxBU,KAAK,EAAE;QACLU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;QACjCC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACF/B,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC2B,EAAE,CACJ3B,GAAG,CAAC4B,EAAE,CACJwC,IAAI,CAACG,SAAS,GACV,KAAK,GACL,GAAG,GAAGH,IAAI,CAACQ,UACjB,CACF,CAAC,CACF,CAAC,CAEN,CAAC,EACD,CAACR,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACK,MAAM,KAAK,CAAC,GAChC,CACExE,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,UAAU;MACvBU,KAAK,EAAE;QACLC,IAAI,EAAE,SAAS;QACf+D,OAAO,EAAE7E,GAAG,CAAC8E;MACf,CAAC;MACD5C,EAAE,EAAE;QAAEC,KAAK,EAAEnC,GAAG,CAAC+E;MAAS;IAC5B,CAAC,EACD,CACE/E,GAAG,CAAC2B,EAAE,CACJ,2CACF,CAAC,CAEL,CAAC,CACF,GACD,CAACyC,IAAI,CAACG,SAAS,GACf,CACEtE,EAAE,CACA,UAAU,EACV;MACEE,WAAW,EAAE,aAAa;MAC1BU,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC;MAC1BoB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUG,MAAM,EAAE;UACvB,OAAOtC,GAAG,CAACgF,UAAU,CAACZ,IAAI,CAACK,MAAM,CAAC;QACpC;MACF;IACF,CAAC,EACD,CACEzE,GAAG,CAAC2B,EAAE,CACJ,2CACF,CAAC,CAEL,CAAC,CACF,GACD,CACE1B,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,QAAQ,EAAE;MACXY,KAAK,EAAE;QACLC,IAAI,EAAE,cAAc;QACpB0C,KAAK,EAAE;MACT;IACF,CAAC,CAAC,EACFvD,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5B,EACD,CACF,CAAC,CACF,CACN,EACD,CACF,CAAC,CAEL,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACF1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,EAC1CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,EAC1CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,EACDF,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,oBAAoB;IACjCU,KAAK,EAAE;MACL8B,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE5C,GAAG,CAACiF,kBAAkB;MAC/BnC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAI,CAAC;MAC3BC,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE;QAAEC,cAAc,EAAE;MAAY,CAAC;MAC1CC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACDnB,EAAE,EAAE;MAAEoB,MAAM,EAAEtD,GAAG,CAACkF;IAAiB;EACrC,CAAC,EACD,CACEjF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,CAAC,EACpDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MAAEU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;MAAEC,GAAG,EAAE;IAAK;EACxD,CAAC,CAAC,EACF/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5B1B,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MAAEU,GAAG,EAAEQ,OAAO,CAAC,mBAAmB,CAAC;MAAEC,GAAG,EAAE;IAAK;EACxD,CAAC,CAAC,CACH,CAAC,EACF/B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CAClDH,GAAG,CAAC2B,EAAE,CAAC,iBAAiB,CAAC,CAC1B,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxB+B,EAAE,EAAE;MAAEC,KAAK,EAAEnC,GAAG,CAACkF;IAAiB;EACpC,CAAC,EACD,CACEjF,EAAE,CAAC,QAAQ,EAAE;IACXY,KAAK,EAAE;MAAEC,IAAI,EAAE,cAAc;MAAE0C,KAAK,EAAE;IAAS;EACjD,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFvD,EAAE,CAAC,QAAQ,EAAE;IAAEY,KAAK,EAAE;MAAEoD,QAAQ,EAAEjE,GAAG,CAACmF;IAAe;EAAE,CAAC,EAAE,CACxDlF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEH,GAAG,CAACoF,WAAW,CAACzB,MAAM,KAAK,CAAC,GACxB1D,EAAE,CAAC,SAAS,EAAE;IACZY,KAAK,EAAE;MAAEwE,WAAW,EAAE;IAAS;EACjC,CAAC,CAAC,GACFpF,EAAE,CAAC,QAAQ,EAAE;IACXE,WAAW,EAAE,WAAW;IACxBU,KAAK,EAAE;MAAEyE,UAAU,EAAEtF,GAAG,CAACoF,WAAW;MAAE9D,IAAI,EAAE;IAAQ,CAAC;IACrDiE,WAAW,EAAEvF,GAAG,CAACwF,EAAE,CAAC,CAClB;MACElB,GAAG,EAAE,YAAY;MACjBmB,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAE;QAClB,OAAOzF,EAAE,CACP,aAAa,EACb;UAAEE,WAAW,EAAE;QAAmB,CAAC,EACnC,CACEF,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAiB,CAAC,EACjC,CACEF,EAAE,CACA,KAAK,EACL;UACEE,WAAW,EAAE,aAAa;UAC1BsB,KAAK,EAAE;YACLkE,MAAM,EAAED,IAAI,CAACE,aAAa,KAAK,CAAC;YAChCC,OAAO,EAAEH,IAAI,CAACE,aAAa,KAAK;UAClC;QACF,CAAC,EACD,CACE3F,EAAE,CAAC,QAAQ,EAAE;UACXY,KAAK,EAAE;YACLC,IAAI,EACF4E,IAAI,CAACE,aAAa,KAAK,CAAC,GACpB,aAAa,GACb,cAAc;YACpBpC,KAAK,EAAE;UACT;QACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDvD,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAc,CAAC,EAC9B,CACEF,EAAE,CACA,KAAK,EACL;UACEE,WAAW,EACT;QACJ,CAAC,EACD,CAACH,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,EAAE,CAAC8D,IAAI,CAACL,WAAW,CAAC,CAAC,CACnC,CAAC,EACDpF,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAc,CAAC,EAC9B,CACEH,GAAG,CAAC2B,EAAE,CACJ3B,GAAG,CAAC4B,EAAE,CACJ5B,GAAG,CAAC8F,UAAU,CACZJ,IAAI,CAACK,UACP,CACF,CACF,CAAC,CAEL,CAAC,CAEL,CAAC,EACD9F,EAAE,CACA,KAAK,EACL;UACEE,WAAW,EAAE,eAAe;UAC5BsB,KAAK,EAAE;YACLkE,MAAM,EAAED,IAAI,CAACE,aAAa,KAAK,CAAC;YAChCC,OAAO,EAAEH,IAAI,CAACE,aAAa,KAAK;UAClC;QACF,CAAC,EACD,CACE5F,GAAG,CAAC2B,EAAE,CACJ,sBAAsB,GACpB3B,GAAG,CAAC4B,EAAE,CACJ8D,IAAI,CAACE,aAAa,KAAK,CAAC,GACpB,GAAG,GACH,GACN,CAAC,GACD5F,GAAG,CAAC4B,EAAE,CAAC8D,IAAI,CAACM,SAAS,CAAC,GACtB,oBACJ,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACNhG,GAAG,CAACoF,WAAW,CAACzB,MAAM,GAAG,CAAC,GACtB1D,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACE,CAACH,GAAG,CAACiG,SAAS,GACVhG,EAAE,CACA,UAAU,EACV;IACEY,KAAK,EAAE;MACLC,IAAI,EAAE,MAAM;MACZoF,QAAQ,EAAElG,GAAG,CAACmF;IAChB,CAAC;IACDjD,EAAE,EAAE;MAAEC,KAAK,EAAEnC,GAAG,CAACmG;IAAgB;EACnC,CAAC,EACD,CACEnG,GAAG,CAAC2B,EAAE,CACJ,kBAAkB,GAChB3B,GAAG,CAAC4B,EAAE,CACJ5B,GAAG,CAACmF,cAAc,GACd,QAAQ,GACR,MACN,CAAC,GACD,gBACJ,CAAC,CAEL,CAAC,GACDlF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACrCH,GAAG,CAAC2B,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,CACP,EACD,CACF,CAAC,GACD3B,GAAG,CAACoG,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,CAAC,EACFnG,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,GAAG,GAAG3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAACqG,WAAW,CAAC,CAAC,CACtC,CAAC,CACH,CAAC,EACFpG,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACF1B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAAC2B,EAAE,CAAC,GAAG,GAAG3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAACsG,YAAY,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBxG,MAAM,CAACyG,aAAa,GAAG,IAAI;AAE3B,SAASzG,MAAM,EAAEwG,eAAe", "ignoreList": []}]}