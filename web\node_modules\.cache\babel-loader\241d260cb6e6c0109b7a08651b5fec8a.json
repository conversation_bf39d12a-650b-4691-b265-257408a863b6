{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\permissions.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\permissions.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["export function actionToObject(json) {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    console.log('err', e.message);\n  }\n  return [];\n}", {"version": 3, "names": ["actionToObject", "json", "JSON", "parse", "e", "console", "log", "message"], "sources": ["E:/teachingproject/teaching/web/src/utils/permissions.js"], "sourcesContent": ["export function actionToObject(json) {\n  try {\n    return JSON.parse(json)\n  } catch (e) {\n    console.log('err', e.message)\n  }\n  return []\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAE;EACnC,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;EACzB,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEF,CAAC,CAACG,OAAO,CAAC;EAC/B;EACA,OAAO,EAAE;AACX", "ignoreList": []}]}