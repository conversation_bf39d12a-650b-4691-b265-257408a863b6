{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectMultiUser.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import JSelectBizComponent from './JSelectBizComponent';\nexport default {\n  name: 'JSelectMultiUser',\n  components: {\n    JSelectBizComponent: JSelectBizComponent\n  },\n  props: ['value'],\n  data: function data() {\n    return {\n      url: {\n        list: '/sys/user/list'\n      },\n      columns: [{\n        title: '姓名',\n        align: 'center',\n        width: '25%',\n        widthRight: '70%',\n        dataIndex: 'realname'\n      }, {\n        title: '账号',\n        align: 'center',\n        width: '25%',\n        dataIndex: 'username'\n      }, {\n        title: '电话',\n        align: 'center',\n        width: '20%',\n        dataIndex: 'phone'\n      }, {\n        title: '出生日期',\n        align: 'center',\n        width: '20%',\n        dataIndex: 'birthday'\n      }],\n      // 定义在这里的参数都是可以在外部传递覆盖的，可以更灵活的定制化使用的组件\n      default: {\n        name: '用户',\n        width: 1200,\n        displayKey: 'realname',\n        returnKeys: ['id', 'username'],\n        queryParamText: '账号'\n      }\n    };\n  },\n  computed: {\n    attrs: function attrs() {\n      return Object.assign(this.default, this.$attrs);\n    }\n  }\n};", {"version": 3, "names": ["JSelectBizComponent", "name", "components", "props", "data", "url", "list", "columns", "title", "align", "width", "widthRight", "dataIndex", "default", "displayKey", "returnKeys", "queryParamText", "computed", "attrs", "Object", "assign", "$attrs"], "sources": ["src/components/jeecgbiz/JSelectMultiUser.vue"], "sourcesContent": ["<template>\n  <!-- 定义在这里的参数都是不可在外部覆盖的，防止出现问题 -->\n  <j-select-biz-component\n    :value=\"value\"\n    :ellipsisLength=\"25\"\n    :listUrl=\"url.list\"\n    :columns=\"columns\"\n    v-on=\"$listeners\"\n    v-bind=\"attrs\"\n  />\n</template>\n\n<script>\n  import JSelectBizComponent from './JSelectBizComponent'\n\n  export default {\n    name: 'JSelectMultiUser',\n    components: { JSelectBizComponent },\n    props: ['value'],\n    data() {\n      return {\n        url: { list: '/sys/user/list' },\n        columns: [\n          { title: '姓名', align: 'center', width: '25%', widthRight: '70%', dataIndex: 'realname' },\n          { title: '账号', align: 'center', width: '25%', dataIndex: 'username' },\n          { title: '电话', align: 'center', width: '20%', dataIndex: 'phone' },\n          { title: '出生日期', align: 'center', width: '20%', dataIndex: 'birthday' }\n        ],\n        // 定义在这里的参数都是可以在外部传递覆盖的，可以更灵活的定制化使用的组件\n        default: {\n          name: '用户',\n          width: 1200,\n          displayKey: 'realname',\n          returnKeys: ['id', 'username'],\n          queryParamText: '账号',\n        }\n      }\n    },\n    computed: {\n      attrs() {\n        return Object.assign(this.default, this.$attrs)\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped></style>"], "mappings": "AAaA,OAAAA,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,mBAAA,EAAAA;EAAA;EACAG,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;QAAAC,IAAA;MAAA;MACAC,OAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,UAAA;QAAAC,SAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAE,SAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAE,SAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAE,SAAA;MAAA,EACA;MACA;MACAC,OAAA;QACAZ,IAAA;QACAS,KAAA;QACAI,UAAA;QACAC,UAAA;QACAC,cAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,OAAAC,MAAA,CAAAC,MAAA,MAAAP,OAAA,OAAAQ,MAAA;IACA;EACA;AACA", "ignoreList": []}]}