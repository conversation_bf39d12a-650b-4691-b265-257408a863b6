{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue?vue&type=template&id=0a6c4f15&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\CardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"content\",\n    staticClass: \"card-list\"\n  }, [_c(\"a-list\", {\n    attrs: {\n      grid: {\n        gutter: 24,\n        lg: 3,\n        md: 2,\n        sm: 1,\n        xs: 1\n      },\n      dataSource: _vm.dataSource\n    },\n    scopedSlots: _vm._u([{\n      key: \"renderItem\",\n      fn: function fn(item, index) {\n        return _c(\"a-list-item\", {}, [item === null ? [_c(\"a-button\", {\n          staticClass: \"new-btn\",\n          attrs: {\n            type: \"dashed\"\n          }\n        }, [_c(\"a-icon\", {\n          attrs: {\n            type: \"plus\"\n          }\n        }), _vm._v(\"\\n          新增产品\\n        \")], 1)] : [_c(\"a-card\", {\n          attrs: {\n            hoverable: true\n          }\n        }, [_c(\"a-card-meta\", [_c(\"div\", {\n          staticStyle: {\n            \"margin-bottom\": \"3px\"\n          },\n          attrs: {\n            slot: \"title\"\n          },\n          slot: \"title\"\n        }, [_vm._v(_vm._s(item.title))]), _c(\"a-avatar\", {\n          staticClass: \"card-avatar\",\n          attrs: {\n            slot: \"avatar\",\n            src: item.avatar,\n            size: \"large\"\n          },\n          slot: \"avatar\"\n        }), _c(\"div\", {\n          staticClass: \"meta-content\",\n          attrs: {\n            slot: \"description\"\n          },\n          slot: \"description\"\n        }, [_vm._v(_vm._s(item.content))])], 1), _c(\"template\", {\n          staticClass: \"ant-card-actions\",\n          slot: \"actions\"\n        }, [_c(\"a\", [_vm._v(\"操作一\")]), _c(\"a\", [_vm._v(\"操作二\")])])], 2)]], 2);\n      }\n    }])\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "attrs", "grid", "gutter", "lg", "md", "sm", "xs", "dataSource", "scopedSlots", "_u", "key", "fn", "item", "index", "type", "_v", "hoverable", "staticStyle", "slot", "_s", "title", "src", "avatar", "size", "content", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/list/CardList.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { ref: \"content\", staticClass: \"card-list\" },\n    [\n      _c(\"a-list\", {\n        attrs: {\n          grid: { gutter: 24, lg: 3, md: 2, sm: 1, xs: 1 },\n          dataSource: _vm.dataSource,\n        },\n        scopedSlots: _vm._u([\n          {\n            key: \"renderItem\",\n            fn: function (item, index) {\n              return _c(\n                \"a-list-item\",\n                {},\n                [\n                  item === null\n                    ? [\n                        _c(\n                          \"a-button\",\n                          { staticClass: \"new-btn\", attrs: { type: \"dashed\" } },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                            _vm._v(\"\\n          新增产品\\n        \"),\n                          ],\n                          1\n                        ),\n                      ]\n                    : [\n                        _c(\n                          \"a-card\",\n                          { attrs: { hoverable: true } },\n                          [\n                            _c(\n                              \"a-card-meta\",\n                              [\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticStyle: { \"margin-bottom\": \"3px\" },\n                                    attrs: { slot: \"title\" },\n                                    slot: \"title\",\n                                  },\n                                  [_vm._v(_vm._s(item.title))]\n                                ),\n                                _c(\"a-avatar\", {\n                                  staticClass: \"card-avatar\",\n                                  attrs: {\n                                    slot: \"avatar\",\n                                    src: item.avatar,\n                                    size: \"large\",\n                                  },\n                                  slot: \"avatar\",\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"meta-content\",\n                                    attrs: { slot: \"description\" },\n                                    slot: \"description\",\n                                  },\n                                  [_vm._v(_vm._s(item.content))]\n                                ),\n                              ],\n                              1\n                            ),\n                            _c(\n                              \"template\",\n                              {\n                                staticClass: \"ant-card-actions\",\n                                slot: \"actions\",\n                              },\n                              [\n                                _c(\"a\", [_vm._v(\"操作一\")]),\n                                _c(\"a\", [_vm._v(\"操作二\")]),\n                              ]\n                            ),\n                          ],\n                          2\n                        ),\n                      ],\n                ],\n                2\n              )\n            },\n          },\n        ]),\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,GAAG,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAY,CAAC,EAC5C,CACEH,EAAE,CAAC,QAAQ,EAAE;IACXI,KAAK,EAAE;MACLC,IAAI,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MAChDC,UAAU,EAAEZ,GAAG,CAACY;IAClB,CAAC;IACDC,WAAW,EAAEb,GAAG,CAACc,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,YAAY;MACjBC,EAAE,EAAE,SAAAA,GAAUC,IAAI,EAAEC,KAAK,EAAE;QACzB,OAAOjB,EAAE,CACP,aAAa,EACb,CAAC,CAAC,EACF,CACEgB,IAAI,KAAK,IAAI,GACT,CACEhB,EAAE,CACA,UAAU,EACV;UAAEG,WAAW,EAAE,SAAS;UAAEC,KAAK,EAAE;YAAEc,IAAI,EAAE;UAAS;QAAE,CAAC,EACrD,CACElB,EAAE,CAAC,QAAQ,EAAE;UAAEI,KAAK,EAAE;YAAEc,IAAI,EAAE;UAAO;QAAE,CAAC,CAAC,EACzCnB,GAAG,CAACoB,EAAE,CAAC,4BAA4B,CAAC,CACrC,EACD,CACF,CAAC,CACF,GACD,CACEnB,EAAE,CACA,QAAQ,EACR;UAAEI,KAAK,EAAE;YAAEgB,SAAS,EAAE;UAAK;QAAE,CAAC,EAC9B,CACEpB,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CACA,KAAK,EACL;UACEqB,WAAW,EAAE;YAAE,eAAe,EAAE;UAAM,CAAC;UACvCjB,KAAK,EAAE;YAAEkB,IAAI,EAAE;UAAQ,CAAC;UACxBA,IAAI,EAAE;QACR,CAAC,EACD,CAACvB,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACwB,EAAE,CAACP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAC7B,CAAC,EACDxB,EAAE,CAAC,UAAU,EAAE;UACbG,WAAW,EAAE,aAAa;UAC1BC,KAAK,EAAE;YACLkB,IAAI,EAAE,QAAQ;YACdG,GAAG,EAAET,IAAI,CAACU,MAAM;YAChBC,IAAI,EAAE;UACR,CAAC;UACDL,IAAI,EAAE;QACR,CAAC,CAAC,EACFtB,EAAE,CACA,KAAK,EACL;UACEG,WAAW,EAAE,cAAc;UAC3BC,KAAK,EAAE;YAAEkB,IAAI,EAAE;UAAc,CAAC;UAC9BA,IAAI,EAAE;QACR,CAAC,EACD,CAACvB,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACwB,EAAE,CAACP,IAAI,CAACY,OAAO,CAAC,CAAC,CAC/B,CAAC,CACF,EACD,CACF,CAAC,EACD5B,EAAE,CACA,UAAU,EACV;UACEG,WAAW,EAAE,kBAAkB;UAC/BmB,IAAI,EAAE;QACR,CAAC,EACD,CACEtB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EACxBnB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAE5B,CAAC,CACF,EACD,CACF,CAAC,CACF,CACN,EACD,CACF,CAAC;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIU,eAAe,GAAG,EAAE;AACxB/B,MAAM,CAACgC,aAAa,GAAG,IAAI;AAE3B,SAAShC,MAAM,EAAE+B,eAAe", "ignoreList": []}]}