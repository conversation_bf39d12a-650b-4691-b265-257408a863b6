{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue?vue&type=template&id=5b82e1da&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\Banner.vue", "mtime": 1751031689617}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"banner\">\n  <!-- 添加公告文字轮播组件 -->\n  <notice-marquee ref=\"noticeMarquee\" @showNotice=\"showNoticeDetail\"></notice-marquee>\n  <div v-if=\"banner.length > 0\">\n    <swiper ref=\"mySwiper\" :options=\"swiperOptions\" class=\"swiper-wrappe carousel\">\n      <swiper-slide class=\"swiper-slide\" v-for=\"(b, i) in banner\" :key=\"i\">\n        <a v-if=\"b.href\" :href=\"b.href\" target=\"_blank\">\n          <img :src=\"b.img\" alt=\"\" />\n        </a>\n        <img v-else :src=\"b.img\" alt=\"\" />\n      </swiper-slide>\n\n      <!-- 分页器 -->\n      <div\n        class=\"swiper-pagination swiper-pagination-white\"\n        slot=\"pagination\"\n      ></div>\n      <!-- 左右箭头 -->\n      <div\n        class=\"swiper-button-prev swiper-button-white\"\n        slot=\"button-prev\"\n      ></div>\n      <div\n        class=\"swiper-button-next swiper-button-white\"\n        slot=\"button-next\"\n      ></div>\n    </swiper>\n  </div>\n  <!-- 添加公告详情对话框组件 -->\n  <show-announcement ref=\"showAnnouncement\"></show-announcement>\n</div>\n", null]}