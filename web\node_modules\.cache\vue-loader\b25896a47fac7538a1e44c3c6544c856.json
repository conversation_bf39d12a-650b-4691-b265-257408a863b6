{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\RepositoryForm.vue?vue&type=template&id=4915c874&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\RepositoryForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-form @submit=\"handleSubmit\" :form=\"form\" class=\"form\">\n  <a-row class=\"form-row\" :gutter=\"16\">\n    <a-col :lg=\"6\" :md=\"12\" :sm=\"24\">\n      <a-form-item label=\"仓库名\">\n        <a-input\n          placeholder=\"请输入仓库名称\"\n          v-decorator=\"[\n            'repository.name',\n            {rules: [{ required: true, message: '请输入仓库名称', whitespace: true}]}\n          ]\" />\n      </a-form-item>\n    </a-col>\n    <a-col :xl=\"{span: 7, offset: 1}\" :lg=\"{span: 8}\" :md=\"{span: 12}\" :sm=\"24\">\n      <a-form-item\n        label=\"仓库域名\">\n        <a-input\n          addonBefore=\"http://\"\n          addonAfter=\".com\"\n          placeholder=\"请输入\"\n          v-decorator=\"[\n            'repository.domain',\n            {rules: [{ required: true, message: '请输入仓库域名', whitespace: true}, {validator: validate}]}\n          ]\" />\n      </a-form-item>\n    </a-col>\n    <a-col :xl=\"{span: 9, offset: 1}\" :lg=\"{span: 10}\" :md=\"{span: 24}\" :sm=\"24\">\n      <a-form-item\n        label=\"仓库管理员\">\n        <a-select placeholder=\"请选择管理员\" v-decorator=\"[ 'repository.manager', {rules: [{ required: true, message: '请选择管理员'}]} ]\">\n          <a-select-option value=\"王同学\">王同学</a-select-option>\n          <a-select-option value=\"李同学\">李同学</a-select-option>\n          <a-select-option value=\"黄同学\">黄同学</a-select-option>\n        </a-select>\n      </a-form-item>\n    </a-col>\n  </a-row>\n  <a-row class=\"form-row\" :gutter=\"16\">\n    <a-col :lg=\"6\" :md=\"12\" :sm=\"24\">\n      <a-form-item\n        label=\"审批人\">\n        <a-select placeholder=\"请选择审批员\" v-decorator=\"[ 'repository.auditor', {rules: [{ required: true, message: '请选择审批员'}]} ]\">\n          <a-select-option value=\"王晓丽\">王晓丽</a-select-option>\n          <a-select-option value=\"李军\">李军</a-select-option>\n        </a-select>\n      </a-form-item>\n    </a-col>\n    <a-col :xl=\"{span: 7, offset: 1}\" :lg=\"{span: 8}\" :md=\"{span: 12}\" :sm=\"24\">\n      <a-form-item\n        label=\"生效日期\">\n        <a-range-picker\n          style=\"width: 100%\"\n          v-decorator=\"[\n            'repository.effectiveDate',\n            {rules: [{ required: true, message: '请选择生效日期'}]}\n          ]\" />\n      </a-form-item>\n    </a-col>\n    <a-col :xl=\"{span: 9, offset: 1}\" :lg=\"{span: 10}\" :md=\"{span: 24}\" :sm=\"24\">\n      <a-form-item\n        label=\"仓库类型\">\n        <a-select\n          placeholder=\"请选择仓库类型\"\n          v-decorator=\"[\n            'repository.type',\n            {rules: [{ required: true, message: '请选择仓库类型'}]}\n          ]\" >\n          <a-select-option value=\"公开\">公开</a-select-option>\n          <a-select-option value=\"私密\">私密</a-select-option>\n        </a-select>\n      </a-form-item>\n    </a-col>\n  </a-row>\n  <a-form-item v-if=\"showSubmit\">\n    <a-button htmlType=\"submit\" >Submit</a-button>\n  </a-form-item>\n</a-form>\n", null]}