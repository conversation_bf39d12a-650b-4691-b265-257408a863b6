{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\MiniBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import moment from 'dayjs';\nvar sourceData = [];\nvar beginDay = new Date().getTime();\nfor (var i = 0; i < 10; i++) {\n  sourceData.push({\n    x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n    y: Math.round(Math.random() * 10)\n  });\n}\nvar tooltip = ['x*y', function (x, y) {\n  return {\n    name: x,\n    value: y\n  };\n}];\nvar scale = [{\n  dataKey: 'x',\n  min: 2\n}, {\n  dataKey: 'y',\n  title: '时间',\n  min: 1,\n  max: 30\n}];\nexport default {\n  name: 'MiniBar',\n  props: {\n    dataSource: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    width: {\n      type: Number,\n      default: null\n    },\n    height: {\n      type: Number,\n      default: 200\n    }\n  },\n  created: function created() {\n    if (this.dataSource.length === 0) {\n      this.data = sourceData;\n    } else {\n      this.data = this.dataSource;\n    }\n  },\n  data: function data() {\n    return {\n      tooltip: tooltip,\n      data: [],\n      scale: scale\n    };\n  }\n};", {"version": 3, "names": ["moment", "sourceData", "beginDay", "Date", "getTime", "i", "push", "x", "format", "y", "Math", "round", "random", "tooltip", "name", "value", "scale", "dataKey", "min", "title", "max", "props", "dataSource", "type", "Array", "default", "_default", "width", "Number", "height", "created", "length", "data"], "sources": ["src/components/chart/MiniBar.vue"], "sourcesContent": ["<template>\n  <div :style=\"{'width':width==null?'auto':width+'px'}\">\n    <v-chart :forceFit=\"width==null\" :height=\"height\" :data=\"data\" padding=\"0\">\n      <v-tooltip/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import moment from 'dayjs'\n\n  const sourceData = []\n  const beginDay = new Date().getTime()\n\n  for (let i = 0; i < 10; i++) {\n    sourceData.push({\n      x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\n      y: Math.round(Math.random() * 10)\n    })\n  }\n\n  const tooltip = [\n    'x*y',\n    (x, y) => ({\n      name: x,\n      value: y\n    })\n  ]\n\n  const scale = [{\n    dataKey: 'x',\n    min: 2\n  }, {\n    dataKey: 'y',\n    title: '时间',\n    min: 1,\n    max: 30\n  }]\n\n  export default {\n    name: 'MiniBar',\n    props: {\n      dataSource: {\n        type: Array,\n        default: () => []\n      },\n      width: {\n        type: Number,\n        default: null\n      },\n      height: {\n        type: Number,\n        default: 200\n      }\n    },\n    created() {\n      if (this.dataSource.length === 0) {\n        this.data = sourceData\n      } else {\n        this.data = this.dataSource\n      }\n    },\n    data() {\n      return {\n        tooltip,\n        data: [],\n        scale\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"chart\";\n</style>"], "mappings": "AAUA,OAAAA,MAAA;AAEA,IAAAC,UAAA;AACA,IAAAC,QAAA,OAAAC,IAAA,GAAAC,OAAA;AAEA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;EACAJ,UAAA,CAAAK,IAAA;IACAC,CAAA,EAAAP,MAAA,KAAAG,IAAA,CAAAD,QAAA,yBAAAG,CAAA,GAAAG,MAAA;IACAC,CAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;EACA;AACA;AAEA,IAAAC,OAAA,IACA,OACA,UAAAN,CAAA,EAAAE,CAAA;EAAA;IACAK,IAAA,EAAAP,CAAA;IACAQ,KAAA,EAAAN;EACA;AAAA,EACA;AAEA,IAAAO,KAAA;EACAC,OAAA;EACAC,GAAA;AACA;EACAD,OAAA;EACAE,KAAA;EACAD,GAAA;EACAE,GAAA;AACA;AAEA;EACAN,IAAA;EACAO,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;IACAI,MAAA;MACAN,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,SAAAR,UAAA,CAAAS,MAAA;MACA,KAAAC,IAAA,GAAA/B,UAAA;IACA;MACA,KAAA+B,IAAA,QAAAV,UAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAnB,OAAA,EAAAA,OAAA;MACAmB,IAAA;MACAhB,KAAA,EAAAA;IACA;EACA;AACA", "ignoreList": []}]}