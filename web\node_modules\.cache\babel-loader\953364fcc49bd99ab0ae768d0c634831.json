{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue?vue&type=template&id=dbb7a860&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\SplitPanelModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-modal\", {\n    staticStyle: {\n      top: \"0px\"\n    },\n    attrs: {\n      title: \"分屏\",\n      width: _vm.modalWidth,\n      visible: _vm.visible,\n      bodyStyle: _vm.bodyStyle,\n      cancelText: \"关闭\"\n    },\n    on: {\n      ok: _vm.handleOk,\n      cancel: _vm.handleCancel\n    }\n  }, [_c(\"split-pane\", {\n    attrs: {\n      \"min-percent\": 20,\n      \"default-percent\": 50,\n      split: \"vertical\"\n    }\n  }, [_c(\"template\", {\n    slot: \"paneL\"\n  }, [_c(\"split-panel-a\")], 1), _c(\"template\", {\n    slot: \"paneR\"\n  }, [_c(\"split-panel-b\")], 1)], 2)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "top", "attrs", "title", "width", "modalWidth", "visible", "bodyStyle", "cancelText", "on", "ok", "handleOk", "cancel", "handleCancel", "split", "slot", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/jeecg/modules/SplitPanelModal.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-modal\",\n    {\n      staticStyle: { top: \"0px\" },\n      attrs: {\n        title: \"分屏\",\n        width: _vm.modalWidth,\n        visible: _vm.visible,\n        bodyStyle: _vm.bodyStyle,\n        cancelText: \"关闭\",\n      },\n      on: { ok: _vm.handleOk, cancel: _vm.handleCancel },\n    },\n    [\n      _c(\n        \"split-pane\",\n        {\n          attrs: {\n            \"min-percent\": 20,\n            \"default-percent\": 50,\n            split: \"vertical\",\n          },\n        },\n        [\n          _c(\"template\", { slot: \"paneL\" }, [_c(\"split-panel-a\")], 1),\n          _c(\"template\", { slot: \"paneR\" }, [_c(\"split-panel-b\")], 1),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,SAAS,EACT;IACEE,WAAW,EAAE;MAAEC,GAAG,EAAE;IAAM,CAAC;IAC3BC,KAAK,EAAE;MACLC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAEP,GAAG,CAACQ,UAAU;MACrBC,OAAO,EAAET,GAAG,CAACS,OAAO;MACpBC,SAAS,EAAEV,GAAG,CAACU,SAAS;MACxBC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MAAEC,EAAE,EAAEb,GAAG,CAACc,QAAQ;MAAEC,MAAM,EAAEf,GAAG,CAACgB;IAAa;EACnD,CAAC,EACD,CACEf,EAAE,CACA,YAAY,EACZ;IACEI,KAAK,EAAE;MACL,aAAa,EAAE,EAAE;MACjB,iBAAiB,EAAE,EAAE;MACrBY,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhB,EAAE,CAAC,UAAU,EAAE;IAAEiB,IAAI,EAAE;EAAQ,CAAC,EAAE,CAACjB,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3DA,EAAE,CAAC,UAAU,EAAE;IAAEiB,IAAI,EAAE;EAAQ,CAAC,EAAE,CAACjB,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAC5D,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIkB,eAAe,GAAG,EAAE;AACxBpB,MAAM,CAACqB,aAAa,GAAG,IAAI;AAE3B,SAASrB,MAAM,EAAEoB,eAAe", "ignoreList": []}]}