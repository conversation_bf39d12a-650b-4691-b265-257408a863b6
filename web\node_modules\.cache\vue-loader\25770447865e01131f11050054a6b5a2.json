{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue?vue&type=template&id=05ee2800&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\modules\\PaperModal.vue", "mtime": 1753249833907}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"modalWidth\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  :maskClosable=\"false\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\">\n  \n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n      <!-- 基本信息 -->\n      <a-form-item label=\"试卷标题\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-input\n          placeholder=\"请输入试卷标题\"\n          v-decorator=\"['title', {rules: [{required: true, message: '请输入试卷标题!'}]}]\" />\n      </a-form-item>\n      \n      <a-form-item label=\"所属科目\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-select\n          placeholder=\"请选择所属科目\"\n          v-decorator=\"['subject', {rules: [{required: true, message: '请选择所属科目!'}]}]\"\n          @change=\"onSubjectChange\">\n          <a-select-option value=\"Scratch\">Scratch</a-select-option>\n          <a-select-option value=\"Python\">Python</a-select-option>\n          <a-select-option value=\"C++\">C++</a-select-option>\n        </a-select>\n      </a-form-item>\n      \n      <a-form-item label=\"所属级别\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-select\n          placeholder=\"请选择所属级别\"\n          v-decorator=\"['level', {rules: [{required: true, message: '请选择所属级别!'}]}]\"\n          @change=\"onLevelChange\">\n          <a-select-option v-for=\"(level, index) in levelOptions\" :key=\"index\" :value=\"level\">\n            {{ level }}\n          </a-select-option>\n        </a-select>\n      </a-form-item>\n      \n      <a-form-item label=\"难度\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-select\n          placeholder=\"请选择难度\"\n          v-decorator=\"['difficulty', {rules: [{required: true, message: '请选择难度!'}]}]\">\n          <a-select-option :value=\"1\">简单</a-select-option>\n          <a-select-option :value=\"2\">中等</a-select-option>\n          <a-select-option :value=\"3\">困难</a-select-option>\n        </a-select>\n      </a-form-item>\n      \n      <a-form-item label=\"类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-select\n          placeholder=\"请选择类型\"\n          v-decorator=\"['type', {rules: [{required: true, message: '请选择类型!'}]}]\">\n          <a-select-option value=\"真题\">真题</a-select-option>\n          <a-select-option value=\"模拟\">模拟</a-select-option>\n        </a-select>\n      </a-form-item>\n      \n      <a-form-item label=\"年份\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-date-picker\n          placeholder=\"请选择年份\"\n          v-decorator=\"['year', {rules: [{required: true, message: '请选择年份!'}]}]\"\n          :format=\"yearFormat\"\n          :mode=\"yearMode\"\n          @panelChange=\"handleYearPanelChange\"\n          @change=\"handleYearChange\" />\n      </a-form-item>\n      \n      <a-form-item label=\"作者\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-input\n          placeholder=\"请输入作者\"\n          v-decorator=\"['author', {rules: [{required: true, message: '请输入作者!'}]}]\" />\n      </a-form-item>\n      \n      <a-form-item label=\"考试时长(分钟)\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\">\n        <a-input-number\n          placeholder=\"请输入考试时长\"\n          v-decorator=\"['examDuration', {rules: [{required: true, message: '请输入考试时长!'}]}]\"\n          :min=\"1\"\n          :max=\"1440\"\n          :step=\"10\" />\n      </a-form-item>\n      \n      <!-- 题目分数设置 -->\n      <a-divider>题目分数设置</a-divider>\n      <a-row :gutter=\"24\">\n        <a-col :span=\"8\">\n          <a-form-item label=\"单选题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n            <a-input-number\n              placeholder=\"单选题每题分数\"\n              v-decorator=\"['singleChoiceScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n              :min=\"1\"\n              :max=\"100\" />\n          </a-form-item>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-form-item label=\"判断题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n            <a-input-number\n              placeholder=\"判断题每题分数\"\n              v-decorator=\"['judgmentScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n              :min=\"1\"\n              :max=\"100\" />\n          </a-form-item>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-form-item label=\"编程题每题分数\" :labelCol=\"smallLabelCol\" :wrapperCol=\"smallWrapperCol\">\n            <a-input-number\n              placeholder=\"编程题每题分数\"\n              v-decorator=\"['programmingScore', {rules: [{required: true, message: '请输入分数!'}]}]\"\n              :min=\"1\"\n              :max=\"100\" />\n          </a-form-item>\n        </a-col>\n      </a-row>\n      \n      <!-- 题目选择区域 -->\n      <a-divider>题目选择</a-divider>\n      \n      <!-- 单选题选择 -->\n      <a-collapse defaultActiveKey=\"1\">\n        <a-collapse-panel key=\"1\" header=\"单选题\">\n          <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(1)\">\n            添加单选题\n          </a-button>\n          <a-table\n            size=\"small\"\n            rowKey=\"id\"\n            :columns=\"singleChoiceColumns\"\n            :dataSource=\"selectedQuestions.singleChoiceQuestions\"\n            :pagination=\"false\"\n            style=\"margin-top: 10px\"\n          >\n            <span slot=\"action\" slot-scope=\"text, record, index\">\n              <a @click=\"() => removeQuestion('singleChoiceQuestions', index)\">删除</a>\n            </span>\n          </a-table>\n        </a-collapse-panel>\n        \n        <!-- 判断题选择 -->\n        <a-collapse-panel key=\"2\" header=\"判断题\">\n          <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(2)\">\n            添加判断题\n          </a-button>\n          <a-table\n            size=\"small\"\n            rowKey=\"id\"\n            :columns=\"judgmentColumns\"\n            :dataSource=\"selectedQuestions.judgmentQuestions\"\n            :pagination=\"false\"\n            style=\"margin-top: 10px\"\n          >\n            <span slot=\"action\" slot-scope=\"text, record, index\">\n              <a @click=\"() => removeQuestion('judgmentQuestions', index)\">删除</a>\n            </span>\n          </a-table>\n        </a-collapse-panel>\n        \n        <!-- 编程题选择 -->\n        <a-collapse-panel key=\"3\" header=\"编程题\">\n          <a-button type=\"primary\" icon=\"plus\" @click=\"showQuestionSelector(3)\">\n            添加编程题\n          </a-button>\n          <a-table\n            size=\"small\"\n            rowKey=\"id\"\n            :columns=\"programmingColumns\"\n            :dataSource=\"selectedQuestions.programmingQuestions\"\n            :pagination=\"false\"\n            style=\"margin-top: 10px\"\n          >\n            <span slot=\"action\" slot-scope=\"text, record, index\">\n              <a @click=\"() => removeQuestion('programmingQuestions', index)\">删除</a>\n            </span>\n          </a-table>\n        </a-collapse-panel>\n      </a-collapse>\n    </a-form>\n  </a-spin>\n  \n  <!-- 题目选择器模态框 -->\n  <a-modal\n    :title=\"questionSelectorTitle\"\n    :width=\"800\"\n    :visible=\"questionSelectorVisible\"\n    :footer=\"null\"\n    @cancel=\"handleQuestionSelectorCancel\"\n  >\n    <!-- 查询条件 -->\n    <a-form layout=\"inline\">\n      <a-row :gutter=\"12\">\n        <a-col :md=\"12\">\n          <a-form-item label=\"题目标题\">\n            <a-input v-model=\"questionQueryParam.title\" placeholder=\"请输入题目标题\" />\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"12\">\n          <a-form-item label=\"科目/级别\">\n            <a-input v-model=\"questionQueryParam.subject\" disabled />\n            <a-input v-model=\"questionQueryParam.level\" disabled style=\"width: 80px; margin-left: 8px;\" />\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"12\">\n          <a-form-item label=\"难度\">\n            <a-select v-model=\"questionQueryParam.difficulty\" placeholder=\"请选择难度\" allowClear style=\"width: 160px;\">\n              <a-select-option :value=\"1\">简单</a-select-option>\n              <a-select-option :value=\"2\">中等</a-select-option>\n              <a-select-option :value=\"3\">困难</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :md=\"12\">\n          <a-button type=\"primary\" @click=\"loadQuestions(1)\">查询</a-button>\n          <a-button style=\"margin-left: 8px\" @click=\"resetQuestionQuery\">重置</a-button>\n        </a-col>\n      </a-row>\n    </a-form>\n    \n    <!-- 题目列表 -->\n    <a-table\n      size=\"middle\"\n      rowKey=\"id\"\n      :columns=\"questionSelectorColumns\"\n      :dataSource=\"questionList\"\n      :pagination=\"questionPagination\"\n      :loading=\"questionLoading\"\n      :rowSelection=\"{\n        selectedRowKeys: tempSelectedQuestionKeys,\n        onChange: onQuestionSelectionChange\n      }\"\n      @change=\"handleQuestionTableChange\"\n      style=\"margin-top: 16px;\"\n    >\n      <!-- 自定义难度展示 -->\n      <template slot=\"difficultySlot\" slot-scope=\"text\">\n        <a-tag color=\"green\" v-if=\"text === 1\">简单</a-tag>\n        <a-tag color=\"orange\" v-else-if=\"text === 2\">中等</a-tag>\n        <a-tag color=\"red\" v-else-if=\"text === 3\">困难</a-tag>\n        <a-tag v-else>未知</a-tag>\n      </template>\n    </a-table>\n    \n    <!-- 操作按钮 -->\n    <div style=\"text-align: right; margin-top: 16px;\">\n      <a-button @click=\"handleQuestionSelectorCancel\">取消</a-button>\n      <a-button type=\"primary\" style=\"margin-left: 8px;\" @click=\"confirmQuestionSelection\">\n        确认选择\n      </a-button>\n    </div>\n  </a-modal>\n</a-modal>\n", null]}