{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue?vue&type=template&id=0c70ee28&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue", "mtime": 1753243085891}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm.showMarquee ? _c(\"div\", {\n    staticClass: \"notice-marquee-wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"notice-icon\"\n  }, [_c(\"a-icon\", {\n    attrs: {\n      type: \"notification\",\n      theme: \"filled\"\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"notice-content\"\n  }, [_c(\"a-carousel\", {\n    attrs: {\n      autoplay: true,\n      vertical: \"\",\n      dots: false,\n      autoplaySpeed: _vm.rotateSpeed\n    }\n  }, _vm._l(_vm.validNotices, function (notice, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"notice-item\"\n    }, [_c(\"div\", {\n      staticClass: \"notice-main-content\"\n    }, [_c(\"div\", {\n      staticClass: \"notice-title-line\"\n    }, [_c(\"a\", {\n      attrs: {\n        title: notice.title\n      },\n      on: {\n        click: function click($event) {\n          return _vm.showNoticeDetail(notice);\n        }\n      }\n    }, [_vm._v(\"\\n              \" + _vm._s(notice.title) + \"\\n            \")]), notice.priority === \"L\" ? _c(\"span\", {\n      staticClass: \"notice-tag normal\"\n    }, [_vm._v(\"一般\")]) : _vm._e(), notice.priority === \"M\" ? _c(\"span\", {\n      staticClass: \"notice-tag important\"\n    }, [_vm._v(\"重要\")]) : _vm._e(), notice.priority === \"H\" ? _c(\"span\", {\n      staticClass: \"notice-tag urgent\"\n    }, [_vm._v(\"紧急\")]) : _vm._e()]), notice.msgAbstract ? _c(\"div\", {\n      staticClass: \"notice-abstract\"\n    }, [_vm._v(_vm._s(notice.msgAbstract))]) : _vm._e()])]);\n  }), 0)], 1), _c(\"div\", {\n    staticClass: \"more-link\"\n  }, [_c(\"a\", {\n    on: {\n      click: _vm.showMoreNotices\n    }\n  }, [_vm._v(\"更多\")])])]) : _vm._e();\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "showMarquee", "staticClass", "attrs", "type", "theme", "autoplay", "vertical", "dots", "autoplaySpeed", "rotateSpeed", "_l", "validNotices", "notice", "index", "key", "title", "on", "click", "$event", "showNoticeDetail", "_v", "_s", "priority", "_e", "msgAbstract", "showMoreNotices", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/home/<USER>/NoticeMarquee.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.showMarquee\n    ? _c(\"div\", { staticClass: \"notice-marquee-wrapper\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"notice-icon\" },\n          [_c(\"a-icon\", { attrs: { type: \"notification\", theme: \"filled\" } })],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"notice-content\" },\n          [\n            _c(\n              \"a-carousel\",\n              {\n                attrs: {\n                  autoplay: true,\n                  vertical: \"\",\n                  dots: false,\n                  autoplaySpeed: _vm.rotateSpeed,\n                },\n              },\n              _vm._l(_vm.validNotices, function (notice, index) {\n                return _c(\"div\", { key: index, staticClass: \"notice-item\" }, [\n                  _c(\"div\", { staticClass: \"notice-main-content\" }, [\n                    _c(\"div\", { staticClass: \"notice-title-line\" }, [\n                      _c(\n                        \"a\",\n                        {\n                          attrs: { title: notice.title },\n                          on: {\n                            click: function ($event) {\n                              return _vm.showNoticeDetail(notice)\n                            },\n                          },\n                        },\n                        [\n                          _vm._v(\n                            \"\\n              \" +\n                              _vm._s(notice.title) +\n                              \"\\n            \"\n                          ),\n                        ]\n                      ),\n                      notice.priority === \"L\"\n                        ? _c(\"span\", { staticClass: \"notice-tag normal\" }, [\n                            _vm._v(\"一般\"),\n                          ])\n                        : _vm._e(),\n                      notice.priority === \"M\"\n                        ? _c(\"span\", { staticClass: \"notice-tag important\" }, [\n                            _vm._v(\"重要\"),\n                          ])\n                        : _vm._e(),\n                      notice.priority === \"H\"\n                        ? _c(\"span\", { staticClass: \"notice-tag urgent\" }, [\n                            _vm._v(\"紧急\"),\n                          ])\n                        : _vm._e(),\n                    ]),\n                    notice.msgAbstract\n                      ? _c(\"div\", { staticClass: \"notice-abstract\" }, [\n                          _vm._v(_vm._s(notice.msgAbstract)),\n                        ])\n                      : _vm._e(),\n                  ]),\n                ])\n              }),\n              0\n            ),\n          ],\n          1\n        ),\n        _c(\"div\", { staticClass: \"more-link\" }, [\n          _c(\"a\", { on: { click: _vm.showMoreNotices } }, [_vm._v(\"更多\")]),\n        ]),\n      ])\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,WAAW,GAClBF,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDH,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAc,CAAC,EAC9B,CAACH,EAAE,CAAC,QAAQ,EAAE;IAAEI,KAAK,EAAE;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAS;EAAE,CAAC,CAAC,CAAC,EACpE,CACF,CAAC,EACDN,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEH,EAAE,CACA,YAAY,EACZ;IACEI,KAAK,EAAE;MACLG,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,KAAK;MACXC,aAAa,EAAEX,GAAG,CAACY;IACrB;EACF,CAAC,EACDZ,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,YAAY,EAAE,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAChD,OAAOf,EAAE,CAAC,KAAK,EAAE;MAAEgB,GAAG,EAAED,KAAK;MAAEZ,WAAW,EAAE;IAAc,CAAC,EAAE,CAC3DH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC9CH,EAAE,CACA,GAAG,EACH;MACEI,KAAK,EAAE;QAAEa,KAAK,EAAEH,MAAM,CAACG;MAAM,CAAC;MAC9BC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOrB,GAAG,CAACsB,gBAAgB,CAACP,MAAM,CAAC;QACrC;MACF;IACF,CAAC,EACD,CACEf,GAAG,CAACuB,EAAE,CACJ,kBAAkB,GAChBvB,GAAG,CAACwB,EAAE,CAACT,MAAM,CAACG,KAAK,CAAC,GACpB,gBACJ,CAAC,CAEL,CAAC,EACDH,MAAM,CAACU,QAAQ,KAAK,GAAG,GACnBxB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,GACFvB,GAAG,CAAC0B,EAAE,CAAC,CAAC,EACZX,MAAM,CAACU,QAAQ,KAAK,GAAG,GACnBxB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAuB,CAAC,EAAE,CAClDJ,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,GACFvB,GAAG,CAAC0B,EAAE,CAAC,CAAC,EACZX,MAAM,CAACU,QAAQ,KAAK,GAAG,GACnBxB,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAoB,CAAC,EAAE,CAC/CJ,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,GACFvB,GAAG,CAAC0B,EAAE,CAAC,CAAC,CACb,CAAC,EACFX,MAAM,CAACY,WAAW,GACd1B,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACwB,EAAE,CAACT,MAAM,CAACY,WAAW,CAAC,CAAC,CACnC,CAAC,GACF3B,GAAG,CAAC0B,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDzB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,EAAE,CAAC,GAAG,EAAE;IAAEkB,EAAE,EAAE;MAAEC,KAAK,EAAEpB,GAAG,CAAC4B;IAAgB;EAAE,CAAC,EAAE,CAAC5B,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAChE,CAAC,CACH,CAAC,GACFvB,GAAG,CAAC0B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIG,eAAe,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI;AAE3B,SAAS/B,MAAM,EAAE8B,eAAe", "ignoreList": []}]}