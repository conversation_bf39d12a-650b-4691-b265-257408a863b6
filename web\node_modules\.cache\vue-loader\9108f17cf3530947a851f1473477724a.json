{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Security.vue?vue&type=template&id=4b2f4ac0&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Security.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<a-list\n  itemLayout=\"horizontal\"\n  :dataSource=\"data\"\n>\n  <a-list-item slot=\"renderItem\" slot-scope=\"item, index\" :key=\"index\">\n    <a-list-item-meta>\n      <a slot=\"title\">{{ item.title }}</a>\n      <span slot=\"description\">\n        <span class=\"security-list-description\">{{ item.description }}</span>\n        <span v-if=\"item.value\"> : </span>\n        <span class=\"security-list-value\">{{ item.value }}</span>\n      </span>\n    </a-list-item-meta>\n    <template v-if=\"item.actions\">\n      <a slot=\"actions\" @click=\"item.actions.callback\">{{ item.actions.title }}</a>\n    </template>\n\n  </a-list-item>\n</a-list>\n", null]}