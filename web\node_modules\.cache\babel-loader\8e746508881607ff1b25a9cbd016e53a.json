{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JImportModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { postAction } from '@/api/manage';\nexport default {\n  name: 'JImportModal',\n  props: {\n    url: {\n      type: String,\n      default: '',\n      required: false\n    },\n    biz: {\n      type: String,\n      default: '',\n      required: false\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      uploading: false,\n      fileList: [],\n      uploadAction: '',\n      foreignKeys: ''\n    };\n  },\n  watch: {\n    url: function url(val) {\n      if (val) {\n        this.uploadAction = window._CONFIG['domianURL'] + val;\n      }\n    }\n  },\n  created: function created() {\n    this.uploadAction = window._CONFIG['domianURL'] + this.url;\n  },\n  methods: {\n    handleClose: function handleClose() {\n      this.visible = false;\n    },\n    show: function show(arg) {\n      this.fileList = [];\n      this.uploading = false;\n      this.visible = true;\n      this.foreignKeys = arg;\n    },\n    handleRemove: function handleRemove(file) {\n      var index = this.fileList.indexOf(file);\n      var newFileList = this.fileList.slice();\n      newFileList.splice(index, 1);\n      this.fileList = newFileList;\n    },\n    beforeUpload: function beforeUpload(file) {\n      this.fileList = [].concat(_toConsumableArray(this.fileList), [file]);\n      return false;\n    },\n    handleImport: function handleImport() {\n      var _this = this;\n      var fileList = this.fileList;\n      var formData = new FormData();\n      if (this.biz) {\n        formData.append('isSingleTableImport', this.biz);\n      }\n      if (this.foreignKeys && this.foreignKeys.length > 0) {\n        formData.append('foreignKeys', this.foreignKeys);\n      }\n      fileList.forEach(function (file) {\n        formData.append('files[]', file);\n      });\n      this.uploading = true;\n      postAction(this.uploadAction, formData).then(function (res) {\n        _this.uploading = false;\n        if (res.success) {\n          _this.$message.success(res.message);\n          _this.visible = false;\n          _this.$emit('ok');\n        } else {\n          _this.$message.warning(res.message);\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["postAction", "name", "props", "url", "type", "String", "default", "required", "biz", "data", "visible", "uploading", "fileList", "uploadAction", "foreignKeys", "watch", "val", "window", "_CONFIG", "created", "methods", "handleClose", "show", "arg", "handleRemove", "file", "index", "indexOf", "newFileList", "slice", "splice", "beforeUpload", "concat", "_toConsumableArray", "handleImport", "_this", "formData", "FormData", "append", "length", "for<PERSON>ach", "then", "res", "success", "$message", "message", "$emit", "warning"], "sources": ["src/components/jeecg/JImportModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    title=\"导入EXCEL\"\n    :width=\"600\"\n    :visible=\"visible\"\n    :confirmLoading=\"uploading\"\n    @cancel=\"handleClose\">\n\n    <a-upload\n      name=\"file\"\n      :multiple=\"true\"\n      accept=\".xls,.xlsx\"\n      :fileList=\"fileList\"\n      :remove=\"handleRemove\"\n      :beforeUpload=\"beforeUpload\">\n      <a-button>\n        <a-icon type=\"upload\" />\n        选择导入文件\n      </a-button>\n    </a-upload>\n\n    <template slot=\"footer\">\n      <a-button @click=\"handleClose\">关闭</a-button>\n      <a-button\n        type=\"primary\"\n        @click=\"handleImport\"\n        :disabled=\"fileList.length === 0\"\n        :loading=\"uploading\">\n        {{ uploading ? '上传中...' : '开始上传' }}\n      </a-button>\n    </template>\n\n  </a-modal>\n</template>\n\n<script>\n  import { postAction } from '@/api/manage'\n  export default {\n    name: 'JImportModal',\n    props:{\n      url:{\n        type: String,\n        default: '',\n        required: false\n      },\n      biz:{\n        type: String,\n        default: '',\n        required: false\n      }\n    },\n    data(){\n      return {\n        visible:false,\n        uploading:false,\n        fileList:[],\n        uploadAction:'',\n        foreignKeys:''\n      }\n    },\n    watch: {\n      url (val) {\n        if(val){\n         this.uploadAction = window._CONFIG['domianURL']+val\n        }\n      }\n    },\n    created () {\n      this.uploadAction = window._CONFIG['domianURL']+this.url\n    },\n\n    methods:{\n      handleClose(){\n        this.visible=false\n      },\n      show(arg){\n        this.fileList = []\n        this.uploading = false\n        this.visible = true\n        this.foreignKeys = arg;\n      },\n      handleRemove(file) {\n        const index = this.fileList.indexOf(file);\n        const newFileList = this.fileList.slice();\n        newFileList.splice(index, 1);\n        this.fileList = newFileList\n      },\n      beforeUpload(file) {\n        this.fileList = [...this.fileList, file]\n        return false;\n      },\n      handleImport() {\n        const { fileList } = this;\n        const formData = new FormData();\n        if(this.biz){\n          formData.append('isSingleTableImport',this.biz);\n        }\n        if(this.foreignKeys && this.foreignKeys.length>0){\n          formData.append('foreignKeys',this.foreignKeys);\n        }\n        fileList.forEach((file) => {\n          formData.append('files[]', file);\n        });\n        this.uploading = true\n        postAction(this.uploadAction, formData).then((res) => {\n          this.uploading = false\n          if(res.success){\n            this.$message.success(res.message)\n            this.visible=false\n            this.$emit('ok')\n          }else{\n            this.$message.warning(res.message)\n          }\n        })\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": ";;;;;;AAoCA,SAAAA,UAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACAC,GAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,YAAA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACAZ,GAAA,WAAAA,IAAAa,GAAA;MACA,IAAAA,GAAA;QACA,KAAAH,YAAA,GAAAI,MAAA,CAAAC,OAAA,gBAAAF,GAAA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAN,YAAA,GAAAI,MAAA,CAAAC,OAAA,qBAAAf,GAAA;EACA;EAEAiB,OAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAX,OAAA;IACA;IACAY,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAX,QAAA;MACA,KAAAD,SAAA;MACA,KAAAD,OAAA;MACA,KAAAI,WAAA,GAAAS,GAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,KAAA,QAAAd,QAAA,CAAAe,OAAA,CAAAF,IAAA;MACA,IAAAG,WAAA,QAAAhB,QAAA,CAAAiB,KAAA;MACAD,WAAA,CAAAE,MAAA,CAAAJ,KAAA;MACA,KAAAd,QAAA,GAAAgB,WAAA;IACA;IACAG,YAAA,WAAAA,aAAAN,IAAA;MACA,KAAAb,QAAA,MAAAoB,MAAA,CAAAC,kBAAA,MAAArB,QAAA,IAAAa,IAAA;MACA;IACA;IACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAvB,QAAA,QAAAA,QAAA;MACA,IAAAwB,QAAA,OAAAC,QAAA;MACA,SAAA7B,GAAA;QACA4B,QAAA,CAAAE,MAAA,6BAAA9B,GAAA;MACA;MACA,SAAAM,WAAA,SAAAA,WAAA,CAAAyB,MAAA;QACAH,QAAA,CAAAE,MAAA,qBAAAxB,WAAA;MACA;MACAF,QAAA,CAAA4B,OAAA,WAAAf,IAAA;QACAW,QAAA,CAAAE,MAAA,YAAAb,IAAA;MACA;MACA,KAAAd,SAAA;MACAX,UAAA,MAAAa,YAAA,EAAAuB,QAAA,EAAAK,IAAA,WAAAC,GAAA;QACAP,KAAA,CAAAxB,SAAA;QACA,IAAA+B,GAAA,CAAAC,OAAA;UACAR,KAAA,CAAAS,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAG,OAAA;UACAV,KAAA,CAAAzB,OAAA;UACAyB,KAAA,CAAAW,KAAA;QACA;UACAX,KAAA,CAAAS,QAAA,CAAAG,OAAA,CAAAL,GAAA,CAAAG,OAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}