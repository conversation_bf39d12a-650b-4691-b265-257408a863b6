{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\CountDown\\CountDown.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\CountDown\\CountDown.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function fixedZero(val) {\n  return val * 1 < 10 ? \"0\".concat(val) : val;\n}\nexport default {\n  name: \"CountDown\",\n  props: {\n    format: {\n      type: Function,\n      default: undefined\n    },\n    target: {\n      type: [Date, Number],\n      required: true\n    },\n    onEnd: {\n      type: Function,\n      default: function _default() {}\n    }\n  },\n  data: function data() {\n    return {\n      dateTime: '0',\n      originTargetTime: 0,\n      lastTime: 0,\n      timer: 0,\n      interval: 1000\n    };\n  },\n  filters: {\n    format: function format(time) {\n      var hours = 60 * 60 * 1000;\n      var minutes = 60 * 1000;\n      var h = Math.floor(time / hours);\n      var m = Math.floor((time - h * hours) / minutes);\n      var s = Math.floor((time - h * hours - m * minutes) / 1000);\n      return \"\".concat(fixedZero(h), \":\").concat(fixedZero(m), \":\").concat(fixedZero(s));\n    }\n  },\n  created: function created() {\n    this.initTime();\n    this.tick();\n  },\n  methods: {\n    initTime: function initTime() {\n      var lastTime = 0;\n      var targetTime = 0;\n      this.originTargetTime = this.target;\n      try {\n        if (Object.prototype.toString.call(this.target) === '[object Date]') {\n          targetTime = this.target;\n        } else {\n          targetTime = new Date(this.target).getTime();\n        }\n      } catch (e) {\n        throw new Error('invalid target prop');\n      }\n      lastTime = targetTime - new Date().getTime();\n      this.lastTime = lastTime < 0 ? 0 : lastTime;\n    },\n    tick: function tick() {\n      var _this = this;\n      var onEnd = this.onEnd;\n      this.timer = setTimeout(function () {\n        if (_this.lastTime < _this.interval) {\n          clearTimeout(_this.timer);\n          _this.lastTime = 0;\n          if (typeof onEnd === 'function') {\n            onEnd();\n          }\n        } else {\n          _this.lastTime -= _this.interval;\n          _this.tick();\n        }\n      }, this.interval);\n    }\n  },\n  beforeUpdate: function beforeUpdate() {\n    if (this.originTargetTime !== this.target) {\n      this.initTime();\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    clearTimeout(this.timer);\n  }\n};", {"version": 3, "names": ["fixedZero", "val", "concat", "name", "props", "format", "type", "Function", "default", "undefined", "target", "Date", "Number", "required", "onEnd", "_default", "data", "dateTime", "originTargetTime", "lastTime", "timer", "interval", "filters", "time", "hours", "minutes", "h", "Math", "floor", "m", "s", "created", "initTime", "tick", "methods", "targetTime", "Object", "prototype", "toString", "call", "getTime", "e", "Error", "_this", "setTimeout", "clearTimeout", "beforeUpdate", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/CountDown/CountDown.vue"], "sourcesContent": ["<template>\n  <span>\n    {{ lastTime | format }}\n  </span>\n</template>\n\n<script>\n\n  function fixedZero(val) {\n    return val * 1 < 10 ? `0${val}` : val;\n  }\n\n  export default {\n    name: \"CountDown\",\n    props: {\n      format: {\n        type: Function,\n        default: undefined\n      },\n      target: {\n        type: [Date, Number],\n        required: true,\n      },\n      onEnd: {\n        type: Function,\n        default: () => {\n        }\n      }\n    },\n    data() {\n      return {\n        dateTime: '0',\n        originTargetTime: 0,\n        lastTime: 0,\n        timer: 0,\n        interval: 1000\n      }\n    },\n    filters: {\n      format(time) {\n        const hours = 60 * 60 * 1000;\n        const minutes = 60 * 1000;\n\n        const h = Math.floor(time / hours);\n        const m = Math.floor((time - h * hours) / minutes);\n        const s = Math.floor((time - h * hours - m * minutes) / 1000);\n        return `${fixedZero(h)}:${fixedZero(m)}:${fixedZero(s)}`\n      }\n    },\n    created() {\n      this.initTime()\n      this.tick()\n    },\n    methods: {\n      initTime() {\n        let lastTime = 0;\n        let targetTime = 0;\n        this.originTargetTime = this.target\n        try {\n          if (Object.prototype.toString.call(this.target) === '[object Date]') {\n            targetTime = this.target\n          } else {\n            targetTime = new Date(this.target).getTime()\n          }\n        } catch (e) {\n          throw new Error('invalid target prop')\n        }\n\n        lastTime = targetTime - new Date().getTime();\n\n        this.lastTime = lastTime < 0 ? 0 : lastTime\n      },\n      tick() {\n        const {onEnd} = this\n\n        this.timer = setTimeout(() => {\n          if (this.lastTime < this.interval) {\n            clearTimeout(this.timer)\n            this.lastTime = 0\n            if (typeof onEnd === 'function') {\n              onEnd();\n            }\n          } else {\n            this.lastTime -= this.interval\n            this.tick()\n          }\n        }, this.interval)\n      }\n    },\n    beforeUpdate () {\n      if (this.originTargetTime !== this.target) {\n        this.initTime()\n      }\n    },\n    beforeDestroy() {\n      clearTimeout(this.timer)\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAQA,SAAAA,UAAAC,GAAA;EACA,OAAAA,GAAA,gBAAAC,MAAA,CAAAD,GAAA,IAAAA,GAAA;AACA;AAEA;EACAE,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,QAAA;MACAC,OAAA,EAAAC;IACA;IACAC,MAAA;MACAJ,IAAA,GAAAK,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,KAAA;MACAR,IAAA,EAAAC,QAAA;MACAC,OAAA,WAAAO,SAAA,GACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACAjB,MAAA,WAAAA,OAAAkB,IAAA;MACA,IAAAC,KAAA;MACA,IAAAC,OAAA;MAEA,IAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,IAAA,GAAAC,KAAA;MACA,IAAAK,CAAA,GAAAF,IAAA,CAAAC,KAAA,EAAAL,IAAA,GAAAG,CAAA,GAAAF,KAAA,IAAAC,OAAA;MACA,IAAAK,CAAA,GAAAH,IAAA,CAAAC,KAAA,EAAAL,IAAA,GAAAG,CAAA,GAAAF,KAAA,GAAAK,CAAA,GAAAJ,OAAA;MACA,UAAAvB,MAAA,CAAAF,SAAA,CAAA0B,CAAA,QAAAxB,MAAA,CAAAF,SAAA,CAAA6B,CAAA,QAAA3B,MAAA,CAAAF,SAAA,CAAA8B,CAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAF,QAAA,WAAAA,SAAA;MACA,IAAAb,QAAA;MACA,IAAAgB,UAAA;MACA,KAAAjB,gBAAA,QAAAR,MAAA;MACA;QACA,IAAA0B,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,MAAA7B,MAAA;UACAyB,UAAA,QAAAzB,MAAA;QACA;UACAyB,UAAA,OAAAxB,IAAA,MAAAD,MAAA,EAAA8B,OAAA;QACA;MACA,SAAAC,CAAA;QACA,UAAAC,KAAA;MACA;MAEAvB,QAAA,GAAAgB,UAAA,OAAAxB,IAAA,GAAA6B,OAAA;MAEA,KAAArB,QAAA,GAAAA,QAAA,WAAAA,QAAA;IACA;IACAc,IAAA,WAAAA,KAAA;MAAA,IAAAU,KAAA;MACA,IAAA7B,KAAA,QAAAA,KAAA;MAEA,KAAAM,KAAA,GAAAwB,UAAA;QACA,IAAAD,KAAA,CAAAxB,QAAA,GAAAwB,KAAA,CAAAtB,QAAA;UACAwB,YAAA,CAAAF,KAAA,CAAAvB,KAAA;UACAuB,KAAA,CAAAxB,QAAA;UACA,WAAAL,KAAA;YACAA,KAAA;UACA;QACA;UACA6B,KAAA,CAAAxB,QAAA,IAAAwB,KAAA,CAAAtB,QAAA;UACAsB,KAAA,CAAAV,IAAA;QACA;MACA,QAAAZ,QAAA;IACA;EACA;EACAyB,YAAA,WAAAA,aAAA;IACA,SAAA5B,gBAAA,UAAAR,MAAA;MACA,KAAAsB,QAAA;IACA;EACA;EACAe,aAAA,WAAAA,cAAA;IACAF,YAAA,MAAAzB,KAAA;EACA;AACA", "ignoreList": []}]}