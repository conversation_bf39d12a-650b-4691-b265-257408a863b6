{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexBdc.vue?vue&type=template&id=51655204&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexBdc.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"page-header-index-wide\">\n  <a-row :gutter=\"24\">\n    <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n      <chart-card :loading=\"loading\" title=\"受理量\" :total=\"cardCount.sll | NumberFormat\">\n        <a-tooltip title=\"指标说明\" slot=\"action\">\n          <a-icon type=\"info-circle-o\" />\n        </a-tooltip>\n        <div>\n          <mini-area :datasource=\"chartData.sll\" />\n        </div>\n        <template slot=\"footer\">今日受理量：<span>{{ todaySll }}</span></template>\n      </chart-card>\n    </a-col>\n    <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n      <chart-card :loading=\"loading\" title=\"办结量\" :total=\"cardCount.bjl | NumberFormat\">\n        <a-tooltip title=\"指标说明\" slot=\"action\">\n          <a-icon type=\"info-circle-o\" />\n        </a-tooltip>\n        <div>\n          <mini-area :datasource=\"chartData.bjl\"/>\n        </div>\n        <template slot=\"footer\">今日办结量：<span>{{ todayBjl }}</span></template>\n      </chart-card>\n    </a-col>\n    <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n      <chart-card :loading=\"loading\" title=\"用户受理量\" :total=\"cardCount.isll | NumberFormat\">\n        <a-tooltip title=\"指标说明\" slot=\"action\">\n          <a-icon type=\"info-circle-o\" />\n        </a-tooltip>\n        <div>\n          <mini-bar :datasource=\"chartData.isll\" :height=\"50\"/>\n        </div>\n        <template slot=\"footer\">用户今日受理量：<span>{{ todayISll }}</span></template>\n      </chart-card>\n    </a-col>\n    <a-col :sm=\"24\" :md=\"12\" :xl=\"6\" :style=\"{ marginBottom: '24px' }\">\n      <chart-card :loading=\"loading\" title=\"用户办结量\" :total=\"cardCount.ibjl | NumberFormat\">\n        <a-tooltip title=\"指标说明\" slot=\"action\">\n          <a-icon type=\"info-circle-o\" />\n        </a-tooltip>\n        <div>\n          <mini-bar :datasource=\"chartData.ibjl\" :height=\"50\"/>\n        </div>\n        <template slot=\"footer\">用户今日办结量：<span>{{ todayIBjl }}</span></template>\n      </chart-card>\n    </a-col>\n  </a-row>\n\n  <a-card :loading=\"loading\" :bordered=\"false\" :body-style=\"{padding: '0'}\">\n    <div class=\"salesCard\">\n      <a-tabs default-active-key=\"1\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n        <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n          <div class=\"extra-item\">\n            <a>今日</a>\n            <a>本周</a>\n            <a>本月</a>\n            <a>本年</a>\n          </div>\n          <a-range-picker :style=\"{width: '256px'}\" />\n        </div>\n\n        <a-tab-pane loading=\"true\" tab=\"受理监管\" key=\"1\">\n          <a-row>\n            <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n              <index-bar title=\"受理量统计\" />\n            </a-col>\n            <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n              <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                <div class=\"item-group\">\n                  <a-row>\n                    <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                      <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                    </a-col>\n                  </a-row>\n                </div>\n              </a-card>\n\n            </a-col>\n          </a-row>\n        </a-tab-pane>\n\n        <a-tab-pane tab=\"交互监管\" key=\"2\">\n          <a-row>\n            <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n              <bar-multid :sourceData=\"jhjgData\" :fields=\"jhjgFields\" title=\"平台与部门交互量统计\"></bar-multid>\n            </a-col>\n            <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n              <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                <div class=\"item-group\">\n                  <a-row>\n                    <a-col :class=\"'more-btn'\" :span=\"12\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                      <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                    </a-col>\n                  </a-row>\n                </div>\n              </a-card>\n\n            </a-col>\n          </a-row>\n        </a-tab-pane>\n\n        <a-tab-pane tab=\"存储监管\" key=\"4\">\n          <a-row>\n            <a-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n              <a-row>\n                <template v-if=\"diskInfo && diskInfo.length>0\">\n                  <a-col :span=\"12\" v-for=\"(item,index) in diskInfo\" :key=\" 'diskInfo'+index \">\n                    <dash-chart-demo :title=\"item.name\" :datasource=\"item.restPPT\"></dash-chart-demo>\n                  </a-col>\n                </template>\n              </a-row>\n            </a-col>\n\n            <a-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\n\n              <a-card title=\"快速开始 / 便捷导航\" style=\"margin-bottom: 24px\" :bordered=\"false\" :body-style=\"{padding: 0}\">\n                <div class=\"item-group\">\n                  <a-row>\n                    <a-col :class=\"'more-btn'\" :span=\"10\" v-for=\"(item,index) in registerTypeList\" :key=\" 'registerType'+index \">\n                      <a-button @click=\"goPage(index)\" style=\"margin-bottom:10px\" size=\"small\" type=\"primary\" ghost>{{ item.text }}</a-button>\n                    </a-col>\n                  </a-row>\n                </div>\n              </a-card>\n\n            </a-col>\n          </a-row>\n        </a-tab-pane>\n\n      </a-tabs>\n\n    </div>\n  </a-card>\n\n  <a-row :gutter=\"12\">\n    <a-card :loading=\"loading\" :class=\"{ 'anty-list-cust':true }\" :bordered=\"false\" :style=\"{ marginTop: '24px' }\">\n\n      <a-tabs v-model=\"indexBottomTab\" size=\"large\" :tab-bar-style=\"{marginBottom: '24px', paddingLeft: '16px'}\">\n        <div class=\"extra-wrapper\" slot=\"tabBarExtraContent\">\n          <a-radio-group v-model=\"indexRegisterType\" @change=\"changeRegisterType\">\n            <a-radio-button value=\"转移登记\">转移登记</a-radio-button>\n            <a-radio-button value=\"抵押登记\">抵押登记</a-radio-button>\n            <a-radio-button value=\"\">所有</a-radio-button>\n          </a-radio-group>\n        </div>\n\n        <a-tab-pane loading=\"true\" tab=\"业务流程限时监管\" key=\"1\">\n\n          <a-table :dataSource=\"dataSource1\" size=\"default\" rowKey=\"id\" :columns=\"columns\" :pagination=\"ipagination1\" @change=\"tableChange1\">\n            <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n              <a-progress :strokeColor=\"getPercentColor(record.flowRate)\" :format=\"getPercentFormat\" :percent=\"getFlowRateNumber(record.flowRate)\" style=\"width:80px\" />\n            </template>\n          </a-table>\n        </a-tab-pane>\n\n        <a-tab-pane loading=\"true\" tab=\"业务节点限时监管\" key=\"2\">\n          <a-table :dataSource=\"dataSource2\" size=\"default\" rowKey=\"id\" :columns=\"columns2\" :pagination=\"ipagination2\" @change=\"tableChange2\">\n            <template slot=\"flowRate\" slot-scope=\"text, record, index\">\n              <span style=\"color: red;\">{{ record.flowRate }}小时</span>\n            </template>\n          </a-table>\n        </a-tab-pane>\n\n      </a-tabs>\n\n\n    </a-card>\n  </a-row>\n\n</div>\n", null]}