{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\search\\SearchLayout.vue?vue&type=style&index=0&id=22be917c&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\search\\SearchLayout.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.search-head{\n  background-color: #fff;\n  margin: -25px -24px -24px;\n  .search-input{\n    text-align: center;\n    margin-bottom: 16px;\n  }\n}\n.search-content{\n  margin-top: 48px;\n}\n", {"version": 3, "sources": ["SearchLayout.vue"], "names": [], "mappings": ";AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SearchLayout.vue", "sourceRoot": "src/views/list/search", "sourcesContent": ["<template>\n  <div class=\"search-content\">\n    <router-view />\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"SearchLayout\",\n    data () {\n      return {\n        tabs: {\n          items: [\n            {\n              key: '1',\n              title: '文章'\n            },\n            {\n              key: '2',\n              title: '项目'\n            },\n            {\n              key: '3',\n              title: '应用'\n            },\n          ],\n          active: () => {\n            switch (this.$route.path) {\n              case '/list/search/article':\n                return '1'\n              case '/list/search/project':\n                return '2'\n              case '/list/search/application':\n                return '3'\n              default:\n                return '1'\n            }\n          },\n          callback: (key) => {\n            switch (key) {\n              case '1':\n                this.$router.push('/list/search/article')\n                break\n              case '2':\n                this.$router.push('/list/search/project')\n                break\n              case '3':\n                this.$router.push('/list/search/application')\n                break\n              default:\n                this.$router.push('/workplace')\n            }\n          }\n        },\n        search: true\n      }\n    },\n    computed: {\n\n    },\n    methods: {\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .search-head{\n    background-color: #fff;\n    margin: -25px -24px -24px;\n    .search-input{\n      text-align: center;\n      margin-bottom: 16px;\n    }\n  }\n  .search-content{\n    margin-top: 48px;\n  }\n</style>"]}]}