{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JAreaLinkage.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { pcaa } from 'area-data';\nimport Area from '@/components/_util/Area';\nexport default {\n  name: 'JAreaLinkage',\n  props: {\n    value: {\n      type: String,\n      required: false\n    },\n    // 组件的类型，可选值：\n    // select 下拉样式\n    // cascader 级联样式（默认）\n    type: {\n      type: String,\n      default: 'cascader'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    }\n  },\n  data: function data() {\n    return {\n      pcaa: pcaa,\n      innerValue: [],\n      usedListeners: ['change'],\n      enums: {\n        type: ['cascader', 'select']\n      },\n      reloading: false,\n      areaData: ''\n    };\n  },\n  computed: {\n    _listeners: function _listeners() {\n      var listeners = _objectSpread({}, this.$listeners);\n      // 去掉已使用的事件，防止冲突\n      this.usedListeners.forEach(function (key) {\n        delete listeners[key];\n      });\n      return listeners;\n    },\n    _type: function _type() {\n      if (this.enums.type.includes(this.type)) {\n        return this.type;\n      } else {\n        console.error(\"JAreaLinkage\\u7684type\\u5C5E\\u6027\\u53EA\\u80FD\\u63A5\\u6536\\u6307\\u5B9A\\u7684\\u503C\\uFF08\".concat(this.enums.type.join('|'), \"\\uFF09\"));\n        return this.enums.type[0];\n      }\n    }\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler: function handler() {\n        this.loadDataByValue(this.value);\n      }\n    }\n  },\n  created: function created() {\n    this.initAreaData();\n  },\n  methods: {\n    /** 通过 value 反推 options */loadDataByValue: function loadDataByValue(value) {\n      var _this = this;\n      if (!value || value.length == 0) {\n        this.innerValue = [];\n        this.reloading = true;\n        setTimeout(function () {\n          _this.reloading = false;\n        }, 100);\n      } else {\n        this.initAreaData();\n        var arr = this.areaData.getRealCode(value);\n        this.innerValue = arr;\n      }\n    },\n    /** 通过地区code获取子级 */loadDataByCode: function loadDataByCode(value) {\n      var options = [];\n      var data = pcaa[value];\n      if (data) {\n        for (var key in data) {\n          if (data.hasOwnProperty(key)) {\n            options.push({\n              value: key,\n              label: data[key]\n            });\n          }\n        }\n        return options;\n      } else {\n        return [];\n      }\n    },\n    /** 判断是否有子节点 */hasChildren: function hasChildren(options) {\n      var _this2 = this;\n      options.forEach(function (option) {\n        var data = _this2.loadDataByCode(option.value);\n        option.isLeaf = data.length === 0;\n      });\n    },\n    handleChange: function handleChange(values) {\n      var value = values[values.length - 1];\n      this.$emit('change', value);\n    },\n    initAreaData: function initAreaData() {\n      if (!this.areaData) {\n        this.areaData = new Area();\n      }\n    }\n  },\n  model: {\n    prop: 'value',\n    event: 'change'\n  }\n};", {"version": 3, "names": ["pcaa", "Area", "name", "props", "value", "type", "String", "required", "default", "width", "data", "innerValue", "usedListeners", "enums", "reloading", "areaData", "computed", "_listeners", "listeners", "_objectSpread", "$listeners", "for<PERSON>ach", "key", "_type", "includes", "console", "error", "concat", "join", "watch", "immediate", "handler", "loadDataByValue", "created", "initAreaData", "methods", "_this", "length", "setTimeout", "arr", "getRealCode", "loadDataByCode", "options", "hasOwnProperty", "push", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this2", "option", "<PERSON><PERSON><PERSON><PERSON>", "handleChange", "values", "$emit", "model", "prop", "event"], "sources": ["src/components/jeecg/JAreaLinkage.vue"], "sourcesContent": ["<template>\n  <div v-if=\"!reloading\" class=\"j-area-linkage\">\n    <area-cascader\n      v-if=\"_type === enums.type[0]\"\n      :value=\"innerValue\"\n      :data=\"pcaa\"\n      :level=\"1\"\n      :style=\"{width}\"\n      v-bind=\"$attrs\"\n      v-on=\"_listeners\"\n      @change=\"handleChange\"\n    />\n    <area-select\n      v-else-if=\"_type === enums.type[1]\"\n      :value=\"innerValue\"\n      :data=\"pcaa\"\n      :level=\"2\"\n      v-bind=\"$attrs\"\n      v-on=\"_listeners\"\n      @change=\"handleChange\"\n    />\n    <div v-else>\n      <span style=\"color:red;\"> Bad type value: {{_type}}</span>\n    </div>\n  </div>\n</template>\n\n<script>\n  import { pcaa } from 'area-data'\n  import Area from '@/components/_util/Area'\n\n  export default {\n    name: 'JAreaLinkage',\n    props: {\n      value: {\n        type: String,\n        required:false\n      },\n      // 组件的类型，可选值：\n      // select 下拉样式\n      // cascader 级联样式（默认）\n      type: {\n        type: String,\n        default: 'cascader'\n      },\n      width: {\n        type: String,\n        default: '100%'\n      }\n    },\n    data() {\n      return {\n        pcaa,\n        innerValue: [],\n        usedListeners: ['change'],\n        enums: {\n          type: ['cascader', 'select']\n        },\n        reloading: false,\n        areaData:''\n      }\n    },\n    computed: {\n      _listeners() {\n        let listeners = { ...this.$listeners }\n        // 去掉已使用的事件，防止冲突\n        this.usedListeners.forEach(key => {\n          delete listeners[key]\n        })\n        return listeners\n      },\n      _type() {\n        if (this.enums.type.includes(this.type)) {\n          return this.type\n        } else {\n          console.error(`JAreaLinkage的type属性只能接收指定的值（${this.enums.type.join('|')}）`)\n          return this.enums.type[0]\n        }\n      },\n    },\n    watch: {\n      value: {\n        immediate: true,\n        handler() {\n          this.loadDataByValue(this.value)\n        }\n      },\n    },\n    created() {\n      this.initAreaData();\n    },\n    methods: {\n      /** 通过 value 反推 options */\n      loadDataByValue(value) {\n        if(!value || value.length==0){\n          this.innerValue = []\n          this.reloading = true;\n          setTimeout(()=>{\n            this.reloading = false\n          },100)\n        }else{\n          this.initAreaData();\n          let arr = this.areaData.getRealCode(value);\n          this.innerValue = arr\n        }\n      },\n      /** 通过地区code获取子级 */\n      loadDataByCode(value) {\n        let options = []\n        let data = pcaa[value]\n        if (data) {\n          for (let key in data) {\n            if (data.hasOwnProperty(key)) {\n              options.push({ value: key, label: data[key], })\n            }\n          }\n          return options\n        } else {\n          return []\n        }\n      },\n      /** 判断是否有子节点 */\n      hasChildren(options) {\n        options.forEach(option => {\n          let data = this.loadDataByCode(option.value)\n          option.isLeaf = data.length === 0\n        })\n      },\n      handleChange(values) {\n        let value = values[values.length - 1]\n        this.$emit('change', value)\n      },\n      initAreaData(){\n        if(!this.areaData){\n          this.areaData = new Area();\n        }\n      },\n\n    },\n    model: { prop: 'value', event: 'change' },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .j-area-linkage {\n    height:40px;\n    /deep/ .area-cascader-wrap .area-select {\n      width: 100%;\n    }\n\n    /deep/ .area-select .area-selected-trigger {\n      line-height: 1.15;\n    }\n  }\n\n</style>"], "mappings": ";;;;;;AA4BA,SAAAA,IAAA;AACA,OAAAC,IAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACA;IACA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAV,IAAA,EAAAA,IAAA;MACAW,UAAA;MACAC,aAAA;MACAC,KAAA;QACAR,IAAA;MACA;MACAS,SAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,SAAA,GAAAC,aAAA,UAAAC,UAAA;MACA;MACA,KAAAR,aAAA,CAAAS,OAAA,WAAAC,GAAA;QACA,OAAAJ,SAAA,CAAAI,GAAA;MACA;MACA,OAAAJ,SAAA;IACA;IACAK,KAAA,WAAAA,MAAA;MACA,SAAAV,KAAA,CAAAR,IAAA,CAAAmB,QAAA,MAAAnB,IAAA;QACA,YAAAA,IAAA;MACA;QACAoB,OAAA,CAAAC,KAAA,4FAAAC,MAAA,MAAAd,KAAA,CAAAR,IAAA,CAAAuB,IAAA;QACA,YAAAf,KAAA,CAAAR,IAAA;MACA;IACA;EACA;EACAwB,KAAA;IACAzB,KAAA;MACA0B,SAAA;MACAC,OAAA,WAAAA,QAAA;QACA,KAAAC,eAAA,MAAA5B,KAAA;MACA;IACA;EACA;EACA6B,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACA,0BACAH,eAAA,WAAAA,gBAAA5B,KAAA;MAAA,IAAAgC,KAAA;MACA,KAAAhC,KAAA,IAAAA,KAAA,CAAAiC,MAAA;QACA,KAAA1B,UAAA;QACA,KAAAG,SAAA;QACAwB,UAAA;UACAF,KAAA,CAAAtB,SAAA;QACA;MACA;QACA,KAAAoB,YAAA;QACA,IAAAK,GAAA,QAAAxB,QAAA,CAAAyB,WAAA,CAAApC,KAAA;QACA,KAAAO,UAAA,GAAA4B,GAAA;MACA;IACA;IACA,mBACAE,cAAA,WAAAA,eAAArC,KAAA;MACA,IAAAsC,OAAA;MACA,IAAAhC,IAAA,GAAAV,IAAA,CAAAI,KAAA;MACA,IAAAM,IAAA;QACA,SAAAY,GAAA,IAAAZ,IAAA;UACA,IAAAA,IAAA,CAAAiC,cAAA,CAAArB,GAAA;YACAoB,OAAA,CAAAE,IAAA;cAAAxC,KAAA,EAAAkB,GAAA;cAAAuB,KAAA,EAAAnC,IAAA,CAAAY,GAAA;YAAA;UACA;QACA;QACA,OAAAoB,OAAA;MACA;QACA;MACA;IACA;IACA,eACAI,WAAA,WAAAA,YAAAJ,OAAA;MAAA,IAAAK,MAAA;MACAL,OAAA,CAAArB,OAAA,WAAA2B,MAAA;QACA,IAAAtC,IAAA,GAAAqC,MAAA,CAAAN,cAAA,CAAAO,MAAA,CAAA5C,KAAA;QACA4C,MAAA,CAAAC,MAAA,GAAAvC,IAAA,CAAA2B,MAAA;MACA;IACA;IACAa,YAAA,WAAAA,aAAAC,MAAA;MACA,IAAA/C,KAAA,GAAA+C,MAAA,CAAAA,MAAA,CAAAd,MAAA;MACA,KAAAe,KAAA,WAAAhD,KAAA;IACA;IACA8B,YAAA,WAAAA,aAAA;MACA,UAAAnB,QAAA;QACA,KAAAA,QAAA,OAAAd,IAAA;MACA;IACA;EAEA;EACAoD,KAAA;IAAAC,IAAA;IAAAC,KAAA;EAAA;AACA", "ignoreList": []}]}