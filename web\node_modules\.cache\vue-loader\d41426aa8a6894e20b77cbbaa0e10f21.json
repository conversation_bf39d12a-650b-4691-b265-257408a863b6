{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue?vue&type=style&index=0&id=4083ec1a&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\GameCenter.vue", "mtime": 1750046294782}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.game-center {\n  font-family: 'Comic Sans MS', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;\n  background: linear-gradient(135deg, #7579ff, #b224ef);\n  background-attachment: fixed;\n  color: #333;\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 添加背景装饰元素 */\n.game-center::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.2) 5px, transparent 6px),\n    radial-gradient(circle at 30% 60%, rgba(255,255,255,0.2) 8px, transparent 9px),\n    radial-gradient(circle at 70% 40%, rgba(255,255,255,0.2) 10px, transparent 11px),\n    radial-gradient(circle at 90% 80%, rgba(255,255,255,0.2) 7px, transparent 8px);\n  background-size: 300px 300px;\n  z-index: 0;\n  animation: floatingBubbles 30s infinite linear;\n}\n\n@keyframes floatingBubbles {\n  0% { background-position: 0 0; }\n  100% { background-position: 300px 300px; }\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  position: relative;\n  z-index: 1;\n}\n\nheader {\n  background: linear-gradient(135deg, #4e54c8, #8f94fb);\n  color: white;\n  padding: 25px;\n  text-align: center;\n  border-radius: 20px;\n  margin-bottom: 30px;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 4px solid #ffffff;\n}\n\n/* 添加彩色边框动画 */\n@keyframes borderAnimation {\n  0% { border-color: #ff9a9e; }\n  25% { border-color: #fad0c4; }\n  50% { border-color: #a1c4fd; }\n  75% { border-color: #c2e9fb; }\n  100% { border-color: #ff9a9e; }\n}\n\nheader:hover {\n  transform: translateY(-5px);\n  animation: borderAnimation 3s infinite;\n}\n\n/* 添加装饰元素 */\nheader::before {\n  content: '🎮';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  top: -15px;\n  left: 20px;\n  transform: rotate(-15deg);\n}\n\nheader::after {\n  content: '🎯';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  bottom: -15px;\n  right: 20px;\n  transform: rotate(15deg);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.title-area {\n  text-align: left;\n}\n\nh1 {\n  font-size: 2.8rem;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  font-weight: bold;\n  color: #fff;\n  letter-spacing: 1px;\n  position: relative;\n  display: inline-block;\n}\n\n/* 添加标题装饰 */\nh1::after {\n  content: '';\n  position: absolute;\n  bottom: -5px;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(90deg, #ff9a9e, #fad0c4, #a1c4fd);\n  border-radius: 10px;\n}\n\n.subtitle {\n  font-size: 1.4rem;\n  font-weight: 300;\n  color: #f0f0f0;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.3);\n  padding: 10px 15px;\n  border-radius: 30px;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n}\n\n.user-avatar {\n  width: 45px;\n  height: 45px;\n  border-radius: 50%;\n  border: 3px solid #fff;\n  margin-right: 12px;\n  object-fit: cover;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n\n.user-name {\n  font-weight: bold;\n  margin-right: 15px;\n  color: #fff;\n  font-size: 1.1rem;\n}\n\n.coin-display {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #ffd86f, #fc6076);\n  padding: 8px 15px;\n  border-radius: 20px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n  transition: all 0.3s ease;\n}\n\n.coin-display:hover {\n  transform: scale(1.05);\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 8px;\n  animation: spin 10s infinite linear;\n}\n\n@keyframes spin {\n  0% { transform: rotateY(0deg); }\n  100% { transform: rotateY(360deg); }\n}\n\n.coin-amount {\n  font-weight: bold;\n  font-size: 18px;\n  color: #fff;\n}\n\n.game-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 25px;\n  margin-top: 30px;\n}\n\nfooter {\n  margin-top: 50px;\n  text-align: center;\n  color: #fff;\n  padding: 20px 0;\n  border-top: 2px dashed rgba(255, 255, 255, 0.3);\n  font-size: 1rem;\n  position: relative;\n}\n\n/* 添加页脚装饰 */\nfooter::before {\n  content: '🚀';\n  position: absolute;\n  left: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\nfooter::after {\n  content: '🌟';\n  position: absolute;\n  right: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n  }\n  \n  .title-area {\n    margin-bottom: 15px;\n    text-align: center;\n  }\n  \n  .user-info {\n    width: 100%;\n    justify-content: center;\n    margin-top: 15px;\n  }\n  \n  .game-grid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  }\n  \n  h1 {\n    font-size: 2.2rem;\n  }\n  \n  .subtitle {\n    font-size: 1.2rem;\n  }\n}\n\n/* 游戏图片样式 */\n.game-image-bg {\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n/* 添加动画效果 */\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n  100% { transform: translateY(0px); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n", {"version": 3, "sources": ["GameCenter.vue"], "names": [], "mappings": ";AAgoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "GameCenter.vue", "sourceRoot": "src/views/game", "sourcesContent": ["<template>\n  <div class=\"game-center\">\n    <div class=\"container\">\n      <header>\n        <div class=\"header-content\">\n          <div class=\"title-area\">\n            <h1>休闲游戏中心</h1>\n            <p class=\"subtitle\">放松心情，享受游戏乐趣</p>\n          </div>\n          <div class=\"user-info\">\n            <img :src=\"userAvatar\" alt=\"用户头像\" class=\"user-avatar\"/>\n            <span class=\"user-name\">{{ userName }}</span>\n            <div class=\"coin-display\">\n              <span class=\"coin-icon\"></span>\n              <span class=\"coin-amount\">{{ userCoins }}</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div class=\"game-grid\">\n        <!-- 游戏卡片列表 -->\n        <game-card\n          v-for=\"game in games\"\n          :key=\"game.id\"\n          :game=\"game\"\n          @play-game=\"openGameModal\"\n        />\n      </div>\n\n      <footer>\n        <p>&copy; 2025 休闲游戏中心 | CFish科技少儿编程</p>\n      </footer>\n    </div>\n\n    <!-- 游戏模态窗口 -->\n    <game-modal\n      v-if=\"showModal\"\n      :game=\"currentGame\"\n      :user-coins=\"userCoins\"\n      :user-name=\"userName\"\n      :user-avatar=\"userAvatar\"\n      @close=\"closeGameModal\"\n      @consume-coins=\"consumeCoins\"\n    />\n\n    <!-- 确认弹窗 -->\n    <confirm-dialog\n      v-if=\"showConfirmDialog\"\n      :message=\"confirmMessage\"\n      :game-cost=\"currentGame ? currentGame.cost : 0\"\n      @confirm=\"handleConfirm\"\n      @cancel=\"showConfirmDialog = false\"\n    />\n  </div>\n</template>\n\n<script>\nimport GameCard from './components/GameCard.vue'\nimport GameModal from './components/GameModal.vue'\nimport ConfirmDialog from './components/ConfirmDialog.vue'\nimport { getAction, postAction, getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'GameCenter',\n  components: {\n    GameCard,\n    GameModal,\n    ConfirmDialog\n  },\n  data() {\n    return {\n      // 用户信息\n      userName: '账号名',\n      userAvatar: '/logo.png',\n      // 用户金币数量\n      userCoins: 0,\n      // 游戏列表\n      games: [\n        {\n          id: 1,\n          title: '合并与挖掘',\n          description: '将相同的方块合并升级，并挖掘获取资源，解锁更多有趣的内容。',\n          imageUrl: this.getGameImageUrl('merge-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/merge-dig',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《合并与挖掘》是一款结合了合并元素和挖矿元素的休闲游戏。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击相同的方块可以将它们合并为更高级的方块</li>\n              <li>使用挖掘工具可以获取矿石和宝石</li>\n              <li>通过合并升级工具，提高挖掘效率</li>\n              <li>收集资源来解锁新的区域和内容</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>优先升级挖掘工具，提高每次挖掘的效率。合理规划方块摆放位置，为后续合并腾出空间。尽可能完成任务目标获取额外奖励。</p>\n          `\n        },\n        {\n          id: 2,\n          title: '涂鸦之路',\n          description: '控制小球沿着不断变化的赛道前进，避开障碍物并收集硬币，享受刺激的奔跑体验。',\n          imageUrl: this.getGameImageUrl('doodle-road-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/doodle-road',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《涂鸦之路》是一款需要玩家在不断变换的道路上控制小车前进的休闲游戏。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击屏幕绘制道路，让小车能够行驶前进</li>\n              <li>避免小车掉出赛道或撞到障碍物</li>\n              <li>收集路上的金币以获得更高分数</li>\n              <li>通过关卡后可解锁更多内容</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>提前规划路线，注意观察前方的障碍物和陷阱。尽量画出平稳的线路避免急转弯。收集金币时注意不要为了金币而冒险走高难度路线。</p>\n          `\n        },\n        {\n          id: 3,\n          title: '极限射击3D',\n          description: '在射击训练场体验各种枪械射击的乐趣，瞄准目标，提高射击精准度，享受沉浸式射击体验。',\n          imageUrl: this.getGameImageUrl('the-range-3d-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/the-range-3d',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《极限射击3D》是一款第一人称射击靶场模拟游戏，让玩家体验各种枪械射击的乐趣。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>使用鼠标瞄准目标</li>\n              <li>点击鼠标左键射击</li>\n              <li>R键重新装弹</li>\n              <li>完成射击挑战以获取积分和解锁新武器</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>保持稳定的瞄准姿态，学习控制后坐力。每把武器都有自己的特点，花时间熟悉不同武器的射击感觉。在射击前深呼吸以提高精准度。</p>\n          `\n        },\n        {\n          id: 4,\n          title: '篮球明星',\n          description: '体验精彩的篮球对决，选择你喜欢的球员，投篮得分并赢得比赛，成为真正的篮球明星。',\n          imageUrl: this.getGameImageUrl('basketball-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/basketball-superstars',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《篮球明星》是一款让你成为篮球巨星的体育游戏，通过投篮比赛让你体验篮球的乐趣。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>点击并拖动屏幕来瞄准篮筐</li>\n              <li>松开手指投球</li>\n              <li>控制力度和角度以提高投篮成功率</li>\n              <li>完成挑战赚取积分并解锁新球员和场地</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>注意投篮力度和角度的掌握，根据距离调整投篮强度。不同的球员有不同的特长，选择适合自己风格的球员。利用练习模式熟悉投篮手感。</p>\n          `\n        },\n        {\n          id: 5,\n          title: '黄金矿工',\n          description: '操作采矿钩索，抓取金块和宝石，躲避石头和炸弹，在限定时间内完成采矿目标。',\n          imageUrl: this.getGameImageUrl('gold-miner-game.png'),\n          cost: 10,\n          costDescription: '10金币/10分钟',\n          url: 'https://www.crazygames.com/embed/gold-miner',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《黄金矿工》是一款经典的挖矿游戏，玩家需要控制采矿钩索抓取金块和宝石。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>按下键盘或点击屏幕发射钩索</li>\n              <li>钩索会自动摆动，需要把握时机发射</li>\n              <li>钩子会抓住触碰到的物体并将其拉回</li>\n              <li>不同物品价值不同，金块和宝石价值高</li>\n              <li>在规定时间内达到目标金额才能进入下一关</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>优先抓取价值高的金块和宝石。石头很重会减慢钩索收回的速度。利用道具可以提高游戏效率，比如炸药可以炸掉岩石。时间有限，请规划好每一次抓取。</p>\n          `\n        },\n        {\n          id: 6,\n          title: '跳跃小人',\n          description: '在充满障碍的关卡中奔跑和跳跃，躲避陷阱，超越对手，成为最后一个到达终点的胜利者。',\n          imageUrl: this.getGameImageUrl('jump-guys-game.png'),\n          cost: 20,\n          costDescription: '20金币/10分钟',\n          url: 'https://www.crazygames.com/embed/jump-guys',\n          tips: `\n            <h3>游戏玩法</h3>\n            <p>《跳跃小人》是一款多人竞技跑酷游戏，玩家需要操控角色通过各种障碍物到达终点。</p>\n            <h3>基本操作</h3>\n            <ul>\n              <li>使用方向键或WASD控制角色移动</li>\n              <li>按空格键或上键进行跳跃</li>\n              <li>避开障碍物并尝试推挤其他玩家</li>\n              <li>努力成为第一个到达终点的玩家</li>\n            </ul>\n            <h3>游戏技巧</h3>\n            <p>跳跃时机很重要，不要过早或过晚跳跃。记住关卡布局以便更好地应对障碍。有时候等待其他玩家先行动可以学习如何避开陷阱。利用物理碰撞可以干扰其他玩家的进度。</p>\n          `\n        }\n      ],\n      // 模态窗口显示状态\n      showModal: false,\n      // 确认对话框显示状态\n      showConfirmDialog: false,\n      // 当前选择的游戏\n      currentGame: null,\n      // 确认对话框消息\n      confirmMessage: '',\n      // 确认回调函数\n      confirmCallback: null\n    }\n  },\n  beforeMount() {\n    // 不需要在beforeMount阶段加载用户信息\n  },\n  mounted() {\n    // 在mounted阶段再次尝试加载用户信息\n    setTimeout(() => {\n      if (this.userName === '账号名') {\n        this.loadUserInfoDirectly()\n      }\n    }, 500)\n  },\n  created() {\n    // 加载用户金币数量\n    this.loadUserCoins()\n    \n    // 加载用户信息\n    this.loadUserInfoDirectly()\n  },\n  beforeDestroy() {\n    // 不需要移除事件监听\n  },\n  methods: {\n    // 尝试加载用户信息的统一方法\n    tryLoadUserInfo() {\n      console.log('尝试加载用户信息...');\n      \n      // 方法1：从Vuex获取\n      this.loadUserInfoFromVuex();\n      \n      // 方法2：从localStorage获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromLocalStorage();\n      }\n      \n      // 方法3：从API获取\n      if (this.userName === '账号名') {\n        this.loadUserInfoFromAPI();\n      }\n    },\n    \n    // 从Vuex获取用户信息\n    loadUserInfoFromVuex() {\n      console.log('从Vuex获取用户信息');\n      console.log('Vuex用户状态:', this.$store.state.user);\n      \n      try {\n        // 确保用户信息已加载\n        if (this.$store.state.user && this.$store.state.user.info) {\n          // 获取用户名\n          this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n          console.log('获取到用户名:', this.userName);\n          \n          // 获取头像路径\n          let avatar = '';\n          \n          // 先尝试从用户信息中获取头像\n          if (this.$store.state.user.info.avatar) {\n            avatar = this.$store.state.user.info.avatar;\n            console.log('从用户信息获取头像:', avatar);\n          } \n          // 再尝试从store.state.user.avatar获取\n          else if (this.$store.state.user.avatar) {\n            avatar = this.$store.state.user.avatar;\n            console.log('从store.user.avatar获取头像:', avatar);\n          }\n          // 最后使用系统默认头像\n          else if (this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n            avatar = this.$store.getters.sysConfig.avatar;\n            console.log('使用系统默认头像:', avatar);\n          }\n          \n          // 处理头像路径\n          if (avatar && avatar !== '') {\n            // 如果头像路径不是http开头，则使用getFileAccessHttpUrl处理\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('处理后的头像路径:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('使用原始头像路径:', this.userAvatar);\n            }\n          } else {\n            // 使用默认头像\n            this.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('使用默认logo头像:', this.userAvatar);\n          }\n          \n          // 如果成功获取了用户名，则不再尝试其他方法\n          if (this.userName !== '账号名') {\n            return true;\n          }\n        } else {\n          console.log('Vuex中用户信息未加载');\n        }\n      } catch (error) {\n        console.error('从Vuex获取用户信息出错:', error);\n      }\n      \n      return false;\n    },\n    \n    // 从localStorage获取用户信息\n    loadUserInfoFromLocalStorage() {\n      console.log('从localStorage获取用户信息');\n      try {\n        // 尝试从localStorage获取用户信息\n        const userInfo = localStorage.getItem('pro__user');\n        if (userInfo) {\n          const parsedUser = JSON.parse(userInfo);\n          console.log('localStorage中的用户信息:', parsedUser);\n          \n          if (parsedUser) {\n            // 获取用户名\n            if (parsedUser.realname || parsedUser.username) {\n              this.userName = parsedUser.realname || parsedUser.username;\n              console.log('从localStorage获取到用户名:', this.userName);\n            }\n            \n            // 获取头像\n            if (parsedUser.avatar) {\n              const avatar = parsedUser.avatar;\n              if (!avatar.startsWith('http')) {\n                this.userAvatar = getFileAccessHttpUrl(avatar);\n                console.log('从localStorage处理后的头像路径:', this.userAvatar);\n              } else {\n                this.userAvatar = avatar;\n                console.log('从localStorage获取的原始头像路径:', this.userAvatar);\n              }\n              \n              return true;\n            }\n          }\n        } else {\n          console.log('localStorage中没有用户信息');\n        }\n      } catch (error) {\n        console.error('从localStorage获取用户信息出错:', error);\n      }\n      \n      return false;\n    },\n    \n    // 从API获取用户信息\n    loadUserInfoFromAPI() {\n      console.log('从API获取用户信息');\n      \n      // 调用API获取用户信息\n      getAction('/sys/user/getUserInfo').then(res => {\n        if (res.success && res.result) {\n          console.log('API返回的用户信息:', res.result);\n          \n          // 获取用户名\n          if (res.result.realname || res.result.username) {\n            this.userName = res.result.realname || res.result.username;\n            console.log('从API获取到用户名:', this.userName);\n          }\n          \n          // 获取头像\n          if (res.result.avatar) {\n            const avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('从API处理后的头像路径:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('从API获取的原始头像路径:', this.userAvatar);\n            }\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(error => {\n        console.error('API获取用户信息出错:', error);\n      });\n    },\n    \n    // 加载用户信息（原方法，保留作为参考）\n    loadUserInfo() {\n      this.tryLoadUserInfo();\n    },\n    \n    // 获取游戏图片URL\n    getGameImageUrl(fileName) {\n      // 使用require导入assets目录中的图片\n      return require('@/assets/game/image/' + fileName);\n    },\n    \n    // 加载用户金币数量\n    loadUserCoins() {\n      getAction('/teaching/coin/getUserCoin')\n        .then(res => {\n          if (res.success) {\n            this.userCoins = res.result || 0\n          } else {\n            // 如果获取失败，尝试重新获取\n            setTimeout(() => {\n              this.loadUserCoins()\n            }, 1000)\n          }\n        })\n        .catch(() => {\n          // 如果出错，尝试重新获取\n          setTimeout(() => {\n            this.loadUserCoins()\n          }, 1000)\n        })\n    },\n    \n    // 消费金币\n    consumeCoins(amount, gameTitle) {\n      // 使用AJAX请求扣减用户金币\n      postAction('/teaching/coin/consumeGameCoin', {\n        coinCount: amount,\n        gameTitle: gameTitle\n      })\n      .then(res => {\n        if (res.success) {\n          this.userCoins -= amount\n        } else {\n          this.closeGameModal()\n        }\n      })\n      .catch(() => {\n        this.closeGameModal()\n      })\n    },\n    \n    // 打开游戏模态窗口\n    openGameModal(game) {\n      this.currentGame = game\n      this.showConfirmDialog = true\n      this.confirmMessage = `确认消费 ${game.cost} 金币开始游戏10分钟？`\n    },\n    \n    // 处理确认对话框确认操作\n    handleConfirm() {\n      if (this.userCoins >= this.currentGame.cost) {\n        this.showConfirmDialog = false\n        this.showModal = true\n      } else {\n        this.$message.error('金币不足，无法开始游戏！')\n        this.showConfirmDialog = false\n      }\n    },\n    \n    // 关闭游戏模态窗口\n    closeGameModal() {\n      this.showModal = false\n      this.currentGame = null\n    },\n    \n    // 直接从localStorage获取用户信息\n    loadUserInfoDirectly() {\n      console.log('直接从localStorage获取用户信息')\n      \n      try {\n        // 1. 尝试从localStorage获取用户信息 - 正确的键名是 Login_Userinfo\n        const userInfoStr = localStorage.getItem('pro__Login_Userinfo')\n        if (userInfoStr) {\n          const userInfoObj = JSON.parse(userInfoStr)\n          if (userInfoObj && userInfoObj.value) {\n            const userInfo = userInfoObj.value\n            console.log('从localStorage获取到用户信息:', userInfo)\n            \n            // 设置用户名\n            if (userInfo.realname || userInfo.username) {\n              this.userName = userInfo.realname || userInfo.username\n              console.log('设置用户名为:', this.userName)\n            }\n            \n            // 设置头像\n            if (userInfo.avatar) {\n              // 直接使用window全局函数处理头像URL\n              this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n              console.log('设置头像URL为:', this.userAvatar)\n            } else {\n              // 使用默认头像\n              this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n              console.log('使用默认头像')\n            }\n            \n            return\n          }\n        }\n        \n        // 2. 如果上面的方法失败，尝试从Vuex获取\n        console.log('尝试从Vuex获取用户信息')\n        if (this.$store.state.user && this.$store.state.user.info) {\n          const userInfo = this.$store.state.user.info\n          \n          // 设置用户名\n          this.userName = userInfo.realname || userInfo.username || '账号名'\n          console.log('从Vuex设置用户名为:', this.userName)\n          \n          // 设置头像\n          if (this.$store.state.user.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(this.$store.state.user.avatar)\n            console.log('从Vuex设置头像URL为:', this.userAvatar)\n          } else if (userInfo.avatar) {\n            this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n            console.log('从Vuex用户信息设置头像URL为:', this.userAvatar)\n          } else {\n            this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n            console.log('使用默认头像')\n          }\n          \n          return\n        }\n        \n        // 3. 如果前两种方法都失败，直接调用API获取\n        console.log('从API获取用户信息')\n        getAction('/sys/user/getUserInfo')\n          .then(res => {\n            if (res.success && res.result) {\n              const userInfo = res.result\n              console.log('API返回用户信息:', userInfo)\n              \n              // 设置用户名\n              this.userName = userInfo.realname || userInfo.username || '账号名'\n              console.log('从API设置用户名为:', this.userName)\n              \n              // 设置头像\n              if (userInfo.avatar) {\n                this.userAvatar = window.getFileAccessHttpUrl(userInfo.avatar)\n                console.log('从API设置头像URL为:', this.userAvatar)\n              } else {\n                this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n                console.log('使用默认头像')\n              }\n            }\n          })\n          .catch(error => {\n            console.error('API获取用户信息失败:', error)\n          })\n        \n      } catch (error) {\n        console.error('获取用户信息出错:', error)\n        // 使用默认值\n        this.userName = '账号名'\n        this.userAvatar = window.getFileAccessHttpUrl('/logo.png')\n      }\n    },\n    \n    // 采用与ShoppingModal相同的方式获取用户信息\n    getShoppingStyleUserInfo() {\n      console.log('采用ShoppingModal方式获取用户信息');\n      \n      // 获取用户信息\n      if (this.$store.state.user && this.$store.state.user.info) {\n        this.userName = this.$store.state.user.info.realname || this.$store.state.user.info.username || '账号名';\n        console.log('获取到用户名:', this.userName);\n        \n        // 使用getFileAccessHttpUrl处理头像路径\n        const avatar = this.$store.state.user.avatar || '/logo.png';\n        console.log('原始头像路径:', avatar);\n        \n        // 尝试从用户信息中获取头像路径\n        if (avatar) {\n          this.userAvatar = getFileAccessHttpUrl(avatar);\n          console.log('处理后头像路径:', this.userAvatar);\n        } else if (this.$store.getters && this.$store.getters.sysConfig && this.$store.getters.sysConfig.avatar) {\n          // 如果用户没有头像，使用默认头像\n          const defaultAvatar = this.$store.getters.sysConfig.avatar;\n          this.userAvatar = getFileAccessHttpUrl(defaultAvatar);\n          console.log('使用默认头像:', this.userAvatar);\n        } else {\n          // 使用静态默认头像\n          this.userAvatar = getFileAccessHttpUrl('/logo.png');\n          console.log('使用静态默认头像:', this.userAvatar);\n        }\n      } else {\n        console.log('用户信息未加载，使用默认值');\n      }\n    },\n    \n    // 直接从API获取用户信息\n    getUserInfoFromAPI() {\n      console.log('直接从API获取用户信息');\n      \n      getAction('/sys/user/getUserInfo').then(res => {\n        if (res.success && res.result) {\n          console.log('API返回用户信息:', res.result);\n          \n          // 设置用户名\n          if (res.result.realname || res.result.username) {\n            this.userName = res.result.realname || res.result.username;\n            console.log('API获取到用户名:', this.userName);\n          }\n          \n          // 设置头像\n          if (res.result.avatar) {\n            const avatar = res.result.avatar;\n            if (!avatar.startsWith('http')) {\n              this.userAvatar = getFileAccessHttpUrl(avatar);\n              console.log('API获取头像处理后:', this.userAvatar);\n            } else {\n              this.userAvatar = avatar;\n              console.log('API获取原始头像:', this.userAvatar);\n            }\n          } else {\n            this.userAvatar = getFileAccessHttpUrl('/logo.png');\n            console.log('API无头像，使用默认头像');\n          }\n        } else {\n          console.log('API获取用户信息失败');\n        }\n      }).catch(err => {\n        console.error('API获取用户信息出错:', err);\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.game-center {\n  font-family: 'Comic Sans MS', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n  background: linear-gradient(135deg, #7579ff, #b224ef);\n  background-attachment: fixed;\n  color: #333;\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 添加背景装饰元素 */\n.game-center::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.2) 5px, transparent 6px),\n    radial-gradient(circle at 30% 60%, rgba(255,255,255,0.2) 8px, transparent 9px),\n    radial-gradient(circle at 70% 40%, rgba(255,255,255,0.2) 10px, transparent 11px),\n    radial-gradient(circle at 90% 80%, rgba(255,255,255,0.2) 7px, transparent 8px);\n  background-size: 300px 300px;\n  z-index: 0;\n  animation: floatingBubbles 30s infinite linear;\n}\n\n@keyframes floatingBubbles {\n  0% { background-position: 0 0; }\n  100% { background-position: 300px 300px; }\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  position: relative;\n  z-index: 1;\n}\n\nheader {\n  background: linear-gradient(135deg, #4e54c8, #8f94fb);\n  color: white;\n  padding: 25px;\n  text-align: center;\n  border-radius: 20px;\n  margin-bottom: 30px;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 4px solid #ffffff;\n}\n\n/* 添加彩色边框动画 */\n@keyframes borderAnimation {\n  0% { border-color: #ff9a9e; }\n  25% { border-color: #fad0c4; }\n  50% { border-color: #a1c4fd; }\n  75% { border-color: #c2e9fb; }\n  100% { border-color: #ff9a9e; }\n}\n\nheader:hover {\n  transform: translateY(-5px);\n  animation: borderAnimation 3s infinite;\n}\n\n/* 添加装饰元素 */\nheader::before {\n  content: '🎮';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  top: -15px;\n  left: 20px;\n  transform: rotate(-15deg);\n}\n\nheader::after {\n  content: '🎯';\n  position: absolute;\n  font-size: 80px;\n  opacity: 0.1;\n  bottom: -15px;\n  right: 20px;\n  transform: rotate(15deg);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.title-area {\n  text-align: left;\n}\n\nh1 {\n  font-size: 2.8rem;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  font-weight: bold;\n  color: #fff;\n  letter-spacing: 1px;\n  position: relative;\n  display: inline-block;\n}\n\n/* 添加标题装饰 */\nh1::after {\n  content: '';\n  position: absolute;\n  bottom: -5px;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(90deg, #ff9a9e, #fad0c4, #a1c4fd);\n  border-radius: 10px;\n}\n\n.subtitle {\n  font-size: 1.4rem;\n  font-weight: 300;\n  color: #f0f0f0;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.3);\n  padding: 10px 15px;\n  border-radius: 30px;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(5px);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n}\n\n.user-avatar {\n  width: 45px;\n  height: 45px;\n  border-radius: 50%;\n  border: 3px solid #fff;\n  margin-right: 12px;\n  object-fit: cover;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n\n.user-name {\n  font-weight: bold;\n  margin-right: 15px;\n  color: #fff;\n  font-size: 1.1rem;\n}\n\n.coin-display {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #ffd86f, #fc6076);\n  padding: 8px 15px;\n  border-radius: 20px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.5);\n  transition: all 0.3s ease;\n}\n\n.coin-display:hover {\n  transform: scale(1.05);\n}\n\n.coin-icon {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: url('data:image/svg+xml;utf8,<svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"%23ffc107\"/><circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"%23ffdc54\"/><text x=\"12\" y=\"16\" font-size=\"10\" text-anchor=\"middle\" fill=\"%23e67e22\">¥</text></svg>');\n  background-size: contain;\n  margin-right: 8px;\n  animation: spin 10s infinite linear;\n}\n\n@keyframes spin {\n  0% { transform: rotateY(0deg); }\n  100% { transform: rotateY(360deg); }\n}\n\n.coin-amount {\n  font-weight: bold;\n  font-size: 18px;\n  color: #fff;\n}\n\n.game-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 25px;\n  margin-top: 30px;\n}\n\nfooter {\n  margin-top: 50px;\n  text-align: center;\n  color: #fff;\n  padding: 20px 0;\n  border-top: 2px dashed rgba(255, 255, 255, 0.3);\n  font-size: 1rem;\n  position: relative;\n}\n\n/* 添加页脚装饰 */\nfooter::before {\n  content: '🚀';\n  position: absolute;\n  left: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\nfooter::after {\n  content: '🌟';\n  position: absolute;\n  right: 20%;\n  top: -15px;\n  font-size: 24px;\n}\n\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n  }\n  \n  .title-area {\n    margin-bottom: 15px;\n    text-align: center;\n  }\n  \n  .user-info {\n    width: 100%;\n    justify-content: center;\n    margin-top: 15px;\n  }\n  \n  .game-grid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  }\n  \n  h1 {\n    font-size: 2.2rem;\n  }\n  \n  .subtitle {\n    font-size: 1.2rem;\n  }\n}\n\n/* 游戏图片样式 */\n.game-image-bg {\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n/* 添加动画效果 */\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n  100% { transform: translateY(0px); }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n</style> "]}]}