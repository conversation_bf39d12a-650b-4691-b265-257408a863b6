{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue?vue&type=style&index=0&id=1cca1d35&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.app-list {\n  .ant-card {\n    min-height: 200px;\n    max-height: 400px;\n    width: 300px;\n    display: inline-block;\n    margin: 20px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    margin-top: 16px;\n    .title{\n        margin-right:20px;\n    }\n    img {\n      width: 100%;\n      max-height: 200px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["CourseUnitListCard.vue"], "names": [], "mappings": ";AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CourseUnitListCard.vue", "sourceRoot": "src/views/account/course", "sourcesContent": ["<template>\n  <div class=\"app-list\">\n    <a-card v-for=\"item in dataSource\" :key=\"item.id\" @click=\"viewUnit(item)\">\n      <a-card-meta>\n          <div style=\"margin-bottom: 3px\" slot=\"title\">\n            <a-icon type=\"right-circle\"/>&nbsp;&nbsp;\n            {{ item.unitName }}\n          </div>\n          <div class=\"meta-cardInfo\" slot=\"description\">\n            <img :src=\"getFileAccessHttpUrl(item.unitCover)\" height=\"25px\" style=\"width:100%;height:100%;\"/>\n          </div>\n      </a-card-meta>\n    </a-card>\n    <unitView-modal ref=\"unitViewModal\"/>\n  </div>\n</template>\n<script>\nimport { getAction,getFileAccessHttpUrl } from '@/api/manage'\n import UnitViewModal from './modules/UnitViewModal'\nexport default {\n  name: 'MineCourseList',\n  components: {\n    UnitViewModal\n  },\n  data() {\n    return {\n      dataSource: [],\n      courseInfo: {},\n      url:{\n        courseInfo: \"/teaching/teachingCourse/queryById\",\n        unitList: \"/teaching/teachingCourseUnit/mineUnit\"\n      },\n      visible : false,\n      unit: {}\n    }\n  },\n  created(){\n    let courseId = this.$route.query.id\n    console.log(\"courseId\"+courseId)\n    if(courseId){\n        this.getCourseInfo(courseId)\n        this.getUnitList(courseId)\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    getCourseInfo(courseId){\n        getAction(this.url.courseInfo, {id: courseId}).then(res=>{\n            console.log(res)\n            if(res.success){\n              this.courseInfo = res.result\n              this.$route.meta.title = \"我的课程-\" + this.courseInfo.courseName\n              this.courseInfo.map = this.getFileAccessHttpUrl(this.courseInfo.map)\n            }else{\n              this.$message.error(res.message)\n            }\n        })\n    },\n    getUnitList(courseId){\n        getAction(this.url.unitList, {courseId: courseId, pageNo: 1, pageSize:99}).then(res=>{\n            console.log(res)\n            if(res.success){\n              this.dataSource = res.result.records\n            }else{\n              this.$message.error(res.message)\n            }\n        })\n    },\n    viewUnit(unit){\n      console.log(unit);\n      this.$refs.unitViewModal.visible = true;\n      this.$refs.unitViewModal.unit = unit\n    },\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.app-list {\n  .ant-card {\n    min-height: 200px;\n    max-height: 400px;\n    width: 300px;\n    display: inline-block;\n    margin: 20px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    margin-top: 16px;\n    .title{\n        margin-right:20px;\n    }\n    img {\n      width: 100%;\n      max-height: 200px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n}\n</style>"]}]}