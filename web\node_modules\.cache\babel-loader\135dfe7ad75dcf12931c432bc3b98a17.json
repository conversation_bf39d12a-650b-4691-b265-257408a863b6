{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\TableTotal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\TableTotal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nexport default {\n  name: 'TableTotal',\n  data: function data() {\n    return {\n      columns: [{\n        title: '#',\n        width: '180px',\n        align: 'center',\n        dataIndex: 'rowIndex',\n        customRender: function customRender(text, r, index) {\n          return text !== '合计' ? parseInt(index) + 1 : text;\n        }\n      }, {\n        title: '姓名',\n        dataIndex: 'name'\n      }, {\n        title: '贡献点',\n        dataIndex: 'point'\n      }, {\n        title: '等级',\n        dataIndex: 'level'\n      }, {\n        title: '更新时间',\n        dataIndex: 'updateTime'\n      }],\n      dataSource: [{\n        name: '张三',\n        point: 23,\n        level: 3,\n        updateTime: '2019-8-14'\n      }, {\n        name: '小王',\n        point: 6,\n        level: 1,\n        updateTime: '2019-8-13'\n      }, {\n        name: '李四',\n        point: 53,\n        level: 8,\n        updateTime: '2019-8-12'\n      }, {\n        name: '小红',\n        point: 44,\n        level: 5,\n        updateTime: '2019-8-11'\n      }, {\n        name: '王五',\n        point: 97,\n        level: 10,\n        updateTime: '2019-8-10'\n      }, {\n        name: '小明',\n        point: 33,\n        level: 2,\n        updateTime: '2019-8-10'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.tableAddTotalRow(this.columns, this.dataSource);\n  },\n  methods: {\n    /** 表格增加合计行 */tableAddTotalRow: function tableAddTotalRow(columns, dataSource) {\n      var numKey = 'rowIndex';\n      var totalRow = _defineProperty({}, numKey, '合计');\n      columns.forEach(function (column) {\n        var key = column.key,\n          dataIndex = column.dataIndex;\n        if (![key, dataIndex].includes(numKey)) {\n          var total = 0;\n          dataSource.forEach(function (data) {\n            total += /^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN;\n            console.log(data[dataIndex], ':', /^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN);\n          });\n          if (Number.isNaN(total)) {\n            total = '-';\n          }\n          totalRow[dataIndex] = total;\n        }\n      });\n      dataSource.push(totalRow);\n    }\n  }\n};", {"version": 3, "names": ["name", "data", "columns", "title", "width", "align", "dataIndex", "customRender", "text", "r", "index", "parseInt", "dataSource", "point", "level", "updateTime", "mounted", "tableAddTotalRow", "methods", "numKey", "totalRow", "_defineProperty", "for<PERSON>ach", "column", "key", "includes", "total", "test", "Number", "NaN", "console", "log", "isNaN", "push"], "sources": ["src/views/jeecg/TableTotal.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <a-table\n      rowKey=\"id\"\n      bordered\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"false\"\n    >\n    </a-table>\n  </a-card>\n</template>\n\n<script>\n  export default {\n    name: 'TableTotal',\n    data() {\n      return {\n        columns: [\n          {\n            title: '#',\n            width: '180px',\n            align: 'center',\n            dataIndex: 'rowIndex',\n            customRender: function (text, r, index) {\n              return (text !== '合计') ? (parseInt(index) + 1) : text\n            }\n          },\n          {\n            title: '姓名',\n            dataIndex: 'name',\n          },\n          {\n            title: '贡献点',\n            dataIndex: 'point',\n          },\n          {\n            title: '等级',\n            dataIndex: 'level',\n          },\n          {\n            title: '更新时间',\n            dataIndex: 'updateTime',\n          },\n        ],\n        dataSource: [\n          { name: '张三', point: 23, level: 3, updateTime: '2019-8-14' },\n          { name: '小王', point: 6, level: 1, updateTime: '2019-8-13' },\n          { name: '李四', point: 53, level: 8, updateTime: '2019-8-12' },\n          { name: '小红', point: 44, level: 5, updateTime: '2019-8-11' },\n          { name: '王五', point: 97, level: 10, updateTime: '2019-8-10' },\n          { name: '小明', point: 33, level: 2, updateTime: '2019-8-10' },\n        ]\n      }\n    },\n    mounted() {\n      this.tableAddTotalRow(this.columns, this.dataSource)\n    },\n    methods: {\n\n      /** 表格增加合计行 */\n      tableAddTotalRow(columns, dataSource) {\n        let numKey = 'rowIndex'\n        let totalRow = { [numKey]: '合计' }\n        columns.forEach(column => {\n          let { key, dataIndex } = column\n          if (![key, dataIndex].includes(numKey)) {\n\n            let total = 0\n            dataSource.forEach(data => {\n              total += /^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN\n              console.log(data[dataIndex], ':', (/^\\d+\\.?\\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN))\n            })\n\n            if (Number.isNaN(total)) {\n              total = '-'\n            }\n            totalRow[dataIndex] = total\n          }\n        })\n\n        dataSource.push(totalRow)\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": ";;;;AAcA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA,EAAAC,CAAA,EAAAC,KAAA;UACA,OAAAF,IAAA,YAAAG,QAAA,CAAAD,KAAA,QAAAF,IAAA;QACA;MACA,GACA;QACAL,KAAA;QACAG,SAAA;MACA,GACA;QACAH,KAAA;QACAG,SAAA;MACA,GACA;QACAH,KAAA;QACAG,SAAA;MACA,GACA;QACAH,KAAA;QACAG,SAAA;MACA,EACA;MACAM,UAAA,GACA;QAAAZ,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA,GACA;QAAAf,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA,GACA;QAAAf,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA,GACA;QAAAf,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA,GACA;QAAAf,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA,GACA;QAAAf,IAAA;QAAAa,KAAA;QAAAC,KAAA;QAAAC,UAAA;MAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA,MAAAf,OAAA,OAAAU,UAAA;EACA;EACAM,OAAA;IAEA,cACAD,gBAAA,WAAAA,iBAAAf,OAAA,EAAAU,UAAA;MACA,IAAAO,MAAA;MACA,IAAAC,QAAA,GAAAC,eAAA,KAAAF,MAAA;MACAjB,OAAA,CAAAoB,OAAA,WAAAC,MAAA;QACA,IAAAC,GAAA,GAAAD,MAAA,CAAAC,GAAA;UAAAlB,SAAA,GAAAiB,MAAA,CAAAjB,SAAA;QACA,MAAAkB,GAAA,EAAAlB,SAAA,EAAAmB,QAAA,CAAAN,MAAA;UAEA,IAAAO,KAAA;UACAd,UAAA,CAAAU,OAAA,WAAArB,IAAA;YACAyB,KAAA,kBAAAC,IAAA,CAAA1B,IAAA,CAAAK,SAAA,KAAAsB,MAAA,CAAAjB,QAAA,CAAAV,IAAA,CAAAK,SAAA,KAAAsB,MAAA,CAAAC,GAAA;YACAC,OAAA,CAAAC,GAAA,CAAA9B,IAAA,CAAAK,SAAA,sBAAAqB,IAAA,CAAA1B,IAAA,CAAAK,SAAA,KAAAsB,MAAA,CAAAjB,QAAA,CAAAV,IAAA,CAAAK,SAAA,KAAAsB,MAAA,CAAAC,GAAA;UACA;UAEA,IAAAD,MAAA,CAAAI,KAAA,CAAAN,KAAA;YACAA,KAAA;UACA;UACAN,QAAA,CAAAd,SAAA,IAAAoB,KAAA;QACA;MACA;MAEAd,UAAA,CAAAqB,IAAA,CAAAb,QAAA;IACA;EAEA;AACA", "ignoreList": []}]}