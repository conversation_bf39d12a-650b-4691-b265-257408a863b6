{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue?vue&type=template&id=76bcb248&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\page\\SHeaderNotice.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-popover trigger=\"click\" placement=\"bottomRight\" :overlayStyle=\"{ width: '300px' }\">\n  <template slot=\"content\">\n    <a-spin :spinning=\"loadding\">\n      <a-tabs>\n        <a-tab-pane v-for=\"(tab, k) in tabs\" :tab=\"tab.title\" :key=\"k\">\n\n        </a-tab-pane>\n      </a-tabs>\n    </a-spin>\n  </template>\n  <span @click=\"fetchNotice\" class=\"header-notice\">\n    <a-badge count=\"12\">\n      <a-icon style=\"font-size: 16px; padding: 4px\" type=\"bell\" />\n    </a-badge>\n  </span>\n</a-popover>\n", null]}