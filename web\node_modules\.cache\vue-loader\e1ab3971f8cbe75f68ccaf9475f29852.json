{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\JSelectBizComponent\\JSelectBizComponentModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { getAction } from '@/api/manage'\n  import Ellipsis from '@/components/Ellipsis'\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import { cloneObject, pushIfNotExist } from '@/utils/util'\n\n  export default {\n    name: 'JSelectBizComponentModal',\n    mixins: [JeecgListMixin],\n    components: { Ellipsis },\n    props: {\n      value: {\n        type: Array,\n        default: () => []\n      },\n      visible: {\n        type: Boolean,\n        default: false\n      },\n      valueKey: {\n        type: String,\n        required: true\n      },\n      multiple: {\n        type: Boolean,\n        default: true\n      },\n      width: {\n        type: Number,\n        default: 900\n      },\n\n      name: {\n        type: String,\n        default: ''\n      },\n      listUrl: {\n        type: String,\n        required: true,\n        default: ''\n      },\n      // 根据 value 获取显示文本的地址，例如存的是 username，可以通过该地址获取到 realname\n      valueUrl: {\n        type: String,\n        default: ''\n      },\n      displayKey: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true,\n        default: () => []\n      },\n      // 查询条件Code\n      queryParamCode: {\n        type: String,\n        default: null\n      },\n      // 查询条件文字\n      queryParamText: {\n        type: String,\n        default: null\n      },\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 过长裁剪长度，设置为 -1 代表不裁剪\n      ellipsisLength: {\n        type: Number,\n        default: 12\n      },\n    },\n    data() {\n      return {\n        innerValue: [],\n        // 已选择列表\n        selectedTable: {\n          pagination: false,\n          scroll: { y: 240 },\n          columns: [\n            {\n              ...this.columns[0],\n              width: this.columns[0].widthRight || this.columns[0].width,\n            },\n            { title: '操作', dataIndex: 'action', align: 'center', width: 60, scopedSlots: { customRender: 'action' }, }\n          ],\n          dataSource: [],\n        },\n        renderEllipsis: (value) => (<ellipsis length={this.ellipsisLength}>{value}</ellipsis>),\n        url: { list: this.listUrl },\n        /* 分页参数 */\n        ipagination: {\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['5', '10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + '-' + range[1] + ' 共' + total + '条'\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        options: [],\n        dataSourceMap: {},\n      }\n    },\n    computed: {\n      // 表头\n      innerColumns() {\n        let columns = cloneObject(this.columns)\n        columns.forEach(column => {\n          // 给所有的列加上过长裁剪\n          if (this.ellipsisLength !== -1) {\n            column.customRender = (text) => this.renderEllipsis(text)\n          }\n        })\n        return columns\n      },\n    },\n    watch: {\n      value: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          this.innerValue = cloneObject(val)\n          this.selectedRowKeys = []\n          this.valueWatchHandler(val)\n          this.queryOptionsByValue(val)\n        }\n      },\n      dataSource: {\n        deep: true,\n        handler(val) {\n          this.emitOptions(val)\n          this.valueWatchHandler(this.innerValue)\n        }\n      },\n      selectedRowKeys: {\n        immediate: true,\n        deep: true,\n        handler(val) {\n          this.selectedTable.dataSource = val.map(key => {\n            for (let data of this.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            for (let data of this.selectedTable.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            console.warn('未找到选择的行信息，key：' + key)\n            return {}\n          })\n        },\n      }\n    },\n\n    methods: {\n\n      /** 关闭弹窗 */\n      close() {\n        this.$emit('update:visible', false)\n      },\n\n      valueWatchHandler(val) {\n        val.forEach(item => {\n          this.dataSource.concat(this.selectedTable.dataSource).forEach(data => {\n            if (data[this.valueKey] === item) {\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n            }\n          })\n        })\n      },\n\n      queryOptionsByValue(value) {\n        if (!value || value.length === 0) {\n          return\n        }\n        // 判断options是否存在value，如果已存在数据就不再请求后台了\n        let notExist = false\n        for (let val of value) {\n          let find = false\n          for (let option of this.options) {\n            if (val === option.value) {\n              find = true\n              break\n            }\n          }\n          if (!find) {\n            notExist = true\n            break\n          }\n        }\n        if (!notExist) return\n        getAction(this.valueUrl || this.listUrl, {\n          // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确\n          [this.valueKey]: value.join(',') + ',',\n          pageNo: 1,\n          pageSize: value.length\n        }).then((res) => {\n          if (res.success) {\n            let dataSource = res.result\n            if (!(dataSource instanceof Array)) {\n              dataSource = res.result.records\n            }\n            this.emitOptions(dataSource, (data) => {\n              pushIfNotExist(this.innerValue, data[this.valueKey])\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n              pushIfNotExist(this.selectedTable.dataSource, data, this.rowKey)\n            })\n          }\n        })\n      },\n\n      emitOptions(dataSource, callback) {\n        dataSource.forEach(data => {\n          let key = data[this.valueKey]\n          this.dataSourceMap[key] = data\n          pushIfNotExist(this.options, { label: data[this.displayKey || this.valueKey], value: key }, 'value')\n          typeof callback === 'function' ? callback(data) : ''\n        })\n        this.$emit('options', this.options, this.dataSourceMap)\n      },\n\n      /** 完成选择 */\n      handleOk() {\n        let value = this.selectedTable.dataSource.map(data => data[this.valueKey])\n        this.$emit('input', value)\n        this.close()\n      },\n\n      /** 删除已选择的 */\n      handleDeleteSelected(record, index) {\n        this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(record[this.rowKey]), 1)\n        this.selectedTable.dataSource.splice(index, 1)\n      },\n\n      customRowFn(record) {\n        return {\n          on: {\n            click: () => {\n              let key = record[this.rowKey]\n              if (!this.multiple) {\n                this.selectedRowKeys = [key]\n                this.selectedTable.dataSource = [record]\n              } else {\n                let index = this.selectedRowKeys.indexOf(key)\n                if (index === -1) {\n                  this.selectedRowKeys.push(key)\n                  this.selectedTable.dataSource.push(record)\n                } else {\n                  this.handleDeleteSelected(record, index)\n                }\n              }\n            }\n          }\n        }\n      },\n\n    }\n  }\n", {"version": 3, "sources": ["JSelectBizComponentModal.vue"], "names": [], "mappings": ";AAgEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "JSelectBizComponentModal.vue", "sourceRoot": "src/components/jeecgbiz/JSelectBizComponent", "sourcesContent": ["<template>\n  <a-modal\n    centered\n    :title=\"name + '选择'\"\n    :width=\"width\"\n    :visible=\"visible\"\n    @ok=\"handleOk\"\n    @cancel=\"close\"\n    cancelText=\"关闭\">\n\n    <a-row :gutter=\"18\">\n      <a-col :span=\"16\">\n        <!-- 查询区域 -->\n        <div class=\"table-page-search-wrapper\">\n          <a-form layout=\"inline\">\n            <a-row :gutter=\"24\">\n\n              <a-col :span=\"14\">\n                <a-form-item :label=\"(queryParamText||name)\">\n                  <a-input v-model=\"queryParam[queryParamCode||valueKey]\" :placeholder=\"'请输入' + (queryParamText||name)\" @pressEnter=\"searchQuery\"/>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"8\">\n                  <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n                    <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n                    <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n                  </span>\n              </a-col>\n\n            </a-row>\n          </a-form>\n        </div>\n\n        <a-table\n          size=\"small\"\n          bordered\n          :rowKey=\"rowKey\"\n          :columns=\"innerColumns\"\n          :dataSource=\"dataSource\"\n          :pagination=\"ipagination\"\n          :loading=\"loading\"\n          :scroll=\"{ y: 240 }\"\n          :rowSelection=\"{selectedRowKeys, onChange: onSelectChange, type: multiple ? 'checkbox':'radio'}\"\n          :customRow=\"customRowFn\"\n          @change=\"handleTableChange\">\n        </a-table>\n\n      </a-col>\n      <a-col :span=\"8\">\n        <a-card :title=\"'已选' + name\" :bordered=\"false\" :head-style=\"{padding:0}\" :body-style=\"{padding:0}\">\n\n          <a-table size=\"small\" :rowKey=\"rowKey\" bordered v-bind=\"selectedTable\">\n              <span slot=\"action\" slot-scope=\"text, record, index\">\n                <a @click=\"handleDeleteSelected(record, index)\">删除</a>\n              </span>\n          </a-table>\n\n        </a-card>\n      </a-col>\n    </a-row>\n  </a-modal>\n</template>\n\n<script>\n  import { getAction } from '@/api/manage'\n  import Ellipsis from '@/components/Ellipsis'\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import { cloneObject, pushIfNotExist } from '@/utils/util'\n\n  export default {\n    name: 'JSelectBizComponentModal',\n    mixins: [JeecgListMixin],\n    components: { Ellipsis },\n    props: {\n      value: {\n        type: Array,\n        default: () => []\n      },\n      visible: {\n        type: Boolean,\n        default: false\n      },\n      valueKey: {\n        type: String,\n        required: true\n      },\n      multiple: {\n        type: Boolean,\n        default: true\n      },\n      width: {\n        type: Number,\n        default: 900\n      },\n\n      name: {\n        type: String,\n        default: ''\n      },\n      listUrl: {\n        type: String,\n        required: true,\n        default: ''\n      },\n      // 根据 value 获取显示文本的地址，例如存的是 username，可以通过该地址获取到 realname\n      valueUrl: {\n        type: String,\n        default: ''\n      },\n      displayKey: {\n        type: String,\n        default: null\n      },\n      columns: {\n        type: Array,\n        required: true,\n        default: () => []\n      },\n      // 查询条件Code\n      queryParamCode: {\n        type: String,\n        default: null\n      },\n      // 查询条件文字\n      queryParamText: {\n        type: String,\n        default: null\n      },\n      rowKey: {\n        type: String,\n        default: 'id'\n      },\n      // 过长裁剪长度，设置为 -1 代表不裁剪\n      ellipsisLength: {\n        type: Number,\n        default: 12\n      },\n    },\n    data() {\n      return {\n        innerValue: [],\n        // 已选择列表\n        selectedTable: {\n          pagination: false,\n          scroll: { y: 240 },\n          columns: [\n            {\n              ...this.columns[0],\n              width: this.columns[0].widthRight || this.columns[0].width,\n            },\n            { title: '操作', dataIndex: 'action', align: 'center', width: 60, scopedSlots: { customRender: 'action' }, }\n          ],\n          dataSource: [],\n        },\n        renderEllipsis: (value) => (<ellipsis length={this.ellipsisLength}>{value}</ellipsis>),\n        url: { list: this.listUrl },\n        /* 分页参数 */\n        ipagination: {\n          current: 1,\n          pageSize: 5,\n          pageSizeOptions: ['5', '10', '20', '30'],\n          showTotal: (total, range) => {\n            return range[0] + '-' + range[1] + ' 共' + total + '条'\n          },\n          showQuickJumper: true,\n          showSizeChanger: true,\n          total: 0\n        },\n        options: [],\n        dataSourceMap: {},\n      }\n    },\n    computed: {\n      // 表头\n      innerColumns() {\n        let columns = cloneObject(this.columns)\n        columns.forEach(column => {\n          // 给所有的列加上过长裁剪\n          if (this.ellipsisLength !== -1) {\n            column.customRender = (text) => this.renderEllipsis(text)\n          }\n        })\n        return columns\n      },\n    },\n    watch: {\n      value: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          this.innerValue = cloneObject(val)\n          this.selectedRowKeys = []\n          this.valueWatchHandler(val)\n          this.queryOptionsByValue(val)\n        }\n      },\n      dataSource: {\n        deep: true,\n        handler(val) {\n          this.emitOptions(val)\n          this.valueWatchHandler(this.innerValue)\n        }\n      },\n      selectedRowKeys: {\n        immediate: true,\n        deep: true,\n        handler(val) {\n          this.selectedTable.dataSource = val.map(key => {\n            for (let data of this.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            for (let data of this.selectedTable.dataSource) {\n              if (data[this.rowKey] === key) {\n                pushIfNotExist(this.innerValue, data[this.valueKey])\n                return data\n              }\n            }\n            console.warn('未找到选择的行信息，key：' + key)\n            return {}\n          })\n        },\n      }\n    },\n\n    methods: {\n\n      /** 关闭弹窗 */\n      close() {\n        this.$emit('update:visible', false)\n      },\n\n      valueWatchHandler(val) {\n        val.forEach(item => {\n          this.dataSource.concat(this.selectedTable.dataSource).forEach(data => {\n            if (data[this.valueKey] === item) {\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n            }\n          })\n        })\n      },\n\n      queryOptionsByValue(value) {\n        if (!value || value.length === 0) {\n          return\n        }\n        // 判断options是否存在value，如果已存在数据就不再请求后台了\n        let notExist = false\n        for (let val of value) {\n          let find = false\n          for (let option of this.options) {\n            if (val === option.value) {\n              find = true\n              break\n            }\n          }\n          if (!find) {\n            notExist = true\n            break\n          }\n        }\n        if (!notExist) return\n        getAction(this.valueUrl || this.listUrl, {\n          // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确\n          [this.valueKey]: value.join(',') + ',',\n          pageNo: 1,\n          pageSize: value.length\n        }).then((res) => {\n          if (res.success) {\n            let dataSource = res.result\n            if (!(dataSource instanceof Array)) {\n              dataSource = res.result.records\n            }\n            this.emitOptions(dataSource, (data) => {\n              pushIfNotExist(this.innerValue, data[this.valueKey])\n              pushIfNotExist(this.selectedRowKeys, data[this.rowKey])\n              pushIfNotExist(this.selectedTable.dataSource, data, this.rowKey)\n            })\n          }\n        })\n      },\n\n      emitOptions(dataSource, callback) {\n        dataSource.forEach(data => {\n          let key = data[this.valueKey]\n          this.dataSourceMap[key] = data\n          pushIfNotExist(this.options, { label: data[this.displayKey || this.valueKey], value: key }, 'value')\n          typeof callback === 'function' ? callback(data) : ''\n        })\n        this.$emit('options', this.options, this.dataSourceMap)\n      },\n\n      /** 完成选择 */\n      handleOk() {\n        let value = this.selectedTable.dataSource.map(data => data[this.valueKey])\n        this.$emit('input', value)\n        this.close()\n      },\n\n      /** 删除已选择的 */\n      handleDeleteSelected(record, index) {\n        this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(record[this.rowKey]), 1)\n        this.selectedTable.dataSource.splice(index, 1)\n      },\n\n      customRowFn(record) {\n        return {\n          on: {\n            click: () => {\n              let key = record[this.rowKey]\n              if (!this.multiple) {\n                this.selectedRowKeys = [key]\n                this.selectedTable.dataSource = [record]\n              } else {\n                let index = this.selectedRowKeys.indexOf(key)\n                if (index === -1) {\n                  this.selectedRowKeys.push(key)\n                  this.selectedTable.dataSource.push(record)\n                } else {\n                  this.handleDeleteSelected(record, index)\n                }\n              }\n            }\n          }\n        }\n      },\n\n    }\n  }\n</script>\n<style lang=\"less\" scoped>\n</style>"]}]}