{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ReadOnlyTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JEditableTable\\ReadOnlyTable.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'ReadOnlyTable',\n    components: { JEditableTable },\n    data() {\n      return {\n\n        columns: [\n          {\n            title: '输入框',\n            key: 'input',\n            type: FormTypes.input,\n            placeholder: '清输入'\n          },\n          {\n            title: '下拉框',\n            key: 'select',\n            type: FormTypes.select,\n            options: [\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            placeholder: '请选择'\n          },\n          {\n            title: '多选框',\n            key: 'checkbox',\n            type: FormTypes.checkbox,\n            customValue: [true, false]\n          },\n          {\n            title: '日期',\n            key: 'datetime',\n            type: FormTypes.datetime\n          }\n        ],\n        dataSource: [\n          { input: 'hello', select: 'int', checkbox: true, datetime: '2019-6-17 14:50:48' },\n          { input: 'world', select: 'string', checkbox: false, datetime: '2019-6-16 14:50:48' },\n          { input: 'one', select: 'double', checkbox: true, datetime: '2019-6-17 15:50:48' },\n          { input: 'two', select: 'boolean', checkbox: false, datetime: '2019-6-14 14:50:48' },\n          { input: 'three', select: '', checkbox: false, datetime: '2019-6-13 14:50:48' }\n        ]\n      }\n    },\n    mounted() {\n\n    }\n  }\n", {"version": 3, "sources": ["ReadOnlyTable.vue"], "names": [], "mappings": ";AAYA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "ReadOnlyTable.vue", "sourceRoot": "src/views/jeecg/modules/JEditableTable", "sourcesContent": ["<template>\n  <j-editable-table\n    :columns=\"columns\"\n    :dataSource=\"dataSource\"\n    :rowNumber=\"true\"\n    :rowSelection=\"true\"\n    :maxHeight=\"400\"\n    :disabled=\"true\"\n  />\n</template>\n\n<script>\n  import { FormTypes } from '@/utils/JEditableTableUtil'\n  import JEditableTable from '@/components/jeecg/JEditableTable'\n\n  export default {\n    name: 'ReadOnlyTable',\n    components: { JEditableTable },\n    data() {\n      return {\n\n        columns: [\n          {\n            title: '输入框',\n            key: 'input',\n            type: FormTypes.input,\n            placeholder: '清输入'\n          },\n          {\n            title: '下拉框',\n            key: 'select',\n            type: FormTypes.select,\n            options: [\n              { title: 'String', value: 'string' },\n              { title: 'Integer', value: 'int' },\n              { title: 'Double', value: 'double' },\n              { title: 'Boolean', value: 'boolean' }\n            ],\n            placeholder: '请选择'\n          },\n          {\n            title: '多选框',\n            key: 'checkbox',\n            type: FormTypes.checkbox,\n            customValue: [true, false]\n          },\n          {\n            title: '日期',\n            key: 'datetime',\n            type: FormTypes.datetime\n          }\n        ],\n        dataSource: [\n          { input: 'hello', select: 'int', checkbox: true, datetime: '2019-6-17 14:50:48' },\n          { input: 'world', select: 'string', checkbox: false, datetime: '2019-6-16 14:50:48' },\n          { input: 'one', select: 'double', checkbox: true, datetime: '2019-6-17 15:50:48' },\n          { input: 'two', select: 'boolean', checkbox: false, datetime: '2019-6-14 14:50:48' },\n          { input: 'three', select: '', checkbox: false, datetime: '2019-6-13 14:50:48' }\n        ]\n      }\n    },\n    mounted() {\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}