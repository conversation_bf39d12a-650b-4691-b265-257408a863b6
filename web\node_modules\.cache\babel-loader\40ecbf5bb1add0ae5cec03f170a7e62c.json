{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexTask.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\IndexTask.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import noDataPng from '@/assets/nodata.png';\nimport JEllipsis from '@/components/jeecg/JEllipsis';\nvar tempSs1 = [{\n  id: \"001\",\n  orderNo: \"电[1]1267102\",\n  orderTitle: \"药品出问题了\",\n  restDay: 1\n}, {\n  id: \"002\",\n  orderNo: \"电[4]5967102\",\n  orderTitle: \"吃了xxx医院的药，病情越来越严重\",\n  restDay: 0\n}, {\n  id: \"003\",\n  orderNo: \"电[3]5988987\",\n  orderTitle: \"今天去超市买鸡蛋，鸡蛋都是坏的\",\n  restDay: 7\n}, {\n  id: \"004\",\n  orderNo: \"电[2]5213491\",\n  orderTitle: \"xx宝实体店高价售卖xx\",\n  restDay: 5\n}, {\n  id: \"005\",\n  orderNo: \"电[1]1603491\",\n  orderTitle: \"以红利相诱，答应退保后扣一年费用\",\n  restDay: 0\n}];\nvar tempSs2 = [{\n  id: \"001\",\n  orderTitle: \"我要投诉这个大超市\",\n  orderNo: \"电[1]10299456\",\n  restDay: 6\n}, {\n  id: \"002\",\n  orderTitle: \"xxx医院乱开药方,售卖假药\",\n  orderNo: \"电[2]20235691\",\n  restDay: 0\n}, {\n  id: \"003\",\n  orderTitle: \"我想问问这家店是干啥的\",\n  orderNo: \"电[3]495867322\",\n  restDay: 7\n}, {\n  id: \"004\",\n  orderTitle: \"我要举报朝阳区奥森公园酒店\",\n  orderNo: \"电[2]1193849\",\n  restDay: 3\n}, {\n  id: \"005\",\n  orderTitle: \"我今天吃饭吃到一个石头子\",\n  orderNo: \"电[4]56782344\",\n  restDay: 9\n}];\n\n//4-7天\nvar tip_green = \"rgba(0, 255, 0, 1)\";\n//1-3天\nvar tip_yellow = \"rgba(255, 255, 0, 1)\";\n//超期\nvar tip_red = \"rgba(255, 0, 0, 1)\";\nexport default {\n  name: \"IndexTask\",\n  components: {\n    JEllipsis: JEllipsis\n  },\n  data: function data() {\n    return {\n      loading: false,\n      textMaxLength: 8,\n      dataSource1: [],\n      dataSource2: [],\n      dataSource3: [],\n      dataSource4: [],\n      columns: [{\n        title: '',\n        dataIndex: '',\n        key: 'rowIndex',\n        width: 50,\n        fixed: 'left',\n        align: \"center\",\n        scopedSlots: {\n          customRender: \"dayWarnning\"\n        }\n      }, {\n        title: '剩余天数',\n        align: \"center\",\n        dataIndex: 'restDay',\n        width: 80\n      }, {\n        title: '工单标题',\n        align: \"center\",\n        dataIndex: 'orderTitle',\n        scopedSlots: {\n          customRender: \"ellipsisText\"\n        }\n      }, {\n        title: '工单编号',\n        align: \"center\",\n        dataIndex: 'orderNo'\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        align: \"center\",\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }]\n    };\n  },\n  created: function created() {\n    this.mock();\n  },\n  mounted: function mounted() {},\n  methods: {\n    getTipColor: function getTipColor(rd) {\n      var num = rd.restDay;\n      if (num <= 0) {\n        return tip_red;\n      } else if (num >= 1 && num < 4) {\n        return tip_yellow;\n      } else if (num >= 4) {\n        return tip_green;\n      }\n    },\n    goPage: function goPage() {\n      this.$message.success(\"请根据具体业务跳转页面\");\n      //this.$router.push({ path: '/comp/mytask' })\n    },\n    mock: function mock() {\n      this.dataSource1 = tempSs1;\n      this.dataSource2 = tempSs2;\n      this.dataSource3 = tempSs1;\n      this.dataSource4 = [];\n      this.ifNullDataSource(this.dataSource4, '.tytable4');\n    },\n    ifNullDataSource: function ifNullDataSource(ds, tb) {\n      this.$nextTick(function () {\n        if (!ds || ds.length == 0) {\n          var tmp = document.createElement('img');\n          tmp.src = noDataPng;\n          tmp.width = 300;\n          var tbclass = \"\".concat(tb, \" .ant-table-placeholder\");\n          document.querySelector(tbclass).innerHTML = \"\";\n          document.querySelector(tbclass).appendChild(tmp);\n        }\n      });\n    },\n    handleData: function handleData() {\n      this.$message.success(\"办理完成\");\n    }\n  }\n};", {"version": 3, "names": ["noDataPng", "JElli<PERSON>", "tempSs1", "id", "orderNo", "orderTitle", "restDay", "tempSs2", "tip_green", "tip_yellow", "tip_red", "name", "components", "data", "loading", "textMaxLength", "dataSource1", "dataSource2", "dataSource3", "dataSource4", "columns", "title", "dataIndex", "key", "width", "fixed", "align", "scopedSlots", "customRender", "created", "mock", "mounted", "methods", "getTipColor", "rd", "num", "goPage", "$message", "success", "ifNullDataSource", "ds", "tb", "$nextTick", "length", "tmp", "document", "createElement", "src", "tbclass", "concat", "querySelector", "innerHTML", "append<PERSON><PERSON><PERSON>", "handleData"], "sources": ["src/views/dashboard/IndexTask.vue"], "sourcesContent": ["<template>\n  <div class=\"index-container-ty\">\n    <a-spin :spinning=\"loading\">\n      <a-row type=\"flex\" justify=\"start\" :gutter=\"3\">\n        <a-col :sm=\"24\" :lg=\"12\">\n          <a-card>\n            <div slot=\"title\" class=\"index-md-title\">\n              <img src=\"../../assets/daiban.png\"/>\n              我的待办【{{ dataSource1.length }}】\n            </div>\n            <div slot=\"extra\">\n              <a v-if=\"dataSource1 && dataSource1.length>0\" slot=\"footer\" @click=\"goPage\">更多 <a-icon type=\"double-right\" /></a>\n            </div>\n            <a-table\n              :class=\"'my-index-table tytable1'\"\n              ref=\"table1\"\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"columns\"\n              :dataSource=\"dataSource1\"\n              :pagination=\"false\">\n              <template slot=\"ellipsisText\" slot-scope=\"text\">\n                <j-ellipsis :value=\"text\" :length=\"textMaxLength\"></j-ellipsis>\n              </template>\n\n              <template slot=\"dayWarnning\" slot-scope=\"text,record\">\n                <a-icon type=\"bulb\" theme=\"twoTone\" style=\"font-size:22px\" :twoToneColor=\"getTipColor(record)\"/>\n              </template>\n\n              <span slot=\"action\" slot-scope=\"text, record\">\n                <a @click=\"handleData\">办理</a>\n              </span>\n\n            </a-table>\n          </a-card>\n        </a-col>\n\n        <a-col :sm=\"24\" :lg=\"12\">\n          <a-card>\n            <div slot=\"title\" class=\"index-md-title\">\n              <img src=\"../../assets/zaiban.png\"/>\n              我的在办【{{ dataSource2.length }}】\n            </div>\n            <div slot=\"extra\">\n              <a v-if=\"dataSource2 && dataSource2.length>0\" slot=\"footer\" @click=\"goPage\">更多 <a-icon type=\"double-right\" /></a>\n            </div>\n            <a-table\n              :class=\"'my-index-table tytable2'\"\n              ref=\"table2\"\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"columns\"\n              :dataSource=\"dataSource2\"\n              :pagination=\"false\">\n              <template slot=\"ellipsisText\" slot-scope=\"text\">\n                <j-ellipsis :value=\"text\" :length=\"textMaxLength\"></j-ellipsis>\n              </template>\n\n              <template slot=\"dayWarnning\" slot-scope=\"text,record\">\n                <a-icon type=\"bulb\" theme=\"twoTone\" style=\"font-size:22px\" :twoToneColor=\"getTipColor(record)\"/>\n              </template>\n\n              <span slot=\"action\" slot-scope=\"text, record\">\n                <a @click=\"handleData\">办理</a>\n              </span>\n\n            </a-table>\n          </a-card>\n        </a-col>\n\n        <a-col :span=\"24\">\n          <div style=\"height: 5px;\"></div>\n        </a-col>\n\n        <a-col :sm=\"24\" :lg=\"12\">\n          <a-card>\n            <div slot=\"title\" class=\"index-md-title\">\n              <img src=\"../../assets/guaz.png\"/>\n              我的挂账【{{ dataSource4.length }}】\n            </div>\n            <a-table\n              :class=\"'my-index-table tytable4'\"\n              ref=\"table4\"\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"columns\"\n              :dataSource=\"dataSource4\"\n              :pagination=\"false\">\n              <template slot=\"ellipsisText\" slot-scope=\"text\">\n                <j-ellipsis :value=\"text\" :length=\"textMaxLength\"></j-ellipsis>\n              </template>\n\n              <template slot=\"dayWarnning\" slot-scope=\"text,record\">\n                <a-icon type=\"bulb\" theme=\"twoTone\" style=\"font-size:22px\" :twoToneColor=\"getTipColor(record)\"/>\n              </template>\n\n              <span slot=\"action\" slot-scope=\"text, record\">\n                <a @click=\"handleData\">办理</a>\n              </span>\n\n            </a-table>\n          </a-card>\n        </a-col>\n\n        <a-col :sm=\"24\" :lg=\"12\">\n          <a-card>\n            <div slot=\"title\" class=\"index-md-title\">\n              <img src=\"../../assets/duban.png\"/>\n              我的督办【{{ dataSource3.length }}】\n            </div>\n            <a-table\n              :class=\"'my-index-table tytable3'\"\n              ref=\"table3\"\n              size=\"small\"\n              rowKey=\"id\"\n              :columns=\"columns\"\n              :dataSource=\"dataSource3\"\n              :pagination=\"false\">\n              <template slot=\"ellipsisText\" slot-scope=\"text\">\n                <j-ellipsis :value=\"text\" :length=\"textMaxLength\"></j-ellipsis>\n              </template>\n\n              <template slot=\"dayWarnning\" slot-scope=\"text,record\">\n                <a-icon type=\"bulb\" theme=\"twoTone\" style=\"font-size:22px\" :twoToneColor=\"getTipColor(record)\"/>\n              </template>\n\n              <span slot=\"action\" slot-scope=\"text, record\">\n                <a @click=\"handleData\">办理</a>\n              </span>\n\n            </a-table>\n          </a-card>\n        </a-col>\n\n      </a-row>\n    </a-spin>\n\n  </div>\n</template>\n\n<script>\n  import noDataPng from '@/assets/nodata.png'\n  import JEllipsis from '@/components/jeecg/JEllipsis'\n\n  const tempSs1=[{\n    id:\"001\",\n    orderNo:\"电[1]1267102\",\n    orderTitle:\"药品出问题了\",\n    restDay:1\n  },{\n    id:\"002\",\n    orderNo:\"电[4]5967102\",\n    orderTitle:\"吃了xxx医院的药，病情越来越严重\",\n    restDay:0\n  },{\n    id:\"003\",\n    orderNo:\"电[3]5988987\",\n    orderTitle:\"今天去超市买鸡蛋，鸡蛋都是坏的\",\n    restDay:7\n  },{\n    id:\"004\",\n    orderNo:\"电[2]5213491\",\n    orderTitle:\"xx宝实体店高价售卖xx\",\n    restDay:5\n  },{\n    id:\"005\",\n    orderNo:\"电[1]1603491\",\n    orderTitle:\"以红利相诱，答应退保后扣一年费用\",\n    restDay:0\n  }]\n\n  const tempSs2=[{\n    id:\"001\",\n    orderTitle:\"我要投诉这个大超市\",\n    orderNo:\"电[1]10299456\",\n    restDay:6\n  },{\n    id:\"002\",\n    orderTitle:\"xxx医院乱开药方,售卖假药\",\n    orderNo:\"电[2]20235691\",\n    restDay:0\n  },{\n    id:\"003\",\n    orderTitle:\"我想问问这家店是干啥的\",\n    orderNo:\"电[3]495867322\",\n    restDay:7\n  },{\n    id:\"004\",\n    orderTitle:\"我要举报朝阳区奥森公园酒店\",\n    orderNo:\"电[2]1193849\",\n    restDay:3\n  },{\n    id:\"005\",\n    orderTitle:\"我今天吃饭吃到一个石头子\",\n    orderNo:\"电[4]56782344\",\n    restDay:9\n  }]\n\n  //4-7天\n  const tip_green = \"rgba(0, 255, 0, 1)\"\n  //1-3天\n  const tip_yellow = \"rgba(255, 255, 0, 1)\"\n  //超期\n  const tip_red = \"rgba(255, 0, 0, 1)\"\n\n  export default {\n    name: \"IndexTask\",\n    components:{ JEllipsis },\n    data() {\n      return {\n        loading:false,\n        textMaxLength:8,\n        dataSource1:[],\n        dataSource2:[],\n        dataSource3:[],\n        dataSource4:[],\n        columns: [\n          {\n            title: '',\n            dataIndex: '',\n            key:'rowIndex',\n            width:50,\n            fixed:'left',\n            align:\"center\",\n            scopedSlots: {customRender: \"dayWarnning\"}\n          },\n          {\n            title:'剩余天数',\n            align:\"center\",\n            dataIndex: 'restDay',\n            width:80\n          },\n          {\n            title:'工单标题',\n            align:\"center\",\n            dataIndex: 'orderTitle',\n            scopedSlots: {customRender: \"ellipsisText\"}\n          },\n          {\n            title:'工单编号',\n            align:\"center\",\n            dataIndex: 'orderNo',\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align:\"center\",\n            scopedSlots: { customRender: 'action' }\n          }\n        ]\n\n      }\n    },\n    created() {\n      this.mock();\n    },\n    mounted(){\n\n    },\n    methods: {\n      getTipColor(rd){\n        let num = rd.restDay\n        if(num<=0){\n          return tip_red\n        }else if(num>=1 && num<4){\n          return tip_yellow\n        }else if(num>=4){\n          return tip_green\n        }\n      },\n      goPage(){\n        this.$message.success(\"请根据具体业务跳转页面\")\n        //this.$router.push({ path: '/comp/mytask' })\n      },\n      mock(){\n        this.dataSource1=tempSs1\n        this.dataSource2=tempSs2\n        this.dataSource3=tempSs1\n        this.dataSource4=[]\n        this.ifNullDataSource(this.dataSource4,'.tytable4')\n      },\n\n      ifNullDataSource(ds,tb){\n        this.$nextTick(()=>{\n          if(!ds || ds.length==0){\n            var tmp = document.createElement('img');\n            tmp.src=noDataPng\n            tmp.width=300\n            let tbclass=`${tb} .ant-table-placeholder`\n            document.querySelector(tbclass).innerHTML=\"\"\n            document.querySelector(tbclass).appendChild(tmp)\n          }\n        })\n      },\n      handleData(){\n        this.$message.success(\"办理完成\")\n      }\n\n\n\n\n    }\n  }\n</script>\n\n<style>\n  .my-index-table{height:270px}\n  .my-index-table table{font-size: 14px !important;}\n\n  .index-container-ty .ant-card-head-title{padding-top: 6px;padding-bottom: 6px;}\n  .index-container-ty .ant-card-extra{padding:0}\n  .index-container-ty .ant-card-extra a{color:#fff}\n  .index-container-ty .ant-card-extra a:hover{color:#152ede}\n  .index-container-ty .ant-card-head-wrapper,.index-container-ty .ant-card-head{\n    line-height:24px;\n    min-height:24px;\n    /*background: #90aeff;*/\n    background: #7196fb;\n  }\n  .index-container-ty .ant-card-body{padding: 10px 12px 0px 12px}\n\n  /* .index-container-ty .ant-card-actions{background: #fff}\n   .index-container-ty .ant-card-actions li {margin:2px 0;}\n   .index-container-ty .ant-card-actions > li > span{width: 100%}*/\n\n\n  .index-container-ty .ant-table-footer{text-align: right;padding:6px 12px 6px 6px;background: #fff;border-top: 2px solid #f7f1f1;}\n\n  .index-md-title{\n    postion:relative;\n    padding-left:24px;\n    width: 100%;\n    color: #fff;\n    font-size: 21px;\n    font-family: cursive;\n  }\n  .index-md-title img{\n    position: absolute;\n    height:32px;\n    top: 2px;\n    left:14px;\n  }\n\n  .index-container-ty .ant-card-body{\n    /*border-left:1px solid #90aeff;\n    /*border-right:1px solid #90aeff;\n    border-bottom:1px solid #90aeff;*/\n  }\n\n\n  .index-container-ty .ant-table-thead > tr > th,\n  .index-container-ty .ant-table-tbody > tr > td{\n    border-bottom: 1px solid #90aeff;\n  }\n\n  .index-container-ty .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,\n  .index-container-ty .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th{\n    border-bottom: 1px solid #90aeff;\n  }\n\n  .index-container-ty  .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th{\n    border-bottom: 1px solid #90aeff;\n  }\n\n  .index-container-ty .ant-table-small{\n    border: 1px solid #90aeff;\n  }\n\n  .index-container-ty .ant-table-placeholder {\n    padding: 0\n  }\n</style>"], "mappings": "AA6IA,OAAAA,SAAA;AACA,OAAAC,SAAA;AAEA,IAAAC,OAAA;EACAC,EAAA;EACAC,OAAA;EACAC,UAAA;EACAC,OAAA;AACA;EACAH,EAAA;EACAC,OAAA;EACAC,UAAA;EACAC,OAAA;AACA;EACAH,EAAA;EACAC,OAAA;EACAC,UAAA;EACAC,OAAA;AACA;EACAH,EAAA;EACAC,OAAA;EACAC,UAAA;EACAC,OAAA;AACA;EACAH,EAAA;EACAC,OAAA;EACAC,UAAA;EACAC,OAAA;AACA;AAEA,IAAAC,OAAA;EACAJ,EAAA;EACAE,UAAA;EACAD,OAAA;EACAE,OAAA;AACA;EACAH,EAAA;EACAE,UAAA;EACAD,OAAA;EACAE,OAAA;AACA;EACAH,EAAA;EACAE,UAAA;EACAD,OAAA;EACAE,OAAA;AACA;EACAH,EAAA;EACAE,UAAA;EACAD,OAAA;EACAE,OAAA;AACA;EACAH,EAAA;EACAE,UAAA;EACAD,OAAA;EACAE,OAAA;AACA;;AAEA;AACA,IAAAE,SAAA;AACA;AACA,IAAAC,UAAA;AACA;AACA,IAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAX,SAAA,EAAAA;EAAA;EACAY,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAP,KAAA;QACAK,KAAA;QACAJ,SAAA;QACAE,KAAA;MACA,GACA;QACAH,KAAA;QACAK,KAAA;QACAJ,SAAA;QACAK,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAP,KAAA;QACAK,KAAA;QACAJ,SAAA;MACA,GACA;QACAD,KAAA;QACAC,SAAA;QACAI,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA;IAGA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,EAAA;MACA,IAAAC,GAAA,GAAAD,EAAA,CAAA5B,OAAA;MACA,IAAA6B,GAAA;QACA,OAAAzB,OAAA;MACA,WAAAyB,GAAA,SAAAA,GAAA;QACA,OAAA1B,UAAA;MACA,WAAA0B,GAAA;QACA,OAAA3B,SAAA;MACA;IACA;IACA4B,MAAA,WAAAA,OAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACA;IACA;IACAR,IAAA,WAAAA,KAAA;MACA,KAAAd,WAAA,GAAAd,OAAA;MACA,KAAAe,WAAA,GAAAV,OAAA;MACA,KAAAW,WAAA,GAAAhB,OAAA;MACA,KAAAiB,WAAA;MACA,KAAAoB,gBAAA,MAAApB,WAAA;IACA;IAEAoB,gBAAA,WAAAA,iBAAAC,EAAA,EAAAC,EAAA;MACA,KAAAC,SAAA;QACA,KAAAF,EAAA,IAAAA,EAAA,CAAAG,MAAA;UACA,IAAAC,GAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,GAAA,CAAAG,GAAA,GAAA/C,SAAA;UACA4C,GAAA,CAAApB,KAAA;UACA,IAAAwB,OAAA,MAAAC,MAAA,CAAAR,EAAA;UACAI,QAAA,CAAAK,aAAA,CAAAF,OAAA,EAAAG,SAAA;UACAN,QAAA,CAAAK,aAAA,CAAAF,OAAA,EAAAI,WAAA,CAAAR,GAAA;QACA;MACA;IACA;IACAS,UAAA,WAAAA,WAAA;MACA,KAAAhB,QAAA,CAAAC,OAAA;IACA;EAKA;AACA", "ignoreList": []}]}