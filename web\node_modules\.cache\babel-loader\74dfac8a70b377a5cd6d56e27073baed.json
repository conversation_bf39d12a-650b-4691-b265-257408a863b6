{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\testSuites\\userExperienceTestSuite.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\testSuites\\userExperienceTestSuite.js", "mtime": 1753521022957}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 用户体验测试套件\n * 测试界面操作流畅性、响应式设计、收集用户反馈、优化用户体验\n */\n\n/**\n * 用户体验测试套件类\n */\nexport var UserExperienceTestSuite = /*#__PURE__*/function () {\n  function UserExperienceTestSuite() {\n    _classCallCheck(this, UserExperienceTestSuite);\n    this.name = '用户体验测试套件';\n    this.description = '测试系统的用户界面和交互体验';\n    this.interactionEvents = [];\n    this.performanceEntries = [];\n  }\n\n  /**\n   * 测试套件初始化\n   */\n  return _createClass(UserExperienceTestSuite, [{\n    key: \"setup\",\n    value: (function () {\n      var _setup = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _this = this;\n        var observer;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              console.log('初始化用户体验测试套件...');\n\n              // 清空事件记录\n              this.interactionEvents = [];\n              this.performanceEntries = [];\n\n              // 设置性能观察器\n              if (window.PerformanceObserver) {\n                try {\n                  observer = new PerformanceObserver(function (list) {\n                    var _this$performanceEntr;\n                    (_this$performanceEntr = _this.performanceEntries).push.apply(_this$performanceEntr, _toConsumableArray(list.getEntries()));\n                  });\n                  observer.observe({\n                    entryTypes: ['measure', 'navigation', 'paint']\n                  });\n                } catch (error) {\n                  console.warn('Performance Observer setup failed:', error);\n                }\n              }\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function setup() {\n        return _setup.apply(this, arguments);\n      }\n      return setup;\n    }()\n    /**\n     * 测试套件清理\n     */\n    )\n  }, {\n    key: \"teardown\",\n    value: (function () {\n      var _teardown = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              console.log('清理用户体验测试套件...');\n            case 1:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      function teardown() {\n        return _teardown.apply(this, arguments);\n      }\n      return teardown;\n    }()\n    /**\n     * 获取测试用例列表\n     * @returns {Array} 测试用例数组\n     */\n    )\n  }, {\n    key: \"getTestCases\",\n    value: function getTestCases() {\n      return [\n      // 界面流畅性测试\n      {\n        name: 'interface_smoothness',\n        description: '测试界面操作流畅性',\n        run: this.testInterfaceSmoothness.bind(this)\n      }, {\n        name: 'scroll_performance',\n        description: '测试滚动性能',\n        run: this.testScrollPerformance.bind(this)\n      }, {\n        name: 'animation_performance',\n        description: '测试动画性能',\n        run: this.testAnimationPerformance.bind(this)\n      },\n      // 响应式设计测试\n      {\n        name: 'responsive_design_mobile',\n        description: '测试移动端响应式设计',\n        run: this.testResponsiveDesignMobile.bind(this)\n      }, {\n        name: 'responsive_design_tablet',\n        description: '测试平板端响应式设计',\n        run: this.testResponsiveDesignTablet.bind(this)\n      }, {\n        name: 'responsive_design_desktop',\n        description: '测试桌面端响应式设计',\n        run: this.testResponsiveDesignDesktop.bind(this)\n      },\n      // 交互体验测试\n      {\n        name: 'button_interaction',\n        description: '测试按钮交互体验',\n        run: this.testButtonInteraction.bind(this)\n      }, {\n        name: 'form_interaction',\n        description: '测试表单交互体验',\n        run: this.testFormInteraction.bind(this)\n      }, {\n        name: 'modal_interaction',\n        description: '测试模态框交互体验',\n        run: this.testModalInteraction.bind(this)\n      },\n      // 可访问性测试\n      {\n        name: 'keyboard_navigation',\n        description: '测试键盘导航',\n        run: this.testKeyboardNavigation.bind(this)\n      }, {\n        name: 'screen_reader_compatibility',\n        description: '测试屏幕阅读器兼容性',\n        run: this.testScreenReaderCompatibility.bind(this)\n      }, {\n        name: 'color_contrast',\n        description: '测试颜色对比度',\n        run: this.testColorContrast.bind(this)\n      },\n      // 加载体验测试\n      {\n        name: 'page_load_experience',\n        description: '测试页面加载体验',\n        run: this.testPageLoadExperience.bind(this)\n      }, {\n        name: 'content_loading_feedback',\n        description: '测试内容加载反馈',\n        run: this.testContentLoadingFeedback.bind(this)\n      },\n      // 错误处理体验测试\n      {\n        name: 'error_message_clarity',\n        description: '测试错误信息清晰度',\n        run: this.testErrorMessageClarity.bind(this)\n      }, {\n        name: 'error_recovery',\n        description: '测试错误恢复机制',\n        run: this.testErrorRecovery.bind(this)\n      }];\n    }\n\n    /**\n     * 测试界面操作流畅性\n     */\n  }, {\n    key: \"testInterfaceSmoothness\",\n    value: (function () {\n      var _testInterfaceSmoothness = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var startTime, testContainer, changeCount, changeTimes, i, changeStart, changeEnd, endTime, totalTime, averageChangeTime;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              startTime = performance.now();\n              _context3.prev = 1;\n              // 创建测试界面元素\n              testContainer = document.createElement('div');\n              testContainer.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: 300px;\\n        height: 200px;\\n        background: #f0f0f0;\\n        transition: all 0.3s ease;\\n      \";\n              document.body.appendChild(testContainer);\n\n              // 测试多次状态变化\n              changeCount = 20;\n              changeTimes = [];\n              i = 0;\n            case 8:\n              if (!(i < changeCount)) {\n                _context3.next = 20;\n                break;\n              }\n              changeStart = performance.now(); // 改变样式\n              testContainer.style.backgroundColor = \"hsl(\".concat(i * 18, \", 50%, 50%)\");\n              testContainer.style.transform = \"translateX(\".concat(i * 5, \"px)\");\n\n              // 强制重排\n              testContainer.offsetHeight;\n              changeEnd = performance.now();\n              changeTimes.push(changeEnd - changeStart);\n              _context3.next = 17;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 10);\n              });\n            case 17:\n              i++;\n              _context3.next = 8;\n              break;\n            case 20:\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageChangeTime = changeTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / changeTimes.length; // 清理\n              document.body.removeChild(testContainer);\n              return _context3.abrupt(\"return\", {\n                success: averageChangeTime < 16.67,\n                // 60fps = 16.67ms per frame\n                metrics: {\n                  changeCount: changeCount,\n                  totalTime: totalTime,\n                  averageChangeTime: averageChangeTime,\n                  maxChangeTime: Math.max.apply(Math, changeTimes),\n                  minChangeTime: Math.min.apply(Math, changeTimes),\n                  fps: 1000 / averageChangeTime\n                }\n              });\n            case 27:\n              _context3.prev = 27;\n              _context3.t0 = _context3[\"catch\"](1);\n              return _context3.abrupt(\"return\", {\n                success: false,\n                error: _context3.t0.message\n              });\n            case 30:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[1, 27]]);\n      }));\n      function testInterfaceSmoothness() {\n        return _testInterfaceSmoothness.apply(this, arguments);\n      }\n      return testInterfaceSmoothness;\n    }()\n    /**\n     * 测试滚动性能\n     */\n    )\n  }, {\n    key: \"testScrollPerformance\",\n    value: (function () {\n      var _testScrollPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var startTime, scrollContainer, contentHeight, content, scrollSteps, scrollTimes, i, scrollStart, scrollTop, scrollEnd, endTime, totalTime, averageScrollTime;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              startTime = performance.now();\n              _context4.prev = 1;\n              // 创建可滚动容器\n              scrollContainer = document.createElement('div');\n              scrollContainer.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: 300px;\\n        height: 200px;\\n        overflow-y: auto;\\n        border: 1px solid #ccc;\\n      \";\n\n              // 添加大量内容\n              contentHeight = 2000;\n              content = document.createElement('div');\n              content.style.height = \"\".concat(contentHeight, \"px\");\n              content.style.background = 'linear-gradient(to bottom, #ff0000, #0000ff)';\n              scrollContainer.appendChild(content);\n              document.body.appendChild(scrollContainer);\n\n              // 模拟滚动\n              scrollSteps = 20;\n              scrollTimes = [];\n              i = 0;\n            case 13:\n              if (!(i < scrollSteps)) {\n                _context4.next = 25;\n                break;\n              }\n              scrollStart = performance.now();\n              scrollTop = i / scrollSteps * (contentHeight - 200);\n              scrollContainer.scrollTop = scrollTop;\n\n              // 强制重排\n              scrollContainer.offsetHeight;\n              scrollEnd = performance.now();\n              scrollTimes.push(scrollEnd - scrollStart);\n              _context4.next = 22;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 5);\n              });\n            case 22:\n              i++;\n              _context4.next = 13;\n              break;\n            case 25:\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageScrollTime = scrollTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / scrollTimes.length; // 清理\n              document.body.removeChild(scrollContainer);\n              return _context4.abrupt(\"return\", {\n                success: averageScrollTime < 10,\n                // 滚动时间小于10ms\n                metrics: {\n                  scrollSteps: scrollSteps,\n                  totalTime: totalTime,\n                  averageScrollTime: averageScrollTime,\n                  maxScrollTime: Math.max.apply(Math, scrollTimes),\n                  contentHeight: contentHeight\n                }\n              });\n            case 32:\n              _context4.prev = 32;\n              _context4.t0 = _context4[\"catch\"](1);\n              return _context4.abrupt(\"return\", {\n                success: false,\n                error: _context4.t0.message\n              });\n            case 35:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[1, 32]]);\n      }));\n      function testScrollPerformance() {\n        return _testScrollPerformance.apply(this, arguments);\n      }\n      return testScrollPerformance;\n    }()\n    /**\n     * 测试动画性能\n     */\n    )\n  }, {\n    key: \"testAnimationPerformance\",\n    value: (function () {\n      var _testAnimationPerformance = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var startTime, animationElement, animationCount, animationTimes, i, animStart, animEnd, endTime, totalTime, averageAnimationTime;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              startTime = performance.now();\n              _context5.prev = 1;\n              // 创建动画元素\n              animationElement = document.createElement('div');\n              animationElement.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: 50px;\\n        height: 50px;\\n        background: #ff6b6b;\\n        transition: transform 0.3s ease;\\n      \";\n              document.body.appendChild(animationElement);\n\n              // 执行多个动画\n              animationCount = 10;\n              animationTimes = [];\n              i = 0;\n            case 8:\n              if (!(i < animationCount)) {\n                _context5.next = 18;\n                break;\n              }\n              animStart = performance.now(); // 触发动画\n              animationElement.style.transform = \"translateX(\".concat(i * 20, \"px) rotate(\").concat(i * 36, \"deg)\");\n\n              // 等待动画完成\n              _context5.next = 13;\n              return new Promise(function (resolve) {\n                animationElement.addEventListener('transitionend', resolve, {\n                  once: true\n                });\n                setTimeout(resolve, 400); // 备用超时\n              });\n            case 13:\n              animEnd = performance.now();\n              animationTimes.push(animEnd - animStart);\n            case 15:\n              i++;\n              _context5.next = 8;\n              break;\n            case 18:\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageAnimationTime = animationTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / animationTimes.length; // 清理\n              document.body.removeChild(animationElement);\n              return _context5.abrupt(\"return\", {\n                success: averageAnimationTime < 500,\n                // 动画时间小于500ms\n                metrics: {\n                  animationCount: animationCount,\n                  totalTime: totalTime,\n                  averageAnimationTime: averageAnimationTime,\n                  maxAnimationTime: Math.max.apply(Math, animationTimes)\n                }\n              });\n            case 25:\n              _context5.prev = 25;\n              _context5.t0 = _context5[\"catch\"](1);\n              return _context5.abrupt(\"return\", {\n                success: false,\n                error: _context5.t0.message\n              });\n            case 28:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[1, 25]]);\n      }));\n      function testAnimationPerformance() {\n        return _testAnimationPerformance.apply(this, arguments);\n      }\n      return testAnimationPerformance;\n    }()\n    /**\n     * 测试移动端响应式设计\n     */\n    )\n  }, {\n    key: \"testResponsiveDesignMobile\",\n    value: (function () {\n      var _testResponsiveDesignMobile = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              return _context6.abrupt(\"return\", this.testResponsiveDesign(375, 667, 'mobile'));\n            case 1:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, this);\n      }));\n      function testResponsiveDesignMobile() {\n        return _testResponsiveDesignMobile.apply(this, arguments);\n      }\n      return testResponsiveDesignMobile;\n    }()\n    /**\n     * 测试平板端响应式设计\n     */\n    )\n  }, {\n    key: \"testResponsiveDesignTablet\",\n    value: (function () {\n      var _testResponsiveDesignTablet = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              return _context7.abrupt(\"return\", this.testResponsiveDesign(768, 1024, 'tablet'));\n            case 1:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, this);\n      }));\n      function testResponsiveDesignTablet() {\n        return _testResponsiveDesignTablet.apply(this, arguments);\n      }\n      return testResponsiveDesignTablet;\n    }()\n    /**\n     * 测试桌面端响应式设计\n     */\n    )\n  }, {\n    key: \"testResponsiveDesignDesktop\",\n    value: (function () {\n      var _testResponsiveDesignDesktop = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              return _context8.abrupt(\"return\", this.testResponsiveDesign(1920, 1080, 'desktop'));\n            case 1:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8, this);\n      }));\n      function testResponsiveDesignDesktop() {\n        return _testResponsiveDesignDesktop.apply(this, arguments);\n      }\n      return testResponsiveDesignDesktop;\n    }()\n    /**\n     * 测试响应式设计\n     * @param {Number} width 屏幕宽度\n     * @param {Number} height 屏幕高度\n     * @param {String} deviceType 设备类型\n     */\n    )\n  }, {\n    key: \"testResponsiveDesign\",\n    value: (function () {\n      var _testResponsiveDesign = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee9(width, height, deviceType) {\n        var startTime, originalWidth, originalHeight, testContainer, gridElement, gridItems, gridComputedStyle, gridTemplateColumns, columnCount, h1Element, h1ComputedStyle, fontSize, endTime, testTime, score;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              startTime = performance.now();\n              _context9.prev = 1;\n              // 保存原始视口大小\n              originalWidth = window.innerWidth;\n              originalHeight = window.innerHeight; // 创建测试容器\n              testContainer = document.createElement('div');\n              testContainer.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: \".concat(width, \"px;\\n        height: \").concat(height, \"px;\\n        border: 1px solid #ccc;\\n        overflow: hidden;\\n      \");\n\n              // 添加响应式测试内容\n              testContainer.innerHTML = \"\\n        <div class=\\\"responsive-test\\\" style=\\\"\\n          width: 100%;\\n          padding: 20px;\\n          box-sizing: border-box;\\n        \\\">\\n          <h1 style=\\\"font-size: 2rem; margin-bottom: 1rem;\\\">\\u54CD\\u5E94\\u5F0F\\u6D4B\\u8BD5</h1>\\n          <div class=\\\"grid\\\" style=\\\"\\n            display: grid;\\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n            gap: 1rem;\\n          \\\">\\n            <div style=\\\"background: #f0f0f0; padding: 1rem;\\\">\\u9879\\u76EE1</div>\\n            <div style=\\\"background: #e0e0e0; padding: 1rem;\\\">\\u9879\\u76EE2</div>\\n            <div style=\\\"background: #d0d0d0; padding: 1rem;\\\">\\u9879\\u76EE3</div>\\n          </div>\\n        </div>\\n      \";\n              document.body.appendChild(testContainer);\n\n              // 检查布局\n              gridElement = testContainer.querySelector('.grid');\n              gridItems = testContainer.querySelectorAll('.grid > div'); // 计算网格列数\n              gridComputedStyle = window.getComputedStyle(gridElement);\n              gridTemplateColumns = gridComputedStyle.gridTemplateColumns;\n              columnCount = gridTemplateColumns.split(' ').length; // 检查字体大小\n              h1Element = testContainer.querySelector('h1');\n              h1ComputedStyle = window.getComputedStyle(h1Element);\n              fontSize = parseFloat(h1ComputedStyle.fontSize);\n              endTime = performance.now();\n              testTime = endTime - startTime; // 清理\n              document.body.removeChild(testContainer);\n\n              // 评估响应式效果\n              score = 0;\n              if (deviceType === 'mobile' && columnCount <= 2) score += 30;\n              if (deviceType === 'tablet' && columnCount <= 3) score += 30;\n              if (deviceType === 'desktop' && columnCount >= 3) score += 30;\n              if (fontSize >= 16) score += 20; // 字体大小合适\n              if (gridItems.length === 3) score += 20; // 所有项目都显示\n              return _context9.abrupt(\"return\", {\n                success: score >= 80,\n                metrics: {\n                  deviceType: deviceType,\n                  width: width,\n                  height: height,\n                  columnCount: columnCount,\n                  fontSize: fontSize,\n                  score: score,\n                  testTime: testTime\n                }\n              });\n            case 28:\n              _context9.prev = 28;\n              _context9.t0 = _context9[\"catch\"](1);\n              return _context9.abrupt(\"return\", {\n                success: false,\n                error: _context9.t0.message\n              });\n            case 31:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9, null, [[1, 28]]);\n      }));\n      function testResponsiveDesign(_x, _x2, _x3) {\n        return _testResponsiveDesign.apply(this, arguments);\n      }\n      return testResponsiveDesign;\n    }()\n    /**\n     * 测试按钮交互体验\n     */\n    )\n  }, {\n    key: \"testButtonInteraction\",\n    value: (function () {\n      var _testButtonInteraction = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var startTime, button, interactions, hoverStart, hoverEnd, clickStart, clickEnd, focusStart, focusEnd, endTime, totalTime, averageInteractionTime;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              startTime = performance.now();\n              _context10.prev = 1;\n              // 创建测试按钮\n              button = document.createElement('button');\n              button.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        padding: 10px 20px;\\n        background: #1890ff;\\n        color: white;\\n        border: none;\\n        border-radius: 4px;\\n        cursor: pointer;\\n        transition: all 0.2s ease;\\n      \";\n              button.textContent = '测试按钮';\n              document.body.appendChild(button);\n\n              // 测试交互状态\n              interactions = []; // 测试hover效果\n              hoverStart = performance.now();\n              button.style.background = '#40a9ff';\n              button.offsetHeight; // 强制重排\n              hoverEnd = performance.now();\n              interactions.push({\n                type: 'hover',\n                time: hoverEnd - hoverStart\n              });\n\n              // 测试点击效果\n              clickStart = performance.now();\n              button.style.transform = 'scale(0.95)';\n              button.offsetHeight;\n              clickEnd = performance.now();\n              interactions.push({\n                type: 'click',\n                time: clickEnd - clickStart\n              });\n\n              // 测试焦点效果\n              focusStart = performance.now();\n              button.style.outline = '2px solid #1890ff';\n              button.offsetHeight;\n              focusEnd = performance.now();\n              interactions.push({\n                type: 'focus',\n                time: focusEnd - focusStart\n              });\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(button);\n              averageInteractionTime = interactions.reduce(function (sum, i) {\n                return sum + i.time;\n              }, 0) / interactions.length;\n              return _context10.abrupt(\"return\", {\n                success: averageInteractionTime < 50,\n                // 交互响应时间小于50ms\n                metrics: {\n                  totalTime: totalTime,\n                  averageInteractionTime: averageInteractionTime,\n                  interactions: interactions\n                }\n              });\n            case 29:\n              _context10.prev = 29;\n              _context10.t0 = _context10[\"catch\"](1);\n              return _context10.abrupt(\"return\", {\n                success: false,\n                error: _context10.t0.message\n              });\n            case 32:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10, null, [[1, 29]]);\n      }));\n      function testButtonInteraction() {\n        return _testButtonInteraction.apply(this, arguments);\n      }\n      return testButtonInteraction;\n    }()\n    /**\n     * 测试表单交互体验\n     */\n    )\n  }, {\n    key: \"testFormInteraction\",\n    value: (function () {\n      var _testFormInteraction = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var startTime, form, inputs, interactionTimes, _iterator, _step, input, interactionStart, interactionEnd, endTime, totalTime, averageInteractionTime;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              startTime = performance.now();\n              _context11.prev = 1;\n              // 创建测试表单\n              form = document.createElement('form');\n              form.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: 300px;\\n        padding: 20px;\\n        background: white;\\n        border: 1px solid #ccc;\\n      \";\n              form.innerHTML = \"\\n        <input type=\\\"text\\\" placeholder=\\\"\\u7528\\u6237\\u540D\\\" style=\\\"width: 100%; margin-bottom: 10px; padding: 8px;\\\">\\n        <input type=\\\"password\\\" placeholder=\\\"\\u5BC6\\u7801\\\" style=\\\"width: 100%; margin-bottom: 10px; padding: 8px;\\\">\\n        <select style=\\\"width: 100%; margin-bottom: 10px; padding: 8px;\\\">\\n          <option>\\u9009\\u98791</option>\\n          <option>\\u9009\\u98792</option>\\n        </select>\\n        <textarea placeholder=\\\"\\u5907\\u6CE8\\\" style=\\\"width: 100%; margin-bottom: 10px; padding: 8px; height: 60px;\\\"></textarea>\\n        <button type=\\\"submit\\\" style=\\\"width: 100%; padding: 10px; background: #1890ff; color: white; border: none;\\\">\\u63D0\\u4EA4</button>\\n      \";\n              document.body.appendChild(form);\n\n              // 测试表单元素交互\n              inputs = form.querySelectorAll('input, select, textarea');\n              interactionTimes = [];\n              _iterator = _createForOfIteratorHelper(inputs);\n              _context11.prev = 9;\n              _iterator.s();\n            case 11:\n              if ((_step = _iterator.n()).done) {\n                _context11.next = 24;\n                break;\n              }\n              input = _step.value;\n              interactionStart = performance.now(); // 模拟焦点\n              input.style.borderColor = '#1890ff';\n              input.style.outline = '2px solid rgba(24, 144, 255, 0.2)';\n\n              // 模拟输入\n              if (input.type === 'text') {\n                input.value = '测试用户';\n              } else if (input.type === 'password') {\n                input.value = '123456';\n              } else if (input.tagName === 'TEXTAREA') {\n                input.value = '测试备注内容';\n              }\n              input.offsetHeight; // 强制重排\n              interactionEnd = performance.now();\n              interactionTimes.push(interactionEnd - interactionStart);\n              _context11.next = 22;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 10);\n              });\n            case 22:\n              _context11.next = 11;\n              break;\n            case 24:\n              _context11.next = 29;\n              break;\n            case 26:\n              _context11.prev = 26;\n              _context11.t0 = _context11[\"catch\"](9);\n              _iterator.e(_context11.t0);\n            case 29:\n              _context11.prev = 29;\n              _iterator.f();\n              return _context11.finish(29);\n            case 32:\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(form);\n              averageInteractionTime = interactionTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / interactionTimes.length;\n              return _context11.abrupt(\"return\", {\n                success: averageInteractionTime < 30,\n                // 表单交互时间小于30ms\n                metrics: {\n                  totalTime: totalTime,\n                  inputCount: inputs.length,\n                  averageInteractionTime: averageInteractionTime,\n                  maxInteractionTime: Math.max.apply(Math, interactionTimes)\n                }\n              });\n            case 39:\n              _context11.prev = 39;\n              _context11.t1 = _context11[\"catch\"](1);\n              return _context11.abrupt(\"return\", {\n                success: false,\n                error: _context11.t1.message\n              });\n            case 42:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[1, 39], [9, 26, 29, 32]]);\n      }));\n      function testFormInteraction() {\n        return _testFormInteraction.apply(this, arguments);\n      }\n      return testFormInteraction;\n    }()\n    /**\n     * 测试模态框交互体验\n     */\n    )\n  }, {\n    key: \"testModalInteraction\",\n    value: (function () {\n      var _testModalInteraction = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var startTime, modal, modalContent, openStart, openEnd, openTime, closeStart, closeEnd, closeTime, endTime, totalTime;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              startTime = performance.now();\n              _context12.prev = 1;\n              // 创建模态框\n              modal = document.createElement('div');\n              modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0, 0, 0, 0.5);\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 9999;\\n        opacity: 0;\\n        transition: opacity 0.3s ease;\\n      \";\n              modalContent = document.createElement('div');\n              modalContent.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 8px;\\n        max-width: 400px;\\n        width: 90%;\\n        transform: scale(0.9);\\n        transition: transform 0.3s ease;\\n      \";\n              modalContent.innerHTML = \"\\n        <h3>\\u6D4B\\u8BD5\\u6A21\\u6001\\u6846</h3>\\n        <p>\\u8FD9\\u662F\\u4E00\\u4E2A\\u6D4B\\u8BD5\\u6A21\\u6001\\u6846\\u7684\\u5185\\u5BB9\\u3002</p>\\n        <button id=\\\"modal-close\\\" style=\\\"padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px;\\\">\\u5173\\u95ED</button>\\n      \";\n              modal.appendChild(modalContent);\n              document.body.appendChild(modal);\n\n              // 测试打开动画\n              openStart = performance.now();\n              modal.style.opacity = '1';\n              modalContent.style.transform = 'scale(1)';\n              modal.offsetHeight; // 强制重排\n              _context12.next = 15;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 300);\n              });\n            case 15:\n              openEnd = performance.now();\n              openTime = openEnd - openStart; // 测试关闭动画\n              closeStart = performance.now();\n              modal.style.opacity = '0';\n              modalContent.style.transform = 'scale(0.9)';\n              modal.offsetHeight;\n              _context12.next = 23;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 300);\n              });\n            case 23:\n              closeEnd = performance.now();\n              closeTime = closeEnd - closeStart;\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(modal);\n              return _context12.abrupt(\"return\", {\n                success: openTime < 400 && closeTime < 400,\n                // 动画时间合理\n                metrics: {\n                  totalTime: totalTime,\n                  openTime: openTime,\n                  closeTime: closeTime,\n                  animationSmooth: openTime < 400 && closeTime < 400\n                }\n              });\n            case 31:\n              _context12.prev = 31;\n              _context12.t0 = _context12[\"catch\"](1);\n              return _context12.abrupt(\"return\", {\n                success: false,\n                error: _context12.t0.message\n              });\n            case 34:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12, null, [[1, 31]]);\n      }));\n      function testModalInteraction() {\n        return _testModalInteraction.apply(this, arguments);\n      }\n      return testModalInteraction;\n    }()\n    /**\n     * 测试键盘导航\n     */\n    )\n  }, {\n    key: \"testKeyboardNavigation\",\n    value: (function () {\n      var _testKeyboardNavigation = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13() {\n        var startTime, container, focusableElements, navigationTimes, i, navStart, element, navEnd, endTime, totalTime, averageNavigationTime;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1) switch (_context13.prev = _context13.next) {\n            case 0:\n              startTime = performance.now();\n              _context13.prev = 1;\n              // 创建可导航元素\n              container = document.createElement('div');\n              container.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        padding: 20px;\\n      \";\n              container.innerHTML = \"\\n        <button tabindex=\\\"1\\\">\\u6309\\u94AE1</button>\\n        <input type=\\\"text\\\" tabindex=\\\"2\\\" placeholder=\\\"\\u8F93\\u5165\\u6846\\\">\\n        <select tabindex=\\\"3\\\">\\n          <option>\\u9009\\u98791</option>\\n          <option>\\u9009\\u98792</option>\\n        </select>\\n        <a href=\\\"#\\\" tabindex=\\\"4\\\">\\u94FE\\u63A5</a>\\n        <button tabindex=\\\"5\\\">\\u6309\\u94AE2</button>\\n      \";\n              document.body.appendChild(container);\n\n              // 测试Tab导航\n              focusableElements = container.querySelectorAll('[tabindex]');\n              navigationTimes = [];\n              i = 0;\n            case 9:\n              if (!(i < focusableElements.length)) {\n                _context13.next = 22;\n                break;\n              }\n              navStart = performance.now();\n              element = focusableElements[i];\n              element.focus();\n              element.style.outline = '2px solid #1890ff';\n              element.offsetHeight; // 强制重排\n              navEnd = performance.now();\n              navigationTimes.push(navEnd - navStart);\n              _context13.next = 19;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 50);\n              });\n            case 19:\n              i++;\n              _context13.next = 9;\n              break;\n            case 22:\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(container);\n              averageNavigationTime = navigationTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / navigationTimes.length;\n              return _context13.abrupt(\"return\", {\n                success: averageNavigationTime < 20,\n                // 导航响应时间小于20ms\n                metrics: {\n                  totalTime: totalTime,\n                  focusableElementCount: focusableElements.length,\n                  averageNavigationTime: averageNavigationTime,\n                  navigationTimes: navigationTimes\n                }\n              });\n            case 29:\n              _context13.prev = 29;\n              _context13.t0 = _context13[\"catch\"](1);\n              return _context13.abrupt(\"return\", {\n                success: false,\n                error: _context13.t0.message\n              });\n            case 32:\n            case \"end\":\n              return _context13.stop();\n          }\n        }, _callee13, null, [[1, 29]]);\n      }));\n      function testKeyboardNavigation() {\n        return _testKeyboardNavigation.apply(this, arguments);\n      }\n      return testKeyboardNavigation;\n    }()\n    /**\n     * 测试屏幕阅读器兼容性\n     */\n    )\n  }, {\n    key: \"testScreenReaderCompatibility\",\n    value: (function () {\n      var _testScreenReaderCompatibility = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee14() {\n        var startTime, container, accessibilityChecks, results, passedChecks, totalChecks, endTime, totalTime;\n        return _regeneratorRuntime().wrap(function _callee14$(_context14) {\n          while (1) switch (_context14.prev = _context14.next) {\n            case 0:\n              startTime = performance.now();\n              _context14.prev = 1;\n              // 创建测试内容\n              container = document.createElement('div');\n              container.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        padding: 20px;\\n      \";\n              container.innerHTML = \"\\n        <h1>\\u4E3B\\u6807\\u9898</h1>\\n        <nav aria-label=\\\"\\u4E3B\\u5BFC\\u822A\\\">\\n          <ul>\\n            <li><a href=\\\"#\\\" aria-label=\\\"\\u9996\\u9875\\u94FE\\u63A5\\\">\\u9996\\u9875</a></li>\\n            <li><a href=\\\"#\\\" aria-label=\\\"\\u5173\\u4E8E\\u94FE\\u63A5\\\">\\u5173\\u4E8E</a></li>\\n          </ul>\\n        </nav>\\n        <main>\\n          <article>\\n            <h2>\\u6587\\u7AE0\\u6807\\u9898</h2>\\n            <p>\\u6587\\u7AE0\\u5185\\u5BB9\\u6BB5\\u843D\\u3002</p>\\n            <img src=\\\"#\\\" alt=\\\"\\u63CF\\u8FF0\\u6027\\u66FF\\u4EE3\\u6587\\u672C\\\">\\n          </article>\\n        </main>\\n        <button aria-label=\\\"\\u63D0\\u4EA4\\u8868\\u5355\\\" aria-describedby=\\\"submit-help\\\">\\u63D0\\u4EA4</button>\\n        <div id=\\\"submit-help\\\">\\u70B9\\u51FB\\u6B64\\u6309\\u94AE\\u63D0\\u4EA4\\u8868\\u5355</div>\\n      \";\n              document.body.appendChild(container);\n\n              // 检查可访问性属性\n              accessibilityChecks = [{\n                name: 'headings',\n                check: function check() {\n                  return container.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0;\n                }\n              }, {\n                name: 'alt_text',\n                check: function check() {\n                  var images = container.querySelectorAll('img');\n                  return Array.from(images).every(function (img) {\n                    return img.hasAttribute('alt');\n                  });\n                }\n              }, {\n                name: 'aria_labels',\n                check: function check() {\n                  return container.querySelectorAll('[aria-label]').length > 0;\n                }\n              }, {\n                name: 'aria_describedby',\n                check: function check() {\n                  return container.querySelectorAll('[aria-describedby]').length > 0;\n                }\n              }, {\n                name: 'semantic_elements',\n                check: function check() {\n                  var semanticElements = container.querySelectorAll('nav, main, article, section, aside, header, footer');\n                  return semanticElements.length > 0;\n                }\n              }];\n              results = accessibilityChecks.map(function (check) {\n                return {\n                  name: check.name,\n                  passed: check.check()\n                };\n              });\n              passedChecks = results.filter(function (r) {\n                return r.passed;\n              }).length;\n              totalChecks = results.length;\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(container);\n              return _context14.abrupt(\"return\", {\n                success: passedChecks >= totalChecks * 0.8,\n                // 80%的检查通过\n                metrics: {\n                  totalTime: totalTime,\n                  passedChecks: passedChecks,\n                  totalChecks: totalChecks,\n                  accessibilityScore: Math.round(passedChecks / totalChecks * 100),\n                  results: results\n                }\n              });\n            case 16:\n              _context14.prev = 16;\n              _context14.t0 = _context14[\"catch\"](1);\n              return _context14.abrupt(\"return\", {\n                success: false,\n                error: _context14.t0.message\n              });\n            case 19:\n            case \"end\":\n              return _context14.stop();\n          }\n        }, _callee14, null, [[1, 16]]);\n      }));\n      function testScreenReaderCompatibility() {\n        return _testScreenReaderCompatibility.apply(this, arguments);\n      }\n      return testScreenReaderCompatibility;\n    }()\n    /**\n     * 测试颜色对比度\n     */\n    )\n  }, {\n    key: \"testColorContrast\",\n    value: (function () {\n      var _testColorContrast = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15() {\n        var startTime, container, colorTests, contrastResults, _i, _colorTests, test, element, contrast, endTime, totalTime, wcagAACompliant, totalTests;\n        return _regeneratorRuntime().wrap(function _callee15$(_context15) {\n          while (1) switch (_context15.prev = _context15.next) {\n            case 0:\n              startTime = performance.now();\n              _context15.prev = 1;\n              // 创建测试元素\n              container = document.createElement('div');\n              container.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        padding: 20px;\\n      \";\n              colorTests = [{\n                bg: '#ffffff',\n                fg: '#000000',\n                name: '黑白对比'\n              }, {\n                bg: '#1890ff',\n                fg: '#ffffff',\n                name: '蓝白对比'\n              }, {\n                bg: '#f5f5f5',\n                fg: '#333333',\n                name: '灰色对比'\n              }, {\n                bg: '#ff4d4f',\n                fg: '#ffffff',\n                name: '红白对比'\n              }, {\n                bg: '#52c41a',\n                fg: '#ffffff',\n                name: '绿白对比'\n              }];\n              contrastResults = [];\n              for (_i = 0, _colorTests = colorTests; _i < _colorTests.length; _i++) {\n                test = _colorTests[_i];\n                element = document.createElement('div');\n                element.style.cssText = \"\\n          background-color: \".concat(test.bg, \";\\n          color: \").concat(test.fg, \";\\n          padding: 10px;\\n          margin: 5px 0;\\n        \");\n                element.textContent = \"\\u6D4B\\u8BD5\\u6587\\u672C - \".concat(test.name);\n                container.appendChild(element);\n\n                // 计算对比度（简化版本）\n                contrast = this.calculateColorContrast(test.bg, test.fg);\n                contrastResults.push({\n                  name: test.name,\n                  background: test.bg,\n                  foreground: test.fg,\n                  contrast: contrast,\n                  wcagAA: contrast >= 4.5,\n                  wcagAAA: contrast >= 7\n                });\n              }\n              document.body.appendChild(container);\n\n              // 强制渲染\n              container.offsetHeight;\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(container);\n              wcagAACompliant = contrastResults.filter(function (r) {\n                return r.wcagAA;\n              }).length;\n              totalTests = contrastResults.length;\n              return _context15.abrupt(\"return\", {\n                success: wcagAACompliant >= totalTests * 0.8,\n                // 80%符合WCAG AA标准\n                metrics: {\n                  totalTime: totalTime,\n                  totalTests: totalTests,\n                  wcagAACompliant: wcagAACompliant,\n                  wcagAAACompliant: contrastResults.filter(function (r) {\n                    return r.wcagAAA;\n                  }).length,\n                  contrastResults: contrastResults\n                }\n              });\n            case 17:\n              _context15.prev = 17;\n              _context15.t0 = _context15[\"catch\"](1);\n              return _context15.abrupt(\"return\", {\n                success: false,\n                error: _context15.t0.message\n              });\n            case 20:\n            case \"end\":\n              return _context15.stop();\n          }\n        }, _callee15, this, [[1, 17]]);\n      }));\n      function testColorContrast() {\n        return _testColorContrast.apply(this, arguments);\n      }\n      return testColorContrast;\n    }()\n    /**\n     * 测试页面加载体验\n     */\n    )\n  }, {\n    key: \"testPageLoadExperience\",\n    value: (function () {\n      var _testPageLoadExperience = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee16() {\n        var startTime, navigation, paint, metrics, endTime, totalTime, score;\n        return _regeneratorRuntime().wrap(function _callee16$(_context16) {\n          while (1) switch (_context16.prev = _context16.next) {\n            case 0:\n              startTime = performance.now();\n              _context16.prev = 1;\n              // 获取导航时间信息\n              navigation = performance.getEntriesByType('navigation')[0];\n              paint = performance.getEntriesByType('paint');\n              metrics = {\n                domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,\n                loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,\n                firstPaint: paint.find(function (p) {\n                  return p.name === 'first-paint';\n                }) && paint.find(function (p) {\n                  return p.name === 'first-paint';\n                }).startTime || 0,\n                firstContentfulPaint: paint.find(function (p) {\n                  return p.name === 'first-contentful-paint';\n                }) && paint.find(function (p) {\n                  return p.name === 'first-contentful-paint';\n                }).startTime || 0\n              };\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 评估加载体验\n              score = 0;\n              if (metrics.firstPaint < 1000) score += 25; // 首次绘制小于1秒\n              if (metrics.firstContentfulPaint < 1500) score += 25; // 首次内容绘制小于1.5秒\n              if (metrics.domContentLoaded < 2000) score += 25; // DOM加载小于2秒\n              if (metrics.loadComplete < 3000) score += 25; // 完全加载小于3秒\n              return _context16.abrupt(\"return\", {\n                success: score >= 75,\n                // 75分以上\n                metrics: _objectSpread({\n                  totalTime: totalTime,\n                  score: score\n                }, metrics)\n              });\n            case 15:\n              _context16.prev = 15;\n              _context16.t0 = _context16[\"catch\"](1);\n              return _context16.abrupt(\"return\", {\n                success: false,\n                error: _context16.t0.message\n              });\n            case 18:\n            case \"end\":\n              return _context16.stop();\n          }\n        }, _callee16, null, [[1, 15]]);\n      }));\n      function testPageLoadExperience() {\n        return _testPageLoadExperience.apply(this, arguments);\n      }\n      return testPageLoadExperience;\n    }()\n    /**\n     * 测试内容加载反馈\n     */\n    )\n  }, {\n    key: \"testContentLoadingFeedback\",\n    value: (function () {\n      var _testContentLoadingFeedback = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee17() {\n        var startTime, loader, style, loadingSteps, stepTimes, _i2, _loadingSteps, step, stepStart, stepEnd, endTime, totalTime, averageStepTime;\n        return _regeneratorRuntime().wrap(function _callee17$(_context17) {\n          while (1) switch (_context17.prev = _context17.next) {\n            case 0:\n              startTime = performance.now();\n              _context17.prev = 1;\n              // 创建加载指示器\n              loader = document.createElement('div');\n              loader.style.cssText = \"\\n        position: absolute;\\n        top: -9999px;\\n        width: 40px;\\n        height: 40px;\\n        border: 4px solid #f3f3f3;\\n        border-top: 4px solid #1890ff;\\n        border-radius: 50%;\\n        animation: spin 1s linear infinite;\\n      \";\n\n              // 添加旋转动画\n              style = document.createElement('style');\n              style.textContent = \"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n      \";\n              document.head.appendChild(style);\n              document.body.appendChild(loader);\n\n              // 模拟加载过程\n              loadingSteps = [{\n                message: '正在加载...',\n                progress: 0\n              }, {\n                message: '加载中...',\n                progress: 30\n              }, {\n                message: '即将完成...',\n                progress: 70\n              }, {\n                message: '加载完成',\n                progress: 100\n              }];\n              stepTimes = [];\n              _i2 = 0, _loadingSteps = loadingSteps;\n            case 11:\n              if (!(_i2 < _loadingSteps.length)) {\n                _context17.next = 23;\n                break;\n              }\n              step = _loadingSteps[_i2];\n              stepStart = performance.now(); // 更新加载状态\n              loader.setAttribute('data-progress', step.progress);\n              loader.setAttribute('aria-label', step.message);\n              _context17.next = 18;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 200);\n              });\n            case 18:\n              stepEnd = performance.now();\n              stepTimes.push(stepEnd - stepStart);\n            case 20:\n              _i2++;\n              _context17.next = 11;\n              break;\n            case 23:\n              endTime = performance.now();\n              totalTime = endTime - startTime; // 清理\n              document.body.removeChild(loader);\n              document.head.removeChild(style);\n              averageStepTime = stepTimes.reduce(function (sum, time) {\n                return sum + time;\n              }, 0) / stepTimes.length;\n              return _context17.abrupt(\"return\", {\n                success: averageStepTime < 250,\n                // 每步小于250ms\n                metrics: {\n                  totalTime: totalTime,\n                  stepCount: loadingSteps.length,\n                  averageStepTime: averageStepTime,\n                  stepTimes: stepTimes\n                }\n              });\n            case 31:\n              _context17.prev = 31;\n              _context17.t0 = _context17[\"catch\"](1);\n              return _context17.abrupt(\"return\", {\n                success: false,\n                error: _context17.t0.message\n              });\n            case 34:\n            case \"end\":\n              return _context17.stop();\n          }\n        }, _callee17, null, [[1, 31]]);\n      }));\n      function testContentLoadingFeedback() {\n        return _testContentLoadingFeedback.apply(this, arguments);\n      }\n      return testContentLoadingFeedback;\n    }()\n    /**\n     * 测试错误信息清晰度\n     */\n    )\n  }, {\n    key: \"testErrorMessageClarity\",\n    value: (function () {\n      var _testErrorMessageClarity = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee18() {\n        var startTime, errorMessages, clarityScores, _i3, _errorMessages, error, score, endTime, totalTime, averageScore;\n        return _regeneratorRuntime().wrap(function _callee18$(_context18) {\n          while (1) switch (_context18.prev = _context18.next) {\n            case 0:\n              startTime = performance.now();\n              _context18.prev = 1;\n              // 创建错误信息测试\n              errorMessages = [{\n                type: 'validation',\n                message: '请输入有效的邮箱地址',\n                clarity: 'good'\n              }, {\n                type: 'network',\n                message: '网络连接失败，请检查网络设置',\n                clarity: 'good'\n              }, {\n                type: 'generic',\n                message: '操作失败',\n                clarity: 'poor'\n              }, {\n                type: 'detailed',\n                message: '文件上传失败：文件大小超过10MB限制',\n                clarity: 'excellent'\n              }];\n              clarityScores = [];\n              for (_i3 = 0, _errorMessages = errorMessages; _i3 < _errorMessages.length; _i3++) {\n                error = _errorMessages[_i3];\n                // 评估错误信息质量\n                score = 0; // 长度适中\n                if (error.message.length >= 10 && error.message.length <= 100) score += 25;\n\n                // 包含具体信息\n                if (error.message.includes('请') || error.message.includes('检查') || error.message.includes('：')) score += 25;\n\n                // 提供解决方案\n                if (error.message.includes('请') && (error.message.includes('检查') || error.message.includes('重试'))) score += 25;\n\n                // 语言友好\n                if (!error.message.includes('错误') || error.message.includes('失败')) score += 25;\n                clarityScores.push({\n                  type: error.type,\n                  message: error.message,\n                  score: score,\n                  clarity: error.clarity\n                });\n              }\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              averageScore = clarityScores.reduce(function (sum, item) {\n                return sum + item.score;\n              }, 0) / clarityScores.length;\n              return _context18.abrupt(\"return\", {\n                success: averageScore >= 75,\n                // 平均分75以上\n                metrics: {\n                  totalTime: totalTime,\n                  messageCount: errorMessages.length,\n                  averageScore: averageScore,\n                  clarityScores: clarityScores\n                }\n              });\n            case 11:\n              _context18.prev = 11;\n              _context18.t0 = _context18[\"catch\"](1);\n              return _context18.abrupt(\"return\", {\n                success: false,\n                error: _context18.t0.message\n              });\n            case 14:\n            case \"end\":\n              return _context18.stop();\n          }\n        }, _callee18, null, [[1, 11]]);\n      }));\n      function testErrorMessageClarity() {\n        return _testErrorMessageClarity.apply(this, arguments);\n      }\n      return testErrorMessageClarity;\n    }()\n    /**\n     * 测试错误恢复机制\n     */\n    )\n  }, {\n    key: \"testErrorRecovery\",\n    value: (function () {\n      var _testErrorRecovery = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee19() {\n        var startTime, recoveryScenarios, recoveryResults, _i4, _recoveryScenarios, scenario, recoveryStart, result, recoveryEnd, endTime, totalTime, successfulRecoveries, averageRecoveryTime;\n        return _regeneratorRuntime().wrap(function _callee19$(_context19) {\n          while (1) switch (_context19.prev = _context19.next) {\n            case 0:\n              startTime = performance.now();\n              _context19.prev = 1;\n              // 模拟错误恢复场景\n              recoveryScenarios = [{\n                name: '表单验证错误',\n                recovery: function recovery() {\n                  // 模拟表单错误和恢复\n                  return {\n                    success: true,\n                    time: 100\n                  };\n                }\n              }, {\n                name: '网络请求失败',\n                recovery: function recovery() {\n                  // 模拟网络错误和重试\n                  return {\n                    success: true,\n                    time: 200\n                  };\n                }\n              }, {\n                name: '文件上传失败',\n                recovery: function recovery() {\n                  // 模拟文件上传错误和恢复\n                  return {\n                    success: true,\n                    time: 150\n                  };\n                }\n              }];\n              recoveryResults = [];\n              _i4 = 0, _recoveryScenarios = recoveryScenarios;\n            case 5:\n              if (!(_i4 < _recoveryScenarios.length)) {\n                _context19.next = 16;\n                break;\n              }\n              scenario = _recoveryScenarios[_i4];\n              recoveryStart = performance.now();\n              result = scenario.recovery();\n              recoveryEnd = performance.now();\n              recoveryResults.push({\n                name: scenario.name,\n                success: result.success,\n                recoveryTime: recoveryEnd - recoveryStart,\n                expectedTime: result.time\n              });\n              _context19.next = 13;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 50);\n              });\n            case 13:\n              _i4++;\n              _context19.next = 5;\n              break;\n            case 16:\n              endTime = performance.now();\n              totalTime = endTime - startTime;\n              successfulRecoveries = recoveryResults.filter(function (r) {\n                return r.success;\n              }).length;\n              averageRecoveryTime = recoveryResults.reduce(function (sum, r) {\n                return sum + r.recoveryTime;\n              }, 0) / recoveryResults.length;\n              return _context19.abrupt(\"return\", {\n                success: successfulRecoveries === recoveryScenarios.length && averageRecoveryTime < 300,\n                metrics: {\n                  totalTime: totalTime,\n                  scenarioCount: recoveryScenarios.length,\n                  successfulRecoveries: successfulRecoveries,\n                  averageRecoveryTime: averageRecoveryTime,\n                  recoveryResults: recoveryResults\n                }\n              });\n            case 23:\n              _context19.prev = 23;\n              _context19.t0 = _context19[\"catch\"](1);\n              return _context19.abrupt(\"return\", {\n                success: false,\n                error: _context19.t0.message\n              });\n            case 26:\n            case \"end\":\n              return _context19.stop();\n          }\n        }, _callee19, null, [[1, 23]]);\n      }));\n      function testErrorRecovery() {\n        return _testErrorRecovery.apply(this, arguments);\n      }\n      return testErrorRecovery;\n    }()\n    /**\n     * 计算颜色对比度（简化版本）\n     * @param {String} bg 背景色\n     * @param {String} fg 前景色\n     * @returns {Number} 对比度\n     */\n    )\n  }, {\n    key: \"calculateColorContrast\",\n    value: function calculateColorContrast(bg, fg) {\n      // 简化的对比度计算\n      // 实际应用中应使用更精确的WCAG对比度算法\n      var bgLuminance = this.getLuminance(bg);\n      var fgLuminance = this.getLuminance(fg);\n      var lighter = Math.max(bgLuminance, fgLuminance);\n      var darker = Math.min(bgLuminance, fgLuminance);\n      return (lighter + 0.05) / (darker + 0.05);\n    }\n\n    /**\n     * 获取颜色亮度\n     * @param {String} color 颜色值\n     * @returns {Number} 亮度\n     */\n  }, {\n    key: \"getLuminance\",\n    value: function getLuminance(color) {\n      // 简化的亮度计算\n      var hex = color.replace('#', '');\n      var r = parseInt(hex.substr(0, 2), 16) / 255;\n      var g = parseInt(hex.substr(2, 2), 16) / 255;\n      var b = parseInt(hex.substr(4, 2), 16) / 255;\n      return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n    }\n  }]);\n}();\nexport default UserExperienceTestSuite;", {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_toPrimitive", "toPrimitive", "String", "Number", "UserExperienceTestSuite", "description", "interactionEvents", "performanceEntries", "_setup", "_callee", "_this", "observer", "_callee$", "_context", "console", "log", "window", "PerformanceObserver", "list", "_this$performanceEntr", "getEntries", "observe", "entryTypes", "error", "warn", "setup", "_teardown", "_callee2", "_callee2$", "_context2", "teardown", "getTestCases", "run", "testInterfaceSmoothness", "bind", "testScrollPerformance", "testAnimationPerformance", "testResponsiveDesignMobile", "testResponsiveDesignTablet", "testResponsiveDesignDesktop", "testButtonInteraction", "testFormInteraction", "testModalInteraction", "testKeyboardNavigation", "testScreenReaderCompatibility", "testColorContrast", "testPageLoadExperience", "testContentLoadingFeedback", "testErrorMessageClarity", "testError<PERSON><PERSON><PERSON>y", "_testInterfaceSmoothness", "_callee3", "startTime", "testC<PERSON>r", "changeCount", "changeTimes", "changeStart", "changeEnd", "endTime", "totalTime", "averageChangeTime", "_callee3$", "_context3", "performance", "now", "document", "createElement", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "backgroundColor", "concat", "transform", "offsetHeight", "setTimeout", "reduce", "sum", "time", "<PERSON><PERSON><PERSON><PERSON>", "success", "metrics", "maxChangeTime", "Math", "max", "minChangeTime", "min", "fps", "t0", "message", "_testScrollPerformance", "_callee4", "scrollContainer", "contentHeight", "content", "scrollSteps", "scrollTimes", "scrollStart", "scrollTop", "scrollEnd", "averageScrollTime", "_callee4$", "_context4", "height", "background", "maxScrollTime", "_testAnimationPerformance", "_callee5", "animationElement", "animationCount", "animationTimes", "animStart", "animEnd", "averageAnimationTime", "_callee5$", "_context5", "addEventListener", "once", "maxAnimationTime", "_testResponsiveDesignMobile", "_callee6", "_callee6$", "_context6", "testResponsiveDesign", "_testResponsiveDesignTablet", "_callee7", "_callee7$", "_context7", "_testResponsiveDesignDesktop", "_callee8", "_callee8$", "_context8", "_testResponsiveDesign", "_callee9", "width", "deviceType", "originalWidth", "originalHeight", "gridElement", "gridItems", "gridComputedStyle", "gridTemplateColumns", "columnCount", "h1Element", "h1ComputedStyle", "fontSize", "testTime", "score", "_callee9$", "_context9", "innerWidth", "innerHeight", "innerHTML", "querySelector", "querySelectorAll", "getComputedStyle", "split", "parseFloat", "_x", "_x2", "_x3", "_testButtonInteraction", "_callee10", "button", "interactions", "hoverStart", "hoverEnd", "clickStart", "clickEnd", "focusStart", "focusEnd", "averageInteractionTime", "_callee10$", "_context10", "textContent", "outline", "_testFormInteraction", "_callee11", "form", "inputs", "interactionTimes", "_iterator", "_step", "input", "interactionStart", "interactionEnd", "_callee11$", "_context11", "_createForOfIteratorHelper", "borderColor", "tagName", "inputCount", "maxInteractionTime", "t1", "_testModalInteraction", "_callee12", "modal", "modalContent", "openStart", "openEnd", "openTime", "closeStart", "closeEnd", "closeTime", "_callee12$", "_context12", "opacity", "animationSmooth", "_testKeyboardNavigation", "_callee13", "container", "focusableElements", "navigationTimes", "navStart", "element", "navEnd", "averageNavigationTime", "_callee13$", "_context13", "focus", "focusableElementCount", "_testScreenReaderCompatibility", "_callee14", "accessibilityChecks", "results", "<PERSON><PERSON><PERSON><PERSON>", "totalChecks", "_callee14$", "_context14", "check", "images", "every", "img", "hasAttribute", "semanticElements", "map", "passed", "filter", "accessibilityScore", "round", "_testColorContrast", "_callee15", "colorTests", "contrastResults", "_i", "_colorTests", "contrast", "wcagAACompliant", "totalTests", "_callee15$", "_context15", "bg", "fg", "calculateColorContrast", "foreground", "wcagAA", "wcagAAA", "wcagAAACompliant", "_testPageLoadExperience", "_callee16", "navigation", "paint", "_callee16$", "_context16", "getEntriesByType", "domContentLoaded", "domContentLoadedEventEnd", "domContentLoadedEventStart", "loadComplete", "loadEventEnd", "loadEventStart", "<PERSON><PERSON><PERSON><PERSON>", "find", "firstContentful<PERSON><PERSON>t", "_objectSpread", "_testContentLoadingFeedback", "_callee17", "loader", "loadingSteps", "stepTimes", "_i2", "_loadingSteps", "step", "stepStart", "stepEnd", "averageStepTime", "_callee17$", "_context17", "head", "progress", "setAttribute", "stepCount", "_testErrorMessageClarity", "_callee18", "errorMessages", "clarityScores", "_i3", "_errorMessages", "averageScore", "_callee18$", "_context18", "clarity", "includes", "item", "messageCount", "_testErrorRecovery", "_callee19", "recoveryScenarios", "recoveryResults", "_i4", "_recoveryScenarios", "scenario", "recoveryStart", "result", "recoveryEnd", "successfulRecoveries", "averageRecoveryTime", "_callee19$", "_context19", "recovery", "recoveryTime", "expectedTime", "scenarioCount", "bgLuminance", "getLuminance", "fgLuminance", "lighter", "darker", "color", "hex", "replace", "parseInt", "substr", "b"], "sources": ["E:/teachingproject/teaching/web/src/utils/testSuites/userExperienceTestSuite.js"], "sourcesContent": ["/**\n * 用户体验测试套件\n * 测试界面操作流畅性、响应式设计、收集用户反馈、优化用户体验\n */\n\n/**\n * 用户体验测试套件类\n */\nexport class UserExperienceTestSuite {\n  constructor() {\n    this.name = '用户体验测试套件'\n    this.description = '测试系统的用户界面和交互体验'\n    this.interactionEvents = []\n    this.performanceEntries = []\n  }\n\n  /**\n   * 测试套件初始化\n   */\n  async setup() {\n    console.log('初始化用户体验测试套件...')\n    \n    // 清空事件记录\n    this.interactionEvents = []\n    this.performanceEntries = []\n    \n    // 设置性能观察器\n    if (window.PerformanceObserver) {\n      try {\n        const observer = new PerformanceObserver((list) => {\n          this.performanceEntries.push(...list.getEntries())\n        })\n        observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] })\n      } catch (error) {\n        console.warn('Performance Observer setup failed:', error)\n      }\n    }\n  }\n\n  /**\n   * 测试套件清理\n   */\n  async teardown() {\n    console.log('清理用户体验测试套件...')\n  }\n\n  /**\n   * 获取测试用例列表\n   * @returns {Array} 测试用例数组\n   */\n  getTestCases() {\n    return [\n      // 界面流畅性测试\n      {\n        name: 'interface_smoothness',\n        description: '测试界面操作流畅性',\n        run: this.testInterfaceSmoothness.bind(this)\n      },\n      {\n        name: 'scroll_performance',\n        description: '测试滚动性能',\n        run: this.testScrollPerformance.bind(this)\n      },\n      {\n        name: 'animation_performance',\n        description: '测试动画性能',\n        run: this.testAnimationPerformance.bind(this)\n      },\n\n      // 响应式设计测试\n      {\n        name: 'responsive_design_mobile',\n        description: '测试移动端响应式设计',\n        run: this.testResponsiveDesignMobile.bind(this)\n      },\n      {\n        name: 'responsive_design_tablet',\n        description: '测试平板端响应式设计',\n        run: this.testResponsiveDesignTablet.bind(this)\n      },\n      {\n        name: 'responsive_design_desktop',\n        description: '测试桌面端响应式设计',\n        run: this.testResponsiveDesignDesktop.bind(this)\n      },\n\n      // 交互体验测试\n      {\n        name: 'button_interaction',\n        description: '测试按钮交互体验',\n        run: this.testButtonInteraction.bind(this)\n      },\n      {\n        name: 'form_interaction',\n        description: '测试表单交互体验',\n        run: this.testFormInteraction.bind(this)\n      },\n      {\n        name: 'modal_interaction',\n        description: '测试模态框交互体验',\n        run: this.testModalInteraction.bind(this)\n      },\n\n      // 可访问性测试\n      {\n        name: 'keyboard_navigation',\n        description: '测试键盘导航',\n        run: this.testKeyboardNavigation.bind(this)\n      },\n      {\n        name: 'screen_reader_compatibility',\n        description: '测试屏幕阅读器兼容性',\n        run: this.testScreenReaderCompatibility.bind(this)\n      },\n      {\n        name: 'color_contrast',\n        description: '测试颜色对比度',\n        run: this.testColorContrast.bind(this)\n      },\n\n      // 加载体验测试\n      {\n        name: 'page_load_experience',\n        description: '测试页面加载体验',\n        run: this.testPageLoadExperience.bind(this)\n      },\n      {\n        name: 'content_loading_feedback',\n        description: '测试内容加载反馈',\n        run: this.testContentLoadingFeedback.bind(this)\n      },\n\n      // 错误处理体验测试\n      {\n        name: 'error_message_clarity',\n        description: '测试错误信息清晰度',\n        run: this.testErrorMessageClarity.bind(this)\n      },\n      {\n        name: 'error_recovery',\n        description: '测试错误恢复机制',\n        run: this.testErrorRecovery.bind(this)\n      }\n    ]\n  }\n\n  /**\n   * 测试界面操作流畅性\n   */\n  async testInterfaceSmoothness() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试界面元素\n      const testContainer = document.createElement('div')\n      testContainer.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: 300px;\n        height: 200px;\n        background: #f0f0f0;\n        transition: all 0.3s ease;\n      `\n      document.body.appendChild(testContainer)\n\n      // 测试多次状态变化\n      const changeCount = 20\n      const changeTimes = []\n\n      for (let i = 0; i < changeCount; i++) {\n        const changeStart = performance.now()\n        \n        // 改变样式\n        testContainer.style.backgroundColor = `hsl(${i * 18}, 50%, 50%)`\n        testContainer.style.transform = `translateX(${i * 5}px)`\n        \n        // 强制重排\n        testContainer.offsetHeight\n        \n        const changeEnd = performance.now()\n        changeTimes.push(changeEnd - changeStart)\n        \n        await new Promise(resolve => setTimeout(resolve, 10))\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      const averageChangeTime = changeTimes.reduce((sum, time) => sum + time, 0) / changeTimes.length\n\n      // 清理\n      document.body.removeChild(testContainer)\n\n      return {\n        success: averageChangeTime < 16.67, // 60fps = 16.67ms per frame\n        metrics: {\n          changeCount: changeCount,\n          totalTime: totalTime,\n          averageChangeTime: averageChangeTime,\n          maxChangeTime: Math.max(...changeTimes),\n          minChangeTime: Math.min(...changeTimes),\n          fps: 1000 / averageChangeTime\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试滚动性能\n   */\n  async testScrollPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建可滚动容器\n      const scrollContainer = document.createElement('div')\n      scrollContainer.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: 300px;\n        height: 200px;\n        overflow-y: auto;\n        border: 1px solid #ccc;\n      `\n\n      // 添加大量内容\n      const contentHeight = 2000\n      const content = document.createElement('div')\n      content.style.height = `${contentHeight}px`\n      content.style.background = 'linear-gradient(to bottom, #ff0000, #0000ff)'\n      scrollContainer.appendChild(content)\n      document.body.appendChild(scrollContainer)\n\n      // 模拟滚动\n      const scrollSteps = 20\n      const scrollTimes = []\n\n      for (let i = 0; i < scrollSteps; i++) {\n        const scrollStart = performance.now()\n        \n        const scrollTop = (i / scrollSteps) * (contentHeight - 200)\n        scrollContainer.scrollTop = scrollTop\n        \n        // 强制重排\n        scrollContainer.offsetHeight\n        \n        const scrollEnd = performance.now()\n        scrollTimes.push(scrollEnd - scrollStart)\n        \n        await new Promise(resolve => setTimeout(resolve, 5))\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      const averageScrollTime = scrollTimes.reduce((sum, time) => sum + time, 0) / scrollTimes.length\n\n      // 清理\n      document.body.removeChild(scrollContainer)\n\n      return {\n        success: averageScrollTime < 10, // 滚动时间小于10ms\n        metrics: {\n          scrollSteps: scrollSteps,\n          totalTime: totalTime,\n          averageScrollTime: averageScrollTime,\n          maxScrollTime: Math.max(...scrollTimes),\n          contentHeight: contentHeight\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试动画性能\n   */\n  async testAnimationPerformance() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建动画元素\n      const animationElement = document.createElement('div')\n      animationElement.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: 50px;\n        height: 50px;\n        background: #ff6b6b;\n        transition: transform 0.3s ease;\n      `\n      document.body.appendChild(animationElement)\n\n      // 执行多个动画\n      const animationCount = 10\n      const animationTimes = []\n\n      for (let i = 0; i < animationCount; i++) {\n        const animStart = performance.now()\n        \n        // 触发动画\n        animationElement.style.transform = `translateX(${i * 20}px) rotate(${i * 36}deg)`\n        \n        // 等待动画完成\n        await new Promise(resolve => {\n          animationElement.addEventListener('transitionend', resolve, { once: true })\n          setTimeout(resolve, 400) // 备用超时\n        })\n        \n        const animEnd = performance.now()\n        animationTimes.push(animEnd - animStart)\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n      const averageAnimationTime = animationTimes.reduce((sum, time) => sum + time, 0) / animationTimes.length\n\n      // 清理\n      document.body.removeChild(animationElement)\n\n      return {\n        success: averageAnimationTime < 500, // 动画时间小于500ms\n        metrics: {\n          animationCount: animationCount,\n          totalTime: totalTime,\n          averageAnimationTime: averageAnimationTime,\n          maxAnimationTime: Math.max(...animationTimes)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试移动端响应式设计\n   */\n  async testResponsiveDesignMobile() {\n    return this.testResponsiveDesign(375, 667, 'mobile')\n  }\n\n  /**\n   * 测试平板端响应式设计\n   */\n  async testResponsiveDesignTablet() {\n    return this.testResponsiveDesign(768, 1024, 'tablet')\n  }\n\n  /**\n   * 测试桌面端响应式设计\n   */\n  async testResponsiveDesignDesktop() {\n    return this.testResponsiveDesign(1920, 1080, 'desktop')\n  }\n\n  /**\n   * 测试响应式设计\n   * @param {Number} width 屏幕宽度\n   * @param {Number} height 屏幕高度\n   * @param {String} deviceType 设备类型\n   */\n  async testResponsiveDesign(width, height, deviceType) {\n    const startTime = performance.now()\n    \n    try {\n      // 保存原始视口大小\n      const originalWidth = window.innerWidth\n      const originalHeight = window.innerHeight\n\n      // 创建测试容器\n      const testContainer = document.createElement('div')\n      testContainer.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: ${width}px;\n        height: ${height}px;\n        border: 1px solid #ccc;\n        overflow: hidden;\n      `\n\n      // 添加响应式测试内容\n      testContainer.innerHTML = `\n        <div class=\"responsive-test\" style=\"\n          width: 100%;\n          padding: 20px;\n          box-sizing: border-box;\n        \">\n          <h1 style=\"font-size: 2rem; margin-bottom: 1rem;\">响应式测试</h1>\n          <div class=\"grid\" style=\"\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 1rem;\n          \">\n            <div style=\"background: #f0f0f0; padding: 1rem;\">项目1</div>\n            <div style=\"background: #e0e0e0; padding: 1rem;\">项目2</div>\n            <div style=\"background: #d0d0d0; padding: 1rem;\">项目3</div>\n          </div>\n        </div>\n      `\n\n      document.body.appendChild(testContainer)\n\n      // 检查布局\n      const gridElement = testContainer.querySelector('.grid')\n      const gridItems = testContainer.querySelectorAll('.grid > div')\n      \n      // 计算网格列数\n      const gridComputedStyle = window.getComputedStyle(gridElement)\n      const gridTemplateColumns = gridComputedStyle.gridTemplateColumns\n      const columnCount = gridTemplateColumns.split(' ').length\n\n      // 检查字体大小\n      const h1Element = testContainer.querySelector('h1')\n      const h1ComputedStyle = window.getComputedStyle(h1Element)\n      const fontSize = parseFloat(h1ComputedStyle.fontSize)\n\n      const endTime = performance.now()\n      const testTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(testContainer)\n\n      // 评估响应式效果\n      let score = 0\n      if (deviceType === 'mobile' && columnCount <= 2) score += 30\n      if (deviceType === 'tablet' && columnCount <= 3) score += 30\n      if (deviceType === 'desktop' && columnCount >= 3) score += 30\n      if (fontSize >= 16) score += 20 // 字体大小合适\n      if (gridItems.length === 3) score += 20 // 所有项目都显示\n\n      return {\n        success: score >= 80,\n        metrics: {\n          deviceType: deviceType,\n          width: width,\n          height: height,\n          columnCount: columnCount,\n          fontSize: fontSize,\n          score: score,\n          testTime: testTime\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试按钮交互体验\n   */\n  async testButtonInteraction() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试按钮\n      const button = document.createElement('button')\n      button.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        padding: 10px 20px;\n        background: #1890ff;\n        color: white;\n        border: none;\n        border-radius: 4px;\n        cursor: pointer;\n        transition: all 0.2s ease;\n      `\n      button.textContent = '测试按钮'\n      document.body.appendChild(button)\n\n      // 测试交互状态\n      const interactions = []\n\n      // 测试hover效果\n      const hoverStart = performance.now()\n      button.style.background = '#40a9ff'\n      button.offsetHeight // 强制重排\n      const hoverEnd = performance.now()\n      interactions.push({ type: 'hover', time: hoverEnd - hoverStart })\n\n      // 测试点击效果\n      const clickStart = performance.now()\n      button.style.transform = 'scale(0.95)'\n      button.offsetHeight\n      const clickEnd = performance.now()\n      interactions.push({ type: 'click', time: clickEnd - clickStart })\n\n      // 测试焦点效果\n      const focusStart = performance.now()\n      button.style.outline = '2px solid #1890ff'\n      button.offsetHeight\n      const focusEnd = performance.now()\n      interactions.push({ type: 'focus', time: focusEnd - focusStart })\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(button)\n\n      const averageInteractionTime = interactions.reduce((sum, i) => sum + i.time, 0) / interactions.length\n\n      return {\n        success: averageInteractionTime < 50, // 交互响应时间小于50ms\n        metrics: {\n          totalTime: totalTime,\n          averageInteractionTime: averageInteractionTime,\n          interactions: interactions\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试表单交互体验\n   */\n  async testFormInteraction() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试表单\n      const form = document.createElement('form')\n      form.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: 300px;\n        padding: 20px;\n        background: white;\n        border: 1px solid #ccc;\n      `\n\n      form.innerHTML = `\n        <input type=\"text\" placeholder=\"用户名\" style=\"width: 100%; margin-bottom: 10px; padding: 8px;\">\n        <input type=\"password\" placeholder=\"密码\" style=\"width: 100%; margin-bottom: 10px; padding: 8px;\">\n        <select style=\"width: 100%; margin-bottom: 10px; padding: 8px;\">\n          <option>选项1</option>\n          <option>选项2</option>\n        </select>\n        <textarea placeholder=\"备注\" style=\"width: 100%; margin-bottom: 10px; padding: 8px; height: 60px;\"></textarea>\n        <button type=\"submit\" style=\"width: 100%; padding: 10px; background: #1890ff; color: white; border: none;\">提交</button>\n      `\n\n      document.body.appendChild(form)\n\n      // 测试表单元素交互\n      const inputs = form.querySelectorAll('input, select, textarea')\n      const interactionTimes = []\n\n      for (const input of inputs) {\n        const interactionStart = performance.now()\n        \n        // 模拟焦点\n        input.style.borderColor = '#1890ff'\n        input.style.outline = '2px solid rgba(24, 144, 255, 0.2)'\n        \n        // 模拟输入\n        if (input.type === 'text') {\n          input.value = '测试用户'\n        } else if (input.type === 'password') {\n          input.value = '123456'\n        } else if (input.tagName === 'TEXTAREA') {\n          input.value = '测试备注内容'\n        }\n        \n        input.offsetHeight // 强制重排\n        \n        const interactionEnd = performance.now()\n        interactionTimes.push(interactionEnd - interactionStart)\n        \n        await new Promise(resolve => setTimeout(resolve, 10))\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(form)\n\n      const averageInteractionTime = interactionTimes.reduce((sum, time) => sum + time, 0) / interactionTimes.length\n\n      return {\n        success: averageInteractionTime < 30, // 表单交互时间小于30ms\n        metrics: {\n          totalTime: totalTime,\n          inputCount: inputs.length,\n          averageInteractionTime: averageInteractionTime,\n          maxInteractionTime: Math.max(...interactionTimes)\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试模态框交互体验\n   */\n  async testModalInteraction() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建模态框\n      const modal = document.createElement('div')\n      modal.style.cssText = `\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.5);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 9999;\n        opacity: 0;\n        transition: opacity 0.3s ease;\n      `\n\n      const modalContent = document.createElement('div')\n      modalContent.style.cssText = `\n        background: white;\n        padding: 20px;\n        border-radius: 8px;\n        max-width: 400px;\n        width: 90%;\n        transform: scale(0.9);\n        transition: transform 0.3s ease;\n      `\n      modalContent.innerHTML = `\n        <h3>测试模态框</h3>\n        <p>这是一个测试模态框的内容。</p>\n        <button id=\"modal-close\" style=\"padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px;\">关闭</button>\n      `\n\n      modal.appendChild(modalContent)\n      document.body.appendChild(modal)\n\n      // 测试打开动画\n      const openStart = performance.now()\n      modal.style.opacity = '1'\n      modalContent.style.transform = 'scale(1)'\n      modal.offsetHeight // 强制重排\n      \n      await new Promise(resolve => setTimeout(resolve, 300))\n      const openEnd = performance.now()\n      const openTime = openEnd - openStart\n\n      // 测试关闭动画\n      const closeStart = performance.now()\n      modal.style.opacity = '0'\n      modalContent.style.transform = 'scale(0.9)'\n      modal.offsetHeight\n      \n      await new Promise(resolve => setTimeout(resolve, 300))\n      const closeEnd = performance.now()\n      const closeTime = closeEnd - closeStart\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(modal)\n\n      return {\n        success: openTime < 400 && closeTime < 400, // 动画时间合理\n        metrics: {\n          totalTime: totalTime,\n          openTime: openTime,\n          closeTime: closeTime,\n          animationSmooth: openTime < 400 && closeTime < 400\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试键盘导航\n   */\n  async testKeyboardNavigation() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建可导航元素\n      const container = document.createElement('div')\n      container.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        padding: 20px;\n      `\n\n      container.innerHTML = `\n        <button tabindex=\"1\">按钮1</button>\n        <input type=\"text\" tabindex=\"2\" placeholder=\"输入框\">\n        <select tabindex=\"3\">\n          <option>选项1</option>\n          <option>选项2</option>\n        </select>\n        <a href=\"#\" tabindex=\"4\">链接</a>\n        <button tabindex=\"5\">按钮2</button>\n      `\n\n      document.body.appendChild(container)\n\n      // 测试Tab导航\n      const focusableElements = container.querySelectorAll('[tabindex]')\n      const navigationTimes = []\n\n      for (let i = 0; i < focusableElements.length; i++) {\n        const navStart = performance.now()\n        \n        const element = focusableElements[i]\n        element.focus()\n        element.style.outline = '2px solid #1890ff'\n        \n        element.offsetHeight // 强制重排\n        \n        const navEnd = performance.now()\n        navigationTimes.push(navEnd - navStart)\n        \n        await new Promise(resolve => setTimeout(resolve, 50))\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(container)\n\n      const averageNavigationTime = navigationTimes.reduce((sum, time) => sum + time, 0) / navigationTimes.length\n\n      return {\n        success: averageNavigationTime < 20, // 导航响应时间小于20ms\n        metrics: {\n          totalTime: totalTime,\n          focusableElementCount: focusableElements.length,\n          averageNavigationTime: averageNavigationTime,\n          navigationTimes: navigationTimes\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试屏幕阅读器兼容性\n   */\n  async testScreenReaderCompatibility() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试内容\n      const container = document.createElement('div')\n      container.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        padding: 20px;\n      `\n\n      container.innerHTML = `\n        <h1>主标题</h1>\n        <nav aria-label=\"主导航\">\n          <ul>\n            <li><a href=\"#\" aria-label=\"首页链接\">首页</a></li>\n            <li><a href=\"#\" aria-label=\"关于链接\">关于</a></li>\n          </ul>\n        </nav>\n        <main>\n          <article>\n            <h2>文章标题</h2>\n            <p>文章内容段落。</p>\n            <img src=\"#\" alt=\"描述性替代文本\">\n          </article>\n        </main>\n        <button aria-label=\"提交表单\" aria-describedby=\"submit-help\">提交</button>\n        <div id=\"submit-help\">点击此按钮提交表单</div>\n      `\n\n      document.body.appendChild(container)\n\n      // 检查可访问性属性\n      const accessibilityChecks = [\n        {\n          name: 'headings',\n          check: () => container.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0\n        },\n        {\n          name: 'alt_text',\n          check: () => {\n            const images = container.querySelectorAll('img')\n            return Array.from(images).every(img => img.hasAttribute('alt'))\n          }\n        },\n        {\n          name: 'aria_labels',\n          check: () => container.querySelectorAll('[aria-label]').length > 0\n        },\n        {\n          name: 'aria_describedby',\n          check: () => container.querySelectorAll('[aria-describedby]').length > 0\n        },\n        {\n          name: 'semantic_elements',\n          check: () => {\n            const semanticElements = container.querySelectorAll('nav, main, article, section, aside, header, footer')\n            return semanticElements.length > 0\n          }\n        }\n      ]\n\n      const results = accessibilityChecks.map(check => ({\n        name: check.name,\n        passed: check.check()\n      }))\n\n      const passedChecks = results.filter(r => r.passed).length\n      const totalChecks = results.length\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(container)\n\n      return {\n        success: passedChecks >= totalChecks * 0.8, // 80%的检查通过\n        metrics: {\n          totalTime: totalTime,\n          passedChecks: passedChecks,\n          totalChecks: totalChecks,\n          accessibilityScore: Math.round((passedChecks / totalChecks) * 100),\n          results: results\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试颜色对比度\n   */\n  async testColorContrast() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建测试元素\n      const container = document.createElement('div')\n      container.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        padding: 20px;\n      `\n\n      const colorTests = [\n        { bg: '#ffffff', fg: '#000000', name: '黑白对比' },\n        { bg: '#1890ff', fg: '#ffffff', name: '蓝白对比' },\n        { bg: '#f5f5f5', fg: '#333333', name: '灰色对比' },\n        { bg: '#ff4d4f', fg: '#ffffff', name: '红白对比' },\n        { bg: '#52c41a', fg: '#ffffff', name: '绿白对比' }\n      ]\n\n      const contrastResults = []\n\n      for (const test of colorTests) {\n        const element = document.createElement('div')\n        element.style.cssText = `\n          background-color: ${test.bg};\n          color: ${test.fg};\n          padding: 10px;\n          margin: 5px 0;\n        `\n        element.textContent = `测试文本 - ${test.name}`\n        container.appendChild(element)\n\n        // 计算对比度（简化版本）\n        const contrast = this.calculateColorContrast(test.bg, test.fg)\n        contrastResults.push({\n          name: test.name,\n          background: test.bg,\n          foreground: test.fg,\n          contrast: contrast,\n          wcagAA: contrast >= 4.5,\n          wcagAAA: contrast >= 7\n        })\n      }\n\n      document.body.appendChild(container)\n      \n      // 强制渲染\n      container.offsetHeight\n      \n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(container)\n\n      const wcagAACompliant = contrastResults.filter(r => r.wcagAA).length\n      const totalTests = contrastResults.length\n\n      return {\n        success: wcagAACompliant >= totalTests * 0.8, // 80%符合WCAG AA标准\n        metrics: {\n          totalTime: totalTime,\n          totalTests: totalTests,\n          wcagAACompliant: wcagAACompliant,\n          wcagAAACompliant: contrastResults.filter(r => r.wcagAAA).length,\n          contrastResults: contrastResults\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试页面加载体验\n   */\n  async testPageLoadExperience() {\n    const startTime = performance.now()\n    \n    try {\n      // 获取导航时间信息\n      const navigation = performance.getEntriesByType('navigation')[0]\n      const paint = performance.getEntriesByType('paint')\n\n      const metrics = {\n        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,\n        loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,\n        firstPaint: (paint.find(p => p.name === 'first-paint') && paint.find(p => p.name === 'first-paint').startTime) || 0,\n        firstContentfulPaint: (paint.find(p => p.name === 'first-contentful-paint') && paint.find(p => p.name === 'first-contentful-paint').startTime) || 0\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 评估加载体验\n      let score = 0\n      if (metrics.firstPaint < 1000) score += 25 // 首次绘制小于1秒\n      if (metrics.firstContentfulPaint < 1500) score += 25 // 首次内容绘制小于1.5秒\n      if (metrics.domContentLoaded < 2000) score += 25 // DOM加载小于2秒\n      if (metrics.loadComplete < 3000) score += 25 // 完全加载小于3秒\n\n      return {\n        success: score >= 75, // 75分以上\n        metrics: {\n          totalTime: totalTime,\n          score: score,\n          ...metrics\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试内容加载反馈\n   */\n  async testContentLoadingFeedback() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建加载指示器\n      const loader = document.createElement('div')\n      loader.style.cssText = `\n        position: absolute;\n        top: -9999px;\n        width: 40px;\n        height: 40px;\n        border: 4px solid #f3f3f3;\n        border-top: 4px solid #1890ff;\n        border-radius: 50%;\n        animation: spin 1s linear infinite;\n      `\n\n      // 添加旋转动画\n      const style = document.createElement('style')\n      style.textContent = `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n      document.head.appendChild(style)\n      document.body.appendChild(loader)\n\n      // 模拟加载过程\n      const loadingSteps = [\n        { message: '正在加载...', progress: 0 },\n        { message: '加载中...', progress: 30 },\n        { message: '即将完成...', progress: 70 },\n        { message: '加载完成', progress: 100 }\n      ]\n\n      const stepTimes = []\n\n      for (const step of loadingSteps) {\n        const stepStart = performance.now()\n        \n        // 更新加载状态\n        loader.setAttribute('data-progress', step.progress)\n        loader.setAttribute('aria-label', step.message)\n        \n        await new Promise(resolve => setTimeout(resolve, 200))\n        \n        const stepEnd = performance.now()\n        stepTimes.push(stepEnd - stepStart)\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      // 清理\n      document.body.removeChild(loader)\n      document.head.removeChild(style)\n\n      const averageStepTime = stepTimes.reduce((sum, time) => sum + time, 0) / stepTimes.length\n\n      return {\n        success: averageStepTime < 250, // 每步小于250ms\n        metrics: {\n          totalTime: totalTime,\n          stepCount: loadingSteps.length,\n          averageStepTime: averageStepTime,\n          stepTimes: stepTimes\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试错误信息清晰度\n   */\n  async testErrorMessageClarity() {\n    const startTime = performance.now()\n    \n    try {\n      // 创建错误信息测试\n      const errorMessages = [\n        { type: 'validation', message: '请输入有效的邮箱地址', clarity: 'good' },\n        { type: 'network', message: '网络连接失败，请检查网络设置', clarity: 'good' },\n        { type: 'generic', message: '操作失败', clarity: 'poor' },\n        { type: 'detailed', message: '文件上传失败：文件大小超过10MB限制', clarity: 'excellent' }\n      ]\n\n      const clarityScores = []\n\n      for (const error of errorMessages) {\n        // 评估错误信息质量\n        let score = 0\n        \n        // 长度适中\n        if (error.message.length >= 10 && error.message.length <= 100) score += 25\n        \n        // 包含具体信息\n        if (error.message.includes('请') || error.message.includes('检查') || error.message.includes('：')) score += 25\n        \n        // 提供解决方案\n        if (error.message.includes('请') && (error.message.includes('检查') || error.message.includes('重试'))) score += 25\n        \n        // 语言友好\n        if (!error.message.includes('错误') || error.message.includes('失败')) score += 25\n\n        clarityScores.push({\n          type: error.type,\n          message: error.message,\n          score: score,\n          clarity: error.clarity\n        })\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      const averageScore = clarityScores.reduce((sum, item) => sum + item.score, 0) / clarityScores.length\n\n      return {\n        success: averageScore >= 75, // 平均分75以上\n        metrics: {\n          totalTime: totalTime,\n          messageCount: errorMessages.length,\n          averageScore: averageScore,\n          clarityScores: clarityScores\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 测试错误恢复机制\n   */\n  async testErrorRecovery() {\n    const startTime = performance.now()\n    \n    try {\n      // 模拟错误恢复场景\n      const recoveryScenarios = [\n        {\n          name: '表单验证错误',\n          recovery: () => {\n            // 模拟表单错误和恢复\n            return { success: true, time: 100 }\n          }\n        },\n        {\n          name: '网络请求失败',\n          recovery: () => {\n            // 模拟网络错误和重试\n            return { success: true, time: 200 }\n          }\n        },\n        {\n          name: '文件上传失败',\n          recovery: () => {\n            // 模拟文件上传错误和恢复\n            return { success: true, time: 150 }\n          }\n        }\n      ]\n\n      const recoveryResults = []\n\n      for (const scenario of recoveryScenarios) {\n        const recoveryStart = performance.now()\n        const result = scenario.recovery()\n        const recoveryEnd = performance.now()\n\n        recoveryResults.push({\n          name: scenario.name,\n          success: result.success,\n          recoveryTime: recoveryEnd - recoveryStart,\n          expectedTime: result.time\n        })\n\n        await new Promise(resolve => setTimeout(resolve, 50))\n      }\n\n      const endTime = performance.now()\n      const totalTime = endTime - startTime\n\n      const successfulRecoveries = recoveryResults.filter(r => r.success).length\n      const averageRecoveryTime = recoveryResults.reduce((sum, r) => sum + r.recoveryTime, 0) / recoveryResults.length\n\n      return {\n        success: successfulRecoveries === recoveryScenarios.length && averageRecoveryTime < 300,\n        metrics: {\n          totalTime: totalTime,\n          scenarioCount: recoveryScenarios.length,\n          successfulRecoveries: successfulRecoveries,\n          averageRecoveryTime: averageRecoveryTime,\n          recoveryResults: recoveryResults\n        }\n      }\n    } catch (error) {\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 计算颜色对比度（简化版本）\n   * @param {String} bg 背景色\n   * @param {String} fg 前景色\n   * @returns {Number} 对比度\n   */\n  calculateColorContrast(bg, fg) {\n    // 简化的对比度计算\n    // 实际应用中应使用更精确的WCAG对比度算法\n    const bgLuminance = this.getLuminance(bg)\n    const fgLuminance = this.getLuminance(fg)\n    \n    const lighter = Math.max(bgLuminance, fgLuminance)\n    const darker = Math.min(bgLuminance, fgLuminance)\n    \n    return (lighter + 0.05) / (darker + 0.05)\n  }\n\n  /**\n   * 获取颜色亮度\n   * @param {String} color 颜色值\n   * @returns {Number} 亮度\n   */\n  getLuminance(color) {\n    // 简化的亮度计算\n    const hex = color.replace('#', '')\n    const r = parseInt(hex.substr(0, 2), 16) / 255\n    const g = parseInt(hex.substr(2, 2), 16) / 255\n    const b = parseInt(hex.substr(4, 2), 16) / 255\n    \n    return 0.2126 * r + 0.7152 * g + 0.0722 * b\n  }\n}\n\nexport default UserExperienceTestSuite\n"], "mappings": ";;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,gBAAAkB,OAAA,CAAAlB,CAAA,KAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,CAAAoB,OAAA,EAAAC,IAAA,WAAAnD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAkD,OAAA,CAAAnB,CAAA,EAAAqB,IAAA,WAAAnD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAAgD,2BAAA,eAAArD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAkD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA3B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAqB,KAAA,sCAAA/C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAsD,IAAA,eAAAlD,CAAA,CAAAmD,MAAA,GAAA9C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAoD,QAAA,MAAA3C,CAAA,QAAAE,CAAA,GAAA0C,mBAAA,CAAA5C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAmD,MAAA,EAAAnD,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAuD,KAAA,GAAAvD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAmD,MAAA,QAAAjD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAwD,iBAAA,CAAAxD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAmD,MAAA,IAAAnD,CAAA,CAAAyD,MAAA,WAAAzD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAkD,IAAA,GAAArB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAA0B,IAAA,EAAAlD,CAAA,CAAAkD,IAAA,kBAAAjB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAmD,MAAA,YAAAnD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA6B,oBAAA1D,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAsD,MAAA,EAAAjD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAuD,QAAA,qBAAApD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAkD,MAAA,KAAA7D,CAAA,CAAAsD,MAAA,aAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAyD,mBAAA,CAAA1D,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAsD,MAAA,kBAAAnD,CAAA,KAAAH,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,uCAAA3D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA2C,IAAA,IAAArD,CAAA,CAAAF,CAAA,CAAAiE,UAAA,IAAArD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAAgE,IAAA,GAAAlE,CAAA,CAAAmE,OAAA,eAAAjE,CAAA,CAAAsD,MAAA,KAAAtD,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAsD,MAAA,YAAAtD,CAAA,CAAA2B,GAAA,OAAAmC,SAAA,sCAAA9D,CAAA,CAAAuD,QAAA,SAAAtB,CAAA,cAAAiC,aAAAnE,CAAA,QAAAD,CAAA,KAAAqE,MAAA,EAAApE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAsE,QAAA,GAAArE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAuE,UAAA,GAAAtE,CAAA,KAAAD,CAAA,CAAAwE,QAAA,GAAAvE,CAAA,WAAAwE,UAAA,CAAAC,IAAA,CAAA1E,CAAA,cAAA2E,cAAA1E,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA2E,UAAA,QAAA5E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA2E,UAAA,GAAA5E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAwE,UAAA,MAAAJ,MAAA,aAAApE,CAAA,CAAA4C,OAAA,CAAAuB,YAAA,cAAAS,KAAA,iBAAAnC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAkE,IAAA,SAAAlE,CAAA,OAAA8E,KAAA,CAAA9E,CAAA,CAAA+E,MAAA,SAAAxE,CAAA,OAAAG,CAAA,YAAAwD,KAAA,aAAA3D,CAAA,GAAAP,CAAA,CAAA+E,MAAA,OAAA1E,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA2D,IAAA,CAAAzD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA2D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAxD,CAAA,CAAAwD,IAAA,GAAAxD,CAAA,gBAAAsD,SAAA,CAAAf,OAAA,CAAAjD,CAAA,kCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA4C,WAAA,GAAA9D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAiF,mBAAA,aAAAhF,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAiF,WAAA,WAAAlF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAAgF,WAAA,IAAAhF,CAAA,CAAAmF,IAAA,OAAAnF,CAAA,CAAAoF,IAAA,aAAAnF,CAAA,WAAAE,MAAA,CAAAkF,cAAA,GAAAlF,MAAA,CAAAkF,cAAA,CAAApF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAqF,SAAA,GAAAjD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,aAAAkD,OAAA,EAAAlD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAwF,KAAA,aAAAvF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA+E,OAAA,OAAA7E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAiF,mBAAA,CAAA/E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAsD,IAAA,GAAAd,IAAA,WAAAnD,CAAA,WAAAA,CAAA,CAAAsD,IAAA,GAAAtD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAsD,IAAA,WAAAtB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAA0F,IAAA,aAAAzF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAwE,IAAA,CAAArE,CAAA,UAAAH,CAAA,CAAAyF,OAAA,aAAAzB,KAAA,WAAAhE,CAAA,CAAA6E,MAAA,SAAA9E,CAAA,GAAAC,CAAA,CAAA0F,GAAA,QAAA3F,CAAA,IAAAD,CAAA,SAAAkE,IAAA,CAAAzD,KAAA,GAAAR,CAAA,EAAAiE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAlE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA8E,WAAA,EAAAzD,OAAA,EAAAoD,KAAA,WAAAA,MAAA7E,CAAA,aAAA6F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA3D,CAAA,OAAAsD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA3B,GAAA,GAAA5B,CAAA,OAAAwE,UAAA,CAAA5B,OAAA,CAAA8B,aAAA,IAAA3E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA4F,MAAA,OAAAzF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA4E,KAAA,EAAA5E,CAAA,CAAA6F,KAAA,cAAA7F,CAAA,IAAAD,CAAA,MAAA+F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAAtD,CAAA,QAAAwE,UAAA,IAAAG,UAAA,kBAAA3E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAoE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA7D,CAAA,aAAAuD,IAAA,QAAAvD,CAAA,MAAAE,CAAA,kBAAAgG,OAAA7F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAAgE,IAAA,GAAA7D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAsD,MAAA,WAAAtD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAkE,UAAA,CAAAM,MAAA,MAAAxE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA+D,UAAA,CAAAlE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAkE,UAAA,iBAAAlE,CAAA,CAAA2D,MAAA,SAAA6B,MAAA,aAAAxF,CAAA,CAAA2D,MAAA,SAAAwB,IAAA,QAAA/E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA6E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,gBAAAuB,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,cAAAzD,CAAA,aAAA+E,IAAA,GAAAnF,CAAA,CAAA4D,QAAA,SAAA4B,MAAA,CAAAxF,CAAA,CAAA4D,QAAA,qBAAAtD,CAAA,QAAAsC,KAAA,qDAAAuC,IAAA,GAAAnF,CAAA,CAAA6D,UAAA,SAAA2B,MAAA,CAAAxF,CAAA,CAAA6D,UAAA,YAAAT,MAAA,WAAAA,OAAA7D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAuE,UAAA,CAAAM,MAAA,MAAA7E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAkE,UAAA,CAAAvE,CAAA,OAAAK,CAAA,CAAA8D,MAAA,SAAAwB,IAAA,IAAAxF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAsF,IAAA,GAAAtF,CAAA,CAAAgE,UAAA,QAAA7D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA2D,MAAA,IAAArE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA6D,UAAA,KAAA7D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAkE,UAAA,cAAAhE,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA8C,MAAA,gBAAAU,IAAA,GAAAxD,CAAA,CAAA6D,UAAA,EAAApC,CAAA,SAAAgE,QAAA,CAAAvF,CAAA,MAAAuF,QAAA,WAAAA,SAAAlG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAsC,IAAA,GAAAjE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAqE,IAAA,QAAApE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA2B,MAAA,kBAAAU,IAAA,yBAAAjE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAkE,IAAA,GAAAlE,CAAA,GAAAmC,CAAA,KAAAiE,MAAA,WAAAA,OAAAnG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAqE,UAAA,KAAAtE,CAAA,cAAAkG,QAAA,CAAAjG,CAAA,CAAA0E,UAAA,EAAA1E,CAAA,CAAAsE,QAAA,GAAAG,aAAA,CAAAzE,CAAA,GAAAiC,CAAA,OAAAkE,KAAA,WAAAC,OAAArG,CAAA,aAAAD,CAAA,QAAAyE,UAAA,CAAAM,MAAA,MAAA/E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAuE,UAAA,CAAAzE,CAAA,OAAAE,CAAA,CAAAmE,MAAA,KAAApE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAA0E,UAAA,kBAAAvE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA8C,aAAA,CAAAzE,CAAA,YAAAK,CAAA,YAAA+C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAvG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAoD,QAAA,KAAA5C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAiE,UAAA,EAAA/D,CAAA,EAAAiE,OAAA,EAAA9D,CAAA,oBAAAmD,MAAA,UAAA3B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAwG,mBAAAtG,CAAA,WAAAuG,kBAAA,CAAAvG,CAAA,KAAAwG,gBAAA,CAAAxG,CAAA,KAAAyG,2BAAA,CAAAzG,CAAA,KAAA0G,kBAAA;AAAA,SAAAA,mBAAA,cAAA5C,SAAA;AAAA,SAAA2C,4BAAAzG,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAA2G,iBAAA,CAAA3G,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAA6G,QAAA,CAAAhF,IAAA,CAAA5B,CAAA,EAAA6F,KAAA,6BAAA9F,CAAA,IAAAC,CAAA,CAAAgF,WAAA,KAAAjF,CAAA,GAAAC,CAAA,CAAAgF,WAAA,CAAAC,IAAA,aAAAlF,CAAA,cAAAA,CAAA,GAAA8G,KAAA,CAAAC,IAAA,CAAA9G,CAAA,oBAAAD,CAAA,+CAAAgH,IAAA,CAAAhH,CAAA,IAAA4G,iBAAA,CAAA3G,CAAA,EAAAU,CAAA;AAAA,SAAA8F,iBAAAxG,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAA6G,KAAA,CAAAC,IAAA,CAAA9G,CAAA;AAAA,SAAAuG,mBAAAvG,CAAA,QAAA6G,KAAA,CAAAG,OAAA,CAAAhH,CAAA,UAAA2G,iBAAA,CAAA3G,CAAA;AAAA,SAAA2G,kBAAA3G,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA6E,MAAA,MAAAnE,CAAA,GAAAV,CAAA,CAAA6E,MAAA,YAAA/E,CAAA,MAAAK,CAAA,GAAA0G,KAAA,CAAAnG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAA8G,mBAAA9G,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA6C,IAAA,GAAAtD,CAAA,CAAAe,CAAA,IAAAyE,OAAA,CAAAvC,OAAA,CAAAlC,CAAA,EAAAoC,IAAA,CAAAlD,CAAA,EAAAK,CAAA;AAAA,SAAA6G,kBAAA/G,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAqH,SAAA,aAAA5B,OAAA,WAAAvF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAiH,KAAA,CAAArH,CAAA,EAAAD,CAAA,YAAAuH,MAAAlH,CAAA,IAAA8G,kBAAA,CAAAvG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAgH,KAAA,EAAAC,MAAA,UAAAnH,CAAA,cAAAmH,OAAAnH,CAAA,IAAA8G,kBAAA,CAAAvG,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAgH,KAAA,EAAAC,MAAA,WAAAnH,CAAA,KAAAkH,KAAA;AAAA,SAAAE,gBAAA7G,CAAA,EAAAP,CAAA,UAAAO,CAAA,YAAAP,CAAA,aAAA2D,SAAA;AAAA,SAAA0D,kBAAA1H,CAAA,EAAAE,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAA6E,MAAA,EAAA9E,CAAA,UAAAM,CAAA,GAAAL,CAAA,CAAAD,CAAA,GAAAM,CAAA,CAAAY,UAAA,GAAAZ,CAAA,CAAAY,UAAA,QAAAZ,CAAA,CAAAa,YAAA,kBAAAb,CAAA,KAAAA,CAAA,CAAAc,QAAA,QAAAlB,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAA2H,cAAA,CAAApH,CAAA,CAAAqH,GAAA,GAAArH,CAAA;AAAA,SAAAsH,aAAA7H,CAAA,EAAAE,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAwH,iBAAA,CAAA1H,CAAA,CAAAI,SAAA,EAAAF,CAAA,GAAAD,CAAA,IAAAyH,iBAAA,CAAA1H,CAAA,EAAAC,CAAA,GAAAE,MAAA,CAAAK,cAAA,CAAAR,CAAA,iBAAAqB,QAAA,SAAArB,CAAA;AAAA,SAAA2H,eAAA1H,CAAA,QAAAS,CAAA,GAAAoH,YAAA,CAAA7H,CAAA,gCAAAgD,OAAA,CAAAvC,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAoH,aAAA7H,CAAA,EAAAC,CAAA,oBAAA+C,OAAA,CAAAhD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAoH,WAAA,kBAAA/H,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,gCAAA+C,OAAA,CAAAvC,CAAA,UAAAA,CAAA,YAAAsD,SAAA,yEAAA9D,CAAA,GAAA8H,MAAA,GAAAC,MAAA,EAAAhI,CAAA;AADA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAaiI,uBAAuB;EAClC,SAAAA,wBAAA,EAAc;IAAAT,eAAA,OAAAS,uBAAA;IACZ,IAAI,CAAC/C,IAAI,GAAG,UAAU;IACtB,IAAI,CAACgD,WAAW,GAAG,gBAAgB;IACnC,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE;EAC9B;;EAEA;AACF;AACA;EAFE,OAAAR,YAAA,CAAAK,uBAAA;IAAAN,GAAA;IAAAnH,KAAA;MAAA,IAAA6H,MAAA,GAAAlB,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAmD,QAAA;QAAA,IAAAC,KAAA;QAAA,IAAAC,QAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAAoH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA9C,IAAA,GAAA8C,QAAA,CAAAzE,IAAA;YAAA;cACE0E,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;cAE7B;cACA,IAAI,CAACT,iBAAiB,GAAG,EAAE;cAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE;;cAE5B;cACA,IAAIS,MAAM,CAACC,mBAAmB,EAAE;gBAC9B,IAAI;kBACIN,QAAQ,GAAG,IAAIM,mBAAmB,CAAC,UAACC,IAAI,EAAK;oBAAA,IAAAC,qBAAA;oBACjD,CAAAA,qBAAA,GAAAT,KAAI,CAACH,kBAAkB,EAAC3D,IAAI,CAAA4C,KAAA,CAAA2B,qBAAA,EAAAzC,kBAAA,CAAIwC,IAAI,CAACE,UAAU,CAAC,CAAC,EAAC;kBACpD,CAAC,CAAC;kBACFT,QAAQ,CAACU,OAAO,CAAC;oBAAEC,UAAU,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO;kBAAE,CAAC,CAAC;gBACtE,CAAC,CAAC,OAAOC,KAAK,EAAE;kBACdT,OAAO,CAACU,IAAI,CAAC,oCAAoC,EAAED,KAAK,CAAC;gBAC3D;cACF;YAAC;YAAA;cAAA,OAAAV,QAAA,CAAA3C,IAAA;UAAA;QAAA,GAAAuC,OAAA;MAAA,CACF;MAAA,SAAAgB,MAAA;QAAA,OAAAjB,MAAA,CAAAhB,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAkC,KAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA3B,GAAA;IAAAnH,KAAA;MAAA,IAAA+I,SAAA,GAAApC,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAqE,SAAA;QAAA,OAAA1J,mBAAA,GAAAuB,IAAA,UAAAoI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAAzF,IAAA;YAAA;cACE0E,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;YAAA;YAAA;cAAA,OAAAc,SAAA,CAAA3D,IAAA;UAAA;QAAA,GAAAyD,QAAA;MAAA,CAC7B;MAAA,SAAAG,SAAA;QAAA,OAAAJ,SAAA,CAAAlC,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAuC,QAAA;IAAA;IAED;AACF;AACA;AACA;IAHE;EAAA;IAAAhC,GAAA;IAAAnH,KAAA,EAIA,SAAAoJ,aAAA,EAAe;MACb,OAAO;MACL;MACA;QACE1E,IAAI,EAAE,sBAAsB;QAC5BgD,WAAW,EAAE,WAAW;QACxB2B,GAAG,EAAE,IAAI,CAACC,uBAAuB,CAACC,IAAI,CAAC,IAAI;MAC7C,CAAC,EACD;QACE7E,IAAI,EAAE,oBAAoB;QAC1BgD,WAAW,EAAE,QAAQ;QACrB2B,GAAG,EAAE,IAAI,CAACG,qBAAqB,CAACD,IAAI,CAAC,IAAI;MAC3C,CAAC,EACD;QACE7E,IAAI,EAAE,uBAAuB;QAC7BgD,WAAW,EAAE,QAAQ;QACrB2B,GAAG,EAAE,IAAI,CAACI,wBAAwB,CAACF,IAAI,CAAC,IAAI;MAC9C,CAAC;MAED;MACA;QACE7E,IAAI,EAAE,0BAA0B;QAChCgD,WAAW,EAAE,YAAY;QACzB2B,GAAG,EAAE,IAAI,CAACK,0BAA0B,CAACH,IAAI,CAAC,IAAI;MAChD,CAAC,EACD;QACE7E,IAAI,EAAE,0BAA0B;QAChCgD,WAAW,EAAE,YAAY;QACzB2B,GAAG,EAAE,IAAI,CAACM,0BAA0B,CAACJ,IAAI,CAAC,IAAI;MAChD,CAAC,EACD;QACE7E,IAAI,EAAE,2BAA2B;QACjCgD,WAAW,EAAE,YAAY;QACzB2B,GAAG,EAAE,IAAI,CAACO,2BAA2B,CAACL,IAAI,CAAC,IAAI;MACjD,CAAC;MAED;MACA;QACE7E,IAAI,EAAE,oBAAoB;QAC1BgD,WAAW,EAAE,UAAU;QACvB2B,GAAG,EAAE,IAAI,CAACQ,qBAAqB,CAACN,IAAI,CAAC,IAAI;MAC3C,CAAC,EACD;QACE7E,IAAI,EAAE,kBAAkB;QACxBgD,WAAW,EAAE,UAAU;QACvB2B,GAAG,EAAE,IAAI,CAACS,mBAAmB,CAACP,IAAI,CAAC,IAAI;MACzC,CAAC,EACD;QACE7E,IAAI,EAAE,mBAAmB;QACzBgD,WAAW,EAAE,WAAW;QACxB2B,GAAG,EAAE,IAAI,CAACU,oBAAoB,CAACR,IAAI,CAAC,IAAI;MAC1C,CAAC;MAED;MACA;QACE7E,IAAI,EAAE,qBAAqB;QAC3BgD,WAAW,EAAE,QAAQ;QACrB2B,GAAG,EAAE,IAAI,CAACW,sBAAsB,CAACT,IAAI,CAAC,IAAI;MAC5C,CAAC,EACD;QACE7E,IAAI,EAAE,6BAA6B;QACnCgD,WAAW,EAAE,YAAY;QACzB2B,GAAG,EAAE,IAAI,CAACY,6BAA6B,CAACV,IAAI,CAAC,IAAI;MACnD,CAAC,EACD;QACE7E,IAAI,EAAE,gBAAgB;QACtBgD,WAAW,EAAE,SAAS;QACtB2B,GAAG,EAAE,IAAI,CAACa,iBAAiB,CAACX,IAAI,CAAC,IAAI;MACvC,CAAC;MAED;MACA;QACE7E,IAAI,EAAE,sBAAsB;QAC5BgD,WAAW,EAAE,UAAU;QACvB2B,GAAG,EAAE,IAAI,CAACc,sBAAsB,CAACZ,IAAI,CAAC,IAAI;MAC5C,CAAC,EACD;QACE7E,IAAI,EAAE,0BAA0B;QAChCgD,WAAW,EAAE,UAAU;QACvB2B,GAAG,EAAE,IAAI,CAACe,0BAA0B,CAACb,IAAI,CAAC,IAAI;MAChD,CAAC;MAED;MACA;QACE7E,IAAI,EAAE,uBAAuB;QAC7BgD,WAAW,EAAE,WAAW;QACxB2B,GAAG,EAAE,IAAI,CAACgB,uBAAuB,CAACd,IAAI,CAAC,IAAI;MAC7C,CAAC,EACD;QACE7E,IAAI,EAAE,gBAAgB;QACtBgD,WAAW,EAAE,UAAU;QACvB2B,GAAG,EAAE,IAAI,CAACiB,iBAAiB,CAACf,IAAI,CAAC,IAAI;MACvC,CAAC,CACF;IACH;;IAEA;AACF;AACA;EAFE;IAAApC,GAAA;IAAAnH,KAAA;MAAA,IAAAuK,wBAAA,GAAA5D,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA6F,SAAA;QAAA,IAAAC,SAAA,EAAAC,aAAA,EAAAC,WAAA,EAAAC,WAAA,EAAA3K,CAAA,EAAA4K,WAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,iBAAA;QAAA,OAAA3L,mBAAA,GAAAuB,IAAA,UAAAqK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/F,IAAA,GAAA+F,SAAA,CAAA1H,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAF,SAAA,CAAA/F,IAAA;cAGjC;cACMsF,aAAa,GAAGY,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACnDb,aAAa,CAACc,KAAK,CAACC,OAAO,kLAO1B;cACDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACjB,aAAa,CAAC;;cAExC;cACMC,WAAW,GAAG,EAAE;cAChBC,WAAW,GAAG,EAAE;cAEb3K,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAG0K,WAAW;gBAAAQ,SAAA,CAAA1H,IAAA;gBAAA;cAAA;cACvBoH,WAAW,GAAGO,WAAW,CAACC,GAAG,CAAC,CAAC,EAErC;cACAX,aAAa,CAACc,KAAK,CAACI,eAAe,UAAAC,MAAA,CAAU5L,CAAC,GAAG,EAAE,gBAAa;cAChEyK,aAAa,CAACc,KAAK,CAACM,SAAS,iBAAAD,MAAA,CAAiB5L,CAAC,GAAG,CAAC,QAAK;;cAExD;cACAyK,aAAa,CAACqB,YAAY;cAEpBjB,SAAS,GAAGM,WAAW,CAACC,GAAG,CAAC,CAAC;cACnCT,WAAW,CAAC3G,IAAI,CAAC6G,SAAS,GAAGD,WAAW,CAAC;cAAAM,SAAA,CAAA1H,IAAA;cAAA,OAEnC,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,EAAE,CAAC;cAAA,EAAC;YAAA;cAbtBxC,CAAC,EAAE;cAAAkL,SAAA,CAAA1H,IAAA;cAAA;YAAA;cAgB9BsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS;cAC/BQ,iBAAiB,GAAGL,WAAW,CAACqB,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGvB,WAAW,CAACtG,MAAM,EAE/F;cACAgH,QAAQ,CAACI,IAAI,CAACU,WAAW,CAAC1B,aAAa,CAAC;cAAA,OAAAS,SAAA,CAAA9H,MAAA,WAEjC;gBACLgJ,OAAO,EAAEpB,iBAAiB,GAAG,KAAK;gBAAE;gBACpCqB,OAAO,EAAE;kBACP3B,WAAW,EAAEA,WAAW;kBACxBK,SAAS,EAAEA,SAAS;kBACpBC,iBAAiB,EAAEA,iBAAiB;kBACpCsB,aAAa,EAAEC,IAAI,CAACC,GAAG,CAAA5F,KAAA,CAAR2F,IAAI,EAAQ5B,WAAW,CAAC;kBACvC8B,aAAa,EAAEF,IAAI,CAACG,GAAG,CAAA9F,KAAA,CAAR2F,IAAI,EAAQ5B,WAAW,CAAC;kBACvCgC,GAAG,EAAE,IAAI,GAAG3B;gBACd;cACF,CAAC;YAAA;cAAAE,SAAA,CAAA/F,IAAA;cAAA+F,SAAA,CAAA0B,EAAA,GAAA1B,SAAA;cAAA,OAAAA,SAAA,CAAA9H,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEuC,SAAA,CAAA0B,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA3B,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAiF,QAAA;MAAA,CAElD;MAAA,SAAAlB,wBAAA;QAAA,OAAAiB,wBAAA,CAAA1D,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA0C,uBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAnC,GAAA;IAAAnH,KAAA;MAAA,IAAA+M,sBAAA,GAAApG,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAqI,SAAA;QAAA,IAAAvC,SAAA,EAAAwC,eAAA,EAAAC,aAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,WAAA,EAAApN,CAAA,EAAAqN,WAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAzC,OAAA,EAAAC,SAAA,EAAAyC,iBAAA;QAAA,OAAAnO,mBAAA,GAAAuB,IAAA,UAAA6M,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvI,IAAA,GAAAuI,SAAA,CAAAlK,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAsC,SAAA,CAAAvI,IAAA;cAGjC;cACM6H,eAAe,GAAG3B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACrD0B,eAAe,CAACzB,KAAK,CAACC,OAAO,4KAO5B;;cAED;cACMyB,aAAa,GAAG,IAAI;cACpBC,OAAO,GAAG7B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC7C4B,OAAO,CAAC3B,KAAK,CAACoC,MAAM,MAAA/B,MAAA,CAAMqB,aAAa,OAAI;cAC3CC,OAAO,CAAC3B,KAAK,CAACqC,UAAU,GAAG,8CAA8C;cACzEZ,eAAe,CAACtB,WAAW,CAACwB,OAAO,CAAC;cACpC7B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACsB,eAAe,CAAC;;cAE1C;cACMG,WAAW,GAAG,EAAE;cAChBC,WAAW,GAAG,EAAE;cAEbpN,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGmN,WAAW;gBAAAO,SAAA,CAAAlK,IAAA;gBAAA;cAAA;cACvB6J,WAAW,GAAGlC,WAAW,CAACC,GAAG,CAAC,CAAC;cAE/BkC,SAAS,GAAItN,CAAC,GAAGmN,WAAW,IAAKF,aAAa,GAAG,GAAG,CAAC;cAC3DD,eAAe,CAACM,SAAS,GAAGA,SAAS;;cAErC;cACAN,eAAe,CAAClB,YAAY;cAEtByB,SAAS,GAAGpC,WAAW,CAACC,GAAG,CAAC,CAAC;cACnCgC,WAAW,CAACpJ,IAAI,CAACuJ,SAAS,GAAGF,WAAW,CAAC;cAAAK,SAAA,CAAAlK,IAAA;cAAA,OAEnC,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,CAAC,CAAC;cAAA,EAAC;YAAA;cAZrBxC,CAAC,EAAE;cAAA0N,SAAA,CAAAlK,IAAA;cAAA;YAAA;cAe9BsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS;cAC/BgD,iBAAiB,GAAGJ,WAAW,CAACpB,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGkB,WAAW,CAAC/I,MAAM,EAE/F;cACAgH,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACa,eAAe,CAAC;cAAA,OAAAU,SAAA,CAAAtK,MAAA,WAEnC;gBACLgJ,OAAO,EAAEoB,iBAAiB,GAAG,EAAE;gBAAE;gBACjCnB,OAAO,EAAE;kBACPc,WAAW,EAAEA,WAAW;kBACxBpC,SAAS,EAAEA,SAAS;kBACpByC,iBAAiB,EAAEA,iBAAiB;kBACpCK,aAAa,EAAEtB,IAAI,CAACC,GAAG,CAAA5F,KAAA,CAAR2F,IAAI,EAAQa,WAAW,CAAC;kBACvCH,aAAa,EAAEA;gBACjB;cACF,CAAC;YAAA;cAAAS,SAAA,CAAAvI,IAAA;cAAAuI,SAAA,CAAAd,EAAA,GAAAc,SAAA;cAAA,OAAAA,SAAA,CAAAtK,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAE+E,SAAA,CAAAd,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAApI,IAAA;UAAA;QAAA,GAAAyH,QAAA;MAAA,CAElD;MAAA,SAAAxD,sBAAA;QAAA,OAAAuD,sBAAA,CAAAlG,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA4C,qBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAArC,GAAA;IAAAnH,KAAA;MAAA,IAAA+N,yBAAA,GAAApH,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAqJ,SAAA;QAAA,IAAAvD,SAAA,EAAAwD,gBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAlO,CAAA,EAAAmO,SAAA,EAAAC,OAAA,EAAAtD,OAAA,EAAAC,SAAA,EAAAsD,oBAAA;QAAA,OAAAhP,mBAAA,GAAAuB,IAAA,UAAA0N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApJ,IAAA,GAAAoJ,SAAA,CAAA/K,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAmD,SAAA,CAAApJ,IAAA;cAGjC;cACM6I,gBAAgB,GAAG3C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACtD0C,gBAAgB,CAACzC,KAAK,CAACC,OAAO,sLAO7B;cACDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACsC,gBAAgB,CAAC;;cAE3C;cACMC,cAAc,GAAG,EAAE;cACnBC,cAAc,GAAG,EAAE;cAEhBlO,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGiO,cAAc;gBAAAM,SAAA,CAAA/K,IAAA;gBAAA;cAAA;cAC1B2K,SAAS,GAAGhD,WAAW,CAACC,GAAG,CAAC,CAAC,EAEnC;cACA4C,gBAAgB,CAACzC,KAAK,CAACM,SAAS,iBAAAD,MAAA,CAAiB5L,CAAC,GAAG,EAAE,iBAAA4L,MAAA,CAAc5L,CAAC,GAAG,EAAE,SAAM;;cAEjF;cAAAuO,SAAA,CAAA/K,IAAA;cAAA,OACM,IAAIuB,OAAO,CAAC,UAAAvC,OAAO,EAAI;gBAC3BwL,gBAAgB,CAACQ,gBAAgB,CAAC,eAAe,EAAEhM,OAAO,EAAE;kBAAEiM,IAAI,EAAE;gBAAK,CAAC,CAAC;gBAC3E1C,UAAU,CAACvJ,OAAO,EAAE,GAAG,CAAC,EAAC;cAC3B,CAAC,CAAC;YAAA;cAEI4L,OAAO,GAAGjD,WAAW,CAACC,GAAG,CAAC,CAAC;cACjC8C,cAAc,CAAClK,IAAI,CAACoK,OAAO,GAAGD,SAAS,CAAC;YAAA;cAbNnO,CAAC,EAAE;cAAAuO,SAAA,CAAA/K,IAAA;cAAA;YAAA;cAgBjCsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS;cAC/B6D,oBAAoB,GAAGH,cAAc,CAAClC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGgC,cAAc,CAAC7J,MAAM,EAExG;cACAgH,QAAQ,CAACI,IAAI,CAACU,WAAW,CAAC6B,gBAAgB,CAAC;cAAA,OAAAO,SAAA,CAAAnL,MAAA,WAEpC;gBACLgJ,OAAO,EAAEiC,oBAAoB,GAAG,GAAG;gBAAE;gBACrChC,OAAO,EAAE;kBACP4B,cAAc,EAAEA,cAAc;kBAC9BlD,SAAS,EAAEA,SAAS;kBACpBsD,oBAAoB,EAAEA,oBAAoB;kBAC1CK,gBAAgB,EAAEnC,IAAI,CAACC,GAAG,CAAA5F,KAAA,CAAR2F,IAAI,EAAQ2B,cAAc;gBAC9C;cACF,CAAC;YAAA;cAAAK,SAAA,CAAApJ,IAAA;cAAAoJ,SAAA,CAAA3B,EAAA,GAAA2B,SAAA;cAAA,OAAAA,SAAA,CAAAnL,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAE4F,SAAA,CAAA3B,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA0B,SAAA,CAAAjJ,IAAA;UAAA;QAAA,GAAAyI,QAAA;MAAA,CAElD;MAAA,SAAAvE,yBAAA;QAAA,OAAAsE,yBAAA,CAAAlH,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA6C,wBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAtC,GAAA;IAAAnH,KAAA;MAAA,IAAA4O,2BAAA,GAAAjI,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAkK,SAAA;QAAA,OAAAvP,mBAAA,GAAAuB,IAAA,UAAAiO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3J,IAAA,GAAA2J,SAAA,CAAAtL,IAAA;YAAA;cAAA,OAAAsL,SAAA,CAAA1L,MAAA,WACS,IAAI,CAAC2L,oBAAoB,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAxJ,IAAA;UAAA;QAAA,GAAAsJ,QAAA;MAAA,CACrD;MAAA,SAAAnF,2BAAA;QAAA,OAAAkF,2BAAA,CAAA/H,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA8C,0BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAvC,GAAA;IAAAnH,KAAA;MAAA,IAAAiP,2BAAA,GAAAtI,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAuK,SAAA;QAAA,OAAA5P,mBAAA,GAAAuB,IAAA,UAAAsO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhK,IAAA,GAAAgK,SAAA,CAAA3L,IAAA;YAAA;cAAA,OAAA2L,SAAA,CAAA/L,MAAA,WACS,IAAI,CAAC2L,oBAAoB,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAA7J,IAAA;UAAA;QAAA,GAAA2J,QAAA;MAAA,CACtD;MAAA,SAAAvF,2BAAA;QAAA,OAAAsF,2BAAA,CAAApI,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA+C,0BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAxC,GAAA;IAAAnH,KAAA;MAAA,IAAAqP,4BAAA,GAAA1I,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA2K,SAAA;QAAA,OAAAhQ,mBAAA,GAAAuB,IAAA,UAAA0O,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApK,IAAA,GAAAoK,SAAA,CAAA/L,IAAA;YAAA;cAAA,OAAA+L,SAAA,CAAAnM,MAAA,WACS,IAAI,CAAC2L,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAjK,IAAA;UAAA;QAAA,GAAA+J,QAAA;MAAA,CACxD;MAAA,SAAA1F,4BAAA;QAAA,OAAAyF,4BAAA,CAAAxI,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAgD,2BAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;IALE;EAAA;IAAAzC,GAAA;IAAAnH,KAAA;MAAA,IAAAyP,qBAAA,GAAA9I,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAMA,SAAA+K,SAA2BC,KAAK,EAAE/B,MAAM,EAAEgC,UAAU;QAAA,IAAAnF,SAAA,EAAAoF,aAAA,EAAAC,cAAA,EAAApF,aAAA,EAAAqF,WAAA,EAAAC,SAAA,EAAAC,iBAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAvF,OAAA,EAAAwF,QAAA,EAAAC,KAAA;QAAA,OAAAlR,mBAAA,GAAAuB,IAAA,UAAA4P,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtL,IAAA,GAAAsL,SAAA,CAAAjN,IAAA;YAAA;cAC5CgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAqF,SAAA,CAAAtL,IAAA;cAGjC;cACMyK,aAAa,GAAGxH,MAAM,CAACsI,UAAU;cACjCb,cAAc,GAAGzH,MAAM,CAACuI,WAAW,EAEzC;cACMlG,aAAa,GAAGY,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACnDb,aAAa,CAACc,KAAK,CAACC,OAAO,2EAAAI,MAAA,CAGhB8D,KAAK,2BAAA9D,MAAA,CACJ+B,MAAM,4EAGjB;;cAED;cACAlD,aAAa,CAACmG,SAAS,stBAiBtB;cAEDvF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACjB,aAAa,CAAC;;cAExC;cACMqF,WAAW,GAAGrF,aAAa,CAACoG,aAAa,CAAC,OAAO,CAAC;cAClDd,SAAS,GAAGtF,aAAa,CAACqG,gBAAgB,CAAC,aAAa,CAAC,EAE/D;cACMd,iBAAiB,GAAG5H,MAAM,CAAC2I,gBAAgB,CAACjB,WAAW,CAAC;cACxDG,mBAAmB,GAAGD,iBAAiB,CAACC,mBAAmB;cAC3DC,WAAW,GAAGD,mBAAmB,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC3M,MAAM,EAEzD;cACM8L,SAAS,GAAG1F,aAAa,CAACoG,aAAa,CAAC,IAAI,CAAC;cAC7CT,eAAe,GAAGhI,MAAM,CAAC2I,gBAAgB,CAACZ,SAAS,CAAC;cACpDE,QAAQ,GAAGY,UAAU,CAACb,eAAe,CAACC,QAAQ,CAAC;cAE/CvF,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BkF,QAAQ,GAAGxF,OAAO,GAAGN,SAAS,EAEpC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAAC1B,aAAa,CAAC;;cAExC;cACI8F,KAAK,GAAG,CAAC;cACb,IAAIZ,UAAU,KAAK,QAAQ,IAAIO,WAAW,IAAI,CAAC,EAAEK,KAAK,IAAI,EAAE;cAC5D,IAAIZ,UAAU,KAAK,QAAQ,IAAIO,WAAW,IAAI,CAAC,EAAEK,KAAK,IAAI,EAAE;cAC5D,IAAIZ,UAAU,KAAK,SAAS,IAAIO,WAAW,IAAI,CAAC,EAAEK,KAAK,IAAI,EAAE;cAC7D,IAAIF,QAAQ,IAAI,EAAE,EAAEE,KAAK,IAAI,EAAE,EAAC;cAChC,IAAIR,SAAS,CAAC1L,MAAM,KAAK,CAAC,EAAEkM,KAAK,IAAI,EAAE,EAAC;cAAA,OAAAE,SAAA,CAAArN,MAAA,WAEjC;gBACLgJ,OAAO,EAAEmE,KAAK,IAAI,EAAE;gBACpBlE,OAAO,EAAE;kBACPsD,UAAU,EAAEA,UAAU;kBACtBD,KAAK,EAAEA,KAAK;kBACZ/B,MAAM,EAAEA,MAAM;kBACduC,WAAW,EAAEA,WAAW;kBACxBG,QAAQ,EAAEA,QAAQ;kBAClBE,KAAK,EAAEA,KAAK;kBACZD,QAAQ,EAAEA;gBACZ;cACF,CAAC;YAAA;cAAAG,SAAA,CAAAtL,IAAA;cAAAsL,SAAA,CAAA7D,EAAA,GAAA6D,SAAA;cAAA,OAAAA,SAAA,CAAArN,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAE8H,SAAA,CAAA7D,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA4D,SAAA,CAAAnL,IAAA;UAAA;QAAA,GAAAmK,QAAA;MAAA,CAElD;MAAA,SAAAV,qBAAAmC,EAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAA5B,qBAAA,CAAA5I,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAoI,oBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA7H,GAAA;IAAAnH,KAAA;MAAA,IAAAsR,sBAAA,GAAA3K,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA4M,UAAA;QAAA,IAAA9G,SAAA,EAAA+G,MAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAhH,OAAA,EAAAC,SAAA,EAAAgH,sBAAA;QAAA,OAAA1S,mBAAA,GAAAuB,IAAA,UAAAoR,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9M,IAAA,GAAA8M,UAAA,CAAAzO,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA6G,UAAA,CAAA9M,IAAA;cAGjC;cACMoM,MAAM,GAAGlG,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;cAC/CiG,MAAM,CAAChG,KAAK,CAACC,OAAO,qQAUnB;cACD+F,MAAM,CAACW,WAAW,GAAG,MAAM;cAC3B7G,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC6F,MAAM,CAAC;;cAEjC;cACMC,YAAY,GAAG,EAAE,EAEvB;cACMC,UAAU,GAAGtG,WAAW,CAACC,GAAG,CAAC,CAAC;cACpCmG,MAAM,CAAChG,KAAK,CAACqC,UAAU,GAAG,SAAS;cACnC2D,MAAM,CAACzF,YAAY,EAAC;cACd4F,QAAQ,GAAGvG,WAAW,CAACC,GAAG,CAAC,CAAC;cAClCoG,YAAY,CAACxN,IAAI,CAAC;gBAAE9C,IAAI,EAAE,OAAO;gBAAEgL,IAAI,EAAEwF,QAAQ,GAAGD;cAAW,CAAC,CAAC;;cAEjE;cACME,UAAU,GAAGxG,WAAW,CAACC,GAAG,CAAC,CAAC;cACpCmG,MAAM,CAAChG,KAAK,CAACM,SAAS,GAAG,aAAa;cACtC0F,MAAM,CAACzF,YAAY;cACb8F,QAAQ,GAAGzG,WAAW,CAACC,GAAG,CAAC,CAAC;cAClCoG,YAAY,CAACxN,IAAI,CAAC;gBAAE9C,IAAI,EAAE,OAAO;gBAAEgL,IAAI,EAAE0F,QAAQ,GAAGD;cAAW,CAAC,CAAC;;cAEjE;cACME,UAAU,GAAG1G,WAAW,CAACC,GAAG,CAAC,CAAC;cACpCmG,MAAM,CAAChG,KAAK,CAAC4G,OAAO,GAAG,mBAAmB;cAC1CZ,MAAM,CAACzF,YAAY;cACbgG,QAAQ,GAAG3G,WAAW,CAACC,GAAG,CAAC,CAAC;cAClCoG,YAAY,CAACxN,IAAI,CAAC;gBAAE9C,IAAI,EAAE,OAAO;gBAAEgL,IAAI,EAAE4F,QAAQ,GAAGD;cAAW,CAAC,CAAC;cAE3D/G,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACoF,MAAM,CAAC;cAE3BQ,sBAAsB,GAAGP,YAAY,CAACxF,MAAM,CAAC,UAACC,GAAG,EAAEjM,CAAC;gBAAA,OAAKiM,GAAG,GAAGjM,CAAC,CAACkM,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGsF,YAAY,CAACnN,MAAM;cAAA,OAAA4N,UAAA,CAAA7O,MAAA,WAE9F;gBACLgJ,OAAO,EAAE2F,sBAAsB,GAAG,EAAE;gBAAE;gBACtC1F,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBgH,sBAAsB,EAAEA,sBAAsB;kBAC9CP,YAAY,EAAEA;gBAChB;cACF,CAAC;YAAA;cAAAS,UAAA,CAAA9M,IAAA;cAAA8M,UAAA,CAAArF,EAAA,GAAAqF,UAAA;cAAA,OAAAA,UAAA,CAAA7O,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEsJ,UAAA,CAAArF,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAoF,UAAA,CAAA3M,IAAA;UAAA;QAAA,GAAAgM,SAAA;MAAA,CAElD;MAAA,SAAA1H,sBAAA;QAAA,OAAAyH,sBAAA,CAAAzK,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAiD,qBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA1C,GAAA;IAAAnH,KAAA;MAAA,IAAAqS,oBAAA,GAAA1L,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA2N,UAAA;QAAA,IAAA7H,SAAA,EAAA8H,IAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAA/H,OAAA,EAAAC,SAAA,EAAAgH,sBAAA;QAAA,OAAA1S,mBAAA,GAAAuB,IAAA,UAAAkS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5N,IAAA,GAAA4N,UAAA,CAAAvP,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA2H,UAAA,CAAA5N,IAAA;cAGjC;cACMmN,IAAI,GAAGjH,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;cAC3CgH,IAAI,CAAC/G,KAAK,CAACC,OAAO,6KAOjB;cAED8G,IAAI,CAAC1B,SAAS,wsBASb;cAEDvF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC4G,IAAI,CAAC;;cAE/B;cACMC,MAAM,GAAGD,IAAI,CAACxB,gBAAgB,CAAC,yBAAyB,CAAC;cACzD0B,gBAAgB,GAAG,EAAE;cAAAC,SAAA,GAAAO,0BAAA,CAEPT,MAAM;cAAAQ,UAAA,CAAA5N,IAAA;cAAAsN,SAAA,CAAAjR,CAAA;YAAA;cAAA,KAAAkR,KAAA,GAAAD,SAAA,CAAA9S,CAAA,IAAAkD,IAAA;gBAAAkQ,UAAA,CAAAvP,IAAA;gBAAA;cAAA;cAAfmP,KAAK,GAAAD,KAAA,CAAA3S,KAAA;cACR6S,gBAAgB,GAAGzH,WAAW,CAACC,GAAG,CAAC,CAAC,EAE1C;cACAuH,KAAK,CAACpH,KAAK,CAAC0H,WAAW,GAAG,SAAS;cACnCN,KAAK,CAACpH,KAAK,CAAC4G,OAAO,GAAG,mCAAmC;;cAEzD;cACA,IAAIQ,KAAK,CAACzR,IAAI,KAAK,MAAM,EAAE;gBACzByR,KAAK,CAAC5S,KAAK,GAAG,MAAM;cACtB,CAAC,MAAM,IAAI4S,KAAK,CAACzR,IAAI,KAAK,UAAU,EAAE;gBACpCyR,KAAK,CAAC5S,KAAK,GAAG,QAAQ;cACxB,CAAC,MAAM,IAAI4S,KAAK,CAACO,OAAO,KAAK,UAAU,EAAE;gBACvCP,KAAK,CAAC5S,KAAK,GAAG,QAAQ;cACxB;cAEA4S,KAAK,CAAC7G,YAAY,EAAC;cAEb+G,cAAc,GAAG1H,WAAW,CAACC,GAAG,CAAC,CAAC;cACxCoH,gBAAgB,CAACxO,IAAI,CAAC6O,cAAc,GAAGD,gBAAgB,CAAC;cAAAG,UAAA,CAAAvP,IAAA;cAAA,OAElD,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,EAAE,CAAC;cAAA,EAAC;YAAA;cAAAuQ,UAAA,CAAAvP,IAAA;cAAA;YAAA;cAAAuP,UAAA,CAAAvP,IAAA;cAAA;YAAA;cAAAuP,UAAA,CAAA5N,IAAA;cAAA4N,UAAA,CAAAnG,EAAA,GAAAmG,UAAA;cAAAN,SAAA,CAAAnT,CAAA,CAAAyT,UAAA,CAAAnG,EAAA;YAAA;cAAAmG,UAAA,CAAA5N,IAAA;cAAAsN,SAAA,CAAAlR,CAAA;cAAA,OAAAwR,UAAA,CAAArN,MAAA;YAAA;cAGjDoF,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACmG,IAAI,CAAC;cAEzBP,sBAAsB,GAAGS,gBAAgB,CAACxG,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGsG,gBAAgB,CAACnO,MAAM;cAAA,OAAA0O,UAAA,CAAA3P,MAAA,WAEvG;gBACLgJ,OAAO,EAAE2F,sBAAsB,GAAG,EAAE;gBAAE;gBACtC1F,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBoI,UAAU,EAAEZ,MAAM,CAAClO,MAAM;kBACzB0N,sBAAsB,EAAEA,sBAAsB;kBAC9CqB,kBAAkB,EAAE7G,IAAI,CAACC,GAAG,CAAA5F,KAAA,CAAR2F,IAAI,EAAQiG,gBAAgB;gBAClD;cACF,CAAC;YAAA;cAAAO,UAAA,CAAA5N,IAAA;cAAA4N,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAAA,OAAAA,UAAA,CAAA3P,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEoK,UAAA,CAAAM,EAAA,CAAMxG;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAkG,UAAA,CAAAzN,IAAA;UAAA;QAAA,GAAA+M,SAAA;MAAA,CAElD;MAAA,SAAAxI,oBAAA;QAAA,OAAAuI,oBAAA,CAAAxL,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAkD,mBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA3C,GAAA;IAAAnH,KAAA;MAAA,IAAAuT,qBAAA,GAAA5M,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA6O,UAAA;QAAA,IAAA/I,SAAA,EAAAgJ,KAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAjJ,OAAA,EAAAC,SAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAAoT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9O,IAAA,GAAA8O,UAAA,CAAAzQ,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA6I,UAAA,CAAA9O,IAAA;cAGjC;cACMqO,KAAK,GAAGnI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC3CkI,KAAK,CAACjI,KAAK,CAACC,OAAO,6UAalB;cAEKiI,YAAY,GAAGpI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAClDmI,YAAY,CAAClI,KAAK,CAACC,OAAO,wNAQzB;cACDiI,YAAY,CAAC7C,SAAS,yTAIrB;cAED4C,KAAK,CAAC9H,WAAW,CAAC+H,YAAY,CAAC;cAC/BpI,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC8H,KAAK,CAAC;;cAEhC;cACME,SAAS,GAAGvI,WAAW,CAACC,GAAG,CAAC,CAAC;cACnCoI,KAAK,CAACjI,KAAK,CAAC2I,OAAO,GAAG,GAAG;cACzBT,YAAY,CAAClI,KAAK,CAACM,SAAS,GAAG,UAAU;cACzC2H,KAAK,CAAC1H,YAAY,EAAC;cAAAmI,UAAA,CAAAzQ,IAAA;cAAA,OAEb,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,GAAG,CAAC;cAAA,EAAC;YAAA;cAChDmR,OAAO,GAAGxI,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BwI,QAAQ,GAAGD,OAAO,GAAGD,SAAS,EAEpC;cACMG,UAAU,GAAG1I,WAAW,CAACC,GAAG,CAAC,CAAC;cACpCoI,KAAK,CAACjI,KAAK,CAAC2I,OAAO,GAAG,GAAG;cACzBT,YAAY,CAAClI,KAAK,CAACM,SAAS,GAAG,YAAY;cAC3C2H,KAAK,CAAC1H,YAAY;cAAAmI,UAAA,CAAAzQ,IAAA;cAAA,OAEZ,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,GAAG,CAAC;cAAA,EAAC;YAAA;cAChDsR,QAAQ,GAAG3I,WAAW,CAACC,GAAG,CAAC,CAAC;cAC5B2I,SAAS,GAAGD,QAAQ,GAAGD,UAAU;cAEjC/I,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACqH,KAAK,CAAC;cAAA,OAAAS,UAAA,CAAA7Q,MAAA,WAEzB;gBACLgJ,OAAO,EAAEwH,QAAQ,GAAG,GAAG,IAAIG,SAAS,GAAG,GAAG;gBAAE;gBAC5C1H,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpB6I,QAAQ,EAAEA,QAAQ;kBAClBG,SAAS,EAAEA,SAAS;kBACpBI,eAAe,EAAEP,QAAQ,GAAG,GAAG,IAAIG,SAAS,GAAG;gBACjD;cACF,CAAC;YAAA;cAAAE,UAAA,CAAA9O,IAAA;cAAA8O,UAAA,CAAArH,EAAA,GAAAqH,UAAA;cAAA,OAAAA,UAAA,CAAA7Q,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEsL,UAAA,CAAArH,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAoH,UAAA,CAAA3O,IAAA;UAAA;QAAA,GAAAiO,SAAA;MAAA,CAElD;MAAA,SAAAzJ,qBAAA;QAAA,OAAAwJ,qBAAA,CAAA1M,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAmD,oBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA5C,GAAA;IAAAnH,KAAA;MAAA,IAAAqU,uBAAA,GAAA1N,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA2P,UAAA;QAAA,IAAA7J,SAAA,EAAA8J,SAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAxU,CAAA,EAAAyU,QAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA7J,OAAA,EAAAC,SAAA,EAAA6J,qBAAA;QAAA,OAAAvV,mBAAA,GAAAuB,IAAA,UAAAiU,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3P,IAAA,GAAA2P,UAAA,CAAAtR,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA0J,UAAA,CAAA3P,IAAA;cAGjC;cACMmP,SAAS,GAAGjJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CgJ,SAAS,CAAC/I,KAAK,CAACC,OAAO,yFAItB;cAED8I,SAAS,CAAC1D,SAAS,2YASlB;cAEDvF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC4I,SAAS,CAAC;;cAEpC;cACMC,iBAAiB,GAAGD,SAAS,CAACxD,gBAAgB,CAAC,YAAY,CAAC;cAC5D0D,eAAe,GAAG,EAAE;cAEjBxU,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGuU,iBAAiB,CAAClQ,MAAM;gBAAAyQ,UAAA,CAAAtR,IAAA;gBAAA;cAAA;cACpCiR,QAAQ,GAAGtJ,WAAW,CAACC,GAAG,CAAC,CAAC;cAE5BsJ,OAAO,GAAGH,iBAAiB,CAACvU,CAAC,CAAC;cACpC0U,OAAO,CAACK,KAAK,CAAC,CAAC;cACfL,OAAO,CAACnJ,KAAK,CAAC4G,OAAO,GAAG,mBAAmB;cAE3CuC,OAAO,CAAC5I,YAAY,EAAC;cAEf6I,MAAM,GAAGxJ,WAAW,CAACC,GAAG,CAAC,CAAC;cAChCoJ,eAAe,CAACxQ,IAAI,CAAC2Q,MAAM,GAAGF,QAAQ,CAAC;cAAAK,UAAA,CAAAtR,IAAA;cAAA,OAEjC,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,EAAE,CAAC;cAAA,EAAC;YAAA;cAZTxC,CAAC,EAAE;cAAA8U,UAAA,CAAAtR,IAAA;cAAA;YAAA;cAe3CsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACmI,SAAS,CAAC;cAE9BM,qBAAqB,GAAGJ,eAAe,CAACxI,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGsI,eAAe,CAACnQ,MAAM;cAAA,OAAAyQ,UAAA,CAAA1R,MAAA,WAEpG;gBACLgJ,OAAO,EAAEwI,qBAAqB,GAAG,EAAE;gBAAE;gBACrCvI,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBiK,qBAAqB,EAAET,iBAAiB,CAAClQ,MAAM;kBAC/CuQ,qBAAqB,EAAEA,qBAAqB;kBAC5CJ,eAAe,EAAEA;gBACnB;cACF,CAAC;YAAA;cAAAM,UAAA,CAAA3P,IAAA;cAAA2P,UAAA,CAAAlI,EAAA,GAAAkI,UAAA;cAAA,OAAAA,UAAA,CAAA1R,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEmM,UAAA,CAAAlI,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAiI,UAAA,CAAAxP,IAAA;UAAA;QAAA,GAAA+O,SAAA;MAAA,CAElD;MAAA,SAAAtK,uBAAA;QAAA,OAAAqK,uBAAA,CAAAxN,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAoD,sBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA7C,GAAA;IAAAnH,KAAA;MAAA,IAAAkV,8BAAA,GAAAvO,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAAwQ,UAAA;QAAA,IAAA1K,SAAA,EAAA8J,SAAA,EAAAa,mBAAA,EAAAC,OAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAxK,OAAA,EAAAC,SAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAA2U,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArQ,IAAA,GAAAqQ,UAAA,CAAAhS,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAoK,UAAA,CAAArQ,IAAA;cAGjC;cACMmP,SAAS,GAAGjJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CgJ,SAAS,CAAC/I,KAAK,CAACC,OAAO,yFAItB;cAED8I,SAAS,CAAC1D,SAAS,syBAiBlB;cAEDvF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC4I,SAAS,CAAC;;cAEpC;cACMa,mBAAmB,GAAG,CAC1B;gBACE1Q,IAAI,EAAE,UAAU;gBAChBgR,KAAK,EAAE,SAAAA,MAAA;kBAAA,OAAMnB,SAAS,CAACxD,gBAAgB,CAAC,wBAAwB,CAAC,CAACzM,MAAM,GAAG,CAAC;gBAAA;cAC9E,CAAC,EACD;gBACEI,IAAI,EAAE,UAAU;gBAChBgR,KAAK,EAAE,SAAAA,MAAA,EAAM;kBACX,IAAMC,MAAM,GAAGpB,SAAS,CAACxD,gBAAgB,CAAC,KAAK,CAAC;kBAChD,OAAOzK,KAAK,CAACC,IAAI,CAACoP,MAAM,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG;oBAAA,OAAIA,GAAG,CAACC,YAAY,CAAC,KAAK,CAAC;kBAAA,EAAC;gBACjE;cACF,CAAC,EACD;gBACEpR,IAAI,EAAE,aAAa;gBACnBgR,KAAK,EAAE,SAAAA,MAAA;kBAAA,OAAMnB,SAAS,CAACxD,gBAAgB,CAAC,cAAc,CAAC,CAACzM,MAAM,GAAG,CAAC;gBAAA;cACpE,CAAC,EACD;gBACEI,IAAI,EAAE,kBAAkB;gBACxBgR,KAAK,EAAE,SAAAA,MAAA;kBAAA,OAAMnB,SAAS,CAACxD,gBAAgB,CAAC,oBAAoB,CAAC,CAACzM,MAAM,GAAG,CAAC;gBAAA;cAC1E,CAAC,EACD;gBACEI,IAAI,EAAE,mBAAmB;gBACzBgR,KAAK,EAAE,SAAAA,MAAA,EAAM;kBACX,IAAMK,gBAAgB,GAAGxB,SAAS,CAACxD,gBAAgB,CAAC,oDAAoD,CAAC;kBACzG,OAAOgF,gBAAgB,CAACzR,MAAM,GAAG,CAAC;gBACpC;cACF,CAAC,CACF;cAEK+Q,OAAO,GAAGD,mBAAmB,CAACY,GAAG,CAAC,UAAAN,KAAK;gBAAA,OAAK;kBAChDhR,IAAI,EAAEgR,KAAK,CAAChR,IAAI;kBAChBuR,MAAM,EAAEP,KAAK,CAACA,KAAK,CAAC;gBACtB,CAAC;cAAA,CAAC,CAAC;cAEGJ,YAAY,GAAGD,OAAO,CAACa,MAAM,CAAC,UAAAzW,CAAC;gBAAA,OAAIA,CAAC,CAACwW,MAAM;cAAA,EAAC,CAAC3R,MAAM;cACnDiR,WAAW,GAAGF,OAAO,CAAC/Q,MAAM;cAE5ByG,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACmI,SAAS,CAAC;cAAA,OAAAkB,UAAA,CAAApS,MAAA,WAE7B;gBACLgJ,OAAO,EAAEiJ,YAAY,IAAIC,WAAW,GAAG,GAAG;gBAAE;gBAC5CjJ,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBsK,YAAY,EAAEA,YAAY;kBAC1BC,WAAW,EAAEA,WAAW;kBACxBY,kBAAkB,EAAE3J,IAAI,CAAC4J,KAAK,CAAEd,YAAY,GAAGC,WAAW,GAAI,GAAG,CAAC;kBAClEF,OAAO,EAAEA;gBACX;cACF,CAAC;YAAA;cAAAI,UAAA,CAAArQ,IAAA;cAAAqQ,UAAA,CAAA5I,EAAA,GAAA4I,UAAA;cAAA,OAAAA,UAAA,CAAApS,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAE6M,UAAA,CAAA5I,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA2I,UAAA,CAAAlQ,IAAA;UAAA;QAAA,GAAA4P,SAAA;MAAA,CAElD;MAAA,SAAAlL,8BAAA;QAAA,OAAAiL,8BAAA,CAAArO,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAqD,6BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA9C,GAAA;IAAAnH,KAAA;MAAA,IAAAqW,kBAAA,GAAA1P,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA2R,UAAA;QAAA,IAAA7L,SAAA,EAAA8J,SAAA,EAAAgC,UAAA,EAAAC,eAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAlQ,IAAA,EAAAmO,OAAA,EAAAgC,QAAA,EAAA5L,OAAA,EAAAC,SAAA,EAAA4L,eAAA,EAAAC,UAAA;QAAA,OAAAvX,mBAAA,GAAAuB,IAAA,UAAAiW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3R,IAAA,GAAA2R,UAAA,CAAAtT,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA0L,UAAA,CAAA3R,IAAA;cAGjC;cACMmP,SAAS,GAAGjJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC/CgJ,SAAS,CAAC/I,KAAK,CAACC,OAAO,yFAItB;cAEK8K,UAAU,GAAG,CACjB;gBAAES,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE,SAAS;gBAAEvS,IAAI,EAAE;cAAO,CAAC,EAC9C;gBAAEsS,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE,SAAS;gBAAEvS,IAAI,EAAE;cAAO,CAAC,EAC9C;gBAAEsS,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE,SAAS;gBAAEvS,IAAI,EAAE;cAAO,CAAC,EAC9C;gBAAEsS,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE,SAAS;gBAAEvS,IAAI,EAAE;cAAO,CAAC,EAC9C;gBAAEsS,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE,SAAS;gBAAEvS,IAAI,EAAE;cAAO,CAAC,CAC/C;cAEK8R,eAAe,GAAG,EAAE;cAE1B,KAAAC,EAAA,MAAAC,WAAA,GAAmBH,UAAU,EAAAE,EAAA,GAAAC,WAAA,CAAApS,MAAA,EAAAmS,EAAA,IAAE;gBAApBjQ,IAAI,GAAAkQ,WAAA,CAAAD,EAAA;gBACP9B,OAAO,GAAGrJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC7CoJ,OAAO,CAACnJ,KAAK,CAACC,OAAO,oCAAAI,MAAA,CACCrF,IAAI,CAACwQ,EAAE,0BAAAnL,MAAA,CAClBrF,IAAI,CAACyQ,EAAE,oEAGjB;gBACDtC,OAAO,CAACxC,WAAW,iCAAAtG,MAAA,CAAarF,IAAI,CAAC9B,IAAI,CAAE;gBAC3C6P,SAAS,CAAC5I,WAAW,CAACgJ,OAAO,CAAC;;gBAE9B;gBACMgC,QAAQ,GAAG,IAAI,CAACO,sBAAsB,CAAC1Q,IAAI,CAACwQ,EAAE,EAAExQ,IAAI,CAACyQ,EAAE,CAAC;gBAC9DT,eAAe,CAACvS,IAAI,CAAC;kBACnBS,IAAI,EAAE8B,IAAI,CAAC9B,IAAI;kBACfmJ,UAAU,EAAErH,IAAI,CAACwQ,EAAE;kBACnBG,UAAU,EAAE3Q,IAAI,CAACyQ,EAAE;kBACnBN,QAAQ,EAAEA,QAAQ;kBAClBS,MAAM,EAAET,QAAQ,IAAI,GAAG;kBACvBU,OAAO,EAAEV,QAAQ,IAAI;gBACvB,CAAC,CAAC;cACJ;cAEArL,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC4I,SAAS,CAAC;;cAEpC;cACAA,SAAS,CAACxI,YAAY;cAEhBhB,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACmI,SAAS,CAAC;cAE9BqC,eAAe,GAAGJ,eAAe,CAACN,MAAM,CAAC,UAAAzW,CAAC;gBAAA,OAAIA,CAAC,CAAC2X,MAAM;cAAA,EAAC,CAAC9S,MAAM;cAC9DuS,UAAU,GAAGL,eAAe,CAAClS,MAAM;cAAA,OAAAyS,UAAA,CAAA1T,MAAA,WAElC;gBACLgJ,OAAO,EAAEuK,eAAe,IAAIC,UAAU,GAAG,GAAG;gBAAE;gBAC9CvK,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpB6L,UAAU,EAAEA,UAAU;kBACtBD,eAAe,EAAEA,eAAe;kBAChCU,gBAAgB,EAAEd,eAAe,CAACN,MAAM,CAAC,UAAAzW,CAAC;oBAAA,OAAIA,CAAC,CAAC4X,OAAO;kBAAA,EAAC,CAAC/S,MAAM;kBAC/DkS,eAAe,EAAEA;gBACnB;cACF,CAAC;YAAA;cAAAO,UAAA,CAAA3R,IAAA;cAAA2R,UAAA,CAAAlK,EAAA,GAAAkK,UAAA;cAAA,OAAAA,UAAA,CAAA1T,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEmO,UAAA,CAAAlK,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAiK,UAAA,CAAAxR,IAAA;UAAA;QAAA,GAAA+Q,SAAA;MAAA,CAElD;MAAA,SAAApM,kBAAA;QAAA,OAAAmM,kBAAA,CAAAxP,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAsD,iBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAA/C,GAAA;IAAAnH,KAAA;MAAA,IAAAuX,uBAAA,GAAA5Q,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA6S,UAAA;QAAA,IAAA/M,SAAA,EAAAgN,UAAA,EAAAC,KAAA,EAAApL,OAAA,EAAAvB,OAAA,EAAAC,SAAA,EAAAwF,KAAA;QAAA,OAAAlR,mBAAA,GAAAuB,IAAA,UAAA8W,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxS,IAAA,GAAAwS,UAAA,CAAAnU,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAAuM,UAAA,CAAAxS,IAAA;cAGjC;cACMqS,UAAU,GAAGrM,WAAW,CAACyM,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;cAC1DH,KAAK,GAAGtM,WAAW,CAACyM,gBAAgB,CAAC,OAAO,CAAC;cAE7CvL,OAAO,GAAG;gBACdwL,gBAAgB,EAAEL,UAAU,GAAGA,UAAU,CAACM,wBAAwB,GAAGN,UAAU,CAACO,0BAA0B,GAAG,CAAC;gBAC9GC,YAAY,EAAER,UAAU,GAAGA,UAAU,CAACS,YAAY,GAAGT,UAAU,CAACU,cAAc,GAAG,CAAC;gBAClFC,UAAU,EAAGV,KAAK,CAACW,IAAI,CAAC,UAAAxW,CAAC;kBAAA,OAAIA,CAAC,CAAC6C,IAAI,KAAK,aAAa;gBAAA,EAAC,IAAIgT,KAAK,CAACW,IAAI,CAAC,UAAAxW,CAAC;kBAAA,OAAIA,CAAC,CAAC6C,IAAI,KAAK,aAAa;gBAAA,EAAC,CAAC+F,SAAS,IAAK,CAAC;gBACnH6N,oBAAoB,EAAGZ,KAAK,CAACW,IAAI,CAAC,UAAAxW,CAAC;kBAAA,OAAIA,CAAC,CAAC6C,IAAI,KAAK,wBAAwB;gBAAA,EAAC,IAAIgT,KAAK,CAACW,IAAI,CAAC,UAAAxW,CAAC;kBAAA,OAAIA,CAAC,CAAC6C,IAAI,KAAK,wBAAwB;gBAAA,EAAC,CAAC+F,SAAS,IAAK;cACpJ,CAAC;cAEKM,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACI+F,KAAK,GAAG,CAAC;cACb,IAAIlE,OAAO,CAAC8L,UAAU,GAAG,IAAI,EAAE5H,KAAK,IAAI,EAAE,EAAC;cAC3C,IAAIlE,OAAO,CAACgM,oBAAoB,GAAG,IAAI,EAAE9H,KAAK,IAAI,EAAE,EAAC;cACrD,IAAIlE,OAAO,CAACwL,gBAAgB,GAAG,IAAI,EAAEtH,KAAK,IAAI,EAAE,EAAC;cACjD,IAAIlE,OAAO,CAAC2L,YAAY,GAAG,IAAI,EAAEzH,KAAK,IAAI,EAAE,EAAC;cAAA,OAAAoH,UAAA,CAAAvU,MAAA,WAEtC;gBACLgJ,OAAO,EAAEmE,KAAK,IAAI,EAAE;gBAAE;gBACtBlE,OAAO,EAAAiM,aAAA;kBACLvN,SAAS,EAAEA,SAAS;kBACpBwF,KAAK,EAAEA;gBAAK,GACTlE,OAAO;cAEd,CAAC;YAAA;cAAAsL,UAAA,CAAAxS,IAAA;cAAAwS,UAAA,CAAA/K,EAAA,GAAA+K,UAAA;cAAA,OAAAA,UAAA,CAAAvU,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEgP,UAAA,CAAA/K,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAA8K,UAAA,CAAArS,IAAA;UAAA;QAAA,GAAAiS,SAAA;MAAA,CAElD;MAAA,SAAArN,uBAAA;QAAA,OAAAoN,uBAAA,CAAA1Q,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAuD,sBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAhD,GAAA;IAAAnH,KAAA;MAAA,IAAAwY,2BAAA,GAAA7R,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA8T,UAAA;QAAA,IAAAhO,SAAA,EAAAiO,MAAA,EAAAlN,KAAA,EAAAmN,YAAA,EAAAC,SAAA,EAAAC,GAAA,EAAAC,aAAA,EAAAC,IAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAlO,OAAA,EAAAC,SAAA,EAAAkO,eAAA;QAAA,OAAA5Z,mBAAA,GAAAuB,IAAA,UAAAsY,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhU,IAAA,GAAAgU,UAAA,CAAA3V,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA+N,UAAA,CAAAhU,IAAA;cAGjC;cACMsT,MAAM,GAAGpN,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC5CmN,MAAM,CAAClN,KAAK,CAACC,OAAO,oQASnB;;cAED;cACMD,KAAK,GAAGF,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;cAC7CC,KAAK,CAAC2G,WAAW,6IAKhB;cACD7G,QAAQ,CAAC+N,IAAI,CAAC1N,WAAW,CAACH,KAAK,CAAC;cAChCF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAAC+M,MAAM,CAAC;;cAEjC;cACMC,YAAY,GAAG,CACnB;gBAAE7L,OAAO,EAAE,SAAS;gBAAEwM,QAAQ,EAAE;cAAE,CAAC,EACnC;gBAAExM,OAAO,EAAE,QAAQ;gBAAEwM,QAAQ,EAAE;cAAG,CAAC,EACnC;gBAAExM,OAAO,EAAE,SAAS;gBAAEwM,QAAQ,EAAE;cAAG,CAAC,EACpC;gBAAExM,OAAO,EAAE,MAAM;gBAAEwM,QAAQ,EAAE;cAAI,CAAC,CACnC;cAEKV,SAAS,GAAG,EAAE;cAAAC,GAAA,MAAAC,aAAA,GAEDH,YAAY;YAAA;cAAA,MAAAE,GAAA,GAAAC,aAAA,CAAAxU,MAAA;gBAAA8U,UAAA,CAAA3V,IAAA;gBAAA;cAAA;cAApBsV,IAAI,GAAAD,aAAA,CAAAD,GAAA;cACPG,SAAS,GAAG5N,WAAW,CAACC,GAAG,CAAC,CAAC,EAEnC;cACAqN,MAAM,CAACa,YAAY,CAAC,eAAe,EAAER,IAAI,CAACO,QAAQ,CAAC;cACnDZ,MAAM,CAACa,YAAY,CAAC,YAAY,EAAER,IAAI,CAACjM,OAAO,CAAC;cAAAsM,UAAA,CAAA3V,IAAA;cAAA,OAEzC,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,GAAG,CAAC;cAAA,EAAC;YAAA;cAEhDwW,OAAO,GAAG7N,WAAW,CAACC,GAAG,CAAC,CAAC;cACjCuN,SAAS,CAAC3U,IAAI,CAACgV,OAAO,GAAGD,SAAS,CAAC;YAAA;cAAAH,GAAA;cAAAO,UAAA,CAAA3V,IAAA;cAAA;YAAA;cAG/BsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS,EAErC;cACAa,QAAQ,CAACI,IAAI,CAACU,WAAW,CAACsM,MAAM,CAAC;cACjCpN,QAAQ,CAAC+N,IAAI,CAACjN,WAAW,CAACZ,KAAK,CAAC;cAE1B0N,eAAe,GAAGN,SAAS,CAAC3M,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;gBAAA,OAAKD,GAAG,GAAGC,IAAI;cAAA,GAAE,CAAC,CAAC,GAAGyM,SAAS,CAACtU,MAAM;cAAA,OAAA8U,UAAA,CAAA/V,MAAA,WAElF;gBACLgJ,OAAO,EAAE6M,eAAe,GAAG,GAAG;gBAAE;gBAChC5M,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBwO,SAAS,EAAEb,YAAY,CAACrU,MAAM;kBAC9B4U,eAAe,EAAEA,eAAe;kBAChCN,SAAS,EAAEA;gBACb;cACF,CAAC;YAAA;cAAAQ,UAAA,CAAAhU,IAAA;cAAAgU,UAAA,CAAAvM,EAAA,GAAAuM,UAAA;cAAA,OAAAA,UAAA,CAAA/V,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEwQ,UAAA,CAAAvM,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAsM,UAAA,CAAA7T,IAAA;UAAA;QAAA,GAAAkT,SAAA;MAAA,CAElD;MAAA,SAAArO,2BAAA;QAAA,OAAAoO,2BAAA,CAAA3R,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAwD,0BAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAjD,GAAA;IAAAnH,KAAA;MAAA,IAAAyZ,wBAAA,GAAA9S,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA+U,UAAA;QAAA,IAAAjP,SAAA,EAAAkP,aAAA,EAAAC,aAAA,EAAAC,GAAA,EAAAC,cAAA,EAAAlR,KAAA,EAAA4H,KAAA,EAAAzF,OAAA,EAAAC,SAAA,EAAA+O,YAAA;QAAA,OAAAza,mBAAA,GAAAuB,IAAA,UAAAmZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7U,IAAA,GAAA6U,UAAA,CAAAxW,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA4O,UAAA,CAAA7U,IAAA;cAGjC;cACMuU,aAAa,GAAG,CACpB;gBAAExY,IAAI,EAAE,YAAY;gBAAE2L,OAAO,EAAE,YAAY;gBAAEoN,OAAO,EAAE;cAAO,CAAC,EAC9D;gBAAE/Y,IAAI,EAAE,SAAS;gBAAE2L,OAAO,EAAE,gBAAgB;gBAAEoN,OAAO,EAAE;cAAO,CAAC,EAC/D;gBAAE/Y,IAAI,EAAE,SAAS;gBAAE2L,OAAO,EAAE,MAAM;gBAAEoN,OAAO,EAAE;cAAO,CAAC,EACrD;gBAAE/Y,IAAI,EAAE,UAAU;gBAAE2L,OAAO,EAAE,qBAAqB;gBAAEoN,OAAO,EAAE;cAAY,CAAC,CAC3E;cAEKN,aAAa,GAAG,EAAE;cAExB,KAAAC,GAAA,MAAAC,cAAA,GAAoBH,aAAa,EAAAE,GAAA,GAAAC,cAAA,CAAAxV,MAAA,EAAAuV,GAAA,IAAE;gBAAxBjR,KAAK,GAAAkR,cAAA,CAAAD,GAAA;gBACd;gBACIrJ,KAAK,GAAG,CAAC,EAEb;gBACA,IAAI5H,KAAK,CAACkE,OAAO,CAACxI,MAAM,IAAI,EAAE,IAAIsE,KAAK,CAACkE,OAAO,CAACxI,MAAM,IAAI,GAAG,EAAEkM,KAAK,IAAI,EAAE;;gBAE1E;gBACA,IAAI5H,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,GAAG,CAAC,IAAIvR,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,IAAI,CAAC,IAAIvR,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,GAAG,CAAC,EAAE3J,KAAK,IAAI,EAAE;;gBAE3G;gBACA,IAAI5H,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,GAAG,CAAC,KAAKvR,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,IAAI,CAAC,IAAIvR,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE3J,KAAK,IAAI,EAAE;;gBAE9G;gBACA,IAAI,CAAC5H,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,IAAI,CAAC,IAAIvR,KAAK,CAACkE,OAAO,CAACqN,QAAQ,CAAC,IAAI,CAAC,EAAE3J,KAAK,IAAI,EAAE;gBAE9EoJ,aAAa,CAAC3V,IAAI,CAAC;kBACjB9C,IAAI,EAAEyH,KAAK,CAACzH,IAAI;kBAChB2L,OAAO,EAAElE,KAAK,CAACkE,OAAO;kBACtB0D,KAAK,EAAEA,KAAK;kBACZ0J,OAAO,EAAEtR,KAAK,CAACsR;gBACjB,CAAC,CAAC;cACJ;cAEMnP,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS;cAE/BsP,YAAY,GAAGH,aAAa,CAAC3N,MAAM,CAAC,UAACC,GAAG,EAAEkO,IAAI;gBAAA,OAAKlO,GAAG,GAAGkO,IAAI,CAAC5J,KAAK;cAAA,GAAE,CAAC,CAAC,GAAGoJ,aAAa,CAACtV,MAAM;cAAA,OAAA2V,UAAA,CAAA5W,MAAA,WAE7F;gBACLgJ,OAAO,EAAE0N,YAAY,IAAI,EAAE;gBAAE;gBAC7BzN,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBqP,YAAY,EAAEV,aAAa,CAACrV,MAAM;kBAClCyV,YAAY,EAAEA,YAAY;kBAC1BH,aAAa,EAAEA;gBACjB;cACF,CAAC;YAAA;cAAAK,UAAA,CAAA7U,IAAA;cAAA6U,UAAA,CAAApN,EAAA,GAAAoN,UAAA;cAAA,OAAAA,UAAA,CAAA5W,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEqR,UAAA,CAAApN,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAmN,UAAA,CAAA1U,IAAA;UAAA;QAAA,GAAAmU,SAAA;MAAA,CAElD;MAAA,SAAArP,wBAAA;QAAA,OAAAoP,wBAAA,CAAA5S,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAAyD,uBAAA;IAAA;IAED;AACF;AACA;IAFE;EAAA;IAAAlD,GAAA;IAAAnH,KAAA;MAAA,IAAAsa,kBAAA,GAAA3T,iBAAA,eAAArH,mBAAA,GAAAqF,IAAA,CAGA,SAAA4V,UAAA;QAAA,IAAA9P,SAAA,EAAA+P,iBAAA,EAAAC,eAAA,EAAAC,GAAA,EAAAC,kBAAA,EAAAC,QAAA,EAAAC,aAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAhQ,OAAA,EAAAC,SAAA,EAAAgQ,oBAAA,EAAAC,mBAAA;QAAA,OAAA3b,mBAAA,GAAAuB,IAAA,UAAAqa,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/V,IAAA,GAAA+V,UAAA,CAAA1X,IAAA;YAAA;cACQgH,SAAS,GAAGW,WAAW,CAACC,GAAG,CAAC,CAAC;cAAA8P,UAAA,CAAA/V,IAAA;cAGjC;cACMoV,iBAAiB,GAAG,CACxB;gBACE9V,IAAI,EAAE,QAAQ;gBACd0W,QAAQ,EAAE,SAAAA,SAAA,EAAM;kBACd;kBACA,OAAO;oBAAE/O,OAAO,EAAE,IAAI;oBAAEF,IAAI,EAAE;kBAAI,CAAC;gBACrC;cACF,CAAC,EACD;gBACEzH,IAAI,EAAE,QAAQ;gBACd0W,QAAQ,EAAE,SAAAA,SAAA,EAAM;kBACd;kBACA,OAAO;oBAAE/O,OAAO,EAAE,IAAI;oBAAEF,IAAI,EAAE;kBAAI,CAAC;gBACrC;cACF,CAAC,EACD;gBACEzH,IAAI,EAAE,QAAQ;gBACd0W,QAAQ,EAAE,SAAAA,SAAA,EAAM;kBACd;kBACA,OAAO;oBAAE/O,OAAO,EAAE,IAAI;oBAAEF,IAAI,EAAE;kBAAI,CAAC;gBACrC;cACF,CAAC,CACF;cAEKsO,eAAe,GAAG,EAAE;cAAAC,GAAA,MAAAC,kBAAA,GAEHH,iBAAiB;YAAA;cAAA,MAAAE,GAAA,GAAAC,kBAAA,CAAArW,MAAA;gBAAA6W,UAAA,CAAA1X,IAAA;gBAAA;cAAA;cAA7BmX,QAAQ,GAAAD,kBAAA,CAAAD,GAAA;cACXG,aAAa,GAAGzP,WAAW,CAACC,GAAG,CAAC,CAAC;cACjCyP,MAAM,GAAGF,QAAQ,CAACQ,QAAQ,CAAC,CAAC;cAC5BL,WAAW,GAAG3P,WAAW,CAACC,GAAG,CAAC,CAAC;cAErCoP,eAAe,CAACxW,IAAI,CAAC;gBACnBS,IAAI,EAAEkW,QAAQ,CAAClW,IAAI;gBACnB2H,OAAO,EAAEyO,MAAM,CAACzO,OAAO;gBACvBgP,YAAY,EAAEN,WAAW,GAAGF,aAAa;gBACzCS,YAAY,EAAER,MAAM,CAAC3O;cACvB,CAAC,CAAC;cAAAgP,UAAA,CAAA1X,IAAA;cAAA,OAEI,IAAIuB,OAAO,CAAC,UAAAvC,OAAO;gBAAA,OAAIuJ,UAAU,CAACvJ,OAAO,EAAE,EAAE,CAAC;cAAA,EAAC;YAAA;cAAAiY,GAAA;cAAAS,UAAA,CAAA1X,IAAA;cAAA;YAAA;cAGjDsH,OAAO,GAAGK,WAAW,CAACC,GAAG,CAAC,CAAC;cAC3BL,SAAS,GAAGD,OAAO,GAAGN,SAAS;cAE/BuQ,oBAAoB,GAAGP,eAAe,CAACvE,MAAM,CAAC,UAAAzW,CAAC;gBAAA,OAAIA,CAAC,CAAC4M,OAAO;cAAA,EAAC,CAAC/H,MAAM;cACpE2W,mBAAmB,GAAGR,eAAe,CAACxO,MAAM,CAAC,UAACC,GAAG,EAAEzM,CAAC;gBAAA,OAAKyM,GAAG,GAAGzM,CAAC,CAAC4b,YAAY;cAAA,GAAE,CAAC,CAAC,GAAGZ,eAAe,CAACnW,MAAM;cAAA,OAAA6W,UAAA,CAAA9X,MAAA,WAEzG;gBACLgJ,OAAO,EAAE2O,oBAAoB,KAAKR,iBAAiB,CAAClW,MAAM,IAAI2W,mBAAmB,GAAG,GAAG;gBACvF3O,OAAO,EAAE;kBACPtB,SAAS,EAAEA,SAAS;kBACpBuQ,aAAa,EAAEf,iBAAiB,CAAClW,MAAM;kBACvC0W,oBAAoB,EAAEA,oBAAoB;kBAC1CC,mBAAmB,EAAEA,mBAAmB;kBACxCR,eAAe,EAAEA;gBACnB;cACF,CAAC;YAAA;cAAAU,UAAA,CAAA/V,IAAA;cAAA+V,UAAA,CAAAtO,EAAA,GAAAsO,UAAA;cAAA,OAAAA,UAAA,CAAA9X,MAAA,WAEM;gBAAEgJ,OAAO,EAAE,KAAK;gBAAEzD,KAAK,EAAEuS,UAAA,CAAAtO,EAAA,CAAMC;cAAQ,CAAC;YAAA;YAAA;cAAA,OAAAqO,UAAA,CAAA5V,IAAA;UAAA;QAAA,GAAAgV,SAAA;MAAA,CAElD;MAAA,SAAAjQ,kBAAA;QAAA,OAAAgQ,kBAAA,CAAAzT,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAA0D,iBAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;IALE;EAAA;IAAAnD,GAAA;IAAAnH,KAAA,EAMA,SAAAkX,uBAAuBF,EAAE,EAAEC,EAAE,EAAE;MAC7B;MACA;MACA,IAAMuE,WAAW,GAAG,IAAI,CAACC,YAAY,CAACzE,EAAE,CAAC;MACzC,IAAM0E,WAAW,GAAG,IAAI,CAACD,YAAY,CAACxE,EAAE,CAAC;MAEzC,IAAM0E,OAAO,GAAGnP,IAAI,CAACC,GAAG,CAAC+O,WAAW,EAAEE,WAAW,CAAC;MAClD,IAAME,MAAM,GAAGpP,IAAI,CAACG,GAAG,CAAC6O,WAAW,EAAEE,WAAW,CAAC;MAEjD,OAAO,CAACC,OAAO,GAAG,IAAI,KAAKC,MAAM,GAAG,IAAI,CAAC;IAC3C;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAzU,GAAA;IAAAnH,KAAA,EAKA,SAAAyb,aAAaI,KAAK,EAAE;MAClB;MACA,IAAMC,GAAG,GAAGD,KAAK,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAClC,IAAMtc,CAAC,GAAGuc,QAAQ,CAACF,GAAG,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAC9C,IAAM/Z,CAAC,GAAG8Z,QAAQ,CAACF,GAAG,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAC9C,IAAMC,CAAC,GAAGF,QAAQ,CAACF,GAAG,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;MAE9C,OAAO,MAAM,GAAGxc,CAAC,GAAG,MAAM,GAAGyC,CAAC,GAAG,MAAM,GAAGga,CAAC;IAC7C;EAAC;AAAA;AAGH,eAAezU,uBAAuB", "ignoreList": []}]}