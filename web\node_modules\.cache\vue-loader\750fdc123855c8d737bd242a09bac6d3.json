{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\NoticeMarquee.vue", "mtime": 1753243085891}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\nimport { getAction } from '@/api/manage'\nimport moment from 'moment'\nimport ShowAnnouncement from '@/components/tools/ShowAnnouncement'\n\nexport default {\n  name: 'NoticeMarquee',\n  components: {\n    ShowAnnouncement\n  },\n  data() {\n    return {\n      notices: [],\n      rotateSpeed: 3000, // 默认轮播速度\n      showMarquee: false, // 默认不显示公告轮播\n      marqueeConfigKey: 'notice_marquee_config', // 配置在系统配置表中的键名\n      noticeConfig: null, // 存储公告轮播配置\n      // 新增定时器ID，用于清除定时器\n      checkTimer: null\n    }\n  },\n  computed: {\n    // 新增计算属性，过滤有效公告\n    validNotices() {\n      return this.notices.filter(notice => this.isNoticeValid(notice));\n    }\n  },\n  mounted() {\n    // 只有在用户已登录时才加载公告轮播配置\n    if (this.isUserLoggedIn()) {\n      this.loadMarqueeConfig()\n      // 新增定时器，每分钟检查一次公告有效性\n      this.checkTimer = setInterval(() => {\n        this.checkNoticesValidity();\n      }, 60000); // 每分钟检查一次\n    }\n  },\n  beforeDestroy() {\n    // 新增组件销毁前清除定时器\n    if (this.checkTimer) {\n      clearInterval(this.checkTimer);\n      this.checkTimer = null;\n    }\n  },\n  watch: {\n    // 监听系统配置变化\n    '$store.getters.sysConfig': {\n      handler(newVal) {\n        // 当系统配置变化时，重新加载公告轮播配置\n        if (newVal) {\n          this.loadMarqueeConfig()\n        }\n      },\n      deep: true\n    },\n    // 监听有效公告数量变化\n    validNotices: {\n      handler(newVal) {\n        // 如果有效公告为空，则隐藏轮播\n        this.showMarquee = newVal && newVal.length > 0;\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 检查用户是否已登录\n    isUserLoggedIn() {\n      try {\n        const token = this.$ls.get('Access-Token')\n        return !!token\n      } catch (error) {\n        console.log(\"检查用户登录状态失败:\", error)\n        return false\n      }\n    },\n    // 加载轮播配置\n    loadMarqueeConfig() {\n      // 从系统配置中获取公告轮播配置\n      const sysConfig = this.$store.getters.sysConfig\n      \n      // 检查公告轮播是否启用\n      // 如果notice_marquee_enabled不为1，则不显示公告轮播\n      if (!sysConfig || sysConfig.notice_marquee_enabled !== '1') {\n        // 公告轮播未启用，不显示\n        console.log('公告轮播未启用，状态值:', sysConfig ? sysConfig.notice_marquee_enabled : '未配置');\n        this.showMarquee = false;\n        return;\n      }\n      \n      console.log('公告轮播已启用，开始加载配置');\n      \n      if (sysConfig && sysConfig[this.marqueeConfigKey]) {\n        try {\n          const config = JSON.parse(sysConfig[this.marqueeConfigKey])\n          if (config) {\n            // 存储公告配置，供后续使用\n            this.noticeConfig = config;\n            // 检查是否在有效期内\n            if (this.checkMarqueeTimeValid(config)) {\n              this.rotateSpeed = (config.rotateSpeed || 3) * 1000\n              // 获取指定的公告列表\n              this.fetchNotices(config)\n            } else {\n              // 全局轮播配置已过期，不显示轮播\n              this.showMarquee = false;\n              this.notices = [];\n            }\n          }\n        } catch (e) {\n          console.error('解析公告轮播配置失败:', e)\n        }\n      }\n    },\n    \n    // 检查当前时间是否在轮播有效期内\n    checkMarqueeTimeValid(config) {\n      if (!config.startTime && !config.endTime) {\n        return true // 未设置时间则默认有效\n      }\n      \n      const now = moment()\n      if (config.startTime && moment(config.startTime).isAfter(now)) {\n        return false // 未到开始时间\n      }\n      \n      if (config.endTime && moment(config.endTime).isBefore(now)) {\n        return false // 已过结束时间\n      }\n      \n      return true\n    },\n    \n    // 新增方法：检查单个公告是否在有效期内\n    isNoticeValid(notice) {\n      // 如果没有配置信息，无法判断，默认有效\n      if (!this.noticeConfig) {\n        return true;\n      }\n      \n      const now = moment();\n      const noticeId = notice.id;\n      \n      // 检查该公告是否有特定日期设置\n      if (this.noticeConfig.noticeDetails && \n          this.noticeConfig.noticeDetails[noticeId]) {\n        const specificConfig = this.noticeConfig.noticeDetails[noticeId];\n        \n        // 检查特定开始时间\n        if (specificConfig.startTime && moment(specificConfig.startTime).isAfter(now)) {\n          console.log(`公告 ${noticeId} 未到特定开始时间`);\n          return false; // 未到特定开始时间\n        }\n        \n        // 检查特定结束时间\n        if (specificConfig.endTime && moment(specificConfig.endTime).isBefore(now)) {\n          console.log(`公告 ${noticeId} 已过特定结束时间`);\n          return false; // 已过特定结束时间\n        }\n      }\n      \n      // 没有特定设置或者特定设置在有效期内，使用全局设置判断\n      return true;\n    },\n    \n    // 新增方法：检查所有公告的有效性\n    checkNoticesValidity() {\n      // 检查全局配置是否有效\n      if (this.noticeConfig && !this.checkMarqueeTimeValid(this.noticeConfig)) {\n        // 全局配置已过期，不显示轮播\n        console.log('全局轮播配置已过期，隐藏轮播');\n        this.showMarquee = false;\n        return;\n      }\n      \n      // 使用计算属性自动过滤有效公告\n      // 如果计算出的有效公告为空，validNotices的watcher会自动将showMarquee设为false\n      if (this.validNotices.length === 0 && this.notices.length > 0) {\n        console.log('所有公告均已过期，隐藏轮播');\n        this.showMarquee = false;\n      }\n    },\n    \n    // 获取需要轮播的公告\n    fetchNotices(config) {\n      if (!config.noticeIds || config.noticeIds.length === 0) {\n        return\n      }\n\n      // 检查用户登录状态\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，跳过公告数据加载\")\n        this.showMarquee = false;\n        this.notices = [];\n        return\n      }\n\n      // 调用接口获取指定ID的公告详情，使用listByIdsForUser接口过滤当前用户有权限看到的公告\n      // 注意：使用新的接口来根据当前用户过滤公告\n      getAction('/sys/annountCement/listByIdsForUser', {\n        ids: config.noticeIds.join(',')\n      }).then(res => {\n        if (res.success && res.result && res.result.length > 0) {\n          // 存储获取到的公告\n          this.notices = res.result;\n          // 根据有效公告显示轮播\n          this.showMarquee = this.validNotices.length > 0;\n          // 立即检查公告有效性\n          this.checkNoticesValidity();\n        } else {\n          // 如果没有有效公告，不显示轮播\n          this.showMarquee = false;\n          this.notices = [];\n        }\n      }).catch(() => {\n        this.showMarquee = false;\n        this.notices = [];\n      })\n    },\n    \n    // 显示公告详情\n    showNoticeDetail(notice) {\n      this.$emit('showNotice', notice)\n    },\n    \n    // 显示更多公告\n    showMoreNotices() {\n      this.$router.push('/isps/userAnnouncement')\n    }\n  }\n}\n", {"version": 3, "sources": ["NoticeMarquee.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "NoticeMarquee.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<template>\n  <div class=\"notice-marquee-wrapper\" v-if=\"showMarquee\">\n    <div class=\"notice-icon\">\n      <a-icon type=\"notification\" theme=\"filled\" />\n    </div>\n    <div class=\"notice-content\">\n      <a-carousel\n        :autoplay=\"true\"\n        vertical\n        :dots=\"false\"\n        :autoplaySpeed=\"rotateSpeed\"\n      >\n        <div v-for=\"(notice, index) in validNotices\" :key=\"index\" class=\"notice-item\">\n          <div class=\"notice-main-content\">\n            <div class=\"notice-title-line\">\n              <a @click=\"showNoticeDetail(notice)\" :title=\"notice.title\">\n                {{ notice.title }}\n              </a>\n              <span v-if=\"notice.priority === 'L'\" class=\"notice-tag normal\">一般</span>\n              <span v-if=\"notice.priority === 'M'\" class=\"notice-tag important\">重要</span>\n              <span v-if=\"notice.priority === 'H'\" class=\"notice-tag urgent\">紧急</span>\n            </div>\n            \n            <!-- 添加公告摘要显示 -->\n            <div v-if=\"notice.msgAbstract\" class=\"notice-abstract\">{{ notice.msgAbstract }}</div>\n          </div>\n        </div>\n      </a-carousel>\n    </div>\n    <div class=\"more-link\">\n      <a @click=\"showMoreNotices\">更多</a>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getAction } from '@/api/manage'\nimport moment from 'moment'\nimport ShowAnnouncement from '@/components/tools/ShowAnnouncement'\n\nexport default {\n  name: 'NoticeMarquee',\n  components: {\n    ShowAnnouncement\n  },\n  data() {\n    return {\n      notices: [],\n      rotateSpeed: 3000, // 默认轮播速度\n      showMarquee: false, // 默认不显示公告轮播\n      marqueeConfigKey: 'notice_marquee_config', // 配置在系统配置表中的键名\n      noticeConfig: null, // 存储公告轮播配置\n      // 新增定时器ID，用于清除定时器\n      checkTimer: null\n    }\n  },\n  computed: {\n    // 新增计算属性，过滤有效公告\n    validNotices() {\n      return this.notices.filter(notice => this.isNoticeValid(notice));\n    }\n  },\n  mounted() {\n    // 只有在用户已登录时才加载公告轮播配置\n    if (this.isUserLoggedIn()) {\n      this.loadMarqueeConfig()\n      // 新增定时器，每分钟检查一次公告有效性\n      this.checkTimer = setInterval(() => {\n        this.checkNoticesValidity();\n      }, 60000); // 每分钟检查一次\n    }\n  },\n  beforeDestroy() {\n    // 新增组件销毁前清除定时器\n    if (this.checkTimer) {\n      clearInterval(this.checkTimer);\n      this.checkTimer = null;\n    }\n  },\n  watch: {\n    // 监听系统配置变化\n    '$store.getters.sysConfig': {\n      handler(newVal) {\n        // 当系统配置变化时，重新加载公告轮播配置\n        if (newVal) {\n          this.loadMarqueeConfig()\n        }\n      },\n      deep: true\n    },\n    // 监听有效公告数量变化\n    validNotices: {\n      handler(newVal) {\n        // 如果有效公告为空，则隐藏轮播\n        this.showMarquee = newVal && newVal.length > 0;\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 检查用户是否已登录\n    isUserLoggedIn() {\n      try {\n        const token = this.$ls.get('Access-Token')\n        return !!token\n      } catch (error) {\n        console.log(\"检查用户登录状态失败:\", error)\n        return false\n      }\n    },\n    // 加载轮播配置\n    loadMarqueeConfig() {\n      // 从系统配置中获取公告轮播配置\n      const sysConfig = this.$store.getters.sysConfig\n      \n      // 检查公告轮播是否启用\n      // 如果notice_marquee_enabled不为1，则不显示公告轮播\n      if (!sysConfig || sysConfig.notice_marquee_enabled !== '1') {\n        // 公告轮播未启用，不显示\n        console.log('公告轮播未启用，状态值:', sysConfig ? sysConfig.notice_marquee_enabled : '未配置');\n        this.showMarquee = false;\n        return;\n      }\n      \n      console.log('公告轮播已启用，开始加载配置');\n      \n      if (sysConfig && sysConfig[this.marqueeConfigKey]) {\n        try {\n          const config = JSON.parse(sysConfig[this.marqueeConfigKey])\n          if (config) {\n            // 存储公告配置，供后续使用\n            this.noticeConfig = config;\n            // 检查是否在有效期内\n            if (this.checkMarqueeTimeValid(config)) {\n              this.rotateSpeed = (config.rotateSpeed || 3) * 1000\n              // 获取指定的公告列表\n              this.fetchNotices(config)\n            } else {\n              // 全局轮播配置已过期，不显示轮播\n              this.showMarquee = false;\n              this.notices = [];\n            }\n          }\n        } catch (e) {\n          console.error('解析公告轮播配置失败:', e)\n        }\n      }\n    },\n    \n    // 检查当前时间是否在轮播有效期内\n    checkMarqueeTimeValid(config) {\n      if (!config.startTime && !config.endTime) {\n        return true // 未设置时间则默认有效\n      }\n      \n      const now = moment()\n      if (config.startTime && moment(config.startTime).isAfter(now)) {\n        return false // 未到开始时间\n      }\n      \n      if (config.endTime && moment(config.endTime).isBefore(now)) {\n        return false // 已过结束时间\n      }\n      \n      return true\n    },\n    \n    // 新增方法：检查单个公告是否在有效期内\n    isNoticeValid(notice) {\n      // 如果没有配置信息，无法判断，默认有效\n      if (!this.noticeConfig) {\n        return true;\n      }\n      \n      const now = moment();\n      const noticeId = notice.id;\n      \n      // 检查该公告是否有特定日期设置\n      if (this.noticeConfig.noticeDetails && \n          this.noticeConfig.noticeDetails[noticeId]) {\n        const specificConfig = this.noticeConfig.noticeDetails[noticeId];\n        \n        // 检查特定开始时间\n        if (specificConfig.startTime && moment(specificConfig.startTime).isAfter(now)) {\n          console.log(`公告 ${noticeId} 未到特定开始时间`);\n          return false; // 未到特定开始时间\n        }\n        \n        // 检查特定结束时间\n        if (specificConfig.endTime && moment(specificConfig.endTime).isBefore(now)) {\n          console.log(`公告 ${noticeId} 已过特定结束时间`);\n          return false; // 已过特定结束时间\n        }\n      }\n      \n      // 没有特定设置或者特定设置在有效期内，使用全局设置判断\n      return true;\n    },\n    \n    // 新增方法：检查所有公告的有效性\n    checkNoticesValidity() {\n      // 检查全局配置是否有效\n      if (this.noticeConfig && !this.checkMarqueeTimeValid(this.noticeConfig)) {\n        // 全局配置已过期，不显示轮播\n        console.log('全局轮播配置已过期，隐藏轮播');\n        this.showMarquee = false;\n        return;\n      }\n      \n      // 使用计算属性自动过滤有效公告\n      // 如果计算出的有效公告为空，validNotices的watcher会自动将showMarquee设为false\n      if (this.validNotices.length === 0 && this.notices.length > 0) {\n        console.log('所有公告均已过期，隐藏轮播');\n        this.showMarquee = false;\n      }\n    },\n    \n    // 获取需要轮播的公告\n    fetchNotices(config) {\n      if (!config.noticeIds || config.noticeIds.length === 0) {\n        return\n      }\n\n      // 检查用户登录状态\n      if (!this.isUserLoggedIn()) {\n        console.log(\"用户未登录，跳过公告数据加载\")\n        this.showMarquee = false;\n        this.notices = [];\n        return\n      }\n\n      // 调用接口获取指定ID的公告详情，使用listByIdsForUser接口过滤当前用户有权限看到的公告\n      // 注意：使用新的接口来根据当前用户过滤公告\n      getAction('/sys/annountCement/listByIdsForUser', {\n        ids: config.noticeIds.join(',')\n      }).then(res => {\n        if (res.success && res.result && res.result.length > 0) {\n          // 存储获取到的公告\n          this.notices = res.result;\n          // 根据有效公告显示轮播\n          this.showMarquee = this.validNotices.length > 0;\n          // 立即检查公告有效性\n          this.checkNoticesValidity();\n        } else {\n          // 如果没有有效公告，不显示轮播\n          this.showMarquee = false;\n          this.notices = [];\n        }\n      }).catch(() => {\n        this.showMarquee = false;\n        this.notices = [];\n      })\n    },\n    \n    // 显示公告详情\n    showNoticeDetail(notice) {\n      this.$emit('showNotice', notice)\n    },\n    \n    // 显示更多公告\n    showMoreNotices() {\n      this.$router.push('/isps/userAnnouncement')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.notice-marquee-wrapper {\n  display: flex;\n  align-items: center;\n  background-color: #f9f9f9;\n  height: 60px;\n  padding: 0 16px;\n  border-radius: 15px;\n  margin-bottom: 12px;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 30px;\n    height: 30px;\n    background: linear-gradient(135deg, #1890ff 0%, transparent 70%);\n    border-top-left-radius: 15px;\n    opacity: 0.6;\n  }\n  \n  &::after {\n    content: \"\";\n    position: absolute;\n    bottom: 0;\n    right: 0;\n    width: 30px;\n    height: 30px;\n    background: linear-gradient(315deg, #1890ff 0%, transparent 70%);\n    border-bottom-right-radius: 15px;\n    opacity: 0.6;\n  }\n  \n  .notice-icon {\n    display: flex;\n    align-items: center;\n    font-size: 18px;\n    color: #1890ff;\n    margin-right: 10px;\n    position: relative;\n    z-index: 2;\n    \n    &::after {\n      content: \"\";\n      position: absolute;\n      width: 32px;\n      height: 32px;\n      background: rgba(24, 144, 255, 0.1);\n      border-radius: 50%;\n      z-index: -1;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, -50%);\n    }\n  }\n  \n  .notice-content {\n    flex: 1;\n    overflow: hidden;\n    height: 44px;\n    position: relative;\n    z-index: 2;\n    \n    .notice-item {\n      display: flex;\n      align-items: flex-start;\n      padding: 3px 0;\n      overflow: hidden;\n      \n      .notice-main-content {\n        width: 100%;\n        display: flex;\n        flex-direction: column;\n        \n        .notice-title-line {\n          width: 100%;\n          display: flex;\n          align-items: center;\n          overflow: hidden;\n          white-space: nowrap;\n          \n          a {\n            color: #333;\n            margin-right: 8px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            max-width: calc(100% - 50px);\n            display: inline-block;\n            \n            &:hover {\n              color: #1890ff;\n              text-decoration: underline;\n            }\n          }\n          \n          .notice-tag {\n            display: inline-block;\n            padding: 0 6px;\n            font-size: 12px;\n            line-height: 18px;\n            border-radius: 10px;\n            color: white;\n            flex-shrink: 0;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n            \n            &.normal {\n              background-color: #1890ff;\n            }\n            \n            &.important {\n              background-color: #fa8c16;\n            }\n            \n            &.urgent {\n              background-color: #f5222d;\n            }\n          }\n        }\n        \n        .notice-abstract {\n          font-size: 12px;\n          color: #666;\n          margin-top: 2px;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          max-width: 100%;\n        }\n      }\n    }\n  }\n  \n  .more-link {\n    margin-left: 16px;\n    white-space: nowrap;\n    position: relative;\n    z-index: 2;\n    \n    a {\n      color: #1890ff;\n      font-weight: 500;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s;\n      \n      &:hover {\n        background-color: rgba(24, 144, 255, 0.1);\n        color: #1890ff;\n      }\n    }\n  }\n}\n</style> "]}]}