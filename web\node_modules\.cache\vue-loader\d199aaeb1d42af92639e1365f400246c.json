{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue?vue&type=style&index=0&id=f3631116&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\WorkList.vue", "mtime": 1750830741902}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n.panel-works {\n  margin: 30px 0;\n  padding: 20px;\n  background-color: #f9f9f9;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n}\n.panel-title {\n  font-size: 28px;\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.title-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 30px 0 15px;\n  position: relative;\n}\n\n.title-text {\n  display: inline-block;\n  padding: 10px 30px;\n  background: linear-gradient(90deg, #FF9900, #FFCC00);\n  color: white;\n  border-radius: 30px;\n  font-weight: bold;\n  box-shadow: 0 4px 10px rgba(255, 153, 0, 0.4);\n  position: relative;\n  z-index: 2;\n  font-size: 22px;\n}\n\n.title-decoration {\n  display: flex;\n  align-items: center;\n  margin: 0 15px;\n  position: relative;\n  \n  &.left {\n    flex-direction: row;\n  }\n  \n  &.right {\n    flex-direction: row-reverse;\n  }\n  \n  .decoration-circle {\n    width: 10px;\n    height: 10px;\n    border-radius: 50%;\n    background: #FFD700;\n    margin: 0 5px;\n    animation: pulse 2s infinite;\n  }\n  \n  .decoration-line {\n    height: 2px;\n    width: 30px;\n    background: linear-gradient(90deg, transparent, #FFD700, transparent);\n  }\n  \n  .decoration-star {\n    width: 15px;\n    height: 15px;\n    background: #FFCC00;\n    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n    margin: 0 5px;\n    animation: spin 5s linear infinite;\n  }\n}\n\n.title-separator {\n  height: 2px;\n  width: 100%;\n  background: linear-gradient(90deg, transparent, #FFD700, transparent);\n  margin-bottom: 20px;\n  border-radius: 3px;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.2); }\n  100% { transform: scale(1); }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.work-card {\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n  min-height: 300px;\n  height: 100%;\n  min-width: 200px;\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n    border: 1px solid rgba(79, 181, 255, 0.3);\n  }\n  \n  /deep/.ant-card-body {\n    padding: 0px;\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n  }\n  \n  a {\n    display: block;\n    height: 200px;\n    overflow: hidden;\n    position: relative;\n    text-align: center;\n  }\n  \n  .work-cover {\n    width: 100%;\n    height: 200px;\n    object-fit: cover;\n    transition: all 0.3s ease;\n  }\n  \n  img[src*=\"code.png\"] {\n    object-fit: contain;\n    padding: 20px;\n    max-width: 100%;\n    max-height: 100%;\n    width: auto;\n    height: auto;\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n  \n  &:hover .work-cover {\n    transform: scale(1.05);\n  }\n  \n  .work-info{\n    padding: 15px;\n    padding-top: 35px;\n    transition: all 0.3s ease;\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n  }\n  \n  .work-info p {\n    margin-top: 5px;\n    margin-bottom: 10px;\n    word-break: break-all;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n  }\n  \n  .work-stats-row {\n    position: absolute;\n    top: -15px;\n    left: 0;\n    right: 0;\n    background: rgba(255, 255, 255, 0.9);\n    padding: 8px 10px;\n    border-radius: 0 0 10px 10px;\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n    z-index: 2;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .stats-left {\n    display: flex;\n    align-items: center;\n  }\n  \n  .stats-right {\n    display: flex;\n    align-items: center;\n    justify-content: flex-end;\n  }\n  \n  .work-author {\n    margin-top: auto;\n    span {\n      line-height: 40px;\n      display: block;\n    }\n\n  }\n  .ant-tag {\n    float: right;\n  }\n  > div {\n    padding: 10px;\n    margin: 10px;\n  }\n  \n  &:hover .work-info {\n    background-color: #f0f8ff;\n  }\n}\n.load-more {\n  display: block;\n  margin: 20px auto;\n  text-align: center;\n  font-weight: bold;\n  background: linear-gradient(90deg, #5cdeff, #4fb5ff);\n  color: white;\n  border: none;\n  border-radius: 50px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n    box-shadow: 0 4px 10px rgba(79, 181, 255, 0.5);\n  }\n}\n.language-tag {\n  font-size: 16px;\n  font-weight: bold;\n  padding: 5px 12px;\n  border-radius: 6px;\n  background: linear-gradient(45deg, #4fb5ff, #6bc3ff);\n  color: white;\n  border: none;\n  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);\n  display: inline-block;\n  margin: 0;\n}\n.filter-container {\n  position: absolute;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.filter-select {\n  width: 120px;\n  border-radius: 20px;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n}\n\n/deep/ .ant-select-selection {\n  border-radius: 20px;\n  border: 1px solid #FFD700;\n}\n\n/deep/ .ant-select-dropdown-menu-item {\n  transition: all 0.3s;\n}\n\n/deep/ .ant-select-dropdown-menu-item:hover {\n  background: rgba(255, 215, 0, 0.1);\n}\n\n/deep/ .ant-select-dropdown-menu-item-selected {\n  background: rgba(255, 215, 0, 0.2);\n  font-weight: bold;\n}\n\n.filter-wrapper {\n  position: relative;\n  margin-bottom: 20px;\n  text-align: right;\n  padding-right: 20px;\n}\n\n.works-container {\n  transition: min-height 0.3s ease;\n}\n\n/* 添加淡入效果 */\n.fade-enter-active, .fade-appear-active {\n  transition: opacity 0.5s ease, transform 0.5s ease;\n}\n.fade-enter, .fade-appear {\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n/* 骨架屏样式 */\n.skeleton-container {\n  padding: 20px 0;\n}\n\n.skeleton-card {\n  background: #fff;\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n  height: 320px;\n  margin-bottom: 20px;\n  animation: skeleton-pulse 1.5s infinite ease-in-out;\n}\n\n.skeleton-image {\n  height: 200px;\n  background: #f0f0f0;\n}\n\n.skeleton-content {\n  padding: 15px;\n}\n\n.skeleton-stats {\n  height: 20px;\n  background: #f0f0f0;\n  margin-bottom: 15px;\n  border-radius: 4px;\n  width: 100%;\n}\n\n.skeleton-title {\n  height: 16px;\n  background: #f0f0f0;\n  margin-bottom: 15px;\n  border-radius: 4px;\n  width: 70%;\n}\n\n.skeleton-author {\n  height: 40px;\n  background: #f0f0f0;\n  border-radius: 4px;\n  display: flex;\n}\n\n@keyframes skeleton-pulse {\n  0% { opacity: 0.6; }\n  50% { opacity: 0.8; }\n  100% { opacity: 0.6; }\n}\n\n/* 为卡片添加动画效果 */\n.work-card {\n  animation: card-pop 0.5s forwards;\n  animation-play-state: paused;\n  opacity: 0;\n}\n\n.work-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(79, 181, 255, 0.3);\n}\n\n@keyframes card-pop {\n  0% { \n    opacity: 0;\n    transform: scale(0.95) translateY(10px); \n  }\n  100% { \n    opacity: 1;\n    transform: scale(1) translateY(0); \n  }\n}\n\n.work-card {\n  animation-play-state: running;\n}\n\n/* 添加提交时间样式 */\n.work-submit-time {\n  font-size: 12px;\n  color: #999;\n  margin-top: -5px;\n}\n", {"version": 3, "sources": ["WorkList.vue"], "names": [], "mappings": ";;AA+sBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "WorkList.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div>\n      <div class=\"panel-works\">\n        <h1 v-if=\"type==0\" class=\"panel-title title-container\">\n          <div class=\"title-decoration left\">\n            <div class=\"decoration-circle\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-star\"></div>\n          </div>\n          <span class=\"title-text\">\n            <a-icon type=\"fire\" theme=\"twoTone\" two-tone-color=\"#eb2f96\" />\n            最火作品\n          </span>\n          <div class=\"title-decoration right\">\n            <div class=\"decoration-star\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-circle\"></div>\n          </div>\n        </h1>\n        <div v-if=\"type==0\" class=\"title-separator\"></div>\n        \n        <h1 v-if=\"type==1\" class=\"panel-title title-container\">\n          <div class=\"title-decoration left\">\n            <div class=\"decoration-circle\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-star\"></div>\n          </div>\n          <span class=\"title-text\">\n            <a-icon type=\"clock-circle\" theme=\"twoTone\" />\n            最新作品\n          </span>\n          <div class=\"title-decoration right\">\n            <div class=\"decoration-star\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-circle\"></div>\n          </div>\n        </h1>\n        <div v-if=\"type==1\" class=\"title-separator\"></div>\n        \n        <!-- 筛选下拉框，移到标题和作品之间 -->\n        <div v-if=\"type==1\" class=\"filter-wrapper\">\n          <a-select \n            :value=\"sortOption\" \n            @change=\"handleSortChange\" \n            size=\"default\"\n            class=\"filter-select\"\n          >\n            <a-select-option value=\"time\">按最新时间</a-select-option>\n            <a-select-option value=\"view\">按观看数</a-select-option>\n            <a-select-option value=\"star\">按点赞数</a-select-option>\n          </a-select>\n        </div>\n        \n        <h1 v-if=\"type==2\" class=\"panel-title title-container\">\n          <div class=\"title-decoration left\">\n            <div class=\"decoration-circle\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-star\"></div>\n          </div>\n          <span class=\"title-text\">\n            <a-icon type=\"like\" theme=\"twoTone\" two-tone-color=\"#52c41a\" />\n            最赞作品\n          </span>\n          <div class=\"title-decoration right\">\n            <div class=\"decoration-star\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-circle\"></div>\n          </div>\n        </h1>\n        <div v-if=\"type==2\" class=\"title-separator\"></div>\n        \n        <h1 v-if=\"type==3\" class=\"panel-title title-container\">\n          <div class=\"title-decoration left\">\n            <div class=\"decoration-circle\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-star\"></div>\n          </div>\n          <span class=\"title-text\">\n            <a-icon type=\"star\" theme=\"twoTone\" two-tone-color=\"#ffd81b\" />\n            精选作品\n          </span>\n          <div class=\"title-decoration right\">\n            <div class=\"decoration-star\"></div>\n            <div class=\"decoration-line\"></div>\n            <div class=\"decoration-circle\"></div>\n          </div>\n        </h1>\n        <div v-if=\"type==3\" class=\"title-separator\"></div>\n        \n        <div class=\"works-container\" :style=\"{ minHeight: containerMinHeight + 'px' }\">\n          <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n            <a-col v-for=\"(item, index) in datasource\" :key=\"item.id || index\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n              <transition name=\"fade\" appear>\n                <a-card class=\"work-card\" :style=\"{ animationDelay: index * 50 + 'ms' }\">\n                  <a @click=\"toDetail(item.id)\" target=\"_blank\">\n                    <img class=\"work-cover\" v-if=\"item.coverFileKey_url\" :src=\"item.coverFileKey_url\" />\n                    <img v-if=\"item.workType == 4 || item.workType == 5 || item.workType == 10\" src=\"@/assets/code.png\" alt=\"\" />\n                  </a>\n                  <div class=\"work-info\">\n                    <div class=\"work-stats-row\">\n                      <div class=\"stats-left\">\n                        <a-icon type=\"eye\" /> {{ item.viewNum }} \n                        <a-divider type=\"vertical\"></a-divider>\n                        <a-icon type=\"like\" /> {{ item.starNum }}\n                      </div>\n                      <div class=\"stats-right\">\n                        <a-tag class=\"language-tag\">{{ item.workType_dictText }}</a-tag>\n                      </div>\n                    </div>\n                    <p>{{ item.workName }}</p>\n                    <a-row class=\"work-author\">\n                      <a-col :span=\"6\">\n                        <a-avatar shape=\"square\" class=\"avatar\" :size=\"40\" :src=\"item.avatar_url\" />\n                      </a-col>\n                      <a-col :span=\"18\">\n                        <span>{{ item.realname || item.username }}</span>\n                        <div class=\"work-submit-time\">{{ formatDate(item.createTime) }}</div>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </a-card>\n              </transition>\n            </a-col>\n          </a-row>\n          \n          <!-- 骨架屏加载状态 -->\n          <div v-if=\"silentLoading && datasource.length === 0\" class=\"skeleton-container\">\n            <a-row type=\"flex\" justify=\"start\" :gutter=\"[24, 24]\">\n              <a-col v-for=\"i in 8\" :key=\"i\" :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\">\n                <div class=\"skeleton-card\">\n                  <div class=\"skeleton-image\"></div>\n                  <div class=\"skeleton-content\">\n                    <div class=\"skeleton-stats\"></div>\n                    <div class=\"skeleton-title\"></div>\n                    <div class=\"skeleton-author\"></div>\n                  </div>\n                </div>\n              </a-col>\n            </a-row>\n          </div>\n          \n          <a-spin style=\"margin:50px auto;\" v-if=\"loading && !silentLoading\"/>\n          <a-empty v-if=\"!loading && !silentLoading && datasource.length==0\"/>\n          <a-button v-if=\"!loading && datasource.length>0\" class=\"load-more\" type=\"dash\" @click=\"getData\">加载更多……</a-button>\n        </div>\n      </div>\n  </div>\n</template>\n\n<script>\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\nimport Header from './modules/Header'\nimport Banner from './modules/Banner'\nimport Footer from './modules/Footer'\nimport UserEnter from './modules/UserEnter'\nimport QrCode from '@/components/tools/QrCode'\nimport moment from 'moment' // 引入moment处理日期\nexport default {\n  name: 'NewWorkList',\n    components: {\n    qrcode: QrCode,\n    Header,\n    Footer,\n    UserEnter,\n    Banner\n  },\n  data() {\n    return {\n      loading: false,\n      silentLoading: false,\n      datasource: [],\n      page: 0,\n      type: 1,\n      sortOption: 'time',\n      containerMinHeight: 200\n    }\n  },\n  created() {\n    if(this.$route.query.type){\n      this.type = this.$route.query.type\n    }\n    this.getData()\n  },\n  mounted() {\n    // 计算初始容器最小高度，避免内容加载时的抖动\n    this.$nextTick(() => {\n      this.updateContainerHeight()\n      window.addEventListener('resize', this.updateContainerHeight)\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.updateContainerHeight)\n  },\n  watch: {\n    // 监听路由变化\n    '$route.query.type': {\n      handler(newType) {\n        if (newType) {\n          this.type = newType\n          this.datasource = [] // 清空当前数据\n          this.page = 0 // 重置页码\n          this.getData() // 重新获取数据\n        }\n      },\n      immediate: true // 组件创建时立即执行一次\n    }\n  },\n   methods: {\n    getFileAccessHttpUrl,\n    formatDate(dateString) {\n      if (!dateString) return ''\n      return moment(dateString).format('YYYY-MM-DD HH:mm')\n    },\n    getData() {\n      this.loading = true\n      this.page += 1\n      let queryParam = {\n        pageSize: this._isMobile() ? 12 : 24,\n        pageNo: this.page,\n        orderBy: 'time'\n      }\n      if(this.type == 0){ //我猜是最火作品（CFish）\n        queryParam.orderBy = 'view'\n      }else if(this.type == 1){\n        queryParam.orderBy = this.sortOption\n      }else if(this.type == 2){\n        queryParam.orderBy = 'star'\n      }else if(this.type == 3){\n        queryParam['workStatus'] = 4\n        queryParam.orderBy = 'time'\n      }\n\n      getAction('/teaching/teachingWork/leaderboard', queryParam).then((res) => {\n        this.loading = false\n        if (res.success) {\n          this.datasource = this.datasource.concat(res.result.records)\n          console.log(this.datasource)\n          if (res.result.records.length == 0 && this.page > 1) {\n            this.$message.info('已加载完啦！')\n          }\n        }\n      })\n    },\n    toDetail(id) {\n      let route = this.$router.resolve({\n        path: \"/work-detail\",\n        query: {\n          id: id,\n        },\n      });\n      window.open(route.href, '_blank');\n    },\n    _isMobile() {\n      return (\n        navigator.userAgent.match(\n          /(phone|pad|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i\n        ) != null\n      )\n    },\n    handleSortChange(value) {\n      if (this.sortOption === value) return // 避免重复触发\n      \n      // 记录当前滚动位置\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop\n      \n      // 计算并设置容器的最小高度，保持视觉稳定\n      this.updateContainerHeight()\n      \n      // 不设置loading状态，避免显示加载动画\n      // this.loading = true\n      this.sortOption = value\n      \n      // 使用防抖处理，降低频繁操作带来的抖动\n      if (this.sortChangeTimer) clearTimeout(this.sortChangeTimer)\n      \n      this.sortChangeTimer = setTimeout(() => {\n        // 静默刷新数据\n        this.datasource = [] // 清空当前数据\n        this.page = 0 // 重置页码\n        \n        // 获取新数据，但不显示loading状态\n        this.getDataSilently()\n        \n        // 维持当前滚动位置\n        this.$nextTick(() => {\n          window.scrollTo(0, scrollPosition)\n        })\n      }, 100)\n    },\n    updateContainerHeight() {\n      const container = document.querySelector('.works-container')\n      if (container && this.datasource.length > 0) {\n        this.containerMinHeight = container.offsetHeight || 400\n      } else {\n        // 设置一个默认的最小高度\n        this.containerMinHeight = 400\n      }\n    },\n    // 新增静默获取数据的方法\n    getDataSilently() {\n      this.silentLoading = true\n      this.page += 1\n      let queryParam = {\n        pageSize: this._isMobile() ? 12 : 24,\n        pageNo: this.page,\n        orderBy: 'time'\n      }\n      if(this.type == 0){\n        queryParam.orderBy = 'view'\n      }else if(this.type == 1){\n        queryParam.orderBy = this.sortOption\n      }else if(this.type == 2){\n        queryParam.orderBy = 'star'\n      }else if(this.type == 3){\n        queryParam['workStatus'] = 4\n        queryParam.orderBy = 'time'\n      }\n\n      getAction('/teaching/teachingWork/leaderboard', queryParam).then((res) => {\n        this.silentLoading = false\n        if (res.success) {\n          this.datasource = this.datasource.concat(res.result.records)\n          console.log(this.datasource)\n          if (res.result.records.length == 0 && this.page > 1) {\n            this.$message.info('已加载完啦！')\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n\n  .panel-works {\n    margin: 30px 0;\n    padding: 20px;\n    background-color: #f9f9f9;\n    border-radius: 15px;\n    box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n  }\n  .panel-title {\n    font-size: 28px;\n    color: #333;\n    margin-bottom: 20px;\n  }\n  \n  .title-container {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 30px 0 15px;\n    position: relative;\n  }\n  \n  .title-text {\n    display: inline-block;\n    padding: 10px 30px;\n    background: linear-gradient(90deg, #FF9900, #FFCC00);\n    color: white;\n    border-radius: 30px;\n    font-weight: bold;\n    box-shadow: 0 4px 10px rgba(255, 153, 0, 0.4);\n    position: relative;\n    z-index: 2;\n    font-size: 22px;\n  }\n  \n  .title-decoration {\n    display: flex;\n    align-items: center;\n    margin: 0 15px;\n    position: relative;\n    \n    &.left {\n      flex-direction: row;\n    }\n    \n    &.right {\n      flex-direction: row-reverse;\n    }\n    \n    .decoration-circle {\n      width: 10px;\n      height: 10px;\n      border-radius: 50%;\n      background: #FFD700;\n      margin: 0 5px;\n      animation: pulse 2s infinite;\n    }\n    \n    .decoration-line {\n      height: 2px;\n      width: 30px;\n      background: linear-gradient(90deg, transparent, #FFD700, transparent);\n    }\n    \n    .decoration-star {\n      width: 15px;\n      height: 15px;\n      background: #FFCC00;\n      clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n      margin: 0 5px;\n      animation: spin 5s linear infinite;\n    }\n  }\n  \n  .title-separator {\n    height: 2px;\n    width: 100%;\n    background: linear-gradient(90deg, transparent, #FFD700, transparent);\n    margin-bottom: 20px;\n    border-radius: 3px;\n  }\n  \n  @keyframes pulse {\n    0% { transform: scale(1); }\n    50% { transform: scale(1.2); }\n    100% { transform: scale(1); }\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n  \n  .work-card {\n    border-radius: 10px;\n    overflow: hidden;\n    box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n    min-height: 300px;\n    height: 100%;\n    min-width: 200px;\n    display: flex;\n    flex-direction: column;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n      border: 1px solid rgba(79, 181, 255, 0.3);\n    }\n    \n    /deep/.ant-card-body {\n      padding: 0px;\n      display: flex;\n      flex-direction: column;\n      flex: 1;\n    }\n    \n    a {\n      display: block;\n      height: 200px;\n      overflow: hidden;\n      position: relative;\n      text-align: center;\n    }\n    \n    .work-cover {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      transition: all 0.3s ease;\n    }\n    \n    img[src*=\"code.png\"] {\n      object-fit: contain;\n      padding: 20px;\n      max-width: 100%;\n      max-height: 100%;\n      width: auto;\n      height: auto;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n    }\n    \n    &:hover .work-cover {\n      transform: scale(1.05);\n    }\n    \n    .work-info{\n      padding: 15px;\n      padding-top: 35px;\n      transition: all 0.3s ease;\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      position: relative;\n    }\n    \n    .work-info p {\n      margin-top: 5px;\n      margin-bottom: 10px;\n      word-break: break-all;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n    \n    .work-stats-row {\n      position: absolute;\n      top: -15px;\n      left: 0;\n      right: 0;\n      background: rgba(255, 255, 255, 0.9);\n      padding: 8px 10px;\n      border-radius: 0 0 10px 10px;\n      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n      z-index: 2;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    }\n    \n    .stats-left {\n      display: flex;\n      align-items: center;\n    }\n    \n    .stats-right {\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n    }\n    \n    .work-author {\n      margin-top: auto;\n      span {\n        line-height: 40px;\n        display: block;\n      }\n\n    }\n    .ant-tag {\n      float: right;\n    }\n    > div {\n      padding: 10px;\n      margin: 10px;\n    }\n    \n    &:hover .work-info {\n      background-color: #f0f8ff;\n    }\n  }\n  .load-more {\n    display: block;\n    margin: 20px auto;\n    text-align: center;\n    font-weight: bold;\n    background: linear-gradient(90deg, #5cdeff, #4fb5ff);\n    color: white;\n    border: none;\n    border-radius: 50px;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: scale(1.05);\n      box-shadow: 0 4px 10px rgba(79, 181, 255, 0.5);\n    }\n  }\n  .language-tag {\n    font-size: 16px;\n    font-weight: bold;\n    padding: 5px 12px;\n    border-radius: 6px;\n    background: linear-gradient(45deg, #4fb5ff, #6bc3ff);\n    color: white;\n    border: none;\n    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);\n    display: inline-block;\n    margin: 0;\n  }\n  .filter-container {\n    position: absolute;\n    right: 20px;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n  .filter-select {\n    width: 120px;\n    border-radius: 20px;\n    background: rgba(255, 255, 255, 0.9);\n    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  }\n  \n  /deep/ .ant-select-selection {\n    border-radius: 20px;\n    border: 1px solid #FFD700;\n  }\n  \n  /deep/ .ant-select-dropdown-menu-item {\n    transition: all 0.3s;\n  }\n  \n  /deep/ .ant-select-dropdown-menu-item:hover {\n    background: rgba(255, 215, 0, 0.1);\n  }\n  \n  /deep/ .ant-select-dropdown-menu-item-selected {\n    background: rgba(255, 215, 0, 0.2);\n    font-weight: bold;\n  }\n  \n  .filter-wrapper {\n    position: relative;\n    margin-bottom: 20px;\n    text-align: right;\n    padding-right: 20px;\n  }\n  \n  .works-container {\n    transition: min-height 0.3s ease;\n  }\n  \n  /* 添加淡入效果 */\n  .fade-enter-active, .fade-appear-active {\n    transition: opacity 0.5s ease, transform 0.5s ease;\n  }\n  .fade-enter, .fade-appear {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  \n  /* 骨架屏样式 */\n  .skeleton-container {\n    padding: 20px 0;\n  }\n  \n  .skeleton-card {\n    background: #fff;\n    border-radius: 10px;\n    overflow: hidden;\n    box-shadow: rgb(218, 218, 218) 2px 2px 5px;\n    height: 320px;\n    margin-bottom: 20px;\n    animation: skeleton-pulse 1.5s infinite ease-in-out;\n  }\n  \n  .skeleton-image {\n    height: 200px;\n    background: #f0f0f0;\n  }\n  \n  .skeleton-content {\n    padding: 15px;\n  }\n  \n  .skeleton-stats {\n    height: 20px;\n    background: #f0f0f0;\n    margin-bottom: 15px;\n    border-radius: 4px;\n    width: 100%;\n  }\n  \n  .skeleton-title {\n    height: 16px;\n    background: #f0f0f0;\n    margin-bottom: 15px;\n    border-radius: 4px;\n    width: 70%;\n  }\n  \n  .skeleton-author {\n    height: 40px;\n    background: #f0f0f0;\n    border-radius: 4px;\n    display: flex;\n  }\n  \n  @keyframes skeleton-pulse {\n    0% { opacity: 0.6; }\n    50% { opacity: 0.8; }\n    100% { opacity: 0.6; }\n  }\n  \n  /* 为卡片添加动画效果 */\n  .work-card {\n    animation: card-pop 0.5s forwards;\n    animation-play-state: paused;\n    opacity: 0;\n  }\n  \n  .work-card:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n    border: 1px solid rgba(79, 181, 255, 0.3);\n  }\n  \n  @keyframes card-pop {\n    0% { \n      opacity: 0;\n      transform: scale(0.95) translateY(10px); \n    }\n    100% { \n      opacity: 1;\n      transform: scale(1) translateY(0); \n    }\n  }\n  \n  .work-card {\n    animation-play-state: running;\n  }\n  \n  /* 添加提交时间样式 */\n  .work-submit-time {\n    font-size: 12px;\n    color: #999;\n    margin-top: -5px;\n  }\n</style>\n"]}]}