{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderMainModal.vue?vue&type=template&id=3727f7e1&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\modules\\JeecgOrderMainModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"title\"\n  :width=\"1200\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\">\n\n  <a-spin :spinning=\"confirmLoading\">\n    <a-form :form=\"form\">\n      <!-- 主表单区域 -->\n      <a-row class=\"form-row\" :gutter=\"16\">\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单号\">\n            <a-input placeholder=\"请输入订单号\" v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\" />\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单类型\">\n            <a-select placeholder=\"请输入订单类型\" v-decorator=\"['ctype',{}]\">\n              <a-select-option value=\"1\">国内订单</a-select-option>\n              <a-select-option value=\"2\">国际订单</a-select-option>\n            </a-select>\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单日期\">\n            <a-date-picker showTime format=\"YYYY-MM-DD HH:mm:ss\" v-decorator=\"[ 'orderDate',{}]\"/>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      <a-row class=\"form-row\" :gutter=\"16\">\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单金额\">\n            <a-input-number style=\"width: 200px\" v-decorator=\"[ 'orderMoney', {}]\" />\n          </a-form-item>\n        </a-col>\n        <a-col :lg=\"8\">\n          <a-form-item\n            :labelCol=\"labelCol\"\n            :wrapperCol=\"wrapperCol\"\n            label=\"订单备注\">\n            <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\" />\n          </a-form-item>\n        </a-col>\n      </a-row>\n\n      <!-- 子表单区域 -->\n      <a-tabs defaultActiveKey=\"1\" >\n        <a-tab-pane tab=\"客户信息\" key=\"1\">\n          <div>\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\">\n              <a-col :span=\"5\">客户名</a-col>\n              <a-col :span=\"5\">性别</a-col>\n              <a-col :span=\"5\">身份证号码</a-col>\n              <a-col :span=\"5\">手机号</a-col>\n              <a-col :span=\"4\">操作</a-col>\n            </a-row>\n\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in orderMainModel.jeecgOrderCustomerList\" :key=\"index\">\n              <a-col :span=\"5\">\n                <a-form-item>\n                  <a-input placeholder=\"客户名\" v-decorator=\"['jeecgOrderCustomerList['+index+'].name', {'initialValue':item.name,rules: [{ required: true, message: '请输入用户名!' }]}]\" />\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"5\">\n                <a-form-item>\n                  <a-select placeholder=\"性别\" v-decorator=\"['jeecgOrderCustomerList['+index+'].sex', {'initialValue':item.sex}]\">\n                    <a-select-option value=\"1\">男</a-select-option>\n                    <a-select-option value=\"2\">女</a-select-option>\n                  </a-select>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"5\">\n                <a-form-item>\n                  <a-input placeholder=\"身份证号\" v-decorator=\"['jeecgOrderCustomerList['+index+'].idcard', {'initialValue':item.idcard,rules: [{ pattern: '^\\\\d{6}(18|19|20)?\\\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\\\d|3[01])\\\\d{3}(\\\\d|[xX])$', message: '身份证号格式不对!' }]}]\"/>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"5\">\n                <a-form-item>\n                  <a-input placeholder=\"手机号\" v-decorator=\"['jeecgOrderCustomerList['+index+'].telphone', {'initialValue':item.telphone,rules: [{ pattern: '^1(3|4|5|7|8)\\\\d{9}$', message: '手机号格式不对!' }]}]\"/>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"4\">\n                <a-form-item>\n                  <a-button @click=\"addRowCustom\" icon=\"plus\"></a-button>&nbsp;\n                  <a-button @click=\"delRowCustom(index)\" icon=\"minus\"></a-button>\n                </a-form-item>\n              </a-col>\n            </a-row>\n          </div>\n        </a-tab-pane>\n\n        <a-tab-pane tab=\"机票信息\" key=\"2\" forceRender>\n          <div>\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\">\n              <a-col :span=\"6\">航班号</a-col>\n              <a-col :span=\"6\">航班时间</a-col>\n              <a-col :span=\"6\">操作</a-col>\n            </a-row>\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in orderMainModel.jeecgOrderTicketList\" :key=\"index\">\n              <a-col :span=\"6\">\n                <a-form-item>\n                  <a-input placeholder=\"航班号\" v-decorator=\"['jeecgOrderTicketList['+index+'].ticketCode', {'initialValue':item.ticketCode,rules: [{ required: true, message: '请输入航班号!' }]}]\" />\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"6\">\n                <a-form-item>\n                  <j-date placeholder=\"航班时间\" :trigger-change=\"true\" v-decorator=\"['jeecgOrderTicketList['+index+'].tickectDate', {'initialValue':item.tickectDate}]\"></j-date>\n                </a-form-item>\n              </a-col>\n              <a-col :span=\"6\">\n                <a-form-item>\n                  <a-button @click=\"addRowTicket\" icon=\"plus\"></a-button>&nbsp;\n                  <a-button @click=\"delRowTicket(index)\" icon=\"minus\"></a-button>\n                </a-form-item>\n              </a-col>\n            </a-row>\n          </div>\n        </a-tab-pane>\n      </a-tabs>\n\n    </a-form>\n  </a-spin>\n</a-modal>\n", null]}