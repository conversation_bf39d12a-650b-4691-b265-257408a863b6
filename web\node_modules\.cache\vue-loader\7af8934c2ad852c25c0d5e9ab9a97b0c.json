{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Bar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { triggerWindowResizeEvent } from '@/utils/util'\n\n  export default {\n    name: 'Bar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true\n      },\n      yaxisText: {\n        type: String,\n        default: 'y'\n      },\n      title: {\n        type: String,\n        default: ''\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return { padding: ['auto', 'auto', '40', '50'] }\n    },\n    computed: {\n      scale() {\n        return [{\n          dataKey: 'y',\n          alias: this.yaxisText\n        }]\n      }\n    },\n    mounted() {\n      triggerWindowResizeEvent()\n    }\n  }\n", {"version": 3, "sources": ["Bar.vue"], "names": [], "mappings": ";AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Bar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :forceFit=\"true\" :height=\"height\" :data=\"dataSource\" :scale=\"scale\" :padding=\"padding\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { triggerWindowResizeEvent } from '@/utils/util'\n\n  export default {\n    name: 'Bar',\n    props: {\n      dataSource: {\n        type: Array,\n        required: true\n      },\n      yaxisText: {\n        type: String,\n        default: 'y'\n      },\n      title: {\n        type: String,\n        default: ''\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return { padding: ['auto', 'auto', '40', '50'] }\n    },\n    computed: {\n      scale() {\n        return [{\n          dataKey: 'y',\n          alias: this.yaxisText\n        }]\n      }\n    },\n    mounted() {\n      triggerWindowResizeEvent()\n    }\n  }\n</script>"]}]}