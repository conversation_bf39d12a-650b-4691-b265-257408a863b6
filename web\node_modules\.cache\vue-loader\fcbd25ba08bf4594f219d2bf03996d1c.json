{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\TransferBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\TransferBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n\n  export default {\n    name: '<PERSON>',\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      x: {\n        type: String,\n        default: 'x'\n      },\n      y: {\n        type: String,\n        default: 'y'\n      },\n      data: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {}\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      // this.getMonthBar()\n    },\n    methods: {\n      // getMonthBar() {\n      //   this.$http.get('/analysis/month-bar')\n      //     .then(res => {\n      //       this.data = res.result\n      //     })\n      // }\n    }\n  }\n", {"version": 3, "sources": ["TransferBar.vue"], "names": [], "mappings": ";;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TransferBar.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart\n      :height=\"height\"\n      :data=\"data\"\n      :scale=\"scale\"\n      :forceFit=\"true\"\n      :padding=\"['auto', 'auto', '40', '50']\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-bar position=\"x*y\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n\n  export default {\n    name: 'Bar',\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      x: {\n        type: String,\n        default: 'x'\n      },\n      y: {\n        type: String,\n        default: 'y'\n      },\n      data: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {}\n    },\n    computed: {\n      scale() {\n        return [\n          { dataKey: 'x', title: this.x, alias: this.x },\n          { dataKey: 'y', title: this.y, alias: this.y }\n        ]\n      }\n    },\n    created() {\n      // this.getMonthBar()\n    },\n    methods: {\n      // getMonthBar() {\n      //   this.$http.get('/analysis/month-bar')\n      //     .then(res => {\n      //       this.data = res.result\n      //     })\n      // }\n    }\n  }\n</script>"]}]}