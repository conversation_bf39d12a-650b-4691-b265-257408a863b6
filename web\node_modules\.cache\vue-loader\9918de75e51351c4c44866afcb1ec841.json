{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSlider.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSlider.vue", "mtime": 1753248758151}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  export default {\n    name:\"<PERSON><PERSON><PERSON><PERSON>\",\n    data(){\n      return {\n        beginClientX:0,           /*距离屏幕左端距离*/\n        mouseMoveStata:false,     /*触发拖动状态  判断*/\n        maxwidth:'',               /*拖动最大宽度，依据滑块宽度算出来的*/\n        confirmWords:'拖动滑块验证',   /*滑块文字*/\n        confirmSuccess:false           /*验证成功判断*/\n      }\n    },\n    methods: {\n      isSuccess(){\n        return this.confirmSuccess\n      },\n      mousedownFn:function (e) {\n        if(!this.confirmSuccess){\n          e.preventDefault && e.preventDefault();   //阻止文字选中等 浏览器默认事件\n          this.mouseMoveStata = true;\n          this.beginClientX = e.clientX;\n        }\n      },        //mousedoen 事件\n      successFunction(){\n        this.confirmSuccess = true\n        this.confirmWords = '验证通过';\n        if(window.addEventListener){\n          document.getElementsByTagName('html')[0].removeEventListener('mousemove',this.mouseMoveFn);\n          document.getElementsByTagName('html')[0].removeEventListener('mouseup',this.moseUpFn);\n        }else {\n          document.getElementsByTagName('html')[0].removeEventListener('mouseup',()=>{});\n        }\n        document.getElementsByClassName('drag_text')[0].style.color = '#fff'\n        document.getElementsByClassName('handler')[0].style.left = this.maxwidth + 'px';\n        document.getElementsByClassName('drag_bg')[0].style.width = this.maxwidth + 'px';\n\n        this.$emit(\"onSuccess\",true)\n      },                //验证成功函数\n      mouseMoveFn(e){\n        if(this.mouseMoveStata){\n          let width = e.clientX - this.beginClientX;\n          if(width>0 && width<=this.maxwidth){\n            document.getElementsByClassName('handler')[0].style.left = width + 'px';\n            document.getElementsByClassName('drag_bg')[0].style.width = width + 'px';\n          }else if(width>this.maxwidth){\n            this.successFunction();\n          }\n        }\n      },                   //mousemove事件\n      moseUpFn(e){\n        this.mouseMoveStata = false;\n        var width = e.clientX - this.beginClientX;\n        if(width<this.maxwidth){\n          // ---- update-begin- author:sunjianlei --- date:20191009 --- for: 修复获取不到 handler 的时候报错 ----\n          let handler = document.getElementsByClassName('handler')[0]\n          if (handler) {\n            handler.style.left = 0 + 'px'\n            document.getElementsByClassName('drag_bg')[0].style.width = 0 + 'px'\n          }\n          // ---- update-end- author:sunjianlei --- date:20191009 --- for: 修复获取不到 handler 的时候报错 ----\n        }\n      }                       //mouseup事件\n    },\n    mounted(){\n      this.$nextTick(() => {\n        if (this.$refs.dragDiv && this.$refs.moveDiv) {\n          this.maxwidth = this.$refs.dragDiv.clientWidth - this.$refs.moveDiv.clientWidth;\n        }\n        const htmlElement = document.getElementsByTagName('html')[0];\n        if (htmlElement) {\n          htmlElement.addEventListener('mousemove', this.mouseMoveFn);\n          htmlElement.addEventListener('mouseup', this.moseUpFn);\n        }\n      });\n    },\n    beforeDestroy() {\n      const htmlElement = document.getElementsByTagName('html')[0];\n      if (htmlElement) {\n        htmlElement.removeEventListener('mousemove', this.mouseMoveFn);\n        htmlElement.removeEventListener('mouseup', this.moseUpFn);\n      }\n    }\n  }\n", {"version": 3, "sources": ["JSlider.vue"], "names": [], "mappings": ";AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JSlider.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <div class=\"drag\" ref=\"dragDiv\">\n    <div class=\"drag_bg\"></div>\n    <div class=\"drag_text\">{{confirmWords}}</div>\n    <div ref=\"moveDiv\" @mousedown=\"mousedownFn($event)\" :class=\"{'handler_ok_bg':confirmSuccess}\" class=\"handler handler_bg\" style=\"border: 0.5px solid #fff;height: 34px;position: absolute;top: 0px;left: 0px;\"></div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name:\"JSlider\",\n    data(){\n      return {\n        beginClientX:0,           /*距离屏幕左端距离*/\n        mouseMoveStata:false,     /*触发拖动状态  判断*/\n        maxwidth:'',               /*拖动最大宽度，依据滑块宽度算出来的*/\n        confirmWords:'拖动滑块验证',   /*滑块文字*/\n        confirmSuccess:false           /*验证成功判断*/\n      }\n    },\n    methods: {\n      isSuccess(){\n        return this.confirmSuccess\n      },\n      mousedownFn:function (e) {\n        if(!this.confirmSuccess){\n          e.preventDefault && e.preventDefault();   //阻止文字选中等 浏览器默认事件\n          this.mouseMoveStata = true;\n          this.beginClientX = e.clientX;\n        }\n      },        //mousedoen 事件\n      successFunction(){\n        this.confirmSuccess = true\n        this.confirmWords = '验证通过';\n        if(window.addEventListener){\n          document.getElementsByTagName('html')[0].removeEventListener('mousemove',this.mouseMoveFn);\n          document.getElementsByTagName('html')[0].removeEventListener('mouseup',this.moseUpFn);\n        }else {\n          document.getElementsByTagName('html')[0].removeEventListener('mouseup',()=>{});\n        }\n        document.getElementsByClassName('drag_text')[0].style.color = '#fff'\n        document.getElementsByClassName('handler')[0].style.left = this.maxwidth + 'px';\n        document.getElementsByClassName('drag_bg')[0].style.width = this.maxwidth + 'px';\n\n        this.$emit(\"onSuccess\",true)\n      },                //验证成功函数\n      mouseMoveFn(e){\n        if(this.mouseMoveStata){\n          let width = e.clientX - this.beginClientX;\n          if(width>0 && width<=this.maxwidth){\n            document.getElementsByClassName('handler')[0].style.left = width + 'px';\n            document.getElementsByClassName('drag_bg')[0].style.width = width + 'px';\n          }else if(width>this.maxwidth){\n            this.successFunction();\n          }\n        }\n      },                   //mousemove事件\n      moseUpFn(e){\n        this.mouseMoveStata = false;\n        var width = e.clientX - this.beginClientX;\n        if(width<this.maxwidth){\n          // ---- update-begin- author:sunjianlei --- date:20191009 --- for: 修复获取不到 handler 的时候报错 ----\n          let handler = document.getElementsByClassName('handler')[0]\n          if (handler) {\n            handler.style.left = 0 + 'px'\n            document.getElementsByClassName('drag_bg')[0].style.width = 0 + 'px'\n          }\n          // ---- update-end- author:sunjianlei --- date:20191009 --- for: 修复获取不到 handler 的时候报错 ----\n        }\n      }                       //mouseup事件\n    },\n    mounted(){\n      this.$nextTick(() => {\n        if (this.$refs.dragDiv && this.$refs.moveDiv) {\n          this.maxwidth = this.$refs.dragDiv.clientWidth - this.$refs.moveDiv.clientWidth;\n        }\n        const htmlElement = document.getElementsByTagName('html')[0];\n        if (htmlElement) {\n          htmlElement.addEventListener('mousemove', this.mouseMoveFn);\n          htmlElement.addEventListener('mouseup', this.moseUpFn);\n        }\n      });\n    },\n    beforeDestroy() {\n      const htmlElement = document.getElementsByTagName('html')[0];\n      if (htmlElement) {\n        htmlElement.removeEventListener('mousemove', this.mouseMoveFn);\n        htmlElement.removeEventListener('mouseup', this.moseUpFn);\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .drag{\n    position: relative;\n    background-color: #e8e8e8;\n    width: 100%;\n    height: 34px;\n    line-height: 34px;\n    text-align: center;\n  }\n  .handler{\n    width: 40px;\n    height: 32px;\n    border: 1px solid #ccc;\n    cursor: move;\n  }\n  .handler_bg{\n    background: #fff url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEyNTVEMURGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEyNTVEMUNGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2MTc5NzNmZS02OTQxLTQyOTYtYTIwNi02NDI2YTNkOWU5YmUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+YiRG4AAAALFJREFUeNpi/P//PwMlgImBQkA9A+bOnfsIiBOxKcInh+yCaCDuByoswaIOpxwjciACFegBqZ1AvBSIS5OTk/8TkmNEjwWgQiUgtQuIjwAxUF3yX3xyGIEIFLwHpKyAWB+I1xGSwxULIGf9A7mQkBwTlhBXAFLHgPgqEAcTkmNCU6AL9d8WII4HOvk3ITkWJAXWUMlOoGQHmsE45ViQ2KuBuASoYC4Wf+OUYxz6mQkgwAAN9mIrUReCXgAAAABJRU5ErkJggg==\") no-repeat center;\n  }\n  .handler_ok_bg{\n    background: #fff url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDlBRDI3NjVGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDlBRDI3NjRGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDphNWEzMWNhMC1hYmViLTQxNWEtYTEwZS04Y2U5NzRlN2Q4YTEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+k+sHwwAAASZJREFUeNpi/P//PwMyKD8uZw+kUoDYEYgloMIvgHg/EM/ptHx0EFk9I8wAoEZ+IDUPiIMY8IN1QJwENOgj3ACo5gNAbMBAHLgAxA4gQ5igAnNJ0MwAVTsX7IKyY7L2UNuJAf+AmAmJ78AEDTBiwGYg5gbifCSxFCZoaBMCy4A4GOjnH0D6DpK4IxNSVIHAfSDOAeLraJrjgJp/AwPbHMhejiQnwYRmUzNQ4VQgDQqXK0ia/0I17wJiPmQNTNBEAgMlQIWiQA2vgWw7QppBekGxsAjIiEUSBNnsBDWEAY9mEFgMMgBk00E0iZtA7AHEctDQ58MRuA6wlLgGFMoMpIG1QFeGwAIxGZo8GUhIysmwQGSAZgwHaEZhICIzOaBkJkqyM0CAAQDGx279Jf50AAAAAABJRU5ErkJggg==\") no-repeat center;\n  }\n  .drag_bg{\n    background-color: #7ac23c;\n    height: 34px;\n    width: 0px;\n  }\n  .drag_text{\n    position: absolute;\n    top: 0px;\n    width: 100%;text-align: center;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    -o-user-select:none;\n    -ms-user-select:none;\n  }\n</style>"]}]}