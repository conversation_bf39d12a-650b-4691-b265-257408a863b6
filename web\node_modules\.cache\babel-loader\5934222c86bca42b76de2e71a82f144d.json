{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\modules\\message\\modules\\SysMessageModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { httpAction } from '@/api/manage';\nimport pick from 'lodash.pick';\nimport moment from \"moment\";\nexport default {\n  name: \"SysMessageModal\",\n  data: function data() {\n    return {\n      title: \"操作\",\n      visible: false,\n      model: {},\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      validatorRules: {},\n      disableSubmit: true,\n      url: {\n        add: \"/message/sysMessage/add\",\n        edit: \"/message/sysMessage/edit\"\n      }\n    };\n  },\n  created: function created() {},\n  methods: {\n    add: function add() {\n      this.edit({});\n    },\n    edit: function edit(record) {\n      var _this = this;\n      this.form.resetFields();\n      this.model = Object.assign({}, record);\n      this.visible = true;\n      this.$nextTick(function () {\n        _this.form.setFieldsValue(pick(_this.model, 'esContent', 'esParam', 'esReceiver', 'esResult', 'esSendNum', 'esSendStatus', 'esTitle', 'esType', 'remark'));\n        //时间格式化\n        _this.form.setFieldsValue({\n          esSendTime: _this.model.esSendTime ? moment(_this.model.esSendTime) : null\n        });\n      });\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      var _this2 = this;\n      var that = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n          var httpurl = '';\n          var method = '';\n          if (!_this2.model.id) {\n            httpurl += _this2.url.add;\n            method = 'post';\n          } else {\n            httpurl += _this2.url.edit;\n            method = 'put';\n          }\n          var formData = Object.assign(_this2.model, values);\n          //时间格式化\n          formData.esSendTime = formData.esSendTime ? formData.esSendTime.format('YYYY-MM-DD HH:mm:ss') : null;\n          console.log(formData);\n          httpAction(httpurl, formData, method).then(function (res) {\n            if (res.success) {\n              that.$message.success(res.message);\n              that.$emit('ok');\n            } else {\n              that.$message.warning(res.message);\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n            that.close();\n          });\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "pick", "moment", "name", "data", "title", "visible", "model", "labelCol", "xs", "span", "sm", "wrapperCol", "confirmLoading", "form", "$form", "createForm", "validatorRules", "disableSubmit", "url", "add", "edit", "created", "methods", "record", "_this", "resetFields", "Object", "assign", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "esSendTime", "close", "$emit", "handleOk", "_this2", "that", "validateFields", "err", "values", "httpurl", "method", "id", "formData", "format", "console", "log", "then", "res", "success", "$message", "message", "warning", "finally", "handleCancel"], "sources": ["src/views/modules/message/modules/SysMessageModal.vue"], "sourcesContent": ["<template>\n  <a-drawer\n    :title=\"title\"\n    :maskClosable=\"true\"\n    width=650\n    placement=\"right\"\n    :closable=\"true\"\n    @close=\"close\"\n    :visible=\"visible\"\n    style=\"height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"消息标题\">\n          <a-input placeholder=\"请输入消息标题\" v-decorator=\"['esTitle', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送内容\">\n          <a-input placeholder=\"请输入发送内容\" v-decorator=\"['esContent', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送所需参数\">\n          <a-input placeholder=\"请输入发送所需参数Json格式\" v-decorator=\"['esParam', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"接收人\">\n          <a-input placeholder=\"请输入接收人\" v-decorator=\"['esReceiver', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送方式\">\n          <j-dict-select-tag :triggerChange=\"true\" dictCode=\"msgType\" v-decorator=\"[ 'esType', {}]\" placeholder=\"请选择发送方式\">\n          </j-dict-select-tag>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送时间\">\n          <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator=\"[ 'esSendTime', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送状态\">\n          <j-dict-select-tag :triggerChange=\"true\" dictCode=\"msgSendStatus\" v-decorator=\"[ 'esSendStatus', {}]\" placeholder=\"请选择发送状态\">\n          </j-dict-select-tag>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送次数\">\n          <a-input-number v-decorator=\"[ 'esSendNum', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"发送失败原因\">\n          <a-input v-decorator=\"['esResult', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"备注\">\n          <a-input v-decorator=\"['remark', {}]\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n    <div v-show=\"!disableSubmit\">\n      <a-button style=\"margin-right: .8rem\" @confirm=\"handleCancel\">取消</a-button>\n      <a-button @click=\"handleOk\" type=\"primary\" :loading=\"confirmLoading\">提交</a-button>\n    </div>\n  </a-drawer>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"SysMessageModal\",\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        model: {},\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        disableSubmit: true,\n        url: {\n          add: \"/message/sysMessage/add\",\n          edit: \"/message/sysMessage/edit\",\n        },\n      }\n    },\n    created() {\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.model = Object.assign({}, record);\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.model, 'esContent', 'esParam', 'esReceiver', 'esResult', 'esSendNum', 'esSendStatus', 'esTitle', 'esType', 'remark'))\n          //时间格式化\n          this.form.setFieldsValue({esSendTime: this.model.esSendTime ? moment(this.model.esSendTime) : null})\n        });\n\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.model.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let formData = Object.assign(this.model, values);\n            //时间格式化\n            formData.esSendTime = formData.esSendTime ? formData.esSendTime.format('YYYY-MM-DD HH:mm:ss') : null;\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n\n\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      },\n\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAsFA,SAAAA,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MAEAG,cAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,GAAA;QACAC,GAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAH,GAAA,WAAAA,IAAA;MACA,KAAAC,IAAA;IACA;IACAA,IAAA,WAAAA,KAAAG,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAX,IAAA,CAAAY,WAAA;MACA,KAAAnB,KAAA,GAAAoB,MAAA,CAAAC,MAAA,KAAAJ,MAAA;MACA,KAAAlB,OAAA;MACA,KAAAuB,SAAA;QACAJ,KAAA,CAAAX,IAAA,CAAAgB,cAAA,CAAA7B,IAAA,CAAAwB,KAAA,CAAAlB,KAAA;QACA;QACAkB,KAAA,CAAAX,IAAA,CAAAgB,cAAA;UAAAC,UAAA,EAAAN,KAAA,CAAAlB,KAAA,CAAAwB,UAAA,GAAA7B,MAAA,CAAAuB,KAAA,CAAAlB,KAAA,CAAAwB,UAAA;QAAA;MACA;IAEA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAA3B,OAAA;IACA;IACA4B,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA;MACA;MACA,KAAAtB,IAAA,CAAAuB,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAF,IAAA,CAAAvB,cAAA;UACA,IAAA2B,OAAA;UACA,IAAAC,MAAA;UACA,KAAAN,MAAA,CAAA5B,KAAA,CAAAmC,EAAA;YACAF,OAAA,IAAAL,MAAA,CAAAhB,GAAA,CAAAC,GAAA;YACAqB,MAAA;UACA;YACAD,OAAA,IAAAL,MAAA,CAAAhB,GAAA,CAAAE,IAAA;YACAoB,MAAA;UACA;UACA,IAAAE,QAAA,GAAAhB,MAAA,CAAAC,MAAA,CAAAO,MAAA,CAAA5B,KAAA,EAAAgC,MAAA;UACA;UACAI,QAAA,CAAAZ,UAAA,GAAAY,QAAA,CAAAZ,UAAA,GAAAY,QAAA,CAAAZ,UAAA,CAAAa,MAAA;UAEAC,OAAA,CAAAC,GAAA,CAAAH,QAAA;UACA3C,UAAA,CAAAwC,OAAA,EAAAG,QAAA,EAAAF,MAAA,EAAAM,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAb,IAAA,CAAAc,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAG,OAAA;cACAf,IAAA,CAAAH,KAAA;YACA;cACAG,IAAA,CAAAc,QAAA,CAAAE,OAAA,CAAAJ,GAAA,CAAAG,OAAA;YACA;UACA,GAAAE,OAAA;YACAjB,IAAA,CAAAvB,cAAA;YACAuB,IAAA,CAAAJ,KAAA;UACA;QAGA;MACA;IACA;IACAsB,YAAA,WAAAA,aAAA;MACA,KAAAtB,KAAA;IACA;EAGA;AACA", "ignoreList": []}]}