{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue?vue&type=template&id=1c8278da&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeResultModal.vue", "mtime": 1753093743655}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<a-modal\n  v-model=\"visible\"\n  :footer=\"null\"\n  width=\"680px\"\n  :maskClosable=\"false\"\n  :keyboard=\"false\"\n  :closable=\"false\"\n  class=\"result-modal\"\n>\n  <!-- 自定义标题栏 -->\n  <template slot=\"title\">\n    <div style=\"display: flex; align-items: center;\">\n      <a-icon type=\"trophy\" theme=\"filled\" style=\"color: #52c41a; font-size: 20px; margin-right: 8px;\" />\n      <span style=\"font-size: 18px; font-weight: 500;\">练习完成</span>\n    </div>\n  </template>\n  \n  <!-- 现代化设计的结果页 -->\n  <div style=\"padding: 0;\">\n    <!-- 顶部祝贺信息 -->\n    <div style=\"background: linear-gradient(135deg, #f6ffed, #e6f7ff); padding: 24px 0; text-align: center; border-radius: 8px; margin-bottom: 20px;\">\n      <a-icon type=\"check-circle\" theme=\"filled\" style=\"font-size: 48px; color: #52c41a; margin-bottom: 16px;\" />\n      <h2 style=\"margin: 0; font-size: 24px; color: #262626; font-weight: 500;\">恭喜您完成本次刷题!</h2>\n    </div>\n    \n    <!-- 主要数据展示 -->\n    <div style=\"display: flex; margin-bottom: 24px;\">\n      <!-- 左侧正确率环形图 -->\n      <div style=\"width: 38%; display: flex; flex-direction: column; align-items: center; justify-content: center;\">\n        <a-progress \n          type=\"circle\" \n          :percent=\"accuracyPercent\" \n          :width=\"120\"\n          :format=\"percent => `${percent}%`\"\n          :strokeColor=\"accuracyStrokeColor\"\n          :status=\"accuracyPercent > 60 ? 'success' : accuracyPercent > 30 ? 'normal' : 'exception'\"\n        />\n        <div style=\"margin-top: 12px; font-size: 16px; color: #262626; font-weight: 500;\">\n          正确率\n        </div>\n      </div>\n      \n      <!-- 右侧数据卡片 -->\n      <div style=\"width: 62%; display: flex; flex-wrap: wrap; gap: 12px;\">\n        <div style=\"flex: 1; min-width: 45%; background: #f6ffed; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n          <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n            <span style=\"color: #52c41a; font-weight: 600; font-size: 28px;\">{{ correctCount }}</span>\n            <a-icon type=\"check-circle\" style=\"font-size: 24px; color: #52c41a;\" />\n          </div>\n          <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">正确题目</div>\n        </div>\n        \n        <div style=\"flex: 1; min-width: 45%; background: #fff2f0; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n          <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n            <span style=\"color: #ff4d4f; font-weight: 600; font-size: 28px;\">{{ incorrectCount }}</span>\n            <a-icon type=\"close-circle\" style=\"font-size: 24px; color: #ff4d4f;\" />\n          </div>\n          <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">错误题目</div>\n        </div>\n        \n        <div style=\"flex: 1; min-width: 45%; background: #f5f5f5; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n          <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n            <span style=\"color: #8c8c8c; font-weight: 600; font-size: 28px;\">{{ unfinishedCount }}</span>\n            <a-icon type=\"minus-circle\" style=\"font-size: 24px; color: #8c8c8c;\" />\n          </div>\n          <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">未完成题目</div>\n        </div>\n        \n        <div style=\"flex: 1; min-width: 45%; background: #e6f7ff; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06);\">\n          <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n            <span style=\"color: #1890ff; font-weight: 600; font-size: 28px;\">{{ totalCount }}</span>\n            <a-icon type=\"file-text\" style=\"font-size: 24px; color: #1890ff;\" />\n          </div>\n          <div style=\"margin-top: 8px; color: rgba(0,0,0,0.65); font-size: 14px;\">总题目数</div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 底部按钮区 -->\n    <div style=\"display: flex; justify-content: center; gap: 12px; flex-wrap: wrap;\">\n      <a-button type=\"primary\" @click=\"startNewPractise\" size=\"large\" style=\"min-width: 140px;\">\n        <a-icon type=\"redo\" /> 再来一组\n      </a-button>\n      \n      <a-button v-if=\"hasAnsweredQuestions\" type=\"primary\" @click=\"enterReviewMode\" size=\"large\" style=\"min-width: 140px; background-color: #722ed1; border-color: #722ed1;\">\n        <a-icon type=\"solution\" /> 查阅答题\n      </a-button>\n      \n      <a-button @click=\"handleExitConfirm\" size=\"large\" style=\"min-width: 140px;\">\n        <a-icon type=\"rollback\" /> {{ isWrongPracticeMode ? '退出错题练习' : '退出刷题' }}\n      </a-button>\n    </div>\n  </div>\n</a-modal>\n", null]}