{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\tools\\Keyboard.vue?vue&type=template&id=16bad24b&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\tools\\Keyboard.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"keyboard\">\n  <a-row class=\"control_A flex\" v-if=\"type == 1\">\n    <a-col :span=\"12\" class=\"control_direct\">\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowUp', 38)\"\n          @touchend=\"keyup($event, 'ArrowUp', 38)\"\n          class=\"key button_up\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowLeft', 37)\"\n          @touchend=\"keyup($event, 'ArrowLeft', 37)\"\n          class=\"key rotate_left button_left\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n        <p\n          @touchstart=\"keydown($event, 'ArrowRight', 39)\"\n          @touchend=\"keyup($event, 'ArrowRight', 39)\"\n          class=\"key rotate_right button_right\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowDown', 40)\"\n          @touchend=\"keyup($event, 'ArrowDown', 40)\"\n          class=\"key rotate_down button_down\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n    </a-col>\n    <a-col :span=\"12\" class=\"control_space\">\n      <p\n        @touchstart=\"keydown($event, ' ', 32)\"\n        @touchend=\"keyup($event, ' ', 32)\"\n        class=\"space button_space\"\n        id=\"\"\n      ></p>\n    </a-col>\n  </a-row>\n  <a-row class=\"control_B flex\" v-if=\"type == 2\">\n    <a-col :span=\"12\" class=\"control_direct\">\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'w', 87)\"\n          @touchend=\"keyup($event, 'w', 87)\"\n          class=\"key button_w\"\n          id=\"\"\n          style=\"background: url('/scratch3/image/w.png') no-repeat left top\"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'a', 65)\"\n          @touchend=\"keyup($event, 'a', 65)\"\n          class=\"key button_a\"\n          id=\"\"\n          style=\"background: url('/scratch3/image/a.png') no-repeat left top\"\n        ></p>\n        <p\n          @touchstart=\"keydown($event, 'd', 68)\"\n          @touchend=\"keyup($event, 'd', 68)\"\n          class=\"key button_d\"\n          id=\"\"\n          style=\"background: url('/scratch3/image/d.png') no-repeat left top\"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 's', 83)\"\n          @touchend=\"keyup($event, 's', 83)\"\n          class=\"key button_s\"\n          id=\"\"\n          style=\"background: url('/scratch3/image/s.png') no-repeat left top\"\n        ></p>\n      </div>\n    </a-col>\n    <a-col :span=\"12\" class=\"control_direct\">\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowUp', 38)\"\n          @touchend=\"keyup($event, 'ArrowUp', 38)\"\n          class=\"key button_up\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowLeft', 37)\"\n          @touchend=\"keyup($event, 'ArrowLeft', 37)\"\n          class=\"key rotate_left button_left\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n        <p\n          @touchstart=\"keydown($event, 'ArrowRight', 39)\"\n          @touchend=\"keyup($event, 'ArrowRight', 39)\"\n          class=\"key rotate_right button_right\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n      <div>\n        <p\n          @touchstart=\"keydown($event, 'ArrowDown', 40)\"\n          @touchend=\"keyup($event, 'ArrowDown', 40)\"\n          class=\"key rotate_down button_down\"\n          id=\"\"\n          style=\"\n            background: url('/scratch3/image/arrow.png') no-repeat left top;\n          \"\n        ></p>\n      </div>\n    </a-col>\n  </a-row>\n</div>\n", null]}