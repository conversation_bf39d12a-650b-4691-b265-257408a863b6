{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSuperQuery.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import moment from 'moment'\n  import * as utils from '@/utils/util'\n  import { mixinDevice } from '@/utils/mixin'\n  import JDate from '@/components/jeecg/JDate.vue'\n  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'\n  import JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser'\n  import JMultiSelectTag from '@/components/dict/JMultiSelectTag'\n  import JAreaLinkage from '@comp/jeecg/JAreaLinkage'\n\n  export default {\n    name: 'JSuperQuery',\n    mixins: [mixinDevice],\n    components: { JAreaLinkage, JMultiSelectTag, JDate, JSelectDepart, JSelectMultiUser },\n    props: {\n      /*\n       fieldList: [{\n          value:'',\n          text:'',\n          type:'',\n          dictCode:'' // 只要 dictCode 有值，无论 type 是什么，都显示为字典下拉框\n       }]\n       type:date datetime int number string\n      * */\n      fieldList: {\n        type: Array,\n        required: true\n      },\n      /*\n      * 这个回调函数接收一个数组参数 即查询条件\n      * */\n      callback: {\n        type: String,\n        required: false,\n        default: 'handleSuperQuery'\n      },\n\n      // 当前是否在加载中\n      loading: {\n        type: Boolean,\n        default: false\n      },\n\n      // 保存查询条件的唯一 code，通过该 code 区分\n      // 默认为 null，代表以当前路由全路径为区分Code\n      saveCode: {\n        type: String,\n        default: null\n      }\n\n    },\n    data() {\n      return {\n        moment,\n        fieldTreeData: [],\n\n        prompt: {\n          visible: false,\n          value: ''\n        },\n\n        visible: false,\n        queryParamsModel: [],\n        treeIcon: <a-icon type=\"file-text\"/>,\n        // 保存查询条件的treeData\n        saveTreeData: [],\n        // 保存查询条件的前缀名\n        saveCodeBefore: 'JSuperQuerySaved_',\n        // 查询类型，过滤条件匹配（and、or）\n        matchType: 'and',\n        superQueryFlag: false,\n      }\n    },\n    computed: {\n      izMobile() {\n        return this.device === 'mobile'\n      },\n      tooltipProps() {\n        return this.izMobile ? { visible: false } : {}\n      },\n      fullSaveCode() {\n        let saveCode = this.saveCode\n        if (saveCode == null || saveCode === '') {\n          saveCode = this.$route.fullPath\n        }\n        return this.saveCodeBefore + saveCode\n      },\n    },\n    watch: {\n      // 当 saveCode 变化时，重新查询已保存的条件\n      fullSaveCode: {\n        immediate: true,\n        handler() {\n          let list = this.$ls.get(this.fullSaveCode)\n          if (list instanceof Array) {\n            this.saveTreeData = list.map(i => this.renderSaveTreeData(i))\n          }\n        }\n      },\n      fieldList: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          let mainData = [], subData = []\n          val.forEach(item => {\n            let data = { ...item }\n            data.label = data.label || data.text\n            let hasChildren = (data.children instanceof Array)\n            data.disabled = hasChildren\n            data.selectable = !hasChildren\n            if (hasChildren) {\n              data.children = data.children.map(item2 => {\n                let child = { ...item2 }\n                child.label = child.label || child.text\n                child.label = data.label + '-' + child.label\n                child.value = data.value + ',' + child.value\n                child.val = ''\n                return child\n              })\n              data.val = ''\n              subData.push(data)\n            } else {\n              mainData.push(data)\n            }\n          })\n          this.fieldTreeData = mainData.concat(subData)\n        }\n      }\n    },\n\n    methods: {\n      show() {\n        if (!this.queryParamsModel || this.queryParamsModel.length === 0) {\n          this.resetLine()\n        }\n        this.visible = true\n      },\n      handleOk() {\n        if (!this.isNullArray(this.queryParamsModel)) {\n          let event = {\n            matchType: this.matchType,\n            params: this.removeEmptyObject(this.queryParamsModel)\n          }\n          // 移动端模式下关闭弹窗\n          if (this.izMobile) {\n            this.visible = false\n          }\n          this.emitCallback(event)\n        } else {\n          this.$message.warn(\"不能查询空条件\")\n        }\n      },\n      emitCallback(event = {}) {\n        let { params = [], matchType = this.matchType } = event\n        this.superQueryFlag = (params && params.length > 0)\n        for (let param of params) {\n          if (Array.isArray(param.val)) {\n            param.val = param.val.join(',')\n          }\n        }\n        console.debug('---高级查询参数--->', { params, matchType })\n        this.$emit(this.callback, params, matchType)\n      },\n      handleCancel() {\n        this.close()\n      },\n      close() {\n        this.$emit('close')\n        this.visible = false\n      },\n      handleAdd() {\n        this.addNewLine()\n      },\n      addNewLine() {\n        this.queryParamsModel.push({ rule: 'eq' })\n      },\n      resetLine() {\n        this.superQueryFlag = false\n        this.queryParamsModel = []\n        this.addNewLine()\n      },\n      handleDel(index) {\n        this.queryParamsModel.splice(index, 1)\n      },\n      handleSelected(node, item) {\n        let { type, options, dictCode, dictTable, customReturnField, popup } = node.dataRef\n        item['type'] = type\n        item['options'] = options\n        item['dictCode'] = dictCode\n        item['dictTable'] = dictTable\n        item['customReturnField'] = customReturnField\n        if (popup) {\n          item['popup'] = popup\n        }\n        this.$set(item, 'val', undefined)\n      },\n      handleOpen() {\n        this.show()\n      },\n      handleReset() {\n        this.resetLine()\n        this.emitCallback()\n      },\n      handleSave() {\n        let queryParams = this.removeEmptyObject(this.queryParamsModel)\n        if (this.isNullArray(queryParams)) {\n          this.$message.warning('空条件不能保存')\n        } else {\n          this.prompt.value = ''\n          this.prompt.visible = true\n        }\n      },\n      handlePromptOk() {\n        let { value } = this.prompt\n        if(!value){\n          this.$message.warning('保存名称不能为空')\n          return\n        }\n        // 取出查询条件\n        let records = this.removeEmptyObject(this.queryParamsModel)\n        // 判断有没有重名的\n        let filterList = this.saveTreeData.filter(i => i.originTitle === value)\n        if (filterList.length > 0) {\n          this.$confirm({\n            content: `${value} 已存在，是否覆盖？`,\n            onOk: () => {\n              this.prompt.visible = false\n              filterList[0].records = records\n              this.saveToLocalStore()\n              this.$message.success('保存成功')\n            }\n          })\n        } else {\n          // 没有重名的，直接添加\n          this.prompt.visible = false\n          // 添加到树列表中\n          this.saveTreeData.push(this.renderSaveTreeData({\n            title: value,\n            matchType: this.matchType,\n            records: records\n          }))\n          // 保存到 LocalStore\n          this.saveToLocalStore()\n          this.$message.success('保存成功')\n        }\n      },\n      handleTreeSelect(idx, event) {\n        if (event.selectedNodes[0]) {\n          let { matchType, records } = event.selectedNodes[0].data.props\n          // 将保存的matchType取出，兼容旧数据，如果没有保存就还是使用原来的\n          this.matchType = matchType || this.matchType\n          this.queryParamsModel = utils.cloneObject(records)\n        }\n      },\n      handleRemoveSaveTreeItem(event, vNode) {\n        // 阻止事件冒泡\n        event.stopPropagation()\n\n        this.$confirm({\n          content: '是否删除当前查询？',\n          onOk: () => {\n            let { eventKey } = vNode\n            this.saveTreeData.splice(Number.parseInt(eventKey.substring(2)), 1)\n            this.saveToLocalStore()\n          },\n        })\n      },\n\n      // 将查询保存到 LocalStore 里\n      saveToLocalStore() {\n        let saveValue = this.saveTreeData.map(({ originTitle, matchType, records }) => ({ title: originTitle, matchType, records }))\n        this.$ls.set(this.fullSaveCode, saveValue)\n      },\n\n      isNullArray(array) {\n        //判断是不是空数组对象\n        if (!array || array.length === 0) {\n          return true\n        }\n        if (array.length === 1) {\n          let obj = array[0]\n          if (!obj.field || (obj.val == null || obj.val === '') || !obj.rule) {\n            return true\n          }\n        }\n        return false\n      },\n      // 去掉数组中的空对象\n      removeEmptyObject(arr) {\n        let array = utils.cloneObject(arr)\n        for (let i = 0; i < array.length; i++) {\n          let item = array[i]\n          if (item == null || Object.keys(item).length <= 0) {\n            array.splice(i--, 1)\n          } else {\n            if (Array.isArray(item.options)) {\n              // 如果有字典属性，就不需要保存 options 了\n              if (item.dictCode) {\n                // 去掉特殊属性\n                delete item.options\n              }\n            }\n          }\n        }\n        return array\n      },\n\n      /** 渲染保存查询条件的 title（加个删除按钮） */\n      renderSaveTreeData(item) {\n        item.icon = this.treeIcon\n        item.originTitle = item['title']\n        item.title = (fn, vNode) => {\n          let { originTitle } = vNode.dataRef\n          return (\n            <div class=\"j-history-tree-title\">\n              <span>{originTitle}</span>\n\n              <div class=\"j-history-tree-title-closer\" onClick={e => this.handleRemoveSaveTreeItem(e, vNode)}>\n                <a-icon type=\"close-circle\"/>\n              </div>\n            </div>\n          )\n        }\n        return item\n      },\n\n      /** 判断是否允许多选 */\n      allowMultiple(item) {\n        return item.rule === 'in'\n      },\n\n      handleRuleChange(item, newValue) {\n        let oldValue = item.rule\n        this.$set(item, 'rule', newValue)\n        // 上一个规则是否是 in，且type是字典或下拉\n        if (oldValue === 'in') {\n          if (item.dictCode || item.options instanceof Array) {\n            let value = item.val\n            if (typeof item.val === 'string') {\n              value = item.val.split(',')[0]\n            } else if (Array.isArray(item.val)) {\n              value = item.val[0]\n            }\n            this.$set(item, 'val', value)\n          }\n        }\n      },\n\n      handleChangeJPopup(item, e, values) {\n        item.val = values[item.popup['destFields']]\n      },\n\n    }\n  }\n", {"version": 3, "sources": ["JSuperQuery.vue"], "names": [], "mappings": ";AAwMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA", "file": "JSuperQuery.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n<div class=\"j-super-query-box\">\n\n  <slot name=\"button\" :isActive=\"superQueryFlag\" :isMobile=\"izMobile\" :open=\"handleOpen\" :reset=\"handleReset\">\n    <a-tooltip v-if=\"superQueryFlag\" v-bind=\"tooltipProps\" :mouseLeaveDelay=\"0.2\">\n      <!-- begin 不知道为什么不加上这段代码就无法生效 -->\n      <span v-show=\"false\">{{tooltipProps}}</span>\n      <!-- end 不知道为什么不加上这段代码就无法生效 -->\n      <template slot=\"title\">\n        <span>已有高级查询条件生效</span>\n        <a-divider type=\"vertical\"/>\n        <a @click=\"handleReset\">清空</a>\n      </template>\n      <a-button-group>\n        <a-button type=\"primary\" @click=\"handleOpen\">\n          <a-icon type=\"appstore\" theme=\"twoTone\" spin/>\n          <span>高级查询</span>\n        </a-button>\n        <a-button v-if=\"izMobile\" type=\"primary\" icon=\"delete\" @click=\"handleReset\"/>\n      </a-button-group>\n    </a-tooltip>\n    <a-button v-else type=\"primary\" icon=\"filter\" @click=\"handleOpen\">高级查询</a-button>\n  </slot>\n\n  <j-modal\n    title=\"高级查询构造器\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    @cancel=\"handleCancel\"\n    :mask=\"false\"\n    :fullscreen=\"izMobile\"\n    class=\"j-super-query-modal\"\n    style=\"top:5%;max-height: 95%;\"\n  >\n\n    <template slot=\"footer\">\n      <div style=\"float: left\">\n        <a-button :loading=\"loading\" @click=\"handleReset\">重置</a-button>\n        <a-button :loading=\"loading\" @click=\"handleSave\">保存查询条件</a-button>\n      </div>\n      <a-button :loading=\"loading\" @click=\"handleCancel\">关闭</a-button>\n      <a-button :loading=\"loading\" type=\"primary\" @click=\"handleOk\">查询</a-button>\n    </template>\n\n    <a-spin :spinning=\"loading\">\n      <a-row>\n        <a-col :sm=\"24\" :md=\"24-5\">\n\n          <a-empty v-if=\"queryParamsModel.length === 0\" style=\"margin-bottom: 12px;\">\n            <div slot=\"description\">\n              <span>没有任何查询条件</span>\n              <a-divider type=\"vertical\"/>\n              <a @click=\"handleAdd\">点击新增</a>\n            </div>\n          </a-empty>\n\n          <a-form v-else layout=\"inline\">\n\n            <a-row style=\"margin-bottom: 12px;\">\n              <a-col :md=\"12\" :xs=\"24\">\n                <a-form-item label=\"过滤条件匹配\" :labelCol=\"{md: 6,xs:24}\" :wrapperCol=\"{md: 18,xs:24}\" style=\"width: 100%;\">\n                  <a-select v-model=\"matchType\" :getPopupContainer=\"node=>node.parentNode\" style=\"width: 100%;\">\n                    <a-select-option value=\"and\">AND（所有条件都要求匹配）</a-select-option>\n                    <a-select-option value=\"or\">OR（条件中的任意一个匹配）</a-select-option>\n                  </a-select>\n                </a-form-item>\n              </a-col>\n            </a-row>\n\n            <a-row type=\"flex\" style=\"margin-bottom:10px\" :gutter=\"16\" v-for=\"(item, index) in queryParamsModel\" :key=\"index\">\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-tree-select\n                  showSearch\n                  v-model=\"item.field\"\n                  :treeData=\"fieldTreeData\"\n                  :dropdownStyle=\"{ maxHeight: '400px', overflow: 'auto' }\"\n                  placeholder=\"选择查询字段\"\n                  allowClear\n                  treeDefaultExpandAll\n                  :getPopupContainer=\"node=>node.parentNode\"\n                  style=\"width: 100%\"\n                  @select=\"(val,option)=>handleSelected(option,item)\"\n                >\n                </a-tree-select>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <a-select placeholder=\"匹配规则\" :value=\"item.rule\" :getPopupContainer=\"node=>node.parentNode\" @change=\"handleRuleChange(item,$event)\">\n                  <a-select-option value=\"eq\">等于</a-select-option>\n                  <a-select-option value=\"like\">包含</a-select-option>\n                  <a-select-option value=\"right_like\">以..开始</a-select-option>\n                  <a-select-option value=\"left_like\">以..结尾</a-select-option>\n                  <a-select-option value=\"in\">在...中</a-select-option>\n                  <a-select-option value=\"ne\">不等于</a-select-option>\n                  <a-select-option value=\"gt\">大于</a-select-option>\n                  <a-select-option value=\"ge\">大于等于</a-select-option>\n                  <a-select-option value=\"lt\">小于</a-select-option>\n                  <a-select-option value=\"le\">小于等于</a-select-option>\n                </a-select>\n              </a-col>\n\n              <a-col :md=\"8\" :xs=\"24\" style=\"margin-bottom: 12px;\">\n                <template v-if=\"item.dictCode\">\n                  <template v-if=\"item.type === 'table-dict'\">\n                    <j-popup\n                      v-model=\"item.val\"\n                      :code=\"item.dictTable\"\n                      :field=\"item.dictCode\"\n                      :orgFields=\"item.dictCode\"\n                      :destFields=\"item.dictCode\"\n                    ></j-popup>\n                  </template>\n                  <template v-else>\n                    <j-multi-select-tag v-show=\"allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                    <j-dict-select-tag v-show=\"!allowMultiple(item)\" v-model=\"item.val\" :dictCode=\"item.dictCode\" placeholder=\"请选择\"/>\n                  </template>\n                </template>\n                <j-popup v-else-if=\"item.type === 'popup'\" :value=\"item.val\" v-bind=\"item.popup\" group-id=\"superQuery\" @input=\"(e,v)=>handleChangeJPopup(item,e,v)\"/>\n                <j-select-multi-user\n                  v-else-if=\"item.type === 'select-user' || item.type === 'sel_user'\"\n                  v-model=\"item.val\"\n                  :buttons=\"false\"\n                  :multiple=\"false\"\n                  placeholder=\"请选择用户\"\n                  :returnKeys=\"['id', item.customReturnField || 'username']\"\n                />\n                <j-select-depart\n                  v-else-if=\"item.type === 'select-depart' || item.type === 'sel_depart'\"\n                  v-model=\"item.val\"\n                  :multi=\"false\"\n                  placeholder=\"请选择部门\"\n                  :customReturnField=\"item.customReturnField || 'id'\"\n                />\n                <a-select\n                  v-else-if=\"item.options instanceof Array\"\n                  v-model=\"item.val\"\n                  :options=\"item.options\"\n                  allowClear\n                  placeholder=\"请选择\"\n                  :mode=\"allowMultiple(item)?'multiple':''\"\n                />\n                <j-area-linkage v-model=\"item.val\" v-else-if=\"item.type==='area-linkage' || item.type==='pca'\" style=\"width: 100%\"/>\n                <j-date v-else-if=\" item.type=='date' \" v-model=\"item.val\" placeholder=\"请选择日期\" style=\"width: 100%\"></j-date>\n                <j-date v-else-if=\" item.type=='datetime' \" v-model=\"item.val\" placeholder=\"请选择时间\" :show-time=\"true\" date-format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\"></j-date>\n                <a-time-picker v-else-if=\"item.type==='time'\" :value=\"item.val ? moment(item.val,'HH:mm:ss') : null\" format=\"HH:mm:ss\" style=\"width: 100%\" @change=\"(time,value)=>item.val=value\"/>\n                <a-input-number v-else-if=\" item.type=='int'||item.type=='number' \" style=\"width: 100%\" placeholder=\"请输入数值\" v-model=\"item.val\"/>\n                <a-input v-else v-model=\"item.val\" placeholder=\"请输入值\"/>\n              </a-col>\n\n              <a-col :md=\"4\" :xs=\"0\" style=\"margin-bottom: 12px;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n              <a-col :md=\"0\" :xs=\"24\" style=\"margin-bottom: 12px;text-align: right;\">\n                <a-button @click=\"handleAdd\" icon=\"plus\"></a-button>&nbsp;\n                <a-button @click=\"handleDel( index )\" icon=\"minus\"></a-button>\n              </a-col>\n\n            </a-row>\n\n          </a-form>\n        </a-col>\n        <a-col :sm=\"24\" :md=\"5\">\n          <!-- 查询记录 -->\n\n          <a-card class=\"j-super-query-history-card\" :bordered=\"true\">\n            <div slot=\"title\">\n              保存的查询\n            </div>\n\n            <a-empty v-if=\"saveTreeData.length === 0\" class=\"j-super-query-history-empty\" description=\"没有保存任何查询\"/>\n            <a-tree\n              v-else\n              class=\"j-super-query-history-tree\"\n              showIcon\n              :treeData=\"saveTreeData\"\n              :selectedKeys=\"[]\"\n              @select=\"handleTreeSelect\"\n            >\n            </a-tree>\n          </a-card>\n\n\n        </a-col>\n      </a-row>\n\n\n    </a-spin>\n\n    <a-modal title=\"请输入保存的名称\" :visible=\"prompt.visible\" @cancel=\"prompt.visible=false\" @ok=\"handlePromptOk\">\n      <a-input v-model=\"prompt.value\"></a-input>\n    </a-modal>\n\n  </j-modal>\n</div>\n</template>\n\n<script>\n  import moment from 'moment'\n  import * as utils from '@/utils/util'\n  import { mixinDevice } from '@/utils/mixin'\n  import JDate from '@/components/jeecg/JDate.vue'\n  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'\n  import JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser'\n  import JMultiSelectTag from '@/components/dict/JMultiSelectTag'\n  import JAreaLinkage from '@comp/jeecg/JAreaLinkage'\n\n  export default {\n    name: 'JSuperQuery',\n    mixins: [mixinDevice],\n    components: { JAreaLinkage, JMultiSelectTag, JDate, JSelectDepart, JSelectMultiUser },\n    props: {\n      /*\n       fieldList: [{\n          value:'',\n          text:'',\n          type:'',\n          dictCode:'' // 只要 dictCode 有值，无论 type 是什么，都显示为字典下拉框\n       }]\n       type:date datetime int number string\n      * */\n      fieldList: {\n        type: Array,\n        required: true\n      },\n      /*\n      * 这个回调函数接收一个数组参数 即查询条件\n      * */\n      callback: {\n        type: String,\n        required: false,\n        default: 'handleSuperQuery'\n      },\n\n      // 当前是否在加载中\n      loading: {\n        type: Boolean,\n        default: false\n      },\n\n      // 保存查询条件的唯一 code，通过该 code 区分\n      // 默认为 null，代表以当前路由全路径为区分Code\n      saveCode: {\n        type: String,\n        default: null\n      }\n\n    },\n    data() {\n      return {\n        moment,\n        fieldTreeData: [],\n\n        prompt: {\n          visible: false,\n          value: ''\n        },\n\n        visible: false,\n        queryParamsModel: [],\n        treeIcon: <a-icon type=\"file-text\"/>,\n        // 保存查询条件的treeData\n        saveTreeData: [],\n        // 保存查询条件的前缀名\n        saveCodeBefore: 'JSuperQuerySaved_',\n        // 查询类型，过滤条件匹配（and、or）\n        matchType: 'and',\n        superQueryFlag: false,\n      }\n    },\n    computed: {\n      izMobile() {\n        return this.device === 'mobile'\n      },\n      tooltipProps() {\n        return this.izMobile ? { visible: false } : {}\n      },\n      fullSaveCode() {\n        let saveCode = this.saveCode\n        if (saveCode == null || saveCode === '') {\n          saveCode = this.$route.fullPath\n        }\n        return this.saveCodeBefore + saveCode\n      },\n    },\n    watch: {\n      // 当 saveCode 变化时，重新查询已保存的条件\n      fullSaveCode: {\n        immediate: true,\n        handler() {\n          let list = this.$ls.get(this.fullSaveCode)\n          if (list instanceof Array) {\n            this.saveTreeData = list.map(i => this.renderSaveTreeData(i))\n          }\n        }\n      },\n      fieldList: {\n        deep: true,\n        immediate: true,\n        handler(val) {\n          let mainData = [], subData = []\n          val.forEach(item => {\n            let data = { ...item }\n            data.label = data.label || data.text\n            let hasChildren = (data.children instanceof Array)\n            data.disabled = hasChildren\n            data.selectable = !hasChildren\n            if (hasChildren) {\n              data.children = data.children.map(item2 => {\n                let child = { ...item2 }\n                child.label = child.label || child.text\n                child.label = data.label + '-' + child.label\n                child.value = data.value + ',' + child.value\n                child.val = ''\n                return child\n              })\n              data.val = ''\n              subData.push(data)\n            } else {\n              mainData.push(data)\n            }\n          })\n          this.fieldTreeData = mainData.concat(subData)\n        }\n      }\n    },\n\n    methods: {\n      show() {\n        if (!this.queryParamsModel || this.queryParamsModel.length === 0) {\n          this.resetLine()\n        }\n        this.visible = true\n      },\n      handleOk() {\n        if (!this.isNullArray(this.queryParamsModel)) {\n          let event = {\n            matchType: this.matchType,\n            params: this.removeEmptyObject(this.queryParamsModel)\n          }\n          // 移动端模式下关闭弹窗\n          if (this.izMobile) {\n            this.visible = false\n          }\n          this.emitCallback(event)\n        } else {\n          this.$message.warn(\"不能查询空条件\")\n        }\n      },\n      emitCallback(event = {}) {\n        let { params = [], matchType = this.matchType } = event\n        this.superQueryFlag = (params && params.length > 0)\n        for (let param of params) {\n          if (Array.isArray(param.val)) {\n            param.val = param.val.join(',')\n          }\n        }\n        console.debug('---高级查询参数--->', { params, matchType })\n        this.$emit(this.callback, params, matchType)\n      },\n      handleCancel() {\n        this.close()\n      },\n      close() {\n        this.$emit('close')\n        this.visible = false\n      },\n      handleAdd() {\n        this.addNewLine()\n      },\n      addNewLine() {\n        this.queryParamsModel.push({ rule: 'eq' })\n      },\n      resetLine() {\n        this.superQueryFlag = false\n        this.queryParamsModel = []\n        this.addNewLine()\n      },\n      handleDel(index) {\n        this.queryParamsModel.splice(index, 1)\n      },\n      handleSelected(node, item) {\n        let { type, options, dictCode, dictTable, customReturnField, popup } = node.dataRef\n        item['type'] = type\n        item['options'] = options\n        item['dictCode'] = dictCode\n        item['dictTable'] = dictTable\n        item['customReturnField'] = customReturnField\n        if (popup) {\n          item['popup'] = popup\n        }\n        this.$set(item, 'val', undefined)\n      },\n      handleOpen() {\n        this.show()\n      },\n      handleReset() {\n        this.resetLine()\n        this.emitCallback()\n      },\n      handleSave() {\n        let queryParams = this.removeEmptyObject(this.queryParamsModel)\n        if (this.isNullArray(queryParams)) {\n          this.$message.warning('空条件不能保存')\n        } else {\n          this.prompt.value = ''\n          this.prompt.visible = true\n        }\n      },\n      handlePromptOk() {\n        let { value } = this.prompt\n        if(!value){\n          this.$message.warning('保存名称不能为空')\n          return\n        }\n        // 取出查询条件\n        let records = this.removeEmptyObject(this.queryParamsModel)\n        // 判断有没有重名的\n        let filterList = this.saveTreeData.filter(i => i.originTitle === value)\n        if (filterList.length > 0) {\n          this.$confirm({\n            content: `${value} 已存在，是否覆盖？`,\n            onOk: () => {\n              this.prompt.visible = false\n              filterList[0].records = records\n              this.saveToLocalStore()\n              this.$message.success('保存成功')\n            }\n          })\n        } else {\n          // 没有重名的，直接添加\n          this.prompt.visible = false\n          // 添加到树列表中\n          this.saveTreeData.push(this.renderSaveTreeData({\n            title: value,\n            matchType: this.matchType,\n            records: records\n          }))\n          // 保存到 LocalStore\n          this.saveToLocalStore()\n          this.$message.success('保存成功')\n        }\n      },\n      handleTreeSelect(idx, event) {\n        if (event.selectedNodes[0]) {\n          let { matchType, records } = event.selectedNodes[0].data.props\n          // 将保存的matchType取出，兼容旧数据，如果没有保存就还是使用原来的\n          this.matchType = matchType || this.matchType\n          this.queryParamsModel = utils.cloneObject(records)\n        }\n      },\n      handleRemoveSaveTreeItem(event, vNode) {\n        // 阻止事件冒泡\n        event.stopPropagation()\n\n        this.$confirm({\n          content: '是否删除当前查询？',\n          onOk: () => {\n            let { eventKey } = vNode\n            this.saveTreeData.splice(Number.parseInt(eventKey.substring(2)), 1)\n            this.saveToLocalStore()\n          },\n        })\n      },\n\n      // 将查询保存到 LocalStore 里\n      saveToLocalStore() {\n        let saveValue = this.saveTreeData.map(({ originTitle, matchType, records }) => ({ title: originTitle, matchType, records }))\n        this.$ls.set(this.fullSaveCode, saveValue)\n      },\n\n      isNullArray(array) {\n        //判断是不是空数组对象\n        if (!array || array.length === 0) {\n          return true\n        }\n        if (array.length === 1) {\n          let obj = array[0]\n          if (!obj.field || (obj.val == null || obj.val === '') || !obj.rule) {\n            return true\n          }\n        }\n        return false\n      },\n      // 去掉数组中的空对象\n      removeEmptyObject(arr) {\n        let array = utils.cloneObject(arr)\n        for (let i = 0; i < array.length; i++) {\n          let item = array[i]\n          if (item == null || Object.keys(item).length <= 0) {\n            array.splice(i--, 1)\n          } else {\n            if (Array.isArray(item.options)) {\n              // 如果有字典属性，就不需要保存 options 了\n              if (item.dictCode) {\n                // 去掉特殊属性\n                delete item.options\n              }\n            }\n          }\n        }\n        return array\n      },\n\n      /** 渲染保存查询条件的 title（加个删除按钮） */\n      renderSaveTreeData(item) {\n        item.icon = this.treeIcon\n        item.originTitle = item['title']\n        item.title = (fn, vNode) => {\n          let { originTitle } = vNode.dataRef\n          return (\n            <div class=\"j-history-tree-title\">\n              <span>{originTitle}</span>\n\n              <div class=\"j-history-tree-title-closer\" onClick={e => this.handleRemoveSaveTreeItem(e, vNode)}>\n                <a-icon type=\"close-circle\"/>\n              </div>\n            </div>\n          )\n        }\n        return item\n      },\n\n      /** 判断是否允许多选 */\n      allowMultiple(item) {\n        return item.rule === 'in'\n      },\n\n      handleRuleChange(item, newValue) {\n        let oldValue = item.rule\n        this.$set(item, 'rule', newValue)\n        // 上一个规则是否是 in，且type是字典或下拉\n        if (oldValue === 'in') {\n          if (item.dictCode || item.options instanceof Array) {\n            let value = item.val\n            if (typeof item.val === 'string') {\n              value = item.val.split(',')[0]\n            } else if (Array.isArray(item.val)) {\n              value = item.val[0]\n            }\n            this.$set(item, 'val', value)\n          }\n        }\n      },\n\n      handleChangeJPopup(item, e, values) {\n        item.val = values[item.popup['destFields']]\n      },\n\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n\n  .j-super-query-box {\n    display: inline-block;\n  }\n\n  .j-super-query-modal {\n\n    .j-super-query-history-card {\n      /deep/ .ant-card-body,\n      /deep/ .ant-card-head-title {\n        padding: 0;\n      }\n\n      /deep/ .ant-card-head {\n        padding: 4px 8px;\n        min-height: initial;\n      }\n    }\n\n    .j-super-query-history-empty {\n      /deep/ .ant-empty-image {\n        height: 80px;\n        line-height: 80px;\n        margin-bottom: 0;\n      }\n\n      /deep/ img {\n        width: 80px;\n        height: 65px;\n      }\n\n      /deep/ .ant-empty-description {\n        color: #afafaf;\n        margin: 8px 0;\n      }\n    }\n\n    .j-super-query-history-tree {\n\n      .j-history-tree-title {\n        width: calc(100% - 24px);\n        position: relative;\n        display: inline-block;\n\n        &-closer {\n          color: #999999;\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 24px;\n          height: 24px;\n          text-align: center;\n          opacity: 0;\n          transition: opacity 0.3s, color 0.3s;\n\n          &:hover {\n            color: #666666;\n          }\n\n          &:active {\n            color: #333333;\n          }\n        }\n\n        &:hover {\n          .j-history-tree-title-closer {\n            opacity: 1;\n          }\n        }\n\n      }\n\n      /deep/ .ant-tree-switcher {\n        display: none;\n      }\n\n      /deep/ .ant-tree-node-content-wrapper {\n        width: 100%;\n      }\n    }\n\n  }\n\n</style>"]}]}