{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\ChartCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\ChartCard.vue", "mtime": 1752894786327}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["export default {\n  name: \"Chart<PERSON><PERSON>\",\n  props: {\n    title: {\n      type: String,\n      default: ''\n    },\n    total: {\n      type: String,\n      default: ''\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "title", "type", "String", "default", "total", "loading", "Boolean"], "sources": ["src/components/ChartCard.vue"], "sourcesContent": ["<template>\n  <a-card :loading=\"loading\" :body-style=\"{ padding: '20px 24px 8px' }\" :bordered=\"false\">\n    <div class=\"chart-card-header\">\n      <div class=\"meta\">\n        <span class=\"chart-card-title\">{{ title }}</span>\n        <span class=\"chart-card-action\">\n          <slot name=\"action\"></slot>\n        </span>\n      </div>\n      <div class=\"total\"><span>{{ total }}</span></div>\n    </div>\n    <div class=\"chart-card-content\">\n      <div class=\"content-fix\">\n        <slot></slot>\n      </div>\n    </div>\n    <div class=\"chart-card-footer\">\n      <div class=\"field\">\n        <slot name=\"footer\"></slot>\n      </div>\n    </div>\n  </a-card>\n</template>\n\n<script>\n  export default {\n    name: \"ChartCard\",\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      total: {\n        type: String,\n        default: ''\n      },\n      loading: {\n        type: Boolean,\n        default: false\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .chart-card-header {\n    position: relative;\n    overflow: hidden;\n    width: 100%;\n\n    .meta {\n      position: relative;\n      overflow: hidden;\n      width: 100%;\n      color: rgba(0, 0, 0, .45);\n      font-size: 14px;\n      line-height: 22px;\n    }\n  }\n\n  .chart-card-action {\n    cursor: pointer;\n    position: absolute;\n    top: 0;\n    right: 0;\n  }\n\n  .chart-card-footer {\n    border-top: 1px solid #e8e8e8;\n    padding-top: 9px;\n    margin-top: 8px;\n\n    > * {\n      position: relative;\n    }\n\n    .field {\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      margin: 0;\n    }\n  }\n\n  .chart-card-content {\n    margin-bottom: 12px;\n    position: relative;\n    height: 46px;\n    width: 100%;\n\n    .content-fix {\n      position: absolute;\n      left: 0;\n      bottom: 0;\n      width: 100%;\n    }\n  }\n\n  .total {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    word-break: break-all;\n    white-space: nowrap;\n    color: #000;\n    margin-top: 4px;\n    margin-bottom: 0;\n    font-size: 30px;\n    line-height: 38px;\n    height: 38px;\n  }\n</style>"], "mappings": "AAyBA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,OAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;EACA;AACA", "ignoreList": []}]}