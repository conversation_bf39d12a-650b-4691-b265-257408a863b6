{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue?vue&type=template&id=f042b266", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Index.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-card\", {\n    attrs: {\n      bordered: null\n    }\n  }, [_vm.html ? _c(\"div\", {\n    domProps: {\n      innerHTML: _vm._s(_vm.html)\n    }\n  }) : _c(\"h1\", [_vm._v(\"欢迎使用\" + _vm._s(_vm.brandName))])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "bordered", "html", "domProps", "innerHTML", "_s", "_v", "brandName", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/dashboard/Index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"a-card\", { attrs: { bordered: null } }, [\n    _vm.html\n      ? _c(\"div\", { domProps: { innerHTML: _vm._s(_vm.html) } })\n      : _c(\"h1\", [_vm._v(\"欢迎使用\" + _vm._s(_vm.brandName))]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE;IAAEE,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAK;EAAE,CAAC,EAAE,CACjDJ,GAAG,CAACK,IAAI,GACJJ,EAAE,CAAC,KAAK,EAAE;IAAEK,QAAQ,EAAE;MAAEC,SAAS,EAAEP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACK,IAAI;IAAE;EAAE,CAAC,CAAC,GACxDJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,MAAM,GAAGT,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC,CAAC,CACvD,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}]}