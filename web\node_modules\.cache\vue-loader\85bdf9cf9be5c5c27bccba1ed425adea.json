{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue?vue&type=template&id=********&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"page-header-index-wide page-header-wrapper-grid-content-main\">\n  <a-row :gutter=\"24\">\n    <a-col :md=\"24\" :lg=\"24\">\n      <a-card\n        style=\"width:100%\"\n        :bordered=\"false\"\n        :tabList=\"tabListNoTitle\"\n        :activeTabKey=\"noTitleKey\"\n        @tabChange=\"key => handleTabChange(key, 'noTitleKey')\"\n      >\n        <mineWorks-page v-if=\"noTitleKey === 'mineWorks'\"></mineWorks-page>\n      </a-card>\n    </a-col>\n  </a-row>\n</div>\n", null]}