{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarMultid.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\BarMultid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'BarMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jeecg', 'Jan.': 18.9, 'Feb.': 28.8, 'Mar.': 39.3, 'Apr.': 81.4, 'May': 47, 'Jun.': 20.3, 'Jul.': 24, 'Aug.': 35.6 },\n          { type: 'Jeeb<PERSON>', 'Jan.': 12.4, 'Feb.': 23.2, 'Mar.': 34.5, 'Apr.': 99.7, 'May': 52.6, 'Jun.': 35.5, 'Jul.': 37.4, 'Aug.': 42.4 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        adjust: [{\n          type: 'dodge',\n          marginRatio: 1 / 32\n        }]\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n\n        // bar 使用不了 - 和 / 所以替换下\n        let rows = dv.rows.map(row => {\n          if (typeof row.x === 'string') {\n            row.x = row.x.replace(/[-/]/g, '_')\n          }\n          return row\n        })\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.type) {\n              row.type = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n", {"version": 3, "sources": ["BarMultid.vue"], "names": [], "mappings": ";AAaA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarMultid.vue", "sourceRoot": "src/components/chart", "sourcesContent": ["<template>\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n    <v-chart :data=\"data\" :height=\"height\" :force-fit=\"true\" :onClick=\"handleClick\">\n      <v-tooltip/>\n      <v-axis/>\n      <v-legend/>\n      <v-bar position=\"x*y\" color=\"type\" :adjust=\"adjust\"/>\n    </v-chart>\n  </div>\n</template>\n\n<script>\n  import { DataSet } from '@antv/data-set'\n  import { ChartEventMixins } from './mixins/ChartMixins'\n\n  export default {\n    name: 'BarMultid',\n    mixins: [ChartEventMixins],\n    props: {\n      title: {\n        type: String,\n        default: ''\n      },\n      dataSource: {\n        type: Array,\n        default: () => [\n          { type: 'Jeecg', 'Jan.': 18.9, 'Feb.': 28.8, 'Mar.': 39.3, 'Apr.': 81.4, 'May': 47, 'Jun.': 20.3, 'Jul.': 24, 'Aug.': 35.6 },\n          { type: 'Jeeb<PERSON>', 'Jan.': 12.4, 'Feb.': 23.2, 'Mar.': 34.5, 'Apr.': 99.7, 'May': 52.6, 'Jun.': 35.5, 'Jul.': 37.4, 'Aug.': 42.4 }\n        ]\n      },\n      fields: {\n        type: Array,\n        default: () => ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.']\n      },\n      // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]\n      aliases: {\n        type: Array,\n        default: () => []\n      },\n      height: {\n        type: Number,\n        default: 254\n      }\n    },\n    data() {\n      return {\n        adjust: [{\n          type: 'dodge',\n          marginRatio: 1 / 32\n        }]\n      }\n    },\n    computed: {\n      data() {\n        const dv = new DataSet.View().source(this.dataSource)\n        dv.transform({\n          type: 'fold',\n          fields: this.fields,\n          key: 'x',\n          value: 'y'\n        })\n\n        // bar 使用不了 - 和 / 所以替换下\n        let rows = dv.rows.map(row => {\n          if (typeof row.x === 'string') {\n            row.x = row.x.replace(/[-/]/g, '_')\n          }\n          return row\n        })\n        // 替换别名\n        rows.forEach(row => {\n          for (let item of this.aliases) {\n            if (item.field === row.type) {\n              row.type = item.alias\n              break\n            }\n          }\n        })\n        return rows\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"]}]}