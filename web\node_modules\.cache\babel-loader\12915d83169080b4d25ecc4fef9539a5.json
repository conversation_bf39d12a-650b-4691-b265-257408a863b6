{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\index.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\Ellipsis\\index.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import Ellipsis from './Ellipsis';\nexport default Ellipsis;", {"version": 3, "names": ["El<PERSON><PERSON>"], "sources": ["E:/teachingproject/teaching/web/src/components/Ellipsis/index.js"], "sourcesContent": ["import Ellipsis from './Ellipsis'\n\nexport default Ellipsis"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AAEjC,eAAeA,QAAQ", "ignoreList": []}]}