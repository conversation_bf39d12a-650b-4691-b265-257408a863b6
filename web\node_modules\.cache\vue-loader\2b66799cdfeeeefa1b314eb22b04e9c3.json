{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\DashChartDemo.vue?vue&type=template&id=02d68dfe", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\DashChartDemo.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div :style=\"{ padding: '0 0 32px 32px' }\">\n  <v-chart :forceFit=\"true\" :height=\"300\" :data=\"chartData\" :scale=\"scale\">\n    <v-coord type=\"polar\" :startAngle=\"-202.5\" :endAngle=\"22.5\" :radius=\"0.75\"></v-coord>\n    <v-axis\n      dataKey=\"value\"\n      :zIndex=\"2\"\n      :line=\"null\"\n      :label=\"axisLabel\"\n      :subTickCount=\"4\"\n      :subTickLine=\"axisSubTickLine\"\n      :tickLine=\"axisTickLine\"\n      :grid=\"null\"\n    ></v-axis>\n    <v-axis dataKey=\"1\" :show=\"false\"></v-axis>\n    <v-series\n      gemo=\"point\"\n      position=\"value*1\"\n      shape=\"pointer\"\n      color=\"#1890FF\"\n      :active=\"false\"\n    ></v-series>\n    <v-guide\n      type=\"arc\"\n      :zIndex=\"0\"\n      :top=\"false\"\n      :start=\"arcGuide1Start\"\n      :end=\"arcGuide1End\"\n      :vStyle=\"arcGuide1Style\"\n    ></v-guide>\n    <v-guide\n      type=\"arc\"\n      :zIndex=\"1\"\n      :start=\"arcGuide2Start\"\n      :end=\"getArcGuide2End\"\n      :vStyle=\"arcGuide2Style\"\n    ></v-guide>\n    <v-guide\n      type=\"html\"\n      :position=\"htmlGuidePosition\"\n      :html=\"getHtmlGuideHtml()\"\n    ></v-guide>\n  </v-chart>\n</div>\n", null]}