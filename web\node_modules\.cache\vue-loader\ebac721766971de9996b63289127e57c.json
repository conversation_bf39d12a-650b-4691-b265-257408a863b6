{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\IndexBar.vue?vue&type=template&id=7da83222", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\IndexBar.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div :style=\"{ padding: '0 0 32px 32px' }\">\n  <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\n  <v-chart\n    height=\"254\"\n    :data=\"datasource\"\n    :forceFit=\"true\"\n    :padding=\"['auto', 'auto', '40', '50']\">\n    <v-tooltip />\n    <v-axis />\n    <v-bar position=\"x*y\"/>\n  </v-chart>\n</div>\n", null]}