{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue?vue&type=template&id=a37e67d6", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"a-select\", {\n    attrs: {\n      value: _vm.arrayValue,\n      mode: \"multiple\",\n      placeholder: _vm.placeholder\n    },\n    on: {\n      change: _vm.onChange\n    }\n  }, _vm._l(_vm.options, function (item, index) {\n    return _c(\"a-select-option\", {\n      key: index,\n      attrs: {\n        value: item.value\n      }\n    }, [_vm._v(\"\\n    \" + _vm._s(item.text || item.label) + \"\\n  \")]);\n  }), 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "value", "arrayValue", "mode", "placeholder", "on", "change", "onChange", "_l", "options", "item", "index", "key", "_v", "_s", "text", "label", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/components/jeecg/JSelectMultiple.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-select\",\n    {\n      attrs: {\n        value: _vm.arrayValue,\n        mode: \"multiple\",\n        placeholder: _vm.placeholder,\n      },\n      on: { change: _vm.onChange },\n    },\n    _vm._l(_vm.options, function (item, index) {\n      return _c(\n        \"a-select-option\",\n        { key: index, attrs: { value: item.value } },\n        [_vm._v(\"\\n    \" + _vm._s(item.text || item.label) + \"\\n  \")]\n      )\n    }),\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,UAAU,EACV;IACEE,KAAK,EAAE;MACLC,KAAK,EAAEJ,GAAG,CAACK,UAAU;MACrBC,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAEP,GAAG,CAACO;IACnB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAET,GAAG,CAACU;IAAS;EAC7B,CAAC,EACDV,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,OAAO,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACzC,OAAOb,EAAE,CACP,iBAAiB,EACjB;MAAEc,GAAG,EAAED,KAAK;MAAEX,KAAK,EAAE;QAAEC,KAAK,EAAES,IAAI,CAACT;MAAM;IAAE,CAAC,EAC5C,CAACJ,GAAG,CAACgB,EAAE,CAAC,QAAQ,GAAGhB,GAAG,CAACiB,EAAE,CAACJ,IAAI,CAACK,IAAI,IAAIL,IAAI,CAACM,KAAK,CAAC,GAAG,MAAM,CAAC,CAC9D,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBrB,MAAM,CAACsB,aAAa,GAAG,IAAI;AAE3B,SAAStB,MAAM,EAAEqB,eAAe", "ignoreList": []}]}