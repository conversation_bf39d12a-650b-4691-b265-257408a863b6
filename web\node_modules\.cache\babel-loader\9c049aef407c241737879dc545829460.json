{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Liquid.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\Liquid.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var sourceDataConst = [{\n  transfer: '一月',\n  value: 813\n}, {\n  transfer: '二月',\n  value: 233\n}, {\n  transfer: '三月',\n  value: 561\n}];\nexport default {\n  name: 'Liquid',\n  props: {\n    height: {\n      type: Number,\n      default: 0\n    },\n    width: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function data() {\n    return {\n      data: sourceDataConst,\n      scale: []\n    };\n  }\n};", {"version": 3, "names": ["sourceDataConst", "transfer", "value", "name", "props", "height", "type", "Number", "default", "width", "data", "scale"], "sources": ["src/components/chart/Liquid.vue"], "sourcesContent": ["<template>\n  <div>\n    <v-chart\n      :forceFit=\"true\"\n      :height=\"height\"\n      :width=\"width\"\n      :data=\"data\"\n      :scale=\"scale\"\n      :padding=\"0\">\n      <v-tooltip/>\n      <v-interval\n        :shape=\"['liquid-fill-gauge']\"\n        position=\"transfer*value\"\n        color=\"\"\n        :v-style=\"{\n          lineWidth: 8,\n          opacity: 0.75\n        }\"\n        :tooltip=\"[\n          'transfer*value',\n          (transfer, value) => {\n            return {\n              name: transfer,\n              value,\n            };\n          },\n        ]\"\n      ></v-interval>\n      <v-guide\n        v-for=\"(row, index) in data\"\n        :key=\"index\"\n        type=\"text\"\n        :top=\"true\"\n        :position=\"{\n          gender: row.transfer,\n          value: 45\n        }\"\n        :content=\"row.value + '%'\"\n        :v-style=\"{\n          fontSize: 100,\n          textAlign: 'center',\n          opacity: 0.75,\n        }\"\n      />\n    </v-chart>\n  </div>\n</template>\n\n<script>\n\n  const sourceDataConst = [\n    { transfer: '一月', value: 813 },\n    { transfer: '二月', value: 233 },\n    { transfer: '三月', value: 561 }\n  ]\n\n  export default {\n    name: 'Liquid',\n    props: {\n      height: {\n        type: Number,\n        default: 0\n      },\n      width: {\n        type: Number,\n        default: 0\n      }\n    },\n    data() {\n      return {\n        data: sourceDataConst,\n        scale: []\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAkDA,IAAAA,eAAA,IACA;EAAAC,QAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,QAAA;EAAAC,KAAA;AAAA,GACA;EAAAD,QAAA;EAAAC,KAAA;AAAA,EACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA,EAAAV,eAAA;MACAW,KAAA;IACA;EACA;AACA", "ignoreList": []}]}