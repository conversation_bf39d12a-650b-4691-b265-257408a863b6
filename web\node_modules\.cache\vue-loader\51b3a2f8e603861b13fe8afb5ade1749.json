{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\JSelectClassModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecgbiz\\modal\\JSelectClassModal.vue", "mtime": 1750647514565}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import { queryDepartTreeList, queryMyDepartTreeList } from '@/api/api'\n  export default {\n    name: 'JSelectClassModal',\n    props:{\n      modalWidth: {\n        type: Number,\n        default: 500\n      },\n      multi: {\n        type: Boolean,\n        default: false\n      },\n      rootOpened: {\n        type: Boolean,\n        default: true\n      },\n      onlyLeaf: {\n        type: Boolean,\n        default: false\n      },\n      departId: {\n        type: String\n      },\n      // 默认传入班级分类，避免显示其他类型机构\n      onlyCategory: {\n        type: Number,\n        default: 3 // 假设3是班级类型\n      }\n    },\n    data(){\n      return {\n        visible: false,\n        loading: false,\n        treeData: [],\n        autoExpandParent: true,\n        expandedKeys: [],\n        dataList: [],\n        checkedKeys: [],\n        checkedRows: [],\n        searchValue: \"\"\n      }\n    },\n    created(){\n      this.loadClasses();\n    },\n    watch:{\n      departId(){\n        this.initClassComponent()\n      },\n      visible: {\n        handler(val) {\n          if (this.departId) {\n            this.checkedKeys = this.departId.split(\",\");\n          } else {\n            this.checkedKeys = [];\n          }\n          // 修改：当模态框显示时，重新加载班级数据\n          if (val === true) {\n            this.loadClasses();\n          }\n        }\n      }\n    },\n    methods:{\n      show(){\n        this.visible = true\n        this.checkedRows = []\n        this.checkedKeys = []\n        this.searchValue = ''\n        this.loadClasses()\n      },\n      loadClasses(){\n        this.loading = true\n        // 注释：恢复使用queryMyDepartTreeList()方法，确保教师角色只能看到自己关联的班级\n        queryMyDepartTreeList().then(res=>{\n          this.loading = false\n          if(res.success){\n            let arr = [...res.result]\n            // 清空之前的数据\n            this.dataList = []\n            this.reWriterWithSlot(arr)\n            this.treeData = arr\n            this.initClassComponent()\n            if(this.onlyLeaf || this.onlyCategory){\n              this.disableNoneChildNode(this.treeData)\n            }\n            if(this.rootOpened){\n              this.initExpandedKeys(res.result)\n            }\n            console.log('班级树数据:', this.treeData)\n            console.log('扁平化数据列表:', this.dataList)\n          }\n        }).catch(() => {\n          this.loading = false\n        })\n      },\n      // 递归禁用节点\n      disableNoneChildNode(treeData){\n        for(var i=0; i<treeData.length; i++){\n          if(this.onlyCategory!=null){\n            if(treeData[i].orgCategory==this.onlyCategory){\n              treeData[i].disabled = false\n            }else{\n              treeData[i].disabled = true\n            }\n          }\n          if(treeData[i].isLeaf){\n            // treeData[i].disabled = false\n          }else{\n            if(this.onlyLeaf){\n              treeData[i].disabled = true\n            }\n            this.disableNoneChildNode(treeData[i].children)\n          }\n        }\n      },\n      initClassComponent(){\n        let names = ''\n        if(this.departId){\n          let currDepartId = this.departId\n          for(let item of this.dataList){\n            if(currDepartId.indexOf(item.key)>=0){\n              names+=\",\"+item.title\n            }\n          }\n          if(names){\n            names = names.substring(1)\n          }\n        }\n        this.$emit(\"initComp\",names)\n      },\n      reWriterWithSlot(arr){\n        for(let item of arr){\n          // 为每个节点添加scopedSlots，确保能正确渲染标题\n          item.scopedSlots = { title: 'title' }\n          \n          // 添加到扁平化列表中\n          this.dataList.push(item)\n          \n          // 递归处理子节点\n          if(item.children && item.children.length>0){\n            this.reWriterWithSlot(item.children)\n          }\n        }\n      },\n      initExpandedKeys(arr){\n        if(arr && arr.length>0){\n          let keys = []\n          for(let item of arr){\n            if(item.children && item.children.length>0){\n              keys.push(item.id)\n            }\n          }\n          this.expandedKeys=[...keys]\n        }else{\n          this.expandedKeys=[]\n        }\n      },\n      onCheck (checkedKeys,info) {\n        if(!this.multi){\n          let arr = checkedKeys.checked.filter(item => this.checkedKeys.indexOf(item) < 0)\n          this.checkedKeys = [...arr]\n          this.checkedRows = (this.checkedKeys.length === 0) ? [] : [info.node.dataRef]\n        }else{\n          this.checkedKeys = checkedKeys.checked\n          this.checkedRows = this.getCheckedRows(this.checkedKeys)\n        }\n      },\n      onSelect(selectedKeys,info) {\n        let keys = []\n        keys.push(selectedKeys[0])\n        if(!this.checkedKeys || this.checkedKeys.length===0 || !this.multi){\n          this.checkedKeys = [...keys]\n          this.checkedRows=[info.node.dataRef]\n        }else{\n          let currKey = info.node.dataRef.key\n          if(this.checkedKeys.indexOf(currKey)>=0){\n            this.checkedKeys = this.checkedKeys.filter(item=> item !==currKey)\n          }else{\n            this.checkedKeys.push(...keys)\n          }\n        }\n        this.checkedRows = this.getCheckedRows(this.checkedKeys)\n      },\n      onExpand (expandedKeys) {\n        this.expandedKeys = expandedKeys\n        this.autoExpandParent = false\n      },\n      handleSubmit(){\n        if(!this.checkedKeys || this.checkedKeys.length==0){\n          this.$emit(\"ok\",'')\n        }else{\n          this.$emit(\"ok\",this.checkedRows,this.checkedKeys.join(\",\"))\n        }\n        this.handleClear()\n      },\n      handleCancel(){\n        this.handleClear()\n      },\n      handleClear(){\n        this.visible=false\n      },\n      getParentKey(currKey,treeData){\n        let parentKey\n        for (let i = 0; i < treeData.length; i++) {\n          const node = treeData[i]\n          if (node.children) {\n            if (node.children.some(item => item.key === currKey)) {\n              parentKey = node.key\n            } else if (this.getParentKey(currKey, node.children)) {\n              parentKey = this.getParentKey(currKey, node.children)\n            }\n          }\n        }\n        return parentKey\n      },\n      onSearch(value){\n        // 重置搜索状态\n        if (!value) {\n          this.searchValue = ''\n          // 如果rootOpened为true，保持原有展开状态，否则折叠所有节点\n          if (!this.rootOpened) {\n            this.expandedKeys = []\n          }\n          this.autoExpandParent = false\n          return\n        }\n        \n        // 转换为小写进行不区分大小写搜索\n        const lowerValue = value.toLowerCase()\n        \n        // 存储匹配的节点和需要展开的父节点\n        let expandedKeys = []\n        let matchedKeys = []\n        \n        // 遍历所有节点查找匹配项\n        for (let item of this.dataList) {\n          if (item.title && item.title.toLowerCase().indexOf(lowerValue) > -1) {\n            // 找到匹配的节点\n            matchedKeys.push(item.key)\n            \n            // 查找并添加所有父节点到展开列表\n            let parentKey = this.getParentKey(item.key, this.treeData)\n            while (parentKey) {\n              expandedKeys.push(parentKey)\n              parentKey = this.getParentKey(parentKey, this.treeData)\n            }\n          }\n        }\n        \n        // 合并展开的键并去重\n        expandedKeys = [...new Set([...expandedKeys, ...matchedKeys])]\n        \n        // 更新组件状态\n        this.expandedKeys = expandedKeys\n        this.searchValue = value\n        this.autoExpandParent = true\n        \n        console.log('搜索值:', value)\n        console.log('展开的节点:', expandedKeys)\n      },\n      // 根据 checkedKeys 获取 rows\n      getCheckedRows(checkedKeys) {\n        const forChildren = (list, key) => {\n          for (let item of list) {\n            if (item.id === key) {\n              return item\n            }\n            if (item.children instanceof Array) {\n              let value = forChildren(item.children, key)\n              if (value != null) {\n                return value\n              }\n            }\n          }\n          return null\n        }\n\n        let rows = []\n        for (let key of checkedKeys) {\n          let row = forChildren(this.treeData, key)\n          if (row != null) {\n            rows.push(row)\n          }\n        }\n        return rows\n      }\n    }\n  }\n\n", {"version": 3, "sources": ["JSelectClassModal.vue"], "names": [], "mappings": ";AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JSelectClassModal.vue", "sourceRoot": "src/components/jeecgbiz/modal", "sourcesContent": ["<template>\n  <a-modal\n    title=\"选择班级\"\n    :width=\"modalWidth\"\n    :visible=\"visible\"\n    @ok=\"handleSubmit\"\n    @cancel=\"handleCancel\"\n    cancelText=\"关闭\">\n    <a-spin tip=\"Loading...\" :spinning=\"loading\">\n      <a-input-search style=\"margin-bottom: 8px\" placeholder=\"请输入班级名称按回车进行搜索\" @search=\"onSearch\" />\n      <a-tree\n        checkable\n        class=\"my-class-select-tree\"\n        :treeData=\"treeData\"\n        :checkStrictly=\"true\"\n        @check=\"onCheck\"\n        @select=\"onSelect\"\n        @expand=\"onExpand\"\n        :autoExpandParent=\"autoExpandParent\"\n        :expandedKeys=\"expandedKeys\"\n        :checkedKeys=\"checkedKeys\">\n\n        <template slot=\"title\" slot-scope=\"{title}\">\n          <template v-if=\"searchValue && title\">\n            <template v-if=\"title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1\">\n              {{title.substr(0, title.toLowerCase().indexOf(searchValue.toLowerCase()))}}\n              <span style=\"color: #f50\">{{title.substr(title.toLowerCase().indexOf(searchValue.toLowerCase()), searchValue.length)}}</span>\n              {{title.substr(title.toLowerCase().indexOf(searchValue.toLowerCase()) + searchValue.length)}}\n            </template>\n            <template v-else>{{title}}</template>\n          </template>\n          <template v-else>{{title}}</template>\n        </template>\n      </a-tree>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import { queryDepartTreeList, queryMyDepartTreeList } from '@/api/api'\n  export default {\n    name: 'JSelectClassModal',\n    props:{\n      modalWidth: {\n        type: Number,\n        default: 500\n      },\n      multi: {\n        type: Boolean,\n        default: false\n      },\n      rootOpened: {\n        type: Boolean,\n        default: true\n      },\n      onlyLeaf: {\n        type: Boolean,\n        default: false\n      },\n      departId: {\n        type: String\n      },\n      // 默认传入班级分类，避免显示其他类型机构\n      onlyCategory: {\n        type: Number,\n        default: 3 // 假设3是班级类型\n      }\n    },\n    data(){\n      return {\n        visible: false,\n        loading: false,\n        treeData: [],\n        autoExpandParent: true,\n        expandedKeys: [],\n        dataList: [],\n        checkedKeys: [],\n        checkedRows: [],\n        searchValue: \"\"\n      }\n    },\n    created(){\n      this.loadClasses();\n    },\n    watch:{\n      departId(){\n        this.initClassComponent()\n      },\n      visible: {\n        handler(val) {\n          if (this.departId) {\n            this.checkedKeys = this.departId.split(\",\");\n          } else {\n            this.checkedKeys = [];\n          }\n          // 修改：当模态框显示时，重新加载班级数据\n          if (val === true) {\n            this.loadClasses();\n          }\n        }\n      }\n    },\n    methods:{\n      show(){\n        this.visible = true\n        this.checkedRows = []\n        this.checkedKeys = []\n        this.searchValue = ''\n        this.loadClasses()\n      },\n      loadClasses(){\n        this.loading = true\n        // 注释：恢复使用queryMyDepartTreeList()方法，确保教师角色只能看到自己关联的班级\n        queryMyDepartTreeList().then(res=>{\n          this.loading = false\n          if(res.success){\n            let arr = [...res.result]\n            // 清空之前的数据\n            this.dataList = []\n            this.reWriterWithSlot(arr)\n            this.treeData = arr\n            this.initClassComponent()\n            if(this.onlyLeaf || this.onlyCategory){\n              this.disableNoneChildNode(this.treeData)\n            }\n            if(this.rootOpened){\n              this.initExpandedKeys(res.result)\n            }\n            console.log('班级树数据:', this.treeData)\n            console.log('扁平化数据列表:', this.dataList)\n          }\n        }).catch(() => {\n          this.loading = false\n        })\n      },\n      // 递归禁用节点\n      disableNoneChildNode(treeData){\n        for(var i=0; i<treeData.length; i++){\n          if(this.onlyCategory!=null){\n            if(treeData[i].orgCategory==this.onlyCategory){\n              treeData[i].disabled = false\n            }else{\n              treeData[i].disabled = true\n            }\n          }\n          if(treeData[i].isLeaf){\n            // treeData[i].disabled = false\n          }else{\n            if(this.onlyLeaf){\n              treeData[i].disabled = true\n            }\n            this.disableNoneChildNode(treeData[i].children)\n          }\n        }\n      },\n      initClassComponent(){\n        let names = ''\n        if(this.departId){\n          let currDepartId = this.departId\n          for(let item of this.dataList){\n            if(currDepartId.indexOf(item.key)>=0){\n              names+=\",\"+item.title\n            }\n          }\n          if(names){\n            names = names.substring(1)\n          }\n        }\n        this.$emit(\"initComp\",names)\n      },\n      reWriterWithSlot(arr){\n        for(let item of arr){\n          // 为每个节点添加scopedSlots，确保能正确渲染标题\n          item.scopedSlots = { title: 'title' }\n          \n          // 添加到扁平化列表中\n          this.dataList.push(item)\n          \n          // 递归处理子节点\n          if(item.children && item.children.length>0){\n            this.reWriterWithSlot(item.children)\n          }\n        }\n      },\n      initExpandedKeys(arr){\n        if(arr && arr.length>0){\n          let keys = []\n          for(let item of arr){\n            if(item.children && item.children.length>0){\n              keys.push(item.id)\n            }\n          }\n          this.expandedKeys=[...keys]\n        }else{\n          this.expandedKeys=[]\n        }\n      },\n      onCheck (checkedKeys,info) {\n        if(!this.multi){\n          let arr = checkedKeys.checked.filter(item => this.checkedKeys.indexOf(item) < 0)\n          this.checkedKeys = [...arr]\n          this.checkedRows = (this.checkedKeys.length === 0) ? [] : [info.node.dataRef]\n        }else{\n          this.checkedKeys = checkedKeys.checked\n          this.checkedRows = this.getCheckedRows(this.checkedKeys)\n        }\n      },\n      onSelect(selectedKeys,info) {\n        let keys = []\n        keys.push(selectedKeys[0])\n        if(!this.checkedKeys || this.checkedKeys.length===0 || !this.multi){\n          this.checkedKeys = [...keys]\n          this.checkedRows=[info.node.dataRef]\n        }else{\n          let currKey = info.node.dataRef.key\n          if(this.checkedKeys.indexOf(currKey)>=0){\n            this.checkedKeys = this.checkedKeys.filter(item=> item !==currKey)\n          }else{\n            this.checkedKeys.push(...keys)\n          }\n        }\n        this.checkedRows = this.getCheckedRows(this.checkedKeys)\n      },\n      onExpand (expandedKeys) {\n        this.expandedKeys = expandedKeys\n        this.autoExpandParent = false\n      },\n      handleSubmit(){\n        if(!this.checkedKeys || this.checkedKeys.length==0){\n          this.$emit(\"ok\",'')\n        }else{\n          this.$emit(\"ok\",this.checkedRows,this.checkedKeys.join(\",\"))\n        }\n        this.handleClear()\n      },\n      handleCancel(){\n        this.handleClear()\n      },\n      handleClear(){\n        this.visible=false\n      },\n      getParentKey(currKey,treeData){\n        let parentKey\n        for (let i = 0; i < treeData.length; i++) {\n          const node = treeData[i]\n          if (node.children) {\n            if (node.children.some(item => item.key === currKey)) {\n              parentKey = node.key\n            } else if (this.getParentKey(currKey, node.children)) {\n              parentKey = this.getParentKey(currKey, node.children)\n            }\n          }\n        }\n        return parentKey\n      },\n      onSearch(value){\n        // 重置搜索状态\n        if (!value) {\n          this.searchValue = ''\n          // 如果rootOpened为true，保持原有展开状态，否则折叠所有节点\n          if (!this.rootOpened) {\n            this.expandedKeys = []\n          }\n          this.autoExpandParent = false\n          return\n        }\n        \n        // 转换为小写进行不区分大小写搜索\n        const lowerValue = value.toLowerCase()\n        \n        // 存储匹配的节点和需要展开的父节点\n        let expandedKeys = []\n        let matchedKeys = []\n        \n        // 遍历所有节点查找匹配项\n        for (let item of this.dataList) {\n          if (item.title && item.title.toLowerCase().indexOf(lowerValue) > -1) {\n            // 找到匹配的节点\n            matchedKeys.push(item.key)\n            \n            // 查找并添加所有父节点到展开列表\n            let parentKey = this.getParentKey(item.key, this.treeData)\n            while (parentKey) {\n              expandedKeys.push(parentKey)\n              parentKey = this.getParentKey(parentKey, this.treeData)\n            }\n          }\n        }\n        \n        // 合并展开的键并去重\n        expandedKeys = [...new Set([...expandedKeys, ...matchedKeys])]\n        \n        // 更新组件状态\n        this.expandedKeys = expandedKeys\n        this.searchValue = value\n        this.autoExpandParent = true\n        \n        console.log('搜索值:', value)\n        console.log('展开的节点:', expandedKeys)\n      },\n      // 根据 checkedKeys 获取 rows\n      getCheckedRows(checkedKeys) {\n        const forChildren = (list, key) => {\n          for (let item of list) {\n            if (item.id === key) {\n              return item\n            }\n            if (item.children instanceof Array) {\n              let value = forChildren(item.children, key)\n              if (value != null) {\n                return value\n              }\n            }\n          }\n          return null\n        }\n\n        let rows = []\n        for (let key of checkedKeys) {\n          let row = forChildren(this.treeData, key)\n          if (row != null) {\n            rows.push(row)\n          }\n        }\n        return rows\n      }\n    }\n  }\n\n</script>\n\n<style lang=\"less\" scoped>\n  // 限制班级选择树高度，避免班级太多时点击确定不便\n  .my-class-select-tree{\n    height: 350px;\n    overflow-y: scroll;\n  }\n\n</style> "]}]}