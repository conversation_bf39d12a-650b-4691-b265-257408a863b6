{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue?vue&type=style&index=0&id=1b0d512f&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\PracticeModeSelector.vue", "mtime": 1752749894266}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.mode-selection {\n  margin-bottom: 12px;\n  background: linear-gradient(135deg, #fff, #f8f9ff);\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  .mode-title {\n    text-align: center;\n    margin-bottom: 24px;\n    position: relative;\n    padding-bottom: 16px;\n\n    &:after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 80px;\n      height: 3px;\n      background: linear-gradient(90deg, #1890ff, #52c41a);\n      border-radius: 3px;\n    }\n\n    h2 {\n      font-size: 32px;\n      margin-bottom: 12px;\n      font-weight: 600;\n      color: #1890ff;\n      text-shadow: 1px 1px 3px rgba(24, 144, 255, 0.2);\n      letter-spacing: 1px;\n\n      .anticon {\n        color: #1890ff;\n        margin-right: 8px;\n        font-size: 28px;\n        vertical-align: middle;\n        animation: shake 2s infinite;\n      }\n\n      @media screen and (max-width: 768px) {\n        font-size: 22px;\n      }\n    }\n\n    p {\n      color: #8c8c8c;\n      font-size: 16px;\n    }\n  }\n\n  // 题库数量标志样式\n  .question-bank-count {\n    position: absolute;\n    top: 10px;\n    right: 20px;\n    background: #e6f7ff;\n    border: 2px solid #91d5ff;\n    border-radius: 8px;\n    padding: 8px 16px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n    z-index: 1;\n\n    .count-number {\n      font-size: 28px;\n      font-weight: 700;\n      color: #1890ff;\n      line-height: 1.2;\n      &:after {\n        content: ' 题';\n        font-size: 16px;\n        font-weight: 400;\n      }\n    }\n\n    .count-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #096dd9;\n      margin-top: 2px;\n    }\n\n    @media screen and (max-width: 768px) {\n      top: 5px;\n      right: 10px;\n      padding: 6px 12px;\n\n      .count-number {\n        font-size: 20px;\n        &:after {\n          font-size: 14px;\n        }\n      }\n\n      .count-label {\n        font-size: 12px;\n      }\n    }\n  }\n\n  // 小标题样式\n  .section-title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 12px;\n    position: relative;\n    padding-left: 10px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 2px;\n      bottom: 2px;\n      width: 3px;\n      background: linear-gradient(to bottom, #1890ff, #40a9ff);\n      border-radius: 3px;\n    }\n\n    .title-icon {\n      margin-right: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 24px;\n      height: 24px;\n      background: #e6f7ff;\n      border-radius: 4px;\n\n      .anticon {\n        color: #1890ff;\n        font-size: 14px;\n      }\n    }\n\n    .title-text {\n      font-size: 16px;\n      font-weight: 600;\n      color: #262626;\n      letter-spacing: 0.5px;\n    }\n\n    @media screen and (max-width: 768px) {\n      margin-bottom: 8px;\n\n      .title-icon {\n        width: 20px;\n        height: 20px;\n\n        .anticon {\n          font-size: 12px;\n        }\n      }\n\n      .title-text {\n        font-size: 14px;\n      }\n    }\n  }\n\n  // 科目选择卡片样式\n  .subject-selection-cards {\n    display: flex;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .subject-card {\n      flex: 1;\n      background: #f9f9f9;\n      border: 2px solid #f0f0f0;\n      border-radius: 10px;\n      padding: 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n\n      &:hover {\n        border-color: #91d5ff;\n        background: #f0f7ff;\n      }\n\n      &.subject-selected {\n        border-color: #1890ff;\n        background: #e6f7ff;\n        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n      }\n\n      .subject-icon {\n        width: 48px;\n        height: 48px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #fff;\n        border-radius: 50%;\n        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);\n        margin-right: 12px;\n      }\n\n      .subject-name {\n        font-size: 16px;\n        font-weight: 600;\n        color: #262626;\n      }\n\n      @media screen and (max-width: 768px) {\n        margin-bottom: 10px;\n      }\n    }\n  }\n\n  // 复选框组样式\n  .custom-checkbox-group {\n    .ant-checkbox-wrapper {\n      margin-right: 20px;\n\n      .anticon {\n        margin-right: 4px;\n        color: #1890ff;\n      }\n    }\n  }\n\n  // 难度选择样式\n  .difficulty-selection {\n    .difficulty-labels {\n      display: flex;\n      justify-content: center;\n      margin-bottom: 10px;\n\n      .difficulty-value {\n        background: #f0f7ff;\n        padding: 4px 16px;\n        border-radius: 16px;\n        font-weight: 500;\n        color: #1890ff;\n      }\n\n      .difficulty-divider {\n        margin: 0 16px;\n        color: #8c8c8c;\n        display: flex;\n        align-items: center;\n      }\n    }\n\n    .slider-divider {\n      display: flex;\n      height: 100%;\n      align-items: center;\n      justify-content: center;\n      color: #8c8c8c;\n    }\n  }\n\n  // 模式卡片样式\n  .mode-cards {\n    display: flex;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .mode-card {\n      flex: 1;\n      background: #f9f9f9;\n      border: 2px solid #f0f0f0;\n      border-radius: 10px;\n      padding: 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n\n      &:hover {\n        border-color: #91d5ff;\n        background: #f0f7ff;\n      }\n\n      &.selected {\n        border-color: #1890ff;\n        background: #e6f7ff;\n        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n      }\n\n      .mode-card-icon {\n        width: 48px;\n        height: 48px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #fff;\n        border-radius: 50%;\n        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);\n        margin-right: 12px;\n\n        .anticon {\n          font-size: 24px;\n          color: #1890ff;\n        }\n      }\n\n      .mode-card-content {\n        h4 {\n          font-size: 16px;\n          font-weight: 600;\n          margin-bottom: 4px;\n          color: #262626;\n        }\n\n        p {\n          font-size: 14px;\n          color: #8c8c8c;\n          margin: 0;\n        }\n      }\n\n      @media screen and (max-width: 768px) {\n        margin-bottom: 10px;\n      }\n    }\n  }\n\n  // 操作按钮样式\n  .practice-actions {\n    text-align: center;\n    margin-top: 24px;\n\n    button {\n      height: 48px;\n      padding: 0 24px;\n      border-radius: 24px;\n      font-size: 16px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n      margin: 0 8px;\n\n      &.ant-btn-primary {\n        background: linear-gradient(135deg, #1890ff, #096dd9);\n        border: none;\n        box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);\n\n        &:hover {\n          background: linear-gradient(135deg, #40a9ff, #1890ff);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);\n        }\n      }\n\n      &.ant-btn-success {\n        background: linear-gradient(135deg, #52c41a, #389e0d);\n        border: none;\n        color: #fff;\n        box-shadow: 0 5px 15px rgba(82, 196, 26, 0.3);\n\n        &:hover {\n          background: linear-gradient(135deg, #73d13d, #52c41a);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 20px rgba(82, 196, 26, 0.4);\n        }\n      }\n\n      &:not(.ant-btn-primary):not(.ant-btn-success) {\n        background: #fff;\n        border: 1px solid #d9d9d9;\n\n        &:hover {\n          border-color: #40a9ff;\n          color: #40a9ff;\n          transform: translateY(-2px);\n        }\n      }\n\n      .anticon {\n        font-size: 18px;\n      }\n\n      @media screen and (max-width: 768px) {\n        height: 36px;\n        padding: 0 12px;\n        font-size: 14px;\n        margin: 0 4px;\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes shake {\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70%, 90% {\n    transform: translateX(-2px);\n  }\n  20%, 40%, 60%, 80% {\n    transform: translateX(2px);\n  }\n}\n", {"version": 3, "sources": ["PracticeModeSelector.vue"], "names": [], "mappings": ";AAmWA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PracticeModeSelector.vue", "sourceRoot": "src/views/examSystem/components", "sourcesContent": ["<template>\n  <a-card class=\"mode-selection\" :bordered=\"false\">\n    <div class=\"mode-title\">\n      <h2><a-icon type=\"rocket\" /> 选择刷题模式</h2>\n      <p>根据您的需求选择最适合的刷题方式</p>\n      <!-- 题库数量标志 -->\n      <div class=\"question-bank-count\">\n        <span class=\"count-number\">{{questionBankCount}}</span>\n        <span class=\"count-label\">题库总量</span>\n      </div>\n    </div>\n    \n    <a-form :form=\"form\" layout=\"vertical\">\n      <a-row :gutter=\"24\">\n        <a-col :span=\"12\">\n          <a-form-item>\n            <!-- 科目选择 -->\n            <div class=\"section-title\">\n              <span class=\"title-icon\"><a-icon type=\"appstore\" /></span>\n              <span class=\"title-text\">科目选择</span>\n            </div>\n            <div class=\"subject-selection-cards\">\n              <div \n                class=\"subject-card\" \n                :class=\"{'subject-selected': queryParam.subject === 'Scratch'}\"\n                @click=\"handleSubjectSelect('Scratch')\">\n                <img src=\"@/assets/scratch.png\" alt=\"Scratch\" class=\"subject-icon\" />\n                <span class=\"subject-name\">Scratch</span>\n              </div>\n              <div \n                class=\"subject-card\" \n                :class=\"{'subject-selected': queryParam.subject === 'Python'}\"\n                @click=\"handleSubjectSelect('Python')\">\n                <img src=\"@/assets/python.png\" alt=\"Python\" class=\"subject-icon\" />\n                <span class=\"subject-name\">Python</span>\n              </div>\n              <div \n                class=\"subject-card\" \n                :class=\"{'subject-selected': queryParam.subject === 'C++'}\"\n                @click=\"handleSubjectSelect('C++')\">\n                <img src=\"@/assets/cpp.png\" alt=\"C++\" class=\"subject-icon\" />\n                <span class=\"subject-name\">C++</span>\n              </div>\n            </div>\n          </a-form-item>\n        </a-col>\n        <a-col :span=\"12\">\n          <a-form-item>\n            <!-- 级别选择 -->\n            <div class=\"section-title\">\n              <span class=\"title-icon\"><a-icon type=\"rise\" /></span>\n              <span class=\"title-text\">级别选择</span>\n            </div>\n            <a-select \n              v-model=\"queryParam.level\" \n              style=\"width: 100%\" \n              placeholder=\"请先选择科目\" \n              allowClear\n              :disabled=\"!queryParam.subject\"\n            >\n              <template v-if=\"queryParam.subject === 'Scratch'\">\n                <a-select-option v-for=\"i in 4\" :key=\"i\" :value=\"i\">{{ ['一', '二', '三', '四'][i-1] }}级</a-select-option>\n              </template>\n              <template v-else>\n                <a-select-option v-for=\"i in 8\" :key=\"i\" :value=\"i\">{{ ['一', '二', '三', '四', '五', '六', '七', '八'][i-1] }}级</a-select-option>\n              </template>\n            </a-select>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      \n      <a-row :gutter=\"24\">\n        <a-col :span=\"12\">\n          <a-form-item>\n            <!-- 题目类型 -->\n            <div class=\"section-title\">\n              <span class=\"title-icon\"><a-icon type=\"file-text\" /></span>\n              <span class=\"title-text\">题目类型</span>\n            </div>\n            <div class=\"custom-checkbox-group\">\n              <a-checkbox-group v-model=\"questionTypes\">\n                <a-checkbox value=\"1\"><a-icon type=\"check-square\" /> 单选题</a-checkbox>\n                <a-checkbox value=\"2\"><a-icon type=\"check-circle\" /> 判断题</a-checkbox>\n                <a-checkbox value=\"3\"><a-icon type=\"code\" /> 编程题</a-checkbox>\n              </a-checkbox-group>\n            </div>\n          </a-form-item>\n        </a-col>\n        <a-col :span=\"12\">\n          <a-form-item>\n            <!-- 难度范围 -->\n            <div class=\"section-title\">\n              <span class=\"title-icon\"><a-icon type=\"dashboard\" /></span>\n              <span class=\"title-text\">难度范围</span>\n            </div>\n            <div class=\"difficulty-selection\">\n              <div class=\"difficulty-labels\">\n                <span class=\"difficulty-value\">{{ difficultyFormatter(difficultyRange[0]) }}</span>\n                <span class=\"difficulty-divider\">至</span>\n                <span class=\"difficulty-value\">{{ difficultyFormatter(difficultyRange[1]) }}</span>\n              </div>\n              <a-row>\n                <a-col :span=\"11\">\n                  <a-slider\n                    v-model=\"difficultyRange[0]\"\n                    :min=\"1\"\n                    :max=\"3\"\n                    :marks=\"difficultyMarks\"\n                  />\n                </a-col>\n                <a-col :span=\"2\" style=\"text-align: center\">\n                  <span class=\"slider-divider\">~</span>\n                </a-col>\n                <a-col :span=\"11\">\n                  <a-slider\n                    v-model=\"difficultyRange[1]\"\n                    :min=\"1\"\n                    :max=\"3\"\n                    :marks=\"difficultyMarks\"\n                  />\n                </a-col>\n              </a-row>\n            </div>\n          </a-form-item>\n        </a-col>\n      </a-row>\n      \n      <a-form-item>\n        <!-- 刷题模式 -->\n        <div class=\"section-title\">\n          <span class=\"title-icon\"><a-icon type=\"play-circle\" /></span>\n          <span class=\"title-text\">刷题模式</span>\n        </div>\n        <div class=\"mode-cards\">\n          <div class=\"mode-card\" \n            :class=\"{'selected': practiseMode === 'count'}\" \n            @click=\"practiseMode = 'count'; handleModeChange('count')\">\n            <div class=\"mode-card-icon\">\n              <a-icon type=\"ordered-list\" />\n            </div>\n            <div class=\"mode-card-content\">\n              <h4>按题目数量</h4>\n              <p>限定题目数量刷题</p>\n            </div>\n          </div>\n          <div class=\"mode-card\" \n            :class=\"{'selected': practiseMode === 'time'}\"\n            @click=\"practiseMode = 'time'; handleModeChange('time')\">\n            <div class=\"mode-card-icon\">\n              <a-icon type=\"clock-circle\" />\n            </div>\n            <div class=\"mode-card-content\">\n              <h4>按时间限制</h4>\n              <p>限时答题模式</p>\n            </div>\n          </div>\n          <div class=\"mode-card\" \n            :class=\"{'selected': practiseMode === 'free'}\"\n            @click=\"practiseMode = 'free'; handleModeChange('free')\">\n            <div class=\"mode-card-icon\">\n              <a-icon type=\"unlock\" />\n            </div>\n            <div class=\"mode-card-content\">\n              <h4>自由模式</h4>\n              <p>无限制刷题</p>\n            </div>\n          </div>\n        </div>\n      </a-form-item>\n    \n      <a-form-item v-if=\"practiseMode === 'count'\">\n        <!-- 题目数量 -->\n        <div class=\"section-title\">\n          <span class=\"title-icon\"><a-icon type=\"ordered-list\" /></span>\n          <span class=\"title-text\">题目数量</span>\n        </div>\n        <a-input-number \n          v-model=\"practiseCount\" \n          :min=\"5\" \n          :max=\"100\" \n          style=\"width: 100%\"\n        />\n      </a-form-item>\n      \n      <a-form-item v-if=\"practiseMode === 'time'\">\n        <!-- 时间限制 -->\n        <div class=\"section-title\">\n          <span class=\"title-icon\"><a-icon type=\"clock-circle\" /></span>\n          <span class=\"title-text\">时间限制(分钟)</span>\n        </div>\n        <a-input-number \n          v-model=\"timeLimit\" \n          :min=\"5\" \n          :max=\"120\" \n          style=\"width: 100%\"\n        />\n      </a-form-item>\n      \n      <div class=\"practice-actions\">\n        <a-button type=\"primary\" size=\"large\" @click=\"startPractise\" :loading=\"loading\">\n          <a-icon type=\"play-circle\" /> 开始刷题\n        </a-button>\n        <a-button type=\"success\" size=\"large\" @click=\"startQuickPractise\" :loading=\"loading\">\n          <a-icon type=\"thunderbolt\" /> 快速刷题\n        </a-button>\n        <a-button v-if=\"hasSavedProgress\" type=\"primary\" size=\"large\" style=\"background-color: #722ed1; border-color: #722ed1;\" @click=\"continuePractise\" :loading=\"loading\">\n          <a-icon type=\"reload\" /> 继续上次答题\n        </a-button>\n        <a-button size=\"large\" @click=\"resetQuery\">\n          <a-icon type=\"reload\" /> 重置选项\n        </a-button>\n      </div>\n    </a-form>\n  </a-card>\n</template>\n\n<script>\nimport { getProblemList } from '@/api/examSystem'\n\nexport default {\n  name: 'PracticeModeSelector',\n  props: {\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    hasSavedProgress: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      form: {},\n      // 查询参数\n      queryParam: {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined,\n        difficulty: undefined\n      },\n      // 题库数量\n      questionBankCount: 0,\n      // 刷题设置\n      practiseCount: 10,\n      practiseMode: undefined,\n      questionTypes: ['1', '2', '3'], // 默认选择所有题型\n      difficultyRange: [1, 3], // 难度范围\n      timeLimit: 30, // 时间限制(分钟)\n      difficultyMarks: {\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n    }\n  },\n  created() {\n    // 获取题库数量\n    this.getQuestionBankCount()\n  },\n  methods: {\n    // 处理科目选择\n    handleSubjectSelect(subject) {\n      // 如果选择了不同的科目，则清空已选级别\n      if (this.queryParam.subject !== subject) {\n        this.queryParam.level = undefined\n      }\n      this.queryParam.subject = subject\n      this.$emit('subject-change', subject)\n    },\n    \n    // 处理刷题模式改变\n    handleModeChange(mode) {\n      if (mode === 'count') {\n        this.practiseCount = 10\n      } else if (mode === 'time') {\n        this.timeLimit = 30\n      }\n      this.$emit('mode-change', mode)\n    },\n    \n    // 格式化难度显示\n    difficultyFormatter(value) {\n      return this.difficultyMarks[value] || value\n    },\n    \n    // 获取题库数量\n    getQuestionBankCount() {\n      const params = {\n        pageNo: 1,\n        pageSize: 1\n      }\n      \n      getProblemList(params).then(res => {\n        if (res.success) {\n          this.questionBankCount = res.result.total || 0\n        } else {\n          this.questionBankCount = '?'\n        }\n      }).catch(() => {\n        this.questionBankCount = '?'\n      })\n    },\n    \n    // 开始刷题\n    startPractise() {\n      this.$emit('start-practise', this.getFormData())\n    },\n    \n    // 快速刷题\n    startQuickPractise() {\n      this.$emit('start-quick-practise', this.getFormData())\n    },\n    \n    // 继续上次答题\n    continuePractise() {\n      this.$emit('continue-practise')\n    },\n    \n    // 重置查询条件\n    resetQuery() {\n      this.$emit('reset-query')\n    },\n    \n    // 获取表单数据\n    getFormData() {\n      return {\n        queryParam: { ...this.queryParam },\n        practiseCount: this.practiseCount,\n        practiseMode: this.practiseMode,\n        questionTypes: [...this.questionTypes],\n        difficultyRange: [...this.difficultyRange],\n        timeLimit: this.timeLimit\n      }\n    },\n    \n    // 重置表单数据\n    resetFormData() {\n      this.queryParam = {\n        subject: undefined,\n        level: undefined,\n        questionType: undefined,\n        difficulty: undefined\n      }\n      this.practiseCount = 10\n      this.practiseMode = undefined\n      this.questionTypes = ['1', '2', '3']\n      this.difficultyRange = [1, 3]\n      this.timeLimit = 30\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.mode-selection {\n  margin-bottom: 12px;\n  background: linear-gradient(135deg, #fff, #f8f9ff);\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  .mode-title {\n    text-align: center;\n    margin-bottom: 24px;\n    position: relative;\n    padding-bottom: 16px;\n\n    &:after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 80px;\n      height: 3px;\n      background: linear-gradient(90deg, #1890ff, #52c41a);\n      border-radius: 3px;\n    }\n\n    h2 {\n      font-size: 32px;\n      margin-bottom: 12px;\n      font-weight: 600;\n      color: #1890ff;\n      text-shadow: 1px 1px 3px rgba(24, 144, 255, 0.2);\n      letter-spacing: 1px;\n\n      .anticon {\n        color: #1890ff;\n        margin-right: 8px;\n        font-size: 28px;\n        vertical-align: middle;\n        animation: shake 2s infinite;\n      }\n\n      @media screen and (max-width: 768px) {\n        font-size: 22px;\n      }\n    }\n\n    p {\n      color: #8c8c8c;\n      font-size: 16px;\n    }\n  }\n\n  // 题库数量标志样式\n  .question-bank-count {\n    position: absolute;\n    top: 10px;\n    right: 20px;\n    background: #e6f7ff;\n    border: 2px solid #91d5ff;\n    border-radius: 8px;\n    padding: 8px 16px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n    z-index: 1;\n\n    .count-number {\n      font-size: 28px;\n      font-weight: 700;\n      color: #1890ff;\n      line-height: 1.2;\n      &:after {\n        content: ' 题';\n        font-size: 16px;\n        font-weight: 400;\n      }\n    }\n\n    .count-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #096dd9;\n      margin-top: 2px;\n    }\n\n    @media screen and (max-width: 768px) {\n      top: 5px;\n      right: 10px;\n      padding: 6px 12px;\n\n      .count-number {\n        font-size: 20px;\n        &:after {\n          font-size: 14px;\n        }\n      }\n\n      .count-label {\n        font-size: 12px;\n      }\n    }\n  }\n\n  // 小标题样式\n  .section-title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 12px;\n    position: relative;\n    padding-left: 10px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 2px;\n      bottom: 2px;\n      width: 3px;\n      background: linear-gradient(to bottom, #1890ff, #40a9ff);\n      border-radius: 3px;\n    }\n\n    .title-icon {\n      margin-right: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 24px;\n      height: 24px;\n      background: #e6f7ff;\n      border-radius: 4px;\n\n      .anticon {\n        color: #1890ff;\n        font-size: 14px;\n      }\n    }\n\n    .title-text {\n      font-size: 16px;\n      font-weight: 600;\n      color: #262626;\n      letter-spacing: 0.5px;\n    }\n\n    @media screen and (max-width: 768px) {\n      margin-bottom: 8px;\n\n      .title-icon {\n        width: 20px;\n        height: 20px;\n\n        .anticon {\n          font-size: 12px;\n        }\n      }\n\n      .title-text {\n        font-size: 14px;\n      }\n    }\n  }\n\n  // 科目选择卡片样式\n  .subject-selection-cards {\n    display: flex;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .subject-card {\n      flex: 1;\n      background: #f9f9f9;\n      border: 2px solid #f0f0f0;\n      border-radius: 10px;\n      padding: 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n\n      &:hover {\n        border-color: #91d5ff;\n        background: #f0f7ff;\n      }\n\n      &.subject-selected {\n        border-color: #1890ff;\n        background: #e6f7ff;\n        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n      }\n\n      .subject-icon {\n        width: 48px;\n        height: 48px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #fff;\n        border-radius: 50%;\n        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);\n        margin-right: 12px;\n      }\n\n      .subject-name {\n        font-size: 16px;\n        font-weight: 600;\n        color: #262626;\n      }\n\n      @media screen and (max-width: 768px) {\n        margin-bottom: 10px;\n      }\n    }\n  }\n\n  // 复选框组样式\n  .custom-checkbox-group {\n    .ant-checkbox-wrapper {\n      margin-right: 20px;\n\n      .anticon {\n        margin-right: 4px;\n        color: #1890ff;\n      }\n    }\n  }\n\n  // 难度选择样式\n  .difficulty-selection {\n    .difficulty-labels {\n      display: flex;\n      justify-content: center;\n      margin-bottom: 10px;\n\n      .difficulty-value {\n        background: #f0f7ff;\n        padding: 4px 16px;\n        border-radius: 16px;\n        font-weight: 500;\n        color: #1890ff;\n      }\n\n      .difficulty-divider {\n        margin: 0 16px;\n        color: #8c8c8c;\n        display: flex;\n        align-items: center;\n      }\n    }\n\n    .slider-divider {\n      display: flex;\n      height: 100%;\n      align-items: center;\n      justify-content: center;\n      color: #8c8c8c;\n    }\n  }\n\n  // 模式卡片样式\n  .mode-cards {\n    display: flex;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    @media screen and (max-width: 768px) {\n      flex-direction: column;\n    }\n\n    .mode-card {\n      flex: 1;\n      background: #f9f9f9;\n      border: 2px solid #f0f0f0;\n      border-radius: 10px;\n      padding: 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n\n      &:hover {\n        border-color: #91d5ff;\n        background: #f0f7ff;\n      }\n\n      &.selected {\n        border-color: #1890ff;\n        background: #e6f7ff;\n        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n      }\n\n      .mode-card-icon {\n        width: 48px;\n        height: 48px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #fff;\n        border-radius: 50%;\n        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);\n        margin-right: 12px;\n\n        .anticon {\n          font-size: 24px;\n          color: #1890ff;\n        }\n      }\n\n      .mode-card-content {\n        h4 {\n          font-size: 16px;\n          font-weight: 600;\n          margin-bottom: 4px;\n          color: #262626;\n        }\n\n        p {\n          font-size: 14px;\n          color: #8c8c8c;\n          margin: 0;\n        }\n      }\n\n      @media screen and (max-width: 768px) {\n        margin-bottom: 10px;\n      }\n    }\n  }\n\n  // 操作按钮样式\n  .practice-actions {\n    text-align: center;\n    margin-top: 24px;\n\n    button {\n      height: 48px;\n      padding: 0 24px;\n      border-radius: 24px;\n      font-size: 16px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n      margin: 0 8px;\n\n      &.ant-btn-primary {\n        background: linear-gradient(135deg, #1890ff, #096dd9);\n        border: none;\n        box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);\n\n        &:hover {\n          background: linear-gradient(135deg, #40a9ff, #1890ff);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);\n        }\n      }\n\n      &.ant-btn-success {\n        background: linear-gradient(135deg, #52c41a, #389e0d);\n        border: none;\n        color: #fff;\n        box-shadow: 0 5px 15px rgba(82, 196, 26, 0.3);\n\n        &:hover {\n          background: linear-gradient(135deg, #73d13d, #52c41a);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 20px rgba(82, 196, 26, 0.4);\n        }\n      }\n\n      &:not(.ant-btn-primary):not(.ant-btn-success) {\n        background: #fff;\n        border: 1px solid #d9d9d9;\n\n        &:hover {\n          border-color: #40a9ff;\n          color: #40a9ff;\n          transform: translateY(-2px);\n        }\n      }\n\n      .anticon {\n        font-size: 18px;\n      }\n\n      @media screen and (max-width: 768px) {\n        height: 36px;\n        padding: 0 12px;\n        font-size: 14px;\n        margin: 0 4px;\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes shake {\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70%, 90% {\n    transform: translateX(-2px);\n  }\n  20%, 40%, 60%, 80% {\n    transform: translateX(2px);\n  }\n}\n</style>\n"]}]}