{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JSelectMultiple.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["//option {label:,value:}\nexport default {\n  name: 'JSelectMultiple',\n  props: {\n    placeholder: {\n      type: String,\n      default: '',\n      required: false\n    },\n    value: {\n      type: String,\n      required: false\n    },\n    readOnly: {\n      type: Boolean,\n      required: false,\n      default: false\n    },\n    options: {\n      type: Array,\n      required: true\n    },\n    triggerChange: {\n      type: Boolean,\n      required: false,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      arrayValue: !this.value ? [] : this.value.split(\",\")\n    };\n  },\n  watch: {\n    value: function value(val) {\n      if (!val) {\n        this.arrayValue = [];\n      } else {\n        this.arrayValue = this.value.split(\",\");\n      }\n    }\n  },\n  methods: {\n    onChange: function onChange(selectedValue) {\n      if (this.triggerChange) {\n        this.$emit('change', selectedValue.join(\",\"));\n      } else {\n        this.$emit('input', selectedValue.join(\",\"));\n      }\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "placeholder", "type", "String", "default", "required", "value", "readOnly", "Boolean", "options", "Array", "trigger<PERSON>hange", "data", "arrayValue", "split", "watch", "val", "methods", "onChange", "selected<PERSON><PERSON><PERSON>", "$emit", "join"], "sources": ["src/components/jeecg/JSelectMultiple.vue"], "sourcesContent": ["<template>\n  <a-select :value=\"arrayValue\" @change=\"onChange\" mode=\"multiple\" :placeholder=\"placeholder\">\n    <a-select-option\n      v-for=\"(item,index) in options\"\n      :key=\"index\"\n      :value=\"item.value\">\n      {{ item.text || item.label }}\n    </a-select-option>\n  </a-select>\n</template>\n\n<script>\n  //option {label:,value:}\n  export default {\n    name: 'JSelectMultiple',\n    props: {\n      placeholder:{\n        type: String,\n        default:'',\n        required: false\n      },\n      value:{\n        type: String,\n        required: false\n      },\n      readOnly:{\n        type: Boolean,\n        required: false,\n        default: false\n      },\n      options:{\n        type: Array,\n        required: true\n      },\n      triggerChange:{\n        type: Boolean,\n        required: false,\n        default: false\n      }\n    },\n    data(){\n      return {\n        arrayValue:!this.value?[]:this.value.split(\",\")\n      }\n    },\n    watch:{\n      value (val) {\n        if(!val){\n          this.arrayValue = []\n        }else{\n          this.arrayValue = this.value.split(\",\")\n        }\n      }\n    },\n    methods:{\n      onChange (selectedValue) {\n        if(this.triggerChange){\n          this.$emit('change', selectedValue.join(\",\"));\n        }else{\n          this.$emit('input', selectedValue.join(\",\"));\n        }\n      },\n    },\n\n  }\n</script>\n"], "mappings": "AAYA;AACA;EACAA,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,QAAA;IACA;IACAE,QAAA;MACAL,IAAA,EAAAM,OAAA;MACAH,QAAA;MACAD,OAAA;IACA;IACAK,OAAA;MACAP,IAAA,EAAAQ,KAAA;MACAL,QAAA;IACA;IACAM,aAAA;MACAT,IAAA,EAAAM,OAAA;MACAH,QAAA;MACAD,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA,QAAAP,KAAA,aAAAA,KAAA,CAAAQ,KAAA;IACA;EACA;EACAC,KAAA;IACAT,KAAA,WAAAA,MAAAU,GAAA;MACA,KAAAA,GAAA;QACA,KAAAH,UAAA;MACA;QACA,KAAAA,UAAA,QAAAP,KAAA,CAAAQ,KAAA;MACA;IACA;EACA;EACAG,OAAA;IACAC,QAAA,WAAAA,SAAAC,aAAA;MACA,SAAAR,aAAA;QACA,KAAAS,KAAA,WAAAD,aAAA,CAAAE,IAAA;MACA;QACA,KAAAD,KAAA,UAAAD,aAAA,CAAAE,IAAA;MACA;IACA;EACA;AAEA", "ignoreList": []}]}