{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Notification.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Notification.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default {\n  data: function data() {\n    return {\n      data: []\n    };\n  },\n  methods: {}\n};", {"version": 3, "names": ["data", "methods"], "sources": ["src/views/account/settings/Notification.vue"], "sourcesContent": ["<template>\n  <a-list\n    itemLayout=\"horizontal\"\n    :dataSource=\"data\"\n  >\n\n  </a-list>\n</template>\n\n<script>\n  export default {\n    data () {\n      return {\n        data: []\n      }\n    },\n    methods: {\n\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>"], "mappings": "AAUA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;IACA;EACA;EACAC,OAAA,GAEA;AACA", "ignoreList": []}]}