{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\form\\JeecgOrderDMainModal.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { httpAction } from '@/api/manage';\nimport JDate from '@/components/jeecg/JDate';\nimport pick from 'lodash.pick';\nimport moment from \"moment\";\nexport default {\n  name: \"JeecgOrderDMainModal\",\n  components: {\n    JDate: JDate\n  },\n  data: function data() {\n    return {\n      title: \"操作\",\n      visible: false,\n      orderMainModel: {\n        jeecgOrderCustomerList: [{}],\n        jeecgOrderTicketList: [{}]\n      },\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      confirmLoading: false,\n      form: this.$form.createForm(this),\n      validatorRules: {},\n      url: {\n        add: \"/test/order/add\",\n        edit: \"/test/order/edit\",\n        orderCustomerList: \"/test/order/listOrderCustomerByMainId\",\n        orderTicketList: \"/test/order/listOrderTicketByMainId\"\n      }\n    };\n  },\n  methods: {\n    add: function add() {\n      this.edit({});\n    },\n    edit: function edit(record) {\n      var _this = this;\n      this.form.resetFields();\n      this.orderMainModel = Object.assign({}, record);\n      //初始化明细表数据\n      console.log(this.orderMainModel.id);\n      this.visible = true;\n      this.$nextTick(function () {\n        _this.form.setFieldsValue(pick(_this.orderMainModel, 'orderCode', 'ctype', 'orderMoney', 'content'));\n        _this.form.setFieldsValue({\n          orderDate: _this.orderMainModel.orderDate ? moment(_this.orderMainModel.orderDate) : null\n        }); //时间格式化\n      });\n      console.log(this.orderMainModel);\n    },\n    close: function close() {\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      var _this2 = this;\n      var that = this;\n      // 触发表单验证\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          that.confirmLoading = true;\n          var httpurl = '';\n          var method = '';\n          if (!_this2.orderMainModel.id) {\n            httpurl += _this2.url.add;\n            method = 'post';\n          } else {\n            httpurl += _this2.url.edit;\n            method = 'put';\n          }\n          var orderMainData = Object.assign(_this2.orderMainModel, values);\n          //时间格式化\n          orderMainData.orderDate = orderMainData.orderDate ? orderMainData.orderDate.format('YYYY-MM-DD HH:mm:ss') : null;\n          var formData = _objectSpread({}, orderMainData);\n          console.log(formData);\n          httpAction(httpurl, formData, method).then(function (res) {\n            if (res.success) {\n              that.$message.success(res.message);\n              that.$emit('ok');\n            } else {\n              that.$message.warning(res.message);\n            }\n          }).finally(function () {\n            that.confirmLoading = false;\n            that.close();\n          });\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    }\n  }\n};", {"version": 3, "names": ["httpAction", "JDate", "pick", "moment", "name", "components", "data", "title", "visible", "orderMainModel", "jeecgOrderCustomerList", "jeecgOrderTicketList", "labelCol", "xs", "span", "sm", "wrapperCol", "confirmLoading", "form", "$form", "createForm", "validatorRules", "url", "add", "edit", "orderCustomerList", "orderTicketList", "methods", "record", "_this", "resetFields", "Object", "assign", "console", "log", "id", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orderDate", "close", "$emit", "handleOk", "_this2", "that", "validateFields", "err", "values", "httpurl", "method", "orderMainData", "format", "formData", "_objectSpread", "then", "res", "success", "$message", "message", "warning", "finally", "handleCancel"], "sources": ["src/views/jeecg/tablist/form/JeecgOrderDMainModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :title=\"title\"\n    :width=\"1000\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\">\n\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form :form=\"form\">\n        <!-- 主表单区域 -->\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单号\"\n          hasFeedback>\n          <a-input\n            placeholder=\"请输入订单号\"\n            v-decorator=\"['orderCode', {rules: [{ required: true, message: '请输入订单号!' }]}]\"\n          />\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单类型\">\n          <a-select placeholder=\"请输入订单类型\" v-decorator=\"['ctype',{}]\">\n            <a-select-option value=\"1\">国内订单</a-select-option>\n            <a-select-option value=\"2\">国际订单</a-select-option>\n          </a-select>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单日期\">\n          <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator=\"[ 'orderDate',{}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单金额\">\n          <a-input-number style=\"width: 200px\" v-decorator=\"[ 'orderMoney', {}]\"/>\n        </a-form-item>\n        <a-form-item\n          :labelCol=\"labelCol\"\n          :wrapperCol=\"wrapperCol\"\n          label=\"订单备注\">\n          <a-input placeholder=\"请输入订单备注\" v-decorator=\"['content', {}]\"/>\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\n  import {httpAction} from '@/api/manage'\n  import JDate from '@/components/jeecg/JDate'\n  import pick from 'lodash.pick'\n  import moment from \"moment\"\n\n  export default {\n    name: \"JeecgOrderDMainModal\",\n    components: {\n      JDate\n    },\n    data() {\n      return {\n        title: \"操作\",\n        visible: false,\n        orderMainModel: {\n          jeecgOrderCustomerList: [{}],\n          jeecgOrderTicketList: [{}]\n        },\n        labelCol: {\n          xs: {span: 24},\n          sm: {span: 5},\n        },\n        wrapperCol: {\n          xs: {span: 24},\n          sm: {span: 16},\n        },\n        confirmLoading: false,\n        form: this.$form.createForm(this),\n        validatorRules: {},\n        url: {\n          add: \"/test/order/add\",\n          edit: \"/test/order/edit\",\n          orderCustomerList: \"/test/order/listOrderCustomerByMainId\",\n          orderTicketList: \"/test/order/listOrderTicketByMainId\",\n        },\n      }\n    },\n    methods: {\n      add() {\n        this.edit({});\n      },\n      edit(record) {\n        this.form.resetFields();\n        this.orderMainModel = Object.assign({}, record);\n        //初始化明细表数据\n        console.log(this.orderMainModel.id)\n        this.visible = true;\n        this.$nextTick(() => {\n          this.form.setFieldsValue(pick(this.orderMainModel, 'orderCode', 'ctype', 'orderMoney', 'content'))\n          this.form.setFieldsValue({orderDate: this.orderMainModel.orderDate ? moment(this.orderMainModel.orderDate) : null}) //时间格式化\n        });\n        console.log(this.orderMainModel)\n      },\n      close() {\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk() {\n        const that = this;\n        // 触发表单验证\n        this.form.validateFields((err, values) => {\n          if (!err) {\n            that.confirmLoading = true;\n            let httpurl = '';\n            let method = '';\n            if (!this.orderMainModel.id) {\n              httpurl += this.url.add;\n              method = 'post';\n            } else {\n              httpurl += this.url.edit;\n              method = 'put';\n            }\n            let orderMainData = Object.assign(this.orderMainModel, values);\n            //时间格式化\n            orderMainData.orderDate = orderMainData.orderDate ? orderMainData.orderDate.format('YYYY-MM-DD HH:mm:ss') : null;\n            let formData = {\n              ...orderMainData\n            }\n\n            console.log(formData)\n            httpAction(httpurl, formData, method).then((res) => {\n              if (res.success) {\n                that.$message.success(res.message);\n                that.$emit('ok');\n              } else {\n                that.$message.warning(res.message);\n              }\n            }).finally(() => {\n              that.confirmLoading = false;\n              that.close();\n            })\n          }\n        })\n      },\n      handleCancel() {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .ant-btn {\n    padding: 0 10px;\n    margin-left: 3px;\n  }\n\n  .ant-form-item-control {\n    line-height: 0px;\n  }\n\n  /** 主表单行间距 */\n  .ant-form .ant-form-item {\n    margin-bottom: 10px;\n  }\n\n  /** Tab页面行间距 */\n  .ant-tabs-content .ant-form-item {\n    margin-bottom: 0px;\n  }\n</style>"], "mappings": ";;;;;;AAuDA,SAAAA,UAAA;AACA,OAAAC,KAAA;AACA,OAAAC,IAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,KAAA,EAAAA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,cAAA;QACAC,sBAAA;QACAC,oBAAA;MACA;MACAC,QAAA;QACAC,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAE,UAAA;QACAH,EAAA;UAAAC,IAAA;QAAA;QACAC,EAAA;UAAAD,IAAA;QAAA;MACA;MACAG,cAAA;MACAC,IAAA,OAAAC,KAAA,CAAAC,UAAA;MACAC,cAAA;MACAC,GAAA;QACAC,GAAA;QACAC,IAAA;QACAC,iBAAA;QACAC,eAAA;MACA;IACA;EACA;EACAC,OAAA;IACAJ,GAAA,WAAAA,IAAA;MACA,KAAAC,IAAA;IACA;IACAA,IAAA,WAAAA,KAAAI,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAX,IAAA,CAAAY,WAAA;MACA,KAAArB,cAAA,GAAAsB,MAAA,CAAAC,MAAA,KAAAJ,MAAA;MACA;MACAK,OAAA,CAAAC,GAAA,MAAAzB,cAAA,CAAA0B,EAAA;MACA,KAAA3B,OAAA;MACA,KAAA4B,SAAA;QACAP,KAAA,CAAAX,IAAA,CAAAmB,cAAA,CAAAnC,IAAA,CAAA2B,KAAA,CAAApB,cAAA;QACAoB,KAAA,CAAAX,IAAA,CAAAmB,cAAA;UAAAC,SAAA,EAAAT,KAAA,CAAApB,cAAA,CAAA6B,SAAA,GAAAnC,MAAA,CAAA0B,KAAA,CAAApB,cAAA,CAAA6B,SAAA;QAAA;MACA;MACAL,OAAA,CAAAC,GAAA,MAAAzB,cAAA;IACA;IACA8B,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;MACA,KAAAhC,OAAA;IACA;IACAiC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA;MACA;MACA,KAAAzB,IAAA,CAAA0B,cAAA,WAAAC,GAAA,EAAAC,MAAA;QACA,KAAAD,GAAA;UACAF,IAAA,CAAA1B,cAAA;UACA,IAAA8B,OAAA;UACA,IAAAC,MAAA;UACA,KAAAN,MAAA,CAAAjC,cAAA,CAAA0B,EAAA;YACAY,OAAA,IAAAL,MAAA,CAAApB,GAAA,CAAAC,GAAA;YACAyB,MAAA;UACA;YACAD,OAAA,IAAAL,MAAA,CAAApB,GAAA,CAAAE,IAAA;YACAwB,MAAA;UACA;UACA,IAAAC,aAAA,GAAAlB,MAAA,CAAAC,MAAA,CAAAU,MAAA,CAAAjC,cAAA,EAAAqC,MAAA;UACA;UACAG,aAAA,CAAAX,SAAA,GAAAW,aAAA,CAAAX,SAAA,GAAAW,aAAA,CAAAX,SAAA,CAAAY,MAAA;UACA,IAAAC,QAAA,GAAAC,aAAA,KACAH,aAAA,CACA;UAEAhB,OAAA,CAAAC,GAAA,CAAAiB,QAAA;UACAnD,UAAA,CAAA+C,OAAA,EAAAI,QAAA,EAAAH,MAAA,EAAAK,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAZ,IAAA,CAAAa,QAAA,CAAAD,OAAA,CAAAD,GAAA,CAAAG,OAAA;cACAd,IAAA,CAAAH,KAAA;YACA;cACAG,IAAA,CAAAa,QAAA,CAAAE,OAAA,CAAAJ,GAAA,CAAAG,OAAA;YACA;UACA,GAAAE,OAAA;YACAhB,IAAA,CAAA1B,cAAA;YACA0B,IAAA,CAAAJ,KAAA;UACA;QACA;MACA;IACA;IACAqB,YAAA,WAAAA,aAAA;MACA,KAAArB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}