{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderTicketList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\jeecg\\tablist\\JeecgOrderTicketList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import JeecgOrderTicketModal from './form/JeecgOrderTicketModal';\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nimport { getAction } from '@/api/manage';\nexport default {\n  name: \"JeecgOrderTicketList\",\n  mixins: [JeecgListMixin],\n  components: {\n    JeecgOrderTicketModal: JeecgOrderTicketModal\n  },\n  data: function data() {\n    return {\n      description: '机票信息',\n      // 表头\n      columns: [{\n        title: '航班号',\n        align: \"center\",\n        dataIndex: 'ticketCode'\n      }, {\n        title: '航班时间',\n        align: \"center\",\n        dataIndex: 'tickectDate'\n      }, {\n        title: '订单号码',\n        align: \"center\",\n        dataIndex: 'orderId'\n      }, {\n        title: '创建人',\n        align: \"center\",\n        dataIndex: 'createBy'\n      }, {\n        title: '创建时间',\n        align: \"center\",\n        dataIndex: 'createTime',\n        sorter: true\n      }, {\n        title: '操作',\n        key: 'operation',\n        align: \"center\",\n        width: 130,\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      url: {\n        list: \"/test/order/listOrderTicketByMainId\",\n        delete: \"/test/order/deleteTicket\",\n        deleteBatch: \"/test/order/deleteBatchTicket\"\n      }\n    };\n  },\n  methods: {\n    loadData: function loadData(arg) {\n      var _this = this;\n      if (arg === 1) {\n        this.ipagination.current = 1;\n      }\n      var params = this.getQueryParams();\n      //update-begin--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页--------------------\n      getAction(this.url.list, {\n        orderId: params.mainId,\n        pageNo: this.ipagination.current,\n        pageSize: this.ipagination.pageSize\n      }).then(function (res) {\n        if (res.success) {\n          _this.dataSource = res.result.records;\n          _this.ipagination.total = res.result.total;\n        } else {\n          _this.dataSource = null;\n        }\n      });\n      //update-end--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页--------------------\n    },\n    getOrderMain: function getOrderMain(orderId) {\n      this.queryParam.mainId = orderId;\n      this.loadData(1);\n    },\n    handleAdd: function handleAdd() {\n      this.$refs.modalForm.add(this.queryParam.mainId);\n      this.$refs.modalForm.title = \"添加机票信息\";\n    }\n  }\n};", {"version": 3, "names": ["JeecgOrderTicketModal", "JeecgListMixin", "getAction", "name", "mixins", "components", "data", "description", "columns", "title", "align", "dataIndex", "sorter", "key", "width", "scopedSlots", "customRender", "url", "list", "delete", "deleteBatch", "methods", "loadData", "arg", "_this", "ipagination", "current", "params", "getQueryParams", "orderId", "mainId", "pageNo", "pageSize", "then", "res", "success", "dataSource", "result", "records", "total", "getOrderMain", "queryParam", "handleAdd", "$refs", "modalForm", "add"], "sources": ["src/views/jeecg/tablist/JeecgOrderTicketList.vue"], "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\" :md=\"24\" :sm=\"24\" style=\"margin: -25px 0px 10px 2px\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\"/>\n            删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作\n          <a-icon type=\"down\"/>\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n          <a-divider type=\"vertical\"/>\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">\n              更多 <a-icon type=\"down\"/>\n            </a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a href=\"javascript:;\" @click=\"handleDetail(record)\">详情</a>\n              </a-menu-item>\n\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <!-- 表单区域 -->\n    <JeecgOrderTicket-modal ref=\"modalForm\" @ok=\"modalFormOk\"></JeecgOrderTicket-modal>\n  </a-card>\n</template>\n\n<script>\n  import JeecgOrderTicketModal from './form/JeecgOrderTicketModal'\n  import {JeecgListMixin} from '@/mixins/JeecgListMixin'\n  import {getAction} from '@/api/manage'\n\n  export default {\n    name: \"JeecgOrderTicketList\",\n    mixins: [JeecgListMixin],\n    components: {\n      JeecgOrderTicketModal,\n    },\n    data() {\n      return {\n        description: '机票信息',\n        // 表头\n        columns: [{\n          title: '航班号',\n          align: \"center\",\n          dataIndex: 'ticketCode'\n        }, {\n          title: '航班时间',\n          align: \"center\",\n          dataIndex: 'tickectDate'\n        }, {\n          title: '订单号码',\n          align: \"center\",\n          dataIndex: 'orderId',\n        }, {\n          title: '创建人',\n          align: \"center\",\n          dataIndex: 'createBy'\n        }, {\n          title: '创建时间',\n          align: \"center\",\n          dataIndex: 'createTime',\n          sorter: true\n        }, {\n          title: '操作',\n          key: 'operation',\n          align: \"center\",\n          width: 130,\n          scopedSlots: {customRender: 'action'},\n        }],\n        url: {\n          list: \"/test/order/listOrderTicketByMainId\",\n          delete: \"/test/order/deleteTicket\",\n          deleteBatch: \"/test/order/deleteBatchTicket\",\n        }\n      }\n    },\n    methods: {\n      loadData(arg) {\n        if (arg === 1) {\n          this.ipagination.current = 1;\n        }\n        var params = this.getQueryParams();\n        //update-begin--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页--------------------\n        getAction(this.url.list, {orderId: params.mainId ,pageNo : this.ipagination.current,\n          pageSize :this.ipagination.pageSize}).then((res) => {\n          if (res.success) {\n            this.dataSource = res.result.records;\n            this.ipagination.total = res.result.total;\n          } else {\n            this.dataSource = null;\n          }\n        })\n        //update-end--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页--------------------\n      },\n      getOrderMain(orderId) {\n        this.queryParam.mainId = orderId;\n        this.loadData(1);\n      },\n      handleAdd: function () {\n        this.$refs.modalForm.add(this.queryParam.mainId);\n        this.$refs.modalForm.title = \"添加机票信息\";\n      },\n    }\n  }\n</script>\n<style scoped>\n  .ant-card {\n    margin-left: -30px;\n    margin-right: -30px;\n  }\n</style>"], "mappings": "AAsEA,OAAAA,qBAAA;AACA,SAAAC,cAAA;AACA,SAAAC,SAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAH,cAAA;EACAI,UAAA;IACAL,qBAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;MACA;QACAF,KAAA;QACAC,KAAA;QACAC,SAAA;QACAC,MAAA;MACA;QACAH,KAAA;QACAI,GAAA;QACAH,KAAA;QACAI,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA;MACAC,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,WAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,GAAA;QACA,KAAAE,WAAA,CAAAC,OAAA;MACA;MACA,IAAAC,MAAA,QAAAC,cAAA;MACA;MACA1B,SAAA,MAAAe,GAAA,CAAAC,IAAA;QAAAW,OAAA,EAAAF,MAAA,CAAAG,MAAA;QAAAC,MAAA,OAAAN,WAAA,CAAAC,OAAA;QACAM,QAAA,OAAAP,WAAA,CAAAO;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,OAAA;UACAX,KAAA,CAAAY,UAAA,GAAAF,GAAA,CAAAG,MAAA,CAAAC,OAAA;UACAd,KAAA,CAAAC,WAAA,CAAAc,KAAA,GAAAL,GAAA,CAAAG,MAAA,CAAAE,KAAA;QACA;UACAf,KAAA,CAAAY,UAAA;QACA;MACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAAX,OAAA;MACA,KAAAY,UAAA,CAAAX,MAAA,GAAAD,OAAA;MACA,KAAAP,QAAA;IACA;IACAoB,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,GAAA,MAAAJ,UAAA,CAAAX,MAAA;MACA,KAAAa,KAAA,CAAAC,SAAA,CAAAnC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}