{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue?vue&type=template&id=64e22abc&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue", "mtime": 1749742934824}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"confirm-dialog\">\n  <div class=\"confirm-content\">\n    <div class=\"confirm-header\">\n      <div class=\"confirm-icon\">❓</div>\n      <h3>确认</h3>\n    </div>\n    <div class=\"confirm-body\">\n      <p>{{ message }}</p>\n      <div class=\"coin-info\">\n        <span class=\"coin-icon\"></span>\n        <span>{{ gameCost }} 金币</span>\n      </div>\n    </div>\n    <div class=\"confirm-footer\">\n      <button class=\"confirm-button\" @click=\"$emit('confirm')\">确 定</button>\n      <button class=\"cancel-button\" @click=\"$emit('cancel')\">取 消</button>\n    </div>\n  </div>\n</div>\n", null]}