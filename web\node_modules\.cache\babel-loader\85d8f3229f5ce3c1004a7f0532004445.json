{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\chart\\mixins\\ChartMixins.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\chart\\mixins\\ChartMixins.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["export var ChartEventMixins = {\n  methods: {\n    handleClick: function handleClick(event, chart) {\n      this.handleEvent('click', event, chart);\n    },\n    handleEvent: function handleEvent(eventName, event, chart) {\n      this.$emit(eventName, event, chart);\n    }\n  }\n};", {"version": 3, "names": ["ChartEventMixins", "methods", "handleClick", "event", "chart", "handleEvent", "eventName", "$emit"], "sources": ["E:/teachingproject/teaching/web/src/components/chart/mixins/ChartMixins.js"], "sourcesContent": ["export const ChartEventMixins = {\n  methods: {\n    handleClick(event, chart) {\n      this.handleEvent('click', event, chart)\n    },\n    handleEvent(eventName, event, chart) {\n      this.$emit(eventName, event, chart)\n    },\n  }\n}"], "mappings": "AAAA,OAAO,IAAMA,gBAAgB,GAAG;EAC9BC,OAAO,EAAE;IACPC,WAAW,WAAAA,YAACC,KAAK,EAAEC,KAAK,EAAE;MACxB,IAAI,CAACC,WAAW,CAAC,OAAO,EAAEF,KAAK,EAAEC,KAAK,CAAC;IACzC,CAAC;IACDC,WAAW,WAAAA,YAACC,SAAS,EAAEH,KAAK,EAAEC,KAAK,EAAE;MACnC,IAAI,CAACG,KAAK,CAACD,SAAS,EAAEH,KAAK,EAAEC,KAAK,CAAC;IACrC;EACF;AACF,CAAC", "ignoreList": []}]}