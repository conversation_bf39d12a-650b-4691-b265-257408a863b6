{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\list\\StandardList.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import HeadInfo from '@/components/tools/HeadInfo';\nvar _data = [];\n_data.push({\n  title: 'Alipay',\n  avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n  description: '那是一种内在的东西， 他们到达不了，也无法触及的',\n  owner: '付晓晓',\n  startAt: '2018-07-26 22:44',\n  progress: {\n    value: 90\n  }\n});\n_data.push({\n  title: 'Angular',\n  avatar: 'https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png',\n  description: '希望是一个好东西，也许是最好的，好东西是不会消亡的',\n  owner: '曲丽丽',\n  startAt: '2018-07-26 22:44',\n  progress: {\n    value: 54\n  }\n});\n_data.push({\n  title: 'Ant Design',\n  avatar: 'https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png',\n  description: '生命就像一盒巧克力，结果往往出人意料',\n  owner: '林东东',\n  startAt: '2018-07-26 22:44',\n  progress: {\n    value: 66\n  }\n});\n_data.push({\n  title: 'Ant Design Pro',\n  avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png',\n  description: '城镇中有那么多的酒馆，她却偏偏走进了我的酒馆',\n  owner: '周星星',\n  startAt: '2018-07-26 22:44',\n  progress: {\n    value: 30\n  }\n});\n_data.push({\n  title: 'Bootstrap',\n  avatar: 'https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png',\n  description: '那时候我只会想自己想要什么，从不想自己拥有什么',\n  owner: '吴加好',\n  startAt: '2018-07-26 22:44',\n  progress: {\n    status: 'exception',\n    value: 100\n  }\n});\nexport default {\n  name: \"StandardList\",\n  components: {\n    HeadInfo: HeadInfo\n  },\n  data: function data() {\n    return {\n      data: _data\n    };\n  }\n};", {"version": 3, "names": ["HeadInfo", "data", "push", "title", "avatar", "description", "owner", "startAt", "progress", "value", "status", "name", "components"], "sources": ["src/views/list/StandardList.vue"], "sourcesContent": ["<template>\n  <div>\n    <a-card :bordered=\"false\">\n      <a-row>\n        <a-col :sm=\"8\" :xs=\"24\">\n          <head-info title=\"我的待办\" content=\"8个任务\" :bordered=\"true\"/>\n        </a-col>\n        <a-col :sm=\"8\" :xs=\"24\">\n          <head-info title=\"本周任务平均处理时间\" content=\"32分钟\" :bordered=\"true\"/>\n        </a-col>\n        <a-col :sm=\"8\" :xs=\"24\">\n          <head-info title=\"本周完成任务数\" content=\"24个\"/>\n        </a-col>\n      </a-row>\n    </a-card>\n\n    <a-card\n      style=\"margin-top: 24px\"\n      :bordered=\"false\"\n      title=\"标准列表\">\n\n      <div slot=\"extra\">\n        <a-radio-group>\n          <a-radio-button>全部</a-radio-button>\n          <a-radio-button>进行中</a-radio-button>\n          <a-radio-button>等待中</a-radio-button>\n        </a-radio-group>\n        <a-input-search style=\"margin-left: 16px; width: 272px;\" />\n      </div>\n\n      <div class=\"operate\">\n        <a-button type=\"dashed\" style=\"width: 100%\" icon=\"plus\">添加</a-button>\n      </div>\n\n      <a-list size=\"large\" :pagination=\"{showSizeChanger: true, showQuickJumper: true, pageSize: 5, total: 50}\">\n        <a-list-item :key=\"index\" v-for=\"(item, index) in data\">\n          <a-list-item-meta :description=\"item.description\">\n            <a-avatar slot=\"avatar\" size=\"large\" shape=\"square\" :src=\"item.avatar\"/>\n            <a slot=\"title\">{{ item.title }}</a>\n          </a-list-item-meta>\n          <div slot=\"actions\">\n            <a>编辑</a>\n          </div>\n          <div slot=\"actions\">\n            <a-dropdown>\n              <a-menu slot=\"overlay\">\n                <a-menu-item><a>编辑</a></a-menu-item>\n                <a-menu-item><a>删除</a></a-menu-item>\n              </a-menu>\n              <a>更多<a-icon type=\"down\"/></a>\n            </a-dropdown>\n          </div>\n          <div class=\"list-content\">\n            <div class=\"list-content-item\">\n              <span>Owner</span>\n              <p>{{ item.owner }}</p>\n            </div>\n            <div class=\"list-content-item\">\n              <span>开始时间</span>\n              <p>{{ item.startAt }}</p>\n            </div>\n            <div class=\"list-content-item\">\n              <a-progress :percent=\"item.progress.value\" :status=\"!item.progress.status ? null : item.progress.status\" style=\"width: 180px\" />\n            </div>\n          </div>\n        </a-list-item>\n      </a-list>\n\n    </a-card>\n  </div>\n</template>\n\n<script>\n  import HeadInfo from '@/components/tools/HeadInfo'\n\n  const data = []\n  data.push({\n    title: 'Alipay',\n    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png',\n    description: '那是一种内在的东西， 他们到达不了，也无法触及的',\n    owner: '付晓晓',\n    startAt: '2018-07-26 22:44',\n    progress: {\n      value: 90\n    }\n  })\n  data.push({\n    title: 'Angular',\n    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png',\n    description: '希望是一个好东西，也许是最好的，好东西是不会消亡的',\n    owner: '曲丽丽',\n    startAt: '2018-07-26 22:44',\n    progress: {\n      value: 54\n    }\n  })\n  data.push({\n    title: 'Ant Design',\n    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png',\n    description: '生命就像一盒巧克力，结果往往出人意料',\n    owner: '林东东',\n    startAt: '2018-07-26 22:44',\n    progress: {\n      value: 66\n    }\n  })\n  data.push({\n    title: 'Ant Design Pro',\n    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png',\n    description: '城镇中有那么多的酒馆，她却偏偏走进了我的酒馆',\n    owner: '周星星',\n    startAt: '2018-07-26 22:44',\n    progress: {\n      value: 30\n    }\n  })\n  data.push({\n    title: 'Bootstrap',\n    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png',\n    description: '那时候我只会想自己想要什么，从不想自己拥有什么',\n    owner: '吴加好',\n    startAt: '2018-07-26 22:44',\n    progress: {\n      status: 'exception',\n      value: 100\n    }\n  })\n\n  export default {\n    name: \"StandardList\",\n    components: {\n      HeadInfo\n    },\n    data () {\n      return {\n        data\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n    .ant-avatar-lg {\n        width: 48px;\n        height: 48px;\n        line-height: 48px;\n    }\n\n    .list-content-item {\n        color: rgba(0, 0, 0, .45);\n        display: inline-block;\n        vertical-align: middle;\n        font-size: 14px;\n        margin-left: 40px;\n        span {\n            line-height: 20px;\n        }\n        p {\n            margin-top: 4px;\n            margin-bottom: 0;\n            line-height: 22px;\n        }\n    }\n</style>"], "mappings": "AAyEA,OAAAA,QAAA;AAEA,IAAAC,KAAA;AACAA,KAAA,CAAAC,IAAA;EACAC,KAAA;EACAC,MAAA;EACAC,WAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;IACAC,KAAA;EACA;AACA;AACAR,KAAA,CAAAC,IAAA;EACAC,KAAA;EACAC,MAAA;EACAC,WAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;IACAC,KAAA;EACA;AACA;AACAR,KAAA,CAAAC,IAAA;EACAC,KAAA;EACAC,MAAA;EACAC,WAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;IACAC,KAAA;EACA;AACA;AACAR,KAAA,CAAAC,IAAA;EACAC,KAAA;EACAC,MAAA;EACAC,WAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;IACAC,KAAA;EACA;AACA;AACAR,KAAA,CAAAC,IAAA;EACAC,KAAA;EACAC,MAAA;EACAC,WAAA;EACAC,KAAA;EACAC,OAAA;EACAC,QAAA;IACAE,MAAA;IACAD,KAAA;EACA;AACA;AAEA;EACAE,IAAA;EACAC,UAAA;IACAZ,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA,EAAAA;IACA;EACA;AACA", "ignoreList": []}]}