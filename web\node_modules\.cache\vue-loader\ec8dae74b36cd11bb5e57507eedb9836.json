{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Analysis.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\dashboard\\Analysis.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import IndexChart from './IndexChart'\n  import IndexTask from \"./IndexTask\"\n  import IndexBdc from './IndexBdc'\n\n  export default {\n    name: \"Analysis\",\n    components: {\n      IndexChart,\n      IndexTask,\n      IndexBdc\n    },\n    data() {\n      return {\n        indexStyle:1\n      }\n    },\n    created() {\n\n    },\n    methods: {\n\n    }\n  }\n", {"version": 3, "sources": ["Analysis.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA", "file": "Analysis.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div>\n    <index-chart v-if=\"indexStyle==1\"></index-chart>\n    <index-bdc v-if=\"indexStyle==2\"></index-bdc>\n    <index-task v-if=\"indexStyle==3\"></index-task>\n    <div style=\"width: 100%;text-align: right;margin-top: 20px\">\n      请选择首页样式：\n      <a-radio-group v-model=\"indexStyle\">\n        <a-radio :value=\"1\">统计图表</a-radio>\n        <a-radio :value=\"2\">统计图表2</a-radio>\n        <a-radio :value=\"3\">任务表格</a-radio>\n      </a-radio-group>\n    </div>\n  </div>\n</template>\n\n<script>\n  import IndexChart from './IndexChart'\n  import IndexTask from \"./IndexTask\"\n  import IndexBdc from './IndexBdc'\n\n  export default {\n    name: \"Analysis\",\n    components: {\n      IndexChart,\n      IndexTask,\n      IndexBdc\n    },\n    data() {\n      return {\n        indexStyle:1\n      }\n    },\n    created() {\n\n    },\n    methods: {\n\n    }\n  }\n</script>"]}]}