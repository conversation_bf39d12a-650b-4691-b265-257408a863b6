{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\ExamPaperPreview.vue?vue&type=template&id=a3abb3e6&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\components\\ExamPaperPreview.vue", "mtime": 1753586761732}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div v-if=\"visible\" class=\"exam-paper-preview-overlay\">\n  <div class=\"exam-paper-preview-container\">\n    <!-- 左侧答题卡 -->\n    <div class=\"preview-answer-card\">\n      <div class=\"preview-card-title\">答题卡-预览界面</div>\n\n      <!-- 考试信息 -->\n      <div class=\"preview-exam-info\" v-if=\"paperData\">\n        <div class=\"preview-exam-name\">{{ paperData.paperTitle }}</div>\n        <div class=\"preview-exam-detail\">\n          <div class=\"info-item\">\n            <span class=\"label\">考试时长：</span>\n            <span class=\"value\">{{ paperData.examDuration }}分钟</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"label\">考试用时：</span>\n            <span class=\"value\">{{ formatDurationFromSeconds(paperData.examDurationSeconds) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"label\">试卷得分：</span>\n            <span class=\"value score\" :class=\"{ 'passed': paperData.score >= 60, 'failed': paperData.score < 60 }\">\n              {{ paperData.score }}分\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 题目导航区域 -->\n      <div class=\"preview-question-section-container\" v-if=\"paperData\">\n        <!-- 单选题区域 -->\n        <div class=\"preview-question-section\" v-if=\"paperData.singleChoiceQuestions && paperData.singleChoiceQuestions.length > 0\">\n          <div class=\"preview-section-header\">一、单选题（每题 {{ getSingleChoiceScore() }} 分，共 {{ paperData.singleChoiceQuestions.length * getSingleChoiceScore() }} 分）</div>\n          <div class=\"preview-question-numbers\">\n            <div\n              v-for=\"(question, index) in paperData.singleChoiceQuestions\"\n              :key=\"'sc-'+index\"\n              :class=\"[\n                'preview-question-number',\n                getQuestionStatusClass(question),\n                {'selected': currentSelectedQuestion === 'single-'+index}\n              ]\"\n              @click=\"selectQuestion('single-'+index)\"\n            >\n              {{ index + 1 }}\n            </div>\n          </div>\n        </div>\n\n        <!-- 判断题区域 -->\n        <div class=\"preview-question-section\" v-if=\"paperData.judgmentQuestions && paperData.judgmentQuestions.length > 0\">\n          <div class=\"preview-section-header\">二、判断题（每题 {{ getJudgmentScore() }} 分，共 {{ paperData.judgmentQuestions.length * getJudgmentScore() }} 分）</div>\n          <div class=\"preview-question-numbers\">\n            <div\n              v-for=\"(question, index) in paperData.judgmentQuestions\"\n              :key=\"'jg-'+index\"\n              :class=\"[\n                'preview-question-number',\n                getQuestionStatusClass(question),\n                {'selected': currentSelectedQuestion === 'judgment-'+index}\n              ]\"\n              @click=\"selectQuestion('judgment-'+index)\"\n            >\n              {{ index + 1 }}\n            </div>\n          </div>\n        </div>\n\n        <!-- 编程题区域 -->\n        <div class=\"preview-question-section\" v-if=\"paperData.programmingQuestions && paperData.programmingQuestions.length > 0\">\n          <div class=\"preview-section-header\">三、编程题（每题 {{ getProgrammingScore() }} 分，共 {{ paperData.programmingQuestions.length * getProgrammingScore() }} 分）</div>\n          <div class=\"preview-question-numbers\">\n            <div\n              v-for=\"(question, index) in paperData.programmingQuestions\"\n              :key=\"'prog-'+index\"\n              :class=\"[\n                'preview-question-number',\n                getQuestionStatusClass(question),\n                {'selected': currentSelectedQuestion === 'prog-'+index}\n              ]\"\n              @click=\"selectQuestion('prog-'+index)\"\n            >\n              {{ index + 1 }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 退出按钮 -->\n      <div class=\"preview-exit-area\">\n        <a-button\n          type=\"primary\"\n          size=\"large\"\n          @click=\"handleExit\"\n          block\n        >\n          退出界面\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 右侧答题页面 -->\n    <div class=\"preview-content-wrapper\">\n      <a-card class=\"preview-question-content-card\" :bordered=\"false\">\n        <!-- 标签页导航 -->\n        <div class=\"preview-exam-tabs\">\n          <div class=\"preview-tab-header\">\n            <div class=\"preview-tab-items\">\n              <div\n                class=\"preview-tab-item\"\n                :class=\"{'tab-active': activeName === 'objectiveQuestions'}\"\n                @click=\"switchToObjectiveQuestions\"\n              >\n                <a-icon type=\"form\" /> 客观题\n              </div>\n              <div\n                class=\"preview-tab-item\"\n                :class=\"{'tab-active': activeName === 'programmingQuestions'}\"\n                @click=\"switchToProgrammingQuestions\"\n              >\n                <a-icon type=\"code\" /> 编程题\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 客观题内容 -->\n        <div v-show=\"activeName === 'objectiveQuestions'\" class=\"preview-tab-content\">\n          <div class=\"preview-exam-content\">\n            <!-- 单选题区域 -->\n            <div v-if=\"paperData && paperData.singleChoiceQuestions && paperData.singleChoiceQuestions.length > 0\" class=\"preview-exam-section\">\n              <h3 class=\"preview-section-title\">一、单选题（每题 {{ getSingleChoiceScore() }} 分，共 {{ paperData.singleChoiceQuestions.length * getSingleChoiceScore() }} 分）</h3>\n              <div v-for=\"(question, index) in paperData.singleChoiceQuestions\" :key=\"'single-'+index\" class=\"preview-exam-question\" :id=\"'single-'+index\">\n                <div class=\"preview-question-title\">\n                  <span v-html=\"(index+1) + '. ' + question.title\"></span>\n                  <!-- 题目状态标签 -->\n                  <span :class=\"['status-tag', getQuestionStatusClass(question)]\">\n                    <a-icon :type=\"getStatusIcon(question)\" />\n                    {{ getStatusText(question) }}\n                  </span>\n                </div>\n                <div class=\"preview-question-options\">\n                  <div v-for=\"option in question.options\" :key=\"option.label\"\n                       class=\"preview-question-option\"\n                       :class=\"{\n                         'review-error-option': question.userAnswer === option.label && question.userAnswer !== question.correctAnswer,\n                         'review-correct-option': option.label === question.correctAnswer\n                       }\">\n                    <span class=\"option-label\">{{ option.label }}.</span>\n                    <span class=\"option-content\" v-html=\"option.content\"></span>\n                  </div>\n                </div>\n                <div class=\"preview-answer-info\">\n                  <div class=\"answer-row\">\n                    <span class=\"answer-label\">得分：</span>\n                    <span class=\"answer-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                      {{ question.actualScore }} 分\n                    </span>\n                  </div>\n                  <div class=\"answer-row\">\n                    <span class=\"answer-label\">答案：</span>\n                    <span class=\"answer-value correct-answer\">{{ question.correctAnswer }}</span>\n                  </div>\n                  <div class=\"answer-row\" v-if=\"question.analysis\">\n                    <span class=\"answer-label\">解析：</span>\n                    <span class=\"answer-value analysis-content\" v-html=\"question.analysis\"></span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 判断题区域 -->\n            <div v-if=\"paperData && paperData.judgmentQuestions && paperData.judgmentQuestions.length > 0\" class=\"preview-exam-section\">\n              <h3 class=\"preview-section-title\">二、判断题（每题 {{ getJudgmentScore() }} 分，共 {{ paperData.judgmentQuestions.length * getJudgmentScore() }} 分）</h3>\n              <div v-for=\"(question, index) in paperData.judgmentQuestions\" :key=\"'judgment-'+index\" class=\"preview-exam-question\" :id=\"'judgment-'+index\">\n                <div class=\"preview-question-title\">\n                  <span v-html=\"(index+1) + '. ' + question.title\"></span>\n                  <!-- 题目状态标签 -->\n                  <span :class=\"['status-tag', getQuestionStatusClass(question)]\">\n                    <a-icon :type=\"getStatusIcon(question)\" />\n                    {{ getStatusText(question) }}\n                  </span>\n                </div>\n                <div class=\"preview-question-options\">\n                  <div class=\"preview-question-option\"\n                       :class=\"{\n                         'review-error-option': question.userAnswer === 'T' && question.userAnswer !== question.correctAnswer,\n                         'review-correct-option': question.correctAnswer === 'T'\n                       }\">\n                    <span class=\"option-label\">正确</span>\n                  </div>\n                  <div class=\"preview-question-option\"\n                       :class=\"{\n                         'review-error-option': question.userAnswer === 'F' && question.userAnswer !== question.correctAnswer,\n                         'review-correct-option': question.correctAnswer === 'F'\n                       }\">\n                    <span class=\"option-label\">错误</span>\n                  </div>\n                </div>\n                <div class=\"preview-answer-info\">\n                  <div class=\"answer-row\">\n                    <span class=\"answer-label\">得分：</span>\n                    <span class=\"answer-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                      {{ question.actualScore }} 分\n                    </span>\n                  </div>\n                  <div class=\"answer-row\">\n                    <span class=\"answer-label\">正确答案：</span>\n                    <span class=\"answer-value correct-answer\">{{ getJudgmentAnswerText(question.correctAnswer) }}</span>\n                  </div>\n                  <div class=\"answer-row\" v-if=\"question.analysis\">\n                    <span class=\"answer-label\">解析：</span>\n                    <span class=\"answer-value analysis-content\" v-html=\"question.analysis\"></span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 编程题内容 -->\n        <div v-show=\"activeName === 'programmingQuestions'\" class=\"preview-tab-content\">\n          <div class=\"preview-exam-content\">\n            <!-- 编程题区域 -->\n            <div v-if=\"paperData && paperData.programmingQuestions && paperData.programmingQuestions.length > 0\" class=\"preview-exam-section\">\n              <h3 class=\"preview-section-title\">三、编程题（每题 {{ getProgrammingScore() }} 分，共 {{ paperData.programmingQuestions.length * getProgrammingScore() }} 分）</h3>\n              <div\n                v-for=\"(question, index) in paperData.programmingQuestions\"\n                :key=\"'prog-'+index\"\n                class=\"preview-exam-question preview-programming-question\"\n                :id=\"'prog-'+index\"\n                v-show=\"currentProgrammingQuestion === 'prog-'+index || (currentProgrammingQuestion === null && index === 0)\"\n              >\n                <!-- 编程题组件 -->\n                <div class=\"preview-coding-question-wrapper\">\n                  <!-- 题目标题 -->\n                  <div class=\"preview-question-title\">\n                    <div class=\"preview-title-header\">\n                      <div class=\"preview-title-left\">\n                        <span class=\"preview-question-number\">{{ index + 1 }}</span>\n                        <h3>{{ question.title }}</h3>\n                      </div>\n\n                    </div>\n                  </div>\n\n                  <!-- 编程题内容布局 -->\n                  <div class=\"preview-coding-question-container\">\n                    <a-row :gutter=\"20\">\n                      <!-- 题目描述区域 -->\n                      <a-col :md=\"10\" :sm=\"24\">\n                        <div class=\"preview-problem-description\">\n                          <!-- 题目描述 -->\n                          <div v-if=\"question.description\" class=\"preview-problem-section\">\n                            <div v-html=\"markdownToHtml(question.description)\"></div>\n                          </div>\n\n                          <!-- 时间和内存限制 -->\n                          <div v-if=\"question.timeLimit || question.memoryLimit\" class=\"preview-problem-section\">\n                            <p><strong>限制条件：</strong></p>\n                            <div class=\"preview-limit-info\">\n                              <div v-if=\"question.timeLimit\" class=\"preview-limit-item\">\n                                <a-icon type=\"clock-circle\" /> 时间限制：{{ question.timeLimit }}ms\n                              </div>\n                              <div v-if=\"question.memoryLimit\" class=\"preview-limit-item\">\n                                <a-icon type=\"database\" /> 内存限制：{{ question.memoryLimit }}KB\n                              </div>\n                            </div>\n                          </div>\n\n                          <a-divider v-if=\"question.inputFormat || question.outputFormat || (question.samples && question.samples.length > 0)\" />\n\n                          <!-- 输入格式 -->\n                          <div v-if=\"question.inputFormat\" class=\"preview-problem-section\">\n                            <p><strong>输入格式：</strong></p>\n                            <div v-html=\"markdownToHtml(question.inputFormat)\"></div>\n                          </div>\n\n                          <a-divider v-if=\"question.outputFormat || (question.samples && question.samples.length > 0)\" />\n\n                          <!-- 输出格式 -->\n                          <div v-if=\"question.outputFormat\" class=\"preview-problem-section\">\n                            <p><strong>输出格式：</strong></p>\n                            <div v-html=\"markdownToHtml(question.outputFormat)\"></div>\n                          </div>\n\n                          <a-divider v-if=\"question.samples && question.samples.length > 0\" />\n\n                          <!-- 样例 -->\n                          <div v-for=\"(sample, sampleIndex) in question.samples\" :key=\"sampleIndex\" v-if=\"question.samples && question.samples.length > 0\" class=\"preview-problem-section\">\n                            <p><strong>样例 {{ sampleIndex + 1 }}：</strong></p>\n                            <div class=\"preview-sample-container\">\n                              <div class=\"preview-sample-input\">\n                                <div class=\"preview-sample-header\">输入：</div>\n                                <pre>{{ sample.input }}</pre>\n                              </div>\n                              <div class=\"preview-sample-output\">\n                                <div class=\"preview-sample-header\">输出：</div>\n                                <pre>{{ sample.output }}</pre>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- 提示 -->\n                          <div v-if=\"question.hint\" class=\"preview-problem-section\">\n                            <a-divider />\n                            <p><strong>提示：</strong></p>\n                            <div v-html=\"markdownToHtml(question.hint)\"></div>\n                          </div>\n\n                          <!-- 题目解析 -->\n                          <div v-if=\"question.analysis\" class=\"preview-problem-section\">\n                            <a-divider />\n                            <p><strong>解析：</strong></p>\n                            <div v-html=\"markdownToHtml(question.analysis)\" class=\"analysis-content\"></div>\n                          </div>\n                        </div>\n                      </a-col>\n\n                      <!-- 代码展示区域 -->\n                      <a-col :md=\"14\" :sm=\"24\">\n                        <div class=\"preview-code-section\">\n                          <div class=\"preview-code-header\">\n                            <span class=\"preview-code-title\">提交的代码</span>\n                            <span class=\"preview-code-language\">{{ question.language }}</span>\n                          </div>\n                          <div class=\"preview-code-content\">\n                            <pre class=\"preview-code-display\">{{ question.userCode || '未提交代码' }}</pre>\n                          </div>\n                          <div class=\"preview-code-result\">\n                            <div class=\"result-row\">\n                              <span class=\"result-label\">提交状态：</span>\n                              <span class=\"result-value\" :class=\"{ 'submitted': question.submitted, 'not-submitted': !question.submitted }\">\n                                {{ question.submitted ? '已提交' : '未提交' }}\n                              </span>\n                            </div>\n                            <div class=\"result-row\">\n                              <span class=\"result-label\">判题结果：</span>\n                              <span class=\"result-value\" :class=\"{ 'correct': question.isCorrect, 'incorrect': !question.isCorrect && question.submitted }\">\n                                {{ getJudgeResultText(question) }}\n                              </span>\n                            </div>\n                            <div class=\"result-row\">\n                              <span class=\"result-label\">得分：</span>\n                              <span class=\"result-value score-value\" :class=\"{ 'full-score': question.actualScore === question.score, 'zero-score': question.actualScore === 0 }\">\n                                {{ question.actualScore }} 分\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </a-col>\n                    </a-row>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </a-card>\n    </div>\n  </div>\n</div>\n", null]}